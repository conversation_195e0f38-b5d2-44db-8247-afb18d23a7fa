-- 签到功能相关数据表
-- 执行此SQL文件来创建签到功能所需的数据表

-- 1. 签到记录表
CREATE TABLE `hiolabs_sign_in_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `sign_date` date NOT NULL COMMENT '签到日期',
  `points_earned` int(11) DEFAULT 0 COMMENT '获得积分',
  `consecutive_days` int(11) DEFAULT 0 COMMENT '连续签到天数',
  `is_bonus` tinyint(1) DEFAULT 0 COMMENT '是否有奖励积分',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_date` (`user_id`, `sign_date`),
  KEY `user_id` (`user_id`),
  KEY `sign_date` (`sign_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户签到记录表';

-- 2. 用户积分表
CREATE TABLE `hiolabs_user_points` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `total_points` int(11) DEFAULT 0 COMMENT '总积分',
  `available_points` int(11) DEFAULT 0 COMMENT '可用积分',
  `used_points` int(11) DEFAULT 0 COMMENT '已使用积分',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户积分表';

-- 3. 积分日志表
CREATE TABLE `hiolabs_points_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `points_change` int(11) NOT NULL COMMENT '积分变化（正数为增加，负数为减少）',
  `points_type` varchar(50) DEFAULT NULL COMMENT '积分类型（signin, bonus, consume等）',
  `source_id` int(11) DEFAULT NULL COMMENT '来源ID（如签到记录ID）',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `balance_after` int(11) DEFAULT 0 COMMENT '变化后余额',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `points_type` (`points_type`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='积分变化日志表';

-- 插入测试数据（可选）
-- 为用户ID 5608 初始化积分记录
INSERT INTO `hiolabs_user_points` (`user_id`, `total_points`, `available_points`, `used_points`, `created_at`, `updated_at`) 
VALUES (5608, 0, 0, 0, NOW(), NOW()) 
ON DUPLICATE KEY UPDATE `updated_at` = NOW();
