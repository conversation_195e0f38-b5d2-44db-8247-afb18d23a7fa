{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\signin.js"], "names": ["Base", "require", "module", "exports", "dataAction", "console", "log", "openid", "get", "fail", "user", "model", "where", "weixin_openid", "find", "think", "isEmpty", "userId", "id", "nickname", "month", "getCurrentMonth", "userPoints", "getUserPoints", "consecutiveDays", "getConsecutiveDays", "signed<PERSON><PERSON><PERSON><PERSON>", "getMonthSignRecords", "signedDates", "map", "record", "sign_date", "today", "getCurrentDate", "todaySigned", "includes", "monthlyStats", "getMonthlyStats", "result", "totalPoints", "total_points", "availablePoints", "available_points", "success", "error", "checkinAction", "post", "signDate", "existingRecord", "user_id", "calculateConsecutiveDays", "pointsInfo", "calculatePoints", "signRecord", "add", "points_earned", "consecutive_days", "is_bonus", "bonusPoints", "created_at", "Date", "insertError", "updatedPoints", "earnedPoints", "bonusInfo", "isBonus", "bonusType", "getBonusType", "used_points", "year", "monthNum", "split", "daysInMonth", "getDate", "signedDays", "length", "totalDays", "isFullAttendance", "basePoints", "updateUserPoints", "addUserPoints", "addPointsLog", "pointsChange", "pointsType", "sourceId", "description", "now", "getFullYear", "String", "getMonth", "padStart", "day", "getDateBefore", "dateStr", "days", "date", "setDate"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;;AAEAC,OAAOC,OAAP,GAAiB,cAAcH,IAAd,CAAmB;;AAElC;;;;AAIMI,YAAN,GAAmB;AAAA;;AAAA;AACjBC,cAAQC,GAAR,CAAY,kBAAZ;;AAEA;AACA,YAAMC,SAAS,MAAKC,GAAL,CAAS,QAAT,CAAf;AACAH,cAAQC,GAAR,CAAY,aAAZ,EAA2BC,MAA3B;;AAEA,UAAI,CAACA,MAAL,EAAa;AACXF,gBAAQC,GAAR,CAAY,YAAZ;AACA,eAAO,MAAKG,IAAL,CAAU,GAAV,EAAe,cAAf,CAAP;AACD;;AAED;AACA,YAAMC,OAAO,MAAM,MAAKC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC1CC,uBAAeN;AAD2B,OAAzB,EAEhBO,IAFgB,EAAnB;;AAIA,UAAIC,MAAMC,OAAN,CAAcN,IAAd,CAAJ,EAAyB;AACvBL,gBAAQC,GAAR,CAAY,eAAZ,EAA6BC,MAA7B;AACA,eAAO,MAAKE,IAAL,CAAU,GAAV,EAAe,cAAf,CAAP;AACD;;AAED,YAAMQ,SAASP,KAAKQ,EAApB;AACAb,cAAQC,GAAR,CAAY,UAAZ,EAAwBW,MAAxB,EAAgC,KAAhC,EAAuCP,KAAKS,QAA5C;;AAEA,UAAI;AACF,cAAMC,QAAQ,MAAKZ,GAAL,CAAS,OAAT,KAAqB,MAAKa,eAAL,EAAnC;AACAhB,gBAAQC,GAAR,CAAY,gBAAZ;AACAD,gBAAQC,GAAR,CAAY,OAAZ,EAAqBW,MAArB;AACAZ,gBAAQC,GAAR,CAAY,OAAZ,EAAqBc,KAArB;;AAEA;AACA,cAAME,aAAa,MAAM,MAAKC,aAAL,CAAmBN,MAAnB,CAAzB;;AAEA;AACA,cAAMO,kBAAkB,MAAM,MAAKC,kBAAL,CAAwBR,MAAxB,CAA9B;;AAEA;AACA,cAAMS,gBAAgB,MAAM,MAAKC,mBAAL,CAAyBV,MAAzB,EAAiCG,KAAjC,CAA5B;AACA,cAAMQ,cAAcF,cAAcG,GAAd,CAAkB;AAAA,iBAAUC,OAAOC,SAAjB;AAAA,SAAlB,CAApB;;AAEA;AACA,cAAMC,QAAQ,MAAKC,cAAL,EAAd;AACA,cAAMC,cAAcN,YAAYO,QAAZ,CAAqBH,KAArB,CAApB;;AAEA;AACA,cAAMI,eAAe,MAAM,MAAKC,eAAL,CAAqBpB,MAArB,EAA6BG,KAA7B,CAA3B;;AAEA,cAAMkB,SAAS;AACbd,2BAAiBA,eADJ;AAEbe,uBAAcjB,cAAcA,WAAWkB,YAA1B,IAA2C,CAF3C;AAGbC,2BAAkBnB,cAAcA,WAAWoB,gBAA1B,IAA+C,CAHnD;AAIbR,uBAAaA,WAJA;AAKbN,uBAAaA,WALA;AAMbQ,wBAAcA;AAND,SAAf;;AASA/B,gBAAQC,GAAR,CAAY,SAAZ,EAAuBgC,MAAvB;AACA,eAAO,MAAKK,OAAL,CAAaL,MAAb,CAAP;AAED,OAnCD,CAmCE,OAAOM,KAAP,EAAc;AACdvC,gBAAQuC,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,MAAKnC,IAAL,CAAU,GAAV,EAAe,UAAf,CAAP;AACD;AA/DgB;AAgElB;;AAED;;;;AAIMoC,eAAN,GAAsB;AAAA;;AAAA;AACpBxC,cAAQC,GAAR,CAAY,kBAAZ;;AAEA;AACA,YAAMC,SAAS,OAAKuC,IAAL,CAAU,QAAV,CAAf;AACAzC,cAAQC,GAAR,CAAY,aAAZ,EAA2BC,MAA3B;;AAEA,UAAI,CAACA,MAAL,EAAa;AACXF,gBAAQC,GAAR,CAAY,YAAZ;AACA,eAAO,OAAKG,IAAL,CAAU,GAAV,EAAe,cAAf,CAAP;AACD;;AAED;AACA,YAAMC,OAAO,MAAM,OAAKC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC1CC,uBAAeN;AAD2B,OAAzB,EAEhBO,IAFgB,EAAnB;;AAIA,UAAIC,MAAMC,OAAN,CAAcN,IAAd,CAAJ,EAAyB;AACvBL,gBAAQC,GAAR,CAAY,eAAZ,EAA6BC,MAA7B;AACA,eAAO,OAAKE,IAAL,CAAU,GAAV,EAAe,cAAf,CAAP;AACD;;AAED,YAAMQ,SAASP,KAAKQ,EAApB;AACAb,cAAQC,GAAR,CAAY,UAAZ,EAAwBW,MAAxB,EAAgC,KAAhC,EAAuCP,KAAKS,QAA5C;;AAEA,UAAI;AACF,cAAM4B,WAAW,OAAKD,IAAL,CAAU,MAAV,KAAqB,OAAKb,cAAL,EAAtC;AACA,cAAMD,QAAQ,OAAKC,cAAL,EAAd;;AAEA5B,gBAAQC,GAAR,CAAY,cAAZ;AACAD,gBAAQC,GAAR,CAAY,OAAZ,EAAqBW,MAArB;AACAZ,gBAAQC,GAAR,CAAY,OAAZ,EAAqByC,QAArB;AACA1C,gBAAQC,GAAR,CAAY,OAAZ,EAAqB0B,KAArB;;AAEA;AACA,YAAIe,aAAaf,KAAjB,EAAwB;AACtB,iBAAO,OAAKvB,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACD;;AAED;AACA,cAAMuC,iBAAiB,MAAM,OAAKrC,KAAL,CAAW,QAAX,EAAqBC,KAArB,CAA2B;AACtDqC,mBAAShC,MAD6C;AAEtDc,qBAAWgB;AAF2C,SAA3B,EAG1BjC,IAH0B,EAA7B;;AAKA,YAAI,CAACC,MAAMC,OAAN,CAAcgC,cAAd,CAAL,EAAoC;AAClC,iBAAO,OAAKvC,IAAL,CAAU,GAAV,EAAe,OAAf,CAAP;AACD;;AAED;AACA,cAAMe,kBAAkB,MAAM,OAAK0B,wBAAL,CAA8BjC,MAA9B,EAAsC8B,QAAtC,CAA9B;;AAEA;AACA,cAAMI,aAAa,OAAKC,eAAL,CAAqB5B,eAArB,CAAnB;;AAEAnB,gBAAQC,GAAR,CAAY,SAAZ,EAAuBkB,eAAvB;AACAnB,gBAAQC,GAAR,CAAY,OAAZ,EAAqB6C,UAArB;;AAEA;AACA9C,gBAAQC,GAAR,CAAY,WAAZ;;AAEA,YAAI;AACF;AACAD,kBAAQC,GAAR,CAAY,WAAZ;AACA,gBAAM+C,aAAa,MAAM,OAAK1C,KAAL,CAAW,QAAX,EAAqB2C,GAArB,CAAyB;AAChDL,qBAAShC,MADuC;AAEhDc,uBAAWgB,QAFqC;AAGhDQ,2BAAeJ,WAAWZ,WAHsB;AAIhDiB,8BAAkBhC,eAJ8B;AAKhDiC,sBAAUN,WAAWO,WAAX,GAAyB,CAAzB,GAA6B,CAA7B,GAAiC,CALK;AAMhDC,wBAAY,IAAIC,IAAJ;AANoC,WAAzB,CAAzB;AAQAvD,kBAAQC,GAAR,CAAY,cAAZ,EAA4B+C,UAA5B;;AAEA;AACAhD,kBAAQC,GAAR,CAAY,eAAZ;AACAD,kBAAQC,GAAR,CAAY,UAAZ,EAAwB6C,WAAWZ,WAAnC;;AAEAlC,kBAAQC,GAAR,CAAY,QAAZ;AACD,SAlBD,CAkBE,OAAOuD,WAAP,EAAoB;AACpBxD,kBAAQuC,KAAR,CAAc,SAAd,EAAyBiB,WAAzB;AACA,gBAAMA,WAAN;AACD;;AAED;AACA,cAAMC,gBAAgB,MAAM,OAAKvC,aAAL,CAAmBN,MAAnB,CAA5B;;AAEA,cAAMqB,SAAS;AACbK,mBAAS,IADI;AAEboB,wBAAcZ,WAAWZ,WAFZ;AAGbf,2BAAiBA,eAHJ;AAIbe,uBAAauB,cAActB,YAJd;AAKbwB,qBAAW;AACTC,qBAASd,WAAWO,WAAX,GAAyB,CADzB;AAETQ,uBAAW,OAAKC,YAAL,CAAkB3C,eAAlB,CAFF;AAGTkC,yBAAaP,WAAWO;AAHf;AALE,SAAf;;AAYArD,gBAAQC,GAAR,CAAY,YAAZ,EAA0BgC,MAA1B;AACA,eAAO,OAAKK,OAAL,CAAaL,MAAb,CAAP;AAED,OA7ED,CA6EE,OAAOM,KAAP,EAAc;AACdvC,gBAAQuC,KAAR,CAAc,OAAd,EAAuBA,KAAvB;AACA,eAAO,OAAKnC,IAAL,CAAU,GAAV,EAAe,UAAf,CAAP;AACD;AAzGmB;AA0GrB;;AAED;;;AAGMc,eAAN,CAAoBN,MAApB,EAA4B;AAAA;AAC1B,aAAO;AACLuB,sBAAc,CADT;AAELE,0BAAkB,CAFb;AAGL0B,qBAAa;AAHR,OAAP;AAD0B;AAM3B;;AAED;;;AAGM3C,oBAAN,CAAyBR,MAAzB,EAAiC;AAAA;;AAAA;AAC/B,aAAO,MAAM,OAAKN,KAAL,CAAW,QAAX,EAAqBc,kBAArB,CAAwCR,MAAxC,CAAb;AAD+B;AAEhC;;AAED;;;AAGMiC,0BAAN,CAA+BjC,MAA/B,EAAuC8B,QAAvC,EAAiD;AAAA;;AAAA;AAC/C,YAAMvB,kBAAkB,MAAM,OAAKC,kBAAL,CAAwBR,MAAxB,CAA9B;AACA,aAAOO,kBAAkB,CAAzB,CAF+C,CAEnB;AAFmB;AAGhD;;AAED;;;AAGMG,qBAAN,CAA0BV,MAA1B,EAAkCG,KAAlC,EAAyC;AAAA;;AAAA;AACvC,aAAO,MAAM,OAAKT,KAAL,CAAW,QAAX,EAAqBgB,mBAArB,CAAyCV,MAAzC,EAAiDG,KAAjD,CAAb;AADuC;AAExC;;AAED;;;AAGMiB,iBAAN,CAAsBpB,MAAtB,EAA8BG,KAA9B,EAAqC;AAAA;;AAAA;AACnC,YAAM,CAACiD,IAAD,EAAOC,QAAP,IAAmBlD,MAAMmD,KAAN,CAAY,GAAZ,CAAzB;AACA,YAAMC,cAAc,IAAIZ,IAAJ,CAASS,IAAT,EAAeC,QAAf,EAAyB,CAAzB,EAA4BG,OAA5B,EAApB;;AAEA,YAAM/C,gBAAgB,MAAM,OAAKC,mBAAL,CAAyBV,MAAzB,EAAiCG,KAAjC,CAA5B;AACA,YAAMsD,aAAahD,cAAciD,MAAjC;;AAEA,aAAO;AACLC,mBAAWJ,WADN;AAELE,oBAAYA,UAFP;AAGLG,0BAAkBH,eAAeF;AAH5B,OAAP;AAPmC;AAYpC;;AAED;;;AAGApB,kBAAgB5B,eAAhB,EAAiC;AAC/B,UAAMsD,aAAa,CAAnB,CAD+B,CACT;AACtB,QAAIpB,cAAc,CAAlB,CAF+B,CAET;;AAEtB;AACA,QAAIlC,mBAAmB,EAAvB,EAA2B;AACzBkC,oBAAc,EAAd,CADyB,CACP;AACnB,KAFD,MAEO,IAAIlC,mBAAmB,EAAvB,EAA2B;AAChCkC,oBAAc,EAAd,CADgC,CACd;AACnB,KAFM,MAEA,IAAIlC,mBAAmB,CAAvB,EAA0B;AAC/BkC,oBAAc,EAAd,CAD+B,CACb;AACnB,KAFM,MAEA,IAAIlC,mBAAmB,CAAvB,EAA0B;AAC/BkC,oBAAc,CAAd,CAD+B,CACb;AACnB;;AAED,WAAO;AACLoB,kBAAYA,UADP;AAELpB,mBAAaA,WAFR;AAGLnB,mBAAauC,aAAapB;AAHrB,KAAP;AAKD;;AAED;;;AAGAS,eAAa3C,eAAb,EAA8B;AAC5B,QAAIA,mBAAmB,EAAvB,EAA2B,OAAO,gBAAP;AAC3B,QAAIA,mBAAmB,EAAvB,EAA2B,OAAO,gBAAP;AAC3B,QAAIA,mBAAmB,CAAvB,EAA0B,OAAO,eAAP;AAC1B,QAAIA,mBAAmB,CAAvB,EAA0B,OAAO,eAAP;AAC1B,WAAO,MAAP;AACD;;AAED;;;AAGMuD,kBAAN,CAAuB9D,MAAvB,EAA+B8C,YAA/B,EAA6C;AAAA;;AAAA;AAC3C,aAAO,MAAM,OAAKpD,KAAL,CAAW,QAAX,EAAqBqE,aAArB,CAAmC/D,MAAnC,EAA2C8C,YAA3C,EAAyD,QAAzD,EAAmE,IAAnE,EAAyE,MAAzE,CAAb;AAD2C;AAE5C;;AAED;;;AAGMkB,cAAN,CAAmBhE,MAAnB,EAA2BiE,YAA3B,EAAyCC,UAAzC,EAAqDC,QAArD,EAA+DC,WAA/D,EAA4E;AAAA;;AAAA;AAC1E,aAAO,MAAM,OAAK1E,KAAL,CAAW,QAAX,EAAqBqE,aAArB,CAAmC/D,MAAnC,EAA2CiE,YAA3C,EAAyDC,UAAzD,EAAqEC,QAArE,EAA+EC,WAA/E,CAAb;AAD0E;AAE3E;;AAED;;;AAGAhE,oBAAkB;AAChB,UAAMiE,MAAM,IAAI1B,IAAJ,EAAZ;AACA,UAAMS,OAAOiB,IAAIC,WAAJ,EAAb;AACA,UAAMnE,QAAQoE,OAAOF,IAAIG,QAAJ,KAAiB,CAAxB,EAA2BC,QAA3B,CAAoC,CAApC,EAAuC,GAAvC,CAAd;AACA,WAAQ,GAAErB,IAAK,IAAGjD,KAAM,EAAxB;AACD;;AAED;;;AAGAa,mBAAiB;AACf,UAAMqD,MAAM,IAAI1B,IAAJ,EAAZ;AACA,UAAMS,OAAOiB,IAAIC,WAAJ,EAAb;AACA,UAAMnE,QAAQoE,OAAOF,IAAIG,QAAJ,KAAiB,CAAxB,EAA2BC,QAA3B,CAAoC,CAApC,EAAuC,GAAvC,CAAd;AACA,UAAMC,MAAMH,OAAOF,IAAIb,OAAJ,EAAP,EAAsBiB,QAAtB,CAA+B,CAA/B,EAAkC,GAAlC,CAAZ;AACA,WAAQ,GAAErB,IAAK,IAAGjD,KAAM,IAAGuE,GAAI,EAA/B;AACD;;AAED;;;AAGAC,gBAAcC,OAAd,EAAuBC,IAAvB,EAA6B;AAC3B,UAAMC,OAAO,IAAInC,IAAJ,CAASiC,OAAT,CAAb;AACAE,SAAKC,OAAL,CAAaD,KAAKtB,OAAL,KAAiBqB,IAA9B;;AAEA,UAAMzB,OAAO0B,KAAKR,WAAL,EAAb;AACA,UAAMnE,QAAQoE,OAAOO,KAAKN,QAAL,KAAkB,CAAzB,EAA4BC,QAA5B,CAAqC,CAArC,EAAwC,GAAxC,CAAd;AACA,UAAMC,MAAMH,OAAOO,KAAKtB,OAAL,EAAP,EAAuBiB,QAAvB,CAAgC,CAAhC,EAAmC,GAAnC,CAAZ;AACA,WAAQ,GAAErB,IAAK,IAAGjD,KAAM,IAAGuE,GAAI,EAA/B;AACD;;AA5TiC,CAApC", "file": "..\\..\\..\\src\\api\\controller\\signin.js", "sourcesContent": ["const Base = require('./base.js');\n\nmodule.exports = class extends Base {\n  \n  /**\n   * 获取签到数据\n   * GET /api/signin/data\n   */\n  async dataAction() {\n    console.log('=== 签到数据请求开始 ===');\n\n    // 获取openid参数\n    const openid = this.get('openid');\n    console.log('获取到的openid:', openid);\n\n    if (!openid) {\n      console.log('openid参数缺失');\n      return this.fail(400, 'openid参数必须提供');\n    }\n\n    // 通过openid查询用户\n    const user = await this.model('user').where({\n      weixin_openid: openid\n    }).find();\n\n    if (think.isEmpty(user)) {\n      console.log('用户不存在，openid:', openid);\n      return this.fail(404, '用户不存在，请先完成授权');\n    }\n\n    const userId = user.id;\n    console.log('查询到用户ID:', userId, '昵称:', user.nickname);\n\n    try {\n      const month = this.get('month') || this.getCurrentMonth();\n      console.log('=== 获取签到数据 ===');\n      console.log('用户ID:', userId);\n      console.log('查询月份:', month);\n\n      // 获取用户积分信息\n      const userPoints = await this.getUserPoints(userId);\n      \n      // 获取连续签到天数\n      const consecutiveDays = await this.getConsecutiveDays(userId);\n      \n      // 获取指定月份的签到记录\n      const signedRecords = await this.getMonthSignRecords(userId, month);\n      const signedDates = signedRecords.map(record => record.sign_date);\n\n      // 检查今日是否已签到\n      const today = this.getCurrentDate();\n      const todaySigned = signedDates.includes(today);\n      \n      // 获取月度统计\n      const monthlyStats = await this.getMonthlyStats(userId, month);\n\n      const result = {\n        consecutiveDays: consecutiveDays,\n        totalPoints: (userPoints && userPoints.total_points) || 0,\n        availablePoints: (userPoints && userPoints.available_points) || 0,\n        todaySigned: todaySigned,\n        signedDates: signedDates,\n        monthlyStats: monthlyStats\n      };\n\n      console.log('返回签到数据:', result);\n      return this.success(result);\n\n    } catch (error) {\n      console.error('获取签到数据失败:', error);\n      return this.fail(500, '获取签到数据失败');\n    }\n  }\n\n  /**\n   * 执行签到\n   * POST /api/signin/checkin\n   */\n  async checkinAction() {\n    console.log('=== 签到执行请求开始 ===');\n\n    // 获取openid参数\n    const openid = this.post('openid');\n    console.log('获取到的openid:', openid);\n\n    if (!openid) {\n      console.log('openid参数缺失');\n      return this.fail(400, 'openid参数必须提供');\n    }\n\n    // 通过openid查询用户\n    const user = await this.model('user').where({\n      weixin_openid: openid\n    }).find();\n\n    if (think.isEmpty(user)) {\n      console.log('用户不存在，openid:', openid);\n      return this.fail(404, '用户不存在，请先完成授权');\n    }\n\n    const userId = user.id;\n    console.log('查询到用户ID:', userId, '昵称:', user.nickname);\n\n    try {\n      const signDate = this.post('date') || this.getCurrentDate();\n      const today = this.getCurrentDate();\n\n      console.log('=== 执行签到 ===');\n      console.log('用户ID:', userId);\n      console.log('签到日期:', signDate);\n      console.log('当前日期:', today);\n\n      // 验证签到日期\n      if (signDate !== today) {\n        return this.fail(400, '只能签到当天');\n      }\n\n      // 检查是否已签到\n      const existingRecord = await this.model('signin').where({\n        user_id: userId,\n        sign_date: signDate\n      }).find();\n\n      if (!think.isEmpty(existingRecord)) {\n        return this.fail(400, '今日已签到');\n      }\n\n      // 计算连续签到天数\n      const consecutiveDays = await this.calculateConsecutiveDays(userId, signDate);\n      \n      // 计算积分奖励\n      const pointsInfo = this.calculatePoints(consecutiveDays);\n      \n      console.log('连续签到天数:', consecutiveDays);\n      console.log('积分奖励:', pointsInfo);\n\n      // 简化执行，先不用事务\n      console.log('开始执行签到...');\n\n      try {\n        // 1. 插入签到记录\n        console.log('插入签到记录...');\n        const signRecord = await this.model('signin').add({\n          user_id: userId,\n          sign_date: signDate,\n          points_earned: pointsInfo.totalPoints,\n          consecutive_days: consecutiveDays,\n          is_bonus: pointsInfo.bonusPoints > 0 ? 1 : 0,\n          created_at: new Date()\n        });\n        console.log('签到记录插入成功，ID:', signRecord);\n\n        // 积分系统已移除，签到功能保留但不发放积分\n        console.log('签到完成（积分系统已移除）');\n        console.log('原本会获得积分:', pointsInfo.totalPoints);\n\n        console.log('签到执行完成');\n      } catch (insertError) {\n        console.error('签到执行失败:', insertError);\n        throw insertError;\n      }\n\n      // 获取更新后的用户积分\n      const updatedPoints = await this.getUserPoints(userId);\n\n      const result = {\n        success: true,\n        earnedPoints: pointsInfo.totalPoints,\n        consecutiveDays: consecutiveDays,\n        totalPoints: updatedPoints.total_points,\n        bonusInfo: {\n          isBonus: pointsInfo.bonusPoints > 0,\n          bonusType: this.getBonusType(consecutiveDays),\n          bonusPoints: pointsInfo.bonusPoints\n        }\n      };\n\n      console.log('签到成功，返回结果:', result);\n      return this.success(result);\n\n    } catch (error) {\n      console.error('签到失败:', error);\n      return this.fail(500, '签到失败，请重试');\n    }\n  }\n\n  /**\n   * 获取用户积分信息（积分系统已移除，返回默认值）\n   */\n  async getUserPoints(userId) {\n    return {\n      total_points: 0,\n      available_points: 0,\n      used_points: 0\n    };\n  }\n\n  /**\n   * 获取连续签到天数\n   */\n  async getConsecutiveDays(userId) {\n    return await this.model('signin').getConsecutiveDays(userId);\n  }\n\n  /**\n   * 计算连续签到天数（包含今天）\n   */\n  async calculateConsecutiveDays(userId, signDate) {\n    const consecutiveDays = await this.getConsecutiveDays(userId);\n    return consecutiveDays + 1; // 加上今天\n  }\n\n  /**\n   * 获取指定月份的签到记录\n   */\n  async getMonthSignRecords(userId, month) {\n    return await this.model('signin').getMonthSignRecords(userId, month);\n  }\n\n  /**\n   * 获取月度统计\n   */\n  async getMonthlyStats(userId, month) {\n    const [year, monthNum] = month.split('-');\n    const daysInMonth = new Date(year, monthNum, 0).getDate();\n\n    const signedRecords = await this.getMonthSignRecords(userId, month);\n    const signedDays = signedRecords.length;\n\n    return {\n      totalDays: daysInMonth,\n      signedDays: signedDays,\n      isFullAttendance: signedDays === daysInMonth\n    };\n  }\n\n  /**\n   * 计算积分奖励\n   */\n  calculatePoints(consecutiveDays) {\n    const basePoints = 5; // 基础积分\n    let bonusPoints = 0;  // 奖励积分\n\n    // 连续签到奖励\n    if (consecutiveDays >= 30) {\n      bonusPoints = 45; // 连续30天额外奖励45积分\n    } else if (consecutiveDays >= 15) {\n      bonusPoints = 15; // 连续15天额外奖励15积分\n    } else if (consecutiveDays >= 7) {\n      bonusPoints = 10; // 连续7天额外奖励10积分\n    } else if (consecutiveDays >= 3) {\n      bonusPoints = 5;  // 连续3天额外奖励5积分\n    }\n\n    return {\n      basePoints: basePoints,\n      bonusPoints: bonusPoints,\n      totalPoints: basePoints + bonusPoints\n    };\n  }\n\n  /**\n   * 获取奖励类型\n   */\n  getBonusType(consecutiveDays) {\n    if (consecutiveDays >= 30) return 'consecutive_30';\n    if (consecutiveDays >= 15) return 'consecutive_15';\n    if (consecutiveDays >= 7) return 'consecutive_7';\n    if (consecutiveDays >= 3) return 'consecutive_3';\n    return 'none';\n  }\n\n  /**\n   * 更新用户积分\n   */\n  async updateUserPoints(userId, earnedPoints) {\n    return await this.model('points').addUserPoints(userId, earnedPoints, 'signin', null, '每日签到');\n  }\n\n  /**\n   * 添加积分日志\n   */\n  async addPointsLog(userId, pointsChange, pointsType, sourceId, description) {\n    return await this.model('points').addUserPoints(userId, pointsChange, pointsType, sourceId, description);\n  }\n\n  /**\n   * 获取当前月份 YYYY-MM\n   */\n  getCurrentMonth() {\n    const now = new Date();\n    const year = now.getFullYear();\n    const month = String(now.getMonth() + 1).padStart(2, '0');\n    return `${year}-${month}`;\n  }\n\n  /**\n   * 获取当前日期 YYYY-MM-DD\n   */\n  getCurrentDate() {\n    const now = new Date();\n    const year = now.getFullYear();\n    const month = String(now.getMonth() + 1).padStart(2, '0');\n    const day = String(now.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n\n  /**\n   * 获取指定日期之前的日期\n   */\n  getDateBefore(dateStr, days) {\n    const date = new Date(dateStr);\n    date.setDate(date.getDate() - days);\n    \n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n\n\n};\n"]}