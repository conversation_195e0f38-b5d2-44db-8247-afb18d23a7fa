{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\index.js"], "names": ["Base", "require", "moment", "Jushuitan", "rp", "http", "module", "exports", "indexAction", "display", "appInfoAction", "banner", "model", "where", "enabled", "is_delete", "field", "order", "select", "notice", "channel", "is_channel", "parent_id", "sort_order", "categoryList", "is_show", "categoryItem", "categoryGoods", "category_id", "id", "goods_number", "is_on_sale", "is_index", "goodsList", "userId", "getLoginUserId", "cartCount", "user_id", "sum", "data", "success"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;AACA,MAAME,YAAYF,QAAQ,WAAR,CAAlB;AACA,MAAMG,KAAKH,QAAQ,iBAAR,CAAX;AACA,MAAMI,OAAOJ,QAAQ,MAAR,CAAb;AACAK,OAAOC,OAAP,GAAiB,cAAcP,IAAd,CAAmB;AAC1BQ,eAAN,GAAoB;AAAA;;AAAA;AAChB;AACA,mBAAO,MAAKC,OAAL,EAAP;AAFgB;AAGnB;AACKC,iBAAN,GAAsB;AAAA;;AAAA;AAClB,kBAAMC,SAAS,MAAM,OAAKC,KAAL,CAAW,IAAX,EAAiBC,KAAjB,CAAuB;AACxCC,yBAAS,CAD+B;AAExCC,2BAAW;AAF6B,aAAvB,EAGlBC,KAHkB,CAGZ,mCAHY,EAGyBC,KAHzB,CAG+B,gBAH/B,EAGiDC,MAHjD,EAArB;AAIA,kBAAMC,SAAS,MAAM,OAAKP,KAAL,CAAW,QAAX,EAAqBC,KAArB,CAA2B;AAC5CE,2BAAW;AADiC,aAA3B,EAElBC,KAFkB,CAEZ,SAFY,EAEDE,MAFC,EAArB;AAGA,kBAAME,UAAU,MAAM,OAAKR,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAC/CQ,4BAAY,CADmC;AAE/CC,2BAAW;AAFoC,aAA7B,EAGnBN,KAHmB,CAGb,6BAHa,EAGkBC,KAHlB,CAGwB;AAC1CM,4BAAY;AAD8B,aAHxB,EAKnBL,MALmB,EAAtB;AAMA,kBAAMM,eAAe,MAAM,OAAKZ,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AACpDS,2BAAW,CADyC;AAEpDG,yBAAS;AAF2C,aAA7B,EAGxBT,KAHwB,CAGlB,+CAHkB,EAG+BC,KAH/B,CAGqC;AAC5DM,4BAAY;AADgD,aAHrC,EAKxBL,MALwB,EAA3B;AAMN,iBAAK,MAAMQ,YAAX,IAA2BF,YAA3B,EAAyC;AACxC,sBAAMG,gBAAgB,MAAM,OAAKf,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AACrDe,iCAAaF,aAAaG,EAD2B;AAErDC,kCAAc,CAAC,IAAD,EAAO,CAAP,CAFuC;AAGrDC,gCAAY,CAHyC;AAIrDC,8BAAU,CAJ2C;AAKrDjB,+BAAW;AAL0C,iBAA1B,EAMzBC,KANyB,CAMnB,2DANmB,EAM0CC,KAN1C,CAMgD;AAC3EM,gCAAY;AAD+D,iBANhD,EAQzBL,MARyB,EAA5B;AASAQ,6BAAaO,SAAb,GAAyBN,aAAzB;AACA;AACD,kBAAMO,SAAS,MAAM,OAAKC,cAAL,EAArB;AACA,gBAAIC,YAAY,CAAhB;AACA,gBAAIF,UAAUA,SAAS,CAAvB,EAA0B;AACzBE,4BAAY,MAAM,OAAKxB,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC1CwB,6BAASH,MADiC;AAE1CnB,+BAAW;AAF+B,iBAAzB,EAGfuB,GAHe,CAGX,QAHW,CAAlB;AAIA;AACD,gBAAGF,aAAa,IAAhB,EAAqB;AACpBA,4BAAY,CAAZ;AACA;AACD,gBAAIG,OAAO;AACVnB,yBAASA,OADC;AAEVT,wBAAQA,MAFE;AAGVQ,wBAAQA,MAHE;AAIVK,8BAAcA,YAJJ;AAKVY,2BAAWA;AALD,aAAX;AAOM,mBAAO,OAAKI,OAAL,CAAaD,IAAb,CAAP;AAlDkB;AAmDrB;AAxD+B,CAApC", "file": "..\\..\\..\\src\\api\\controller\\index.js", "sourcesContent": ["const Base = require('./base.js');\n// const view = require('think-view');\nconst moment = require('moment');\nconst Jushuitan = require('jushuitan');\nconst rp = require('request-promise');\nconst http = require(\"http\");\nmodule.exports = class extends Base {\n    async indexAction() {\n        //auto render template file index_index.html\n        return this.display();\n    }\n    async appInfoAction() {\n        const banner = await this.model('ad').where({\n            enabled: 1,\n            is_delete: 0\n        }).field('link_type,goods_id,image_url,link').order('sort_order asc').select();\n        const notice = await this.model('notice').where({\n            is_delete: 0\n        }).field('content').select();\n        const channel = await this.model('category').where({\n            is_channel: 1,\n            parent_id: 0,\n        }).field('id,icon_url,name,sort_order').order({\n            sort_order: 'asc'\n        }).select();\n        const categoryList = await this.model('category').where({\n            parent_id: 0,\n            is_show: 1\n        }).field('id,name,img_url as banner, p_height as height').order({\n            sort_order: 'asc'\n        }).select();\n\t\tfor (const categoryItem of categoryList) {\n\t\t\tconst categoryGoods = await this.model('goods').where({\n\t\t\t\tcategory_id: categoryItem.id,\n\t\t\t\tgoods_number: ['>=', 0],\n\t\t\t\tis_on_sale: 1,\n\t\t\t\tis_index: 1,\n\t\t\t\tis_delete: 0\n\t\t\t}).field('id,list_pic_url,is_new,goods_number,name,min_retail_price').order({\n\t\t\t\tsort_order: 'asc'\n\t\t\t}).select();\n\t\t\tcategoryItem.goodsList = categoryGoods;\n\t\t}\n\t\tconst userId = await this.getLoginUserId();\n\t\tlet cartCount = 0;\n\t\tif (userId && userId > 0) {\n\t\t\tcartCount = await this.model('cart').where({\n\t\t\t\tuser_id: userId,\n\t\t\t\tis_delete: 0\n\t\t\t}).sum('number');\n\t\t}\n\t\tif(cartCount == null){\n\t\t\tcartCount = 0;\n\t\t}\n\t\tlet data = {\n\t\t\tchannel: channel,\n\t\t\tbanner: banner,\n\t\t\tnotice: notice,\n\t\t\tcategoryList: categoryList,\n\t\t\tcartCount: cartCount,\n\t\t}\n        return this.success(data);\n    }\n};"]}