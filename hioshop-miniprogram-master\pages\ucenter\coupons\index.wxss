/* pages/ucenter/coupons/index.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 标签页样式 */
.tabs {
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  position: relative;
  color: #666;
  font-size: 28rpx;
}

.tab-item.active {
  color: #ff6b35;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #ff6b35;
  border-radius: 2rpx;
}

.count {
  background-color: #ff6b35;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  margin-left: 8rpx;
  min-width: 32rpx;
  text-align: center;
}

/* 优惠券列表 */
.coupon-list {
  padding: 20rpx;
  height: calc(100vh - 120rpx);
}

.loading {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 优惠券项样式 - 高级版 */
.coupon-item {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 22rpx; /* 减少10%: 24rpx * 0.9 = 21.6rpx ≈ 22rpx */
  margin-bottom: 22rpx; /* 减少10%: 24rpx * 0.9 = 21.6rpx ≈ 22rpx */
  position: relative;
  overflow: hidden;
  box-shadow: 0 7rpx 29rpx rgba(0, 0, 0, 0.06), 0 2rpx 7rpx rgba(0, 0, 0, 0.04); /* 减少10%: 8rpx * 0.9 = 7.2rpx ≈ 7rpx, 32rpx * 0.9 = 28.8rpx ≈ 29rpx, 8rpx * 0.9 = 7.2rpx ≈ 7rpx */
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.coupon-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, #ff6b35, #ff8f5a, #ffa726);
  opacity: 0.8;
}

.coupon-item.used,
.coupon-item.expired {
  opacity: 0.5;
  filter: grayscale(0.3);
  transform: scale(0.98);
}

.coupon-content {
  display: flex;
  padding: 29rpx 25rpx; /* 减少10%: 32rpx * 0.9 = 28.8rpx ≈ 29rpx, 28rpx * 0.9 = 25.2rpx ≈ 25rpx */
  position: relative;
}

/* 左侧优惠信息 - 高级版 */
.coupon-left {
  width: 198rpx; /* 减少10%: 220rpx * 0.9 = 198rpx */
  height: 144rpx; /* 减少10%: 160rpx * 0.9 = 144rpx */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff6b35 0%, #ff8f5a 50%, #ffa726 100%);
  color: #fff;
  border-radius: 18rpx; /* 减少10%: 20rpx * 0.9 = 18rpx */
  margin-right: 29rpx; /* 减少10%: 32rpx * 0.9 = 28.8rpx ≈ 29rpx */
  position: relative;
  box-shadow: 0 5rpx 18rpx rgba(255, 107, 53, 0.3); /* 减少10%: 6rpx * 0.9 = 5.4rpx ≈ 5rpx, 20rpx * 0.9 = 18rpx */
  overflow: hidden;
}

.coupon-left::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: shimmer 3s infinite;
}

.coupon-left::after {
  content: '';
  position: absolute;
  right: -14rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 28rpx;
  height: 28rpx;
  background: radial-gradient(circle, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 50%;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

@keyframes shimmer {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.discount-amount {
  display: flex;
  align-items: baseline;
  margin-bottom: 7rpx; /* 减少10%: 8rpx * 0.9 = 7.2rpx ≈ 7rpx */
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.symbol {
  font-size: 28rpx;
  margin-right: 6rpx;
  font-weight: 600;
  opacity: 0.95;
}

.amount {
  font-size: 50rpx; /* 减少10%: 56rpx * 0.9 = 50.4rpx ≈ 50rpx */
  font-weight: 700;
  letter-spacing: -1rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.condition {
  font-size: 24rpx;
  opacity: 0.9;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

/* 右侧详细信息 - 高级版 */
.coupon-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-left: 8rpx;
}

.coupon-name {
  font-size: 31rpx; /* 减少10%: 34rpx * 0.9 = 30.6rpx ≈ 31rpx */
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 11rpx; /* 减少10%: 12rpx * 0.9 = 10.8rpx ≈ 11rpx */
  line-height: 1.3;
  letter-spacing: 0.5rpx;
}

.coupon-type {
  font-size: 20rpx; /* 减少10%: 22rpx * 0.9 = 19.8rpx ≈ 20rpx */
  color: #ff6b35;
  background: linear-gradient(135deg, #fff2ee 0%, #ffeee6 100%);
  padding: 5rpx 14rpx; /* 减少10%: 6rpx * 0.9 = 5.4rpx ≈ 5rpx, 16rpx * 0.9 = 14.4rpx ≈ 14rpx */
  border-radius: 18rpx; /* 减少10%: 20rpx * 0.9 = 18rpx */
  align-self: flex-start;
  margin-bottom: 14rpx; /* 减少10%: 16rpx * 0.9 = 14.4rpx ≈ 14rpx */
  font-weight: 500;
  border: 1rpx solid rgba(255, 107, 53, 0.1);
  box-shadow: 0 2rpx 7rpx rgba(255, 107, 53, 0.1); /* 减少10%: 8rpx * 0.9 = 7.2rpx ≈ 7rpx */
}

.time-info {
  font-size: 23rpx; /* 减少10%: 26rpx * 0.9 = 23.4rpx ≈ 23rpx */
  color: #666;
  margin-bottom: 14rpx; /* 减少10%: 16rpx * 0.9 = 14.4rpx ≈ 14rpx */
  font-weight: 400;
  display: flex;
  align-items: center;
}

.time-info::before {
  content: '⏰';
  margin-right: 8rpx;
  font-size: 24rpx;
}

/* 操作按钮 - 高级版 */
.coupon-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.use-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8f5a 50%, #ffa726 100%);
  color: #fff;
  border: none;
  border-radius: 25rpx; /* 减少10%: 28rpx * 0.9 = 25.2rpx ≈ 25rpx */
  padding: 14rpx 32rpx; /* 减少10%: 16rpx * 0.9 = 14.4rpx ≈ 14rpx, 36rpx * 0.9 = 32.4rpx ≈ 32rpx */
  font-size: 25rpx; /* 减少10%: 28rpx * 0.9 = 25.2rpx ≈ 25rpx */
  font-weight: 600;
  box-shadow: 0 5rpx 14rpx rgba(255, 107, 53, 0.3); /* 减少10%: 6rpx * 0.9 = 5.4rpx ≈ 5rpx, 16rpx * 0.9 = 14.4rpx ≈ 14rpx */
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: 0.5rpx;
}

.use-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.use-btn:active::before {
  left: 100%;
}

.use-btn::after {
  border: none;
}

.use-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.4);
}

/* 状态标识 - 高级版 */
.status-badge {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.status-badge.used {
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.9) 0%, rgba(115, 209, 61, 0.9) 100%);
  color: #fff;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.status-badge.expired {
  background: linear-gradient(135deg, rgba(255, 77, 79, 0.9) 0%, rgba(255, 120, 117, 0.9) 100%);
  color: #fff;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.go-get-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8f5a 100%);
  color: #fff;
  border: none;
  border-radius: 24rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.go-get-btn::after {
  border: none;
}

/* 高级视觉效果 */
.coupon-item:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1), 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

/* 优惠券装饰元素 */
.coupon-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 252rpx; /* 左侧区域宽度 + 间距 */
  transform: translateY(-50%);
  width: 2rpx;
  height: 60rpx;
  background: linear-gradient(to bottom, transparent, #e9ecef, transparent);
}

/* 新用户券特殊样式 */
.coupon-item.newuser {
  background: linear-gradient(135deg, #fff8f0 0%, #ffffff 100%);
  border: 2rpx solid #ffa726;
}

.coupon-item.newuser .coupon-left {
  background: linear-gradient(135deg, #ffa726 0%, #ffb74d 50%, #ffc947 100%);
  box-shadow: 0 6rpx 20rpx rgba(255, 167, 38, 0.4);
}

.coupon-item.newuser::before {
  background: linear-gradient(90deg, #ffa726, #ffb74d, #ffc947);
}

/* 满减券特殊样式 */
.coupon-item.full-reduction {
  background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);
  border: 2rpx solid #1890ff;
}

.coupon-item.full-reduction .coupon-left {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 50%, #69c0ff 100%);
  box-shadow: 0 6rpx 20rpx rgba(24, 144, 255, 0.3);
}

.coupon-item.full-reduction::before {
  background: linear-gradient(90deg, #1890ff, #40a9ff, #69c0ff);
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.coupon-item {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.coupon-item:nth-child(1) { animation-delay: 0.1s; }
.coupon-item:nth-child(2) { animation-delay: 0.2s; }
.coupon-item:nth-child(3) { animation-delay: 0.3s; }
.coupon-item:nth-child(4) { animation-delay: 0.4s; }
.coupon-item:nth-child(5) { animation-delay: 0.5s; }
