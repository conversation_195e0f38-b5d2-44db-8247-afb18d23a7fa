{"name": "hi<PERSON><PERSON>", "description": "hioshop - open source mini program shop", "version": "1.0.0", "scripts": {"start": "node start.js", "start:dev": "cross-env NODE_ENV=development node start.js", "start:prod": "cross-env NODE_ENV=production node start.js", "compile": "node compile.js", "compile:watch": "babel --no-babelrc src/ --presets think-node --out-dir app/ --watch", "clean": "rimraf app/", "build": "npm run clean && npm run compile", "check": "node check-env.js", "fix": "npm run build && npm run start", "lint": "eslint src/", "lint-fix": "eslint --fix src/"}, "dependencies": {"gm": "^1.23.0", "jsonwebtoken": "^8.5.1", "jushuitan": "^1.0.2", "kcors": "^2.2.1", "lodash": "^4.17.4", "md5": "^2.2.1", "mime-types": "^2.1.24", "moment": "^2.18.1", "mysql2": "^3.14.1", "nanoid": "^2.1.1", "node-wget": "^0.4.3", "pinyin": "^2.9.0", "qiniu": "7.2.1", "querystring": "^0.2.0", "request": "^2.81.0", "request-promise": "^4.2.6", "think-cache": "^1.0.0", "think-cache-file": "^1.0.8", "think-logger3": "^1.0.0", "think-model": "^1.0.0", "think-model-mysql": "^1.0.0", "think-view": "^1.0.11", "think-view-nunjucks": "^1.0.7", "thinkjs": "^3.0.0", "weixinpay": "^1.0.12", "xml2js": "^0.4.19"}, "resolutions": {"address": "1.2.2", "utility": "1.16.3"}, "devDependencies": {"babel-cli": "^6.24.1", "babel-preset-think-node": "^1.0.0", "node-notifier": "^5.0.2", "think-watcher": "^3.0.0", "think-inspect": "0.0.2", "think-babel": "^1.0.3", "eslint": "^4.2.0", "eslint-config-think": "^1.0.0", "rimraf": "^3.0.2", "cross-env": "^7.0.3"}, "repository": "", "license": "MIT", "engines": {"node": ">=6.0.0"}, "readmeFilename": "README.md"}