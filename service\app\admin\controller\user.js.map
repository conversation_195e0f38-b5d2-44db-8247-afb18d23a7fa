{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\user.js"], "names": ["Base", "require", "moment", "module", "exports", "indexAction", "page", "get", "size", "nickname", "buffer", "<PERSON><PERSON><PERSON>", "from", "toString", "model", "data", "where", "field", "order", "countSelect", "item", "register_time", "unix", "format", "last_login_time", "avatar", "startsWith", "isPromoter", "isDistributor", "promoter", "query", "id", "length", "error", "console", "log", "message", "distributor", "is_distributor", "info", "userData", "success", "infoAction", "find", "datainfoAction", "orderSum", "user_id", "order_type", "is_delete", "count", "orderDone", "order_status", "orderMoney", "sum", "cartSum", "addressAction", "addr", "province_name", "province_id", "getField", "city_name", "city_id", "district_name", "district_id", "full_region", "address", "saveaddressAction", "post", "name", "mobile", "addOptions", "province", "city", "district", "update", "cartdataAction", "add_time", "footAction", "alias", "join", "table", "as", "on", "orderAction", "goodsList", "order_id", "select", "goodsCount", "for<PERSON>ach", "v", "number", "postscript", "order_status_text", "getOrderStatusText", "button_text", "getOrderBtnText", "orderInfo", "statusText", "updateInfoAction", "updateNameAction", "updateMobileAction", "storeAction", "isPost", "values", "is_show", "is_new", "add", "destoryAction", "limit", "delete", "addressesAction", "userId", "addresses", "county", "address_detail", "cartStatsAction", "totalItems", "cartItems", "totalAmount", "goods", "goods_id", "parseFloat", "retail_price", "parseInt", "stats", "toFixed"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;AACAE,OAAOC,OAAP,GAAiB,cAAcJ,IAAd,CAAmB;AAChC;;;;AAIMK,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAMC,OAAO,MAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,MAAKD,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,gBAAIE,WAAW,MAAKF,GAAL,CAAS,UAAT,KAAwB,EAAvC;AACA,kBAAMG,SAASC,OAAOC,IAAP,CAAYH,QAAZ,CAAf;AACAA,uBAAWC,OAAOG,QAAP,CAAgB,QAAhB,CAAX;AACA,kBAAMC,QAAQ,MAAKA,KAAL,CAAW,MAAX,CAAd;AACA,kBAAMC,OAAO,MAAMD,MAAME,KAAN,CAAY;AAC3BP,0BAAU,CAAC,MAAD,EAAU,IAAGA,QAAS,GAAtB;AADiB,aAAZ,EAEhBQ,KAFgB,CAEV,gEAFU,EAEwDC,KAFxD,CAE8D,CAAC,SAAD,CAF9D,EAE2EZ,IAF3E,CAEgFA,IAFhF,EAEsFE,IAFtF,EAE4FW,WAF5F,EAAnB;;AAIA,iBAAK,MAAMC,IAAX,IAAmBL,KAAKA,IAAxB,EAA8B;AAC1BK,qBAAKC,aAAL,GAAqBnB,OAAOoB,IAAP,CAAYF,KAAKC,aAAjB,EAAgCE,MAAhC,CAAuC,qBAAvC,CAArB;AACAH,qBAAKI,eAAL,GAAuBtB,OAAOoB,IAAP,CAAYF,KAAKI,eAAjB,EAAkCD,MAAlC,CAAyC,qBAAzC,CAAvB;AACAH,qBAAKX,QAAL,GAAgBE,OAAOC,IAAP,CAAYQ,KAAKX,QAAjB,EAA2B,QAA3B,EAAqCI,QAArC,EAAhB;;AAEA;AACA,oBAAI,CAACO,KAAKK,MAAN,IAAgBL,KAAKK,MAAL,KAAgB,EAAhC,IAAsCL,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,WAAvB,CAA1C,EAA+E;AAC3E;AACAN,yBAAKK,MAAL,GAAc,2DAAd;AACH,iBAHD,MAGO,IAAI,CAACL,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,SAAvB,CAAD,IAAsC,CAACN,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,UAAvB,CAA3C,EAA+E;AAClF;AACAN,yBAAKK,MAAL,GAAe,yBAAwBL,KAAKK,MAAO,EAAnD;AACH;;AAED;AACA,oBAAIE,aAAa,KAAjB;AACA,oBAAIC,gBAAgB,KAApB;;AAEA,oBAAI;AACA;AACA,0BAAMC,WAAW,MAAM,MAAKf,KAAL,GAAagB,KAAb,CAAoB;;sCAErBV,KAAKW,EAAG;;iBAFP,CAAvB;AAKAJ,iCAAaE,YAAYA,SAASG,MAAT,GAAkB,CAA3C;AACH,iBARD,CAQE,OAAOC,KAAP,EAAc;AACZC,4BAAQC,GAAR,CAAY,WAAZ,EAAyBF,MAAMG,OAA/B;AACH;;AAED,oBAAI;AACA;AACA,0BAAMC,cAAc,MAAM,MAAKvB,KAAL,GAAagB,KAAb,CAAoB;;sCAExBV,KAAKW,EAAG;;iBAFJ,CAA1B;AAKAH,oCAAgBS,eAAeA,YAAYL,MAAZ,GAAqB,CAApD;AACH,iBARD,CAQE,OAAOC,KAAP,EAAc;AACZC,4BAAQC,GAAR,CAAY,WAAZ,EAAyBF,MAAMG,OAA/B;AACH;;AAED;AACAhB,qBAAKkB,cAAL,GAAsBX,cAAcC,aAApC;AACH;AACD,gBAAIW,OAAO;AACPC,0BAAUzB;AADH,aAAX;AAGA,mBAAO,MAAK0B,OAAL,CAAaF,IAAb,CAAP;AA3DgB;AA4DnB;AACKG,cAAN,GAAmB;AAAA;;AAAA;AACf,kBAAMX,KAAK,OAAKxB,GAAL,CAAS,IAAT,CAAX;AACA,kBAAMO,QAAQ,OAAKA,KAAL,CAAW,MAAX,CAAd;AACA,gBAAIyB,OAAO,MAAMzB,MAAME,KAAN,CAAY;AACzBe,oBAAIA;AADqB,aAAZ,EAEdY,IAFc,EAAjB;AAGAJ,iBAAKlB,aAAL,GAAqBnB,OAAOoB,IAAP,CAAYiB,KAAKlB,aAAjB,EAAgCE,MAAhC,CAAuC,qBAAvC,CAArB;AACAgB,iBAAKf,eAAL,GAAuBtB,OAAOoB,IAAP,CAAYiB,KAAKf,eAAjB,EAAkCD,MAAlC,CAAyC,qBAAzC,CAAvB;AACAgB,iBAAK9B,QAAL,GAAgBE,OAAOC,IAAP,CAAY2B,KAAK9B,QAAjB,EAA2B,QAA3B,EAAqCI,QAArC,EAAhB;;AAEA;AACA,gBAAI,CAAC0B,KAAKd,MAAN,IAAgBc,KAAKd,MAAL,KAAgB,EAAhC,IAAsCc,KAAKd,MAAL,CAAYC,UAAZ,CAAuB,WAAvB,CAA1C,EAA+E;AAC3E;AACAa,qBAAKd,MAAL,GAAc,2DAAd;AACH,aAHD,MAGO,IAAI,CAACc,KAAKd,MAAL,CAAYC,UAAZ,CAAuB,SAAvB,CAAD,IAAsC,CAACa,KAAKd,MAAL,CAAYC,UAAZ,CAAuB,UAAvB,CAA3C,EAA+E;AAClF;AACAa,qBAAKd,MAAL,GAAe,yBAAwBc,KAAKd,MAAO,EAAnD;AACH;;AAED;AACA,gBAAIE,aAAa,KAAjB;AACA,gBAAIC,gBAAgB,KAApB;;AAEA,gBAAI;AACA;AACA,sBAAMC,WAAW,MAAM,OAAKf,KAAL,GAAagB,KAAb,CAAoB;;kCAErBS,KAAKR,EAAG;;aAFP,CAAvB;AAKAJ,6BAAaE,YAAYA,SAASG,MAAT,GAAkB,CAA3C;AACH,aARD,CAQE,OAAOC,KAAP,EAAc;AACZC,wBAAQC,GAAR,CAAY,WAAZ,EAAyBF,MAAMG,OAA/B;AACH;;AAED,gBAAI;AACA;AACA,sBAAMC,cAAc,MAAM,OAAKvB,KAAL,GAAagB,KAAb,CAAoB;;kCAExBS,KAAKR,EAAG;;aAFJ,CAA1B;AAKAH,gCAAgBS,eAAeA,YAAYL,MAAZ,GAAqB,CAApD;AACH,aARD,CAQE,OAAOC,KAAP,EAAc;AACZC,wBAAQC,GAAR,CAAY,WAAZ,EAAyBF,MAAMG,OAA/B;AACH;;AAED;AACAG,iBAAKD,cAAL,GAAsBX,cAAcC,aAApC;;AAEA,mBAAO,OAAKa,OAAL,CAAaF,IAAb,CAAP;AAlDe;AAmDlB;AACKK,kBAAN,GAAuB;AAAA;;AAAA;AACnB,kBAAMb,KAAK,OAAKxB,GAAL,CAAS,IAAT,CAAX;AACA,gBAAIgC,OAAO,EAAX;AACAA,iBAAKM,QAAL,GAAgB,MAAM,OAAK/B,KAAL,CAAW,OAAX,EAAoBE,KAApB,CAA0B;AAC5C8B,yBAASf,EADmC;AAE5CgB,4BAAY,CAAC,GAAD,EAAM,CAAN,CAFgC;AAG5CC,2BAAW;AAHiC,aAA1B,EAInBC,KAJmB,EAAtB;AAKAV,iBAAKW,SAAL,GAAiB,MAAM,OAAKpC,KAAL,CAAW,OAAX,EAAoBE,KAApB,CAA0B;AAC7C8B,yBAASf,EADoC;AAE7CoB,8BAAc,CAAC,IAAD,EAAO,aAAP,CAF+B;AAG7CJ,4BAAY,CAAC,GAAD,EAAM,CAAN,CAHiC;AAI7CC,2BAAW;AAJkC,aAA1B,EAKpBC,KALoB,EAAvB;AAMAV,iBAAKa,UAAL,GAAkB,MAAM,OAAKtC,KAAL,CAAW,OAAX,EAAoBE,KAApB,CAA0B;AAC9C8B,yBAASf,EADqC;AAE9CoB,8BAAc,CAAC,IAAD,EAAO,aAAP,CAFgC;AAG9CJ,4BAAY,CAAC,GAAD,EAAM,CAAN,CAHkC;AAI9CC,2BAAW;AAJmC,aAA1B,EAKrBK,GALqB,CAKjB,cALiB,CAAxB;AAMAd,iBAAKe,OAAL,GAAe,MAAM,OAAKxC,KAAL,CAAW,MAAX,EAAmBE,KAAnB,CAAyB;AAC1C8B,yBAASf,EADiC;AAE1CiB,2BAAW;AAF+B,aAAzB,EAGlBK,GAHkB,CAGd,QAHc,CAArB;AAIA,mBAAO,OAAKZ,OAAL,CAAaF,IAAb,CAAP;AAxBmB;AAyBtB;AACKgB,iBAAN,GAAsB;AAAA;;AAAA;AAClB,kBAAMxB,KAAK,OAAKxB,GAAL,CAAS,IAAT,CAAX;AACA,kBAAMD,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,OAAKD,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,gBAAIiD,OAAO,MAAM,OAAK1C,KAAL,CAAW,SAAX,EAAsBE,KAAtB,CAA4B;AACzC8B,yBAASf;AADgC,aAA5B,EAEdzB,IAFc,CAETA,IAFS,EAEHE,IAFG,EAEGW,WAFH,EAAjB;AAGA,iBAAK,MAAMC,IAAX,IAAmBoC,KAAKzC,IAAxB,EAA8B;AAC1B,oBAAI0C,gBAAgB,MAAM,OAAK3C,KAAL,CAAW,QAAX,EAAqBE,KAArB,CAA2B;AACjDe,wBAAIX,KAAKsC;AADwC,iBAA3B,EAEvBC,QAFuB,CAEd,MAFc,EAEN,IAFM,CAA1B;AAGA,oBAAIC,YAAY,MAAM,OAAK9C,KAAL,CAAW,QAAX,EAAqBE,KAArB,CAA2B;AAC7Ce,wBAAIX,KAAKyC;AADoC,iBAA3B,EAEnBF,QAFmB,CAEV,MAFU,EAEF,IAFE,CAAtB;AAGA,oBAAIG,gBAAgB,MAAM,OAAKhD,KAAL,CAAW,QAAX,EAAqBE,KAArB,CAA2B;AACjDe,wBAAIX,KAAK2C;AADwC,iBAA3B,EAEvBJ,QAFuB,CAEd,MAFc,EAEN,IAFM,CAA1B;AAGAvC,qBAAK4C,WAAL,GAAmBP,gBAAgBG,SAAhB,GAA4BE,aAA5B,GAA4C1C,KAAK6C,OAApE;AACH;AACD,mBAAO,OAAKxB,OAAL,CAAae,IAAb,CAAP;AAnBkB;AAoBrB;AACKU,qBAAN,GAA0B;AAAA;;AAAA;AACtB,kBAAMnC,KAAK,OAAKoC,IAAL,CAAU,IAAV,CAAX;AACA,kBAAMrB,UAAU,OAAKqB,IAAL,CAAU,SAAV,CAAhB;AACA,kBAAMC,OAAO,OAAKD,IAAL,CAAU,MAAV,CAAb;AACA,kBAAME,SAAS,OAAKF,IAAL,CAAU,QAAV,CAAf;AACA,kBAAMF,UAAU,OAAKE,IAAL,CAAU,SAAV,CAAhB;AACA,kBAAMG,aAAa,OAAKH,IAAL,CAAU,YAAV,CAAnB;AACA,kBAAMI,WAAWD,WAAW,CAAX,CAAjB;AACA,kBAAME,OAAOF,WAAW,CAAX,CAAb;AACA,kBAAMG,WAAWH,WAAW,CAAX,CAAjB;AACA,gBAAI/B,OAAO;AACP6B,sBAAMA,IADC;AAEPC,wBAAQA,MAFD;AAGPJ,yBAASA,OAHF;AAIPP,6BAAaa,QAJN;AAKPR,6BAAaU,QALN;AAMPZ,yBAASW;AANF,aAAX;AAQA,kBAAM,OAAK1D,KAAL,CAAW,SAAX,EAAsBE,KAAtB,CAA4B;AAC9B8B,yBAASA,OADqB;AAE9Bf,oBAAIA;AAF0B,aAA5B,EAGH2C,MAHG,CAGInC,IAHJ,CAAN;AAIA,mBAAO,OAAKE,OAAL,EAAP;AAtBsB;AAuBzB;AACKkC,kBAAN,GAAuB;AAAA;;AAAA;AACnB,kBAAM5C,KAAK,OAAKxB,GAAL,CAAS,IAAT,CAAX;AACA,kBAAMD,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,OAAKD,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,kBAAMO,QAAQ,OAAKA,KAAL,CAAW,MAAX,CAAd;AACA,kBAAMC,OAAO,MAAMD,MAAME,KAAN,CAAY;AAC3B8B,yBAASf;AADkB,aAAZ,EAEhBb,KAFgB,CAEV,CAAC,eAAD,CAFU,EAESZ,IAFT,CAEcA,IAFd,EAEoBE,IAFpB,EAE0BW,WAF1B,EAAnB;AAGA,iBAAK,MAAMC,IAAX,IAAmBL,KAAKA,IAAxB,EAA8B;AAC1BK,qBAAKwD,QAAL,GAAgB1E,OAAOoB,IAAP,CAAYF,KAAKwD,QAAjB,EAA2BrD,MAA3B,CAAkC,qBAAlC,CAAhB;AACH;AACD,mBAAO,OAAKkB,OAAL,CAAa1B,IAAb,CAAP;AAXmB;AAYtB;AACK8D,cAAN,GAAmB;AAAA;;AAAA;AACf,kBAAM9C,KAAK,OAAKxB,GAAL,CAAS,IAAT,CAAX;AACA,kBAAMD,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,OAAKD,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,kBAAMO,QAAQ,OAAKA,KAAL,CAAW,WAAX,CAAd;AACA,kBAAMC,OAAO,MAAMD,MAAMgE,KAAN,CAAY,GAAZ,EAAiBC,IAAjB,CAAsB;AACrCC,uBAAO,OAD8B;AAErCD,sBAAM,MAF+B;AAGrCE,oBAAI,GAHiC;AAIrCC,oBAAI,CAAC,YAAD,EAAe,MAAf;AAJiC,aAAtB,EAKhBlE,KALgB,CAKV;AACL8B,yBAASf;AADJ,aALU,EAOhBzB,IAPgB,CAOXA,IAPW,EAOLE,IAPK,EAOCW,WAPD,EAAnB;AAQAe,oBAAQC,GAAR,CAAYpB,IAAZ;AACA,mBAAO,OAAK0B,OAAL,CAAa1B,IAAb,CAAP;AAde;AAelB;AACKoE,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAM7E,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,OAAKD,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,kBAAMuC,UAAU,OAAKvC,GAAL,CAAS,IAAT,CAAhB;AACA,kBAAMO,QAAQ,OAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMC,OAAO,MAAMD,MAAME,KAAN,CAAY;AAC3B8B,yBAASA,OADkB;AAE3BC,4BAAY,CAAC,GAAD,EAAM,CAAN;AAFe,aAAZ,EAGhB7B,KAHgB,CAGV,CAAC,SAAD,CAHU,EAGGZ,IAHH,CAGQA,IAHR,EAGcE,IAHd,EAGoBW,WAHpB,EAAnB;AAIAe,oBAAQC,GAAR,CAAYpB,KAAKkC,KAAjB;AACA,iBAAK,MAAM7B,IAAX,IAAmBL,KAAKA,IAAxB,EAA8B;AAC1BK,qBAAKgE,SAAL,GAAiB,MAAM,OAAKtE,KAAL,CAAW,aAAX,EAA0BG,KAA1B,CAAgC,0EAAhC,EAA4GD,KAA5G,CAAkH;AACrIqE,8BAAUjE,KAAKW,EADsH;AAErIiB,+BAAW;AAF0H,iBAAlH,EAGpBsC,MAHoB,EAAvB;AAIAlE,qBAAKmE,UAAL,GAAkB,CAAlB;AACAnE,qBAAKgE,SAAL,CAAeI,OAAf,CAAuB,aAAK;AACxBpE,yBAAKmE,UAAL,IAAmBE,EAAEC,MAArB;AACH,iBAFD;AAGA,oBAAIjC,gBAAgB,MAAM,OAAK3C,KAAL,CAAW,QAAX,EAAqBE,KAArB,CAA2B;AACjDe,wBAAIX,KAAKmD;AADwC,iBAA3B,EAEvBZ,QAFuB,CAEd,MAFc,EAEN,IAFM,CAA1B;AAGA,oBAAIC,YAAY,MAAM,OAAK9C,KAAL,CAAW,QAAX,EAAqBE,KAArB,CAA2B;AAC7Ce,wBAAIX,KAAKoD;AADoC,iBAA3B,EAEnBb,QAFmB,CAEV,MAFU,EAEF,IAFE,CAAtB;AAGA,oBAAIG,gBAAgB,MAAM,OAAKhD,KAAL,CAAW,QAAX,EAAqBE,KAArB,CAA2B;AACjDe,wBAAIX,KAAKqD;AADwC,iBAA3B,EAEvBd,QAFuB,CAEd,MAFc,EAEN,IAFM,CAA1B;AAGAvC,qBAAK4C,WAAL,GAAmBP,gBAAgBG,SAAhB,GAA4BE,aAA/C;AACA1C,qBAAKuE,UAAL,GAAkBhF,OAAOC,IAAP,CAAYQ,KAAKuE,UAAjB,EAA6B,QAA7B,EAAuC9E,QAAvC,EAAlB;AACAO,qBAAKwD,QAAL,GAAgB1E,OAAOoB,IAAP,CAAYF,KAAKwD,QAAjB,EAA2BrD,MAA3B,CAAkC,qBAAlC,CAAhB;AACAH,qBAAKwE,iBAAL,GAAyB,MAAM,OAAK9E,KAAL,CAAW,OAAX,EAAoB+E,kBAApB,CAAuCzE,KAAKW,EAA5C,CAA/B;AACAX,qBAAK0E,WAAL,GAAmB,MAAM,OAAKhF,KAAL,CAAW,OAAX,EAAoBiF,eAApB,CAAoC3E,KAAKW,EAAzC,CAAzB;AACH;AACD,mBAAO,OAAKU,OAAL,CAAa1B,IAAb,CAAP;AAlCgB;AAmCnB;AACK8E,sBAAN,CAAyBG,SAAzB,EAAoC;AAAA;AAChC,gBAAIC,aAAa,KAAjB;AACA,oBAAQD,UAAU7C,YAAlB;AACI,qBAAK,GAAL;AACI8C,iCAAa,KAAb;AACA;AACJ,qBAAK,GAAL;AACIA,iCAAa,MAAb;AACA;AACJ,qBAAK,GAAL;AACIA,iCAAa,MAAb,CADJ,CACyB;AACrB;AACJ,qBAAK,GAAL;AACIA,iCAAa,KAAb;AACA;AACJ,qBAAK,GAAL;AACIA,iCAAa,KAAb;AACA;AACJ,qBAAK,GAAL;AACIA,iCAAa,KAAb;AACA;AACJ,qBAAK,GAAL;AACIA,iCAAa,KAAb;AACA;AACJ,qBAAK,GAAL;AACIA,iCAAa,KAAb;AACA;AACJ,qBAAK,GAAL;AACIA,iCAAa,KAAb;AACA;AACJ,qBAAK,GAAL;AACIA,iCAAa,KAAb,CADJ,CACwB;AACpB;AACJ,qBAAK,GAAL;AACIA,iCAAa,MAAb,CADJ,CACyB;AACrB;AACJ,qBAAK,GAAL;AACIA,iCAAa,OAAb;AACA;AACJ,qBAAK,GAAL;AACIA,iCAAa,KAAb,CADJ,CACwB;AACpB;AAvCR;AAyCA,mBAAOA,UAAP;AA3CgC;AA4CnC;AACKC,oBAAN,GAAyB;AAAA;;AAAA;AACrB,kBAAMnE,KAAK,OAAKoC,IAAL,CAAU,IAAV,CAAX;AACA,gBAAI1D,WAAW,OAAK0D,IAAL,CAAU,UAAV,CAAf;AACA,kBAAMzD,SAASC,OAAOC,IAAP,CAAYH,QAAZ,CAAf;AACAA,uBAAWC,OAAOG,QAAP,CAAgB,QAAhB,CAAX;AACA,kBAAMC,QAAQ,OAAKA,KAAL,CAAW,MAAX,CAAd;AACA,kBAAMC,OAAO,MAAMD,MAAME,KAAN,CAAY;AAC3Be,oBAAIA;AADuB,aAAZ,EAEhB2C,MAFgB,CAET;AACNjE,0BAAUA;AADJ,aAFS,CAAnB;AAKA,mBAAO,OAAKgC,OAAL,CAAa1B,IAAb,CAAP;AAXqB;AAYxB;AACKoF,oBAAN,GAAyB;AAAA;;AAAA;AACrB,kBAAMpE,KAAK,QAAKoC,IAAL,CAAU,IAAV,CAAX;AACA,kBAAMC,OAAO,QAAKD,IAAL,CAAU,MAAV,CAAb;AACA,kBAAMrD,QAAQ,QAAKA,KAAL,CAAW,MAAX,CAAd;AACA,kBAAMC,OAAO,MAAMD,MAAME,KAAN,CAAY;AAC3Be,oBAAIA;AADuB,aAAZ,EAEhB2C,MAFgB,CAET;AACNN,sBAAMA;AADA,aAFS,CAAnB;AAKA,mBAAO,QAAK3B,OAAL,CAAa1B,IAAb,CAAP;AATqB;AAUxB;AACKqF,sBAAN,GAA2B;AAAA;;AAAA;AACvB,kBAAMrE,KAAK,QAAKoC,IAAL,CAAU,IAAV,CAAX;AACA,kBAAME,SAAS,QAAKF,IAAL,CAAU,QAAV,CAAf;AACA,kBAAMrD,QAAQ,QAAKA,KAAL,CAAW,MAAX,CAAd;AACA,kBAAMC,OAAO,MAAMD,MAAME,KAAN,CAAY;AAC3Be,oBAAIA;AADuB,aAAZ,EAEhB2C,MAFgB,CAET;AACNL,wBAAQA;AADF,aAFS,CAAnB;AAKA,mBAAO,QAAK5B,OAAL,CAAa1B,IAAb,CAAP;AATuB;AAU1B;AACKsF,eAAN,GAAoB;AAAA;;AAAA;AAChB,gBAAI,CAAC,QAAKC,MAAV,EAAkB;AACd,uBAAO,KAAP;AACH;AACD,kBAAMC,SAAS,QAAKpC,IAAL,EAAf;AACA,kBAAMpC,KAAK,QAAKoC,IAAL,CAAU,IAAV,CAAX;AACA,kBAAMrD,QAAQ,QAAKA,KAAL,CAAW,MAAX,CAAd;AACAyF,mBAAOC,OAAP,GAAiBD,OAAOC,OAAP,GAAiB,CAAjB,GAAqB,CAAtC;AACAD,mBAAOE,MAAP,GAAgBF,OAAOE,MAAP,GAAgB,CAAhB,GAAoB,CAApC;AACA,gBAAI1E,KAAK,CAAT,EAAY;AACR,sBAAMjB,MAAME,KAAN,CAAY;AACde,wBAAIA;AADU,iBAAZ,EAEH2C,MAFG,CAEI6B,MAFJ,CAAN;AAGH,aAJD,MAIO;AACH,uBAAOA,OAAOxE,EAAd;AACA,sBAAMjB,MAAM4F,GAAN,CAAUH,MAAV,CAAN;AACH;AACD,mBAAO,QAAK9D,OAAL,CAAa8D,MAAb,CAAP;AAjBgB;AAkBnB;AACKI,iBAAN,GAAsB;AAAA;;AAAA;AAClB,kBAAM5E,KAAK,QAAKoC,IAAL,CAAU,IAAV,CAAX;AACA,kBAAM,QAAKrD,KAAL,CAAW,MAAX,EAAmBE,KAAnB,CAAyB;AAC3Be,oBAAIA;AADuB,aAAzB,EAEH6E,KAFG,CAEG,CAFH,EAEMC,MAFN,EAAN;AAGA,mBAAO,QAAKpE,OAAL,EAAP;AALkB;AAMrB;;AAED;AACMqE,mBAAN,GAAwB;AAAA;;AAAA;AACpB,kBAAMC,SAAS,QAAKxG,GAAL,CAAS,IAAT,CAAf;AACA,kBAAMyG,YAAY,MAAM,QAAKlG,KAAL,CAAW,SAAX,EAAsBE,KAAtB,CAA4B;AAChD8B,yBAASiE;AADuC,aAA5B,EAErB7F,KAFqB,CAEf,CAAC,iBAAD,EAAoB,SAApB,CAFe,EAEiBoE,MAFjB,EAAxB;;AAIA;AACA,iBAAK,MAAMlE,IAAX,IAAmB4F,SAAnB,EAA8B;AAC1B,oBAAI;AACA,0BAAMzC,WAAW,MAAM,QAAKzD,KAAL,CAAW,QAAX,EAAqBE,KAArB,CAA2B,EAAEe,IAAIX,KAAKsC,WAAX,EAA3B,EAAqDC,QAArD,CAA8D,MAA9D,EAAsE,IAAtE,CAAvB;AACA,0BAAMa,OAAO,MAAM,QAAK1D,KAAL,CAAW,QAAX,EAAqBE,KAArB,CAA2B,EAAEe,IAAIX,KAAKyC,OAAX,EAA3B,EAAiDF,QAAjD,CAA0D,MAA1D,EAAkE,IAAlE,CAAnB;AACA,0BAAMc,WAAW,MAAM,QAAK3D,KAAL,CAAW,QAAX,EAAqBE,KAArB,CAA2B,EAAEe,IAAIX,KAAK2C,WAAX,EAA3B,EAAqDJ,QAArD,CAA8D,MAA9D,EAAsE,IAAtE,CAAvB;;AAEAvC,yBAAKmD,QAAL,GAAgBA,YAAY,EAA5B;AACAnD,yBAAKoD,IAAL,GAAYA,QAAQ,EAApB;AACApD,yBAAK6F,MAAL,GAAcxC,YAAY,EAA1B;AACArD,yBAAK8F,cAAL,GAAsB9F,KAAK6C,OAAL,IAAgB,EAAtC;AACH,iBATD,CASE,OAAOhC,KAAP,EAAc;AACZC,4BAAQC,GAAR,CAAY,aAAZ,EAA2BF,MAAMG,OAAjC;AACAhB,yBAAKmD,QAAL,GAAgB,EAAhB;AACAnD,yBAAKoD,IAAL,GAAY,EAAZ;AACApD,yBAAK6F,MAAL,GAAc,EAAd;AACA7F,yBAAK8F,cAAL,GAAsB9F,KAAK6C,OAAL,IAAgB,EAAtC;AACH;AACJ;;AAED,mBAAO,QAAKxB,OAAL,CAAauE,SAAb,CAAP;AA1BoB;AA2BvB;;AAED;AACMG,mBAAN,GAAwB;AAAA;;AAAA;AACpB,kBAAMJ,SAAS,QAAKxG,GAAL,CAAS,IAAT,CAAf;;AAEA,gBAAI;AACA;AACA,sBAAM6G,aAAa,OAAM,QAAKtG,KAAL,CAAW,MAAX,EAAmBE,KAAnB,CAAyB;AAC9C8B,6BAASiE,MADqC;AAE9C/D,+BAAW;AAFmC,iBAAzB,EAGtBK,GAHsB,CAGlB,QAHkB,CAAN,KAGC,CAHpB;;AAKA;AACA,sBAAMgE,YAAY,MAAM,QAAKvG,KAAL,CAAW,MAAX,EAAmBE,KAAnB,CAAyB;AAC7C8B,6BAASiE,MADoC;AAE7C/D,+BAAW;AAFkC,iBAAzB,EAGrBsC,MAHqB,EAAxB;;AAKA,oBAAIgC,cAAc,CAAlB;AACA,qBAAK,MAAMlG,IAAX,IAAmBiG,SAAnB,EAA8B;AAC1B;AACA,0BAAME,QAAQ,MAAM,QAAKzG,KAAL,CAAW,OAAX,EAAoBE,KAApB,CAA0B,EAAEe,IAAIX,KAAKoG,QAAX,EAA1B,EAAiD7E,IAAjD,EAApB;AACA,wBAAI4E,KAAJ,EAAW;AACPD,uCAAeG,WAAWF,MAAMG,YAAN,IAAsB,CAAjC,IAAsCC,SAASvG,KAAKsE,MAAL,IAAe,CAAxB,CAArD;AACH;AACJ;;AAED,sBAAMkC,QAAQ;AACVR,gCAAYO,SAASP,UAAT,CADF;AAEVE,iCAAaA,YAAYO,OAAZ,CAAoB,CAApB;AAFH,iBAAd;;AAKA,uBAAO,QAAKpF,OAAL,CAAamF,KAAb,CAAP;AACH,aA5BD,CA4BE,OAAO3F,KAAP,EAAc;AACZC,wBAAQC,GAAR,CAAY,YAAZ,EAA0BF,MAAMG,OAAhC;AACA,uBAAO,QAAKK,OAAL,CAAa;AAChB2E,gCAAY,CADI;AAEhBE,iCAAa;AAFG,iBAAb,CAAP;AAIH;AArCmB;AAsCvB;AA9a+B,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\user.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\nmodule.exports = class extends Base {\n    /**\n     * index action\n     * @return {Promise} []\n     */\n    async indexAction() {\n        const page = this.get('page') || 1;\n        const size = this.get('size') || 10;\n        let nickname = this.get('nickname') || '';\n        const buffer = Buffer.from(nickname);\n        nickname = buffer.toString('base64');\n        const model = this.model('user');\n        const data = await model.where({\n            nickname: ['like', `%${nickname}%`],\n        }).field('id,nickname,gender,mobile,avatar,register_time,last_login_time').order(['id DESC']).page(page, size).countSelect();\n\n        for (const item of data.data) {\n            item.register_time = moment.unix(item.register_time).format('YYYY-MM-DD HH:mm:ss');\n            item.last_login_time = moment.unix(item.last_login_time).format('YYYY-MM-DD HH:mm:ss');\n            item.nickname = Buffer.from(item.nickname, 'base64').toString();\n\n            // 处理头像URL\n            if (!item.avatar || item.avatar === '' || item.avatar.startsWith('wxfile://')) {\n                // 如果没有头像或者是微信临时文件，使用默认头像\n                item.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';\n            } else if (!item.avatar.startsWith('http://') && !item.avatar.startsWith('https://')) {\n                // 如果是相对路径，补充完整的域名\n                item.avatar = `https://ht.rxkjsdj.com${item.avatar}`;\n            }\n\n            // 查询是否为推广员（使用正确的表名）\n            let isPromoter = false;\n            let isDistributor = false;\n\n            try {\n                // 查询个人推广员表（使用完整表名）\n                const promoter = await this.model().query(`\n                    SELECT id FROM hiolabs_personal_promoters\n                    WHERE user_id = ${item.id} AND status = 1\n                    LIMIT 1\n                `);\n                isPromoter = promoter && promoter.length > 0;\n            } catch (error) {\n                console.log('查询推广员表失败:', error.message);\n            }\n\n            try {\n                // 查询分销商表（使用完整表名）\n                const distributor = await this.model().query(`\n                    SELECT id FROM hiolabs_distributors\n                    WHERE user_id = ${item.id} AND is_active = 1 AND audit_status = 1\n                    LIMIT 1\n                `);\n                isDistributor = distributor && distributor.length > 0;\n            } catch (error) {\n                console.log('查询分销商表失败:', error.message);\n            }\n\n            // 设置推广员状态（推广员或分销商都算作推广员）\n            item.is_distributor = isPromoter || isDistributor;\n        }\n        let info = {\n            userData: data,\n        }\n        return this.success(info);\n    }\n    async infoAction() {\n        const id = this.get('id');\n        const model = this.model('user');\n        let info = await model.where({\n            id: id\n        }).find();\n        info.register_time = moment.unix(info.register_time).format('YYYY-MM-DD HH:mm:ss');\n        info.last_login_time = moment.unix(info.last_login_time).format('YYYY-MM-DD HH:mm:ss');\n        info.nickname = Buffer.from(info.nickname, 'base64').toString();\n\n        // 处理头像URL\n        if (!info.avatar || info.avatar === '' || info.avatar.startsWith('wxfile://')) {\n            // 如果没有头像或者是微信临时文件，使用默认头像\n            info.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';\n        } else if (!info.avatar.startsWith('http://') && !info.avatar.startsWith('https://')) {\n            // 如果是相对路径，补充完整的域名\n            info.avatar = `https://ht.rxkjsdj.com${info.avatar}`;\n        }\n\n        // 查询是否为推广员（使用正确的表名）\n        let isPromoter = false;\n        let isDistributor = false;\n\n        try {\n            // 查询个人推广员表（使用完整表名）\n            const promoter = await this.model().query(`\n                SELECT id FROM hiolabs_personal_promoters\n                WHERE user_id = ${info.id} AND status = 1\n                LIMIT 1\n            `);\n            isPromoter = promoter && promoter.length > 0;\n        } catch (error) {\n            console.log('查询推广员表失败:', error.message);\n        }\n\n        try {\n            // 查询分销商表（使用完整表名）\n            const distributor = await this.model().query(`\n                SELECT id FROM hiolabs_distributors\n                WHERE user_id = ${info.id} AND is_active = 1 AND audit_status = 1\n                LIMIT 1\n            `);\n            isDistributor = distributor && distributor.length > 0;\n        } catch (error) {\n            console.log('查询分销商表失败:', error.message);\n        }\n\n        // 设置推广员状态\n        info.is_distributor = isPromoter || isDistributor;\n\n        return this.success(info);\n    }\n    async datainfoAction() {\n        const id = this.get('id');\n        let info = {};\n        info.orderSum = await this.model('order').where({\n            user_id: id,\n            order_type: ['<', 8],\n            is_delete: 0\n        }).count();\n        info.orderDone = await this.model('order').where({\n            user_id: id,\n            order_status: ['IN', '302,303,401'],\n            order_type: ['<', 8],\n            is_delete: 0\n        }).count();\n        info.orderMoney = await this.model('order').where({\n            user_id: id,\n            order_status: ['IN', '302,303,401'],\n            order_type: ['<', 8],\n            is_delete: 0\n        }).sum('actual_price');\n        info.cartSum = await this.model('cart').where({\n            user_id: id,\n            is_delete: 0\n        }).sum('number');\n        return this.success(info);\n    }\n    async addressAction() {\n        const id = this.get('id');\n        const page = this.get('page') || 1;\n        const size = this.get('size') || 10;\n        let addr = await this.model('address').where({\n            user_id: id\n        }).page(page, size).countSelect();\n        for (const item of addr.data) {\n            let province_name = await this.model('region').where({\n                id: item.province_id\n            }).getField('name', true);\n            let city_name = await this.model('region').where({\n                id: item.city_id\n            }).getField('name', true);\n            let district_name = await this.model('region').where({\n                id: item.district_id\n            }).getField('name', true);\n            item.full_region = province_name + city_name + district_name + item.address;\n        }\n        return this.success(addr);\n    }\n    async saveaddressAction() {\n        const id = this.post('id');\n        const user_id = this.post('user_id');\n        const name = this.post('name');\n        const mobile = this.post('mobile');\n        const address = this.post('address');\n        const addOptions = this.post('addOptions');\n        const province = addOptions[0];\n        const city = addOptions[1];\n        const district = addOptions[2];\n        let info = {\n            name: name,\n            mobile: mobile,\n            address: address,\n            province_id: province,\n            district_id: district,\n            city_id: city\n        }\n        await this.model('address').where({\n            user_id: user_id,\n            id: id\n        }).update(info);\n        return this.success();\n    }\n    async cartdataAction() {\n        const id = this.get('id');\n        const page = this.get('page') || 1;\n        const size = this.get('size') || 10;\n        const model = this.model('cart');\n        const data = await model.where({\n            user_id: id\n        }).order(['add_time DESC']).page(page, size).countSelect();\n        for (const item of data.data) {\n            item.add_time = moment.unix(item.add_time).format('YYYY-MM-DD HH:mm:ss');\n        }\n        return this.success(data);\n    }\n    async footAction() {\n        const id = this.get('id');\n        const page = this.get('page') || 1;\n        const size = this.get('size') || 10;\n        const model = this.model('footprint');\n        const data = await model.alias('f').join({\n            table: 'goods',\n            join: 'left',\n            as: 'g',\n            on: ['f.goods_id', 'g.id']\n        }).where({\n            user_id: id\n        }).page(page, size).countSelect();\n        console.log(data);\n        return this.success(data);\n    }\n    async orderAction() {\n        const page = this.get('page') || 1;\n        const size = this.get('size') || 10;\n        const user_id = this.get('id');\n        const model = this.model('order');\n        const data = await model.where({\n            user_id: user_id,\n            order_type: ['<', 8],\n        }).order(['id DESC']).page(page, size).countSelect();\n        console.log(data.count);\n        for (const item of data.data) {\n            item.goodsList = await this.model('order_goods').field('goods_name,list_pic_url,number,goods_specifition_name_value,retail_price').where({\n                order_id: item.id,\n                is_delete: 0\n            }).select();\n            item.goodsCount = 0;\n            item.goodsList.forEach(v => {\n                item.goodsCount += v.number;\n            });\n            let province_name = await this.model('region').where({\n                id: item.province\n            }).getField('name', true);\n            let city_name = await this.model('region').where({\n                id: item.city\n            }).getField('name', true);\n            let district_name = await this.model('region').where({\n                id: item.district\n            }).getField('name', true);\n            item.full_region = province_name + city_name + district_name;\n            item.postscript = Buffer.from(item.postscript, 'base64').toString();\n            item.add_time = moment.unix(item.add_time).format('YYYY-MM-DD HH:mm:ss');\n            item.order_status_text = await this.model('order').getOrderStatusText(item.id);\n            item.button_text = await this.model('order').getOrderBtnText(item.id);\n        }\n        return this.success(data);\n    }\n    async getOrderStatusText(orderInfo) {\n        let statusText = '待付款';\n        switch (orderInfo.order_status) {\n            case 101:\n                statusText = '待付款';\n                break;\n            case 102:\n                statusText = '交易关闭';\n                break;\n            case 103:\n                statusText = '交易关闭'; //到时间系统自动取消\n                break;\n            case 201:\n                statusText = '待发货';\n                break;\n            case 202:\n                statusText = '退款中';\n                break;\n            case 203:\n                statusText = '已退款';\n                break;\n            case 300:\n                statusText = '已备货';\n                break;\n            case 301:\n                statusText = '已发货';\n                break;\n            case 302:\n                statusText = '待评价';\n                break;\n            case 303:\n                statusText = '待评价'; //到时间，未收货的系统自动收货、\n                break;\n            case 401:\n                statusText = '交易成功'; //到时间，未收货的系统自动收货、\n                break;\n            case 801:\n                statusText = '拼团待付款';\n                break;\n            case 802:\n                statusText = '拼团中'; // 如果sum变为0了。则，变成201待发货\n                break;\n        }\n        return statusText;\n    }\n    async updateInfoAction() {\n        const id = this.post('id');\n        let nickname = this.post('nickname');\n        const buffer = Buffer.from(nickname);\n        nickname = buffer.toString('base64');\n        const model = this.model('user');\n        const data = await model.where({\n            id: id\n        }).update({\n            nickname: nickname\n        });\n        return this.success(data);\n    }\n    async updateNameAction() {\n        const id = this.post('id');\n        const name = this.post('name');\n        const model = this.model('user');\n        const data = await model.where({\n            id: id\n        }).update({\n            name: name\n        });\n        return this.success(data);\n    }\n    async updateMobileAction() {\n        const id = this.post('id');\n        const mobile = this.post('mobile');\n        const model = this.model('user');\n        const data = await model.where({\n            id: id\n        }).update({\n            mobile: mobile\n        });\n        return this.success(data);\n    }\n    async storeAction() {\n        if (!this.isPost) {\n            return false;\n        }\n        const values = this.post();\n        const id = this.post('id');\n        const model = this.model('user');\n        values.is_show = values.is_show ? 1 : 0;\n        values.is_new = values.is_new ? 1 : 0;\n        if (id > 0) {\n            await model.where({\n                id: id\n            }).update(values);\n        } else {\n            delete values.id;\n            await model.add(values);\n        }\n        return this.success(values);\n    }\n    async destoryAction() {\n        const id = this.post('id');\n        await this.model('user').where({\n            id: id\n        }).limit(1).delete();\n        return this.success();\n    }\n\n    // 获取用户地址列表（用于购物车详情弹窗）\n    async addressesAction() {\n        const userId = this.get('id');\n        const addresses = await this.model('address').where({\n            user_id: userId\n        }).order(['is_default DESC', 'id DESC']).select();\n\n        // 处理地址信息，获取完整的省市区名称\n        for (const item of addresses) {\n            try {\n                const province = await this.model('region').where({ id: item.province_id }).getField('name', true);\n                const city = await this.model('region').where({ id: item.city_id }).getField('name', true);\n                const district = await this.model('region').where({ id: item.district_id }).getField('name', true);\n\n                item.province = province || '';\n                item.city = city || '';\n                item.county = district || '';\n                item.address_detail = item.address || '';\n            } catch (error) {\n                console.log('获取地址区域信息失败:', error.message);\n                item.province = '';\n                item.city = '';\n                item.county = '';\n                item.address_detail = item.address || '';\n            }\n        }\n\n        return this.success(addresses);\n    }\n\n    // 获取用户购物车统计信息\n    async cartStatsAction() {\n        const userId = this.get('id');\n\n        try {\n            // 获取购物车商品总数\n            const totalItems = await this.model('cart').where({\n                user_id: userId,\n                is_delete: 0\n            }).sum('number') || 0;\n\n            // 获取购物车总金额\n            const cartItems = await this.model('cart').where({\n                user_id: userId,\n                is_delete: 0\n            }).select();\n\n            let totalAmount = 0;\n            for (const item of cartItems) {\n                // 获取商品当前价格\n                const goods = await this.model('goods').where({ id: item.goods_id }).find();\n                if (goods) {\n                    totalAmount += parseFloat(goods.retail_price || 0) * parseInt(item.number || 0);\n                }\n            }\n\n            const stats = {\n                totalItems: parseInt(totalItems),\n                totalAmount: totalAmount.toFixed(2)\n            };\n\n            return this.success(stats);\n        } catch (error) {\n            console.log('获取购物车统计失败:', error.message);\n            return this.success({\n                totalItems: 0,\n                totalAmount: '0.00'\n            });\n        }\n    }\n};"]}