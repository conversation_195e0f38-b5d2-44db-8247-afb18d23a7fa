{"version": 3, "sources": ["..\\..\\..\\src\\api\\service\\weixin.js"], "names": ["crypto", "require", "md5", "moment", "rp", "fs", "http", "module", "exports", "think", "Service", "decryptUserInfoData", "<PERSON><PERSON><PERSON>", "encryptedData", "iv", "_session<PERSON>ey", "<PERSON><PERSON><PERSON>", "from", "decoded", "decipher", "createDecipheriv", "setAutoPadding", "update", "final", "JSON", "parse", "err", "watermark", "appid", "config", "createUnifiedOrder", "payInfo", "WeiXinPay", "weixinpay", "openid", "mch_id", "partner_key", "Promise", "resolve", "reject", "body", "out_trade_no", "total_fee", "spbill_create_ip", "notify_url", "trade_type", "res", "console", "log", "return_code", "result_code", "returnParams", "parseInt", "Date", "now", "nonce_str", "prepay_id", "paramStr", "nonceStr", "package", "signType", "timeStamp", "paySign", "toUpperCase", "order_sn", "getTotalFee", "sn", "model", "where", "field", "find", "actual_price", "buildQuery", "queryObj", "sortPayOptions", "key", "Object", "keys", "sort", "payOptionQuery", "substring", "length", "signQuery", "queryStr", "md5Sign", "payNotify", "notifyData", "isEmpty", "notifyObj", "sign", "signString", "timeInfo", "time_end", "pay_time", "getTime", "createRefund", "refundInfo", "request", "xml2js", "Math", "random", "toString", "substr", "refundParams", "out_refund_no", "refund_fee", "refund_desc", "generateSign", "builder", "Builder", "xml", "buildObject", "certPath", "keyP<PERSON>", "requestOptions", "url", "method", "headers", "existsSync", "cert", "readFileSync", "passphrase", "error", "response", "success", "error_code", "error_msg", "message", "parseString", "result", "parseWXReturnXML", "refund_id", "transaction_id", "settlement_refund_fee", "settlement_total_fee", "fee_type", "cash_fee", "cash_refund_fee", "err_code", "err_code_des", "return_msg", "getAccessToken", "options", "qs", "grant_type", "secret", "sessionData", "token", "access_token", "getSelfToken", "params", "timestamp", "nonce", "join", "sha1", "createHash", "a", "digest", "b", "signature", "sendMessage", "data", "sendInfo", "json", "posting", "getMessageATempId", "type", "getMessageTempId", "sendTextMessage", "touser", "FromUserName", "msgtype", "text", "content", "Content", "sendImageMessage", "media_id", "image", "uploadShippingInfo", "shippingData", "stringify", "<PERSON><PERSON><PERSON>", "errmsg", "getOrderShippingStatus", "orderKey", "notifyConfirmReceive", "confirmData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path", "buildShippingData", "orderInfo", "expressInfo", "logisticsType", "validationResult", "validateShippingData", "valid", "Error", "currentTime", "formatRFC3339Time", "order_key", "order_number_type", "mchid", "delivery_mode", "logistics_type", "upload_time", "payer", "logistic_code", "itemDesc", "buildItemDescription", "goods_list", "shipping_list", "tracking_no", "express_company", "shipper_code", "item_desc", "maskedContact", "maskContactInfo", "contact", "consignor_contact", "goodsList", "description", "goods_name", "number", "includes", "trim", "date", "toISOString", "replace", "cleaned", "lastFour", "slice", "masked", "validateContactFormat", "lastFourMatch", "match", "filteredParams", "for<PERSON>ach", "k", "undefined", "indexOf", "sortedKeys", "stringA", "map", "stringSignTemp", "xmlObject", "newObject"], "mappings": ";;AAAA,MAAMA,SAASC,QAAQ,QAAR,CAAf;AACA,MAAMC,MAAMD,QAAQ,KAAR,CAAZ;AACA,MAAME,SAASF,QAAQ,QAAR,CAAf;AACA,MAAMG,KAAKH,QAAQ,iBAAR,CAAX;AACA,MAAMI,KAAKJ,QAAQ,IAAR,CAAX;AACA,MAAMK,OAAOL,QAAQ,MAAR,CAAb;AACAM,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,OAApB,CAA4B;AACzC;;;;;;;AAOMC,uBAAN,CAA0BC,UAA1B,EAAsCC,aAAtC,EAAqDC,EAArD,EAAyD;AAAA;AACrD;AACA,kBAAMC,cAAcC,OAAOC,IAAP,CAAYL,UAAZ,EAAwB,QAAxB,CAApB;AACAC,4BAAgBG,OAAOC,IAAP,CAAYJ,aAAZ,EAA2B,QAA3B,CAAhB;AACAC,iBAAKE,OAAOC,IAAP,CAAYH,EAAZ,EAAgB,QAAhB,CAAL;AACA,gBAAII,UAAU,EAAd;AACA,gBAAI;AACA;AACA,sBAAMC,WAAWnB,OAAOoB,gBAAP,CAAwB,aAAxB,EAAuCL,WAAvC,EAAoDD,EAApD,CAAjB;AACA;AACAK,yBAASE,cAAT,CAAwB,IAAxB;AACAH,0BAAUC,SAASG,MAAT,CAAgBT,aAAhB,EAA+B,QAA/B,EAAyC,MAAzC,CAAV;AACAK,2BAAWC,SAASI,KAAT,CAAe,MAAf,CAAX;AACAL,0BAAUM,KAAKC,KAAL,CAAWP,OAAX,CAAV;AACH,aARD,CAQE,OAAOQ,GAAP,EAAY;AACV,uBAAO,EAAP;AACH;AACD,gBAAIR,QAAQS,SAAR,CAAkBC,KAAlB,KAA4BnB,MAAMoB,MAAN,CAAa,cAAb,CAAhC,EAA8D;AAC1D,uBAAO,EAAP;AACH;AACD,mBAAOX,OAAP;AApBqD;AAqBxD;AACD;;;;;AAKMY,sBAAN,CAAyBC,OAAzB,EAAkC;AAAA;AAC9B,kBAAMC,YAAY/B,QAAQ,WAAR,CAAlB;AACA,kBAAMgC,YAAY,IAAID,SAAJ,CAAc;AAC5BJ,uBAAOnB,MAAMoB,MAAN,CAAa,cAAb,CADqB,EACS;AACrCK,wBAAQH,QAAQG,MAFY,EAEJ;AACxBC,wBAAQ1B,MAAMoB,MAAN,CAAa,eAAb,CAHoB,EAGW;AACvCO,6BAAa3B,MAAMoB,MAAN,CAAa,oBAAb,CAJe,CAIoB;AAJpB,aAAd,CAAlB;AAMA,mBAAO,IAAIQ,OAAJ,CAAY,UAACC,OAAD,EAAUC,MAAV,EAAqB;AACpC;AACAN,0BAAUH,kBAAV,CAA6B;AACzBU,0BAAMT,QAAQS,IADW;AAEzBC,kCAAcV,QAAQU,YAFG;AAGzBC,+BAAWX,QAAQW,SAHM;AAIzB;AACAC,sCAAkBZ,QAAQY,gBALD;AAMzBC,gCAAYnC,MAAMoB,MAAN,CAAa,mBAAb,CANa;AAOzBgB,gCAAY;AAPa,iBAA7B,EAQG,UAACC,GAAD,EAAS;AACRC,4BAAQC,GAAR,CAAYF,GAAZ;AACA,wBAAIA,IAAIG,WAAJ,KAAoB,SAApB,IAAiCH,IAAII,WAAJ,KAAoB,SAAzD,EAAoE;AAChE,8BAAMC,eAAe;AACjB,qCAASL,IAAIlB,KADI;AAEjB,yCAAawB,SAASC,KAAKC,GAAL,KAAa,IAAtB,IAA8B,EAF1B;AAGjB,wCAAYR,IAAIS,SAHC;AAIjB,uCAAW,eAAeT,IAAIU,SAJb;AAKjB,wCAAY;AALK,yBAArB;AAOA,8BAAMC,WAAY,SAAQN,aAAavB,KAAM,aAAYuB,aAAaO,QAAS,YAAWP,aAAaQ,OAAQ,aAAYR,aAAaS,QAAS,cAAaT,aAAaU,SAAU,OAApK,GAA6KpD,MAAMoB,MAAN,CAAa,oBAAb,CAA9L;AACAsB,qCAAaW,OAAb,GAAuB5D,IAAIuD,QAAJ,EAAcM,WAAd,EAAvB;AACA,4BAAIC,WAAWjC,QAAQU,YAAvB;AACAH,gCAAQa,YAAR;AACH,qBAZD,MAYO;AACHZ,+BAAOO,GAAP;AACH;AACJ,iBAzBD;AA0BH,aA5BM,CAAP;AAR8B;AAqCjC;AACKmB,eAAN,CAAkBC,EAAlB,EAAsB;AAAA;;AAAA;AAClB,gBAAIxB,YAAY,MAAM,MAAKyB,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5CJ,0BAAUE;AADkC,aAA1B,EAEnBG,KAFmB,CAEb,cAFa,EAEGC,IAFH,EAAtB;AAGA,gBAAIxB,MAAMJ,UAAU6B,YAApB;AACA,mBAAOzB,GAAP;AALkB;AAMrB;AACD;;;;;AAKA0B,eAAWC,QAAX,EAAqB;AACjB,cAAMC,iBAAiB,EAAvB;AACA,aAAK,MAAMC,GAAX,IAAkBC,OAAOC,IAAP,CAAYJ,QAAZ,EAAsBK,IAAtB,EAAlB,EAAgD;AAC5CJ,2BAAeC,GAAf,IAAsBF,SAASE,GAAT,CAAtB;AACH;AACD,YAAII,iBAAiB,EAArB;AACA,aAAK,MAAMJ,GAAX,IAAkBC,OAAOC,IAAP,CAAYH,cAAZ,EAA4BI,IAA5B,EAAlB,EAAsD;AAClDC,8BAAkBJ,MAAM,GAAN,GAAYD,eAAeC,GAAf,CAAZ,GAAkC,GAApD;AACH;AACDI,yBAAiBA,eAAeC,SAAf,CAAyB,CAAzB,EAA4BD,eAAeE,MAAf,GAAwB,CAApD,CAAjB;AACA,eAAOF,cAAP;AACH;AACD;;;;;AAKAG,cAAUC,QAAV,EAAoB;AAChBA,mBAAWA,WAAW,OAAX,GAAqB1E,MAAMoB,MAAN,CAAa,oBAAb,CAAhC;AACA,cAAM3B,MAAMD,QAAQ,KAAR,CAAZ;AACA,cAAMmF,UAAUlF,IAAIiF,QAAJ,CAAhB;AACA,eAAOC,QAAQrB,WAAR,EAAP;AACH;AACD;;;;;AAKAsB,cAAUC,UAAV,EAAsB;AAClB,YAAI7E,MAAM8E,OAAN,CAAcD,UAAd,CAAJ,EAA+B;AAC3B,mBAAO,KAAP;AACH;AACD,cAAME,YAAY,EAAlB;AACA,YAAIC,OAAO,EAAX;AACA,aAAK,MAAMd,GAAX,IAAkBC,OAAOC,IAAP,CAAYS,UAAZ,CAAlB,EAA2C;AACvC,gBAAIX,QAAQ,MAAZ,EAAoB;AAChBa,0BAAUb,GAAV,IAAiBW,WAAWX,GAAX,EAAgB,CAAhB,CAAjB;AACH,aAFD,MAEO;AACHc,uBAAOH,WAAWX,GAAX,EAAgB,CAAhB,CAAP;AACH;AACJ;AACD,YAAIa,UAAUvC,WAAV,KAA0B,SAA1B,IAAuCuC,UAAUtC,WAAV,KAA0B,SAArE,EAAgF;AAC5E,mBAAO,KAAP;AACH;AACD,cAAMwC,aAAa,KAAKR,SAAL,CAAe,KAAKV,UAAL,CAAgBgB,SAAhB,CAAf,CAAnB;AACA,YAAI/E,MAAM8E,OAAN,CAAcE,IAAd,KAAuBC,eAAeD,IAA1C,EAAgD;AAC5C,mBAAO,KAAP;AACH;AACD,YAAIE,WAAWH,UAAUI,QAAzB;AACA,YAAIC,WAAW1F,OAAOwF,QAAP,EAAiB,gBAAjB,CAAf;AACAH,kBAAUI,QAAV,GAAqB,IAAIvC,IAAJ,CAASA,KAAK5B,KAAL,CAAWoE,QAAX,CAAT,EAA+BC,OAA/B,KAA2C,IAAhE;AACA,eAAON,SAAP;AACH;AACD;;;;;AAKAO,iBAAaC,UAAb,EAAyB;AACrB,cAAMhG,SAASC,QAAQ,QAAR,CAAf;AACA,cAAMgG,UAAUhG,QAAQ,SAAR,CAAhB;AACA,cAAMiG,SAASjG,QAAQ,QAAR,CAAf;AACA,cAAMI,KAAKJ,QAAQ,IAAR,CAAX;;AAEA,eAAO,IAAIoC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpCQ,oBAAQC,GAAR,CAAY,mBAAZ;AACAD,oBAAQC,GAAR,CAAY,OAAZ,EAAqBgD,UAArB;;AAEA,gBAAI;AACA;AACA,sBAAMzC,YAAY4C,KAAKC,MAAL,GAAcC,QAAd,CAAuB,EAAvB,EAA2BC,MAA3B,CAAkC,CAAlC,EAAqC,EAArC,CAAlB;;AAEA;AACA,sBAAMC,eAAe;AACjB3E,2BAAOnB,MAAMoB,MAAN,CAAa,cAAb,CADU;AAEjBM,4BAAQ1B,MAAMoB,MAAN,CAAa,eAAb,CAFS;AAGjB0B,+BAAWA,SAHM;AAIjBd,kCAAcuD,WAAWvD,YAJR;AAKjB+D,mCAAeR,WAAWQ,aALT;AAMjB9D,+BAAWsD,WAAWtD,SANL;AAOjB+D,gCAAYT,WAAWS,UAPN;AAQjBC,iCAAaV,WAAWU,WAAX,IAA0B,QARtB;AASjB9D,gCAAYnC,MAAMoB,MAAN,CAAa,0BAAb,KAA4CpB,MAAMoB,MAAN,CAAa,mBAAb;AATvC,iBAArB;;AAYA;AACA,sBAAM4D,OAAO,KAAKkB,YAAL,CAAkBJ,YAAlB,EAAgC9F,MAAMoB,MAAN,CAAa,oBAAb,CAAhC,CAAb;AACA0E,6BAAad,IAAb,GAAoBA,IAApB;;AAEA1C,wBAAQC,GAAR,CAAY,WAAZ,EAAyBuD,YAAzB;;AAEA;AACA,sBAAMK,UAAU,IAAIV,OAAOW,OAAX,EAAhB;AACA,sBAAMC,MAAMF,QAAQG,WAAR,CAAoBR,YAApB,CAAZ;;AAEAxD,wBAAQC,GAAR,CAAY,UAAZ,EAAwB8D,GAAxB;;AAEA;AACA,sBAAME,WAAWvG,MAAMoB,MAAN,CAAa,kBAAb,KAAoC,2BAArD;AACA,sBAAMoF,UAAUxG,MAAMoB,MAAN,CAAa,iBAAb,KAAmC,0BAAnD;;AAEA,oBAAIqF,iBAAiB;AACjBC,yBAAK,0CADY;AAEjBC,4BAAQ,MAFS;AAGjB5E,0BAAMsE,GAHW;AAIjBO,6BAAS;AACL,wCAAgB;AADX;AAJQ,iBAArB;;AASA;AACA,oBAAIhH,GAAGiH,UAAH,CAAcN,QAAd,KAA2B3G,GAAGiH,UAAH,CAAcL,OAAd,CAA/B,EAAuD;AACnDlE,4BAAQC,GAAR,CAAY,eAAZ;AACAkE,mCAAeK,IAAf,GAAsBlH,GAAGmH,YAAH,CAAgBR,QAAhB,CAAtB;AACAE,mCAAevC,GAAf,GAAqBtE,GAAGmH,YAAH,CAAgBP,OAAhB,CAArB;AACAC,mCAAeO,UAAf,GAA4BhH,MAAMoB,MAAN,CAAa,eAAb,CAA5B,CAJmD,CAIQ;AAC9D,iBALD,MAKO;AACHkB,4BAAQC,GAAR,CAAY,8BAAZ;AACAD,4BAAQC,GAAR,CAAa,SAAQgE,QAAS,EAA9B;AACAjE,4BAAQC,GAAR,CAAa,SAAQiE,OAAQ,EAA7B;AACH;;AAED;AACAhB,wBAAQiB,cAAR,EAAwB,CAACQ,KAAD,EAAQC,QAAR,EAAkBnF,IAAlB,KAA2B;AAC/C,wBAAIkF,KAAJ,EAAW;AACP3E,gCAAQ2E,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACAnF,+BAAO;AACHqF,qCAAS,KADN;AAEHC,wCAAY,eAFT;AAGHC,uCAAWJ,MAAMK;AAHd,yBAAP;AAKA;AACH;;AAEDhF,4BAAQC,GAAR,CAAY,YAAZ,EAA0BR,IAA1B;;AAEA;AACA0D,2BAAO8B,WAAP,CAAmBxF,IAAnB,EAAyB,CAACd,GAAD,EAAMuG,MAAN,KAAiB;AACtC,4BAAIvG,GAAJ,EAAS;AACLqB,oCAAQ2E,KAAR,CAAc,aAAd,EAA6BhG,GAA7B;AACAa,mCAAO;AACHqF,yCAAS,KADN;AAEHC,4CAAY,aAFT;AAGHC,2CAAWpG,IAAIqG;AAHZ,6BAAP;AAKA;AACH;;AAED;AACA,8BAAMjF,MAAM,KAAKoF,gBAAL,CAAsBD,MAAtB,CAAZ;AACAlF,gCAAQC,GAAR,CAAY,WAAZ,EAAyBF,GAAzB;;AAEA,4BAAIA,IAAIG,WAAJ,KAAoB,SAAxB,EAAmC;AAC/B,gCAAIH,IAAII,WAAJ,KAAoB,SAAxB,EAAmC;AAC/B;AACAH,wCAAQC,GAAR,CAAY,YAAZ;AACAV,wCAAQ;AACJsF,6CAAS,IADL;AAEJO,+CAAWrF,IAAIqF,SAFX;AAGJ3B,mDAAe1D,IAAI0D,aAHf;AAIJ4B,oDAAgBtF,IAAIsF,cAJhB;AAKJ3F,kDAAcK,IAAIL,YALd;AAMJgE,gDAAY3D,IAAI2D,UANZ;AAOJ4B,2DAAuBvF,IAAIuF,qBAPvB;AAQJ3F,+CAAWI,IAAIJ,SARX;AASJ4F,0DAAsBxF,IAAIwF,oBATtB;AAUJC,8CAAUzF,IAAIyF,QAVV;AAWJC,8CAAU1F,IAAI0F,QAXV;AAYJC,qDAAiB3F,IAAI2F;AAZjB,iCAAR;AAcH,6BAjBD,MAiBO;AACH;AACA1F,wCAAQ2E,KAAR,CAAc,aAAd,EAA6B5E,IAAI4F,QAAjC,EAA2C5F,IAAI6F,YAA/C;AACApG,uCAAO;AACHqF,6CAAS,KADN;AAEHC,gDAAY/E,IAAI4F,QAFb;AAGHZ,+CAAWhF,IAAI6F,YAHZ;AAIHC,gDAAY9F,IAAI8F;AAJb,iCAAP;AAMH;AACJ,yBA5BD,MA4BO;AACH;AACA7F,oCAAQ2E,KAAR,CAAc,aAAd,EAA6B5E,IAAI8F,UAAjC;AACArG,mCAAO;AACHqF,yCAAS,KADN;AAEHC,4CAAY,qBAFT;AAGHC,2CAAWhF,IAAI8F;AAHZ,6BAAP;AAKH;AACJ,qBApDD;AAqDH,iBAnED;AAqEH,aA5HD,CA4HE,OAAOlB,KAAP,EAAc;AACZ3E,wBAAQ2E,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACAnF,uBAAO;AACHqF,6BAAS,KADN;AAEHC,gCAAY,cAFT;AAGHC,+BAAWJ,MAAMK;AAHd,iBAAP;AAKH;AACJ,SAxIM,CAAP;AAyIH;AACKc,kBAAN,GAAuB;AAAA;AACnB,kBAAMC,UAAU;AACZ1B,wBAAQ,MADI;AAEZ;AACAD,qBAAK,yCAHO;AAIZ4B,oBAAI;AACAC,gCAAY,mBADZ;AAEAC,4BAAQxI,MAAMoB,MAAN,CAAa,eAAb,CAFR;AAGAD,2BAAOnB,MAAMoB,MAAN,CAAa,cAAb;AAHP;AAJQ,aAAhB;AAUA,gBAAIqH,cAAc,MAAM9I,GAAG0I,OAAH,CAAxB;AACAI,0BAAc1H,KAAKC,KAAL,CAAWyH,WAAX,CAAd;AACA,gBAAIC,QAAQD,YAAYE,YAAxB;AACA,mBAAOD,KAAP;AAdmB;AAetB;AACKE,gBAAN,CAAmBC,MAAnB,EAA2B;AAAA;AACvB,gBAAI3E,MAAM,CAAC,0BAAD,EAA6B2E,OAAOC,SAApC,EAA+CD,OAAOE,KAAtD,EAA6D1E,IAA7D,GAAoE2E,IAApE,CAAyE,EAAzE,CAAV;AACA;AACA,gBAAIC,OAAO1J,OAAO2J,UAAP,CAAkB,MAAlB,CAAX;AACA;AACAD,iBAAKpI,MAAL,CAAYqD,GAAZ;AACA;AACA,gBAAIiF,IAAIF,KAAKG,MAAL,CAAY,KAAZ,CAAR;AACA,gBAAIC,IAAIR,OAAOS,SAAf;AACA,gBAAIH,KAAKE,CAAT,EAAY;AACR,uBAAO,IAAP;AACH;AAXsB;AAY1B;AACKE,eAAN,CAAkBb,KAAlB,EAAyBc,IAAzB,EAA+B;AAAA;AAC3B,kBAAMC,WAAW;AACb9C,wBAAQ,MADK;AAEbD,qBAAK,2EAA2EgC,KAFnE;AAGb3G,sBAAMyH,IAHO;AAIbE,sBAAM;AAJO,aAAjB;AAMA,gBAAIC,UAAU,MAAMhK,GAAG8J,QAAH,CAApB;AACAnH,oBAAQC,GAAR,CAAYoH,OAAZ;AACA,mBAAOA,OAAP;AAT2B;AAU9B;AACKC,qBAAN,CAAwBC,IAAxB,EAA8B;AAAA;AAC1B,oBAAQA,IAAR;AACI,qBAAK,CAAL;AACI,2BAAO,6CAAP;AACA;AACA;AACJ,qBAAK,CAAL;AACI,2BAAO,6CAAP;AACA;AACA;AACJ;AACI,2BAAO,KAAP;AAVR;AAD0B;AAa7B;AACKC,oBAAN,CAAuBD,IAAvB,EAA6B;AAAA;AACzB,oBAAQA,IAAR;AACI,qBAAK,CAAL;AACI,2BAAO,6CAAP;AACA;AACA;AACJ,qBAAK,CAAL;AACI,2BAAO,6CAAP;AACA;AACA;AACJ;AACI,2BAAO,KAAP;AAVR;AADyB;AAa5B;AACKE,mBAAN,CAAsBP,IAAtB,EAA4Bb,YAA5B,EAA0C;AAAA;AACtC,kBAAMc,WAAW;AACb9C,wBAAQ,MADK;AAEbD,qBAAK,wEAAwEiC,YAFhE;AAGb5G,sBAAM;AACFiI,4BAAQR,KAAKS,YADX;AAEFC,6BAAS,MAFP;AAGFC,0BAAM;AACFC,iCAASZ,KAAKa;AADZ;AAHJ,iBAHO;AAUbX,sBAAM;AAVO,aAAjB;AAYA,gBAAIC,UAAU,MAAMhK,GAAG8J,QAAH,CAApB;AACA,mBAAOE,OAAP;AAdsC;AAezC;AACKW,oBAAN,CAAuBC,QAAvB,EAAiCf,IAAjC,EAAuCb,YAAvC,EAAqD;AAAA;AACjD,kBAAMc,WAAW;AACb9C,wBAAQ,MADK;AAEbD,qBAAK,wEAAwEiC,YAFhE;AAGb5G,sBAAM;AACFiI,4BAAQR,KAAKS,YADX;AAEFC,6BAAS,OAFP;AAGFM,2BAAO;AACHD,kCAAUA;AADP;AAHL,iBAHO;AAUbb,sBAAM;AAVO,aAAjB;AAYA,gBAAIC,UAAU,MAAMhK,GAAG8J,QAAH,CAApB;AACA,mBAAOE,OAAP;AAdiD;AAepD;;AAED;;;;;AAKMc,sBAAN,CAAyBC,YAAzB,EAAuC;AAAA;;AAAA;AACnC,gBAAI;AACA,sBAAMhC,QAAQ,MAAM,OAAKN,cAAL,EAApB;AACA,sBAAMC,UAAU;AACZ1B,4BAAQ,MADI;AAEZD,yBAAK,+EAA+EgC,KAFxE;AAGZ3G,0BAAM2I,YAHM;AAIZhB,0BAAM;AAJM,iBAAhB;;AAOApH,wBAAQC,GAAR,CAAY,oBAAZ;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBxB,KAAK4J,SAAL,CAAeD,YAAf,EAA6B,IAA7B,EAAmC,CAAnC,CAArB;;AAEA,sBAAMlD,SAAS,MAAM7H,GAAG0I,OAAH,CAArB;AACA/F,wBAAQC,GAAR,CAAY,WAAZ,EAAyBiF,MAAzB;;AAEA,oBAAIA,OAAOoD,OAAP,KAAmB,CAAvB,EAA0B;AACtBtI,4BAAQC,GAAR,CAAY,UAAZ;AACA,2BAAO,EAAE4E,SAAS,IAAX,EAAiBqC,MAAMhC,MAAvB,EAAP;AACH,iBAHD,MAGO;AACHlF,4BAAQ2E,KAAR,CAAc,WAAd,EAA2BO,OAAOoD,OAAlC,EAA2CpD,OAAOqD,MAAlD;AACA,2BAAO,EAAE1D,SAAS,KAAX,EAAkBF,OAAOO,MAAzB,EAAP;AACH;AACJ,aAtBD,CAsBE,OAAOP,KAAP,EAAc;AACZ3E,wBAAQ2E,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,EAAEE,SAAS,KAAX,EAAkBF,OAAOA,MAAMK,OAA/B,EAAP;AACH;AA1BkC;AA2BtC;;AAED;;;;;AAKMwD,0BAAN,CAA6BC,QAA7B,EAAuC;AAAA;;AAAA;AACnC,gBAAI;AACA,sBAAMrC,QAAQ,MAAM,OAAKN,cAAL,EAApB;AACA,sBAAMC,UAAU;AACZ1B,4BAAQ,MADI;AAEZD,yBAAK,oEAAoEgC,KAF7D;AAGZ3G,0BAAMgJ,QAHM;AAIZrB,0BAAM;AAJM,iBAAhB;;AAOA,sBAAMlC,SAAS,MAAM7H,GAAG0I,OAAH,CAArB;AACA/F,wBAAQC,GAAR,CAAY,aAAZ,EAA2BiF,MAA3B;;AAEA,oBAAIA,OAAOoD,OAAP,KAAmB,CAAvB,EAA0B;AACtB,2BAAO,EAAEzD,SAAS,IAAX,EAAiBqC,MAAMhC,MAAvB,EAAP;AACH,iBAFD,MAEO;AACHlF,4BAAQ2E,KAAR,CAAc,aAAd,EAA6BO,OAAOoD,OAApC,EAA6CpD,OAAOqD,MAApD;AACA,2BAAO,EAAE1D,SAAS,KAAX,EAAkBF,OAAOO,MAAzB,EAAP;AACH;AACJ,aAlBD,CAkBE,OAAOP,KAAP,EAAc;AACZ3E,wBAAQ2E,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO,EAAEE,SAAS,KAAX,EAAkBF,OAAOA,MAAMK,OAA/B,EAAP;AACH;AAtBkC;AAuBtC;;AAED;;;;;AAKM0D,wBAAN,CAA2BC,WAA3B,EAAwC;AAAA;;AAAA;AACpC,gBAAI;AACA,sBAAMvC,QAAQ,MAAM,OAAKN,cAAL,EAApB;AACA,sBAAMC,UAAU;AACZ1B,4BAAQ,MADI;AAEZD,yBAAK,iFAAiFgC,KAF1E;AAGZ3G,0BAAMkJ,WAHM;AAIZvB,0BAAM;AAJM,iBAAhB;;AAOA,sBAAMlC,SAAS,MAAM7H,GAAG0I,OAAH,CAArB;AACA/F,wBAAQC,GAAR,CAAY,WAAZ,EAAyBiF,MAAzB;;AAEA,oBAAIA,OAAOoD,OAAP,KAAmB,CAAvB,EAA0B;AACtB,2BAAO,EAAEzD,SAAS,IAAX,EAAiBqC,MAAMhC,MAAvB,EAAP;AACH,iBAFD,MAEO;AACHlF,4BAAQ2E,KAAR,CAAc,WAAd,EAA2BO,OAAOoD,OAAlC,EAA2CpD,OAAOqD,MAAlD;AACA,2BAAO,EAAE1D,SAAS,KAAX,EAAkBF,OAAOO,MAAzB,EAAP;AACH;AACJ,aAlBD,CAkBE,OAAOP,KAAP,EAAc;AACZ3E,wBAAQ2E,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,EAAEE,SAAS,KAAX,EAAkBF,OAAOA,MAAMK,OAA/B,EAAP;AACH;AAtBmC;AAuBvC;;AAED;;;;;AAKM4D,kBAAN,CAAqBC,IAArB,EAA2B;AAAA;;AAAA;AACvB,gBAAI;AACA,sBAAMzC,QAAQ,MAAM,OAAKN,cAAL,EAApB;AACA,sBAAMC,UAAU;AACZ1B,4BAAQ,MADI;AAEZD,yBAAK,4EAA4EgC,KAFrE;AAGZ3G,0BAAM,EAAEoJ,MAAMA,IAAR,EAHM;AAIZzB,0BAAM;AAJM,iBAAhB;;AAOA,sBAAMlC,SAAS,MAAM7H,GAAG0I,OAAH,CAArB;AACA/F,wBAAQC,GAAR,CAAY,aAAZ,EAA2BiF,MAA3B;;AAEA,oBAAIA,OAAOoD,OAAP,KAAmB,CAAvB,EAA0B;AACtB,2BAAO,EAAEzD,SAAS,IAAX,EAAiBqC,MAAMhC,MAAvB,EAAP;AACH,iBAFD,MAEO;AACHlF,4BAAQ2E,KAAR,CAAc,aAAd,EAA6BO,OAAOoD,OAApC,EAA6CpD,OAAOqD,MAApD;AACA,2BAAO,EAAE1D,SAAS,KAAX,EAAkBF,OAAOO,MAAzB,EAAP;AACH;AACJ,aAlBD,CAkBE,OAAOP,KAAP,EAAc;AACZ3E,wBAAQ2E,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO,EAAEE,SAAS,KAAX,EAAkBF,OAAOA,MAAMK,OAA/B,EAAP;AACH;AAtBsB;AAuB1B;;AAED;;;;;;;;AAQA8D,sBAAkBC,SAAlB,EAA6BC,WAA7B,EAA0C7J,MAA1C,EAAkD8J,gBAAgB,CAAlE,EAAqE;AACjE;AACA,cAAMC,mBAAmB,KAAKC,oBAAL,CAA0BJ,SAA1B,EAAqCC,WAArC,EAAkD7J,MAAlD,EAA0D8J,aAA1D,CAAzB;AACA,YAAI,CAACC,iBAAiBE,KAAtB,EAA6B;AACzB,kBAAM,IAAIC,KAAJ,CAAW,aAAYH,iBAAiBvE,KAAM,EAA9C,CAAN;AACH;;AAED,cAAM2E,cAAc,KAAKC,iBAAL,CAAuB,IAAIjJ,IAAJ,EAAvB,CAApB;;AAEA;AACA,cAAM8H,eAAe;AACjBoB,uBAAW;AACPC,mCAAmB,CADZ,EACe;AACtBC,uBAAOhM,MAAMoB,MAAN,CAAa,eAAb,CAFA;AAGPY,8BAAcqJ,UAAU9H;AAHjB,aADM;AAMjB0I,2BAAe,CANE,EAMC;AAClBC,4BAAgBX,aAPC;AAQjBY,yBAAaP,WARI;AASjBQ,mBAAO;AACH3K,wBAAQA;AADL;AATU,SAArB;;AAcA;AACA,YAAI8J,kBAAkB,CAAlB,IAAuBD,WAAvB,IAAsCA,YAAYe,aAAtD,EAAqE;AACjE;AACA,kBAAMC,WAAW,KAAKC,oBAAL,CAA0BlB,UAAUmB,UAAV,IAAwB,EAAlD,CAAjB;AACA9B,yBAAa+B,aAAb,GAA6B,CAAC;AAC1BC,6BAAapB,YAAYe,aADC;AAE1BM,iCAAiBrB,YAAYsB,YAAZ,IAA4B,OAFnB;AAG1BC,2BAAWP;AAHe,aAAD,CAA7B;;AAMA;AACA,gBAAIhB,YAAYsB,YAAZ,KAA6B,IAAjC,EAAuC;AACnC,sBAAME,gBAAgB,KAAKC,eAAL,CAAqB,WAArB,CAAtB;AACArC,6BAAa+B,aAAb,CAA2B,CAA3B,EAA8BO,OAA9B,GAAwC;AACpCC,uCAAmBH;AADiB,iBAAxC;AAGH;AACJ,SAhBD,MAgBO,IAAIvB,kBAAkB,CAAtB,EAAyB;AAC5B;AACAb,yBAAa+B,aAAb,GAA6B,CAAC;AAC1BI,2BAAW,KAAKN,oBAAL,CAA0BlB,UAAUmB,UAAV,IAAwB,EAAlD;AADe,aAAD,CAA7B;AAGH,SALM,MAKA,IAAIjB,kBAAkB,CAAtB,EAAyB;AAC5B;AACAb,yBAAa+B,aAAb,GAA6B,CAAC;AAC1BI,2BAAW,KAAKN,oBAAL,CAA0BlB,UAAUmB,UAAV,IAAwB,EAAlD;AADe,aAAD,CAA7B;AAGH,SALM,MAKA;AACH;AACA9B,yBAAa+B,aAAb,GAA6B,CAAC;AAC1BI,2BAAW,KAAKN,oBAAL,CAA0BlB,UAAUmB,UAAV,IAAwB,EAAlD;AADe,aAAD,CAA7B;AAGH;;AAED,eAAO9B,YAAP;AACH;;AAED;;;;;AAKA6B,yBAAqBW,SAArB,EAAgC;AAC5B,YAAI,CAACA,SAAD,IAAcA,UAAU1I,MAAV,KAAqB,CAAvC,EAA0C;AACtC,mBAAO,IAAP;AACH;;AAED,YAAI2I,cAAc,EAAlB;AACA,YAAID,UAAU1I,MAAV,KAAqB,CAAzB,EAA4B;AACxB2I,0BAAcD,UAAU,CAAV,EAAaE,UAAb,GAA0B,GAA1B,IAAiCF,UAAU,CAAV,EAAaG,MAAb,IAAuB,CAAxD,IAA6D,GAA3E;AACH,SAFD,MAEO;AACHF,0BAAcD,UAAU,CAAV,EAAaE,UAAb,GAA0B,GAA1B,GAAgCF,UAAU1I,MAA1C,GAAmD,KAAjE;AACH;;AAED;AACA,YAAI2I,YAAY3I,MAAZ,GAAqB,GAAzB,EAA8B;AAC1B2I,0BAAcA,YAAY5I,SAAZ,CAAsB,CAAtB,EAAyB,GAAzB,IAAgC,KAA9C;AACH;;AAED,eAAO4I,WAAP;AACH;;AAED;;;;;;;;AAQA1B,yBAAqBJ,SAArB,EAAgCC,WAAhC,EAA6C7J,MAA7C,EAAqD8J,aAArD,EAAoE;AAChE;AACA,YAAI,CAACF,SAAD,IAAc,CAACA,UAAU9H,QAA7B,EAAuC;AACnC,mBAAO,EAAEmI,OAAO,KAAT,EAAgBzE,OAAO,SAAvB,EAAP;AACH;;AAED,YAAI,CAACxF,MAAD,IAAW,OAAOA,MAAP,KAAkB,QAAjC,EAA2C;AACvC,mBAAO,EAAEiK,OAAO,KAAT,EAAgBzE,OAAO,qBAAvB,EAAP;AACH;;AAED,YAAIxF,OAAO+C,MAAP,GAAgB,CAAhB,IAAqB/C,OAAO+C,MAAP,GAAgB,GAAzC,EAA8C;AAC1C,mBAAO,EAAEkH,OAAO,KAAT,EAAgBzE,OAAO,wBAAvB,EAAP;AACH;;AAED;AACA,YAAI,CAAC,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAaqG,QAAb,CAAsB/B,aAAtB,CAAL,EAA2C;AACvC,mBAAO,EAAEG,OAAO,KAAT,EAAgBzE,OAAO,kCAAvB,EAAP;AACH;;AAED;AACA,YAAIsE,kBAAkB,CAAtB,EAAyB;AACrB,gBAAI,CAACD,WAAD,IAAgB,CAACA,YAAYe,aAAjC,EAAgD;AAC5C,uBAAO,EAAEX,OAAO,KAAT,EAAgBzE,OAAO,eAAvB,EAAP;AACH;;AAED,gBAAIqE,YAAYe,aAAZ,CAA0B7H,MAA1B,GAAmC,GAAvC,EAA4C;AACxC,uBAAO,EAAEkH,OAAO,KAAT,EAAgBzE,OAAO,iBAAvB,EAAP;AACH;;AAED,gBAAI,CAACqE,YAAYsB,YAAjB,EAA+B;AAC3B,uBAAO,EAAElB,OAAO,KAAT,EAAgBzE,OAAO,iBAAvB,EAAP;AACH;;AAED,gBAAIqE,YAAYsB,YAAZ,CAAyBpI,MAAzB,GAAkC,GAAtC,EAA2C;AACvC,uBAAO,EAAEkH,OAAO,KAAT,EAAgBzE,OAAO,mBAAvB,EAAP;AACH;AACJ;;AAED;AACA,cAAMqF,WAAW,KAAKC,oBAAL,CAA0BlB,UAAUmB,UAAV,IAAwB,EAAlD,CAAjB;AACA,YAAI,CAACF,QAAD,IAAaA,SAASiB,IAAT,OAAoB,EAArC,EAAyC;AACrC,mBAAO,EAAE7B,OAAO,KAAT,EAAgBzE,OAAO,UAAvB,EAAP;AACH;;AAED,eAAO,EAAEyE,OAAO,IAAT,EAAP;AACH;;AAED;;;;;AAKAG,sBAAkB2B,IAAlB,EAAwB;AACpB,YAAI,EAAEA,gBAAgB5K,IAAlB,CAAJ,EAA6B;AACzB4K,mBAAO,IAAI5K,IAAJ,EAAP;AACH;;AAED;AACA;AACA,eAAO4K,KAAKC,WAAL,GAAmBC,OAAnB,CAA2B,GAA3B,EAAgC,QAAhC,CAAP;AACH;;AAED;;;;;AAKAX,oBAAgBC,OAAhB,EAAyB;AACrB,YAAI,CAACA,OAAD,IAAY,OAAOA,OAAP,KAAmB,QAAnC,EAA6C;AACzC,mBAAO,EAAP;AACH;;AAED;AACA,cAAMW,UAAUX,QAAQU,OAAR,CAAgB,SAAhB,EAA2B,EAA3B,CAAhB;;AAEA,YAAIC,QAAQnJ,MAAR,GAAiB,CAArB,EAAwB;AACpB,mBAAOwI,OAAP;AACH;;AAED;AACA,cAAMY,WAAWD,QAAQE,KAAR,CAAc,CAAC,CAAf,CAAjB;AACA,cAAMC,SAASH,QAAQE,KAAR,CAAc,CAAd,EAAiB,CAAC,CAAlB,EAAqBH,OAArB,CAA6B,KAA7B,EAAoC,GAApC,CAAf;;AAEA,eAAOI,SAASF,QAAhB;AACH;;AAED;;;;;AAKAG,0BAAsBf,OAAtB,EAA+B;AAC3B,YAAI,CAACA,OAAD,IAAY,OAAOA,OAAP,KAAmB,QAAnC,EAA6C;AACzC,mBAAO,KAAP;AACH;;AAED;AACA,YAAIA,QAAQxI,MAAR,GAAiB,IAArB,EAA2B;AACvB,mBAAO,KAAP;AACH;;AAED;AACA,cAAMwJ,gBAAgBhB,QAAQiB,KAAR,CAAc,QAAd,CAAtB;AACA,YAAI,CAACD,aAAL,EAAoB;AAChB,mBAAO,KAAP;AACH;;AAED,eAAO,IAAP;AACH;;AAED;;;;;;AAMA9H,iBAAa2C,MAAb,EAAqB3E,GAArB,EAA0B;AACtB;AACA,cAAMgK,iBAAiB,EAAvB;AACA/J,eAAOC,IAAP,CAAYyE,MAAZ,EAAoBsF,OAApB,CAA4BC,KAAK;AAC7B,gBAAIvF,OAAOuF,CAAP,MAAcC,SAAd,IAA2BxF,OAAOuF,CAAP,MAAc,EAAzC,IAA+C,CAAC,MAAD,EAAS,KAAT,EAAgBE,OAAhB,CAAwBF,CAAxB,IAA6B,CAAhF,EAAmF;AAC/EF,+BAAeE,CAAf,IAAoBvF,OAAOuF,CAAP,CAApB;AACH;AACJ,SAJD;;AAMA;AACA,cAAMG,aAAapK,OAAOC,IAAP,CAAY8J,cAAZ,EAA4B7J,IAA5B,EAAnB;;AAEA;AACA,cAAMmK,UAAUD,WAAWE,GAAX,CAAeL,KAAM,GAAEA,CAAE,IAAGF,eAAeE,CAAf,CAAkB,EAA9C,EAAiDpF,IAAjD,CAAsD,GAAtD,CAAhB;AACA,cAAM0F,iBAAkB,GAAEF,OAAQ,QAAOtK,GAAI,EAA7C;;AAEA5B,gBAAQC,GAAR,CAAY,QAAZ,EAAsBmM,cAAtB;;AAEA;AACA,cAAMnP,SAASC,QAAQ,QAAR,CAAf;AACA,cAAMwF,OAAOzF,OAAO2J,UAAP,CAAkB,KAAlB,EAAyBrI,MAAzB,CAAgC6N,cAAhC,EAAgD,MAAhD,EAAwDtF,MAAxD,CAA+D,KAA/D,EAAsE9F,WAAtE,EAAb;;AAEAhB,gBAAQC,GAAR,CAAY,QAAZ,EAAsByC,IAAtB;AACA,eAAOA,IAAP;AACH;;AAED;;;;;AAKAyC,qBAAiBkH,SAAjB,EAA4B;AACxB,cAAMC,YAAY,EAAlB;AACAD,oBAAYA,UAAUtI,GAAV,IAAiB,EAA7B;AACA,aAAK,MAAMnC,GAAX,IAAkByK,SAAlB,EAA6B;AACzB,gBAAIA,UAAUzK,GAAV,KAAkByK,UAAUzK,GAAV,EAAe,CAAf,CAAtB,EAAyC;AACrC0K,0BAAU1K,GAAV,IAAiByK,UAAUzK,GAAV,EAAe,CAAf,CAAjB;AACH;AACJ;AACD,eAAO0K,SAAP;AACH;AAlwBwC,CAA7C", "file": "..\\..\\..\\src\\api\\service\\weixin.js", "sourcesContent": ["const crypto = require('crypto');\nconst md5 = require('md5');\nconst moment = require('moment');\nconst rp = require('request-promise');\nconst fs = require('fs');\nconst http = require(\"http\");\nmodule.exports = class extends think.Service {\n    /**\n     * 解析微信登录用户数据\n     * @param sessionKey\n     * @param encryptedData\n     * @param iv\n     * @returns {Promise.<string>}\n     */\n    async decryptUserInfoData(sessionKey, encryptedData, iv) {\n        // base64 decode\n        const _sessionKey = Buffer.from(sessionKey, 'base64');\n        encryptedData = Buffer.from(encryptedData, 'base64');\n        iv = Buffer.from(iv, 'base64');\n        let decoded = '';\n        try {\n            // 解密\n            const decipher = crypto.createDecipheriv('aes-128-cbc', _sessionKey, iv);\n            // 设置自动 padding 为 true，删除填充补位\n            decipher.setAutoPadding(true);\n            decoded = decipher.update(encryptedData, 'binary', 'utf8');\n            decoded += decipher.final('utf8');\n            decoded = JSON.parse(decoded);\n        } catch (err) {\n            return '';\n        }\n        if (decoded.watermark.appid !== think.config('weixin.appid')) {\n            return '';\n        }\n        return decoded;\n    }\n    /**\n     * 统一下单\n     * @param payInfo\n     * @returns {Promise}\n     */\n    async createUnifiedOrder(payInfo) {\n        const WeiXinPay = require('weixinpay');\n        const weixinpay = new WeiXinPay({\n            appid: think.config('weixin.appid'), // 微信小程序appid\n            openid: payInfo.openid, // 用户openid\n            mch_id: think.config('weixin.mch_id'), // 商户帐号ID\n            partner_key: think.config('weixin.partner_key') // 秘钥\n        });\n        return new Promise((resolve, reject) => {\n            // let total_fee = this.getTotalFee(payInfo.out_trade_no);\n            weixinpay.createUnifiedOrder({\n                body: payInfo.body,\n                out_trade_no: payInfo.out_trade_no,\n                total_fee: payInfo.total_fee,\n                // total_fee: total_fee,\n                spbill_create_ip: payInfo.spbill_create_ip,\n                notify_url: think.config('weixin.notify_url'),\n                trade_type: 'JSAPI'\n            }, (res) => {\n                console.log(res);\n                if (res.return_code === 'SUCCESS' && res.result_code === 'SUCCESS') {\n                    const returnParams = {\n                        'appid': res.appid,\n                        'timeStamp': parseInt(Date.now() / 1000) + '',\n                        'nonceStr': res.nonce_str,\n                        'package': 'prepay_id=' + res.prepay_id,\n                        'signType': 'MD5'\n                    };\n                    const paramStr = `appId=${returnParams.appid}&nonceStr=${returnParams.nonceStr}&package=${returnParams.package}&signType=${returnParams.signType}&timeStamp=${returnParams.timeStamp}&key=` + think.config('weixin.partner_key');\n                    returnParams.paySign = md5(paramStr).toUpperCase();\n                    let order_sn = payInfo.out_trade_no;\n                    resolve(returnParams);\n                } else {\n                    reject(res);\n                }\n            });\n        });\n    }\n    async getTotalFee(sn) {\n        let total_fee = await this.model('order').where({\n            order_sn: sn\n        }).field('actual_price').find();\n        let res = total_fee.actual_price;\n        return res;\n    }\n    /**\n     * 生成排序后的支付参数 query\n     * @param queryObj\n     * @returns {Promise.<string>}\n     */\n    buildQuery(queryObj) {\n        const sortPayOptions = {};\n        for (const key of Object.keys(queryObj).sort()) {\n            sortPayOptions[key] = queryObj[key];\n        }\n        let payOptionQuery = '';\n        for (const key of Object.keys(sortPayOptions).sort()) {\n            payOptionQuery += key + '=' + sortPayOptions[key] + '&';\n        }\n        payOptionQuery = payOptionQuery.substring(0, payOptionQuery.length - 1);\n        return payOptionQuery;\n    }\n    /**\n     * 对 query 进行签名\n     * @param queryStr\n     * @returns {Promise.<string>}\n     */\n    signQuery(queryStr) {\n        queryStr = queryStr + '&key=' + think.config('weixin.partner_key');\n        const md5 = require('md5');\n        const md5Sign = md5(queryStr);\n        return md5Sign.toUpperCase();\n    }\n    /**\n     * 处理微信支付回调\n     * @param notifyData\n     * @returns {{}}\n     */\n    payNotify(notifyData) {\n        if (think.isEmpty(notifyData)) {\n            return false;\n        }\n        const notifyObj = {};\n        let sign = '';\n        for (const key of Object.keys(notifyData)) {\n            if (key !== 'sign') {\n                notifyObj[key] = notifyData[key][0];\n            } else {\n                sign = notifyData[key][0];\n            }\n        }\n        if (notifyObj.return_code !== 'SUCCESS' || notifyObj.result_code !== 'SUCCESS') {\n            return false;\n        }\n        const signString = this.signQuery(this.buildQuery(notifyObj));\n        if (think.isEmpty(sign) || signString !== sign) {\n            return false;\n        }\n        let timeInfo = notifyObj.time_end;\n        let pay_time = moment(timeInfo, 'YYYYMMDDHHmmss');\n        notifyObj.time_end = new Date(Date.parse(pay_time)).getTime() / 1000\n        return notifyObj;\n    }\n    /**\n     * 申请退款\n     * @param refundInfo\n     * @returns {Promise}\n     */\n    createRefund(refundInfo) {\n        const crypto = require('crypto');\n        const request = require('request');\n        const xml2js = require('xml2js');\n        const fs = require('fs');\n\n        return new Promise((resolve, reject) => {\n            console.log('=== 调用微信退款API ===');\n            console.log('退款参数:', refundInfo);\n\n            try {\n                // 生成随机字符串\n                const nonce_str = Math.random().toString(36).substr(2, 15);\n\n                // 构建退款参数\n                const refundParams = {\n                    appid: think.config('weixin.appid'),\n                    mch_id: think.config('weixin.mch_id'),\n                    nonce_str: nonce_str,\n                    out_trade_no: refundInfo.out_trade_no,\n                    out_refund_no: refundInfo.out_refund_no,\n                    total_fee: refundInfo.total_fee,\n                    refund_fee: refundInfo.refund_fee,\n                    refund_desc: refundInfo.refund_desc || '用户申请退款',\n                    notify_url: think.config('weixin.refund_notify_url') || think.config('weixin.notify_url')\n                };\n\n                // 生成签名\n                const sign = this.generateSign(refundParams, think.config('weixin.partner_key'));\n                refundParams.sign = sign;\n\n                console.log('签名后的退款参数:', refundParams);\n\n                // 构建XML\n                const builder = new xml2js.Builder();\n                const xml = builder.buildObject(refundParams);\n\n                console.log('退款请求XML:', xml);\n\n                // 检查API证书文件\n                const certPath = think.config('weixin.cert_path') || './cert/apiclient_cert.pem';\n                const keyPath = think.config('weixin.key_path') || './cert/apiclient_key.pem';\n\n                let requestOptions = {\n                    url: 'https://api.mch.weixin.qq.com/pay/refund',\n                    method: 'POST',\n                    body: xml,\n                    headers: {\n                        'Content-Type': 'application/xml'\n                    }\n                };\n\n                // 如果证书文件存在，添加证书配置\n                if (fs.existsSync(certPath) && fs.existsSync(keyPath)) {\n                    console.log('✅ 使用API证书进行请求');\n                    requestOptions.cert = fs.readFileSync(certPath);\n                    requestOptions.key = fs.readFileSync(keyPath);\n                    requestOptions.passphrase = think.config('weixin.mch_id'); // 证书密码通常是商户号\n                } else {\n                    console.log('⚠️  API证书文件不存在，尝试无证书请求（可能失败）');\n                    console.log(`证书路径: ${certPath}`);\n                    console.log(`私钥路径: ${keyPath}`);\n                }\n\n                // 发送退款请求\n                request(requestOptions, (error, response, body) => {\n                    if (error) {\n                        console.error('❌ 退款请求失败:', error);\n                        reject({\n                            success: false,\n                            error_code: 'REQUEST_ERROR',\n                            error_msg: error.message\n                        });\n                        return;\n                    }\n\n                    console.log('微信退款API响应:', body);\n\n                    // 解析XML响应\n                    xml2js.parseString(body, (err, result) => {\n                        if (err) {\n                            console.error('❌ 解析退款响应失败:', err);\n                            reject({\n                                success: false,\n                                error_code: 'PARSE_ERROR',\n                                error_msg: err.message\n                            });\n                            return;\n                        }\n\n                        // 转换响应格式\n                        const res = this.parseWXReturnXML(result);\n                        console.log('解析后的退款响应:', res);\n\n                        if (res.return_code === 'SUCCESS') {\n                            if (res.result_code === 'SUCCESS') {\n                                // 退款成功\n                                console.log('✅ 微信退款申请成功');\n                                resolve({\n                                    success: true,\n                                    refund_id: res.refund_id,\n                                    out_refund_no: res.out_refund_no,\n                                    transaction_id: res.transaction_id,\n                                    out_trade_no: res.out_trade_no,\n                                    refund_fee: res.refund_fee,\n                                    settlement_refund_fee: res.settlement_refund_fee,\n                                    total_fee: res.total_fee,\n                                    settlement_total_fee: res.settlement_total_fee,\n                                    fee_type: res.fee_type,\n                                    cash_fee: res.cash_fee,\n                                    cash_refund_fee: res.cash_refund_fee\n                                });\n                            } else {\n                                // 业务失败\n                                console.error('❌ 微信退款业务失败:', res.err_code, res.err_code_des);\n                                reject({\n                                    success: false,\n                                    error_code: res.err_code,\n                                    error_msg: res.err_code_des,\n                                    return_msg: res.return_msg\n                                });\n                            }\n                        } else {\n                            // 通信失败\n                            console.error('❌ 微信退款通信失败:', res.return_msg);\n                            reject({\n                                success: false,\n                                error_code: 'COMMUNICATION_ERROR',\n                                error_msg: res.return_msg\n                            });\n                        }\n                    });\n                });\n\n            } catch (error) {\n                console.error('❌ 退款处理异常:', error);\n                reject({\n                    success: false,\n                    error_code: 'SYSTEM_ERROR',\n                    error_msg: error.message\n                });\n            }\n        });\n    }\n    async getAccessToken() {\n        const options = {\n            method: 'POST',\n            // url: 'https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=',\n            url: 'https://api.weixin.qq.com/cgi-bin/token',\n            qs: {\n                grant_type: 'client_credential',\n                secret: think.config('weixin.secret'),\n                appid: think.config('weixin.appid')\n            }\n        };\n        let sessionData = await rp(options);\n        sessionData = JSON.parse(sessionData);\n        let token = sessionData.access_token;\n        return token;\n    }\n    async getSelfToken(params) {\n        var key = ['meiweiyuxianmeiweiyuxian', params.timestamp, params.nonce].sort().join('');\n        //将token （自己设置的） 、timestamp（时间戳）、nonce（随机数）三个参数进行字典排序\n        var sha1 = crypto.createHash('sha1');\n        //将上面三个字符串拼接成一个字符串再进行sha1加密\n        sha1.update(key);\n        //将加密后的字符串与signature进行对比，若成功，返回success。如果通过验证，则，注释掉这个函数\n        let a = sha1.digest('hex');\n        let b = params.signature;\n        if (a == b) {\n            return true;\n        }\n    }\n    async sendMessage(token, data) {\n        const sendInfo = {\n            method: 'POST',\n            url: 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=' + token,\n            body: data,\n            json: true\n        };\n        let posting = await rp(sendInfo);\n        console.log(posting);\n        return posting;\n    }\n    async getMessageATempId(type) {\n        switch (type) {\n            case 1:\n                return 'TXWzXjO4C0odXCwQk4idgBtGcgSKBEWXJETYBZcRAzE';\n                break;\n                // 支付成功\n            case 2:\n                return 'COiQGBTzTtz_us5qYeJf0K-pFAyubBuWQh40sV1eAuw';\n                break;\n                // 发货通知\n            default:\n                return '400';\n        }\n    }\n    async getMessageTempId(type) {\n        switch (type) {\n            case 1:\n                return 'TXWzXjO4C0odXCwQk4idgBtGcgSKBEWXJETYBZcRAzE';\n                break;\n                // 支付成功\n            case 2:\n                return 'COiQGBTzTtz_us5qYeJf0K-pFAyubBuWQh40sV1eAuw';\n                break;\n                // 发货通知\n            default:\n                return '400';\n        }\n    }\n    async sendTextMessage(data, access_token) {\n        const sendInfo = {\n            method: 'POST',\n            url: 'https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=' + access_token,\n            body: {\n                touser: data.FromUserName,\n                msgtype: \"text\",\n                text: {\n                    content: data.Content\n                }\n            },\n            json: true\n        };\n        let posting = await rp(sendInfo);\n        return posting;\n    }\n    async sendImageMessage(media_id, data, access_token) {\n        const sendInfo = {\n            method: 'POST',\n            url: 'https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=' + access_token,\n            body: {\n                touser: data.FromUserName,\n                msgtype: \"image\",\n                image: {\n                    media_id: media_id\n                }\n            },\n            json: true\n        };\n        let posting = await rp(sendInfo);\n        return posting;\n    }\n\n    /**\n     * 微信小程序订单发货信息录入接口\n     * @param {Object} shippingData 发货信息\n     * @returns {Promise}\n     */\n    async uploadShippingInfo(shippingData) {\n        try {\n            const token = await this.getAccessToken();\n            const options = {\n                method: 'POST',\n                url: 'https://api.weixin.qq.com/wxa/sec/order/upload_shipping_info?access_token=' + token,\n                body: shippingData,\n                json: true\n            };\n\n            console.log('=== 微信发货信息录入开始 ===');\n            console.log('发货数据:', JSON.stringify(shippingData, null, 2));\n\n            const result = await rp(options);\n            console.log('微信发货接口返回:', result);\n\n            if (result.errcode === 0) {\n                console.log('发货信息录入成功');\n                return { success: true, data: result };\n            } else {\n                console.error('发货信息录入失败:', result.errcode, result.errmsg);\n                return { success: false, error: result };\n            }\n        } catch (error) {\n            console.error('发货信息录入异常:', error);\n            return { success: false, error: error.message };\n        }\n    }\n\n    /**\n     * 查询订单发货状态\n     * @param {Object} orderKey 订单信息\n     * @returns {Promise}\n     */\n    async getOrderShippingStatus(orderKey) {\n        try {\n            const token = await this.getAccessToken();\n            const options = {\n                method: 'POST',\n                url: 'https://api.weixin.qq.com/wxa/sec/order/get_order?access_token=' + token,\n                body: orderKey,\n                json: true\n            };\n\n            const result = await rp(options);\n            console.log('查询订单发货状态返回:', result);\n\n            if (result.errcode === 0) {\n                return { success: true, data: result };\n            } else {\n                console.error('查询订单发货状态失败:', result.errcode, result.errmsg);\n                return { success: false, error: result };\n            }\n        } catch (error) {\n            console.error('查询订单发货状态异常:', error);\n            return { success: false, error: error.message };\n        }\n    }\n\n    /**\n     * 确认收货提醒接口\n     * @param {Object} confirmData 确认收货数据\n     * @returns {Promise}\n     */\n    async notifyConfirmReceive(confirmData) {\n        try {\n            const token = await this.getAccessToken();\n            const options = {\n                method: 'POST',\n                url: 'https://api.weixin.qq.com/wxa/sec/order/notify_confirm_receive?access_token=' + token,\n                body: confirmData,\n                json: true\n            };\n\n            const result = await rp(options);\n            console.log('确认收货提醒返回:', result);\n\n            if (result.errcode === 0) {\n                return { success: true, data: result };\n            } else {\n                console.error('确认收货提醒失败:', result.errcode, result.errmsg);\n                return { success: false, error: result };\n            }\n        } catch (error) {\n            console.error('确认收货提醒异常:', error);\n            return { success: false, error: error.message };\n        }\n    }\n\n    /**\n     * 设置消息跳转路径\n     * @param {string} path 跳转路径\n     * @returns {Promise}\n     */\n    async setMsgJumpPath(path) {\n        try {\n            const token = await this.getAccessToken();\n            const options = {\n                method: 'POST',\n                url: 'https://api.weixin.qq.com/wxa/sec/order/set_msg_jump_path?access_token=' + token,\n                body: { path: path },\n                json: true\n            };\n\n            const result = await rp(options);\n            console.log('设置消息跳转路径返回:', result);\n\n            if (result.errcode === 0) {\n                return { success: true, data: result };\n            } else {\n                console.error('设置消息跳转路径失败:', result.errcode, result.errmsg);\n                return { success: false, error: result };\n            }\n        } catch (error) {\n            console.error('设置消息跳转路径异常:', error);\n            return { success: false, error: error.message };\n        }\n    }\n\n    /**\n     * 构建发货信息数据\n     * @param {Object} orderInfo 订单信息\n     * @param {Object} expressInfo 快递信息\n     * @param {string} openid 用户openid\n     * @param {number} logisticsType 物流类型 1-快递 2-同城 3-虚拟 4-自提\n     * @returns {Object}\n     */\n    buildShippingData(orderInfo, expressInfo, openid, logisticsType = 1) {\n        // 数据验证\n        const validationResult = this.validateShippingData(orderInfo, expressInfo, openid, logisticsType);\n        if (!validationResult.valid) {\n            throw new Error(`发货数据验证失败: ${validationResult.error}`);\n        }\n\n        const currentTime = this.formatRFC3339Time(new Date());\n\n        // 构建基础发货数据\n        const shippingData = {\n            order_key: {\n                order_number_type: 1, // 使用商户单号\n                mchid: think.config('weixin.mch_id'),\n                out_trade_no: orderInfo.order_sn\n            },\n            delivery_mode: 1, // 统一发货\n            logistics_type: logisticsType,\n            upload_time: currentTime,\n            payer: {\n                openid: openid\n            }\n        };\n\n        // 根据物流类型构建shipping_list\n        if (logisticsType === 1 && expressInfo && expressInfo.logistic_code) {\n            // 快递发货\n            const itemDesc = this.buildItemDescription(orderInfo.goods_list || []);\n            shippingData.shipping_list = [{\n                tracking_no: expressInfo.logistic_code,\n                express_company: expressInfo.shipper_code || 'OTHER',\n                item_desc: itemDesc\n            }];\n\n            // 如果是顺丰快递，需要添加联系方式\n            if (expressInfo.shipper_code === 'SF') {\n                const maskedContact = this.maskContactInfo(\"400-95338\");\n                shippingData.shipping_list[0].contact = {\n                    consignor_contact: maskedContact\n                };\n            }\n        } else if (logisticsType === 4) {\n            // 自提\n            shippingData.shipping_list = [{\n                item_desc: this.buildItemDescription(orderInfo.goods_list || [])\n            }];\n        } else if (logisticsType === 3) {\n            // 虚拟商品\n            shippingData.shipping_list = [{\n                item_desc: this.buildItemDescription(orderInfo.goods_list || [])\n            }];\n        } else {\n            // 默认情况\n            shippingData.shipping_list = [{\n                item_desc: this.buildItemDescription(orderInfo.goods_list || [])\n            }];\n        }\n\n        return shippingData;\n    }\n\n    /**\n     * 构建商品描述\n     * @param {Array} goodsList 商品列表\n     * @returns {string}\n     */\n    buildItemDescription(goodsList) {\n        if (!goodsList || goodsList.length === 0) {\n            return '商品';\n        }\n\n        let description = '';\n        if (goodsList.length === 1) {\n            description = goodsList[0].goods_name + '*' + (goodsList[0].number || 1) + '个';\n        } else {\n            description = goodsList[0].goods_name + '等' + goodsList.length + '件商品';\n        }\n\n        // 限制商品描述长度为120个字符\n        if (description.length > 120) {\n            description = description.substring(0, 117) + '...';\n        }\n\n        return description;\n    }\n\n    /**\n     * 验证发货数据\n     * @param {Object} orderInfo 订单信息\n     * @param {Object} expressInfo 快递信息\n     * @param {string} openid 用户openid\n     * @param {number} logisticsType 物流类型\n     * @returns {Object}\n     */\n    validateShippingData(orderInfo, expressInfo, openid, logisticsType) {\n        // 验证基础参数\n        if (!orderInfo || !orderInfo.order_sn) {\n            return { valid: false, error: '订单号不能为空' };\n        }\n\n        if (!openid || typeof openid !== 'string') {\n            return { valid: false, error: '用户openid不能为空且必须为字符串' };\n        }\n\n        if (openid.length < 1 || openid.length > 128) {\n            return { valid: false, error: '用户openid长度必须在1-128字符之间' };\n        }\n\n        // 验证物流类型\n        if (![1, 2, 3, 4].includes(logisticsType)) {\n            return { valid: false, error: '物流类型必须为1(快递)、2(同城)、3(虚拟)、4(自提)之一' };\n        }\n\n        // 验证快递信息\n        if (logisticsType === 1) {\n            if (!expressInfo || !expressInfo.logistic_code) {\n                return { valid: false, error: '快递发货时物流单号不能为空' };\n            }\n\n            if (expressInfo.logistic_code.length > 128) {\n                return { valid: false, error: '物流单号长度不能超过128字符' };\n            }\n\n            if (!expressInfo.shipper_code) {\n                return { valid: false, error: '快递发货时物流公司编码不能为空' };\n            }\n\n            if (expressInfo.shipper_code.length > 128) {\n                return { valid: false, error: '物流公司编码长度不能超过128字符' };\n            }\n        }\n\n        // 验证商品描述\n        const itemDesc = this.buildItemDescription(orderInfo.goods_list || []);\n        if (!itemDesc || itemDesc.trim() === '') {\n            return { valid: false, error: '商品描述不能为空' };\n        }\n\n        return { valid: true };\n    }\n\n    /**\n     * 格式化时间为RFC 3339格式\n     * @param {Date} date 日期对象\n     * @returns {string}\n     */\n    formatRFC3339Time(date) {\n        if (!(date instanceof Date)) {\n            date = new Date();\n        }\n\n        // 确保时间格式符合RFC 3339标准\n        // 例如: 2022-12-15T13:29:35.120+08:00\n        return date.toISOString().replace('Z', '+08:00');\n    }\n\n    /**\n     * 联系方式掩码处理\n     * @param {string} contact 联系方式\n     * @returns {string}\n     */\n    maskContactInfo(contact) {\n        if (!contact || typeof contact !== 'string') {\n            return '';\n        }\n\n        // 移除所有非数字和连字符\n        const cleaned = contact.replace(/[^\\d-]/g, '');\n\n        if (cleaned.length < 4) {\n            return contact;\n        }\n\n        // 保留最后4位数字，其他用*替换\n        const lastFour = cleaned.slice(-4);\n        const masked = cleaned.slice(0, -4).replace(/\\d/g, '*');\n\n        return masked + lastFour;\n    }\n\n    /**\n     * 验证联系方式格式\n     * @param {string} contact 联系方式\n     * @returns {boolean}\n     */\n    validateContactFormat(contact) {\n        if (!contact || typeof contact !== 'string') {\n            return false;\n        }\n\n        // 联系方式长度限制\n        if (contact.length > 1024) {\n            return false;\n        }\n\n        // 检查是否包含最后4位数字（不能打掩码）\n        const lastFourMatch = contact.match(/\\d{4}$/);\n        if (!lastFourMatch) {\n            return false;\n        }\n\n        return true;\n    }\n\n    /**\n     * 生成微信支付签名\n     * @param {Object} params 参数对象\n     * @param {String} key 商户密钥\n     * @returns {String} 签名\n     */\n    generateSign(params, key) {\n        // 过滤空值和特殊字段\n        const filteredParams = {};\n        Object.keys(params).forEach(k => {\n            if (params[k] !== undefined && params[k] !== '' && ['sign', 'key'].indexOf(k) < 0) {\n                filteredParams[k] = params[k];\n            }\n        });\n\n        // 按字典序排序\n        const sortedKeys = Object.keys(filteredParams).sort();\n\n        // 构建签名字符串\n        const stringA = sortedKeys.map(k => `${k}=${filteredParams[k]}`).join('&');\n        const stringSignTemp = `${stringA}&key=${key}`;\n\n        console.log('签名字符串:', stringSignTemp);\n\n        // MD5签名并转大写\n        const crypto = require('crypto');\n        const sign = crypto.createHash('md5').update(stringSignTemp, 'utf8').digest('hex').toUpperCase();\n\n        console.log('生成的签名:', sign);\n        return sign;\n    }\n\n    /**\n     * 解析微信返回的XML数据\n     * @param {Object} xmlObject XML解析后的对象\n     * @returns {Object} 解析后的数据\n     */\n    parseWXReturnXML(xmlObject) {\n        const newObject = {};\n        xmlObject = xmlObject.xml || {};\n        for (const key in xmlObject) {\n            if (xmlObject[key] && xmlObject[key][0]) {\n                newObject[key] = xmlObject[key][0];\n            }\n        }\n        return newObject;\n    }\n};"]}