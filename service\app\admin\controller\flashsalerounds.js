function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

const Base = require('./base.js');

module.exports = class extends Base {

  /**
   * 获取秒杀统计数据
   * GET /admin/flashsalerounds/statistics
   */
  statisticsAction() {
    var _this = this;

    return _asyncToGenerator(function* () {
      try {
        console.log('=== 获取轮次秒杀统计数据 ===');

        const roundModel = _this.model('flash_sale_rounds');
        const orderModel = _this.model('flash_sale_orders');

        // 获取轮次统计
        const totalRounds = yield roundModel.count();
        const activeRounds = yield roundModel.where({ status: 'active' }).count();
        const upcomingRounds = yield roundModel.where({ status: 'upcoming' }).count();
        const endedRounds = yield roundModel.where({ status: 'ended' }).count();

        // 获取订单统计
        const totalOrders = yield orderModel.count();
        const todayOrders = yield orderModel.where({
          created_at: ['>=', think.datetime(new Date(), 'YYYY-MM-DD 00:00:00')]
        }).count();

        // 计算总销售额
        const totalSalesResult = yield orderModel.field('SUM(total_amount) as total_sales').select();
        const totalSales = totalSalesResult[0] && totalSalesResult[0].total_sales ? totalSalesResult[0].total_sales : 0;

        const result = {
          totalRounds,
          activeRounds,
          upcomingRounds,
          endedRounds,
          totalOrders,
          todayOrders,
          totalSales: parseFloat(totalSales)
        };

        console.log('统计数据:', result);
        return _this.success(result);
      } catch (error) {
        console.error('获取统计数据失败:', error);
        return _this.fail('获取统计数据失败');
      }
    })();
  }

  /**
   * 获取轮次列表
   * GET /admin/flashsalerounds/list
   */
  listAction() {
    var _this2 = this;

    return _asyncToGenerator(function* () {
      try {
        console.log('=== 获取轮次列表 ===');

        const roundModel = _this2.model('flash_sale_rounds');
        const page = _this2.get('page') || 1;
        const limit = _this2.get('limit') || 20;
        const status = _this2.get('status');

        let where = {};
        if (status) {
          where.status = status;
        }

        const rounds = yield roundModel.where(where).order('round_number DESC').page(page, limit).countSelect();

        // 计算倒计时
        const now = new Date();
        rounds.data.forEach(function (round) {
          const startTime = new Date(round.start_time);
          const endTime = new Date(round.end_time);

          if (round.status === 'upcoming') {
            round.countdown = Math.max(0, Math.floor((startTime - now) / 1000));
          } else if (round.status === 'active') {
            round.countdown = Math.max(0, Math.floor((endTime - now) / 1000));
          } else {
            round.countdown = 0;
          }

          // 计算库存进度
          round.stockProgress = round.stock > 0 ? Math.round(round.sold_count / round.stock * 100) : 100;
        });

        return _this2.success(rounds);
      } catch (error) {
        console.error('获取轮次列表失败:', error);
        return _this2.fail('获取轮次列表失败');
      }
    })();
  }

  /**
   * 获取当前和即将开始的轮次
   * GET /admin/flashsalerounds/current
   */
  currentAction() {
    var _this3 = this;

    return _asyncToGenerator(function* () {
      try {
        console.log('=== 获取当前轮次 ===');

        const roundModel = _this3.model('flash_sale_rounds');
        const now = new Date();

        // 更新过期轮次状态
        yield _this3.updateExpiredRounds();

        // 获取当前进行中的轮次
        const currentRounds = yield roundModel.where({
          status: 'active',
          start_time: ['<=', think.datetime(now)],
          end_time: ['>', think.datetime(now)]
        }).order('start_time ASC').select();

        // 获取即将开始的轮次（未来30分钟内）
        const futureTime = new Date(now.getTime() + 30 * 60 * 1000);
        const upcomingRounds = yield roundModel.where({
          status: 'upcoming',
          start_time: ['>', think.datetime(now)],
          start_time: ['<=', think.datetime(futureTime)]
        }).order('start_time ASC').select();

        // 计算倒计时
        [...currentRounds, ...upcomingRounds].forEach(function (round) {
          const startTime = new Date(round.start_time);
          const endTime = new Date(round.end_time);

          if (round.status === 'upcoming') {
            round.countdown = Math.max(0, Math.floor((startTime - now) / 1000));
          } else if (round.status === 'active') {
            round.countdown = Math.max(0, Math.floor((endTime - now) / 1000));
          }

          // 计算库存进度
          round.stockProgress = round.stock > 0 ? Math.round(round.sold_count / round.stock * 100) : 100;
        });

        const result = {
          current: currentRounds,
          upcoming: upcomingRounds,
          total: currentRounds.length + upcomingRounds.length
        };

        return _this3.success(result);
      } catch (error) {
        console.error('获取当前轮次失败:', error);
        return _this3.fail('获取当前轮次失败');
      }
    })();
  }

  /**
   * 创建新轮次
   * POST /admin/flashsalerounds/create
   */
  createAction() {
    var _this4 = this;

    return _asyncToGenerator(function* () {
      try {
        console.log('=== 创建新轮次 ===');

        const data = _this4.post();
        console.log('接收到的数据:', data);

        // 验证必填字段
        const requiredFields = ['goods_id', 'flash_price', 'stock'];
        for (const field of requiredFields) {
          if (!data[field]) {
            return _this4.fail(`请填写${field}`);
          }
        }

        const roundModel = _this4.model('flash_sale_rounds');
        const configModel = _this4.model('flash_sale_config');
        const goodsModel = _this4.model('goods');

        // 获取商品信息
        const goods = yield goodsModel.where({ id: data.goods_id }).find();
        if (think.isEmpty(goods)) {
          return _this4.fail('商品不存在');
        }

        // 获取系统配置
        const config = yield configModel.where({ id: 1 }).find();
        if (think.isEmpty(config)) {
          return _this4.fail('系统配置不存在');
        }

        // 获取下一个轮次编号
        const lastRound = yield roundModel.order('round_number DESC').find();
        const nextRoundNumber = lastRound && lastRound.round_number ? lastRound.round_number + 1 : 1;

        // 计算开始时间（如果有进行中的轮次，则在其结束后开始）
        const activeRound = yield roundModel.where({ status: 'active' }).find();
        let startTime;

        if (activeRound && activeRound.end_time) {
          // 在当前轮次结束后 + 间隔时间开始
          const activeEndTime = new Date(activeRound.end_time);
          if (!isNaN(activeEndTime.getTime())) {
            startTime = new Date(activeEndTime.getTime() + config.break_duration * 1000);
          } else {
            startTime = new Date(Date.now() + 60 * 1000);
          }
        } else {
          // 立即开始（或稍后开始）
          startTime = new Date(Date.now() + 60 * 1000); // 1分钟后开始
        }

        const endTime = new Date(startTime.getTime() + config.round_duration * 1000);

        // 验证时间有效性
        if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
          return _this4.fail('时间计算错误');
        }

        const roundData = {
          round_number: nextRoundNumber,
          goods_id: data.goods_id,
          goods_name: goods.name,
          goods_image: goods.list_pic_url || '',
          original_price: goods.retail_price,
          flash_price: data.flash_price,
          stock: data.stock,
          limit_quantity: data.limit_quantity || 1,
          start_time: startTime.toISOString().slice(0, 19).replace('T', ' '),
          end_time: endTime.toISOString().slice(0, 19).replace('T', ' '),
          status: 'upcoming',
          created_by: _this4.ctx.state.userInfo && _this4.ctx.state.userInfo.id ? _this4.ctx.state.userInfo.id : 0
        };

        const roundId = yield roundModel.add(roundData);

        if (roundId) {
          console.log('轮次创建成功，ID:', roundId);
          return _this4.success({
            id: roundId,
            round_number: nextRoundNumber,
            start_time: roundData.start_time,
            end_time: roundData.end_time
          });
        } else {
          return _this4.fail('创建失败');
        }
      } catch (error) {
        console.error('创建轮次失败:', error);
        return _this4.fail('创建失败: ' + error.message);
      }
    })();
  }

  /**
   * 更新过期轮次状态
   */
  updateExpiredRounds() {
    var _this5 = this;

    return _asyncToGenerator(function* () {
      try {
        const roundModel = _this5.model('flash_sale_rounds');
        const now = new Date();

        // 将过期的upcoming轮次设为active
        yield roundModel.where({
          status: 'upcoming',
          start_time: ['<=', think.datetime(now)]
        }).update({ status: 'active' });

        // 将过期的active轮次设为ended
        yield roundModel.where({
          status: 'active',
          end_time: ['<=', think.datetime(now)]
        }).update({ status: 'ended' });
      } catch (error) {
        console.error('更新轮次状态失败:', error);
      }
    })();
  }

  /**
   * 获取商品列表（用于选择）
   * GET /admin/flashsalerounds/goods
   */
  goodsAction() {
    var _this6 = this;

    return _asyncToGenerator(function* () {
      try {
        const goodsModel = _this6.model('goods');

        const goods = yield goodsModel.where({
          is_delete: 0,
          is_on_sale: 1
        }).field('id, name, list_pic_url, retail_price').select();

        return _this6.success(goods);
      } catch (error) {
        console.error('获取商品列表失败:', error);
        return _this6.fail('获取商品列表失败');
      }
    })();
  }

  /**
   * 获取系统配置
   * GET /admin/flashsalerounds/config
   */
  configAction() {
    var _this7 = this;

    return _asyncToGenerator(function* () {
      try {
        const configModel = _this7.model('flash_sale_config');
        const config = yield configModel.where({ id: 1 }).find();

        return _this7.success(config || {});
      } catch (error) {
        console.error('获取配置失败:', error);
        return _this7.fail('获取配置失败');
      }
    })();
  }

  /**
   * 更新系统配置
   * POST /admin/flashsalerounds/config
   */
  updateConfigAction() {
    var _this8 = this;

    return _asyncToGenerator(function* () {
      try {
        const data = _this8.post();
        const configModel = _this8.model('flash_sale_config');

        yield configModel.where({ id: 1 }).update(data);

        return _this8.success('配置更新成功');
      } catch (error) {
        console.error('更新配置失败:', error);
        return _this8.fail('更新配置失败');
      }
    })();
  }
};