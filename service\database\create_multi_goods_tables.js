const mysql = require('mysql2/promise');
const fs = require('fs');

async function executeSql() {
  const connection = await mysql.createConnection({
    host: '127.0.0.1',
    port: 3306,
    user: 'root',
    password: '19841020',
    database: 'hiolabsDB'
  });

  try {
    console.log('开始执行多商品轮次秒杀系统数据库创建...');
    
    // 删除旧表
    console.log('1. 删除旧表...');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0');
    await connection.execute('DROP TABLE IF EXISTS `hiolabs_flash_sale_orders`');
    await connection.execute('DROP TABLE IF EXISTS `hiolabs_flash_sale_round_goods`');
    await connection.execute('DROP TABLE IF EXISTS `hiolabs_flash_sale_rounds`');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1');
    
    // 创建轮次表
    console.log('2. 创建轮次表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS \`hiolabs_flash_sale_rounds\` (
        \`id\` int(11) NOT NULL AUTO_INCREMENT COMMENT '轮次ID',
        \`round_number\` int(11) NOT NULL COMMENT '轮次编号（全局递增）',
        \`round_name\` varchar(100) DEFAULT NULL COMMENT '轮次名称',
        \`start_time\` datetime NOT NULL COMMENT '开始时间',
        \`end_time\` datetime NOT NULL COMMENT '结束时间',
        \`status\` enum('upcoming','active','ended','cancelled') DEFAULT 'upcoming' COMMENT '状态：即将开始，进行中，已结束，已取消',
        \`created_by\` int(11) DEFAULT 0 COMMENT '创建者ID',
        \`created_at\` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        \`updated_at\` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (\`id\`),
        UNIQUE KEY \`uk_round_number\` (\`round_number\`),
        KEY \`idx_status\` (\`status\`),
        KEY \`idx_start_time\` (\`start_time\`),
        KEY \`idx_end_time\` (\`end_time\`),
        KEY \`idx_created_at\` (\`created_at\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀轮次表'
    `);
    
    // 创建轮次商品表
    console.log('3. 创建轮次商品表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS \`hiolabs_flash_sale_round_goods\` (
        \`id\` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
        \`round_id\` int(11) NOT NULL COMMENT '轮次ID',
        \`goods_id\` int(11) NOT NULL COMMENT '商品ID',
        \`goods_name\` varchar(200) NOT NULL COMMENT '商品名称（冗余字段）',
        \`goods_image\` varchar(500) DEFAULT NULL COMMENT '商品图片（冗余字段）',
        \`original_price\` decimal(10,2) NOT NULL COMMENT '商品原价',
        \`flash_price\` decimal(10,2) NOT NULL COMMENT '秒杀价格',
        \`discount_rate\` decimal(5,2) DEFAULT NULL COMMENT '折扣率（%）',
        \`stock\` int(11) NOT NULL COMMENT '本轮库存数量',
        \`sold_count\` int(11) DEFAULT 0 COMMENT '已售数量',
        \`limit_quantity\` int(11) DEFAULT 1 COMMENT '每人限购数量',
        \`sort_order\` int(11) DEFAULT 0 COMMENT '排序',
        \`created_at\` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        PRIMARY KEY (\`id\`),
        UNIQUE KEY \`uk_round_goods\` (\`round_id\`, \`goods_id\`),
        KEY \`idx_round_id\` (\`round_id\`),
        KEY \`idx_goods_id\` (\`goods_id\`),
        KEY \`idx_sort_order\` (\`sort_order\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀轮次商品表'
    `);
    
    // 创建订单表
    console.log('4. 创建订单表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS \`hiolabs_flash_sale_orders\` (
        \`id\` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
        \`round_id\` int(11) NOT NULL COMMENT '轮次ID',
        \`round_goods_id\` int(11) NOT NULL COMMENT '轮次商品ID',
        \`order_id\` int(11) NOT NULL COMMENT '订单ID',
        \`user_id\` int(11) NOT NULL COMMENT '用户ID',
        \`goods_id\` int(11) NOT NULL COMMENT '商品ID',
        \`quantity\` int(11) NOT NULL COMMENT '购买数量',
        \`flash_price\` decimal(10,2) NOT NULL COMMENT '秒杀价格',
        \`total_amount\` decimal(10,2) NOT NULL COMMENT '总金额',
        \`created_at\` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        PRIMARY KEY (\`id\`),
        KEY \`idx_round_id\` (\`round_id\`),
        KEY \`idx_round_goods_id\` (\`round_goods_id\`),
        KEY \`idx_order_id\` (\`order_id\`),
        KEY \`idx_user_id\` (\`user_id\`),
        KEY \`idx_goods_id\` (\`goods_id\`),
        KEY \`idx_created_at\` (\`created_at\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀订单记录表'
    `);
    
    // 确保配置表存在
    console.log('5. 创建/更新配置表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS \`hiolabs_flash_sale_config\` (
        \`id\` int(1) NOT NULL DEFAULT 1 COMMENT '配置ID（固定为1）',
        \`round_duration\` int(11) NOT NULL DEFAULT 300 COMMENT '每轮持续时间（秒，默认5分钟）',
        \`break_duration\` int(11) NOT NULL DEFAULT 120 COMMENT '轮次间隔时间（秒，默认2分钟）',
        \`auto_start_next\` tinyint(1) DEFAULT 1 COMMENT '是否自动开始下一轮',
        \`daily_start_time\` time NOT NULL DEFAULT '09:00:00' COMMENT '每日开始时间',
        \`daily_end_time\` time NOT NULL DEFAULT '22:00:00' COMMENT '每日结束时间',
        \`is_enabled\` tinyint(1) DEFAULT 1 COMMENT '是否启用秒杀系统',
        \`updated_at\` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (\`id\`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀系统配置表'
    `);
    
    // 插入默认配置
    await connection.execute(`
      INSERT INTO \`hiolabs_flash_sale_config\` (\`id\`, \`round_duration\`, \`break_duration\`, \`auto_start_next\`, \`daily_start_time\`, \`daily_end_time\`, \`is_enabled\`) 
      VALUES (1, 300, 120, 1, '09:00:00', '22:00:00', 1) 
      ON DUPLICATE KEY UPDATE 
      \`round_duration\` = VALUES(\`round_duration\`),
      \`break_duration\` = VALUES(\`break_duration\`)
    `);
    
    console.log('✅ 多商品轮次秒杀系统数据库创建成功！');
    
  } catch (error) {
    console.error('❌ 执行SQL失败:', error.message);
  } finally {
    await connection.end();
  }
}

executeSql();
