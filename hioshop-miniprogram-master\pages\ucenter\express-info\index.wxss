page {
    height: 100%;
    background: #f8f8f8;
}

.container {
    background-color: #f8f8f8;
    min-height: 100%;
    overflow-x: hidden;
    align-items: unset;
    justify-content: unset;
}

.express-corp-wrap {
    margin: 18rpx 0;
    background: #fff;
    padding: 24rpx;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.express-icon-wrap {
    width: 80rpx;
    height: 80rpx;
    margin-right: 30rpx;
}

.express-icon {
    width: 80rpx;
    height: 80rpx;
}

.express-info-wrap {
    width: 100%;
    height: 120rpx;
}

.express-corp-name {
    height: 60rpx;
    line-height: 60rpx;
    font-size: 28rpx;
    color: #233445;
}

.express-info-id {
    height: 60rpx;
    line-height: 60rpx;
    font-size: 28rpx;
    color: #333;
}

.no-info {
    font-size: 28rpx;
    color: #999;
    text-align: center;
    line-height: 150rpx;
}

.express-details {
    width: 100%;
    /* padding:  */
    background: #fff;
}

.express-details .active {
    color: #233445;
    font-weight: bold;
}

.title {
    padding: 24rpx;
    border-bottom: 1rpx solid #eee;
    font-size: 28rpx;
    color: #333;
}

.wrap {
    display: flex;
    padding: 0 0 0 24rpx;
    justify-content: space-between;
    color: #666;
}

.dot-wrap {
    width: 40rpx;
    border-left: 1rpx solid #ccc;
    position: relative;
}

.info-wrap {
    border-bottom: 1rpx solid #eee;
    min-height: 120rpx;
    width: 100%;
    padding: 24rpx 0;
}

.wrap:last-child .info-wrap {
    border-bottom: none;
}

.express-info {
    font-size: 26rpx;
    min-height: 60rpx;
    line-height: 50rpx;
    padding-right: 24rpx;
}

.express-time {
    font-size: 24rpx;
    height: 50rpx;
    line-height: 50rpx;
}

.dot {
    width: 10rpx;
    height: 10rpx;
    border-radius: 10rpx;
    background: #999;
    position: absolute;
    top: 48rpx;
    left: -6rpx;
}
