<!--订单兑换页面-->
<view class="page-container">


  <!-- 主要内容区 -->
  <view class="main-content">
    <view class="content-wrapper">
      <!-- 文本中心区域 -->
      <view class="text-center">
        <!-- 礼品图标 -->
        <view class="gift-icon-container">
          <image
            class="gift-image"
            src="https://readdy.ai/api/search-image?query=3D%20illustration%20of%20a%20gift%20box%20with%20a%20ribbon%2C%20vibrant%20colors%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look&width=256&height=256&seq=gift123&orientation=squarish"
            mode="aspectFit"
          />
        </view>

        <!-- 标题 -->
        <view class="main-title">输入订单号获取礼品</view>
        <!-- 副标题 -->
        <view class="subtitle">完成订单后，即可在此兑换您的专属礼品</view>
      </view>

      <!-- 兑换功能区域 -->
      <view class="exchange-section" wx:if="{{hasUserInfo}}">
        <!-- 订单号输入框 -->
        <view class="input-wrapper">
          <input
            class="order-input"
            placeholder="{{inputFocused || orderNumber ? '' : '请输入订单号'}}"
            value="{{orderNumber}}"
            bindinput="onOrderNumberInput"
            bindfocus="onInputFocus"
            bindblur="onInputBlur"
            maxlength="50"
            focus="{{inputFocus}}"
          />
          <view class="clear-btn {{orderNumber ? 'show' : ''}}" bindtap="clearInput">
            <view class="clear-icon-wrapper">
              <text class="clear-icon">×</text>
            </view>
          </view>
        </view>

        <!-- 兑换按钮 -->
        <button
          class="exchange-btn"
          bindtap="submitExchange"
          disabled="{{isExchanging}}"
        >
          <text wx:if="{{isExchanging}}">兑换中...</text>
          <text wx:else>立即兑换</text>
        </button>

        <!-- 兑换记录 -->
        <view class="records-section">

          <!-- 记录列表 -->
          <view class="records-list" wx:if="{{exchangeRecords.length > 0}}">
            <view class="record-item" wx:for="{{exchangeRecords}}" wx:key="id">
              <view class="record-left">
                <view class="order-number">{{item.order_number}}</view>
                <view class="record-date">{{item.created_at}}</view>
              </view>
              <view class="record-right">
                <view class="points-earned">+{{item.exchange_points}}积分</view>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view class="empty-state" wx:else>
            <view class="empty-text">暂无记录</view>
          </view>
        </view>
      </view>
    </view>
  </view>



  <!-- 授权弹窗 -->
  <view class="modal-overlay {{showAuthModal ? 'show' : ''}}" catchtap="closeAuthModal">
    <view class="auth-modal {{showAuthModal ? 'show' : ''}}" catchtap="stopPropagation">
      <!-- 弹窗头部 -->
      <view class="modal-header">
        <view class="modal-title">完善个人信息</view>
        <view class="modal-close" bindtap="closeAuthModal">
          <text class="close-icon">×</text>
        </view>
      </view>

      <!-- 第一步：获取手机号 -->
      <view class="auth-step" wx:if="{{authStep === 1}}">
        <view class="step-content">
          <view class="step-icon">📱</view>
          <view class="step-title">验证手机号</view>
          <view class="step-desc">手机号用于订单通知和账户安全</view>
          <button
            class="auth-btn phone-btn"
            open-type="getPhoneNumber"
            bindgetphonenumber="modalGetPhoneNumber">
            获取手机号
          </button>
          <view class="step-tip">点击上方按钮授权获取手机号</view>
        </view>
      </view>

      <!-- 第二步：设置昵称 -->
      <view class="auth-step" wx:if="{{authStep === 2}}">
        <view class="step-content">
          <view class="step-icon">✏️</view>
          <view class="step-title">设置昵称</view>
          <view class="step-desc">为您设置一个专属昵称</view>

          <!-- 头像选择 -->
          <view class="avatar-section">
            <button
              class="avatar-btn"
              open-type="chooseAvatar"
              bindchooseavatar="modalChooseAvatar">
              <image class="avatar-img" src="{{tempAvatarUrl}}" mode="aspectFill"></image>
              <view class="avatar-overlay">
                <text class="overlay-text">选择头像</text>
              </view>
            </button>
          </view>

          <!-- 昵称输入 -->
          <view class="nickname-section">
            <input
              class="nickname-input"
              type="nickname"
              placeholder="请输入昵称"
              value="{{tempNickname}}"
              bindinput="modalNicknameInput"
              maxlength="20"
            />
          </view>

          <button
            class="auth-btn complete-btn"
            bindtap="completeModalAuth"
            disabled="{{!tempNickname || tempNickname.trim().length === 0}}">
            完成设置
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 兑换成功模态窗 -->
  <view class="modal-overlay {{showSuccessModal ? 'show' : ''}}" bindtap="closeSuccessModal">
    <view class="success-modal {{showSuccessModal ? 'show' : ''}}" catchtap="stopPropagation">
      <view class="success-icon-container">
        <text class="success-check">✓</text>
      </view>
      <view class="success-title">兑换成功</view>
      <view class="success-desc">{{successMessage || '您已成功兑换积分'}}</view>
      <view class="success-points" wx:if="{{earnedPoints > 0}}">
        <text class="points-label">获得积分：</text>
        <text class="points-value">{{earnedPoints}}</text>
      </view>
      <button class="success-btn" bindtap="closeSuccessModal">我知道了</button>
    </view>
  </view>


</view>
