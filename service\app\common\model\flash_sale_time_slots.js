function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

module.exports = class extends think.Model {

  /**
   * 获取表名
   */
  get tableName() {
    return 'hiolabs_flash_sale_time_slots';
  }

  /**
   * 获取所有启用的时段
   */
  getActiveTimeSlots() {
    var _this = this;

    return _asyncToGenerator(function* () {
      return yield _this.where({
        is_active: 1
      }).order('sort_order ASC').select();
    })();
  }

  /**
   * 根据当前时间获取对应的时段
   */
  getCurrentTimeSlot() {
    var _this2 = this;

    return _asyncToGenerator(function* () {
      const now = new Date();
      const currentTime = now.toTimeString().substring(0, 8); // HH:MM:SS格式

      return yield _this2.where({
        is_active: 1,
        start_time: ['<=', currentTime],
        end_time: ['>=', currentTime]
      }).find();
    })();
  }

  /**
   * 获取下一个时段
   */
  getNextTimeSlot() {
    var _this3 = this;

    return _asyncToGenerator(function* () {
      const now = new Date();
      const currentTime = now.toTimeString().substring(0, 8);

      return yield _this3.where({
        is_active: 1,
        start_time: ['>', currentTime]
      }).order('start_time ASC').find();
    })();
  }
};