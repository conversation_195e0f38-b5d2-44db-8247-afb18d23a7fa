const util = require('../../utils/util.js');
const api = require('../../config/api.js');

Page({
  data: {
    userInfo: {},
    validityDate: '',
    qrCodeGenerated: false,
    showShareModal: false,
    showSuccessToast: false,
    toastMessage: '',
    qrCodeData: ''
  },

  onLoad: function (options) {
    this.getUserInfo();
    this.generateValidityDate();
    this.generateQrCode();
  },

  // 获取用户信息
  getUserInfo: function() {
    let userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({
        userInfo: userInfo
      });
    } else {
      // 如果没有用户信息，从服务器获取
      this.getSettingsDetail();
    }
  },

  // 从服务器获取用户详情
  getSettingsDetail: function() {
    let that = this;
    util.request(api.SettingsDetail).then(function (res) {
      if (res.errno === 0) {
        let userInfo = res.data;
        that.setData({
          userInfo: userInfo
        });
        wx.setStorageSync('userInfo', userInfo);
      }
    });
  },

  // 生成会员有效期（默认一年后）
  generateValidityDate: function() {
    const now = new Date();
    const nextYear = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());
    const validityDate = `${nextYear.getFullYear()} 年 ${nextYear.getMonth() + 1} 月 ${nextYear.getDate()} 日`;
    this.setData({
      validityDate: validityDate
    });
  },

  // 生成二维码
  generateQrCode: function() {
    const that = this;
    const userInfo = this.data.userInfo;
    
    // 生成会员码数据（包含用户ID和时间戳）
    const memberData = {
      userId: userInfo.id || 'guest',
      nickname: userInfo.nickname || '会员用户',
      memberLevel: 'gold',
      timestamp: Date.now()
    };
    
    const qrData = JSON.stringify(memberData);
    
    // 使用微信小程序的二维码生成
    this.drawQrCode(qrData);
  },

  // 绘制二维码
  drawQrCode: function(data) {
    const that = this;

    // 使用微信小程序API生成二维码
    if (api.GetBase64) {
      // 如果有后端二维码生成接口，使用后端生成
      util.request(api.GetBase64, {
        content: data,
        size: 240
      }).then(function (res) {
        if (res.errno === 0 && res.data) {
          that.drawQrCodeFromBase64(res.data);
        } else {
          that.drawSimpleQrCode(data);
        }
      }).catch(() => {
        that.drawSimpleQrCode(data);
      });
    } else {
      // 使用简单的二维码绘制
      this.drawSimpleQrCode(data);
    }
  },

  // 从Base64绘制二维码
  drawQrCodeFromBase64: function(base64Data) {
    const that = this;
    const ctx = wx.createCanvasContext('qrcode');

    // 将base64转换为临时文件
    const buffer = wx.base64ToArrayBuffer(base64Data);
    const filePath = wx.env.USER_DATA_PATH + '/qrcode_temp.png';

    wx.getFileSystemManager().writeFile({
      filePath,
      data: buffer,
      encoding: 'binary',
      success() {
        ctx.drawImage(filePath, 0, 0, 240, 240);
        ctx.draw(false, () => {
          that.setData({
            qrCodeGenerated: true
          });
        });
      },
      fail() {
        that.drawSimpleQrCode(that.data.qrCodeData);
      }
    });
  },

  // 绘制简单的二维码样式
  drawSimpleQrCode: function(data) {
    const that = this;
    const ctx = wx.createCanvasContext('qrcode');

    // 设置背景
    ctx.setFillStyle('#ffffff');
    ctx.fillRect(0, 0, 240, 240);

    // 绘制简单的二维码图案
    ctx.setFillStyle('#000000');

    // 绘制定位点
    this.drawPositionMarker(ctx, 20, 20);
    this.drawPositionMarker(ctx, 180, 20);
    this.drawPositionMarker(ctx, 20, 180);

    // 绘制数据点（基于数据生成伪随机图案）
    const seed = this.hashCode(data);
    for (let i = 0; i < 20; i++) {
      for (let j = 0; j < 20; j++) {
        if (this.pseudoRandom(seed + i * 20 + j) > 0.5) {
          ctx.fillRect(60 + i * 8, 60 + j * 8, 6, 6);
        }
      }
    }

    ctx.draw(false, () => {
      that.setData({
        qrCodeGenerated: true,
        qrCodeData: data
      });
    });
  },

  // 生成字符串哈希值
  hashCode: function(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash);
  },

  // 伪随机数生成器
  pseudoRandom: function(seed) {
    const x = Math.sin(seed) * 10000;
    return x - Math.floor(x);
  },

  // 绘制二维码定位点
  drawPositionMarker: function(ctx, x, y) {
    // 外框
    ctx.fillRect(x, y, 40, 40);
    ctx.setFillStyle('#ffffff');
    ctx.fillRect(x + 6, y + 6, 28, 28);
    ctx.setFillStyle('#000000');
    ctx.fillRect(x + 12, y + 12, 16, 16);
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  // 保存二维码
  saveQrCode: function() {
    const that = this;
    
    wx.canvasToTempFilePath({
      canvasId: 'qrcode',
      success: function(res) {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: function() {
            that.showToast('二维码已保存到相册');
          },
          fail: function() {
            wx.showModal({
              title: '保存失败',
              content: '请在设置中允许访问相册',
              showCancel: false
            });
          }
        });
      },
      fail: function() {
        that.showToast('保存失败，请重试');
      }
    });
  },

  // 分享二维码
  shareQrCode: function() {
    this.setData({
      showShareModal: true
    });
  },

  // 隐藏分享弹窗
  hideShareModal: function() {
    this.setData({
      showShareModal: false
    });
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 阻止点击事件冒泡
  },

  // 分享给好友
  shareToFriend: function() {
    const that = this;
    
    wx.canvasToTempFilePath({
      canvasId: 'qrcode',
      success: function(res) {
        wx.showShareImageMenu({
          path: res.tempFilePath,
          success: function() {
            that.hideShareModal();
            that.showToast('分享成功');
          },
          fail: function() {
            that.showToast('分享失败');
          }
        });
      }
    });
  },

  // 分享到朋友圈
  shareToMoments: function() {
    this.showToast('请长按二维码保存后分享到朋友圈');
    this.hideShareModal();
  },

  // 显示提示信息
  showToast: function(message) {
    this.setData({
      toastMessage: message,
      showSuccessToast: true
    });
    
    setTimeout(() => {
      this.setData({
        showSuccessToast: false
      });
    }, 2000);
  },

  // 页面分享配置
  onShareAppMessage: function() {
    return {
      title: '我的会员码',
      path: '/pages/member-code/index',
      imageUrl: '/images/share/member-code.jpg'
    };
  }
});
