-- 限时秒杀功能相关数据表
-- 执行此SQL文件来创建限时秒杀功能所需的数据表

USE hiolabsDB;

-- 1. 秒杀时段表
CREATE TABLE IF NOT EXISTS `hiolabs_flash_sale_time_slots` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '时段ID',
  `name` varchar(50) NOT NULL COMMENT '时段名称',
  `start_time` time NOT NULL COMMENT '开始时间',
  `end_time` time NOT NULL COMMENT '结束时间',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_active_sort` (`is_active`, `sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='秒杀时段表';

-- 2. 秒杀活动表
CREATE TABLE IF NOT EXISTS `hiolabs_flash_sales` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '秒杀活动ID',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `time_slot_id` int(11) NOT NULL COMMENT '时段ID',
  `flash_price` decimal(10,2) NOT NULL COMMENT '秒杀价格',
  `stock` int(11) NOT NULL COMMENT '秒杀库存',
  `sold_count` int(11) DEFAULT 0 COMMENT '已售数量',
  `limit_quantity` int(11) DEFAULT 1 COMMENT '限购数量',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `status` enum('upcoming','active','ended','disabled') DEFAULT 'upcoming' COMMENT '状态：即将开始，进行中，已结束，已停用',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_time_slot_id` (`time_slot_id`),
  KEY `idx_status_time` (`status`, `start_time`, `end_time`),
  KEY `idx_start_end_time` (`start_time`, `end_time`),
  FOREIGN KEY (`goods_id`) REFERENCES `hiolabs_goods` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`time_slot_id`) REFERENCES `hiolabs_flash_sale_time_slots` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='秒杀活动表';

-- 3. 秒杀订单记录表
CREATE TABLE IF NOT EXISTS `hiolabs_flash_sale_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `flash_sale_id` int(11) NOT NULL COMMENT '秒杀活动ID',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `quantity` int(11) NOT NULL COMMENT '购买数量',
  `flash_price` decimal(10,2) NOT NULL COMMENT '秒杀价格',
  `total_amount` decimal(10,2) NOT NULL COMMENT '总金额',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_flash_sale_id` (`flash_sale_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_goods_id` (`goods_id`),
  FOREIGN KEY (`flash_sale_id`) REFERENCES `hiolabs_flash_sales` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`order_id`) REFERENCES `hiolabs_order` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `hiolabs_user` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`goods_id`) REFERENCES `hiolabs_goods` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='秒杀订单记录表';

-- 插入默认时段数据
INSERT INTO `hiolabs_flash_sale_time_slots` (`name`, `start_time`, `end_time`, `sort_order`) VALUES
('早场', '09:00:00', '11:00:00', 1),
('午场', '12:00:00', '14:00:00', 2),
('下午场', '15:00:00', '17:00:00', 3),
('晚场', '19:00:00', '21:00:00', 4),
('夜场', '22:00:00', '23:59:59', 5),
('深夜场', '00:00:00', '02:00:00', 6);

-- 创建索引以提高查询性能
CREATE INDEX idx_flash_sales_time_status ON hiolabs_flash_sales (start_time, end_time, status);
CREATE INDEX idx_flash_sale_orders_created ON hiolabs_flash_sale_orders (created_at);
