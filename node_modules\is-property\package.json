{"_from": "is-property@^1.0.2", "_id": "is-property@1.0.2", "_inBundle": false, "_integrity": "sha512-Ks/IoX00TtClbGQr4TWXemAnktAQvYB7HzcCxDGqEZU6oCmb2INHuOoKxbtR+HFkmYWBKv/dOZtGRiAjDhj92g==", "_location": "/is-property", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-property@^1.0.2", "name": "is-property", "escapedName": "is-property", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/generate-function"], "_resolved": "https://registry.npmjs.org/is-property/-/is-property-1.0.2.tgz", "_shasum": "57fe1c4e48474edd65b09911f26b1cd4095dda84", "_spec": "is-property@^1.0.2", "_where": "D:\\py-ide\\hioshop-miniprogram-master\\node_modules\\generate-function", "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Tests if a JSON property can be accessed using . syntax", "devDependencies": {"tape": "~1.0.4"}, "directories": {"test": "test"}, "gitHead": "0a85ea5b6b1264ea1cdecc6e5cf186adbb3ffc50", "homepage": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property#readme", "keywords": ["is", "property", "json", "dot", "bracket", ".", "[]"], "license": "MIT", "main": "is-property.js", "name": "is-property", "repository": {"type": "git", "url": "git://github.com/mi<PERSON><PERSON><PERSON><PERSON>/is-property.git"}, "scripts": {"test": "tap test/*.js"}, "version": "1.0.2"}