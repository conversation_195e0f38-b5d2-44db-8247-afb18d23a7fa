function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

const Base = require('./base.js');

module.exports = class extends Base {
  /**
   * 获取优惠券列表
   */
  indexAction() {
    var _this = this;

    return _asyncToGenerator(function* () {
      try {
        const { page = 1, pageSize = 20, type, status, search } = _this.get();

        let where = { is_delete: 0 };
        if (type) where.type = type;
        if (status) where.status = status;
        if (search) {
          where['name|code'] = ['like', `%${search}%`];
        }

        const list = yield _this.model('coupons').where(where).page(page, pageSize).order('created_at DESC').countSelect();

        // 统计每个优惠券的使用情况
        for (let coupon of list.data) {
          const stats = yield _this.getCouponStats(coupon.id);
          coupon.stats = stats;
        }

        return _this.success(list);
      } catch (error) {
        think.logger.error('获取优惠券列表失败:', error);
        return _this.fail('获取列表失败');
      }
    })();
  }

  /**
   * 获取优惠券详情
   */
  detailAction() {
    var _this2 = this;

    return _asyncToGenerator(function* () {
      try {
        const { id } = _this2.get();
        if (!id) {
          return _this2.fail('参数错误');
        }

        const coupon = yield _this2.model('coupons').where({
          id: id,
          is_delete: 0
        }).find();

        if (think.isEmpty(coupon)) {
          return _this2.fail('优惠券不存在');
        }

        // 获取统计信息
        const stats = yield _this2.getCouponStats(id);
        coupon.stats = stats;

        return _this2.success(coupon);
      } catch (error) {
        think.logger.error('获取优惠券详情失败:', error);
        return _this2.fail('获取详情失败');
      }
    })();
  }

  /**
   * 创建优惠券
   */
  addAction() {
    var _this3 = this;

    return _asyncToGenerator(function* () {
      try {
        const data = _this3.post();

        // 验证必填字段
        if (!data.name || !data.type || !data.discount_type || !data.discount_value) {
          return _this3.fail('参数不完整');
        }

        // 验证时间
        if (!data.start_time || !data.end_time) {
          return _this3.fail('请设置有效期');
        }

        if (new Date(data.start_time) >= new Date(data.end_time)) {
          return _this3.fail('开始时间必须早于结束时间');
        }

        // 生成优惠券代码
        if (!data.code) {
          data.code = _this3.generateCouponCode();
        } else {
          // 检查代码是否重复
          const existCoupon = yield _this3.model('coupons').where({
            code: data.code,
            is_delete: 0
          }).find();
          if (!think.isEmpty(existCoupon)) {
            return _this3.fail('优惠券代码已存在');
          }
        }

        // 设置默认值
        data.min_amount = data.min_amount || 0;
        data.total_quantity = data.total_quantity || -1;
        data.per_user_limit = data.per_user_limit || 1;
        data.status = data.status || 'active';
        data.auto_distribute = data.auto_distribute || 0;

        const id = yield _this3.model('coupons').add(data);
        return _this3.success({ id });
      } catch (error) {
        think.logger.error('创建优惠券失败:', error);
        return _this3.fail('创建失败');
      }
    })();
  }

  /**
   * 更新优惠券
   */
  updateAction() {
    var _this4 = this;

    return _asyncToGenerator(function* () {
      try {
        const { id } = _this4.post();
        if (!id) {
          return _this4.fail('参数错误');
        }

        const data = _this4.post();
        delete data.id;

        // 检查优惠券是否存在
        const coupon = yield _this4.model('coupons').where({
          id: id,
          is_delete: 0
        }).find();

        if (think.isEmpty(coupon)) {
          return _this4.fail('优惠券不存在');
        }

        // 如果修改了代码，检查是否重复
        if (data.code && data.code !== coupon.code) {
          const existCoupon = yield _this4.model('coupons').where({
            code: data.code,
            is_delete: 0,
            id: ['!=', id]
          }).find();
          if (!think.isEmpty(existCoupon)) {
            return _this4.fail('优惠券代码已存在');
          }
        }

        // 验证时间
        if (data.start_time && data.end_time) {
          if (new Date(data.start_time) >= new Date(data.end_time)) {
            return _this4.fail('开始时间必须早于结束时间');
          }
        }

        yield _this4.model('coupons').where({ id }).update(data);
        return _this4.success('更新成功');
      } catch (error) {
        think.logger.error('更新优惠券失败:', error);
        return _this4.fail('更新失败');
      }
    })();
  }

  /**
   * 删除优惠券
   */
  deleteAction() {
    var _this5 = this;

    return _asyncToGenerator(function* () {
      try {
        const { id } = _this5.post();
        if (!id) {
          return _this5.fail('参数错误');
        }

        // 检查是否有用户已领取
        const userCouponCount = yield _this5.model('user_coupons').where({
          coupon_id: id
        }).count();

        if (userCouponCount > 0) {
          return _this5.fail('该优惠券已有用户领取，无法删除');
        }

        yield _this5.model('coupons').where({ id }).update({
          is_delete: 1
        });

        return _this5.success('删除成功');
      } catch (error) {
        think.logger.error('删除优惠券失败:', error);
        return _this5.fail('删除失败');
      }
    })();
  }

  /**
   * 切换优惠券状态
   */
  toggleStatusAction() {
    var _this6 = this;

    return _asyncToGenerator(function* () {
      try {
        const { id } = _this6.post();
        if (!id) {
          return _this6.fail('参数错误');
        }

        const coupon = yield _this6.model('coupons').where({
          id: id,
          is_delete: 0
        }).find();

        if (think.isEmpty(coupon)) {
          return _this6.fail('优惠券不存在');
        }

        const newStatus = coupon.status === 'active' ? 'disabled' : 'active';
        yield _this6.model('coupons').where({ id }).update({
          status: newStatus
        });

        return _this6.success('状态更新成功');
      } catch (error) {
        think.logger.error('切换优惠券状态失败:', error);
        return _this6.fail('状态更新失败');
      }
    })();
  }

  /**
   * 批量发放优惠券
   */
  batchDistributeAction() {
    var _this7 = this;

    return _asyncToGenerator(function* () {
      try {
        const { couponId, userIds, userType, quantity } = _this7.post();

        if (!couponId) {
          return _this7.fail('请选择优惠券');
        }

        const coupon = yield _this7.model('coupons').where({
          id: couponId,
          is_delete: 0
        }).find();

        if (think.isEmpty(coupon)) {
          return _this7.fail('优惠券不存在');
        }

        let targetUsers = [];

        if (userType === 'all') {
          targetUsers = yield _this7.model('user').where({ is_delete: 0 }).field('id').select();
        } else if (userType === 'new') {
          targetUsers = yield _this7.model('user').where({
            is_new_user: 1,
            is_delete: 0
          }).field('id').select();
        } else if (userIds && userIds.length > 0) {
          targetUsers = userIds.map(function (id) {
            return { id };
          });
        } else {
          return _this7.fail('请选择发放对象');
        }

        let successCount = 0;
        let failCount = 0;

        for (let user of targetUsers) {
          try {
            // 检查用户是否已达到领取上限
            const receivedCount = yield _this7.model('user_coupons').where({
              user_id: user.id,
              coupon_id: couponId
            }).count();

            if (receivedCount >= coupon.per_user_limit) {
              failCount++;
              continue;
            }

            const couponCode = _this7.generateCouponCode();
            const expireAt = coupon.valid_days ? new Date(Date.now() + coupon.valid_days * 24 * 60 * 60 * 1000) : new Date(coupon.end_time);

            yield _this7.model('user_coupons').add({
              user_id: user.id,
              coupon_id: couponId,
              coupon_code: couponCode,
              expire_at: expireAt,
              source: 'batch'
            });

            successCount++;
          } catch (error) {
            failCount++;
          }
        }

        return _this7.success({
          message: `发放完成，成功${successCount}个，失败${failCount}个`,
          successCount,
          failCount
        });
      } catch (error) {
        think.logger.error('批量发放优惠券失败:', error);
        return _this7.fail('批量发放失败');
      }
    })();
  }

  /**
   * 获取优惠券统计信息
   */
  statisticsAction() {
    var _this8 = this;

    return _asyncToGenerator(function* () {
      try {
        const totalCoupons = yield _this8.model('coupons').where({
          is_delete: 0
        }).count();

        const activeCoupons = yield _this8.model('coupons').where({
          status: 'active',
          is_delete: 0
        }).count();

        const claimedCoupons = yield _this8.model('user_coupons').count();

        const usedCoupons = yield _this8.model('user_coupons').where({
          status: 'used'
        }).count();

        const totalDiscount = (yield _this8.model('coupon_usage_logs').sum('discount_amount')) || 0;

        return _this8.success({
          totalCoupons,
          activeCoupons,
          claimedCoupons,
          usedCoupons,
          totalDiscount
        });
      } catch (error) {
        think.logger.error('获取优惠券统计失败:', error);
        return _this8.fail('获取统计失败');
      }
    })();
  }

  /**
   * 获取单个优惠券的统计信息
   */
  getCouponStats(couponId) {
    var _this9 = this;

    return _asyncToGenerator(function* () {
      const totalReceived = yield _this9.model('user_coupons').where({ coupon_id: couponId }).count();

      const totalUsed = yield _this9.model('user_coupons').where({ coupon_id: couponId, status: 'used' }).count();

      const totalDiscount = (yield _this9.model('coupon_usage_logs').where({ coupon_id: couponId }).sum('discount_amount')) || 0;

      return {
        totalReceived,
        totalUsed,
        totalDiscount,
        usageRate: totalReceived > 0 ? (totalUsed / totalReceived * 100).toFixed(2) : 0
      };
    })();
  }

  /**
   * 生成优惠券代码
   */
  generateCouponCode() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `CPN${timestamp}${random}`.toUpperCase();
  }
};