{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\auth.js"], "names": ["Base", "require", "crypto", "module", "exports", "loginAction", "username", "post", "password", "admin", "model", "where", "find", "think", "isEmpty", "console", "log", "fail", "md5", "password_salt", "id", "update", "last_login_time", "parseInt", "Date", "now", "last_login_ip", "ctx", "ip", "TokenSerivce", "service", "sessionData", "user_id", "<PERSON><PERSON><PERSON>", "create", "userInfo", "name", "success", "token", "generateQrCodeAction", "qrToken", "randomBytes", "toString", "currentTime", "expireTime", "qrData", "status", "create_time", "expire_time", "query", "qr<PERSON><PERSON>nt", "error", "checkQrStatusAction", "get", "qr<PERSON><PERSON><PERSON>", "length", "TokenService", "user_type", "adminToken", "getUserInfo", "userId", "userType", "user", "roleInfo", "promoter", "role", "level", "distributor", "is_active", "audit_status", "level_id", "mobile", "nickname", "avatar"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;;AAEAE,OAAOC,OAAP,GAAiB,cAAcJ,IAAd,CAAmB;AAC1BK,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAMC,WAAW,MAAKC,IAAL,CAAU,UAAV,CAAjB;AACA,kBAAMC,WAAW,MAAKD,IAAL,CAAU,UAAV,CAAjB;;AAEA;AACA,kBAAME,QAAQ,MAAM,MAAKC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1CL,0BAAUA;AADgC,aAA1B,EAEjBM,IAFiB,EAApB;;AAIA,gBAAIC,MAAMC,OAAN,CAAcL,KAAd,CAAJ,EAA0B;AACtBM,wBAAQC,GAAR,CAAY,QAAZ,EAAsBV,QAAtB;AACA,uBAAO,MAAKW,IAAL,CAAU,GAAV,EAAe,YAAf,CAAP;AACH;;AAEDF,oBAAQC,GAAR,CAAY,UAAZ,EAAwBH,MAAMK,GAAN,CAAUV,WAAW,EAAX,GAAgBC,MAAMU,aAAhC,CAAxB;AACAJ,oBAAQC,GAAR,CAAY,UAAZ,EAAwBP,MAAMD,QAA9B;;AAEA,gBAAIK,MAAMK,GAAN,CAAUV,WAAW,EAAX,GAAgBC,MAAMU,aAAhC,MAAmDV,MAAMD,QAA7D,EAAuE;AACnEO,wBAAQC,GAAR,CAAY,OAAZ;AACA,uBAAO,MAAKC,IAAL,CAAU,GAAV,EAAe,aAAf,CAAP;AACH;AACD;AACA,kBAAM,MAAKP,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5BS,oBAAIX,MAAMW;AADkB,aAA1B,EAEHC,MAFG,CAEI;AACNC,iCAAiBC,SAASC,KAAKC,GAAL,KAAa,IAAtB,CADX;AAENC,+BAAe,MAAKC,GAAL,CAASC;AAFlB,aAFJ,CAAN;AAMA,kBAAMC,eAAe,MAAKC,OAAL,CAAa,OAAb,EAAsB,OAAtB,CAArB;AACA,gBAAIC,cAAc,EAAlB;AACAA,wBAAYC,OAAZ,GAAsBvB,MAAMW,EAA5B;AACA,kBAAMa,aAAa,MAAMJ,aAAaK,MAAb,CAAoBH,WAApB,CAAzB;AACA,gBAAIlB,MAAMC,OAAN,CAAcmB,UAAd,CAAJ,EAA+B;AAC3B,uBAAO,MAAKhB,IAAL,CAAU,MAAV,CAAP;AACH;AACD,kBAAMkB,WAAW;AACbf,oBAAIX,MAAMW,EADG;AAEbd,0BAAUG,MAAMH,QAFH;AAGb8B,sBAAK3B,MAAM2B;AAHE,aAAjB;AAKA,mBAAO,MAAKC,OAAL,CAAa;AAChBC,uBAAOL,UADS;AAEhBE,0BAAUA;AAFM,aAAb,CAAP;AAxCgB;AA4CnB;;AAED;AACMI,wBAAN,GAA6B;AAAA;;AAAA;AACzB,gBAAI;AACA;AACA,sBAAMC,UAAUtC,OAAOuC,WAAP,CAAmB,EAAnB,EAAuBC,QAAvB,CAAgC,KAAhC,CAAhB;AACA,sBAAMC,cAAcpB,SAASC,KAAKC,GAAL,KAAa,IAAtB,CAApB;AACA,sBAAMmB,aAAaD,cAAc,GAAjC,CAJA,CAIsC;;AAEtC;AACA;AACA,sBAAME,SAAS;AACXP,2BAAOE,OADI;AAEXM,4BAAQ,SAFG,EAEQ;AACnBC,iCAAaJ,WAHF;AAIXK,iCAAaJ,UAJF;AAKXZ,6BAAS;AALE,iBAAf;;AAQA;AACA,sBAAM,OAAKtB,KAAL,GAAauC,KAAb,CAAoB;;2BAEXT,OAAQ,iBAAgBG,WAAY,KAAIC,UAAW;aAF5D,CAAN;;AAKA;AACA,sBAAMM,YAAa,yBAAwBV,OAAQ,EAAnD;;AAEA,uBAAO,OAAKH,OAAL,CAAa;AAChBG,6BAASA,OADO;AAEhBU,+BAAWA,SAFK;AAGhBN,gCAAYA;AAHI,iBAAb,CAAP;AAKH,aA9BD,CA8BE,OAAOO,KAAP,EAAc;AACZpC,wBAAQoC,KAAR,CAAc,UAAd,EAA0BA,KAA1B;AACA,uBAAO,OAAKlC,IAAL,CAAU,SAAV,CAAP;AACH;AAlCwB;AAmC5B;;AAED;AACMmC,uBAAN,GAA4B;AAAA;;AAAA;AACxB,gBAAI;AACA,sBAAMZ,UAAU,OAAKa,GAAL,CAAS,OAAT,CAAhB;AACA,oBAAI,CAACb,OAAL,EAAc;AACV,2BAAO,OAAKvB,IAAL,CAAU,WAAV,CAAP;AACH;;AAED,sBAAM0B,cAAcpB,SAASC,KAAKC,GAAL,KAAa,IAAtB,CAApB;;AAEA;AACA,sBAAMoB,SAAS,MAAM,OAAKnC,KAAL,GAAauC,KAAb,CAAoB;uEACkBT,OAAQ;aAD9C,CAArB;AAGA,sBAAMc,WAAWT,UAAUA,OAAOU,MAAP,GAAgB,CAA1B,GAA8BV,OAAO,CAAP,CAA9B,GAA0C,IAA3D;;AAEA,oBAAI,CAACS,QAAL,EAAe;AACX,2BAAO,OAAKrC,IAAL,CAAU,QAAV,CAAP;AACH;;AAED;AACA,oBAAI0B,cAAcW,SAASN,WAA3B,EAAwC;AACpC,0BAAM,OAAKtC,KAAL,GAAauC,KAAb,CAAoB;2FACiDT,OAAQ;iBAD7E,CAAN;AAGA,2BAAO,OAAKvB,IAAL,CAAU,QAAV,CAAP;AACH;;AAED,oBAAIqC,SAASR,MAAT,KAAoB,SAApB,IAAiCQ,SAAStB,OAA9C,EAAuD;AACnD;AACA,0BAAMwB,eAAe,OAAK1B,OAAL,CAAa,OAAb,EAAsB,OAAtB,CAArB;AACA,0BAAMC,cAAc;AAChBC,iCAASsB,SAAStB,OADF;AAEhByB,mCAAWH,SAASG,SAFJ,CAEc;AAFd,qBAApB;AAIA,0BAAMC,aAAa,MAAMF,aAAatB,MAAb,CAAoBH,WAApB,CAAzB;;AAEA;AACA,0BAAMI,WAAW,MAAM,OAAKwB,WAAL,CAAiBL,SAAStB,OAA1B,EAAmCsB,SAASG,SAA5C,CAAvB;;AAEA,2BAAO,OAAKpB,OAAL,CAAa;AAChBS,gCAAQ,SADQ;AAEhBR,+BAAOoB,UAFS;AAGhBvB,kCAAUA;AAHM,qBAAb,CAAP;AAKH;;AAED,uBAAO,OAAKE,OAAL,CAAa;AAChBS,4BAAQQ,SAASR;AADD,iBAAb,CAAP;AAGH,aAhDD,CAgDE,OAAOK,KAAP,EAAc;AACZpC,wBAAQoC,KAAR,CAAc,YAAd,EAA4BA,KAA5B;AACA,uBAAO,OAAKlC,IAAL,CAAU,QAAV,CAAP;AACH;AApDuB;AAqD3B;;AAED;AACM0C,eAAN,CAAkBC,MAAlB,EAA0BC,QAA1B,EAAoC;AAAA;;AAAA;AAChC,gBAAI;AACA,sBAAMC,OAAO,MAAM,OAAKpD,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB,EAAES,IAAIwC,MAAN,EAAzB,EAAyChD,IAAzC,EAAnB;AACA,oBAAIC,MAAMC,OAAN,CAAcgD,IAAd,CAAJ,EAAyB;AACrB,2BAAO,IAAP;AACH;;AAED,oBAAIC,WAAW,EAAf;AACA,oBAAIF,aAAa,UAAjB,EAA6B;AACzB,0BAAMG,WAAW,MAAM,OAAKtD,KAAL,CAAW,oBAAX,EAAiCC,KAAjC,CAAuC;AAC1DqB,iCAAS4B,MADiD;AAE1Dd,gCAAQ;AAFkD,qBAAvC,EAGpBlC,IAHoB,EAAvB;AAIAmD,+BAAW;AACPE,8BAAM,UADC;AAEPC,+BAAOF,WAAWA,SAASE,KAApB,GAA4B;AAF5B,qBAAX;AAIH,iBATD,MASO,IAAIL,aAAa,aAAjB,EAAgC;AACnC,0BAAMM,cAAc,MAAM,OAAKzD,KAAL,CAAW,cAAX,EAA2BC,KAA3B,CAAiC;AACvDqB,iCAAS4B,MAD8C;AAEvDQ,mCAAW,CAF4C;AAGvDC,sCAAc;AAHyC,qBAAjC,EAIvBzD,IAJuB,EAA1B;AAKAmD,+BAAW;AACPE,8BAAM,aADC;AAEPC,+BAAOC,cAAcA,YAAYG,QAA1B,GAAqC;AAFrC,qBAAX;AAIH;;AAED;AACIlD,wBAAI0C,KAAK1C,EADb;AAEId,8BAAUwD,KAAKS,MAAL,IAAeT,KAAKU,QAFlC;AAGIpC,0BAAM0B,KAAKU,QAHf;AAIIC,4BAAQX,KAAKW;AAJjB,mBAKOV,QALP;AAOH,aAnCD,CAmCE,OAAOZ,KAAP,EAAc;AACZpC,wBAAQoC,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,IAAP;AACH;AAvC+B;AAwCnC;AAtL+B,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\auth.js", "sourcesContent": ["const Base = require('./base.js');\nconst crypto = require('crypto');\n\nmodule.exports = class extends Base {\n    async loginAction() {\n        const username = this.post('username');\n        const password = this.post('password');\n\n        // 正常的数据库验证流程\n        const admin = await this.model('admin').where({\n            username: username\n        }).find();\n\n        if (think.isEmpty(admin)) {\n            console.log('用户不存在:', username);\n            return this.fail(401, '用户名或密码不正确!');\n        }\n\n        console.log('输入密码加密后:', think.md5(password + '' + admin.password_salt));\n        console.log('数据库中的密码:', admin.password);\n\n        if (think.md5(password + '' + admin.password_salt) !== admin.password) {\n            console.log('密码不匹配');\n            return this.fail(400, '用户名或密码不正确!!');\n        }\n        // 更新登录信息\n        await this.model('admin').where({\n            id: admin.id\n        }).update({\n            last_login_time: parseInt(Date.now() / 1000),\n            last_login_ip: this.ctx.ip\n        });\n        const TokenSerivce = this.service('token', 'admin');\n        let sessionData = {}\n        sessionData.user_id = admin.id\n        const sessionKey = await TokenSerivce.create(sessionData);\n        if (think.isEmpty(sessionKey)) {\n            return this.fail('登录失败');\n        }\n        const userInfo = {\n            id: admin.id,\n            username: admin.username,\n            name:admin.name\n        };\n        return this.success({\n            token: sessionKey,\n            userInfo: userInfo\n        });\n    }\n\n    // 生成扫码登录二维码\n    async generateQrCodeAction() {\n        try {\n            // 生成随机token\n            const qrToken = crypto.randomBytes(32).toString('hex');\n            const currentTime = parseInt(Date.now() / 1000);\n            const expireTime = currentTime + 300; // 5分钟过期\n\n            // 存储到数据库或缓存中\n            // 这里简单存储到一个临时表或使用内存存储\n            const qrData = {\n                token: qrToken,\n                status: 'pending', // pending, success, expired\n                create_time: currentTime,\n                expire_time: expireTime,\n                user_id: null\n            };\n\n            // 存储二维码数据\n            await this.model().query(`\n                INSERT INTO hiolabs_qr_login_tokens (token, status, create_time, expire_time)\n                VALUES ('${qrToken}', 'pending', ${currentTime}, ${expireTime})\n            `);\n\n            // 生成二维码内容\n            const qrContent = `hioshop://login?token=${qrToken}`;\n\n            return this.success({\n                qrToken: qrToken,\n                qrContent: qrContent,\n                expireTime: expireTime\n            });\n        } catch (error) {\n            console.error('生成二维码失败:', error);\n            return this.fail('生成二维码失败');\n        }\n    }\n\n    // 检查二维码登录状态\n    async checkQrStatusAction() {\n        try {\n            const qrToken = this.get('token');\n            if (!qrToken) {\n                return this.fail('缺少token参数');\n            }\n\n            const currentTime = parseInt(Date.now() / 1000);\n\n            // 查询二维码状态\n            const qrData = await this.model().query(`\n                SELECT * FROM hiolabs_qr_login_tokens WHERE token = '${qrToken}' LIMIT 1\n            `);\n            const qrRecord = qrData && qrData.length > 0 ? qrData[0] : null;\n\n            if (!qrRecord) {\n                return this.fail('无效的二维码');\n            }\n\n            // 检查是否过期\n            if (currentTime > qrRecord.expire_time) {\n                await this.model().query(`\n                    UPDATE hiolabs_qr_login_tokens SET status = 'expired' WHERE token = '${qrToken}'\n                `);\n                return this.fail('二维码已过期');\n            }\n\n            if (qrRecord.status === 'success' && qrRecord.user_id) {\n                // 生成管理后台token\n                const TokenService = this.service('token', 'admin');\n                const sessionData = {\n                    user_id: qrRecord.user_id,\n                    user_type: qrRecord.user_type // 'promoter' 或 'distributor'\n                };\n                const adminToken = await TokenService.create(sessionData);\n\n                // 获取用户信息\n                const userInfo = await this.getUserInfo(qrRecord.user_id, qrRecord.user_type);\n\n                return this.success({\n                    status: 'success',\n                    token: adminToken,\n                    userInfo: userInfo\n                });\n            }\n\n            return this.success({\n                status: qrRecord.status\n            });\n        } catch (error) {\n            console.error('检查二维码状态失败:', error);\n            return this.fail('检查状态失败');\n        }\n    }\n\n    // 获取用户信息\n    async getUserInfo(userId, userType) {\n        try {\n            const user = await this.model('user').where({ id: userId }).find();\n            if (think.isEmpty(user)) {\n                return null;\n            }\n\n            let roleInfo = {};\n            if (userType === 'promoter') {\n                const promoter = await this.model('personal_promoters').where({\n                    user_id: userId,\n                    status: 1\n                }).find();\n                roleInfo = {\n                    role: 'promoter',\n                    level: promoter ? promoter.level : 1\n                };\n            } else if (userType === 'distributor') {\n                const distributor = await this.model('distributors').where({\n                    user_id: userId,\n                    is_active: 1,\n                    audit_status: 1\n                }).find();\n                roleInfo = {\n                    role: 'distributor',\n                    level: distributor ? distributor.level_id : 1\n                };\n            }\n\n            return {\n                id: user.id,\n                username: user.mobile || user.nickname,\n                name: user.nickname,\n                avatar: user.avatar,\n                ...roleInfo\n            };\n        } catch (error) {\n            console.error('获取用户信息失败:', error);\n            return null;\n        }\n    }\n};"]}