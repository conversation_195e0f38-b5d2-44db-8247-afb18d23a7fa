const fs = require('fs');
const path = require('path');
const mysql = require('mysql');
const md5 = require('md5');
const readline = require('readline');

// 数据库配置
const dbConfig = {
  host: '127.0.0.1',
  port: '3306',
  user: 'root',
  password: '19841020',
  database: 'hiolabsDB'
};

// 创建readline接口用于用户输入
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 创建数据库连接
const connection = mysql.createConnection(dbConfig);

// 连接数据库
connection.connect(err => {
  if (err) {
    console.error('数据库连接失败:', err);
    return;
  }
  console.log('数据库连接成功');

  // 检查admin表是否存在
  connection.query(`SHOW TABLES LIKE 'hiolabs_admin'`, (err, results) => {
    if (err) {
      console.error('查询表失败:', err);
      connection.end();
      return;
    }

    if (results.length === 0) {
      console.log('admin表不存在，需要先创建表');
      connection.end();
      return;
    }

    // 检查是否已存在admin用户
    connection.query(`SELECT * FROM hiolabs_admin WHERE username = 'admin'`, (err, results) => {
      if (err) {
        console.error('查询用户失败:', err);
        connection.end();
        return;
      }

      if (results.length > 0) {
        console.log('admin用户已存在，无需创建');
        console.log('如需重置密码，请手动删除现有admin用户后重新运行此脚本');
        connection.end();
        rl.close();
        return;
      }

      // 提示用户输入密码
      console.log('正在创建管理员账户...');
      rl.question('请输入管理员密码（至少6位字符）: ', (inputPassword) => {
        if (!inputPassword || inputPassword.length < 6) {
          console.log('密码长度不能少于6位字符，请重新运行脚本');
          connection.end();
          rl.close();
          return;
        }

        // 创建admin用户
        const password_salt = Math.random().toString(36).substring(2, 15);
        const password = md5(inputPassword + password_salt);

        const admin = {
          username: 'admin',
          password: password,
          password_salt: password_salt,
          name: '管理员',
          last_login_time: Math.floor(Date.now() / 1000),
          last_login_ip: '127.0.0.1',
          is_delete: 0
        };

        connection.query('INSERT INTO hiolabs_admin SET ?', admin, (err, results) => {
          if (err) {
            console.error('创建管理员失败:', err);
            connection.end();
            rl.close();
            return;
          }

          console.log('管理员创建成功！');
          console.log('用户名: admin');
          console.log('密码: ' + inputPassword);
          console.log('请妥善保管您的登录信息');
          connection.end();
          rl.close();
        });
      });
    });
  });
});
