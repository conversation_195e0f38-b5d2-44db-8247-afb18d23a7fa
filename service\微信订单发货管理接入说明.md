# 微信小程序订单发货管理接入说明

## 概述

本项目已集成微信小程序订单发货管理功能，符合微信平台对商家自营类小程序的运营规范要求。当商家发货时，系统会自动将发货信息同步到微信平台，确保用户能及时收到发货通知。

## 功能特性

### 1. 自动同步发货信息
- 商家发货时自动调用微信发货信息录入接口
- 支持快递发货、自提、虚拟商品等多种发货方式
- 自动构建符合微信要求的发货数据格式

### 2. 数据验证增强
- 严格的参数验证，符合微信官方要求
- RFC 3339时间格式标准化
- 联系方式自动掩码处理
- 商品描述长度限制（120字符）

### 3. 错误处理与日志
- 完善的错误处理机制
- 同步失败时记录详细错误日志
- 支持手动重试功能

### 4. 配置化管理
- 可通过配置文件控制功能开关
- 支持自定义消息跳转路径

## 配置说明

### 基础配置
在 `service/src/common/config/config.js` 中的微信配置部分：

```javascript
weixin: {
    appid: 'your_appid',           // 小程序 appid
    secret: 'your_secret',         // 小程序密钥
    mch_id: 'your_mch_id',         // 商户号
    partner_key: 'your_partner_key', // 微信支付密钥
    notify_url: 'your_notify_url',   // 支付回调地址

    // 订单发货管理配置
    shipping_management: {
        enabled: true,                    // 是否启用订单发货管理
        auto_sync: true,                  // 是否自动同步发货信息
        msg_jump_path: '/pages/order/detail' // 消息跳转路径
    }
}
```

### 权限配置
需要在微信小程序后台授权142权限集，用于订单发货管理功能。

## 使用方法

### 1. 自动同步（推荐）
系统已在以下发货操作中自动集成微信同步：

- **打印并发货**：`goDeliveryAction()` 方法
- **API发货**：`orderDeliveryAction()` 方法

无需额外操作，发货时会自动同步到微信平台。

### 2. 手动重试
如果自动同步失败，可以通过以下接口手动重试：

```
POST /admin/order/resyncWeixinShipping
参数：
{
    "orderId": "订单ID"
}
```

## 支持的发货方式

### 1. 快递发货（logistics_type: 1）
- 需要提供快递公司编码和快递单号
- 支持顺丰快递的联系方式要求
- 自动构建物流信息

### 2. 自提发货（logistics_type: 4）
- 适用于用户到店自提的场景
- 无需快递信息

### 3. 虚拟商品（logistics_type: 3）
- 适用于虚拟商品发货
- 如话费充值、点卡等

## 数据库字段说明

系统会在订单表中记录同步状态：

- `weixin_shipping_sync`: 同步状态（0-失败，1-成功）
- `weixin_shipping_sync_time`: 同步时间戳
- `weixin_shipping_sync_error`: 错误信息（JSON格式）

## 日志监控

### 控制台日志
系统会输出详细的同步日志：
```
=== 开始同步微信订单发货信息 ===
订单ID: 123 订单号: ORDER20241201001
物流类型: 1 快递信息: 有
发货数据构建完成: {...}
微信发货信息同步成功
```

### 错误日志
同步失败时会记录：
- 微信接口返回的错误码和错误信息
- 系统异常的详细堆栈信息

## 常见问题

### 1. 同步失败怎么办？
- 检查微信配置是否正确
- 确认小程序是否已授权142权限集
- 查看错误日志确定具体原因
- 使用手动重试接口

### 2. 如何关闭自动同步？
在配置文件中设置：
```javascript
shipping_management: {
    enabled: false  // 关闭功能
}
```

### 3. 支持哪些快递公司？
支持微信平台认可的所有快递公司，快递公司编码请参考微信官方文档。

### 4. 数据验证失败怎么办？
- 检查订单号、用户openid等基础数据是否正确
- 确认快递信息（快递单号、快递公司编码）是否完整
- 查看控制台日志中的具体验证错误信息

## 注意事项

1. **必须接入**：根据微信运营规范，提供实物商品的小程序必须接入订单发货管理
2. **数据准确性**：确保订单号、商品信息、用户openid等数据准确
3. **时效性**：建议在实际发货后立即同步信息
4. **错误处理**：关注同步失败的订单，及时处理

## 技术支持

如遇到技术问题，请：
1. 查看控制台日志
2. 检查数据库中的错误记录
3. 参考微信官方文档
4. 联系技术支持团队
