function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

const Base = require('./base.js');
const moment = require('moment');
module.exports = class extends Base {
    /**
     * index action
     * @return {Promise} []
     */
    indexAction() {
        var _this = this;

        return _asyncToGenerator(function* () {
            const page = _this.get('page') || 1;
            const size = _this.get('size') || 10;
            const name = _this.get('name') || '';

            // 使用JOIN查询，只获取用户存在的购物车记录
            const model = _this.model('cart');
            const data = yield model.alias('c').join({
                table: 'user',
                join: 'inner', // 使用inner join过滤掉用户不存在的记录
                as: 'u',
                on: ['c.user_id', 'u.id']
            }).where({
                'c.goods_name': ['like', `%${name}%`]
            }).field('c.*, u.nickname as user_nickname').order(['c.id DESC']).page(page, size).countSelect();

            for (const item of data.data) {
                item.add_time = moment.unix(item.add_time).format('YYYY-MM-DD HH:mm:ss');
                // 直接使用JOIN查询得到的nickname，无需再次查询用户表
                item.nickname = Buffer.from(item.user_nickname, 'base64').toString();
            }

            return _this.success(data);
        })();
    }

    // 删除购物车项
    deleteAction() {
        var _this2 = this;

        return _asyncToGenerator(function* () {
            const id = _this2.get('id');
            if (!id) {
                return _this2.fail('缺少购物车ID参数');
            }

            try {
                // 软删除，设置is_delete为1
                yield _this2.model('cart').where({ id: id }).update({ is_delete: 1 });
                return _this2.success('删除成功');
            } catch (error) {
                console.log('删除购物车项失败:', error.message);
                return _this2.fail('删除失败');
            }
        })();
    }

};