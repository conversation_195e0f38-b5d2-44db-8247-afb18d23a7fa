{"_from": "generate-function@^2.3.1", "_id": "generate-function@2.3.1", "_inBundle": false, "_integrity": "sha512-eeB5GfMNeevm/GRYq20ShmsaGcmI81kIX2K9XQx5miC8KdHaC6Jm0qQ8ZNeGOi7wYB8OsdxKs+Y2oVuTFuVwKQ==", "_location": "/generate-function", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "generate-function@^2.3.1", "name": "generate-function", "escapedName": "generate-function", "rawSpec": "^2.3.1", "saveSpec": null, "fetchSpec": "^2.3.1"}, "_requiredBy": ["/mysql2"], "_resolved": "https://registry.npmjs.org/generate-function/-/generate-function-2.3.1.tgz", "_shasum": "f069617690c10c868e73b8465746764f97c3479f", "_spec": "generate-function@^2.3.1", "_where": "D:\\py-ide\\hioshop-miniprogram-master\\node_modules\\mysql2", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/mafintosh/generate-function/issues"}, "bundleDependencies": false, "dependencies": {"is-property": "^1.0.2"}, "deprecated": false, "description": "Module that helps you write generated functions in Node", "devDependencies": {"tape": "^4.9.1"}, "homepage": "https://github.com/mafintosh/generate-function", "keywords": ["generate", "code", "generation", "function", "performance"], "license": "MIT", "main": "index.js", "name": "generate-function", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/generate-function.git"}, "scripts": {"test": "tape test.js"}, "version": "2.3.1"}