/* 售后申请页面样式 */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 订单信息 */
.order-info {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.order-sn {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.goods-item {
  display: flex;
  align-items: center;
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.goods-info {
  flex: 1;
}

.goods-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.goods-spec {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.goods-price {
  font-size: 32rpx;
  color: #e64340;
  font-weight: bold;
}

/* 表单区域 */
.form-section {
  background: #fff;
  margin-bottom: 20rpx;
}

.form-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  min-height: 80rpx;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  width: 160rpx;
  font-size: 30rpx;
  color: #333;
}

.form-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-input {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.form-picker {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  text-align: right;
}

.form-picker.placeholder {
  color: #999;
}

.arrow-right {
  width: 16rpx;
  height: 16rpx;
  border-top: 2rpx solid #ccc;
  border-right: 2rpx solid #ccc;
  transform: rotate(45deg);
  margin-left: 20rpx;
}

/* 退款金额 */
.amount-input {
  text-align: right;
  color: #e64340;
  font-weight: bold;
}

.max-amount {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 退款说明 */
.desc-textarea {
  width: 100%;
  min-height: 120rpx;
  font-size: 30rpx;
  color: #333;
}

/* 图片上传 */
.image-upload {
  padding: 30rpx;
}

.upload-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background: #e64340;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24rpx;
}

.add-image {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24rpx;
}

.add-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

/* 提交按钮 */
.submit-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #e64340;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 0;
}

.submit-btn:after {
  border: none;
}

.submit-btn.disabled {
  background: #ccc;
}

/* 退款类型选择 */
.refund-type {
  padding: 30rpx;
}

.type-options {
  display: flex;
}

.type-option {
  flex: 1;
  padding: 20rpx;
  text-align: center;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;
}

.type-option:last-child {
  margin-right: 0;
}

.type-option.active {
  border-color: #e64340;
  color: #e64340;
  background: #fff5f5;
}

/* 提示信息 */
.tips {
  padding: 30rpx;
  background: #fff9e6;
  margin-bottom: 20rpx;
}

.tips-text {
  font-size: 26rpx;
  color: #f39800;
  line-height: 1.5;
}
