{"version": 3, "sources": ["..\\..\\..\\src\\api\\model\\points.js"], "names": ["module", "exports", "think", "Model", "tableName", "getUserPoints", "userId", "userPoints", "model", "where", "user_id", "find", "isEmpty", "pointsId", "add", "total_points", "available_points", "used_points", "created_at", "Date", "updated_at", "id", "error", "console", "addUserPoints", "points", "type", "sourceId", "description", "transaction", "Error", "newTotalPoints", "newAvailablePoints", "update", "points_change", "points_type", "source_id", "balance_after", "success", "totalPoints", "availablePoints", "message", "consumeUserPoints", "newUsedPoints", "usedPoints", "getUserPointsLog", "page", "limit", "offset", "logs", "field", "order", "select", "total", "count", "totalPages", "Math", "ceil", "getPointsStats", "currentMonth", "getCurrentMonth", "monthEarned", "sum", "monthUsed", "signinEarned", "abs", "getPointsRanking", "sql", "rankings", "query", "getSystemPointsStats", "totalIssued", "totalConsumed", "totalCirculation", "today", "getCurrentDate", "todayIssued", "now", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;;AAEzC,MAAIC,SAAJ,GAAgB;AACd,WAAO,aAAP;AACD;;AAED;;;AAGMC,eAAN,CAAoBC,MAApB,EAA4B;AAAA;;AAAA;AAC1B,UAAI;AACF,YAAIC,aAAa,MAAM,MAAKC,KAAL,CAAW,aAAX,EAA0BC,KAA1B,CAAgC;AACrDC,mBAASJ;AAD4C,SAAhC,EAEpBK,IAFoB,EAAvB;;AAIA;AACA,YAAIT,MAAMU,OAAN,CAAcL,UAAd,CAAJ,EAA+B;AAC7B,gBAAMM,WAAW,MAAM,MAAKL,KAAL,CAAW,aAAX,EAA0BM,GAA1B,CAA8B;AACnDJ,qBAASJ,MAD0C;AAEnDS,0BAAc,CAFqC;AAGnDC,8BAAkB,CAHiC;AAInDC,yBAAa,CAJsC;AAKnDC,wBAAY,IAAIC,IAAJ,EALuC;AAMnDC,wBAAY,IAAID,IAAJ;AANuC,WAA9B,CAAvB;;AASAZ,uBAAa;AACXc,gBAAIR,QADO;AAEXH,qBAASJ,MAFE;AAGXS,0BAAc,CAHH;AAIXC,8BAAkB,CAJP;AAKXC,yBAAa;AALF,WAAb;AAOD;;AAED,eAAOV,UAAP;AACD,OA1BD,CA0BE,OAAOe,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,IAAP;AACD;AA9ByB;AA+B3B;;AAED;;;AAGME,eAAN,CAAoBlB,MAApB,EAA4BmB,MAA5B,EAAoCC,OAAO,QAA3C,EAAqDC,WAAW,IAAhE,EAAsEC,cAAc,EAApF,EAAwF;AAAA;;AAAA;AACtF,UAAI;AACF;AACA,eAAO,MAAM,OAAKpB,KAAL,CAAW,aAAX,EAA0BqB,WAA1B,mBAAsC,aAAY;AAC7D;AACA,gBAAMtB,aAAa,MAAM,OAAKF,aAAL,CAAmBC,MAAnB,CAAzB;AACA,cAAI,CAACC,UAAL,EAAiB;AACf,kBAAM,IAAIuB,KAAJ,CAAU,UAAV,CAAN;AACD;;AAED;AACA,gBAAMC,iBAAiBxB,WAAWQ,YAAX,GAA0BU,MAAjD;AACA,gBAAMO,qBAAqBzB,WAAWS,gBAAX,GAA8BS,MAAzD;;AAEA,gBAAM,OAAKjB,KAAL,CAAW,aAAX,EAA0BC,KAA1B,CAAgC;AACpCC,qBAASJ;AAD2B,WAAhC,EAEH2B,MAFG,CAEI;AACRlB,0BAAcgB,cADN;AAERf,8BAAkBgB,kBAFV;AAGRZ,wBAAY,IAAID,IAAJ;AAHJ,WAFJ,CAAN;;AAQA;AACA,gBAAM,OAAKX,KAAL,CAAW,YAAX,EAAyBM,GAAzB,CAA6B;AACjCJ,qBAASJ,MADwB;AAEjC4B,2BAAeT,MAFkB;AAGjCU,yBAAaT,IAHoB;AAIjCU,uBAAWT,QAJsB;AAKjCC,yBAAaA,WALoB;AAMjCS,2BAAeN,cANkB;AAOjCb,wBAAY,IAAIC,IAAJ;AAPqB,WAA7B,CAAN;;AAUA,iBAAO;AACLmB,qBAAS,IADJ;AAELC,yBAAaR,cAFR;AAGLS,6BAAiBR;AAHZ,WAAP;AAKD,SAnCY,EAAb;AAoCD,OAtCD,CAsCE,OAAOV,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO;AACLgB,mBAAS,KADJ;AAELhB,iBAAOA,MAAMmB;AAFR,SAAP;AAID;AA7CqF;AA8CvF;;AAED;;;AAGMC,mBAAN,CAAwBpC,MAAxB,EAAgCmB,MAAhC,EAAwCC,OAAO,SAA/C,EAA0DC,WAAW,IAArE,EAA2EC,cAAc,EAAzF,EAA6F;AAAA;;AAAA;AAC3F,UAAI;AACF;AACA,eAAO,MAAM,OAAKpB,KAAL,CAAW,aAAX,EAA0BqB,WAA1B,mBAAsC,aAAY;AAC7D;AACA,gBAAMtB,aAAa,MAAM,OAAKF,aAAL,CAAmBC,MAAnB,CAAzB;AACA,cAAI,CAACC,UAAL,EAAiB;AACf,kBAAM,IAAIuB,KAAJ,CAAU,UAAV,CAAN;AACD;;AAED;AACA,cAAIvB,WAAWS,gBAAX,GAA8BS,MAAlC,EAA0C;AACxC,kBAAM,IAAIK,KAAJ,CAAU,MAAV,CAAN;AACD;;AAED;AACA,gBAAME,qBAAqBzB,WAAWS,gBAAX,GAA8BS,MAAzD;AACA,gBAAMkB,gBAAgBpC,WAAWU,WAAX,GAAyBQ,MAA/C;;AAEA,gBAAM,OAAKjB,KAAL,CAAW,aAAX,EAA0BC,KAA1B,CAAgC;AACpCC,qBAASJ;AAD2B,WAAhC,EAEH2B,MAFG,CAEI;AACRjB,8BAAkBgB,kBADV;AAERf,yBAAa0B,aAFL;AAGRvB,wBAAY,IAAID,IAAJ;AAHJ,WAFJ,CAAN;;AAQA;AACA,gBAAM,OAAKX,KAAL,CAAW,YAAX,EAAyBM,GAAzB,CAA6B;AACjCJ,qBAASJ,MADwB;AAEjC4B,2BAAe,CAACT,MAFiB,EAET;AACxBU,yBAAaT,IAHoB;AAIjCU,uBAAWT,QAJsB;AAKjCC,yBAAaA,WALoB;AAMjCS,2BAAe9B,WAAWQ,YANO,EAMO;AACxCG,wBAAY,IAAIC,IAAJ;AAPqB,WAA7B,CAAN;;AAUA,iBAAO;AACLmB,qBAAS,IADJ;AAELC,yBAAahC,WAAWQ,YAFnB;AAGLyB,6BAAiBR,kBAHZ;AAILY,wBAAYD;AAJP,WAAP;AAMD,SAzCY,EAAb;AA0CD,OA5CD,CA4CE,OAAOrB,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO;AACLgB,mBAAS,KADJ;AAELhB,iBAAOA,MAAMmB;AAFR,SAAP;AAID;AAnD0F;AAoD5F;;AAED;;;AAGMI,kBAAN,CAAuBvC,MAAvB,EAA+BwC,OAAO,CAAtC,EAAyCC,QAAQ,EAAjD,EAAqD;AAAA;;AAAA;AACnD,UAAI;AACF,cAAMC,SAAS,CAACF,OAAO,CAAR,IAAaC,KAA5B;;AAEA,cAAME,OAAO,MAAM,OAAKzC,KAAL,CAAW,YAAX,EAAyBC,KAAzB,CAA+B;AAChDC,mBAASJ;AADuC,SAA/B,EAEhB4C,KAFgB,CAEV,oEAFU,EAGhBC,KAHgB,CAGV,iBAHU,EAIhBJ,KAJgB,CAIVC,MAJU,EAIFD,KAJE,EAKhBK,MALgB,EAAnB;;AAOA,cAAMC,QAAQ,MAAM,OAAK7C,KAAL,CAAW,YAAX,EAAyBC,KAAzB,CAA+B;AACjDC,mBAASJ;AADwC,SAA/B,EAEjBgD,KAFiB,EAApB;;AAIA,eAAO;AACLL,gBAAMA,IADD;AAELI,iBAAOA,KAFF;AAGLP,gBAAMA,IAHD;AAILC,iBAAOA,KAJF;AAKLQ,sBAAYC,KAAKC,IAAL,CAAUJ,QAAQN,KAAlB;AALP,SAAP;AAOD,OArBD,CAqBE,OAAOzB,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,IAAP;AACD;AAzBkD;AA0BpD;;AAED;;;AAGMoC,gBAAN,CAAqBpD,MAArB,EAA6B;AAAA;;AAAA;AAC3B,UAAI;AACF;AACA,cAAMC,aAAa,MAAM,OAAKF,aAAL,CAAmBC,MAAnB,CAAzB;;AAEA;AACA,cAAMqD,eAAe,OAAKC,eAAL,EAArB;AACA,cAAMC,cAAc,MAAM,OAAKrD,KAAL,CAAW,YAAX,EAAyBC,KAAzB,CAA+B;AACvDC,mBAASJ,MAD8C;AAEvD4B,yBAAe,CAAC,GAAD,EAAM,CAAN,CAFwC;AAGvDhB,sBAAY,CAAC,MAAD,EAAU,GAAEyC,YAAa,GAAzB;AAH2C,SAA/B,EAIvBG,GAJuB,CAInB,eAJmB,CAA1B;;AAMA;AACA,cAAMC,YAAY,MAAM,OAAKvD,KAAL,CAAW,YAAX,EAAyBC,KAAzB,CAA+B;AACrDC,mBAASJ,MAD4C;AAErD4B,yBAAe,CAAC,GAAD,EAAM,CAAN,CAFsC;AAGrDhB,sBAAY,CAAC,MAAD,EAAU,GAAEyC,YAAa,GAAzB;AAHyC,SAA/B,EAIrBG,GAJqB,CAIjB,eAJiB,CAAxB;;AAMA;AACA,cAAME,eAAe,MAAM,OAAKxD,KAAL,CAAW,YAAX,EAAyBC,KAAzB,CAA+B;AACxDC,mBAASJ,MAD+C;AAExD6B,uBAAa,CAAC,IAAD,EAAO,CAAC,QAAD,EAAW,OAAX,CAAP;AAF2C,SAA/B,EAGxB2B,GAHwB,CAGpB,eAHoB,CAA3B;;AAKA,eAAO;AACLvB,uBAAahC,WAAWQ,YADnB;AAELyB,2BAAiBjC,WAAWS,gBAFvB;AAGL4B,sBAAYrC,WAAWU,WAHlB;AAIL4C,uBAAaA,eAAe,CAJvB;AAKLE,qBAAWP,KAAKS,GAAL,CAASF,aAAa,CAAtB,CALN;AAMLC,wBAAcA,gBAAgB;AANzB,SAAP;AAQD,OAjCD,CAiCE,OAAO1C,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,IAAP;AACD;AArC0B;AAsC5B;;AAED;;;AAGM4C,kBAAN,CAAuBnB,QAAQ,EAA/B,EAAmC;AAAA;;AAAA;AACjC,UAAI;AACF,cAAMoB,MAAO;;;;;;;;;;;gBAWHpB,KAAM;OAXhB;;AAcA,cAAMqB,WAAW,MAAM,OAAK5D,KAAL,CAAW,aAAX,EAA0B6D,KAA1B,CAAgCF,GAAhC,CAAvB;AACA,eAAOC,QAAP;AACD,OAjBD,CAiBE,OAAO9C,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,YAAd,EAA4BA,KAA5B;AACA,eAAO,EAAP;AACD;AArBgC;AAsBlC;;AAED;;;AAGMgD,sBAAN,GAA6B;AAAA;;AAAA;AAC3B,UAAI;AACF;AACA,cAAMC,cAAc,MAAM,OAAK/D,KAAL,CAAW,YAAX,EAAyBC,KAAzB,CAA+B;AACvDyB,yBAAe,CAAC,GAAD,EAAM,CAAN;AADwC,SAA/B,EAEvB4B,GAFuB,CAEnB,eAFmB,CAA1B;;AAIA;AACA,cAAMU,gBAAgB,MAAM,OAAKhE,KAAL,CAAW,YAAX,EAAyBC,KAAzB,CAA+B;AACzDyB,yBAAe,CAAC,GAAD,EAAM,CAAN;AAD0C,SAA/B,EAEzB4B,GAFyB,CAErB,eAFqB,CAA5B;;AAIA;AACA,cAAMW,mBAAmB,MAAM,OAAKjE,KAAL,CAAW,aAAX,EAA0BsD,GAA1B,CAA8B,kBAA9B,CAA/B;;AAEA;AACA,cAAMY,QAAQ,OAAKC,cAAL,EAAd;AACA,cAAMC,cAAc,MAAM,OAAKpE,KAAL,CAAW,YAAX,EAAyBC,KAAzB,CAA+B;AACvDyB,yBAAe,CAAC,GAAD,EAAM,CAAN,CADwC;AAEvDhB,sBAAY,CAAC,MAAD,EAAU,GAAEwD,KAAM,GAAlB;AAF2C,SAA/B,EAGvBZ,GAHuB,CAGnB,eAHmB,CAA1B;;AAKA,eAAO;AACLS,uBAAaA,eAAe,CADvB;AAELC,yBAAehB,KAAKS,GAAL,CAASO,iBAAiB,CAA1B,CAFV;AAGLC,4BAAkBA,oBAAoB,CAHjC;AAILG,uBAAaA,eAAe;AAJvB,SAAP;AAMD,OA3BD,CA2BE,OAAOtD,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,eAAO,IAAP;AACD;AA/B0B;AAgC5B;;AAED;;;AAGAsC,oBAAkB;AAChB,UAAMiB,MAAM,IAAI1D,IAAJ,EAAZ;AACA,UAAM2D,OAAOD,IAAIE,WAAJ,EAAb;AACA,UAAMC,QAAQC,OAAOJ,IAAIK,QAAJ,KAAiB,CAAxB,EAA2BC,QAA3B,CAAoC,CAApC,EAAuC,GAAvC,CAAd;AACA,WAAQ,GAAEL,IAAK,IAAGE,KAAM,EAAxB;AACD;;AAED;;;AAGAL,mBAAiB;AACf,UAAME,MAAM,IAAI1D,IAAJ,EAAZ;AACA,UAAM2D,OAAOD,IAAIE,WAAJ,EAAb;AACA,UAAMC,QAAQC,OAAOJ,IAAIK,QAAJ,KAAiB,CAAxB,EAA2BC,QAA3B,CAAoC,CAApC,EAAuC,GAAvC,CAAd;AACA,UAAMC,MAAMH,OAAOJ,IAAIQ,OAAJ,EAAP,EAAsBF,QAAtB,CAA+B,CAA/B,EAAkC,GAAlC,CAAZ;AACA,WAAQ,GAAEL,IAAK,IAAGE,KAAM,IAAGI,GAAI,EAA/B;AACD;AAnTwC,CAA3C", "file": "..\\..\\..\\src\\api\\model\\points.js", "sourcesContent": ["module.exports = class extends think.Model {\n\n  get tableName() {\n    return 'user_points';\n  }\n\n  /**\n   * 获取用户积分信息\n   */\n  async getUserPoints(userId) {\n    try {\n      let userPoints = await this.model('user_points').where({\n        user_id: userId\n      }).find();\n\n      // 如果用户没有积分记录，创建一个\n      if (think.isEmpty(userPoints)) {\n        const pointsId = await this.model('user_points').add({\n          user_id: userId,\n          total_points: 0,\n          available_points: 0,\n          used_points: 0,\n          created_at: new Date(),\n          updated_at: new Date()\n        });\n        \n        userPoints = {\n          id: pointsId,\n          user_id: userId,\n          total_points: 0,\n          available_points: 0,\n          used_points: 0\n        };\n      }\n\n      return userPoints;\n    } catch (error) {\n      console.error('获取用户积分失败:', error);\n      return null;\n    }\n  }\n\n  /**\n   * 增加用户积分\n   */\n  async addUserPoints(userId, points, type = 'signin', sourceId = null, description = '') {\n    try {\n      // 开始事务\n      return await this.model('user_points').transaction(async () => {\n        // 1. 获取当前积分\n        const userPoints = await this.getUserPoints(userId);\n        if (!userPoints) {\n          throw new Error('获取用户积分失败');\n        }\n\n        // 2. 更新积分\n        const newTotalPoints = userPoints.total_points + points;\n        const newAvailablePoints = userPoints.available_points + points;\n\n        await this.model('user_points').where({\n          user_id: userId\n        }).update({\n          total_points: newTotalPoints,\n          available_points: newAvailablePoints,\n          updated_at: new Date()\n        });\n\n        // 3. 记录积分日志\n        await this.model('points_log').add({\n          user_id: userId,\n          points_change: points,\n          points_type: type,\n          source_id: sourceId,\n          description: description,\n          balance_after: newTotalPoints,\n          created_at: new Date()\n        });\n\n        return {\n          success: true,\n          totalPoints: newTotalPoints,\n          availablePoints: newAvailablePoints\n        };\n      });\n    } catch (error) {\n      console.error('增加用户积分失败:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n\n  /**\n   * 消费用户积分\n   */\n  async consumeUserPoints(userId, points, type = 'consume', sourceId = null, description = '') {\n    try {\n      // 开始事务\n      return await this.model('user_points').transaction(async () => {\n        // 1. 获取当前积分\n        const userPoints = await this.getUserPoints(userId);\n        if (!userPoints) {\n          throw new Error('获取用户积分失败');\n        }\n\n        // 2. 检查积分是否足够\n        if (userPoints.available_points < points) {\n          throw new Error('积分不足');\n        }\n\n        // 3. 更新积分\n        const newAvailablePoints = userPoints.available_points - points;\n        const newUsedPoints = userPoints.used_points + points;\n\n        await this.model('user_points').where({\n          user_id: userId\n        }).update({\n          available_points: newAvailablePoints,\n          used_points: newUsedPoints,\n          updated_at: new Date()\n        });\n\n        // 4. 记录积分日志\n        await this.model('points_log').add({\n          user_id: userId,\n          points_change: -points, // 负数表示消费\n          points_type: type,\n          source_id: sourceId,\n          description: description,\n          balance_after: userPoints.total_points, // 总积分不变\n          created_at: new Date()\n        });\n\n        return {\n          success: true,\n          totalPoints: userPoints.total_points,\n          availablePoints: newAvailablePoints,\n          usedPoints: newUsedPoints\n        };\n      });\n    } catch (error) {\n      console.error('消费用户积分失败:', error);\n      return {\n        success: false,\n        error: error.message\n      };\n    }\n  }\n\n  /**\n   * 获取用户积分日志\n   */\n  async getUserPointsLog(userId, page = 1, limit = 20) {\n    try {\n      const offset = (page - 1) * limit;\n      \n      const logs = await this.model('points_log').where({\n        user_id: userId\n      }).field('points_change, points_type, description, balance_after, created_at')\n        .order('created_at DESC')\n        .limit(offset, limit)\n        .select();\n\n      const total = await this.model('points_log').where({\n        user_id: userId\n      }).count();\n\n      return {\n        logs: logs,\n        total: total,\n        page: page,\n        limit: limit,\n        totalPages: Math.ceil(total / limit)\n      };\n    } catch (error) {\n      console.error('获取积分日志失败:', error);\n      return null;\n    }\n  }\n\n  /**\n   * 获取积分统计信息\n   */\n  async getPointsStats(userId) {\n    try {\n      // 获取用户积分信息\n      const userPoints = await this.getUserPoints(userId);\n      \n      // 获取本月获得积分\n      const currentMonth = this.getCurrentMonth();\n      const monthEarned = await this.model('points_log').where({\n        user_id: userId,\n        points_change: ['>', 0],\n        created_at: ['LIKE', `${currentMonth}%`]\n      }).sum('points_change');\n\n      // 获取本月消费积分\n      const monthUsed = await this.model('points_log').where({\n        user_id: userId,\n        points_change: ['<', 0],\n        created_at: ['LIKE', `${currentMonth}%`]\n      }).sum('points_change');\n\n      // 获取签到获得的积分\n      const signinEarned = await this.model('points_log').where({\n        user_id: userId,\n        points_type: ['IN', ['signin', 'bonus']]\n      }).sum('points_change');\n\n      return {\n        totalPoints: userPoints.total_points,\n        availablePoints: userPoints.available_points,\n        usedPoints: userPoints.used_points,\n        monthEarned: monthEarned || 0,\n        monthUsed: Math.abs(monthUsed || 0),\n        signinEarned: signinEarned || 0\n      };\n    } catch (error) {\n      console.error('获取积分统计失败:', error);\n      return null;\n    }\n  }\n\n  /**\n   * 获取积分排行榜\n   */\n  async getPointsRanking(limit = 10) {\n    try {\n      const sql = `\n        SELECT \n          up.user_id,\n          u.nickname,\n          u.avatar,\n          up.total_points,\n          up.available_points\n        FROM user_points up\n        LEFT JOIN user u ON up.user_id = u.id\n        WHERE up.total_points > 0\n        ORDER BY up.total_points DESC\n        LIMIT ${limit}\n      `;\n\n      const rankings = await this.model('user_points').query(sql);\n      return rankings;\n    } catch (error) {\n      console.error('获取积分排行榜失败:', error);\n      return [];\n    }\n  }\n\n  /**\n   * 系统积分统计\n   */\n  async getSystemPointsStats() {\n    try {\n      // 总积分发放量\n      const totalIssued = await this.model('points_log').where({\n        points_change: ['>', 0]\n      }).sum('points_change');\n\n      // 总积分消费量\n      const totalConsumed = await this.model('points_log').where({\n        points_change: ['<', 0]\n      }).sum('points_change');\n\n      // 当前流通积分\n      const totalCirculation = await this.model('user_points').sum('available_points');\n\n      // 今日发放积分\n      const today = this.getCurrentDate();\n      const todayIssued = await this.model('points_log').where({\n        points_change: ['>', 0],\n        created_at: ['LIKE', `${today}%`]\n      }).sum('points_change');\n\n      return {\n        totalIssued: totalIssued || 0,\n        totalConsumed: Math.abs(totalConsumed || 0),\n        totalCirculation: totalCirculation || 0,\n        todayIssued: todayIssued || 0\n      };\n    } catch (error) {\n      console.error('获取系统积分统计失败:', error);\n      return null;\n    }\n  }\n\n  /**\n   * 获取当前月份 YYYY-MM\n   */\n  getCurrentMonth() {\n    const now = new Date();\n    const year = now.getFullYear();\n    const month = String(now.getMonth() + 1).padStart(2, '0');\n    return `${year}-${month}`;\n  }\n\n  /**\n   * 获取当前日期 YYYY-MM-DD\n   */\n  getCurrentDate() {\n    const now = new Date();\n    const year = now.getFullYear();\n    const month = String(now.getMonth() + 1).padStart(2, '0');\n    const day = String(now.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n};\n"]}