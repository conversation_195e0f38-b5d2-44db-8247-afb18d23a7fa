<template>
	<div class="content-page bg-gray-50 min-h-screen">
		<!-- 面包屑导航 -->
		<div class="flex items-center text-sm text-gray-500 mb-4">
			<a href="#" class="hover:text-primary">首页</a>
			<span class="mx-2">/</span>
			<span class="text-gray-700">购物车管理</span>
		</div>

		<!-- 统计卡片 -->
		<div style="display: flex; gap: 1.5rem; margin-bottom: 1.5rem; flex-wrap: nowrap; overflow-x: auto;">
			<div style="flex: 1; min-width: 200px; background-color: white; border-radius: 0.5rem; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); padding: 1.25rem;">
				<div style="display: flex; align-items: center; justify-content: space-between;">
					<div>
						<p style="color: #6b7280; font-size: 0.875rem;">购物车总数</p>
						<h3 style="font-size: 1.5rem; font-weight: 700; margin-top: 0.25rem;">{{ total || 0 }}</h3>
						<p style="color: #10b981; font-size: 0.875rem; margin-top: 0.25rem;">
							活跃购物车
						</p>
					</div>
					<div style="width: 3rem; height: 3rem; display: flex; align-items: center; justify-content: center; background-color: #dbeafe; color: #4f46e5; border-radius: 9999px;">
						<i class="el-icon-shopping-cart-2" style="font-size: 1.5rem;"></i>
					</div>
				</div>
			</div>
		</div>

		<!-- 购物车管理标题 -->
		<div class="mb-6">
			<h1 class="text-2xl font-bold">购物车管理</h1>
		</div>

		<!-- 筛选区域 -->
		<div class="bg-white rounded shadow mb-6 p-4">
			<div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
				<input
					v-model="filterForm.name"
					placeholder="搜索商品名称..."
					class="px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary w-64 text-sm"
				/>
				<button
					@click="onSubmitFilter"
					class="bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-button text-gray-700 flex items-center whitespace-nowrap"
				>
					<i class="el-icon-search mr-1"></i> 搜索
				</button>
			</div>
		</div>

		<!-- 购物车列表 -->
		<div class="table-container">
			<div v-if="tableData.length === 0" class="p-4 text-center text-gray-500">
				暂无购物车数据
			</div>
			<div v-else class="p-2 text-xs text-gray-400">
				共 {{ tableData.length }} 条购物车记录
			</div>

			<table class="w-full" style="width: 100%; border-collapse: collapse;">
				<thead>
					<tr style="background-color: #f9fafb; color: #6b7280; font-size: 0.75rem;">
						<th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 5%;">
							<input type="checkbox" style="width: 1rem; height: 1rem;" />
						</th>
						<th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 40%;">商品信息</th>
						<th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 15%;">用户信息</th>
						<th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 10%;">数量</th>
						<th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 10%;">价格</th>
						<th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 15%;">添加时间</th>
						<th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 15%;">操作</th>
					</tr>
				</thead>
				<tbody style="border-top: 1px solid #e5e7eb;">
					<tr v-for="item in tableData" :key="item.id"
						style="border-bottom: 1px solid #f3f4f6; cursor: pointer;"
						@mouseover="$event.target.style.backgroundColor='#f9fafb'"
						@mouseout="$event.target.style.backgroundColor=''">

						<!-- 复选框 -->
						<td style="padding: 1rem; vertical-align: middle; text-align: center;">
							<input type="checkbox" style="width: 1rem; height: 1rem;" />
						</td>

						<!-- 商品信息 -->
						<td style="padding: 1rem; vertical-align: middle;">
							<div style="display: flex; align-items: center;">
								<img
									:src="item.list_pic_url || '/static/images/default-goods.png'"
									alt="商品图片"
									style="width: 3rem; height: 3rem; object-fit: cover; border-radius: 0.5rem; margin-right: 0.75rem; flex-shrink: 0;"
								/>
								<div style="flex: 1; min-width: 0;">
									<div style="font-weight: 500; font-size: 0.875rem; margin-bottom: 0.25rem; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
										{{ item.goods_name }}
									</div>
									<div style="color: #6b7280; font-size: 0.75rem; margin-bottom: 0.125rem;">
										商品ID：{{ item.goods_id }}
									</div>
									<div style="color: #6b7280; font-size: 0.75rem;">
										规格：{{ item.goods_specifition_name_value || '默认规格' }}
									</div>
								</div>
							</div>
						</td>

						<!-- 用户信息 -->
						<td style="padding: 1rem; vertical-align: middle;">
							<div style="font-size: 0.875rem; font-weight: 500; margin-bottom: 0.25rem;">
								{{ item.nickname || '未知用户' }}
							</div>
							<div style="color: #6b7280; font-size: 0.75rem;">
								ID: {{ item.user_id }}
							</div>
						</td>

						<!-- 数量 -->
						<td style="padding: 1rem; vertical-align: middle;">
							<div style="font-weight: 500; font-size: 0.875rem; text-align: center;">
								{{ item.number }}
							</div>
						</td>

						<!-- 价格 -->
						<td style="padding: 1rem; vertical-align: middle;">
							<div style="font-weight: 500; font-size: 0.875rem; color: #e6a23c;">
								¥{{ item.retail_price }}
							</div>
						</td>

						<!-- 添加时间 -->
						<td style="padding: 1rem; vertical-align: middle;">
							<div style="color: #6b7280; font-size: 0.75rem;">
								{{ item.add_time }}
							</div>
							<div v-if="item.is_delete == 1" style="color: #ef4444; font-size: 0.75rem; margin-top: 0.125rem;">
								已删除
							</div>
						</td>

						<!-- 操作 -->
						<td style="padding: 1rem; vertical-align: middle;">
							<div style="display: flex; flex-direction: column; gap: 0.25rem;">
								<button
									style="color: #4f46e5; font-size: 0.75rem; white-space: nowrap; background: none; border: none; cursor: pointer; text-align: left; padding: 0;"
									@click="handleRowEdit(item)"
								>
									查看详情
								</button>
								<button
									style="color: #ef4444; font-size: 0.75rem; white-space: nowrap; background: none; border: none; cursor: pointer; text-align: left; padding: 0;"
									@click="handleRowDelete(item)"
								>
									删除
								</button>
							</div>
						</td>
					</tr>
				</tbody>
			</table>
		</div>

		<!-- 分页 -->
		<div style="display: flex; justify-content: space-between; align-items: center; margin-top: 1.5rem; padding: 1rem; background-color: white; border-radius: 0.5rem; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);">
			<div style="font-size: 0.875rem; color: #6b7280;">
				显示第 {{ (page - 1) * 10 + 1 }} - {{ Math.min(page * 10, total) }} 条，共 {{ total }} 条记录
			</div>
			<el-pagination
				@current-change="handlePageChange"
				:current-page="page"
				:page-size="10"
				layout="prev, pager, next"
				:total="total"
				class="modern-pagination"
			>
			</el-pagination>
		</div>

		<!-- 用户详情弹窗 -->
		<el-dialog
			title="用户详细信息"
			:visible.sync="showUserDetailDialog"
			width="600px"
			:before-close="handleCloseUserDetail"
		>
			<div v-if="currentUserDetail" class="user-detail-content">
				<!-- 基本信息 -->
				<div class="detail-section">
					<h3 class="section-title">基本信息</h3>
					<div class="info-grid">
						<div class="info-item">
							<label>用户ID：</label>
							<span>{{ currentUserDetail.id }}</span>
						</div>
						<div class="info-item">
							<label>昵称：</label>
							<span>{{ currentUserDetail.nickname || '未设置' }}</span>
						</div>
						<div class="info-item">
							<label>手机号：</label>
							<span>{{ currentUserDetail.mobile || '未绑定' }}</span>
						</div>
						<div class="info-item">
							<label>注册时间：</label>
							<span>{{ currentUserDetail.add_time }}</span>
						</div>
					</div>
				</div>

				<!-- 收货地址信息 -->
				<div class="detail-section">
					<h3 class="section-title">收货地址</h3>
					<div v-if="userAddresses.length > 0">
						<div v-for="(address, index) in userAddresses" :key="address.id" class="address-item">
							<div class="address-header">
								<span class="address-title">地址 {{ index + 1 }}</span>
								<el-tag v-if="address.is_default == 1" type="success" size="mini">默认地址</el-tag>
							</div>
							<div class="address-content">
								<div class="info-item">
									<label>收货人：</label>
									<span>{{ address.name }}</span>
								</div>
								<div class="info-item">
									<label>手机号：</label>
									<span>{{ address.mobile }}</span>
								</div>
								<div class="info-item">
									<label>详细地址：</label>
									<span>{{ getFullAddress(address) }}</span>
								</div>
							</div>
						</div>
					</div>
					<div v-else class="no-data">
						<i class="el-icon-location-outline"></i>
						<span>暂无收货地址</span>
					</div>
				</div>

				<!-- 购物车统计 -->
				<div class="detail-section">
					<h3 class="section-title">购物车统计</h3>
					<div class="stats-grid">
						<div class="stat-item">
							<div class="stat-number">{{ userCartStats.totalItems || 0 }}</div>
							<div class="stat-label">购物车商品数</div>
						</div>
						<div class="stat-item">
							<div class="stat-number">¥{{ userCartStats.totalAmount || '0.00' }}</div>
							<div class="stat-label">购物车总金额</div>
						</div>
					</div>
				</div>
			</div>

			<div v-else class="loading-content">
				<i class="el-icon-loading"></i>
				<span>加载中...</span>
			</div>

			<span slot="footer" class="dialog-footer">
				<el-button @click="showUserDetailDialog = false">关闭</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>

export default {
	data() {
		return {
			page: 1,
			total: 0,
			filterForm: {
				name: ''
			},
			tableData: [],
			// 用户详情弹窗相关
			showUserDetailDialog: false,
			currentUserDetail: null,
			userAddresses: [],
			userCartStats: {},
			loadingUserDetail: false
		}
	},
	methods: {
		handlePageChange(val) {
			this.page = val;
			//保存到localStorage
			localStorage.setItem('shopCartPage', this.page)
			localStorage.setItem('shopCartFilterForm', JSON.stringify(this.filterForm));
			this.getList()
		},
		
		onSubmitFilter() {
			this.page = 1
			this.getList()
		},
		getList() {
			this.axios.get('shopcart', {
				params: {
					page: this.page,
					name: this.filterForm.name
				}
			}).then((response) => {
                this.tableData = response.data.data.data
                this.page = response.data.data.currentPage
                this.total = response.data.data.count
			})
		},

		// 查看用户详情
		async handleRowEdit(item) {
			console.log('查看详情', item)
			this.showUserDetailDialog = true
			this.loadingUserDetail = true
			this.currentUserDetail = null
			this.userAddresses = []
			this.userCartStats = {}

			try {
				// 获取用户详细信息
				await this.getUserDetail(item.user_id)
				// 获取用户地址信息
				await this.getUserAddresses(item.user_id)
				// 获取用户购物车统计
				await this.getUserCartStats(item.user_id)
			} catch (error) {
				console.error('获取用户详情失败:', error)
				this.$message.error('获取用户详情失败')
			} finally {
				this.loadingUserDetail = false
			}
		},

		// 删除购物车项
		handleRowDelete(item) {
			this.$confirm('确定要删除这个购物车项吗？', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.axios.get(`shopcart/delete`, {
					params: { id: item.id }
				}).then(() => {
					this.$message.success('删除成功')
					this.getList()
				}).catch(() => {
					this.$message.error('删除失败')
				})
			})
		},

		// 获取用户详细信息
		async getUserDetail(userId) {
			const response = await this.axios.get(`user/info`, {
				params: { id: userId }
			})
			this.currentUserDetail = response.data.data
			// 格式化时间
			if (this.currentUserDetail.register_time) {
				this.currentUserDetail.add_time = this.currentUserDetail.register_time
			}
		},

		// 获取用户地址信息
		async getUserAddresses(userId) {
			try {
				const response = await this.axios.get(`user/addresses`, {
					params: { id: userId }
				})
				this.userAddresses = response.data.data || []
			} catch (error) {
				console.log('获取地址信息失败:', error)
				this.userAddresses = []
			}
		},

		// 获取用户购物车统计
		async getUserCartStats(userId) {
			try {
				const response = await this.axios.get(`user/cart-stats`, {
					params: { id: userId }
				})
				this.userCartStats = response.data.data || {}
			} catch (error) {
				console.log('获取购物车统计失败:', error)
				this.userCartStats = {}
			}
		},

		// 关闭用户详情弹窗
		handleCloseUserDetail() {
			this.showUserDetailDialog = false
			this.currentUserDetail = null
			this.userAddresses = []
			this.userCartStats = {}
		},

		// 获取完整地址
		getFullAddress(address) {
			const parts = []
			if (address.province) parts.push(address.province)
			if (address.city) parts.push(address.city)
			if (address.county) parts.push(address.county)
			if (address.address_detail) parts.push(address.address_detail)
			return parts.join(' ')
		},

		// 格式化时间
		formatTime(timestamp) {
			if (!timestamp) return '未知'
			const date = new Date(timestamp * 1000)
			return date.toLocaleString('zh-CN')
		}
	},
	components: {

	},
	mounted() {
		this.getList();
	}
}

</script>

<style scoped>
/* 表格样式 */
.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-container table {
  border-collapse: collapse;
}

.table-container th {
  background-color: #f9fafb;
  font-weight: 500;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.table-container td {
  border-bottom: 1px solid #f3f4f6;
  vertical-align: top;
}

.table-container tr:hover {
  background-color: #f9fafb;
}

/* 按钮样式 */
.rounded-button {
  border-radius: 6px;
}

/* 主色调 */
.text-primary {
  color: #4f46e5;
}

.bg-primary {
  background-color: #4f46e5;
}

/* 分页样式 */
.modern-pagination .el-pagination {
  font-weight: normal;
}

.modern-pagination .el-pager li {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin: 0 2px;
}

.modern-pagination .el-pager li.active {
  background-color: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

.modern-pagination .btn-prev,
.modern-pagination .btn-next {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}
/* 用户详情弹窗样式 */
.user-detail-content {
	max-height: 500px;
	overflow-y: auto;
}

.detail-section {
	margin-bottom: 24px;
	padding-bottom: 16px;
	border-bottom: 1px solid #f0f0f0;
}

.detail-section:last-child {
	border-bottom: none;
	margin-bottom: 0;
}

.section-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
	margin: 0 0 16px 0;
	padding-bottom: 8px;
	border-bottom: 2px solid #409eff;
	display: inline-block;
}

.info-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 12px;
}

.info-item {
	display: flex;
	align-items: center;
	padding: 8px 0;
}

.info-item label {
	font-weight: 500;
	color: #666;
	min-width: 80px;
	margin-right: 8px;
}

.info-item span {
	color: #333;
	flex: 1;
}

.address-item {
	background: #f8f9fa;
	border-radius: 8px;
	padding: 16px;
	margin-bottom: 12px;
}

.address-item:last-child {
	margin-bottom: 0;
}

.address-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;
}

.address-title {
	font-weight: 500;
	color: #333;
}

.address-content .info-grid {
	grid-template-columns: 1fr;
	gap: 8px;
}

.stats-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 16px;
}

.stat-item {
	text-align: center;
	background: #f8f9fa;
	border-radius: 8px;
	padding: 16px;
}

.stat-number {
	font-size: 24px;
	font-weight: 600;
	color: #409eff;
	margin-bottom: 4px;
}

.stat-label {
	font-size: 14px;
	color: #666;
}

.no-data {
	text-align: center;
	padding: 32px;
	color: #999;
}

.no-data i {
	font-size: 48px;
	margin-bottom: 8px;
	display: block;
}

.loading-content {
	text-align: center;
	padding: 32px;
	color: #666;
}

.loading-content i {
	font-size: 24px;
	margin-right: 8px;
}

</style>
