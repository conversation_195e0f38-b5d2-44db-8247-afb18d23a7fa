/**
 * 位置相关配置备份文件
 * 审核通过后需要恢复的配置
 *
 * 使用说明：
 * 1. 审核通过后，将以下配置添加回 app.json
 * 2. 恢复定位按钮显示（移除 style="display: none;"）
 *
 * 注意：已移除 chooseLocation 相关功能，只保留自动定位
 */

// 需要在 app.json 中恢复的 permission 配置（添加到 tabBar 配置后面）
"permission": {
    "scope.address": {
        "desc": "你的地址信息将用于快速填写收货地址"
    },
    "scope.userLocation": {
        "desc": "你的位置信息将用于获取当前地址"
    }
},

// 需要在 requiredPrivateInfos 数组中恢复的接口
"requiredPrivateInfos": [
    "getLocation",
    "chooseAddress"
],

// 需要在 pages/ucenter/address-detail/index.wxml 中恢复的定位按钮
// 移除 style="display: none;" 即可
<view class="location-btn" bindtap="getLocation">
    <image class="location-icon" src="/images/icon/location.png"></image>
    <text class="location-text">定位</text>
</view>
