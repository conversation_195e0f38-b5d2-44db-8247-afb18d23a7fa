<view class="container">
	<view class="tab-nav">
		<view class="tab {{ showType == 0 ? 'active' : ''}}" bindtap="switchTab" data-index='0'>全部</view>
		<view class="tab {{ showType == 1 ? 'active' : ''}}" bindtap="switchTab" data-index='1'>
			<view wx:if="{{status.toPay > 0 }}" class='list-num'>{{status.toPay}}</view>待付款
		</view>
		<view class="tab {{ showType == 2 ? 'active' : ''}}" bindtap="switchTab" data-index='2'>
			<view wx:if="{{status.toDelivery > 0 }}" class='list-num'>{{status.toDelivery}}</view>待发货
		</view>
		<view class="tab {{ showType == 3 ? 'active' : ''}}" bindtap="switchTab" data-index='3'>
			<view wx:if="{{status.toReceive > 0 }}" class='list-num'>{{status.toReceive}}</view>待收货
		</view>
	</view>

	<view class="no-order {{hasOrder == 1? 'show':'' }}" hidden="" wx:if="{{orderList.length <= 0}}">
		<!-- wx:if="{{orderList.length <= 0}}" -->
		<image src="/images/icon/no-order.png" class="no-order-img"></image>
		<view class="text">您目前没有相关订单</view>
		<view class="to-index-btn" bindtap="toIndexPage">
			马上去逛逛
		</view>
		<!-- <view class="to-index-btn" bindtap="test">
            马上去逛逛
        </view>  -->
	</view>
	<view wx:else class="orders-container">
		<!-- 订单卡片 - 参考main.html设计 -->
		<view bindtap='toOrderDetails' class='order-card' data-id="{{item.id}}" wx:for="{{orderList}}" wx:key="id">
			<!-- 订单头部 -->
			<view class='order-header'>
				<view class='order-time'>{{item.add_time}}</view>
				<view class='order-status-wrap'>
					<view class="offline-pay-tag" wx:if="{{item.offline_pay}}">线下支付</view>
					<!-- 如果有售后申请，显示售后状态 -->
					<view wx:if="{{item.hasRefund}}" class="order-status status-refund">{{item.display_status_text}}</view>
					<!-- 否则显示订单状态 -->
					<view wx:else class="order-status {{item.order_status == 101 ? 'status-pending' : item.order_status == 201 ? 'status-paid' : item.order_status == 300 ? 'status-shipped' : item.order_status == 401 ? 'status-success' : 'status-closed'}}">{{item.order_status_text}}</view>
				</view>
			</view>

			<!-- 商品信息 -->
			<view class="order-goods">
				<view class="goods-images">
					<image wx:for="{{item.goodsList}}" wx:key="id" wx:for-item="gitem" wx:if="{{index<3}}"
						   src="{{gitem.list_pic_url}}" class="goods-thumb" mode="aspectFill" />
				</view>
				<view class='goods-details'>
					<view class="goods-names">
						<view class="goods-name" wx:for="{{item.goodsList}}" wx:key="id" wx:for-item="gitem" wx:if="{{index<2}}">
							{{gitem.goods_name}}
						</view>
						<view class="more-goods" wx:if="{{item.goodsList.length > 2}}">
							等{{item.goodsCount}}件商品
						</view>
					</view>
					<view class='goods-count'>共{{item.goodsCount}}件</view>
				</view>
			</view>



			<!-- 订单底部 -->
			<view class='order-footer'>
				<view class='order-total'>
					<text class="total-label">总价: </text>
					<text class='total-price'>¥{{item.actual_price}}</text>
					<text class='freight-info'>(含运费 ¥{{item.freight_price}})</text>
				</view>
				<!-- 只保留待付款状态的支付按钮 -->
				<view wx:if="{{item.handleOption.cancel && item.handleOption.pay}}" class="order-actions">
					<view class="action-buttons">
						<view catchtap='payOrder' class="btn-primary" data-orderid="{{item.id}}">继续支付</view>
					</view>
				</view>
				<!-- 其他状态不显示任何内容 -->
				<view wx:else class="order-arrow">
				</view>
			</view>
		</view>
		<view wx:if="{{showTips}}" class="no-more-tip">没有更多订单了</view>
	</view>
</view>