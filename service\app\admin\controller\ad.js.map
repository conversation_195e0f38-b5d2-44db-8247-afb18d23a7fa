{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\ad.js"], "names": ["Base", "require", "moment", "module", "exports", "indexAction", "page", "get", "size", "model", "data", "where", "is_delete", "order", "countSelect", "item", "end_time", "unix", "format", "enabled", "success", "updateSortAction", "id", "post", "sort", "update", "sort_order", "infoAction", "find", "storeAction", "isPost", "values", "console", "log", "parseInt", "Date", "getTime", "ex", "goods_id", "think", "isEmpty", "link_type", "link", "add", "fail", "getallrelateAction", "is_on_sale", "field", "select", "destoryAction", "limit", "saleStatusAction", "status", "sale"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;AACAE,OAAOC,OAAP,GAAiB,cAAcJ,IAAd,CAAmB;AAChC;;;;AAIMK,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAMC,OAAO,MAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,MAAKD,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,kBAAME,QAAQ,MAAKA,KAAL,CAAW,IAAX,CAAd;AACA,kBAAMC,OAAO,MAAMD,MAAME,KAAN,CAAY;AAC3BC,2BAAW;AADgB,aAAZ,EAEhBC,KAFgB,CAEV,CAAC,QAAD,CAFU,EAEEP,IAFF,CAEOA,IAFP,EAEaE,IAFb,EAEmBM,WAFnB,EAAnB;AAGA,iBAAK,MAAMC,IAAX,IAAmBL,KAAKA,IAAxB,EAA8B;AAC1B,oBAAIK,KAAKC,QAAL,IAAiB,CAArB,EAAwB;AACpBD,yBAAKC,QAAL,GAAgBd,OAAOe,IAAP,CAAYF,KAAKC,QAAjB,EAA2BE,MAA3B,CAAkC,qBAAlC,CAAhB;AACH;AACD,oBAAIH,KAAKI,OAAL,IAAgB,CAApB,EAAuB;AACnBJ,yBAAKI,OAAL,GAAe,IAAf;AACH,iBAFD,MAEO;AACHJ,yBAAKI,OAAL,GAAe,KAAf;AACH;AACJ;AACD,mBAAO,MAAKC,OAAL,CAAaV,IAAb,CAAP;AAjBgB;AAkBnB;AACKW,oBAAN,GAAyB;AAAA;;AAAA;AACrB,kBAAMC,KAAK,OAAKC,IAAL,CAAU,IAAV,CAAX;AACA,kBAAMC,OAAO,OAAKD,IAAL,CAAU,MAAV,CAAb;AACA,kBAAMd,QAAQ,OAAKA,KAAL,CAAW,IAAX,CAAd;AACA,kBAAMC,OAAO,MAAMD,MAAME,KAAN,CAAY;AAC3BW,oBAAIA;AADuB,aAAZ,EAEhBG,MAFgB,CAET;AACNC,4BAAYF;AADN,aAFS,CAAnB;AAKA,mBAAO,OAAKJ,OAAL,CAAaV,IAAb,CAAP;AATqB;AAUxB;AACKiB,cAAN,GAAmB;AAAA;;AAAA;AACf,kBAAML,KAAK,OAAKf,GAAL,CAAS,IAAT,CAAX;AACA,kBAAME,QAAQ,OAAKA,KAAL,CAAW,IAAX,CAAd;AACA,kBAAMC,OAAO,MAAMD,MAAME,KAAN,CAAY;AAC3BW,oBAAIA;AADuB,aAAZ,EAEhBM,IAFgB,EAAnB;AAGA,mBAAO,OAAKR,OAAL,CAAaV,IAAb,CAAP;AANe;AAOlB;AACKmB,eAAN,GAAoB;AAAA;;AAAA;AAChB,gBAAI,CAAC,OAAKC,MAAV,EAAkB;AACd,uBAAO,KAAP;AACH;AACD,kBAAMC,SAAS,OAAKR,IAAL,EAAf;AACAS,oBAAQC,GAAR,CAAYF,MAAZ;AACAA,mBAAOf,QAAP,GAAkBkB,SAAS,IAAIC,IAAJ,CAASJ,OAAOf,QAAhB,EAA0BoB,OAA1B,KAAsC,IAA/C,CAAlB;AACA,kBAAMd,KAAK,OAAKC,IAAL,CAAU,IAAV,CAAX;AACA,kBAAMd,QAAQ,OAAKA,KAAL,CAAW,IAAX,CAAd;AACA,gBAAIa,KAAK,CAAT,EAAY;AACR,sBAAMb,MAAME,KAAN,CAAY;AACdW,wBAAIA;AADU,iBAAZ,EAEHG,MAFG,CAEIM,MAFJ,CAAN;AAGH,aAJD,MAIO;AACH,oBAAIM,KAAK,MAAM5B,MAAME,KAAN,CAAY;AACvB2B,8BAAUP,OAAOO,QADM;AAEvB1B,+BAAU;AAFa,iBAAZ,EAGZgB,IAHY,EAAf;AAIA,oBAAIW,MAAMC,OAAN,CAAcH,EAAd,CAAJ,EAAuB;AACnB,2BAAON,OAAOT,EAAd;AACA,wBAAIS,OAAOU,SAAP,IAAoB,CAAxB,EAA2B;AACvBV,+BAAOW,IAAP,GAAc,EAAd;AACH,qBAFD,MAEO;AACHX,+BAAOO,QAAP,GAAkB,CAAlB;AACH;AACD,0BAAM7B,MAAMkC,GAAN,CAAUZ,MAAV,CAAN;AACH,iBARD,MAQO;AACH,2BAAO,OAAKa,IAAL,CAAU,GAAV,EAAe,MAAf,CAAP;AACH;AACJ;AACD,mBAAO,OAAKxB,OAAL,CAAaW,MAAb,CAAP;AA9BgB;AA+BnB;AACKc,sBAAN,GAA2B;AAAA;;AAAA;AACvB,gBAAInC,OAAO,MAAM,OAAKD,KAAL,CAAW,OAAX,EAAoBE,KAApB,CAA0B;AACvCmC,4BAAY,CAD2B;AAEvClC,2BAAW;AAF4B,aAA1B,EAGdmC,KAHc,CAGR,sBAHQ,EAGgBC,MAHhB,EAAjB;AAIA,mBAAO,OAAK5B,OAAL,CAAaV,IAAb,CAAP;AALuB;AAM1B;AACKuC,iBAAN,GAAsB;AAAA;;AAAA;AAClB,kBAAM3B,KAAK,OAAKC,IAAL,CAAU,IAAV,CAAX;AACA,kBAAM,OAAKd,KAAL,CAAW,IAAX,EAAiBE,KAAjB,CAAuB;AACzBW,oBAAIA;AADqB,aAAvB,EAEH4B,KAFG,CAEG,CAFH,EAEMzB,MAFN,CAEa;AACfb,2BAAW;AADI,aAFb,CAAN;AAKA,mBAAO,OAAKQ,OAAL,EAAP;AAPkB;AAQrB;AACK+B,oBAAN,GAAyB;AAAA;;AAAA;AACrB,kBAAM7B,KAAK,OAAKf,GAAL,CAAS,IAAT,CAAX;AACA,kBAAM6C,SAAS,OAAK7C,GAAL,CAAS,QAAT,CAAf;AACA,gBAAI8C,OAAO,CAAX;AACA,gBAAID,UAAU,MAAd,EAAsB;AAClBC,uBAAO,CAAP;AACH;AACD,kBAAM5C,QAAQ,OAAKA,KAAL,CAAW,IAAX,CAAd;AACA,kBAAMA,MAAME,KAAN,CAAY;AACdW,oBAAIA;AADU,aAAZ,EAEHG,MAFG,CAEI;AACNN,yBAASkC;AADH,aAFJ,CAAN;AARqB;AAaxB;AAxG+B,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\ad.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\nmodule.exports = class extends Base {\n    /**\n     * index action\n     * @return {Promise} []\n     */\n    async indexAction() {\n        const page = this.get('page') || 1;\n        const size = this.get('size') || 10;\n        const model = this.model('ad');\n        const data = await model.where({\n            is_delete: 0\n        }).order(['id ASC']).page(page, size).countSelect();\n        for (const item of data.data) {\n            if (item.end_time != 0) {\n                item.end_time = moment.unix(item.end_time).format('YYYY-MM-DD HH:mm:ss');\n            }\n            if (item.enabled == 1) {\n                item.enabled = true;\n            } else {\n                item.enabled = false;\n            }\n        }\n        return this.success(data);\n    }\n    async updateSortAction() {\n        const id = this.post('id');\n        const sort = this.post('sort');\n        const model = this.model('ad');\n        const data = await model.where({\n            id: id\n        }).update({\n            sort_order: sort\n        });\n        return this.success(data);\n    }\n    async infoAction() {\n        const id = this.get('id');\n        const model = this.model('ad');\n        const data = await model.where({\n            id: id\n        }).find();\n        return this.success(data);\n    }\n    async storeAction() {\n        if (!this.isPost) {\n            return false;\n        }\n        const values = this.post();\n        console.log(values);\n        values.end_time = parseInt(new Date(values.end_time).getTime() / 1000);\n        const id = this.post('id');\n        const model = this.model('ad');\n        if (id > 0) {\n            await model.where({\n                id: id\n            }).update(values);\n        } else {\n            let ex = await model.where({\n                goods_id: values.goods_id,\n                is_delete:0\n            }).find();\n            if (think.isEmpty(ex)) {\n                delete values.id;\n                if (values.link_type == 0) {\n                    values.link = '';\n                } else {\n                    values.goods_id = 0;\n                }\n                await model.add(values);\n            } else {\n                return this.fail(100, '发生错误');\n            }\n        }\n        return this.success(values);\n    }\n    async getallrelateAction() {\n        let data = await this.model('goods').where({\n            is_on_sale: 1,\n            is_delete: 0\n        }).field('id,name,list_pic_url').select();\n        return this.success(data);\n    }\n    async destoryAction() {\n        const id = this.post('id');\n        await this.model('ad').where({\n            id: id\n        }).limit(1).update({\n            is_delete: 1\n        });\n        return this.success();\n    }\n    async saleStatusAction() {\n        const id = this.get('id');\n        const status = this.get('status');\n        let sale = 0;\n        if (status == 'true') {\n            sale = 1;\n        }\n        const model = this.model('ad');\n        await model.where({\n            id: id\n        }).update({\n            enabled: sale\n        });\n    }\n};"]}