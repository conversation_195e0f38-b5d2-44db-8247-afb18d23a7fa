{"version": 3, "sources": ["..\\..\\..\\src\\common\\config\\adapter.js"], "names": ["fileCache", "require", "<PERSON><PERSON><PERSON>", "File", "DateFile", "path", "database", "nunjucks", "isDev", "think", "env", "exports", "cache", "type", "common", "timeout", "file", "handle", "cachePath", "join", "ROOT_PATH", "<PERSON><PERSON><PERSON><PERSON>", "gcInterval", "model", "logConnect", "logSql", "logger", "msg", "info", "mysql", "console", "backups", "absolute", "maxLogSize", "filename", "dateFile", "level", "pattern", "alwaysIncludePattern", "view", "viewPath", "sep", "extname", "beforeRender", "options"], "mappings": "AAAA,MAAMA,YAAYC,QAAQ,kBAAR,CAAlB;AACA,MAAM;AACFC,WADE;AAEFC,QAFE;AAGFC;AAHE,IAIFH,QAAQ,eAAR,CAJJ;AAKA,MAAMI,OAAOJ,QAAQ,MAAR,CAAb;AACA,MAAMK,WAAWL,QAAQ,eAAR,CAAjB;AACA,MAAMM,WAAWN,QAAQ,qBAAR,CAAjB;AACA,MAAMO,QAAQC,MAAMC,GAAN,KAAc,aAA5B;AACA;;;;AAIAC,QAAQC,KAAR,GAAgB;AACZC,UAAM,MADM;AAEZC,YAAQ;AACJC,iBAAS,KAAK,EAAL,GAAU,EAAV,GAAe,IADpB,CACyB;AADzB,KAFI;AAKZC,UAAM;AACFC,gBAAQjB,SADN;AAEFkB,mBAAWb,KAAKc,IAAL,CAAUV,MAAMW,SAAhB,EAA2B,eAA3B,CAFT,EAEsD;AACxDC,mBAAW,CAHT;AAIFC,oBAAY,KAAK,EAAL,GAAU,EAAV,GAAe,IAJzB,CAI8B;AAJ9B;AALM,CAAhB;AAYA;;;;AAIAX,QAAQY,KAAR,GAAgB;AACZV,UAAM,OADM;AAEZC,YAAQ;AACJU,oBAAYhB,KADR;AAEJiB,gBAAQjB,KAFJ;AAGJkB,gBAAQC,OAAOlB,MAAMiB,MAAN,CAAaE,IAAb,CAAkBD,GAAlB;AAHX,KAFI;AAOZE,WAAOvB;AAPK,CAAhB;AASA;;;;AAIAK,QAAQe,MAAR,GAAiB;AACbb,UAAML,QAAQ,SAAR,GAAoB,UADb;AAEbsB,aAAS;AACLb,gBAAQf;AADH,KAFI;AAKbc,UAAM;AACFC,gBAAQd,IADN;AAEF4B,iBAAS,EAFP,EAEW;AACbC,kBAAU,IAHR;AAIFC,oBAAY,KAAK,IAJf,EAIqB;AACvBC,kBAAU7B,KAAKc,IAAL,CAAUV,MAAMW,SAAhB,EAA2B,cAA3B;AALR,KALO;AAYbe,cAAU;AACNlB,gBAAQb,QADF;AAENgC,eAAO,KAFD;AAGNJ,kBAAU,IAHJ;AAINK,iBAAS,aAJH;AAKNC,8BAAsB,IALhB;AAMNJ,kBAAU7B,KAAKc,IAAL,CAAUV,MAAMW,SAAhB,EAA2B,cAA3B;AANJ;AAZG,CAAjB;AAqBAT,QAAQ4B,IAAR,GAAe;AACX1B,UAAM,UADK,EACO;AAClBC,YAAQ;AACJ0B,kBAAUnC,KAAKc,IAAL,CAAUV,MAAMW,SAAhB,EAA2B,MAA3B,CADN,EAC0C;AAC9CqB,aAAK,GAFD,EAEM;AACVC,iBAAS,OAHL,CAGa;AAHb,KAFG;AAOXnC,cAAU;AACNU,gBAAQV,QADF;AAENoC,sBAAc,MAAM,CAAE,CAFhB,EAEkB;AACxBC,iBAAS,CAAE;AAAF;AAHH;AAPC,CAAf", "file": "..\\..\\..\\src\\common\\config\\adapter.js", "sourcesContent": ["const fileCache = require('think-cache-file');\nconst {\n    Console,\n    File,\n    DateFile\n} = require('think-logger3');\nconst path = require('path');\nconst database = require('./database.js');\nconst nunjucks = require('think-view-nunjucks');\nconst isDev = think.env === 'development';\n/**\n * cache adapter config\n * @type {Object}\n */\nexports.cache = {\n    type: 'file',\n    common: {\n        timeout: 24 * 60 * 60 * 1000 // millisecond\n    },\n    file: {\n        handle: fileCache,\n        cachePath: path.join(think.ROOT_PATH, 'runtime/cache'), // absoulte path is necessarily required\n        pathDepth: 1,\n        gcInterval: 24 * 60 * 60 * 1000 // gc interval\n    }\n};\n/**\n * model adapter config\n * @type {Object}\n */\nexports.model = {\n    type: 'mysql',\n    common: {\n        logConnect: isDev,\n        logSql: isDev,\n        logger: msg => think.logger.info(msg)\n    },\n    mysql: database\n};\n/**\n * logger adapter config\n * @type {Object}\n */\nexports.logger = {\n    type: isDev ? 'console' : 'dateFile',\n    console: {\n        handle: Console\n    },\n    file: {\n        handle: File,\n        backups: 10, // max chunk number\n        absolute: true,\n        maxLogSize: 50 * 1024, // 50M\n        filename: path.join(think.ROOT_PATH, 'logs/app.log')\n    },\n    dateFile: {\n        handle: DateFile,\n        level: 'ALL',\n        absolute: true,\n        pattern: '-yyyy-MM-dd',\n        alwaysIncludePattern: true,\n        filename: path.join(think.ROOT_PATH, 'logs/app.log')\n    }\n};\nexports.view = {\n    type: 'nunjucks', // 这里指定默认的模板引擎是 nunjucks\n    common: {\n        viewPath: path.join(think.ROOT_PATH, 'view'), //模板文件的根目录\n        sep: '_', //Controller 与 Action 之间的连接符\n        extname: '.html' //模板文件扩展名\n    },\n    nunjucks: {\n        handle: nunjucks,\n        beforeRender: () => {}, // 模板渲染预处理\n        options: { // 模板引擎额外的配置参数\n        }\n    }\n}"]}