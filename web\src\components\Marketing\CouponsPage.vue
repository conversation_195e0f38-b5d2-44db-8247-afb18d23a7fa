<template>
  <div class="coupons-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">优惠券管理</h1>
      <p class="page-description">管理新人券和满减券，控制小程序首页显示</p>
      <div class="action-buttons">
        <button type="button" @click="openCreateModal" class="btn btn-primary">
          <i class="icon-add"></i>
          创建优惠券
        </button>
        <button type="button" @click="loadCoupons" class="btn btn-outline">
          <i class="icon-refresh"></i>
          刷新数据
        </button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ statistics.total }}</div>
          <div class="stat-label">优惠券总数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ statistics.active }}</div>
          <div class="stat-label">启用中</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ statistics.received }}</div>
          <div class="stat-label">已领取</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ statistics.used }}</div>
          <div class="stat-label">已使用</div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载优惠券数据...</p>
    </div>

    <!-- 空状态 -->
    <div v-else-if="coupons.length === 0" class="empty-container">
      <div class="empty-icon">🎫</div>
      <h3>暂无优惠券</h3>
      <p>创建您的第一个优惠券，开始营销活动</p>
      <button type="button" @click="openCreateModal" class="btn btn-primary">
        创建优惠券
      </button>
    </div>

    <!-- 优惠券列表 -->
    <div v-else class="coupons-container">
      <div class="coupons-grid">
        <div v-for="coupon in coupons" :key="coupon.id" class="coupon-card">
          <!-- 优惠券头部 -->
          <div class="coupon-header">
            <div class="coupon-type" :class="'type-' + coupon.type">
              {{ getTypeText(coupon.type) }}
            </div>
            <div class="coupon-status">
              <label class="switch">
                <input
                  type="checkbox"
                  :checked="coupon.status === 'active'"
                  @change="toggleStatus(coupon)"
                >
                <span class="slider"></span>
              </label>
              <span class="status-text">{{ coupon.status === 'active' ? '启用' : '禁用' }}</span>
            </div>
          </div>

          <!-- 优惠券主体 -->
          <div class="coupon-body">
            <h3 class="coupon-name">{{ coupon.name }}</h3>
            <div class="coupon-code">代码: {{ coupon.code }}</div>

            <div class="coupon-details">
              <div class="detail-item">
                <span class="detail-label">优惠内容:</span>
                <span class="detail-value discount" :class="'discount-' + coupon.discount_type">
                  <span v-if="coupon.discount_type === 'fixed'">减 ¥{{ coupon.discount_value }}</span>
                  <span v-else>{{ coupon.discount_value }}折</span>
                </span>
              </div>

              <div class="detail-item">
                <span class="detail-label">使用条件:</span>
                <span class="detail-value">
                  {{ coupon.min_amount > 0 ? `满 ¥${coupon.min_amount}` : '无门槛' }}
                </span>
              </div>

              <div class="detail-item">
                <span class="detail-label">发放数量:</span>
                <span class="detail-value">
                  {{ coupon.total_quantity === -1 ? '无限制' : coupon.total_quantity }}
                </span>
              </div>

              <div class="detail-item">
                <span class="detail-label">有效期:</span>
                <span class="detail-value">{{ formatDate(coupon.end_time) }}</span>
              </div>
            </div>

            <div v-if="coupon.description" class="coupon-description">
              {{ coupon.description }}
            </div>
          </div>

          <!-- 优惠券操作 -->
          <div class="coupon-actions">
            <button type="button" @click="viewStats(coupon)" class="btn btn-stats">统计</button>
            <button type="button" @click="deleteCoupon(coupon)" class="btn btn-delete">删除</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建优惠券弹窗 -->
    <div v-if="showCreateModal" class="modal-overlay" @click="closeCreateModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>创建优惠券</h3>
          <button type="button" @click="closeCreateModal" class="close-btn">×</button>
        </div>

        <div class="modal-body">
          <div class="form-section">
            <h4>基础信息</h4>
            <div class="form-row">
              <div class="form-group">
                <label>优惠券名称 *</label>
                <input v-model="form.name" type="text" placeholder="请输入优惠券名称" />
              </div>
              <div class="form-group">
                <label>优惠券代码</label>
                <div class="input-group">
                  <input v-model="form.code" type="text" placeholder="留空自动生成" />
                  <button type="button" @click="generateCode" class="btn-generate">生成</button>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label>使用说明</label>
              <textarea v-model="form.description" placeholder="请输入使用说明"></textarea>
            </div>
          </div>

          <div class="form-section">
            <h4>优惠设置</h4>
            <div class="form-row">
              <div class="form-group">
                <label>券类型 *</label>
                <div class="radio-group">
                  <label class="radio-item">
                    <input type="radio" v-model="form.type" value="newuser" />
                    <span>新人券</span>
                  </label>
                  <label class="radio-item">
                    <input type="radio" v-model="form.type" value="full_reduction" />
                    <span>满减券</span>
                  </label>
                </div>
              </div>

              <div class="form-group">
                <label>优惠类型 *</label>
                <div class="radio-group">
                  <label class="radio-item">
                    <input type="radio" v-model="form.discount_type" value="fixed" />
                    <span>固定金额</span>
                  </label>
                  <label class="radio-item">
                    <input type="radio" v-model="form.discount_type" value="percent" />
                    <span>百分比</span>
                  </label>
                </div>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>优惠值 *</label>
                <div class="input-with-unit">
                  <input v-model="form.discount_value" type="number" step="0.01" />
                  <span class="unit">{{ form.discount_type === 'fixed' ? '元' : '%' }}</span>
                </div>
              </div>

              <div class="form-group">
                <label>最低消费金额</label>
                <div class="input-with-unit">
                  <input v-model="form.min_amount" type="number" step="0.01" placeholder="0表示无门槛" />
                  <span class="unit">元</span>
                </div>
              </div>
            </div>

            <div class="form-group" v-if="form.discount_type === 'percent'">
              <label>最大优惠金额</label>
              <div class="input-with-unit">
                <input v-model="form.max_discount" type="number" step="0.01" placeholder="百分比券的最大优惠金额" />
                <span class="unit">元</span>
              </div>
            </div>
          </div>

          <div class="form-section">
            <h4>发放设置</h4>
            <div class="form-row">
              <div class="form-group">
                <label>发放总量</label>
                <div class="input-with-unit">
                  <input v-model="form.total_quantity" type="number" placeholder="-1表示无限制" />
                  <span class="unit">张</span>
                </div>
              </div>

              <div class="form-group">
                <label>每人限领</label>
                <div class="input-with-unit">
                  <input v-model="form.per_user_limit" type="number" min="1" />
                  <span class="unit">张</span>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label class="checkbox-item">
                <input type="checkbox" v-model="form.auto_distribute" />
                <span>新用户自动获得</span>
              </label>
            </div>
          </div>

          <div class="form-section">
            <h4>时间设置</h4>
            <div class="form-row">
              <div class="form-group">
                <label>有效期开始 *</label>
                <input v-model="form.start_time" type="datetime-local" />
              </div>

              <div class="form-group">
                <label>有效期结束 *</label>
                <input v-model="form.end_time" type="datetime-local" />
              </div>
            </div>

            <div class="form-group">
              <label>领取后有效天数</label>
              <div class="input-with-unit">
                <input v-model="form.valid_days" type="number" placeholder="留空使用固定时间" />
                <span class="unit">天</span>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" @click="closeCreateModal" class="btn btn-secondary">取消</button>
          <button type="button" @click="createCoupon" class="btn btn-primary" :disabled="creating">
            {{ creating ? '创建中...' : '创建优惠券' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CouponsPage',
  data() {
    return {
      coupons: [],
      loading: false,
      creating: false,
      showCreateModal: false,
      statistics: {
        total: 0,
        active: 0,
        received: 0,
        used: 0
      },
      form: {
        name: '',
        code: '',
        type: 'newuser',
        discount_type: 'fixed',
        discount_value: '',
        min_amount: 0,
        max_discount: '',
        total_quantity: -1,
        per_user_limit: 1,
        start_time: '',
        end_time: '',
        valid_days: '',
        description: '',
        auto_distribute: false
      }
    }
  },

  watch: {
    showCreateModal: {
      handler(newVal, oldVal) {
        console.log('showCreateModal 变化:', oldVal, '->', newVal);
        if (!newVal && oldVal) {
          console.log('模态窗被关闭');
        }
        if (newVal && !oldVal) {
          console.log('模态窗被打开');
          // 使用nextTick确保DOM更新完成
          this.$nextTick(() => {
            console.log('DOM更新完成，模态窗应该可见');
          });
        }
      },
      immediate: true
    }
  },

  mounted() {
    console.log('优惠券页面已加载');
    this.loadCoupons();
    this.loadStatistics();
  },

  errorCaptured(err, vm, info) {
    console.error('Vue组件错误:', err);
    console.error('错误信息:', info);
    console.error('组件实例:', vm);
    this.$message.error('页面出现错误，请刷新重试');
    return false;
  },

  methods: {
    async loadCoupons() {
      this.loading = true;
      try {
        console.log('开始加载优惠券数据...');
        const response = await this.axios.get('/coupon/index');
        console.log('API响应:', response);

        if (response.data && response.data.errno === 0) {
          this.coupons = response.data.data.data || [];
          console.log('加载到优惠券数据:', this.coupons.length, '条');
          // 重新加载统计数据以确保数据同步
          await this.loadStatistics();
        } else {
          console.error('API返回错误:', response.data);
          this.coupons = [];
        }
      } catch (error) {
        console.error('加载优惠券列表失败:', error);
        this.coupons = [];
      } finally {
        this.loading = false;
      }
    },

    async loadStatistics() {
      try {
        console.log('开始加载统计数据...');
        const response = await this.axios.get('/coupon/statistics');
        console.log('统计API响应:', response);

        if (response.data && response.data.errno === 0) {
          this.statistics = response.data.data;
          console.log('统计数据加载成功:', this.statistics);
        } else {
          console.error('统计API返回错误:', response.data);
          // 如果API失败，使用本地计算的基础数据
          this.updateStatisticsFromLocal();
        }
      } catch (error) {
        console.error('加载统计数据失败:', error);
        // 如果API失败，使用本地计算的基础数据
        this.updateStatisticsFromLocal();
      }
    },

    updateStatisticsFromLocal() {
      // 仅在API失败时使用，提供基础的统计数据
      this.statistics.total = this.coupons.length;
      this.statistics.active = this.coupons.filter(c => c.status === 'active').length;
      // received 和 used 需要从数据库获取，这里设为0
      this.statistics.received = this.statistics.received || 0;
      this.statistics.used = this.statistics.used || 0;
      console.log('使用本地计算的统计数据:', this.statistics);
    },

    getTypeText(type) {
      const types = {
        'newuser': '新人券',
        'full_reduction': '满减券'
      };
      return types[type] || type;
    },

    formatDate(date) {
      if (!date) return '';
      return new Date(date).toLocaleDateString('zh-CN');
    },

    async toggleStatus(coupon) {
      try {
        const newStatus = coupon.status === 'active' ? 'disabled' : 'active';
        const response = await this.axios.post('/coupon/toggleStatus', {
          id: coupon.id,
          status: newStatus
        });

        if (response.data && response.data.errno === 0) {
          coupon.status = newStatus;
          // 重新加载统计数据以确保数据准确
          await this.loadStatistics();
          this.$message.success(`${newStatus === 'active' ? '启用' : '禁用'}成功`);
        } else {
          this.$message.error(response.data.errmsg || '操作失败');
        }
      } catch (error) {
        console.error('切换状态失败:', error);
        this.$message.error('操作失败');
      }
    },

    async createCoupon(event) {
      // 阻止默认行为，防止表单提交导致页面刷新
      if (event) {
        event.preventDefault();
        event.stopPropagation();
      }

      // 验证表单数据
      const validationResult = this.validateForm();
      if (!validationResult.valid) {
        this.$message.error(validationResult.message);
        return;
      }

      if (this.creating) {
        return; // 防止重复提交
      }

      this.creating = true;
      try {
        // 准备提交数据，确保数据类型正确
        const submitData = this.prepareSubmitData();
        console.log('提交表单数据:', submitData);

        const response = await this.axios.post('/coupon/add', submitData);
        console.log('创建响应:', response);

        if (response.data && response.data.errno === 0) {
          this.$message.success('创建成功');
          this.showCreateModal = false;
          this.resetForm();
          await this.loadCoupons();
        } else {
          this.$message.error(response.data.errmsg || '创建失败');
        }
      } catch (error) {
        console.error('创建优惠券失败:', error);
        if (error.response && error.response.data && error.response.data.errmsg) {
          this.$message.error(error.response.data.errmsg);
        } else {
          this.$message.error('创建失败，请检查网络连接');
        }
      } finally {
        this.creating = false;
      }
    },

    validateForm() {
      // 验证必填字段
      if (!this.form.name.trim()) {
        return { valid: false, message: '请输入优惠券名称' };
      }

      if (!this.form.type) {
        return { valid: false, message: '请选择优惠券类型' };
      }

      if (!this.form.discount_type) {
        return { valid: false, message: '请选择优惠类型' };
      }

      if (!this.form.discount_value || this.form.discount_value <= 0) {
        return { valid: false, message: '请输入有效的优惠值' };
      }

      if (!this.form.start_time) {
        return { valid: false, message: '请设置有效期开始时间' };
      }

      if (!this.form.end_time) {
        return { valid: false, message: '请设置有效期结束时间' };
      }

      // 验证时间逻辑
      if (new Date(this.form.start_time) >= new Date(this.form.end_time)) {
        return { valid: false, message: '开始时间必须早于结束时间' };
      }

      // 验证百分比优惠值
      if (this.form.discount_type === 'percent') {
        if (this.form.discount_value > 100) {
          return { valid: false, message: '百分比优惠值不能超过100%' };
        }
      }

      return { valid: true };
    },

    prepareSubmitData() {
      const data = { ...this.form };

      // 确保数值类型正确
      data.discount_value = parseFloat(data.discount_value) || 0;
      data.min_amount = parseFloat(data.min_amount) || 0;
      data.total_quantity = parseInt(data.total_quantity) || -1;
      data.per_user_limit = parseInt(data.per_user_limit) || 1;

      // 处理可选字段
      if (data.max_discount) {
        data.max_discount = parseFloat(data.max_discount) || null;
      }

      if (data.valid_days) {
        data.valid_days = parseInt(data.valid_days) || null;
      }

      // 确保布尔值正确
      data.auto_distribute = Boolean(data.auto_distribute);

      // 清理空字符串
      Object.keys(data).forEach(key => {
        if (data[key] === '') {
          data[key] = null;
        }
      });

      return data;
    },



    viewStats(coupon) {
      console.log('查看统计:', coupon);
      this.$message.info('统计功能开发中...');
    },

    openCreateModal() {
      try {
        console.log('打开创建优惠券模态窗');
        this.resetForm();
        this.initializeFormDefaults();
        this.showCreateModal = true;
        console.log('模态窗状态:', this.showCreateModal);
        console.log('表单初始数据:', this.form);
      } catch (error) {
        console.error('打开模态窗失败:', error);
        this.$message.error('打开创建窗口失败');
      }
    },

    closeCreateModal() {
      console.log('关闭创建优惠券模态窗');
      this.showCreateModal = false;
      this.resetForm();
    },

    initializeFormDefaults() {
      // 设置默认的开始时间为当前时间
      const now = new Date();
      const startTime = new Date(now.getTime());

      // 设置默认的结束时间为30天后
      const endTime = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

      // 格式化为datetime-local需要的格式
      this.form.start_time = this.formatDateTimeLocal(startTime);
      this.form.end_time = this.formatDateTimeLocal(endTime);
    },

    formatDateTimeLocal(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day}T${hours}:${minutes}`;
    },

    generateCode() {
      const timestamp = Date.now().toString(36);
      const random = Math.random().toString(36).substr(2, 5);
      this.form.code = `CPN${timestamp}${random}`.toUpperCase();
    },

    resetForm() {
      this.creating = false;
      this.form = {
        name: '',
        code: '',
        type: 'newuser',
        discount_type: 'fixed',
        discount_value: '',
        min_amount: 0,
        max_discount: '',
        total_quantity: -1,
        per_user_limit: 1,
        start_time: '',
        end_time: '',
        valid_days: '',
        description: '',
        auto_distribute: false
      };
    },

    async deleteCoupon(coupon) {
      try {
        await this.$confirm('确定要删除这个优惠券吗？', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        const response = await this.axios.post('/coupon/delete', {
          id: coupon.id
        });

        if (response.data && response.data.errno === 0) {
          this.$message.success('删除成功');
          this.loadCoupons();
        } else {
          this.$message.error(response.data.errmsg || '删除失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除优惠券失败:', error);
          this.$message.error('删除失败');
        }
      }
    }
  }
}
</script>

<style scoped>
.coupons-page {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.page-description {
  color: #718096;
  margin: 0 0 20px 0;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-outline {
  background: white;
  color: #4a5568;
  border: 1px solid #e2e8f0;
}

.btn-outline:hover {
  background: #f7fafc;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

/* 加载和空状态 */
.loading-container, .empty-container {
  background: white;
  padding: 60px 20px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

/* 优惠券网格 */
.coupons-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.coupons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

/* 优惠券卡片 */
.coupon-card {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s;
}

.coupon-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.coupon-header {
  padding: 16px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-type {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.type-newuser {
  background: #ebf8ff;
  color: #3182ce;
}

.type-full_reduction {
  background: #f0fff4;
  color: #38a169;
}

.coupon-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #cbd5e0;
  transition: .4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #48bb78;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

.status-text {
  font-size: 12px;
  color: #718096;
}

.coupon-body {
  padding: 20px;
}

.coupon-name {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.coupon-code {
  font-size: 12px;
  color: #718096;
  font-family: monospace;
  margin-bottom: 16px;
}

.coupon-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  color: #718096;
}

.detail-value {
  font-size: 14px;
  color: #1a202c;
  font-weight: 500;
}

.detail-value.discount {
  font-size: 16px;
  font-weight: 600;
}

.discount-fixed {
  color: #e53e3e;
}

.discount-percent {
  color: #805ad5;
}

.coupon-description {
  font-size: 13px;
  color: #718096;
  line-height: 1.5;
  background: #f7fafc;
  padding: 12px;
  border-radius: 6px;
}

.coupon-actions {
  padding: 16px 20px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  display: flex;
  gap: 8px;
}

.btn-stats {
  background: #805ad5;
  color: white;
  padding: 6px 12px;
  font-size: 12px;
}

.btn-delete {
  background: #e53e3e;
  color: white;
  padding: 6px 12px;
  font-size: 12px;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8fafc;
  border-radius: 12px 12px 0 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #718096;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #e2e8f0;
  color: #4a5568;
}

.modal-body {
  padding: 24px;
}

.form-section {
  margin-bottom: 32px;
}

.form-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #4a5568;
  font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.input-group {
  display: flex;
  gap: 8px;
}

.input-group input {
  flex: 1;
}

.btn-generate {
  padding: 10px 16px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  white-space: nowrap;
}

.btn-generate:hover {
  background: #5a67d8;
}

.input-with-unit {
  display: flex;
  align-items: center;
}

.input-with-unit input {
  flex: 1;
  border-radius: 6px 0 0 6px;
}

.unit {
  padding: 10px 12px;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-left: none;
  border-radius: 0 6px 6px 0;
  font-size: 14px;
  color: #718096;
}

.radio-group {
  display: flex;
  gap: 20px;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 14px;
}

.radio-item input[type="radio"] {
  width: auto;
  margin: 0;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
}

.checkbox-item input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: #f8fafc;
  border-radius: 0 0 12px 12px;
}

.btn-secondary {
  background: white;
  color: #4a5568;
  border: 1px solid #e2e8f0;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.btn-secondary:hover {
  background: #f7fafc;
}

/* 响应式 */
@media (max-width: 768px) {
  .coupons-grid {
    grid-template-columns: 1fr;
  }

  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .coupon-details {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .radio-group {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
