<template>
  <div class="content-page bg-gray-50 min-h-screen">
    <!-- 面包屑导航 -->
    <div class="flex items-center text-sm text-gray-500 mb-4">
      <a href="#" class="hover:text-primary">首页</a>
      <span class="mx-2">/</span>
      <span class="text-gray-700">订单管理</span>
    </div>

    <!-- 统计卡片 -->
    <div style="display: flex; gap: 1.5rem; margin-bottom: 1.5rem; flex-wrap: nowrap; overflow-x: auto;">
      <div class="stats-card" style="flex: 1; min-width: 200px; background-color: white; border-radius: 0.5rem; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); padding: 1.25rem;">
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <div>
            <p style="color: #6b7280; font-size: 0.875rem;">今日订单</p>
            <h3 style="font-size: 1.5rem; font-weight: 700; margin-top: 0.25rem;">{{ todayOrderCount || 0 }}</h3>
            <p style="color: #10b981; font-size: 0.875rem; margin-top: 0.25rem;">
              +12.5% <span style="color: #6b7280;">较昨日</span>
            </p>
          </div>
          <div style="width: 3rem; height: 3rem; display: flex; align-items: center; justify-content: center; background-color: #dbeafe; color: #4f46e5; border-radius: 9999px;">
            <i class="ri-file-list-line ri-xl"></i>
          </div>
        </div>
      </div>
      <div style="flex: 1; min-width: 200px; background-color: white; border-radius: 0.5rem; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); padding: 1.25rem;">
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <div>
            <p style="color: #6b7280; font-size: 0.875rem;">待处理订单</p>
            <h3 style="font-size: 1.5rem; font-weight: 700; margin-top: 0.25rem;">{{ pendingOrderCount || 0 }}</h3>
            <p style="color: #ef4444; font-size: 0.875rem; margin-top: 0.25rem;">
              +8.3% <span style="color: #6b7280;">较昨日</span>
            </p>
          </div>
          <div style="width: 3rem; height: 3rem; display: flex; align-items: center; justify-content: center; background-color: #fef3c7; color: #f59e0b; border-radius: 9999px;">
            <i class="ri-time-line ri-xl"></i>
          </div>
        </div>
      </div>
      <div style="flex: 1; min-width: 200px; background-color: white; border-radius: 0.5rem; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); padding: 1.25rem;">
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <div>
            <p style="color: #6b7280; font-size: 0.875rem;">今日销售额</p>
            <h3 style="font-size: 1.5rem; font-weight: 700; margin-top: 0.25rem;">¥{{ todaySales || 0 }}</h3>
            <p style="color: #10b981; font-size: 0.875rem; margin-top: 0.25rem;">
              +16.2% <span style="color: #6b7280;">较昨日</span>
            </p>
          </div>
          <div style="width: 3rem; height: 3rem; display: flex; align-items: center; justify-content: center; background-color: #d1fae5; color: #10b981; border-radius: 9999px;">
            <i class="ri-money-cny-circle-line ri-xl"></i>
          </div>
        </div>
      </div>
      <div style="flex: 1; min-width: 200px; background-color: white; border-radius: 0.5rem; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); padding: 1.25rem;">
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <div>
            <p style="color: #6b7280; font-size: 0.875rem;">本月销售额</p>
            <h3 style="font-size: 1.5rem; font-weight: 700; margin-top: 0.25rem;">¥{{ monthSales || 0 }}</h3>
            <p style="color: #10b981; font-size: 0.875rem; margin-top: 0.25rem;">
              +8.7% <span style="color: #6b7280;">较上月</span>
            </p>
          </div>
          <div style="width: 3rem; height: 3rem; display: flex; align-items: center; justify-content: center; background-color: #e9d5ff; color: #8b5cf6; border-radius: 9999px;">
            <i class="ri-line-chart-line ri-xl"></i>
          </div>
        </div>
      </div>
      <div style="flex: 1; min-width: 200px; background-color: white; border-radius: 0.5rem; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); padding: 1.25rem;">
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <div>
            <p style="color: #6b7280; font-size: 0.875rem;">待处理售后</p>
            <h3 style="font-size: 1.5rem; font-weight: 700; margin-top: 0.25rem;">{{ pendingRefundCount || 0 }}</h3>
            <p style="color: #ef4444; font-size: 0.875rem; margin-top: 0.25rem;">
              需要处理 <span style="color: #6b7280;">的申请</span>
            </p>
          </div>
          <div style="width: 3rem; height: 3rem; display: flex; align-items: center; justify-content: center; background-color: #fee2e2; color: #dc2626; border-radius: 9999px;">
            <i class="ri-customer-service-line ri-xl"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单管理标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold">订单管理</h1>
    </div>

    <!-- 订单状态标签 -->
    <div class="bg-white rounded shadow mb-6">
      <div class="flex border-b overflow-x-auto">
        <button
          @click="handleTabClick('sixth')"
          :class="['px-6 py-4 font-medium whitespace-nowrap tab-button', activeName === 'sixth' ? 'tab-active' : 'text-gray-500 hover:text-gray-700']"
        >
          全部订单
          <span class="ml-1 bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
            <span v-if="statusCountsLoading">...</span>
            <span v-else>{{ statusCounts.all || 0 }}</span>
          </span>
        </button>
        <button
          @click="handleTabClick('first')"
          :class="['px-6 py-4 font-medium whitespace-nowrap tab-button', activeName === 'first' ? 'tab-active' : 'text-gray-500 hover:text-gray-700']"
        >
          待付款
          <span class="ml-1 bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
            <span v-if="statusCountsLoading">...</span>
            <span v-else>{{ statusCounts.toPay || 0 }}</span>
          </span>
        </button>
        <button
          @click="handleTabClick('second')"
          :class="['px-6 py-4 font-medium whitespace-nowrap tab-button', activeName === 'second' ? 'tab-active' : 'text-gray-500 hover:text-gray-700']"
        >
          待发货
          <span class="ml-1 bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
            <span v-if="statusCountsLoading">...</span>
            <span v-else>{{ statusCounts.toDelivery || 0 }}</span>
          </span>
        </button>
        <button
          @click="handleTabClick('third')"
          :class="['px-6 py-4 font-medium whitespace-nowrap tab-button', activeName === 'third' ? 'tab-active' : 'text-gray-500 hover:text-gray-700']"
        >
          待收货
          <span class="ml-1 bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
            <span v-if="statusCountsLoading">...</span>
            <span v-else>{{ statusCounts.toReceive || 0 }}</span>
          </span>
        </button>
        <button
          @click="handleTabClick('fourth')"
          :class="['px-6 py-4 font-medium whitespace-nowrap tab-button', activeName === 'fourth' ? 'tab-active' : 'text-gray-500 hover:text-gray-700']"
        >
          已收货
          <span class="ml-1 bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
            <span v-if="statusCountsLoading">...</span>
            <span v-else>{{ statusCounts.received || 0 }}</span>
          </span>
        </button>
        <button
          @click="handleTabClick('fifth')"
          :class="['px-6 py-4 font-medium whitespace-nowrap tab-button', activeName === 'fifth' ? 'tab-active' : 'text-gray-500 hover:text-gray-700']"
        >
          已关闭
          <span class="ml-1 bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">
            <span v-if="statusCountsLoading">...</span>
            <span v-else>{{ statusCounts.closed || 0 }}</span>
          </span>
        </button>
        <button
          @click="handleTabClick('refund')"
          :class="['px-6 py-4 font-medium whitespace-nowrap tab-button', activeName === 'refund' ? 'tab-active' : 'text-gray-500 hover:text-gray-700']"
        >
          售后管理
          <span class="ml-1 bg-red-100 text-red-700 text-xs px-2 py-1 rounded-full">
            <span v-if="statusCountsLoading">...</span>
            <span v-else>{{ pendingRefundCount || 0 }}</span>
          </span>
        </button>
      </div>

      <!-- 筛选工具栏 -->
      <div class="p-4 flex flex-wrap gap-4 items-center border-b">
        <div class="relative">
          <input
            type="text"
            v-model="filterForm.order_sn"
            placeholder="搜索订单号/商品/用户"
            class="pl-10 pr-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary w-64 text-sm"
          />
          <div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center text-gray-400">
            <i class="ri-search-line"></i>
          </div>
        </div>
        <input
          type="text"
          v-model="filterForm.consignee"
          placeholder="收货人"
          class="px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary w-36 text-sm"
        />
        <input
          type="text"
          v-model="filterForm.logistic_code"
          placeholder="快递单号"
          class="px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary w-36 text-sm"
        />
        <!-- 售后管理专用筛选 -->
        <template v-if="activeName === 'refund'">
          <select
            v-model="filterForm.processStatus"
            class="px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary w-36 text-sm"
          >
            <option value="">全部处理状态</option>
            <option value="pending">待处理</option>
            <option value="processed">已处理</option>
          </select>
          <select
            v-model="filterForm.refundType"
            class="px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary w-36 text-sm"
          >
            <option value="">全部类型</option>
            <option value="refund_only">仅退款</option>
            <option value="return_refund">退货退款</option>
          </select>
          <select
            v-model="filterForm.refundStatus"
            class="px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary w-36 text-sm"
          >
            <option value="">全部状态</option>
            <option value="pending">待处理</option>
            <option value="processing">处理中</option>
            <option value="approved">已同意</option>
            <option value="rejected">已拒绝</option>
            <option value="completed">已完成</option>
          </select>
        </template>
        <button
          @click="onSubmitFilter"
          class="bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-button text-gray-700 flex items-center whitespace-nowrap"
        >
          <i class="ri-filter-line mr-1"></i> 筛选
        </button>
        <button class="bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-button text-gray-700 flex items-center ml-auto whitespace-nowrap">
          <i class="ri-download-line mr-1"></i> 导出
        </button>
      </div>
      <!-- 订单列表 -->
      <transition name="fade-slide" mode="out-in">
        <div class="table-container" :key="activeName">
          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-overlay">
            <div class="loading-spinner">
              <div class="spinner"></div>
              <span class="loading-text">加载中...</span>
            </div>
          </div>

          <!-- 调试信息 -->
          <div v-else-if="tableData.length === 0" style="padding: 1rem; text-align: center; color: #6b7280;">
            <div class="empty-state">
              <i class="ri-inbox-line" style="font-size: 2.5rem; color: #d1d5db; margin-bottom: 0.5rem;"></i>
              <div>暂无订单数据</div>
            </div>
          </div>
          <div v-else>
            <div style="padding: 0.5rem; font-size: 0.75rem; color: #9ca3af;">
              共 {{ tableData.length }} 条订单数据
            </div>

            <table class="w-full" style="width: 100%; border-collapse: collapse;">
          <thead>
            <tr style="background-color: #f9fafb; color: #6b7280; font-size: 0.75rem;">
              <th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 5%;">
                <input
                  type="checkbox"
                  style="width: 1rem; height: 1rem;"
                />
              </th>
              <th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 24%;">订单信息</th>
              <th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 12%;">下单账号</th>
              <th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 16%;">收货信息</th>
              <th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 8%;">金额</th>
              <th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 10%;">状态</th>
              <th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 16%;">操作</th>
              <th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 6%;">标识</th>
              <th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 8%;">客服备注</th>
            </tr>
          </thead>
          <tbody style="border-top: 1px solid #e5e7eb;">
            <tr v-for="item in tableData" :key="item.id" style="border-bottom: 1px solid #f3f4f6; cursor: pointer;">
              <!-- 复选框 -->
              <td style="padding: 1rem; vertical-align: middle; text-align: center;">
                <input
                  type="checkbox"
                  style="width: 1rem; height: 1rem;"
                />
              </td>

              <!-- 订单信息（商品+订单号+时间） -->
              <td style="padding: 1rem; vertical-align: middle;">
                <div style="display: flex; align-items: center;">
                  <img
                    v-if="item.goodsList && item.goodsList[0]"
                    :src="item.goodsList[0].list_pic_url"
                    alt="商品图片"
                    style="width: 3rem; height: 3rem; object-fit: cover; border-radius: 0.5rem; margin-right: 0.75rem; flex-shrink: 0;"
                  />
                  <div style="flex: 1; min-width: 0;">
                    <div style="font-weight: 500; font-size: 0.875rem; margin-bottom: 0.25rem; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                      {{ item.goodsList && item.goodsList[0] ? item.goodsList[0].goods_name : '暂无商品' }}
                    </div>
                    <div style="color: #6b7280; font-size: 0.75rem; margin-bottom: 0.125rem;">
                      订单号：{{ item.order_sn }}
                    </div>
                    <div style="color: #6b7280; font-size: 0.75rem; margin-bottom: 0.125rem;">
                      下单：{{ item.add_time }} | 付款：{{ item.pay_time || '未付款' }}
                    </div>
                    <div style="color: #6b7280; font-size: 0.75rem;">
                      数量：{{ item.goodsCount }}件
                      <span v-if="item.offline_pay" style="color: #ef4444; margin-left: 0.5rem;">线下支付</span>
                    </div>
                  </div>
                </div>
              </td>

              <!-- 下单账号 -->
              <td style="padding: 1rem; vertical-align: middle;">
                <div>
                  <div style="font-size: 0.875rem; font-weight: 500; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                    {{ item.userInfo ? item.userInfo.nickname : '未知用户' }}
                  </div>
                  <div style="color: #6b7280; font-size: 0.75rem; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                    {{ item.userInfo ? item.userInfo.name : '' }}
                  </div>
                </div>
              </td>

              <!-- 收货信息 -->
              <td style="padding: 1rem; vertical-align: middle;">
                <div style="font-size: 0.875rem; font-weight: 500; margin-bottom: 0.25rem; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" :title="item.consignee">
                  {{ item.consignee }}
                </div>
                <div style="color: #6b7280; font-size: 0.75rem; margin-bottom: 0.125rem;">{{ item.mobile }}</div>
                <div
                  style="color: #6b7280; font-size: 0.75rem; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; max-width: 180px;"
                  :title="(item.full_region || '') + (item.address || '')"
                >
                  {{ getShortAddress(item.full_region, item.address) }}
                </div>
              </td>

              <!-- 金额 -->
              <td style="padding: 1rem; vertical-align: middle;">
                <div style="font-weight: 500; font-size: 0.875rem;">¥{{ item.actual_price }}</div>
                <div v-if="item.change_price != item.actual_price" style="color: #6b7280; font-size: 0.75rem;">
                  改价前¥{{ item.change_price }}
                </div>
              </td>

              <!-- 状态 -->
              <td style="padding: 1rem; vertical-align: middle;">
                <!-- 如果有售后申请，优先显示售后状态 -->
                <div v-if="item.hasRefund" style="display: flex; flex-direction: column; gap: 0.25rem;">
                  <span :style="getRefundStatusStyle(item.refundInfo.status)" style="font-size: 0.75rem; padding: 0.25rem 0.5rem; border-radius: 0.25rem; white-space: nowrap;">
                    {{ getRefundTypeText(item.refundInfo.refund_type) }}{{ item.refundInfo.status_text }}
                  </span>
                  <div style="font-size: 0.625rem; color: #6b7280; max-width: 120px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" :title="item.refundInfo.refund_reason">
                    {{ item.refundInfo.refund_reason }}
                  </div>
                  <div style="font-size: 0.625rem; color: #ef4444; font-weight: 500;">
                    ¥{{ item.refundInfo.refund_amount }}
                  </div>
                </div>
                <!-- 否则显示订单状态 -->
                <span v-else :style="getStatusStyle(item.order_status)">
                  {{ item.order_status_text }}
                </span>
              </td>

              <!-- 操作 -->
              <td style="padding: 1rem; vertical-align: middle;">
                <div style="display: flex; flex-direction: column; gap: 0.25rem;">
                  <button
                    style="color: #4f46e5; font-size: 0.75rem; white-space: nowrap; background: none; border: none; cursor: pointer; text-align: left; padding: 0;"
                    @click="viewDetail(item.id)"
                  >
                    详情
                  </button>
                  <button
                    v-if="item.print_status == 1 && item.order_status == 300"
                    style="color: #4f46e5; font-size: 0.75rem; white-space: nowrap; background: none; border: none; cursor: pointer; text-align: left; padding: 0;"
                    @click="deliveryConfirm(item.id)"
                  >
                    发货
                  </button>
                  <button
                    v-if="item.order_status == 101"
                    style="color: #4f46e5; font-size: 0.75rem; white-space: nowrap; background: none; border: none; cursor: pointer; text-align: left; padding: 0;"
                    @click="orderEdit(item)"
                  >
                    修改价格
                  </button>
                  <button
                    v-else-if="item.order_status == 300 || item.order_status == 301"
                    style="color: #4f46e5; font-size: 0.75rem; white-space: nowrap; background: none; border: none; cursor: pointer; text-align: left; padding: 0;"
                    @click="orderEdit(item)"
                  >
                    打印快递单
                  </button>
                  <!-- 售后处理按钮 -->
                  <button
                    v-if="item.hasRefund && (item.refundInfo.status === 'pending' || item.refundInfo.status === 'processing')"
                    style="color: #dc2626; font-size: 0.75rem; white-space: nowrap; background: none; border: none; cursor: pointer; text-align: left; padding: 0;"
                    @click="handleRefund(item.refundInfo)"
                  >
                    处理售后
                  </button>
                  <button
                    v-if="item.hasRefund && item.refundInfo.status === 'returned'"
                    style="color: #059669; font-size: 0.75rem; white-space: nowrap; background: none; border: none; cursor: pointer; text-align: left; padding: 0;"
                    @click="confirmReturn(item.refundInfo)"
                  >
                    确认收货
                  </button>
                </div>
              </td>

              <!-- 标识（旗子） -->
              <td style="padding: 1rem; vertical-align: middle;">
                <div style="display: flex; justify-content: center; align-items: center; width: 100%;">
                  <div
                    :style="getFlagStyle(item.service_flag_color)"
                    style="width: 1.5rem; height: 1.5rem; border-radius: 0.25rem; display: flex; align-items: center; justify-content: center; cursor: pointer;"
                    @click="openFlagDialog(item)"
                    :title="getFlagTitle(item.service_flag_color)"
                  >
                    <i class="el-icon-flag" style="font-size: 0.875rem; color: white;"></i>
                  </div>
                </div>
              </td>

              <!-- 客服备注 -->
              <td style="padding: 1rem; vertical-align: middle;">
                <div style="font-size: 0.75rem; color: #374151; line-height: 1.4; max-width: 120px; word-wrap: break-word;">
                  {{ item.admin_memo || '' }}
                </div>
              </td>
            </tr>
          </tbody>
            </table>

            <!-- 分页 -->
            <div class="pagination-container" style="display: flex; justify-content: space-between; align-items: center; margin-top: 1.5rem; padding: 1rem; background-color: white; border-radius: 0.5rem; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);">
              <div style="font-size: 0.875rem; color: #6b7280;">
                显示第 {{ (page - 1) * 10 + 1 }} - {{ Math.min(page * 10, total) }} 条，共 {{ total }} 条记录
              </div>
              <el-pagination
                @current-change="handlePageChange"
                :current-page="page"
                :page-size="10"
                layout="prev, pager, next"
                :total="total"
                class="modern-pagination"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </transition>
    </div>
    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
      <span>确定打包备货</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="提示" :visible.sync="dialogVisible2">
      <span>确定收货？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible2 = false">取 消</el-button>
        <el-button type="primary" @click="receiveConfirm">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="打印快递单" :visible.sync="dialogFormVisible">
      <el-form :model="dform">
        <div class="dialog-wrap">
          <div class="list-wrap">
            <div class="goods-list" v-for="(ditem, index) in orderInfo.goodsList" :key="index">
              <img :src="ditem.list_pic_url" class="goods-img" />
              <div class="goods-name">{{ ditem.goods_name }}</div>
              <div class="goods-name">{{ ditem.goods_aka }}</div>
              <div class="goods-spec">
                {{ ditem.goods_specifition_name_value }}
              </div>
              <div class="goods-number">
                <label>数量：</label>{{ ditem.number }}
              </div>
            </div>
          </div>
          <div class="dialog-main" v-if="dform.method == 1">
            <div class="l">
              <div class="title">寄件人</div>
              <div class="detail">
                <div class="m1">
                  <div class="user-name">
                    <el-input
                      size="mini"
                      class="senderInput"
                      v-model="sender.name"
                      placeholder="寄件人姓名"
                    ></el-input>
                  </div>
                  <div class="user-mobile">
                    <el-input
                      size="mini"
                      class="senderInput"
                      v-model="sender.mobile"
                      placeholder="寄件人手机"
                    ></el-input>
                  </div>
                </div>
                <div class="user-address">
                  <el-cascader
                    style="width: 200px"
                    size="mini"
                    :options="options"
                    placeholder="请选择地区"
                    v-model="senderOptions"
                  >
                  </el-cascader>
                  <el-input
                    size="mini"
                    class="address-input"
                    v-model="sender.address"
                    auto-complete="off"
                    placeholder="请输入详细地"
                  ></el-input>
                </div>
              </div>
            </div>
          </div>
          <div class="dialog-main" v-if="dform.method == 1">
            <div class="l">
              <div class="title">收件人</div>
              <div class="detail">
                <div class="m1">
                  <div class="user-name">
                    <el-input
                      size="mini"
                      class="senderInput"
                      v-model="receiver.name"
                      placeholder="收件人姓名"
                    ></el-input>
                  </div>
                  <div class="user-mobile">
                    <el-input
                      size="mini"
                      class="senderInput"
                      v-model="receiver.mobile"
                      placeholder="收件人手机"
                    ></el-input>
                  </div>
                </div>
                <div class="user-address">
                  <el-cascader
                    style="width: 200px"
                    size="mini"
                    :options="options"
                    placeholder="请选择地区"
                    v-model="receiveOptions"
                  >
                  </el-cascader>
                  <el-input
                    size="mini"
                    class="address-input"
                    v-model="receiver.address"
                    auto-complete="off"
                    placeholder="请输入详细地"
                  ></el-input>
                </div>
              </div>
            </div>
          </div>
          <div v-if="orderInfo.postscript != ''" class="user-post-t">
            买家留言：{{ orderInfo.postscript }}
          </div>
          <div
            v-if="orderInfo.admin_memo != '' && orderInfo.admin_memo != null"
            class="user-admin-t"
          >
            备注：{{ orderInfo.admin_memo }}
          </div>
        </div>
        <el-form-item label="类型" style="margin-top: 10px">
          <el-radio-group
            v-model="dform.method"
            @change="deliveryMethodChange(dform.method)"
          >
            <el-radio :label="1">快递</el-radio>
            <el-radio :label="2">手动输入快递</el-radio>
            <el-radio :label="3">自提件</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="选择快递" v-if="dform.method == 1">
          <el-radio-group v-model="expressType">
            <el-radio :label="1">顺丰(保价)</el-radio>
            <el-radio :label="2">外省顺丰(不保价)</el-radio>
            <el-radio :label="3">江浙沪皖顺丰(特惠)</el-radio>
            <el-radio :label="4">圆通</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="保价金额及提醒" v-if="expressType == 1">
          <el-input-number
            :mini="1"
            :max="9999"
            v-model="orderInfo.express_value"
            @change="changeExpressValue(orderInfo)"
            @blur="changeExpressValue(orderInfo)"
            placeholder="请输入保价金额"
          ></el-input-number>
          <el-input
            v-model="orderInfo.remark"
            @blur="changeRemarkInfo(orderInfo)"
            placeholder="快递单上的提醒"
          ></el-input>
        </el-form-item>
        <el-form-item label="快递单上的提醒" v-if="expressType == 2">
          <el-input
            v-model="orderInfo.remark"
            @blur="changeRemarkInfo(orderInfo)"
            placeholder="快递单上的提醒"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="要在快递单上打印单发货内容"
          v-if="dform.method == 1"
        >
          <el-input
            type="textarea"
            v-model="orderInfo.print_info"
            @blur="changeInfo(orderInfo)"
            placeholder="请输入发货信息"
          ></el-input>
        </el-form-item>
        <el-form-item label="快递单号" v-if="dform.method == 2">
          <el-input v-model="dform.logistic_code"></el-input>
        </el-form-item>
        <el-form-item label="选择快递" v-if="dform.method == 2">
          <el-select
            v-model="nowDeliveryId"
            value-key="id"
            placeholder="请选择快递"
          >
            <el-option
              v-for="item in deliveryCom"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer print-footer">
        <div class="f-left">
          <el-checkbox
            v-if="dform.method == 1 && orderInfo.order_status == 300"
            v-model="autoGoDelivery"
            >打印完成后自动发货
          </el-checkbox>
        </div>
        <div class="f-right" v-if="dform.method != 1">
          <el-button style="margin-right: 20px" @click="hidePrintDialog"
            >取 消</el-button
          >
          <el-button type="primary" @click="deliveryGoGo">发货</el-button>
        </div>
        <div class="f-right" v-if="dform.method == 1">
          <el-button style="margin-right: 20px" @click="hidePrintDialog"
            >取 消</el-button
          >
          <div v-if="rePrintStatus == 0">
            <el-button
              v-if="autoGoDelivery"
              type="primary"
              @click="deliveyGoConfirm"
              >打印快递单并发货</el-button
            >
            <el-button
              v-if="!autoGoDelivery"
              type="primary"
              @click="deliveyGoConfirm"
              >只打印快递单</el-button
            >
          </div>
          <div v-if="rePrintStatus == 1">
            <el-button
              v-if="autoGoDelivery"
              type="primary"
              @click="directPrintConfirm"
              >打印快递单并发货</el-button
            >
            <el-button
              v-if="!autoGoDelivery"
              type="primary"
              @click="directPrintConfirm"
              >只打印快递单</el-button
            >
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog title="提醒！" :visible.sync="dialogExpressVisible">
      <div class="form-table-box">
        该订单已经生成过一个快递单号，是否用该单号打印？
      </div>
      <div slot="footer" class="dialog-footer">
        <div class="f-right">
          <el-button @click="dialogExpressVisible = false">取 消</el-button>
          <el-button type="primary" @click="rePrintExpress"
            >重新生成订单号打印</el-button
          >
          <el-button type="success" @click="directPrintExpress"
            >直接用该单号打印</el-button
          >
        </div>
      </div>
    </el-dialog>
    <el-dialog
      title="快递单预览"
      class="express-dialog"
      :visible.sync="printMiandan"
    >
      <div
        id="sfbj-block"
        v-if="expressType == 1 || expressType == 2"
        style="
          display: block;
          width: 400px;
          height: 500px;
          border: 1px solid #333;
          background: #fff;
          overflow: hidden;
          margin: 0 auto;
        "
      >
        <div
          style="
            display: flex;
            width: 100%;
            justify-content: space-between;
            border-bottom: 1px solid #333;
          "
        >
          <div
            style="
              border-right: 1px solid #333;
              width: 300px;
              text-align: center;
            "
          >
            <barcode
              :value="sfHasValue.LogisticCode"
              height="40"
              display-value="false"
              tag="img"
            ></barcode>
            <div
              style="
                width: 100%;
                margin: 0 auto;
                text-align: center;
                font-size: 12px;
                padding-bottom: 2px;
              "
            >
              快递单号:{{ sfHasValue.LogisticCode }}
            </div>
          </div>
          <div style="font-size: 16px; font-weight: bold">
            {{ sfHasValue.remark }}
          </div>
        </div>
        <div
          style="
            display: flex;
            flex-direction: column;
            width: 100%;
            border-bottom: 1px solid #333;
          "
        >
          <div style="text-align: center; font-size: 32px; font-weight: bold">
            {{ sfHasValue.DestinatioCode }}
          </div>
        </div>
        <div
          style="
            display: flex;
            justify-content: flex-start;
            border-bottom: 1px solid #333;
            position: relative;
          "
        >
          <div
            style="
              width: 12px;
              padding: 0 2px;
              text-align: center;
              font-size: 10px;
              border-right: 1px solid #333;
              margin-right: 10px;
            "
          >
            收件人
          </div>
          <div style="display: flex; flex-direction: column">
            <div
              style="
                display: flex;
                justify-content: flex-start;
                font-size: 12px;
              "
            >
              {{ receiverInfo.Name }} {{ receiverInfo.Tel }}
            </div>
            <div style="font-size: 12px">
              {{
                receiverInfo.ProvinceName +
                receiverInfo.CityName +
                receiverInfo.ExpAreaName +
                receiverInfo.Address
              }}
            </div>
          </div>
        </div>
        <div
          style="
            display: flex;
            justify-content: flex-start;
            border-bottom: 1px solid #333;
            position: relative;
          "
        >
          <div
            style="
              width: 12px;
              padding: 0 2px;
              text-align: center;
              font-size: 10px;
              border-right: 1px solid #333;
              margin-right: 10px;
            "
          >
            寄件人
          </div>
          <div style="display: flex; flex-direction: column">
            <div
              style="
                display: flex;
                justify-content: flex-start;
                font-size: 10px;
              "
            >
              {{ senderInfo.Name }} {{ senderInfo.Tel }}
            </div>
            <div style="font-size: 10px">
              {{
                senderInfo.ProvinceName +
                senderInfo.CityName +
                senderInfo.ExpAreaName +
                senderInfo.Address
              }}
            </div>
          </div>
        </div>
        <div
          style="
            display: flex;
            justify-content: flex-start;
            border-bottom: 1px solid #333;
            position: relative;
          "
        >
          <div
            style="
              width: 12px;
              padding: 0 2px;
              text-align: center;
              font-size: 10px;
              border-right: 1px solid #333;
              margin-right: 10px;
            "
          >
            托寄物
          </div>
          <div
            style="
              font-size: 20px;
              width: 30%;
              border-right: 1px solid #333;
              margin-right: 4px;
            "
          >
            海鲜
          </div>
          <div
            style="
              width: 30%;
              display: flex;
              flex-direction: column;
              border-right: 1px solid #333;
              margin-right: 4px;
            "
          >
            <div style="height: 30px; font-size: 12px">收件员</div>
            <div style="height: 30px; font-size: 12px">派件员</div>
          </div>
          <div style="width: 20%; display: flex; flex-direction: column">
            <div style="height: 30px; font-size: 12px">收方签署</div>
            <div style="height: 30px; font-size: 12px">日期</div>
          </div>
        </div>
        <div
          style="
            width: 100%;
            text-align: center;
            height: 16px;
            font-size: 10px;
            border-bottom: 1px solid #333;
          "
        >
          寄件时间:{{ sfHasValue.send_time }},账号:{{
            sfHasValue.MonthCode
          }},<label v-if="sfHasValue.expressValue > 0"
            >保价:{{ sfHasValue.expressValue }}元</label
          >
        </div>
        <div style="display: flex; width: 100%; border-bottom: 1px solid #333">
          <div style="width: 100%; text-align: center">
            <barcode
              :value="sfHasValue.LogisticCode"
              height="30"
              display-value="false"
              tag="img"
            ></barcode>
            <div
              style="
                width: 100%;
                margin: 0 auto;
                text-align: center;
                font-size: 12px;
                padding-bottom: 2px;
              "
            >
              快递单号:{{ sfHasValue.LogisticCode }}
            </div>
          </div>
        </div>
        <div
          style="
            display: flex;
            justify-content: flex-start;
            border-bottom: 1px solid #333;
            position: relative;
          "
        >
          <div
            style="
              width: 12px;
              padding: 0 2px;
              text-align: center;
              font-size: 10px;
              border-right: 1px solid #333;
              margin-right: 10px;
            "
          >
            收方
          </div>
          <div style="display: flex; flex-direction: column">
            <div
              style="
                display: flex;
                justify-content: flex-start;
                font-size: 10px;
              "
            >
              {{ receiverInfo.Name }} {{ receiverInfo.Tel }}
            </div>
            <div style="font-size: 10px">
              {{
                receiverInfo.ProvinceName +
                receiverInfo.CityName +
                receiverInfo.ExpAreaName +
                receiverInfo.Address
              }}
            </div>
          </div>
        </div>
        <div
          style="
            display: flex;
            justify-content: flex-start;
            border-bottom: 1px solid #333;
            position: relative;
          "
        >
          <div
            style="
              width: 12px;
              padding: 0 2px;
              text-align: center;
              font-size: 10px;
              border-right: 1px solid #333;
              margin-right: 10px;
            "
          >
            寄方
          </div>
          <div style="display: flex; flex-direction: column">
            <div
              style="
                display: flex;
                justify-content: flex-start;
                font-size: 10px;
              "
            >
              {{ senderInfo.Name }} {{ senderInfo.Tel }}
            </div>
            <div style="font-size: 10px">
              {{
                senderInfo.ProvinceName +
                senderInfo.CityName +
                senderInfo.ExpAreaName +
                senderInfo.Address
              }}
            </div>
          </div>
        </div>
        <div style="height: 80px; font-size: 10px">
          {{ orderInfo.print_info }}
        </div>
      </div>
      <div
        id="sfth-block"
        v-if="expressType == 3"
        style="
          display: block;
          width: 400px;
          height: 500px;
          border: 1px solid #333;
          background: #fff;
          overflow: hidden;
          margin: 0 auto;
        "
      >
        <div style="display: flex; width: 100%; border-bottom: 1px solid #333">
          <div style="width: 100%; text-align: center">
            <barcode
              :value="sfHasValue.LogisticCode"
              height="40"
              display-value="false"
              tag="img"
            ></barcode>
            <div
              style="
                width: 100%;
                margin: 0 auto;
                text-align: center;
                font-size: 12px;
                padding-bottom: 2px;
              "
            >
              快递单号:{{ sfHasValue.LogisticCode }}
            </div>
          </div>
        </div>
        <div
          style="
            display: flex;
            flex-direction: column;
            width: 100%;
            border-bottom: 1px solid #333;
          "
        >
          <div style="text-align: center; font-size: 32px; font-weight: bold">
            {{ sfHasValue.DestinatioCode }}
          </div>
        </div>
        <div
          style="
            display: flex;
            justify-content: flex-start;
            border-bottom: 1px solid #333;
            position: relative;
          "
        >
          <div
            style="
              width: 12px;
              padding: 0 2px;
              text-align: center;
              font-size: 10px;
              border-right: 1px solid #333;
              margin-right: 10px;
            "
          >
            收件人
          </div>
          <div style="display: flex; flex-direction: column">
            <div
              style="
                display: flex;
                justify-content: flex-start;
                font-size: 12px;
              "
            >
              {{ receiverInfo.Name }} {{ receiverInfo.Tel }}
            </div>
            <div style="font-size: 12px">
              {{
                receiverInfo.ProvinceName +
                receiverInfo.CityName +
                receiverInfo.ExpAreaName +
                receiverInfo.Address
              }}
            </div>
          </div>
        </div>
        <div
          style="
            display: flex;
            justify-content: flex-start;
            border-bottom: 1px solid #333;
            position: relative;
          "
        >
          <div
            style="
              width: 12px;
              padding: 0 2px;
              text-align: center;
              font-size: 10px;
              border-right: 1px solid #333;
              margin-right: 10px;
            "
          >
            寄件人
          </div>
          <div style="display: flex; flex-direction: column">
            <div
              style="
                display: flex;
                justify-content: flex-start;
                font-size: 10px;
              "
            >
              {{ senderInfo.Name }} {{ senderInfo.Tel }}
            </div>
            <div style="font-size: 10px">
              {{
                senderInfo.ProvinceName +
                senderInfo.CityName +
                senderInfo.ExpAreaName +
                senderInfo.Address
              }}
            </div>
          </div>
        </div>
        <div
          style="
            display: flex;
            justify-content: flex-start;
            border-bottom: 1px solid #333;
            position: relative;
          "
        >
          <div
            style="
              width: 12px;
              padding: 0 2px;
              text-align: center;
              font-size: 10px;
              border-right: 1px solid #333;
              margin-right: 10px;
            "
          >
            托寄物
          </div>
          <div
            style="
              font-size: 20px;
              width: 30%;
              border-right: 1px solid #333;
              margin-right: 4px;
            "
          >
            海干货
          </div>
          <div
            style="
              width: 30%;
              display: flex;
              flex-direction: column;
              border-right: 1px solid #333;
              margin-right: 4px;
            "
          >
            <div style="height: 30px; font-size: 12px">收件员</div>
            <div style="height: 30px; font-size: 12px">派件员</div>
          </div>
          <div style="width: 20%; display: flex; flex-direction: column">
            <div style="height: 30px; font-size: 12px">收方签署</div>
            <div style="height: 30px; font-size: 12px">日期</div>
          </div>
        </div>
        <div
          style="
            width: 100%;
            text-align: center;
            height: 16px;
            font-size: 10px;
            border-bottom: 1px solid #333;
          "
        >
          寄件时间:{{ sfHasValue.send_time }},账号:{{
            sfHasValue.MonthCode
          }},<label v-if="sfHasValue.expressValue > 0"
            >保价:{{ sfHasValue.expressValue }}元</label
          >
        </div>
        <div style="display: flex; width: 100%; border-bottom: 1px solid #333">
          <div style="width: 100%; text-align: center">
            <barcode
              :value="sfHasValue.LogisticCode"
              height="30"
              display-value="false"
              tag="img"
            ></barcode>
            <div
              style="
                width: 100%;
                margin: 0 auto;
                text-align: center;
                font-size: 12px;
                padding-bottom: 2px;
              "
            >
              快递单号:{{ sfHasValue.LogisticCode }}
            </div>
          </div>
        </div>
        <div
          style="
            display: flex;
            justify-content: flex-start;
            border-bottom: 1px solid #333;
            position: relative;
          "
        >
          <div
            style="
              width: 12px;
              padding: 0 2px;
              text-align: center;
              font-size: 10px;
              border-right: 1px solid #333;
              margin-right: 10px;
            "
          >
            收方
          </div>
          <div style="display: flex; flex-direction: column">
            <div
              style="
                display: flex;
                justify-content: flex-start;
                font-size: 10px;
              "
            >
              {{ receiverInfo.Name }} {{ receiverInfo.Tel }}
            </div>
            <div style="font-size: 10px">
              {{
                receiverInfo.ProvinceName +
                receiverInfo.CityName +
                receiverInfo.ExpAreaName +
                receiverInfo.Address
              }}
            </div>
          </div>
        </div>
        <div
          style="
            display: flex;
            justify-content: flex-start;
            border-bottom: 1px solid #333;
            position: relative;
          "
        >
          <div
            style="
              width: 12px;
              padding: 0 2px;
              text-align: center;
              font-size: 10px;
              border-right: 1px solid #333;
              margin-right: 10px;
            "
          >
            寄方
          </div>
          <div style="display: flex; flex-direction: column">
            <div
              style="
                display: flex;
                justify-content: flex-start;
                font-size: 10px;
              "
            >
              {{ senderInfo.Name }} {{ senderInfo.Tel }}
            </div>
            <div style="font-size: 10px">
              {{
                senderInfo.ProvinceName +
                senderInfo.CityName +
                senderInfo.ExpAreaName +
                senderInfo.Address
              }}
            </div>
          </div>
        </div>
        <div style="height: 80px; font-size: 10px">
          {{ orderInfo.print_info }}
        </div>
      </div>
      <div
        id="yto-block"
        v-if="expressType == 4"
        style="
          display: block;
          width: 400px;
          height: 720px;
          border: 1px solid #333;
          background: #fff;
          overflow: hidden;
          margin: 0 auto;
        "
      >
        <div style="display: flex; width: 100%; border-bottom: 1px solid #333">
          <div style="width: 100%; text-align: center">
            <barcode
              :value="sfHasValue.LogisticCode"
              height="50"
              display-value="false"
              tag="img"
            ></barcode>
            <div
              style="
                width: 100%;
                margin: 0 auto;
                text-align: center;
                font-size: 12px;
                padding-bottom: 2px;
              "
            >
              快递单号:{{ sfHasValue.LogisticCode }}
            </div>
          </div>
        </div>
        <div
          style="
            display: flex;
            flex-direction: column;
            width: 100%;
            border-bottom: 1px solid #333;
          "
        >
          <div style="text-align: center; font-size: 44px; font-weight: bold">
            {{ sfHasValue.MarkDestination }}
          </div>
        </div>
        <div
          style="
            display: flex;
            justify-content: flex-start;
            border-bottom: 1px solid #333;
            position: relative;
          "
        >
          <div
            style="
              width: 12px;
              padding: 0 2px;
              text-align: center;
              font-size: 12px;
              border-right: 1px solid #333;
              margin-right: 10px;
            "
          >
            收件人
          </div>
          <div style="display: flex; flex-direction: column">
            <div
              style="
                display: flex;
                justify-content: flex-start;
                font-size: 12px;
              "
            >
              {{ receiverInfo.Name }} {{ receiverInfo.Tel }}
            </div>
            <div style="font-size: 12px">
              {{
                receiverInfo.ProvinceName +
                receiverInfo.CityName +
                receiverInfo.ExpAreaName +
                receiverInfo.Address
              }}
            </div>
          </div>
        </div>
        <div
          style="
            display: flex;
            justify-content: flex-start;
            border-bottom: 1px solid #333;
            position: relative;
          "
        >
          <div
            style="
              width: 12px;
              padding: 0 2px;
              text-align: center;
              font-size: 12px;
              border-right: 1px solid #333;
              margin-right: 10px;
            "
          >
            寄件人
          </div>
          <div style="display: flex; flex-direction: column">
            <div
              style="
                display: flex;
                justify-content: flex-start;
                font-size: 12px;
              "
            >
              {{ senderInfo.Name }}{{ senderInfo.Tel }}
            </div>
            <div style="font-size: 12px">
              {{
                senderInfo.ProvinceName +
                senderInfo.CityName +
                senderInfo.ExpAreaName +
                senderInfo.Address
              }}
            </div>
          </div>
        </div>
        <div
          style="
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid #333;
            position: relative;
          "
        >
          <div style="width: 50%; height: 70px; border-right: 1px solid #333">
            <div style="height: 30px; font-size: 12px">收件人/代收人：</div>
          </div>
          <div style="width: 50%; height: 70px">
            <div style="height: 30px; font-size: 12px">签收时间：</div>
          </div>
        </div>
        <div
          style="width: 100%; text-align: center; height: 30px; font-size: 10px"
        >
          寄件时间：{{ sfHasValue.send_time }}
        </div>

        <div
          style="width: 100%; text-align: center; height: 60px; font-size: 10px"
        ></div>
        <div style="display: flex; width: 100%; border-bottom: 1px solid #333">
          <div style="width: 100%; text-align: center">
            <barcode
              :value="sfHasValue.LogisticCode"
              height="30"
              display-value="false"
              tag="img"
            ></barcode>
            <div
              style="
                width: 100%;
                margin: 0 auto;
                text-align: center;
                font-size: 12px;
                padding-bottom: 2px;
              "
            >
              快递单号:{{ sfHasValue.LogisticCode }}
            </div>
          </div>
        </div>
        <div
          style="
            display: flex;
            justify-content: flex-start;
            border-bottom: 1px solid #333;
            position: relative;
          "
        >
          <div
            style="
              width: 12px;
              padding: 0 2px;
              text-align: center;
              font-size: 10px;
              border-right: 1px solid #333;
              margin-right: 10px;
            "
          >
            收方
          </div>
          <div style="display: flex; flex-direction: column">
            <div
              style="
                display: flex;
                justify-content: flex-start;
                font-size: 10px;
              "
            >
              {{ receiverInfo.Name }} {{ receiverInfo.Tel }}
            </div>
            <div style="font-size: 10px">
              {{
                receiverInfo.ProvinceName +
                receiverInfo.CityName +
                receiverInfo.ExpAreaName +
                receiverInfo.Address
              }}
            </div>
          </div>
        </div>
        <div
          style="
            display: flex;
            justify-content: flex-start;
            border-bottom: 1px solid #333;
            position: relative;
          "
        >
          <div
            style="
              width: 12px;
              padding: 0 2px;
              text-align: center;
              font-size: 10px;
              border-right: 1px solid #333;
              margin-right: 10px;
            "
          >
            寄方
          </div>
          <div style="display: flex; flex-direction: column">
            <div
              style="
                display: flex;
                justify-content: flex-start;
                font-size: 10px;
              "
            >
              {{ senderInfo.Name }}{{ senderInfo.Tel }}
            </div>
            <div style="font-size: 10px">
              {{
                senderInfo.ProvinceName +
                senderInfo.CityName +
                senderInfo.ExpAreaName +
                senderInfo.Address
              }}
            </div>
          </div>
        </div>
        <div style="height: 80px; font-size: 10px">
          {{ orderInfo.print_info }}
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelPrint">取 消</el-button>
        <el-button
          v-if="autoGoDelivery"
          type="primary"
          @click="printAndDeliveryConfirm"
          >打印并发货</el-button
        >
        <el-button
          v-else-if="!autoGoDelivery"
          type="primary"
          @click="printOnlyConfirm"
          >只打印快递单</el-button
        >
      </div>
    </el-dialog>
    <el-dialog title="修改价格" :visible.sync="dialogPriceVisible">
      <el-form :model="orderInfo">
        <el-form-item label="改价前总价:">
          <h2>¥{{ orderInfo.change_price }}</h2>
        </el-form-item>

        <el-form-item label="修改商品价格:">
          <el-input-number
            @change="goodsPriceChange"
            :min="0"
            :max="99999999"
            v-model="orderInfo.goods_price"
            auto-complete="off"
            placeholder="请输入商品价格"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="修改快递价格:">
          <el-input-number
            @change="freightPriceChange"
            :min="0"
            :max="99999999"
            v-model="orderInfo.freight_price"
            auto-complete="off"
            placeholder="请输入修改后的快递"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="改价后总价:">
          <h2>¥{{ orderInfo.actual_price }}</h2>
        </el-form-item>
        <el-form-item label="">
          {{ getPriceChangeText() }}
        </el-form-item>
        <!-- <el-form-item label="快递费用:">
                    <el-input v-model="orderInfo.freight_price" auto-complete="off" placeholder="请输入修改后的快递价格"></el-input>
                </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogPriceVisible = false">取 消</el-button>
        <el-button type="primary" @click="priceChangeConfirm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 旗子设置弹窗 -->
    <el-dialog
      title="设置订单标识和备注"
      :visible.sync="flagDialogVisible"
      width="500px"
      :before-close="handleCloseFlagDialog"
    >
      <div v-if="currentOrderInfo" class="flag-setting-content">
        <!-- 订单基本信息 -->
        <div class="order-basic-info">
          <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; margin-bottom: 20px;">
            <div style="font-size: 13px; color: #666; margin-bottom: 4px;">
              订单号：{{ currentOrderInfo.order_sn }}
            </div>
            <div style="font-size: 13px; color: #666;">
              下单时间：{{ currentOrderInfo.add_time }}
            </div>
          </div>
        </div>

        <!-- 旗子颜色选择 -->
        <div class="flag-color-section">
          <h4 style="margin: 0 0 12px 0; color: #333; font-size: 14px;">选择标识颜色</h4>
          <div style="display: flex; gap: 8px; margin-bottom: 20px; flex-wrap: wrap;">
            <div
              v-for="(flag, key) in flagColors"
              :key="key"
              :style="getFlagOptionStyle(key, flagForm.service_flag_color)"
              @click="flagForm.service_flag_color = key"
              style="width: 40px; height: 40px; border-radius: 6px; display: flex; align-items: center; justify-content: center; cursor: pointer; border: 2px solid transparent;"
              :title="flag.name + '：' + flag.description"
            >
              <i class="el-icon-flag" style="font-size: 16px; color: white;"></i>
            </div>
            <!-- 清除选择 -->
            <div
              :style="getFlagOptionStyle(null, flagForm.service_flag_color)"
              @click="flagForm.service_flag_color = null"
              style="width: 40px; height: 40px; border-radius: 6px; display: flex; align-items: center; justify-content: center; cursor: pointer; border: 2px solid #d1d5db; background: white;"
              title="清除标识"
            >
              <i class="el-icon-close" style="font-size: 16px; color: #6b7280;"></i>
            </div>
          </div>
          <div v-if="flagForm.service_flag_color" style="font-size: 12px; color: #666; margin-bottom: 16px;">
            当前选择：{{ flagColors[flagForm.service_flag_color].name }} - {{ flagColors[flagForm.service_flag_color].description }}
          </div>
        </div>

        <!-- 备注内容 -->
        <div class="memo-content-section">
          <h4 style="margin: 0 0 12px 0; color: #333; font-size: 14px;">客服备注</h4>
          <el-input
            v-model="flagForm.admin_memo"
            type="textarea"
            :rows="3"
            placeholder="请输入客服备注内容..."
            maxlength="255"
            show-word-limit
          ></el-input>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="flagDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveFlagSetting" :loading="flagSaving">保存</el-button>
      </span>
    </el-dialog>



    <!-- 客服备注弹窗 -->
    <el-dialog
      title="客服备注"
      :visible.sync="serviceMemoDialogVisible"
      width="500px"
      :before-close="handleCloseServiceMemo"
    >
      <div v-if="currentOrderInfo" class="service-memo-content">
        <!-- 订单基本信息 -->
        <div class="order-basic-info">
          <h4 style="margin: 0 0 12px 0; color: #333; font-size: 14px;">订单信息</h4>
          <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; margin-bottom: 20px;">
            <div style="font-size: 13px; color: #666; margin-bottom: 4px;">
              订单号：{{ currentOrderInfo.order_sn }}
            </div>
            <div style="font-size: 13px; color: #666;">
              下单时间：{{ currentOrderInfo.add_time }}
            </div>
          </div>
        </div>

        <!-- 旗子颜色选择 -->
        <div class="flag-color-section">
          <h4 style="margin: 0 0 12px 0; color: #333; font-size: 14px;">优先级标识</h4>
          <div style="display: flex; gap: 8px; margin-bottom: 20px;">
            <div
              v-for="(flag, key) in flagColors"
              :key="key"
              :style="getFlagOptionStyle(key, serviceMemoForm.service_flag_color)"
              @click="serviceMemoForm.service_flag_color = key"
              style="width: 40px; height: 40px; border-radius: 6px; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.2s; border: 2px solid transparent;"
              :title="flag.name + '：' + flag.description"
            >
              <i class="el-icon-flag" style="font-size: 16px; color: white;"></i>
            </div>
            <!-- 清除选择 -->
            <div
              :style="getFlagOptionStyle(null, serviceMemoForm.service_flag_color)"
              @click="serviceMemoForm.service_flag_color = null"
              style="width: 40px; height: 40px; border-radius: 6px; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.2s; border: 2px solid #d1d5db; background: white;"
              title="清除标识"
            >
              <i class="el-icon-close" style="font-size: 16px; color: #6b7280;"></i>
            </div>
          </div>
          <div v-if="serviceMemoForm.service_flag_color" style="font-size: 12px; color: #666; margin-bottom: 16px;">
            当前选择：{{ flagColors[serviceMemoForm.service_flag_color].name }} - {{ flagColors[serviceMemoForm.service_flag_color].description }}
          </div>
        </div>

        <!-- 备注内容 -->
        <div class="memo-content-section">
          <h4 style="margin: 0 0 12px 0; color: #333; font-size: 14px;">备注内容</h4>
          <el-input
            v-model="serviceMemoForm.admin_memo"
            type="textarea"
            :rows="4"
            placeholder="请输入客服备注内容..."
            maxlength="255"
            show-word-limit
          ></el-input>
        </div>

        <!-- 更新信息 -->
        <div v-if="currentOrderInfo.service_updated_at" class="update-info">
          <div style="font-size: 12px; color: #999; margin-top: 16px; padding-top: 16px; border-top: 1px solid #f0f0f0;">
            最后更新：{{ currentOrderInfo.service_updated_at }}
            <span v-if="currentOrderInfo.service_updated_by">by {{ currentOrderInfo.service_updated_by }}</span>
          </div>
        </div>
      </div>

      <div v-else class="loading-content" style="text-align: center; padding: 40px;">
        <i class="el-icon-loading" style="font-size: 24px; margin-right: 8px;"></i>
        <span>加载中...</span>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="serviceMemoDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveServiceMemo" :loading="serviceMemoSaving">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import VueBarcode from "../../../node_modules/vue-barcode";
import ElButton from "../../../node_modules/element-ui/packages/button/src/button.vue";
// Vue.component(VueBarcode.name, VueBarcode);

// import { Button } from 'element-ui';
export default {
  components: {
    barcode: VueBarcode
  },
  data() {
    return {
      autoGoDelivery: true,
      sfHasValue: {},
      barcodeValue: "test",
      printMiandan: false,
      rawHtml: "",
      testApi:
        "http://sandboxapi.kdniao.com:8080/kdniaosandbox/gateway/exterfaceInvoke.json",
      expressType: 0, // 选择快递方式
      checkAll: false,
      checkedCities: ["上海", "北京"],
      cities: ["上海", "北京", "广州", "深圳"],
      isIndeterminate: true,
      page: 1,
      total: 0,
      isLoading: false,
      // 状态统计相关
      statusCounts: {
        all: 0,
        toPay: 0,
        toDelivery: 0,
        toReceive: 0,
        received: 0,
        closed: 0
      },
      statusCountsLoading: true,
      // 客服备注相关
      serviceMemoDialogVisible: false,
      flagDialogVisible: false,
      currentOrderInfo: null,
      serviceMemoForm: {
        admin_memo: '',
        service_flag_color: null,
        service_priority: 0
      },
      flagForm: {
        service_flag_color: null,
        service_priority: 0,
        admin_memo: ''
      },
      serviceMemoSaving: false,
      flagSaving: false,
      flagColors: {
        red: { name: '紧急', color: '#ff4d4f', description: '需要紧急处理' },
        orange: { name: '重要', color: '#ff7a00', description: '重要客户或订单' },
        yellow: { name: '注意', color: '#faad14', description: '需要特别注意' },
        green: { name: '正常', color: '#52c41a', description: '正常处理' },
        blue: { name: '跟进', color: '#1890ff', description: '需要跟进' },
        purple: { name: '特殊', color: '#722ed1', description: '特殊情况' }
      },
      filterForm: {
        name: "",
        logistic_code: "",
        order_sn: "",
        consignee: "",
        refundStatus: "",
        refundType: "",
        processStatus: ""
      },
      tableData: [],
      activeName: "second",
      order_status: 300,
      // 新增统计数据
      todayOrderCount: 0,
      pendingOrderCount: 0,
      todaySales: 0,
      monthSales: 0,
      pendingRefundCount: 0,
      dialogVisible: false,
      dialogVisible2: false,
      dialogFormVisible: false,
      dialogPriceVisible: false,
      dialogText: "",
      dialogIndex: 0,
      order_sn: 0,
      order_id: 0,
      dform: {
        method: 2,
      },
      orderInfo: {},
      isShow: true,
      deliveryCom: [],
      nowDeliveryId: "",
      formLabelWidth: "120px",
      barcode_option: {
        displayValue: false, //是否默认显示条形码数据
        background: "#fff", //条形码背景颜色
        width: 2,
        height: 100,
        fontSize: 20, //字体大小
      },
      senderInfo: {},
      receiverInfo: {},
      dialogExpressVisible: false,
      rePrintStatus: 0,
      changeSender: 0,
      changeReceive: 0,
      options: [],
      senderOptions: [],
      receiveOptions: [],
      receiver: {},
      sender: {},
    };
  },
  methods: {
    // 获取订单状态统计
    async getStatusCounts() {
      try {
        this.statusCountsLoading = true;
        const response = await this.axios.get('order/getStatusCount');
        this.statusCounts = response.data.data;

        // 获取售后统计
        await this.getRefundCounts();
      } catch (error) {
        console.error('获取订单状态统计失败:', error);
        this.$message.error('获取订单统计失败');
      } finally {
        this.statusCountsLoading = false;
      }
    },

    // 获取售后统计
    async getRefundCounts() {
      try {
        const response = await this.axios.get('order/refundList', {
          params: {
            page: 1,
            size: 1
            // 不传递refundStatus参数，使用默认逻辑排除已完成的售后
          }
        });
        this.pendingRefundCount = response.data.data.count || 0;
      } catch (error) {
        console.error('获取售后统计失败:', error);
        this.pendingRefundCount = 0;
      }
    },

    // 客服备注相关方法
    // 获取旗子样式
    getFlagStyle(flagColor) {
      if (!flagColor || !this.flagColors[flagColor]) {
        return {
          background: '#e5e7eb',
          border: '1px solid #d1d5db'
        };
      }
      return {
        background: this.flagColors[flagColor].color,
        border: `1px solid ${this.flagColors[flagColor].color}`
      };
    },

    // 获取旗子标题
    getFlagTitle(flagColor) {
      if (!flagColor || !this.flagColors[flagColor]) {
        return '点击设置优先级';
      }
      return `${this.flagColors[flagColor].name}：${this.flagColors[flagColor].description}`;
    },

    // 获取旗子选项样式
    getFlagOptionStyle(flagKey, currentFlag) {
      const isSelected = flagKey === currentFlag;
      const baseStyle = {
        border: isSelected ? '2px solid #4f46e5' : '2px solid transparent'
      };

      if (flagKey === null) {
        return {
          ...baseStyle,
          background: 'white',
          border: isSelected ? '2px solid #4f46e5' : '2px solid #d1d5db'
        };
      }

      if (this.flagColors[flagKey]) {
        return {
          ...baseStyle,
          background: this.flagColors[flagKey].color
        };
      }

      return baseStyle;
    },

    // 打开旗子设置弹窗
    async openFlagDialog(orderItem) {
      this.currentOrderInfo = orderItem;
      this.flagForm = {
        service_flag_color: orderItem.service_flag_color || null,
        service_priority: orderItem.service_priority || 0,
        admin_memo: orderItem.admin_memo || ''
      };
      this.flagDialogVisible = true;
    },



    // 打开客服备注弹窗（保留原有功能）
    async openServiceMemo(orderItem) {
      this.currentOrderInfo = orderItem;
      this.serviceMemoForm = {
        admin_memo: orderItem.admin_memo || '',
        service_flag_color: orderItem.service_flag_color || null,
        service_priority: orderItem.service_priority || 0
      };
      this.serviceMemoDialogVisible = true;
    },

    // 关闭旗子设置弹窗
    handleCloseFlagDialog() {
      this.flagDialogVisible = false;
      this.currentOrderInfo = null;
      this.flagForm = {
        service_flag_color: null,
        service_priority: 0,
        admin_memo: ''
      };
    },



    // 关闭客服备注弹窗
    handleCloseServiceMemo() {
      this.serviceMemoDialogVisible = false;
      this.currentOrderInfo = null;
      this.serviceMemoForm = {
        admin_memo: '',
        service_flag_color: null,
        service_priority: 0
      };
    },

    // 保存旗子设置
    async saveFlagSetting() {
      if (!this.currentOrderInfo) {
        this.$message.error('订单信息不存在');
        return;
      }

      try {
        this.flagSaving = true;

        const response = await this.axios.post('order/updateServiceMemo', {
          order_id: this.currentOrderInfo.id,
          admin_memo: this.flagForm.admin_memo,
          service_flag_color: this.flagForm.service_flag_color,
          service_priority: this.flagForm.service_priority
        });

        if (response.data.errno === 0) {
          this.$message.success('标识和备注保存成功');

          // 更新当前订单数据
          const orderIndex = this.tableData.findIndex(item => item.id === this.currentOrderInfo.id);
          if (orderIndex !== -1) {
            this.tableData[orderIndex].service_flag_color = this.flagForm.service_flag_color;
            this.tableData[orderIndex].service_priority = this.flagForm.service_priority;
            this.tableData[orderIndex].admin_memo = this.flagForm.admin_memo;
          }

          this.flagDialogVisible = false;
        } else {
          this.$message.error(response.data.errmsg || '保存失败');
        }
      } catch (error) {
        console.error('保存旗子设置失败:', error);
        this.$message.error('保存失败，请重试');
      } finally {
        this.flagSaving = false;
      }
    },



    // 保存客服备注
    async saveServiceMemo() {
      if (!this.currentOrderInfo) {
        this.$message.error('订单信息不存在');
        return;
      }

      try {
        this.serviceMemoSaving = true;

        const response = await this.axios.post('order/updateServiceMemo', {
          order_id: this.currentOrderInfo.id,
          admin_memo: this.serviceMemoForm.admin_memo,
          service_flag_color: this.serviceMemoForm.service_flag_color,
          service_priority: this.serviceMemoForm.service_priority
        });

        if (response.data.errno === 0) {
          this.$message.success('客服备注保存成功');

          // 更新当前订单数据
          const orderIndex = this.tableData.findIndex(item => item.id === this.currentOrderInfo.id);
          if (orderIndex !== -1) {
            this.tableData[orderIndex].admin_memo = this.serviceMemoForm.admin_memo;
            this.tableData[orderIndex].service_flag_color = this.serviceMemoForm.service_flag_color;
            this.tableData[orderIndex].service_priority = this.serviceMemoForm.service_priority;
          }

          this.serviceMemoDialogVisible = false;
        } else {
          this.$message.error(response.data.errmsg || '保存失败');
        }
      } catch (error) {
        console.error('保存客服备注失败:', error);
        this.$message.error('保存失败，请重试');
      } finally {
        this.serviceMemoSaving = false;
      }
    },

    // 新增方法：处理标签点击
    handleTabClick(tabName) {
      // 如果点击的是当前激活的tab，不执行任何操作
      if (this.activeName === tabName) {
        return;
      }

      this.activeName = tabName;
      // 切换tab时先重置total，避免显示旧数据
      this.total = 0;
      this.isLoading = true;

      // 添加短暂延迟，让动画效果更明显
      setTimeout(() => {
        if (tabName === 'first') {
          this.order_status = "101,801";
        } else if (tabName === 'second') {
          this.order_status = 300;
        } else if (tabName === 'third') {
          this.order_status = 301;
        } else if (tabName === 'fourth') {
          this.order_status = 401;
        } else if (tabName === 'fifth') {
          this.order_status = "102,103,203"; // 包含售后完成状态
        } else if (tabName === 'sixth') {
          this.order_status = "101,102,103,202,203,300,301,302,303,401,801,802";
        } else if (tabName === 'refund') {
          // 售后管理标签，加载售后订单列表
          this.getRefundList();
          return;
        }
        this.page = 1;
        this.getList();
      }, 150); // 150ms延迟，配合动画时间
    },

    // 获取售后订单列表
    async getRefundList() {
      try {
        this.isLoading = true;
        const response = await this.axios.get('order/refundList', {
          params: {
            page: this.page,
            size: 10,
            orderSn: this.filterForm.order_sn,
            consignee: this.filterForm.consignee,
            refundStatus: this.filterForm.refundStatus,
            refundType: this.filterForm.refundType,
            processStatus: this.filterForm.processStatus
          }
        });

        // 将售后数据转换为订单格式以便在现有表格中显示
        this.tableData = response.data.data.data.map(refund => ({
          id: refund.order_id,
          order_sn: refund.order_sn,
          consignee: refund.consignee,
          mobile: refund.mobile,
          actual_price: refund.actual_price,
          add_time: refund.order_add_time_text,
          pay_time: '已付款',
          order_status_text: '有售后',
          goodsList: refund.goodsList,
          goodsCount: refund.goodsList ? refund.goodsList.reduce((sum, item) => sum + item.number, 0) : 0,
          userInfo: refund.userInfo,
          hasRefund: true,
          refundInfo: {
            id: refund.id,
            status: refund.status,
            status_text: refund.status_text,
            refund_amount: refund.refund_amount,
            refund_reason: refund.refund_reason,
            apply_time_text: refund.apply_time_text
          },
          // 标记这是售后订单，用于区分显示
          isRefundOrder: true
        }));

        this.total = response.data.data.count;
        this.isLoading = false;
      } catch (error) {
        console.error('获取售后订单列表失败:', error);
        this.$message.error('获取售后订单列表失败');
        this.isLoading = false;
      }
    },

    // 处理售后申请
    handleRefund(refundInfo) {
      this.$confirm(`确定要处理售后申请吗？\n申请ID: ${refundInfo.id}\n退款金额: ¥${refundInfo.refund_amount}\n退款原因: ${refundInfo.refund_reason}`, '处理售后', {
        confirmButtonText: '同意',
        cancelButtonText: '拒绝',
        distinguishCancelAndClose: true,
        type: 'warning'
      }).then(() => {
        // 同意售后
        this.approveRefund(refundInfo.id);
      }).catch((action) => {
        if (action === 'cancel') {
          // 拒绝售后
          this.rejectRefund(refundInfo.id);
        }
      });
    },

    // 同意售后申请
    async approveRefund(applyId) {
      try {
        const response = await this.axios.post('order/handleRefund', {
          applyId: applyId,
          action: 'approve',
          adminMemo: '管理员同意退款申请'
        });

        if (response.data.errno === 0) {
          this.$message.success('售后申请已同意');
          // 刷新列表
          if (this.activeName === 'refund') {
            this.getRefundList();
          } else {
            this.getList();
          }
          this.getRefundCounts(); // 更新售后统计
        } else {
          this.$message.error(response.data.errmsg || '操作失败');
        }
      } catch (error) {
        console.error('同意售后失败:', error);
        this.$message.error('操作失败，请重试');
      }
    },

    // 拒绝售后申请
    async rejectRefund(applyId) {
      this.$prompt('请输入拒绝原因', '拒绝售后', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '请输入拒绝原因'
      }).then(async ({ value }) => {
        try {
          const response = await this.axios.post('order/handleRefund', {
            applyId: applyId,
            action: 'reject',
            rejectReason: value
          });

          if (response.data.errno === 0) {
            this.$message.success('售后申请已拒绝');
            // 刷新列表
            if (this.activeName === 'refund') {
              this.getRefundList();
            } else {
              this.getList();
            }
            this.getRefundCounts(); // 更新售后统计
          } else {
            this.$message.error(response.data.errmsg || '操作失败');
          }
        } catch (error) {
          console.error('拒绝售后失败:', error);
          this.$message.error('操作失败，请重试');
        }
      });
    },

    // 完成售后
    async completeRefund(refundInfo) {
      this.$confirm(`确定要完成售后吗？\n申请ID: ${refundInfo.id}\n退款金额: ¥${refundInfo.refund_amount}`, '完成售后', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(async () => {
        try {
          const response = await this.axios.post('order/handleRefund', {
            applyId: refundInfo.id,
            action: 'complete'
          });

          if (response.data.errno === 0) {
            this.$message.success('售后已完成');
            // 刷新列表
            if (this.activeName === 'refund') {
              this.getRefundList();
            } else {
              this.getList();
            }
            this.getRefundCounts(); // 更新售后统计
          } else {
            this.$message.error(response.data.errmsg || '操作失败');
          }
        } catch (error) {
          console.error('完成售后失败:', error);
          this.$message.error('操作失败，请重试');
        }
      });
    },

    // 确认收到退货
    async confirmReturn(refundInfo) {
      this.$confirm(`确定已收到退货商品吗？\n申请ID: ${refundInfo.id}\n退款金额: ¥${refundInfo.refund_amount}`, '确认收货', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(async () => {
        try {
          const response = await this.axios.post('order/handleRefund', {
            applyId: refundInfo.id,
            action: 'confirm_return',
            adminMemo: '已确认收到退货商品，退款处理完成'
          });

          if (response.data.errno === 0) {
            this.$message.success('确认收货成功，订单已关闭');
            // 刷新列表
            if (this.activeName === 'refund') {
              this.getRefundList();
            } else {
              this.getList();
            }
            this.getRefundCounts(); // 更新售后统计
          } else {
            this.$message.error(response.data.errmsg || '操作失败');
          }
        } catch (error) {
          console.error('确认收货失败:', error);
          this.$message.error('操作失败，请重试');
        }
      });
    },

    // 新增方法：获取状态对应的订单数量
    getStatusCount(status) {
      // 这里可以根据实际需要实现状态统计
      // 暂时返回0，后续可以通过API获取
      return 0;
    },

    // 新增方法：获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        101: 'px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800',
        300: 'px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800',
        301: 'px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800',
        401: 'px-2 py-1 text-xs rounded-full bg-green-100 text-green-800',
        102: 'px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800',
        103: 'px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800',
        801: 'px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800'
      };
      return statusMap[status] || 'px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800';
    },

    // 新增方法：获取状态内联样式
    getStatusStyle(status) {
      const statusMap = {
        101: 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #fef3c7; color: #92400e;',
        300: 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #dbeafe; color: #1e40af;',
        301: 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #e9d5ff; color: #7c3aed;',
        401: 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #d1fae5; color: #065f46;',
        102: 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #f3f4f6; color: #374151;',
        103: 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #f3f4f6; color: #374151;',
        801: 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #fef3c7; color: #92400e;'
      };
      return statusMap[status] || 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #f3f4f6; color: #374151;';
    },

    // 获取售后状态样式
    getRefundStatusStyle(status) {
      const statusMap = {
        'pending': 'background-color: #fef3c7; color: #92400e;',     // 黄色 - 待处理
        'processing': 'background-color: #dbeafe; color: #1e40af;',  // 蓝色 - 处理中
        'approved': 'background-color: #d1fae5; color: #065f46;',    // 绿色 - 已同意
        'rejected': 'background-color: #fee2e2; color: #dc2626;',    // 红色 - 已拒绝
        'wait_return': 'background-color: #e0e7ff; color: #3730a3;', // 紫色 - 等待退货
        'returned': 'background-color: #ecfdf5; color: #14532d;',    // 深绿 - 已退货
        'completed': 'background-color: #e5e7eb; color: #374151;'    // 灰色 - 已完成
      };
      return statusMap[status] || 'background-color: #f3f4f6; color: #6b7280;';
    },

    // 获取退款类型文本
    getRefundTypeText(refundType) {
      const typeMap = {
        'refund_only': '仅退款',
        'return_refund': '退货退款'
      };
      return typeMap[refundType] || '售后';
    },

    // 获取简短地址显示
    getShortAddress(fullRegion, address) {
      const region = fullRegion || '';
      const addr = address || '';
      const fullAddress = region + addr;

      // 如果总长度小于等于20个字符，直接显示
      if (fullAddress.length <= 20) {
        return fullAddress;
      }

      // 如果地区信息太长，优先显示详细地址
      if (region.length > 10 && addr.length > 0) {
        // 保留省份，截断市区信息
        const parts = region.split(/[省市区县]/);
        const province = parts[0] + (region.includes('省') ? '省' : '');
        const shortRegion = province.length > 8 ? province.substring(0, 6) + '...' : province;
        const remainingLength = 18 - shortRegion.length;

        if (addr.length <= remainingLength) {
          return shortRegion + addr;
        } else {
          return shortRegion + addr.substring(0, remainingLength - 3) + '...';
        }
      }

      // 普通截断
      return fullAddress.substring(0, 17) + '...';
    },

    // 新增方法：获取价格变化文本
    getPriceChangeText() {
      if (!this.orderInfo || !this.orderInfo.change_price || !this.orderInfo.actual_price) {
        return '';
      }

      const changePrice = Number(this.orderInfo.change_price);
      const actualPrice = Number(this.orderInfo.actual_price);

      if (changePrice > actualPrice) {
        return `优惠金额：${(changePrice - actualPrice).toFixed(2)}`;
      } else {
        return `涨价金额：${(actualPrice - changePrice).toFixed(2)}`;
      }
    },


    hidePrintDialog() {
      this.dform.method = 2;
      this.dialogFormVisible = false;
      console.log("11111");
    },
    goodsPriceChange(value) {
      console.log(value);
      this.orderInfo.goods_price = value;
      this.orderInfo.actual_price =
        Number(this.orderInfo.goods_price) +
        Number(this.orderInfo.freight_price);
    },
    freightPriceChange(value) {
      this.orderInfo.freight_price = value;
      this.orderInfo.actual_price =
        Number(this.orderInfo.goods_price) + Number(value);
    },
    getAllRegion() {
      let that = this;
      this.axios.get("order/getAllRegion").then((response) => {
        this.options = response.data.data;
      });
    },
    deliveryGoGo() {
      console.log(this.order_id);
      console.log(this.nowDeliveryId);
      console.log(this.dform);
      if (this.dform.method == 2) {
        if (this.dform.logistic_code == undefined || this.nowDeliveryId == "") {
          this.$message({
            type: "error",
            message: "请输入快递单号和快递公司",
          });
          return false;
        }
      }
      this.axios
        .get("order/orderDelivery", {
          params: {
            orderId: this.order_id,
            shipper: this.nowDeliveryId,
            method: this.dform.method,
            logistic_code: this.dform.logistic_code,
          },
        })
        .then((response) => {
          this.dialogFormVisible = false;
          this.dialogExpressVisible = false;
          this.getList();
        });
    },
    getDeliveyInfo() {
      this.axios.get("delivery").then((response) => {
        this.deliveryCom = response.data.data;
      });
    },
    changeExpressValue(info) {
      if (this.expressType == 1) {
        this.axios
          .post("order/saveExpressValueInfo", {
            express_value: info.express_value,
            id: info.id,
          })
          .then((response) => {
            if (response.data.errno === 0) {
              this.$message({
                type: "success",
                message: "保存成功!",
              });
            } else {
              this.$message({
                type: "error",
                message: "保存失败",
              });
            }
          });
      }
    },
    confirm() {
      this.axios
        .get("order/orderpack", {
          params: {
            orderId: this.order_id,
          },
        })
        .then((response) => {
          this.dialogVisible = false;
          this.getList();
        });
    },
    changeRemarkInfo(info) {
      this.axios
        .post("order/saveRemarkInfo", {
          remark: info.remark,
          id: info.id,
        })
        .then((response) => {
          if (response.data.errno === 0) {
            this.$message({
              type: "success",
              message: "保存成功!",
            });
          } else {
            this.$message({
              type: "error",
              message: "保存失败",
            });
          }
        });
    },
    changeInfo(info) {
      let id = info.id;
      let print_info = info.print_info;
      this.axios
        .post("order/savePrintInfo", {
          print_info: print_info,
          id: id,
        })
        .then((response) => {
          if (response.data.errno === 0) {
            this.$message({
              type: "success",
              message: "保存成功!",
            });
          } else {
            this.$message({
              type: "error",
              message: "保存失败",
            });
          }
        });
    },
    changeMemo(id, text) {
      this.axios
        .post("order/saveAdminMemo", {
          text: text,
          id: id,
        })
        .then((response) => {
          if (response.data.errno === 0) {
            this.$message({
              type: "success",
              message: "保存成功!",
            });
          } else {
            this.$message({
              type: "error",
              message: "保存失败",
            });
          }
        });
    },
    cancelPrint() {
      this.printMiandan = false;
      this.dialogFormVisible = false;
    },
    handleCheckedCitiesChange() {
      console.log("哈哈");
    },
    onPrintNum() {
      this.axios
        .post(this.testApi, this.testData, {
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
          },
        })
        .then((response) => {
          console.log(response.data);
        });
    },
    viewDetail(index) {
      this.$router.push({
        name: "order_detail",
        query: {
          id: index,
        },
      });
    },
    handleClick(tab, event) {
      let pindex = tab._data.index;
      if (pindex == 0) {
        this.order_status = "101,801";
      } else if (pindex == 1) {
        this.order_status = 300;
      } else if (pindex == 2) {
        this.order_status = 301;
      } else if (pindex == 3) {
        this.order_status = 401;
      } else if (pindex == 4) {
        this.order_status = "102,103";
      } else if (pindex == 5) {
        this.order_status = "101,102,103,202,203,300,301,302,303,401,801,802";
      }
      this.getList();
    },

    handlePageChange(val) {
      this.page = val;
      //保存到localStorage
      localStorage.setItem("orderPage", this.page);
      localStorage.setItem("orderFilterForm", JSON.stringify(this.filterForm));
      if (this.activeName === 'refund') {
        this.getRefundList();
      } else {
        this.getList();
      }
    },
    handleRowEdit(index, row) {
      this.$router.push({
        name: "order_detail",
        query: {
          id: row.id,
        },
      });
    },
    handleRowDelete(index, row) {
      this.$confirm("确定要删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.axios
          .post("order/destory", {
            id: row.id,
          })
          .then((response) => {
            console.log(response.data);
            if (response.data.errno === 0) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getList();
            }
          });
      });
    },
    onSubmitFilter() {
      this.page = 1;
      if (this.activeName === 'refund') {
        this.getRefundList();
      } else {
        this.getList();
      }
    },
    getList() {
      this.axios
        .get("order", {
          params: {
            page: this.page,
            orderSn: this.filterForm.order_sn,
            consignee: this.filterForm.consignee,
            logistic_code: this.filterForm.logistic_code,
            status: this.order_status,
          },
        })
        .then((response) => {
          this.tableData = response.data.data.data;
          console.log('订单列表数据:', this.tableData);
          // 调试用户头像数据
          if (this.tableData && this.tableData.length > 0) {
            this.tableData.forEach((item, index) => {
              if (item.userInfo) {
                console.log(`订单${index} 用户信息:`, item.userInfo);
                console.log(`订单${index} 用户头像:`, item.userInfo.avatar);
              }
            });
          }
          this.page = response.data.data.currentPage;
          this.total = response.data.data.count;
          this.isLoading = false; // 数据加载完成后重置加载状态
        })
        .catch((error) => {
          console.error('获取订单列表失败:', error);
          this.isLoading = false; // 即使出错也要重置加载状态
        });
    },
    orderEdit(item) {
      this.rePrintStatus = 0;
      console.log(0);
      this.order_id = item.id;
      if (item.order_status == 300 || item.order_status == 301) {
        this.rePrintStatus = 0;
        this.checkExpressInfo();
        console.log(1);
      } else if (item.order_status == 101) {
        this.getOrderInfo(this.order_id);
        this.dialogPriceVisible = true;
        console.log(2);
      } else if (item.order_status == 301 && item.is_fake == 1) {
        this.dialogVisible2 = true;
        console.log(3);
      }
    },
    rePrintExpress() {
      this.rePrintStatus = 0;
      this.axios
        .get("order/rePrintExpress", {
          params: {
            orderId: this.order_id,
          },
        })
        .then((response) => {
          if (response.data.errno === 0) {
            this.expressType = 0;
            this.getOrderInfo(this.order_id);
            this.dialogFormVisible = true;
          }
        });
    },
    directPrintExpress() {
      this.rePrintStatus = 1;
      this.axios
        .get("order/directPrintExpress", {
          params: {
            orderId: this.order_id,
          },
        })
        .then((response) => {
          if (response.data.errno === 0) {
            let express = response.data.data;
            this.expressType = express.express_type;
            let orderInfo = this.orderInfo;
            this.sfHasValue = {
              LogisticCode: express.logistic_code,
              remark: orderInfo.remark,
              DestinatioCode: express.region_code,
              MarkDestination: express.region_code,
              send_time: express.send_time,
              MonthCode: express.MonthCode,
            };
            this.expressType = 0;
            this.dialogFormVisible = true;
          }
        });
    },
    directPrintConfirm() {
      let expressType = this.expressType;
      if (expressType == 0) {
        this.$message({
          type: "error",
          message: "请选择一个快递免单模板!",
        });
        return false;
      }
      if (expressType == 1) {
        this.sfHasValue.expressValue = this.orderInfo.express_value;
      }
      this.printMiandan = true;
      this.dialogFormVisible = false;
      this.dialogExpressVisible = false;
    },
    checkExpressInfo() {
      this.getOrderInfo(this.order_id);
      this.axios
        .get("order/checkExpress", {
          params: {
            orderId: this.order_id,
          },
        })
        .then((response) => {
          console.log(response.data);
          if (response.data.errno === 0) {
            this.dialogExpressVisible = true;
          } else {
            this.expressType = 0;
            this.dialogFormVisible = true;
          }
        });
    },
    receiveConfirm() {
      this.axios
        .get("order/orderReceive", {
          params: {
            orderId: this.order_id,
          },
        })
        .then((response) => {
          this.dialogVisible2 = false;
          this.getList();
        });
    },

    deliveyGoConfirm() {
      // 可以设置成不预览，那么直接打印了
      // 逻辑：打印快递单，这时会向快递鸟发送请求，然后得到快递单号,

      let expressType = this.expressType;
      if (expressType == 0) {
        this.$message({
          type: "error",
          message: "请选择一个快递免单模板!",
        });
        return false;
      }
      console.log(expressType);
      this.sender.senderOptions = this.senderOptions;
      this.receiver.receiveOptions = this.receiveOptions;
      this.axios
        .post("order/getMianExpress", {
          orderId: this.orderInfo.id,
          sender: this.sender,
          receiver: this.receiver,
          expressType: expressType,
        })
        .then((response) => {
          let expressInfo = response.data.data.latestExpressInfo;
          if (expressInfo.ResultCode == 100) {
            this.rawHtml = expressInfo.PrintTemplate;
            this.sfHasValue = expressInfo.Order;
            this.sfHasValue.expressValue = expressInfo.expressValue;
            this.sfHasValue.send_time = expressInfo.send_time;
            this.sfHasValue.remark = expressInfo.remark;
            this.sfHasValue.MonthCode = expressInfo.MonthCode;
            this.sfHasValue.send_time = expressInfo.send_time;
            this.sfHasValue.orderId = this.orderInfo.id;
            this.sfHasValue.expressType = expressType;
            this.senderInfo = response.data.data.sender;
            this.receiverInfo = response.data.data.receiver;
            this.printMiandan = true;
            this.dialogFormVisible = false;
            this.dialogExpressVisible = false;
          } else if (response.data.data.latestExpressInfo.ResultCode == 105) {
            this.$message({
              type: "error",
              message: "操作超时，请重试!",
            });
          }
          // let newWindow = window.open("_blank");   //打开新窗口
          // let codestr = this.rawHtml;   //获取需要生成pdf页面的div代码
          // newWindow.document.write(codestr);   //向文档写入HTML表达式或者JavaScript代码
          // newWindow.document.close();     //关闭document的输出流, 显示选定的数据
          // newWindow.print();   //打印当前窗口
        });
      // console.log('这里进来了');
      // this.axios.post('order/getPrintTest').then((response) => {
      //     console.log(response);
      //     this.rawHtml = response.data.data;
      //     this.printMiandan = true;
      // });
    },
    deliveryConfirm(id) {
      this.axios
        .post("order/goDelivery", {
          order_id: id,
        })
        .then((response) => {
          if (response.data.errno === 0) {
            this.getList();
            this.$message({
              type: "success",
              message: "发货成功!",
            });
          } else {
            this.$message({
              type: "error",
              message: "失败了!",
            });
          }
        });
    },
    printAndDeliveryConfirm() {
      this.axios
        .post("order/goDelivery", {
          order_id: this.order_id,
        })
        .then((response) => {
          if (response.data.errno === 0) {
            this.getList();
            this.printMiandan = false;
            this.dialogFormVisible = false;
            let expressType = this.sfHasValue.expressType;
            if (expressType == 1 || expressType == 2) {
              this.printit1();
            } else if (expressType == 3) {
              this.printit2();
            } else if (expressType == 4) {
              this.printit3();
            }
          } else {
            this.$message({
              type: "error",
              message: "失败了!",
            });
          }
        });
    },
    printOnlyConfirm() {
      this.axios
        .post("order/goPrintOnly", {
          order_id: this.order_id,
        })
        .then((response) => {
          if (response.data.errno === 0) {
            this.getList();
            this.printMiandan = false;
            this.dialogFormVisible = false;
            let expressType = this.sfHasValue.expressType;
            if (expressType == 1 || expressType == 2) {
              this.printit1();
            } else if (expressType == 3) {
              this.printit2();
            } else if (expressType == 4) {
              this.printit3();
            }
          } else {
            this.$message({
              type: "error",
              message: "失败了!",
            });
          }
        });
    },
    printit1() {
      var mywindow = window.open("", "PRINT", "height=1500,width=1000");
      mywindow.document.write("<html><head><title></title>");
      mywindow.document.write("</head><body >");
      mywindow.document.write(document.getElementById("sfbj-block").innerHTML);
      mywindow.document.write("</body></html>");
      mywindow.document.close(); // necessary for IE >= 10
      mywindow.focus(); // necessary for IE >= 10*/
      mywindow.print();
      mywindow.close();
      return true;
    },
    printit2() {
      var mywindow = window.open("", "PRINT", "height=1500,width=1000");
      mywindow.document.write("<html><head><title></title>");
      mywindow.document.write("</head><body >");
      mywindow.document.write(document.getElementById("sfth-block").innerHTML);
      mywindow.document.write("</body></html>");
      mywindow.document.close(); // necessary for IE >= 10
      mywindow.focus(); // necessary for IE >= 10*/
      mywindow.print();
      mywindow.close();
      return true;
    },
    printit3() {
      var mywindow = window.open("", "PRINT", "height=1800,width=1000");
      mywindow.document.write("<html><head><title></title>");
      mywindow.document.write("</head><body >");
      mywindow.document.write(document.getElementById("yto-block").innerHTML);
      mywindow.document.write("</body></html>");
      mywindow.document.close(); // necessary for IE >= 10
      mywindow.focus(); // necessary for IE >= 10*/
      mywindow.print();
      mywindow.close();
      return true;
    },

    priceChangeConfirm() {
      if (
        this.orderInfo.actual_price == "" ||
        this.orderInfo.actual_price == 0
      ) {
        this.$message({
          type: "error",
          message: "总价不能为空!",
        });
        return false;
      }
      this.axios
        .get("order/orderPrice", {
          params: {
            orderId: this.order_id,
            actualPrice: this.orderInfo.actual_price,
            freightPrice: this.orderInfo.freight_price,
            goodsPrice: this.orderInfo.goods_price,
          },
        })
        .then((response) => {
          this.dialogPriceVisible = false;
          this.getList();
        });
    },
    getAutoStatus() {
      this.axios.get("order/getAutoStatus").then((response) => {
        let ele = response.data.data;
        ele == 1 ? (this.autoGoDelivery = true) : (this.autoGoDelivery = false);
      });
    },
    getOrderInfo(sn) {
      this.axios
        .get("order/detail", {
          params: {
            orderId: this.order_id,
          },
        })
        .then((response) => {
          this.orderInfo = response.data.data.orderInfo;
          this.receiver = response.data.data.receiver;
          this.sender = response.data.data.sender;
          console.log(this.sender);
          this.receiveOptions = [];
          this.receiveOptions.push(
            this.receiver.province_id,
            this.receiver.city_id,
            this.receiver.district_id
          );
          this.senderOptions = [];
          this.senderOptions.push(
            this.sender.province_id,
            this.sender.city_id,
            this.sender.district_id
          );
        });
    },
    deliveryMethodChange(val) {
      if (val != 1) {
        this.expressType = 0;
      }
    },
  },
  // created(){
  //     this.getList();
  // },
  mounted() {
    this.isLoading = true; // 页面初始化时设置加载状态
    this.getList();
    this.getAutoStatus();
    this.getDeliveyInfo();
    // this.getSenderInfo();
    this.getAllRegion();
    // 获取订单状态统计
    this.getStatusCounts();
  },
};
</script>

<style scoped>
.filter-input {
  width: 200px !important;
}

.float-right {
  float: right;
}

.d-btn {
  margin-bottom: 10px;
}

.print-footer {
  display: flex;
  justify-content: space-between;
}

.print-footer .f-right {
  display: flex;
  justify-content: flex-end;
}

.btn-beihuo {
  margin-bottom: 22px;
}

.btn-fahuo {
  margin-bottom: 22px;
  margin-left: 30px;
}

.box-check {
  float: left;
}

.filter-box {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.filter-box .box {
  margin-right: 20px;
  margin-bottom: 10px;
}

.demo-form-inline {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-wrap {
  width: 100%;
  border: 1px solid #dfe5ed;
  margin-bottom: 10px;
}

.goods-img {
  width: 40px;
  height: 40px;
}

.list-wrap .header {
  width: 100%;
  height: 40px;
  background-color: rgba(236, 245, 255, 0.51);
  line-height: 40px;
  color: #1f2d3d;
  font-size: 13px;
  padding: 0 10px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header .left {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.header .right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.off-text {
  color: #fff;
  border-radius: 4px;
  background: #594d72;
  line-height: 15px;
  padding: 4px 10px;
  font-size: 12px;
  margin-right: 10px;
}

.status-text {
  color: #f0797f;
  margin-right: 10px;
}

.add-time {
  margin-right: 20px;
}

.pay-time {
  margin-right: 20px;
}

.goods-num {
  margin-right: 20px;
}

.price-wrap {
  float: right;
  margin-right: 20px;
}

.edit-wrap {
  float: right;
  margin-top: 5px;
}

.price-change {
  float: right;
  margin-right: 10px;
  color: #e64242;
}

.content-wrap {
  width: 100%;
  display: flex;
  justify-content: flex-start;
}

.content-wrap .left {
  width: 30%;
  border-right: 1px solid #d1dbe5;
  padding: 20px 10px;
}

.content-wrap .user-wrap {
  width: 16%;
  border-right: 1px solid #d1dbe5;
  display: flex;
  flex-direction: column;
  padding: 20px 10px;
}

.content-wrap .user-wrap .avatar-wrap {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 10px;
}

.content-wrap .user-wrap .avatar-wrap .avatar-img {
  width: 40px;
  height: 40px;
  border-radius: 100px;
  margin-right: 10px;
}

.content-wrap .user-wrap .avatar-wrap .nickname {
  font-size: 14px;
}

.content-wrap .user-wrap .name {
  width: 100%;
  font-size: 14px;
}

.content-wrap .user-wrap .mobile {
  width: 100%;
  font-size: 14px;
}

.content-wrap .main {
  width: 36%;
  border-right: 1px solid #d1dbe5;
  padding: 20px 10px;
}

.content-wrap .right {
  width: 12%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.right .right-detail {
  margin-left: 0;
  margin-top: 6px;
}

.goods-list {
  display: flex;
  justify-content: flex-start;
  border-bottom: 1px solid #f1f1f1;
  padding: 6px 0;
  align-items: center;
}

.goods-list:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.goods-list:first-child {
  padding-top: 0;
}

.dialog-wrap .list-wrap {
  margin-bottom: 10px;
  padding: 10px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.dialog-wrap .goods-list {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  /*margin-bottom:20px;*/
  /*border-bottom:1px solid #d1dbe5;*/
}

.dialog-wrap .main {
  padding: 10px;
  margin-bottom: 20px;
  border: 1px solid #d1dbe5;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.dialog-wrap .main div {
  font-size: 14px;
}

.goods-name {
  color: #5e7382;
  font-size: 14px;
  margin: 0 20px 0 10px;
  width: 180px;
}

.goods-aka {
  color: #999;
  font-size: 12px;
  margin: 2px 20px 0 10px;
  width: 180px;
}

.goods-spec {
  color: #0066cc;
  font-size: 14px;
  margin-right: 30px;
  width: 60px;
}

.goods-number {
  color: #ff3456;
  font-size: 14px;
  margin-right: 20px;
}

.goods-number label {
  color: #666;
}

.goods-price {
  color: #333;
  font-size: 14px;
  margin-right: 20px;
}

.m1 {
  display: flex;
  justify-content: flex-start;
}

.dialog-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /*background: #f6fdff;*/
  border-bottom: 1px solid #f1f1f1;
}

.dialog-main .l {
  display: flex;
  justify-content: flex-start;
}

.other {
  /*background: #f1f1f1;*/
  border-top: none;
}

.dialog-main .title {
  /*background: #ecf0ff;*/
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20px;
}

.other .title {
  background: #eaeaea;
}

.user-name {
  color: #000000;
  font-size: 14px;
  margin-right: 10px;
  line-height: 20px;
}

.user-mobile {
  color: #000000;
  font-size: 14px;
  line-height: 20px;
  margin-right: 20px;
}

.user-address {
  color: #333;
  font-size: 13px;
  line-height: 20px;
  margin-top: 10px;
  display: flex;
  justify-content: flex-start;
}

.user-post {
  color: #333;
  font-size: 14px;
  line-height: 20px;
  margin-top: 4px;
  background-color: #fbf7c5;
  padding: 10px;
}

.detail {
  padding: 10px 0;
}

.receive-detail {
  padding: 10px 0;
}

.user-post-t {
  color: #333;
  font-size: 14px;
  line-height: 20px;
  margin-top: 4px;
  background-color: #fbf7c5;
  padding: 10px;
  margin: 10px 0;
}

.user-admin-t {
  color: #333;
  font-size: 14px;
  line-height: 20px;
  margin-top: 4px;
  background-color: #fde7e7;
  padding: 10px;
  margin: 10px 0;
}

.admin-memo {
  margin-top: 10px;
}

.el-input {
  width: 300px;
}

.address-input {
  margin-left: 10px;
}

.senderInput {
  width: 200px !important;
}

.senderInput .el-input__inner {
  width: 100px;
}

.senderAddressInput {
  width: 530px !important;
  margin-bottom: 10px;
}

.el-checkbox {
  margin-bottom: 22px;
  margin-right: 20px;
}

.express-info {
  padding: 10px;
  color: #ff3456;
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 10px;
  background: #f0f0f0;
}

.el-form-item {
  margin-bottom: 10px;
}

/*.express-dialog{*/
/*display: flex;*/
/*!*justify-content: center;*!*/
/*}*/

/* 新增现代化样式 */
.content-page {
  padding: 24px;
  background-color: #f9fafb;
  min-height: 100vh;
}

/* 确保基础样式 */
.bg-gray-50 {
  background-color: #f9fafb !important;
}

.bg-white {
  background-color: white !important;
}

.text-gray-500 {
  color: #6b7280 !important;
}

.text-gray-700 {
  color: #374151 !important;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.gap-6 {
  gap: 1.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.p-5 {
  padding: 1.25rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.text-sm {
  font-size: 0.875rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.font-bold {
  font-weight: 700;
}

.mt-1 {
  margin-top: 0.25rem;
}

.w-12 {
  width: 3rem;
}

.h-12 {
  height: 3rem;
}

.rounded-full {
  border-radius: 9999px;
}

.w-8 {
  width: 2rem;
}

.h-8 {
  height: 2rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.w-16 {
  width: 4rem;
}

.h-16 {
  height: 4rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.object-cover {
  object-fit: cover;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.font-medium {
  font-weight: 500;
}

.text-xs {
  font-size: 0.75rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.whitespace-nowrap {
  white-space: nowrap;
}

/* 统计卡片样式 */
.bg-white {
  background-color: white;
}

.rounded {
  border-radius: 8px;
}

.shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* 客服备注样式 */
.service-memo-content {
  max-height: 500px;
  overflow-y: auto;
}

.order-basic-info h4,
.flag-color-section h4,
.memo-content-section h4 {
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}



.update-info {
  font-size: 12px;
  color: #9ca3af;
  border-top: 1px solid #f3f4f6;
  padding-top: 12px;
  margin-top: 16px;
}



/* 标签样式 */
.tab-button {
  transition: all 0.3s ease;
  position: relative;
}

.tab-button:hover {
  transform: translateY(-1px);
  background-color: #f8fafc;
}

.tab-active {
  color: #4f46e5;
  border-bottom: 2px solid #4f46e5;
  background-color: #f8fafc;
  transform: translateY(-1px);
}

/* 内容过渡动画 */
.fade-slide-enter-active, .fade-slide-leave-active {
  transition: all 0.4s ease;
}

.fade-slide-enter {
  opacity: 0;
  transform: translateY(20px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 加载状态样式 */
.loading-overlay {
  position: relative;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 8px;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #6b7280;
  font-size: 0.875rem;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  color: #6b7280;
}

/* 分页容器动画 */
.pagination-container {
  animation: fadeInUp 0.5s ease 0.2s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 统计卡片动画 */
.stats-card {
  animation: fadeInUp 0.6s ease both;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15) !important;
}

.stats-card:nth-child(1) { animation-delay: 0.1s; }
.stats-card:nth-child(2) { animation-delay: 0.2s; }
.stats-card:nth-child(3) { animation-delay: 0.3s; }
.stats-card:nth-child(4) { animation-delay: 0.4s; }

/* 表格样式 */
.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.table-container table {
  border-collapse: collapse;
}

.table-container th {
  background-color: #f9fafb;
  font-weight: 500;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.table-container td {
  border-bottom: 1px solid #f3f4f6;
  vertical-align: top;
}

.table-container tr:hover {
  background-color: #f9fafb;
}

/* 按钮样式 */
.rounded-button {
  border-radius: 6px;
}

/* 主色调 */
.text-primary {
  color: #4f46e5;
}

.bg-primary {
  background-color: #4f46e5;
}

.bg-primary\/90:hover {
  background-color: rgba(79, 70, 229, 0.9);
}

/* 分页样式 */
.modern-pagination .el-pagination {
  font-weight: normal;
}

.modern-pagination .el-pager li {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin: 0 2px;
}

.modern-pagination .el-pager li.active {
  background-color: #4f46e5;
  border-color: #4f46e5;
  color: white;
}

.modern-pagination .btn-prev,
.modern-pagination .btn-next {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}
</style>
