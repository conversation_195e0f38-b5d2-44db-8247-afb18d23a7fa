const util = require('../../../utils/util.js');
const api = require('../../../config/api.js');

Page({
  data: {
    activeTab: 'unused',
    coupons: {
      unused: [],
      used: [],
      expired: []
    },
    loading: false
  },

  onLoad() {
    this.loadMyCoupons();
  },

  onShow() {
    this.loadMyCoupons();
  },

  // 切换标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
  },

  // 加载我的优惠券
  async loadMyCoupons() {
    this.setData({ loading: true });
    
    try {
      const res = await util.request(api.CouponMy);
      
      if (res.errno === 0) {
        this.setData({
          coupons: res.data
        });
      } else {
        wx.showToast({
          title: res.errmsg || '获取优惠券失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载优惠券失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 获取优惠券类型标签
  getTypeLabel(type) {
    const labels = {
      'newuser': '新人券',
      'full_reduction': '满减券'
    };
    return labels[type] || type;
  },

  // 获取优惠文本
  getDiscountText(coupon) {
    if (coupon.discount_type === 'fixed') {
      return `¥${coupon.discount_value}`;
    } else {
      return `${coupon.discount_value}折`;
    }
  },

  // 获取使用条件文本
  getConditionText(coupon) {
    if (coupon.min_amount > 0) {
      return `满¥${coupon.min_amount}可用`;
    } else {
      return '无门槛';
    }
  },

  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return util.formatTime(date);
  },

  // 立即使用优惠券
  useCoupon(e) {
    const coupon = e.currentTarget.dataset.coupon;
    
    // 跳转到商品列表或首页
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 查看优惠券详情 - 已移除弹窗功能
  viewCouponDetail(e) {
    // 功能已移除，不再显示弹窗
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadMyCoupons().then(() => {
      wx.stopPullDownRefresh();
    });
  }
});
