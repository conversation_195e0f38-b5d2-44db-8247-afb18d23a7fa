<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <!-- 面包屑导航 -->
    <div class="flex items-center text-sm text-gray-500 mb-6">
      <a href="#" class="hover:text-indigo-600">首页</a>
      <span class="mx-2">/</span>
      <a href="#" class="hover:text-indigo-600">个人推广</a>
      <span class="mx-2">/</span>
      <span class="text-gray-700">推广员列表</span>
    </div>

    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">推广员管理</h1>
      <p class="mt-2 text-gray-600">管理个人推广员信息和统计数据</p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">总推广员</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.total_promoters || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">活跃推广员</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.active_promoters || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">总浏览次数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.total_views || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">总成交次数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.total_orders || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">总佣金</p>
            <p class="text-2xl font-semibold text-gray-900">¥{{ stats.total_commission || 0 }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
      <div class="flex flex-wrap items-center gap-4">
        <div class="flex-1 min-w-0">
          <label class="block text-sm font-medium text-gray-700 mb-2">搜索推广员</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
            <input
              v-model="searchForm.keyword"
              type="text"
              placeholder="输入推广员昵称或手机号..."
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>
        </div>

        <div class="w-40">
          <label class="block text-sm font-medium text-gray-700 mb-2">等级筛选</label>
          <select
            v-model="searchForm.level"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          >
            <option value="">全部等级</option>
            <option value="1">新手</option>
            <option value="2">优秀</option>
            <option value="3">金牌</option>
            <option value="4">钻石</option>
          </select>
        </div>

        <div class="w-32">
          <label class="block text-sm font-medium text-gray-700 mb-2">状态筛选</label>
          <select
            v-model="searchForm.status"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          >
            <option value="">全部状态</option>
            <option value="1">正常</option>
            <option value="0">禁用</option>
          </select>
        </div>

        <div class="flex items-end gap-2">
          <button
            @click="searchPromoters"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            搜索
          </button>
          <button
            @click="resetSearch"
            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            重置
          </button>
        </div>
      </div>
    </div>

    <!-- 推广员列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <!-- 表格头部 -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-medium text-gray-900">推广员列表</h3>
            <p class="mt-1 text-sm text-gray-500">
              共 {{ promoters.length }} 条推广员记录
            </p>
          </div>
          <div class="flex items-center space-x-3">
            <button class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              导出
            </button>
            <button class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              添加推广员
            </button>
          </div>
        </div>
      </div>

      <!-- 表格内容 -->
      <div v-if="promoters.length === 0" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无推广员</h3>
        <p class="mt-1 text-sm text-gray-500">开始添加推广员来管理您的推广团队。</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" />
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                推广员信息
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                等级状态
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                推广数据
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                收益统计
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                注册时间
              </th>
              <th scope="col" class="relative px-6 py-3">
                <span class="sr-only">操作</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="item in promoters" :key="item.id" class="hover:bg-gray-50 transition-colors duration-200">

              <!-- 复选框 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" />
              </td>

              <!-- 推广员信息 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-12 w-12">
                    <img
                      :src="getAvatarUrl(item.avatar)"
                      alt="推广员头像"
                      class="h-12 w-12 rounded-full object-cover ring-2 ring-white"
                      @error="handleImageError"
                    />
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">
                      {{ item.nickname }}
                    </div>
                    <div class="text-sm text-gray-500">
                      ID: {{ item.id }}
                    </div>
                    <div class="text-xs text-gray-400 mt-1">
                      {{ item.mobile || '未绑定手机' }}
                    </div>
                  </div>
                </div>
              </td>

              <!-- 等级状态 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex flex-col space-y-2">
                  <span :class="getLevelBadgeClass(item.level)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                    {{ getLevelText(item.level) }}
                  </span>
                  <span :class="getStatusBadgeClass(item.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                    {{ item.status === 1 ? '正常' : '禁用' }}
                  </span>
                </div>
              </td>

              <!-- 推广数据 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="space-y-1">
                  <div class="flex items-center text-sm">
                    <span class="text-gray-500 w-12">浏览:</span>
                    <span class="font-medium text-gray-900">{{ item.total_views || 0 }}</span>
                  </div>
                  <div class="flex items-center text-sm">
                    <span class="text-gray-500 w-12">分享:</span>
                    <span class="font-medium text-gray-900">{{ item.total_shares || 0 }}</span>
                  </div>
                  <div class="flex items-center text-sm">
                    <span class="text-gray-500 w-12">订单:</span>
                    <span class="font-medium text-gray-900">{{ item.total_orders || 0 }}</span>
                  </div>
                </div>
              </td>

              <!-- 收益统计 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="space-y-1">
                  <div class="flex items-center text-sm">
                    <span class="text-gray-500 w-16">总积分:</span>
                    <span class="font-medium text-green-600">{{ item.total_points || 0 }}</span>
                  </div>
                  <div class="flex items-center text-sm">
                    <span class="text-gray-500 w-16">本月:</span>
                    <span class="font-medium text-blue-600">{{ item.month_points || 0 }}</span>
                  </div>
                  <div class="flex items-center text-sm">
                    <span class="text-gray-500 w-16">成交:</span>
                    <span class="font-medium text-gray-900">{{ item.month_orders || 0 }}</span>
                  </div>
                </div>
              </td>

              <!-- 注册时间 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">
                  {{ formatDate(item.created_at) }}
                </div>
                <div class="text-xs text-gray-500 mt-1">
                  {{ getTimeAgo(item.created_at) }}
                </div>
              </td>

              <!-- 操作 -->
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <button
                    @click="viewDetail(item)"
                    class="text-indigo-600 hover:text-indigo-900 transition-colors duration-200"
                  >
                    查看详情
                  </button>
                  <span class="text-gray-300">|</span>
                  <button
                    @click="toggleStatus(item)"
                    :class="item.status === 1 ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'"
                    class="transition-colors duration-200"
                  >
                    {{ item.status === 1 ? '禁用' : '启用' }}
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="bg-white px-6 py-4 border-t border-gray-200 rounded-b-lg">
      <div class="flex items-center justify-between">
        <div class="flex items-center text-sm text-gray-700">
          <span>显示</span>
          <select
            v-model="pageSize"
            @change="handleSizeChange($event.target.value)"
            class="mx-2 border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
            <option value="100">100</option>
          </select>
          <span>条，共 {{ total }} 条记录</span>
        </div>

        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
          <button
            @click="handleCurrentChange(currentPage - 1)"
            :disabled="currentPage <= 1"
            class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span class="sr-only">上一页</span>
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
            </svg>
          </button>

          <template v-for="page in getPageNumbers()">
            <button
              v-if="typeof page === 'number'"
              :key="'btn-' + page"
              @click="handleCurrentChange(page)"
              :class="[
                'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                page === currentPage
                  ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                  : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
              ]"
            >
              {{ page }}
            </button>
            <span
              v-else
              :key="'dots-' + page"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
            >
              ...
            </span>
          </template>

          <button
            @click="handleCurrentChange(currentPage + 1)"
            :disabled="currentPage >= Math.ceil(total / pageSize)"
            class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span class="sr-only">下一页</span>
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </button>
        </nav>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PromoterListPage',
  data() {
    return {
      loading: false,
      promoters: [],
      stats: {
        total_promoters: 0,
        active_promoters: 0,
        total_views: 0,
        total_orders: 0,
        total_commission: '0.00'
      },
      searchForm: {
        keyword: '',
        level: '',
        status: ''
      },
      currentPage: 1,
      pageSize: 20,
      total: 0
    }
  },
  mounted() {
    this.loadStats();
    this.loadPromoters();
  },
  methods: {
    async loadStats() {
      try {
        const response = await this.$http.get('/share-records/promostats');
        if (response.data.errno === 0) {
          this.stats = response.data.data;
        }
      } catch (error) {
        console.error('加载推广员统计数据失败:', error);
      }
    },
    async loadPromoters() {
      this.loading = true;
      try {
        const params = {
          page: this.currentPage,
          limit: this.pageSize,
          keyword: this.searchForm.keyword,
          level: this.searchForm.level,
          status: this.searchForm.status
        };

        const response = await this.$http.get('/share-records/promoters', { params });
        console.log('推广员列表API响应:', response.data);

        if (response.data.errno === 0) {
          this.promoters = response.data.data.data || [];
          this.total = response.data.data.total || 0;
        } else {
          this.$message.error(response.data.errmsg || '加载数据失败');
        }
      } catch (error) {
        console.error('加载推广员列表失败:', error);
        this.$message.error('加载数据失败');
      } finally {
        this.loading = false;
      }
    },
    searchPromoters() {
      this.currentPage = 1;
      this.loadPromoters();
    },
    resetSearch() {
      this.searchForm = {
        keyword: '',
        level: '',
        status: ''
      };
      this.currentPage = 1;
      this.loadPromoters();
    },
    handleSizeChange(val) {
      this.pageSize = parseInt(val);
      this.currentPage = 1;
      this.loadPromoters();
    },
    handleCurrentChange(val) {
      if (val >= 1 && val <= Math.ceil(this.total / this.pageSize)) {
        this.currentPage = val;
        this.loadPromoters();
      }
    },
    viewDetail(promoter) {
      console.log('查看推广员详情:', promoter);
    },
    toggleStatus(promoter) {
      console.log('切换推广员状态:', promoter);
    },
    getLevelText(level) {
      const levelMap = {
        1: '新手',
        2: '优秀',
        3: '金牌',
        4: '钻石'
      };
      return levelMap[level] || '未知';
    },
    getLevelType(level) {
      const typeMap = {
        1: '',
        2: 'warning',
        3: 'success',
        4: 'danger'
      };
      return typeMap[level] || '';
    },

    // 获取等级样式
    getLevelStyle(level) {
      const styleMap = {
        1: 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #f3f4f6; color: #374151;',
        2: 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #fef3c7; color: #92400e;',
        3: 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #d1fae5; color: #065f46;',
        4: 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #fecaca; color: #991b1b;'
      };
      return styleMap[level] || styleMap[1];
    },

    // 获取状态样式
    getStatusStyle(status) {
      return status === 1
        ? 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #d1fae5; color: #065f46;'
        : 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #fecaca; color: #991b1b;';
    },

    // 处理头像URL
    getAvatarUrl(avatar) {
      console.log('推广员头像URL:', avatar);

      if (!avatar || avatar === undefined || avatar === null || avatar === '') {
        console.log('头像为空，使用默认头像');
        return 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';
      }

      // 如果是完整的URL，直接返回
      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
        return avatar;
      }

      // 如果是相对路径，补充域名
      if (avatar.startsWith('/')) {
        return `https://ht.rxkjsdj.com${avatar}`;
      }

      // 其他情况使用默认头像
      return 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';
    },

    // 图片加载失败处理
    handleImageError(event) {
      console.log('推广员头像加载失败，使用默认头像');
      event.target.src = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';
    },

    // 获取等级徽章样式类
    getLevelBadgeClass(level) {
      const classMap = {
        1: 'bg-gray-100 text-gray-800',
        2: 'bg-yellow-100 text-yellow-800',
        3: 'bg-green-100 text-green-800',
        4: 'bg-purple-100 text-purple-800'
      };
      return classMap[level] || classMap[1];
    },

    // 获取状态徽章样式类
    getStatusBadgeClass(status) {
      return status === 1
        ? 'bg-green-100 text-green-800'
        : 'bg-red-100 text-red-800';
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '未知';
      try {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
      } catch (error) {
        return '未知';
      }
    },

    // 获取时间距离现在的描述
    getTimeAgo(dateString) {
      if (!dateString) return '';
      try {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) return '1天前';
        if (diffDays < 30) return `${diffDays}天前`;
        if (diffDays < 365) return `${Math.floor(diffDays / 30)}个月前`;
        return `${Math.floor(diffDays / 365)}年前`;
      } catch (error) {
        return '';
      }
    },

    // 获取分页页码数组
    getPageNumbers() {
      const totalPages = Math.ceil(this.total / this.pageSize);
      const current = this.currentPage;
      const pages = [];

      if (totalPages <= 7) {
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        if (current <= 4) {
          for (let i = 1; i <= 5; i++) {
            pages.push(i);
          }
          pages.push('...' + Math.random()); // 添加随机数确保唯一key
          pages.push(totalPages);
        } else if (current >= totalPages - 3) {
          pages.push(1);
          pages.push('...' + Math.random()); // 添加随机数确保唯一key
          for (let i = totalPages - 4; i <= totalPages; i++) {
            pages.push(i);
          }
        } else {
          pages.push(1);
          pages.push('...' + Math.random()); // 添加随机数确保唯一key
          for (let i = current - 1; i <= current + 1; i++) {
            pages.push(i);
          }
          pages.push('...' + Math.random()); // 添加随机数确保唯一key
          pages.push(totalPages);
        }
      }

      return pages;
    }
  }
}
</script>

<style scoped>
/* 使用 Tailwind CSS，移除大部分自定义样式 */
</style>
