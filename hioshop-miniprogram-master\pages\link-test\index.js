Page({
  data: {
    testLinks: [
      {
        title: '基础跳转',
        desc: '直接跳转到订单兑换页面',
        url: '/pages/order-exchange/index',
        type: 'navigate'
      },
      {
        title: '带订单号跳转',
        desc: '跳转并传递订单号参数',
        url: '/pages/order-exchange/index?orderNumber=TEST123456',
        type: 'navigate'
      },
      {
        title: '带多个参数跳转',
        desc: '跳转并传递多个参数',
        url: '/pages/order-exchange/index?orderNumber=TEST123456&source=test&utm=linktest',
        type: 'navigate'
      },
      {
        title: 'URL Scheme 测试',
        desc: '生成并复制 URL Scheme',
        url: 'weixin://dl/business/?appid=wx919ca2ec612e6ecb&path=pages/order-exchange/index&env_version=release',
        type: 'scheme'
      },
      {
        title: '重定向跳转',
        desc: '使用 redirectTo 跳转',
        url: '/pages/order-exchange/index',
        type: 'redirect'
      },
      {
        title: 'Tab 切换跳转',
        desc: '如果是 Tab 页面使用 switchTab',
        url: '/pages/order-exchange/index',
        type: 'switchTab'
      }
    ]
  },

  onLoad: function(options) {
    console.log('链接测试页面加载完成');
  },

  // 处理链接点击
  handleLinkTap: function(e) {
    const index = e.currentTarget.dataset.index;
    const link = this.data.testLinks[index];
    
    console.log('点击链接:', link);
    
    wx.showLoading({
      title: '跳转中...'
    });

    switch(link.type) {
      case 'navigate':
        this.navigateToPage(link.url);
        break;
      case 'redirect':
        this.redirectToPage(link.url);
        break;
      case 'switchTab':
        this.switchToTab(link.url);
        break;
      case 'scheme':
        this.copyScheme(link.url);
        break;
      default:
        this.navigateToPage(link.url);
    }
  },

  // 普通跳转
  navigateToPage: function(url) {
    wx.navigateTo({
      url: url,
      success: function() {
        wx.hideLoading();
        console.log('跳转成功:', url);
      },
      fail: function(err) {
        wx.hideLoading();
        console.error('跳转失败:', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 重定向跳转
  redirectToPage: function(url) {
    wx.redirectTo({
      url: url,
      success: function() {
        wx.hideLoading();
        console.log('重定向成功:', url);
      },
      fail: function(err) {
        wx.hideLoading();
        console.error('重定向失败:', err);
        wx.showToast({
          title: '重定向失败',
          icon: 'none'
        });
      }
    });
  },

  // Tab 切换
  switchToTab: function(url) {
    wx.switchTab({
      url: url,
      success: function() {
        wx.hideLoading();
        console.log('Tab切换成功:', url);
      },
      fail: function(err) {
        wx.hideLoading();
        console.error('Tab切换失败:', err);
        wx.showToast({
          title: 'Tab切换失败',
          icon: 'none'
        });
      }
    });
  },

  // 复制 URL Scheme
  copyScheme: function(scheme) {
    wx.hideLoading();
    wx.setClipboardData({
      data: scheme,
      success: function() {
        wx.showToast({
          title: 'URL Scheme已复制',
          icon: 'success'
        });
        console.log('URL Scheme已复制:', scheme);
      },
      fail: function(err) {
        console.error('复制失败:', err);
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 生成动态链接
  generateDynamicLink: function() {
    const timestamp = Date.now();
    const orderNumber = 'TEST' + timestamp;
    const url = `/pages/order-exchange/index?orderNumber=${orderNumber}&source=dynamic&timestamp=${timestamp}`;
    
    wx.showModal({
      title: '动态链接生成',
      content: `生成的链接：${url}`,
      confirmText: '立即跳转',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.navigateToPage(url);
        }
      }
    });
  },

  // 测试外部链接（模拟从外部进入）
  testExternalEntry: function() {
    // 模拟从外部链接进入的场景
    const externalParams = {
      orderNumber: 'EXTERNAL123',
      source: 'external',
      utm: 'test_campaign'
    };
    
    let url = '/pages/order-exchange/index?';
    const params = Object.keys(externalParams).map(key => 
      `${key}=${encodeURIComponent(externalParams[key])}`
    ).join('&');
    url += params;
    
    wx.showModal({
      title: '模拟外部进入',
      content: `模拟从外部链接进入小程序\n参数：${JSON.stringify(externalParams, null, 2)}`,
      confirmText: '开始测试',
      success: (res) => {
        if (res.confirm) {
          this.navigateToPage(url);
        }
      }
    });
  },

  // 测试后端跳转页面接口
  testBackendRedirect: function() {
    wx.showLoading({
      title: '测试中...'
    });

    wx.request({
      url: 'https://**************:8360/api/redirect/test',
      method: 'GET',
      success: (res) => {
        wx.hideLoading();
        console.log('后端接口返回:', res);

        if (res.statusCode === 200) {
          wx.showModal({
            title: '测试成功',
            content: '后端跳转页面接口正常工作！\n\n您可以复制链接到微信中测试跳转功能。',
            confirmText: '复制链接',
            cancelText: '知道了',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.setClipboardData({
                  data: 'https://**************:8360/api/redirect/test',
                  success: () => {
                    wx.showToast({
                      title: '链接已复制',
                      icon: 'success'
                    });
                  }
                });
              }
            }
          });
        } else {
          wx.showToast({
            title: '接口返回异常',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('后端接口请求失败:', err);
        wx.showModal({
          title: '测试失败',
          content: `请求失败: ${err.errMsg}\n\n请检查：\n1. 后端服务是否启动\n2. 网络连接是否正常\n3. 接口地址是否正确`,
          showCancel: false
        });
      }
    });
  },

  // 返回首页
  goHome: function() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
});
