{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\goods.js"], "names": ["Base", "require", "moment", "module", "exports", "indexAction", "model", "goodsList", "select", "success", "detailAction", "goodsId", "get", "userId", "getLoginUserId", "info", "where", "id", "is_delete", "find", "think", "isEmpty", "fail", "gallery", "goods_id", "order", "limit", "addFootprint", "productList", "getProductList", "goodsNumber", "item", "goods_number", "specificationList", "getSpecificationList", "sell_volume", "orderModel", "sellVolumeResult", "sum", "error", "console", "log", "distributionConfig", "is_distributed", "commission_rate", "personal_rate", "level1_rate", "level2_rate", "team_leader_rate", "goodsShareAction", "field", "listAction", "keyword", "sort", "sales", "whereMap", "is_on_sale", "name", "add", "user_id", "add_time", "parseInt", "Date", "getTime", "orderMap", "retail_price", "sort_order", "goodsData", "countAction", "goodsCount", "count"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;AACAE,OAAOC,OAAP,GAAiB,cAAcJ,IAAd,CAAmB;AAC1BK,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAMC,QAAQ,MAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMC,YAAY,MAAMD,MAAME,MAAN,EAAxB;AACA,mBAAO,MAAKC,OAAL,CAAaF,SAAb,CAAP;AAHgB;AAInB;AACD;;;;AAIMG,gBAAN,GAAqB;AAAA;;AAAA;AACjB,kBAAMC,UAAU,OAAKC,GAAL,CAAS,IAAT,CAAhB;AACN,kBAAMC,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,kBAAMR,QAAQ,OAAKA,KAAL,CAAW,OAAX,CAAd;AACA,gBAAIS,OAAO,MAAMT,MAAMU,KAAN,CAAY;AACzBC,oBAAIN,OADqB;AAElCO,2BAAU;AAFwB,aAAZ,EAGdC,IAHc,EAAjB;AAIN,gBAAGC,MAAMC,OAAN,CAAcN,IAAd,CAAH,EAAuB;AACtB,uBAAO,OAAKO,IAAL,CAAU,YAAV,CAAP;AACA;AACK,kBAAMC,UAAU,MAAM,OAAKjB,KAAL,CAAW,eAAX,EAA4BU,KAA5B,CAAkC;AACpDQ,0BAAUb,OAD0C;AAEpDO,2BAAW;AAFyC,aAAlC,EAGnBO,KAHmB,CAGb,YAHa,EAGCC,KAHD,CAGO,CAHP,EAGUlB,MAHV,EAAtB;AAIA,kBAAM,OAAKF,KAAL,CAAW,WAAX,EAAwBqB,YAAxB,CAAqCd,MAArC,EAA6CF,OAA7C,CAAN;AACA,gBAAIiB,cAAc,MAAMtB,MAAMuB,cAAN,CAAqBlB,OAArB,CAAxB;AACA,gBAAImB,cAAc,CAAlB;AACA,iBAAK,MAAMC,IAAX,IAAmBH,WAAnB,EAAgC;AAC5B,oBAAIG,KAAKC,YAAL,GAAoB,CAAxB,EAA2B;AACvBF,kCAAcA,cAAcC,KAAKC,YAAjC;AACH;AACJ;AACD,gBAAIC,oBAAoB,MAAM3B,MAAM4B,oBAAN,CAA2BvB,OAA3B,CAA9B;AACAI,iBAAKiB,YAAL,GAAoBF,WAApB;;AAEA;AACA;AACA,gBAAI,CAACf,KAAKoB,WAAV,EAAuB;AACnB,oBAAI;AACA,0BAAMC,aAAa,OAAK9B,KAAL,CAAW,aAAX,CAAnB;AACA,0BAAM+B,mBAAmB,MAAMD,WAC1BpB,KAD0B,CACpB;AACHQ,kCAAUb;AADP,qBADoB,EAI1B2B,GAJ0B,CAItB,QAJsB,CAA/B;AAKAvB,yBAAKoB,WAAL,GAAmBE,oBAAoB,CAAvC;AACH,iBARD,CAQE,OAAOE,KAAP,EAAc;AACZC,4BAAQC,GAAR,CAAY,eAAZ,EAA6BF,KAA7B;AACAxB,yBAAKoB,WAAL,GAAmB,CAAnB;AACH;AACJ;;AAED;AACA,gBAAI;AACA,sBAAMO,qBAAqB,MAAM,OAAKpC,KAAL,CAAW,oBAAX,EAC5BU,KAD4B,CACtB,EAAEQ,UAAUb,OAAZ,EADsB,EAE5BQ,IAF4B,EAAjC;;AAIA,oBAAIuB,kBAAJ,EAAwB;AACpB3B,yBAAK4B,cAAL,GAAsBD,mBAAmBC,cAAzC;AACA5B,yBAAK6B,eAAL,GAAuBF,mBAAmBE,eAAnB,IAAsC,CAA7D;AACA7B,yBAAK8B,aAAL,GAAqBH,mBAAmBG,aAAnB,IAAoC,CAAzD;AACA9B,yBAAK+B,WAAL,GAAmBJ,mBAAmBI,WAAnB,IAAkC,CAArD;AACA/B,yBAAKgC,WAAL,GAAmBL,mBAAmBK,WAAnB,IAAkC,CAArD;AACAhC,yBAAKiC,gBAAL,GAAwBN,mBAAmBM,gBAAnB,IAAuC,CAA/D;AACH,iBAPD,MAOO;AACHjC,yBAAK4B,cAAL,GAAsB,IAAtB;AACA5B,yBAAK6B,eAAL,GAAuB,CAAvB;AACA7B,yBAAK8B,aAAL,GAAqB,CAArB;AACA9B,yBAAK+B,WAAL,GAAmB,CAAnB;AACA/B,yBAAKgC,WAAL,GAAmB,CAAnB;AACAhC,yBAAKiC,gBAAL,GAAwB,CAAxB;AACH;AACJ,aApBD,CAoBE,OAAOT,KAAP,EAAc;AACZC,wBAAQC,GAAR,CAAY,iBAAZ,EAA+BF,KAA/B;AACAxB,qBAAK4B,cAAL,GAAsB,IAAtB;AACA5B,qBAAK6B,eAAL,GAAuB,CAAvB;AACA7B,qBAAK8B,aAAL,GAAqB,CAArB;AACA9B,qBAAK+B,WAAL,GAAmB,CAAnB;AACA/B,qBAAKgC,WAAL,GAAmB,CAAnB;AACAhC,qBAAKiC,gBAAL,GAAwB,CAAxB;AACH;;AAED,mBAAO,OAAKvC,OAAL,CAAa;AAChBM,sBAAMA,IADU;AAEhBQ,yBAASA,OAFO;AAGhBU,mCAAmBA,iBAHH;AAIhBL,6BAAaA;AAJG,aAAb,CAAP;AA1EiB;AAgFpB;AACKqB,oBAAN,GAAyB;AAAA;;AAAA;AACrB,kBAAMtC,UAAU,OAAKC,GAAL,CAAS,IAAT,CAAhB;AACA,kBAAMG,OAAO,MAAM,OAAKT,KAAL,CAAW,OAAX,EAAoBU,KAApB,CAA0B;AACzCC,oBAAIN;AADqC,aAA1B,EAEhBuC,KAFgB,CAEV,mBAFU,EAEW/B,IAFX,EAAnB;AAGA,mBAAO,OAAKV,OAAL,CAAaM,IAAb,CAAP;AALqB;AAMxB;AACD;;;;AAIMoC,cAAN,GAAmB;AAAA;;AAAA;AACrB,kBAAMtC,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,kBAAMsC,UAAU,OAAKxC,GAAL,CAAS,SAAT,CAAhB;AACA,kBAAMyC,OAAO,OAAKzC,GAAL,CAAS,MAAT,CAAb;AACA,kBAAMa,QAAQ,OAAKb,GAAL,CAAS,OAAT,CAAd;AACA,kBAAM0C,QAAQ,OAAK1C,GAAL,CAAS,OAAT,CAAd;AACA,kBAAMN,QAAQ,OAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMiD,WAAW;AACbC,4BAAY,CADC;AAEbtC,2BAAW;AAFE,aAAjB;AAIA,gBAAI,CAACE,MAAMC,OAAN,CAAc+B,OAAd,CAAL,EAA6B;AACzBG,yBAASE,IAAT,GAAgB,CAAC,MAAD,EAAU,IAAGL,OAAQ,GAArB,CAAhB;AACA;AACA,sBAAM,OAAK9C,KAAL,CAAW,gBAAX,EAA6BoD,GAA7B,CAAiC;AACnCN,6BAASA,OAD0B;AAEnCO,6BAAS9C,MAF0B;AAGnC+C,8BAAUC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC;AAHyB,iBAAjC,CAAN;AAKA;AACH;AACD;AACA,gBAAIC,WAAW,EAAf;AACA,gBAAIX,SAAS,OAAb,EAAsB;AAClB;AACAW,2BAAW;AACPC,kCAAcxC;AADP,iBAAX;AAGH,aALD,MAKO,IAAI4B,SAAS,OAAb,EAAsB;AACzB;AACAW,2BAAW;AACP7B,iCAAamB;AADN,iBAAX;AAGH,aALM,MAKA;AACH;AACAU,2BAAW;AACPE,gCAAY;AADL,iBAAX;AAGH;AACD,kBAAMC,YAAY,MAAM7D,MAAMU,KAAN,CAAYuC,QAAZ,EAAsB9B,KAAtB,CAA4BuC,QAA5B,EAAsCxD,MAAtC,EAAxB;AACA,mBAAO,OAAKC,OAAL,CAAa0D,SAAb,CAAP;AAxCe;AAyClB;AACD;;;;AAIMC,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAMC,aAAa,MAAM,OAAK/D,KAAL,CAAW,OAAX,EAAoBU,KAApB,CAA0B;AAC/CE,2BAAW,CADoC;AAE/CsC,4BAAY;AAFmC,aAA1B,EAGtBc,KAHsB,CAGhB,IAHgB,CAAzB;AAIA,mBAAO,OAAK7D,OAAL,CAAa;AAChB4D,4BAAYA;AADI,aAAb,CAAP;AALgB;AAQnB;AA5J+B,CAApC", "file": "..\\..\\..\\src\\api\\controller\\goods.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\nmodule.exports = class extends Base {\n    async indexAction() {\n        const model = this.model('goods');\n        const goodsList = await model.select();\n        return this.success(goodsList);\n    }\n    /**\n     * 商品详情页数据\n     * @returns {Promise.<Promise|PreventPromise|void>}\n     */\n    async detailAction() {\n        const goodsId = this.get('id');\n\t\tconst userId = await this.getLoginUserId();\n        const model = this.model('goods');\n        let info = await model.where({\n            id: goodsId,\n\t\t\tis_delete:0\n        }).find();\n\t\tif(think.isEmpty(info)){\n\t\t\treturn this.fail('该商品不存在或已下架');\n\t\t}\n        const gallery = await this.model('goods_gallery').where({\n            goods_id: goodsId,\n            is_delete: 0,\n        }).order('sort_order').limit(6).select();\n        await this.model('footprint').addFootprint(userId, goodsId);\n        let productList = await model.getProductList(goodsId);\n        let goodsNumber = 0;\n        for (const item of productList) {\n            if (item.goods_number > 0) {\n                goodsNumber = goodsNumber + item.goods_number;\n            }\n        }\n        let specificationList = await model.getSpecificationList(goodsId);\n        info.goods_number = goodsNumber;\n\n        // 获取商品销量数据\n        // 如果商品表中没有sell_volume字段，从订单表中统计\n        if (!info.sell_volume) {\n            try {\n                const orderModel = this.model('order_goods');\n                const sellVolumeResult = await orderModel\n                    .where({\n                        goods_id: goodsId\n                    })\n                    .sum('number');\n                info.sell_volume = sellVolumeResult || 0;\n            } catch (error) {\n                console.log('获取销量失败，使用默认值:', error);\n                info.sell_volume = 0;\n            }\n        }\n\n        // 获取商品分销状态\n        try {\n            const distributionConfig = await this.model('goods_distribution')\n                .where({ goods_id: goodsId })\n                .find();\n\n            if (distributionConfig) {\n                info.is_distributed = distributionConfig.is_distributed;\n                info.commission_rate = distributionConfig.commission_rate || 0;\n                info.personal_rate = distributionConfig.personal_rate || 0;\n                info.level1_rate = distributionConfig.level1_rate || 0;\n                info.level2_rate = distributionConfig.level2_rate || 0;\n                info.team_leader_rate = distributionConfig.team_leader_rate || 0;\n            } else {\n                info.is_distributed = null;\n                info.commission_rate = 0;\n                info.personal_rate = 0;\n                info.level1_rate = 0;\n                info.level2_rate = 0;\n                info.team_leader_rate = 0;\n            }\n        } catch (error) {\n            console.log('获取分销配置失败，使用默认值:', error);\n            info.is_distributed = null;\n            info.commission_rate = 0;\n            info.personal_rate = 0;\n            info.level1_rate = 0;\n            info.level2_rate = 0;\n            info.team_leader_rate = 0;\n        }\n\n        return this.success({\n            info: info,\n            gallery: gallery,\n            specificationList: specificationList,\n            productList: productList\n        });\n    }\n    async goodsShareAction() {\n        const goodsId = this.get('id');\n        const info = await this.model('goods').where({\n            id: goodsId\n        }).field('name,retail_price').find();\n        return this.success(info);\n    }\n    /**\n     * 获取商品列表\n     * @returns {Promise.<*>}\n     */\n    async listAction() {\n\t\tconst userId = await this.getLoginUserId();\n        const keyword = this.get('keyword');\n        const sort = this.get('sort');\n        const order = this.get('order');\n        const sales = this.get('sales');\n        const model = this.model('goods');\n        const whereMap = {\n            is_on_sale: 1,\n            is_delete: 0,\n        };\n        if (!think.isEmpty(keyword)) {\n            whereMap.name = ['like', `%${keyword}%`];\n            // 添加到搜索历史\n            await this.model('search_history').add({\n                keyword: keyword,\n                user_id: userId,\n                add_time: parseInt(new Date().getTime() / 1000)\n            });\n            //    TODO 之后要做个判断，这个词在搜索记录中的次数，如果大于某个值，则将他存入keyword\n        }\n        // 排序\n        let orderMap = {};\n        if (sort === 'price') {\n            // 按价格\n            orderMap = {\n                retail_price: order\n            };\n        } else if (sort === 'sales') {\n            // 按价格\n            orderMap = {\n                sell_volume: sales\n            };\n        } else {\n            // 按商品添加时间\n            orderMap = {\n                sort_order: 'asc'\n            };\n        }\n        const goodsData = await model.where(whereMap).order(orderMap).select();\n        return this.success(goodsData);\n    }\n    /**\n     * 在售的商品总数\n     * @returns {Promise.<Promise|PreventPromise|void>}\n     */\n    async countAction() {\n        const goodsCount = await this.model('goods').where({\n            is_delete: 0,\n            is_on_sale: 1\n        }).count('id');\n        return this.success({\n            goodsCount: goodsCount\n        });\n    }\n};"]}