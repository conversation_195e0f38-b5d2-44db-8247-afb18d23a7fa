{"version": 3, "sources": ["..\\..\\..\\src\\common\\crontab\\flash_sale_scheduler.js"], "names": ["FlashSaleMultiScheduler", "require", "module", "exports", "think", "Service", "run", "console", "log", "scheduler", "result", "updateRoundStatus", "model", "now", "Date", "getMinutes", "cleanedCount", "cleanupExpiredData", "stats", "getActiveRoundsStats", "error"], "mappings": ";;AAAA;;;;;AAKA,MAAMA,0BAA0BC,QAAQ,uCAAR,CAAhC;;AAEAC,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,OAApB,CAA4B;;AAE3C;;;AAGMC,KAAN,GAAY;AAAA;;AAAA;AACV,UAAI;AACFC,gBAAQC,GAAR,CAAY,sBAAZ;;AAEA,cAAMC,YAAY,IAAIT,uBAAJ,EAAlB;;AAEA;AACA,cAAMU,SAAS,MAAMD,UAAUE,iBAAV,CAA4B,MAAKC,KAAjC,CAArB;;AAEA;AACA,cAAMC,MAAM,IAAIC,IAAJ,EAAZ;AACA,YAAID,IAAIE,UAAJ,OAAqB,CAAzB,EAA4B;AAC1BR,kBAAQC,GAAR,CAAY,gBAAZ;AACA,gBAAMQ,eAAe,MAAMP,UAAUQ,kBAAV,CAA6B,MAAKL,KAAlC,EAAyC,EAAzC,CAA3B;AACAL,kBAAQC,GAAR,CAAa,cAAaQ,YAAa,QAAvC;AACD;;AAED;AACA,cAAME,QAAQ,MAAMT,UAAUU,oBAAV,CAA+B,MAAKP,KAApC,CAApB;AACAL,gBAAQC,GAAR,CAAY,cAAZ,EAA4BU,KAA5B;;AAEAX,gBAAQC,GAAR,CAAY,gBAAZ;AAED,OAtBD,CAsBE,OAAOY,KAAP,EAAc;AACdb,gBAAQa,KAAR,CAAc,iBAAd,EAAiCA,KAAjC;AACD;AAzBS;AA0BX;AA/B0C,CAA7C", "file": "..\\..\\..\\src\\common\\crontab\\flash_sale_scheduler.js", "sourcesContent": ["/**\n * 秒杀轮次状态更新定时任务\n * 每分钟执行一次，检查并更新轮次状态\n */\n\nconst FlashSaleMultiScheduler = require('../service/flash_sale_multi_scheduler');\n\nmodule.exports = class extends think.Service {\n  \n  /**\n   * 执行定时任务\n   */\n  async run() {\n    try {\n      console.log('🔄 开始执行秒杀轮次状态更新任务...');\n      \n      const scheduler = new FlashSaleMultiScheduler();\n      \n      // 更新轮次状态\n      const result = await scheduler.updateRoundStatus(this.model);\n      \n      // 每小时清理一次过期数据（当分钟数为0时）\n      const now = new Date();\n      if (now.getMinutes() === 0) {\n        console.log('🧹 开始清理过期数据...');\n        const cleanedCount = await scheduler.cleanupExpiredData(this.model, 30);\n        console.log(`✅ 清理完成，删除了 ${cleanedCount} 个过期轮次`);\n      }\n      \n      // 输出统计信息\n      const stats = await scheduler.getActiveRoundsStats(this.model);\n      console.log('📊 当前轮次状态统计:', stats);\n      \n      console.log('✅ 秒杀轮次状态更新任务完成');\n      \n    } catch (error) {\n      console.error('❌ 秒杀轮次状态更新任务失败:', error);\n    }\n  }\n};\n"]}