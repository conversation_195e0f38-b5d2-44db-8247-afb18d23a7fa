{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleMultiPage.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleMultiPage.vue", "mtime": 1753847684181}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRmxhc2hTYWxlTXVsdGlQYWdlJywKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgc3RhdGlzdGljczoge30sCiAgICAgIGN1cnJlbnRSb3VuZHM6IHsgY3VycmVudDogW10sIHVwY29taW5nOiBbXSB9LAogICAgICByb3VuZHNMaXN0OiB7IGRhdGE6IFtdLCBjb3VudDogMCB9LAogICAgICBnb29kc0xpc3Q6IFtdLAogICAgICBsb2FkaW5nR29vZHM6IGZhbHNlLAogICAgICBzaG93QWRkTW9kYWw6IGZhbHNlLAogICAgICBzaG93RGV0YWlsTW9kYWw6IGZhbHNlLAogICAgICBzZWxlY3RlZFJvdW5kOiB7fSwKICAgICAgaXNDcmVhdGluZzogZmFsc2UsCiAgICAgIG5ld1JvdW5kOiB7CiAgICAgICAgc3RhcnRfZGF0ZTogJycsCiAgICAgICAgZW5kX2RhdGU6ICcnLAogICAgICAgIGdvb2RzX2xpc3Q6IFtdCiAgICAgIH0sCiAgICAgIHJlZnJlc2hUaW1lcjogbnVsbCwKICAgICAgY3JlYXRpb25Qcm9ncmVzczogewogICAgICAgIGN1cnJlbnQ6IDAsCiAgICAgICAgdG90YWw6IDAKICAgICAgfQogICAgfTsKICB9LAogIAogIGNvbXB1dGVkOiB7CiAgICBjYW5DcmVhdGVSb3VuZCgpIHsKICAgICAgY29uc3QgaGFzU3RhcnREYXRlID0gdGhpcy5uZXdSb3VuZC5zdGFydF9kYXRlOwogICAgICBjb25zdCBoYXNFbmREYXRlID0gdGhpcy5uZXdSb3VuZC5lbmRfZGF0ZTsKICAgICAgY29uc3QgaGFzR29vZHMgPSB0aGlzLm5ld1JvdW5kLmdvb2RzX2xpc3QubGVuZ3RoID4gMDsKICAgICAgY29uc3QgZ29vZHNWYWxpZCA9IHRoaXMubmV3Um91bmQuZ29vZHNfbGlzdC5ldmVyeShnID0+IGcuZmxhc2hfcHJpY2UgPiAwICYmIGcuc3RvY2sgPiAwKTsKICAgICAgY29uc3QgZGF0ZVZhbGlkID0gaGFzU3RhcnREYXRlICYmIGhhc0VuZERhdGUgJiYgbmV3IERhdGUodGhpcy5uZXdSb3VuZC5zdGFydF9kYXRlKSA8PSBuZXcgRGF0ZSh0aGlzLm5ld1JvdW5kLmVuZF9kYXRlKTsKCiAgICAgIGNvbnNvbGUubG9nKCdjYW5DcmVhdGVSb3VuZOajgOafpTonLCB7CiAgICAgICAgaGFzU3RhcnREYXRlLAogICAgICAgIGhhc0VuZERhdGUsCiAgICAgICAgaGFzR29vZHMsCiAgICAgICAgZ29vZHNWYWxpZCwKICAgICAgICBkYXRlVmFsaWQsCiAgICAgICAgZ29vZHNMaXN0OiB0aGlzLm5ld1JvdW5kLmdvb2RzX2xpc3QKICAgICAgfSk7CgogICAgICByZXR1cm4gaGFzU3RhcnREYXRlICYmIGhhc0VuZERhdGUgJiYgaGFzR29vZHMgJiYgZ29vZHNWYWxpZCAmJiBkYXRlVmFsaWQ7CiAgICB9LAoKICAgIC8vIOiHquWKqOeUn+aIkOi9ruasoeWQjeensAogICAgZ2VuZXJhdGVkUm91bmROYW1lKCkgewogICAgICBpZiAoIXRoaXMubmV3Um91bmQuc3RhcnRfZGF0ZSB8fCAhdGhpcy5uZXdSb3VuZC5lbmRfZGF0ZSkgewogICAgICAgIHJldHVybiAnJzsKICAgICAgfQoKICAgICAgY29uc3Qgc3RhcnREYXRlID0gbmV3IERhdGUodGhpcy5uZXdSb3VuZC5zdGFydF9kYXRlKTsKICAgICAgY29uc3QgZW5kRGF0ZSA9IG5ldyBEYXRlKHRoaXMubmV3Um91bmQuZW5kX2RhdGUpOwoKICAgICAgaWYgKHN0YXJ0RGF0ZS5nZXRUaW1lKCkgPT09IGVuZERhdGUuZ2V0VGltZSgpKSB7CiAgICAgICAgLy8g5Y2V5pel5rS75YqoCiAgICAgICAgY29uc3QgZGF0ZVN0ciA9IHN0YXJ0RGF0ZS50b0xvY2FsZURhdGVTdHJpbmcoJ3poLUNOJywgewogICAgICAgICAgbW9udGg6ICdsb25nJywKICAgICAgICAgIGRheTogJ251bWVyaWMnCiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuIGAke2RhdGVTdHJ95pW054K556eS5p2AYDsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlpJrml6XmtLvliqgKICAgICAgICBjb25zdCBzdGFydFN0ciA9IHN0YXJ0RGF0ZS50b0xvY2FsZURhdGVTdHJpbmcoJ3poLUNOJywgewogICAgICAgICAgbW9udGg6ICdsb25nJywKICAgICAgICAgIGRheTogJ251bWVyaWMnCiAgICAgICAgfSk7CiAgICAgICAgY29uc3QgZW5kU3RyID0gZW5kRGF0ZS50b0xvY2FsZURhdGVTdHJpbmcoJ3poLUNOJywgewogICAgICAgICAgbW9udGg6ICdsb25nJywKICAgICAgICAgIGRheTogJ251bWVyaWMnCiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuIGAke3N0YXJ0U3RyfeiHsyR7ZW5kU3RyfeaVtOeCueenkuadgGA7CiAgICAgIH0KICAgIH0sCgogICAgLy8g5pW054K556eS5p2A5pe25q616aKE6KeI77yIMjTlsI/ml7blhajlpKnlgJnvvIkKICAgIGhvdXJseVNsb3RQcmV2aWV3KCkgewogICAgICBpZiAoIXRoaXMubmV3Um91bmQuc3RhcnRfZGF0ZSB8fCAhdGhpcy5uZXdSb3VuZC5lbmRfZGF0ZSkgewogICAgICAgIHJldHVybiBbXTsKICAgICAgfQoKICAgICAgY29uc3Qgc2xvdHMgPSBbXTsKICAgICAgY29uc3Qgc3RhcnREYXRlID0gbmV3IERhdGUodGhpcy5uZXdSb3VuZC5zdGFydF9kYXRlKTsKICAgICAgY29uc3QgZW5kRGF0ZSA9IG5ldyBEYXRlKHRoaXMubmV3Um91bmQuZW5kX2RhdGUpOwoKICAgICAgLy8g6K6+572u57uT5p2f5pel5pyf5Li65b2T5aSp55qEMjM6NTk6NTkKICAgICAgZW5kRGF0ZS5zZXRIb3VycygyMywgNTksIDU5LCA5OTkpOwoKICAgICAgbGV0IGN1cnJlbnREYXRlID0gbmV3IERhdGUoc3RhcnREYXRlKTsKICAgICAgY3VycmVudERhdGUuc2V0SG91cnMoMCwgMCwgMCwgMCk7IC8vIOS7jjAwOjAw5byA5aeLCgogICAgICB3aGlsZSAoY3VycmVudERhdGUgPD0gZW5kRGF0ZSkgewogICAgICAgIGZvciAobGV0IGhvdXIgPSAwOyBob3VyIDwgMjQ7IGhvdXIrKykgewogICAgICAgICAgY29uc3Qgc2xvdFN0YXJ0ID0gbmV3IERhdGUoY3VycmVudERhdGUpOwogICAgICAgICAgc2xvdFN0YXJ0LnNldEhvdXJzKGhvdXIsIDAsIDAsIDApOwoKICAgICAgICAgIGNvbnN0IHNsb3RFbmQgPSBuZXcgRGF0ZShjdXJyZW50RGF0ZSk7CiAgICAgICAgICBzbG90RW5kLnNldEhvdXJzKGhvdXIsIDQwLCAwLCAwKTsKCiAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbotoXlh7rnu5PmnZ/ml6XmnJ8KICAgICAgICAgIGlmIChzbG90U3RhcnQgPiBlbmREYXRlKSBicmVhazsKCiAgICAgICAgICBzbG90cy5wdXNoKHsKICAgICAgICAgICAgc3RhcnQ6IHNsb3RTdGFydC50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE5KS5yZXBsYWNlKCdUJywgJyAnKSwKICAgICAgICAgICAgZW5kOiBzbG90RW5kLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTkpLnJlcGxhY2UoJ1QnLCAnICcpLAogICAgICAgICAgICBzdGFydFRpbWU6IHNsb3RTdGFydCwKICAgICAgICAgICAgZW5kVGltZTogc2xvdEVuZAogICAgICAgICAgfSk7CiAgICAgICAgfQoKICAgICAgICAvLyDnp7vliqjliLDkuIvkuIDlpKkKICAgICAgICBjdXJyZW50RGF0ZS5zZXREYXRlKGN1cnJlbnREYXRlLmdldERhdGUoKSArIDEpOwogICAgICB9CgogICAgICByZXR1cm4gc2xvdHM7CiAgICB9LAoKICAgIC8vIOacieaViOaXtuauteaVsOmHj++8iOacqui/h+acn+eahOaXtuaute+8iQogICAgdmFsaWRTbG90c0NvdW50KCkgewogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOwogICAgICByZXR1cm4gdGhpcy5ob3VybHlTbG90UHJldmlldy5maWx0ZXIoc2xvdCA9PiBuZXcgRGF0ZShzbG90LnN0YXJ0KSA+IG5vdykubGVuZ3RoOwogICAgfQogIH0sCgogIG1vdW50ZWQoKSB7CiAgICBjb25zb2xlLmxvZygnRmxhc2hTYWxlTXVsdGlQYWdl57uE5Lu25bey5oyC6L29Jyk7CiAgICBjb25zb2xlLmxvZygn5Yid5aeLc2hvd0FkZE1vZGFs5YC8OicsIHRoaXMuc2hvd0FkZE1vZGFsKTsKICAgIHRoaXMubG9hZERhdGEoKTsKICAgIHRoaXMuc3RhcnRBdXRvUmVmcmVzaCgpOwogIH0sCgogIGJlZm9yZURlc3Ryb3koKSB7CiAgICBpZiAodGhpcy5yZWZyZXNoVGltZXIpIHsKICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLnJlZnJlc2hUaW1lcik7CiAgICB9CiAgfSwKCiAgbWV0aG9kczogewogICAgYXN5bmMgbG9hZERhdGEoKSB7CiAgICAgIGF3YWl0IFByb21pc2UuYWxsKFsKICAgICAgICB0aGlzLmxvYWRTdGF0aXN0aWNzKCksCiAgICAgICAgdGhpcy5sb2FkQ3VycmVudFJvdW5kcygpLAogICAgICAgIHRoaXMubG9hZFJvdW5kc0xpc3QoKSwKICAgICAgICB0aGlzLmxvYWRHb29kc0xpc3QoKQogICAgICBdKTsKICAgIH0sCgogICAgYXN5bmMgbG9hZFN0YXRpc3RpY3MoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmF4aW9zLmdldCgnZmxhc2hzYWxlbXVsdGkvc3RhdGlzdGljcycpOwogICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmVycm5vID09PSAwKSB7CiAgICAgICAgICB0aGlzLnN0YXRpc3RpY3MgPSByZXNwb25zZS5kYXRhLmRhdGE7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9vee7n+iuoeaVsOaNruWksei0pTonLCBlcnJvcik7CiAgICAgIH0KICAgIH0sCgogICAgYXN5bmMgbG9hZEN1cnJlbnRSb3VuZHMoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmF4aW9zLmdldCgnZmxhc2hzYWxlbXVsdGkvY3VycmVudCcpOwogICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmVycm5vID09PSAwKSB7CiAgICAgICAgICB0aGlzLmN1cnJlbnRSb3VuZHMgPSByZXNwb25zZS5kYXRhLmRhdGE7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veW9k+WJjei9ruasoeWksei0pTonLCBlcnJvcik7CiAgICAgIH0KICAgIH0sCgogICAgYXN5bmMgbG9hZFJvdW5kc0xpc3QoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmF4aW9zLmdldCgnZmxhc2hzYWxlbXVsdGkvbGlzdCcpOwogICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmVycm5vID09PSAwKSB7CiAgICAgICAgICB0aGlzLnJvdW5kc0xpc3QgPSByZXNwb25zZS5kYXRhLmRhdGE7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9vei9ruasoeWIl+ihqOWksei0pTonLCBlcnJvcik7CiAgICAgIH0KICAgIH0sCgogICAgYXN5bmMgbG9hZEdvb2RzTGlzdCgpIHsKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLmxvYWRpbmdHb29kcyA9IHRydWU7CiAgICAgICAgY29uc29sZS5sb2coJ+W8gOWni+WKoOi9veWVhuWTgeWIl+ihqC4uLicpOwoKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuYXhpb3MuZ2V0KCdmbGFzaHNhbGVtdWx0aS9nb29kcycpOwogICAgICAgIGNvbnNvbGUubG9nKCfllYblk4HliJfooahBUEnlk43lupQ6JywgcmVzcG9uc2UuZGF0YSk7CgogICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmVycm5vID09PSAwKSB7CiAgICAgICAgICB0aGlzLmdvb2RzTGlzdCA9IHJlc3BvbnNlLmRhdGEuZGF0YSB8fCBbXTsKICAgICAgICAgIGNvbnNvbGUubG9nKCfllYblk4HliJfooajliqDovb3miJDlip/vvIzmlbDph486JywgdGhpcy5nb29kc0xpc3QubGVuZ3RoKTsKCiAgICAgICAgICBpZiAodGhpcy5nb29kc0xpc3QubGVuZ3RoID09PSAwKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5pqC5peg5Y+v6YCJ5ZWG5ZOB77yM6K+356Gu5L+d5pyJ5LiK5p6255qE5ZWG5ZOB5LiU5pyq5Y+C5LiO5YW25LuW56eS5p2A5rS75YqoJyk7CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0FQSei/lOWbnumUmeivrzonLCByZXNwb25zZS5kYXRhLmVycm1zZyk7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLmRhdGEuZXJybXNnIHx8ICfliqDovb3llYblk4HliJfooajlpLHotKUnKTsKICAgICAgICAgIHRoaXMuZ29vZHNMaXN0ID0gW107CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veWVhuWTgeWIl+ihqOWksei0pTonLCBlcnJvcik7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign572R57uc6ZSZ6K+v77yM6K+35qOA5p+l5pyN5Yqh5Zmo6L+e5o6lJyk7CiAgICAgICAgdGhpcy5nb29kc0xpc3QgPSBbXTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLmxvYWRpbmdHb29kcyA9IGZhbHNlOwogICAgICB9CiAgICB9LAoKICAgIHN0YXJ0QXV0b1JlZnJlc2goKSB7CiAgICAgIHRoaXMucmVmcmVzaFRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gewogICAgICAgIHRoaXMubG9hZEN1cnJlbnRSb3VuZHMoKTsKICAgICAgICB0aGlzLmxvYWRTdGF0aXN0aWNzKCk7CiAgICAgIH0sIDMwMDAwKTsgLy8gMzDnp5LliLfmlrDkuIDmrKEKICAgIH0sCgogICAgaXNHb29kc1NlbGVjdGVkKGdvb2RzSWQpIHsKICAgICAgcmV0dXJuIHRoaXMubmV3Um91bmQuZ29vZHNfbGlzdC5zb21lKGcgPT4gZy5nb29kc19pZCA9PT0gZ29vZHNJZCk7CiAgICB9LAoKICAgIGdldFNlbGVjdGVkR29vZHMoZ29vZHNJZCkgewogICAgICByZXR1cm4gdGhpcy5uZXdSb3VuZC5nb29kc19saXN0LmZpbmQoZyA9PiBnLmdvb2RzX2lkID09PSBnb29kc0lkKTsKICAgIH0sCgogICAgdG9nZ2xlR29vZHMoZ29vZHMpIHsKICAgICAgaWYgKCFnb29kcy5jYW5fc2VsZWN0KSByZXR1cm47CgogICAgICBpZiAodGhpcy5pc0dvb2RzU2VsZWN0ZWQoZ29vZHMuaWQpKSB7CiAgICAgICAgdGhpcy5yZW1vdmVHb29kcyhnb29kcy5pZCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY29uc3Qgb3JpZ2luYWxQcmljZSA9IHBhcnNlRmxvYXQoZ29vZHMucmV0YWlsX3ByaWNlKSB8fCAwOwogICAgICAgIGNvbnN0IGRlZmF1bHREaXNjb3VudCA9IDIwOyAvLyDpu5jorqQyMCXmipjmiaMKICAgICAgICBjb25zdCBmbGFzaFByaWNlID0gTWF0aC5yb3VuZChvcmlnaW5hbFByaWNlICogKDEwMCAtIGRlZmF1bHREaXNjb3VudCkgLyAxMDAgKiAxMDApIC8gMTAwOwoKICAgICAgICB0aGlzLm5ld1JvdW5kLmdvb2RzX2xpc3QucHVzaCh7CiAgICAgICAgICBnb29kc19pZDogZ29vZHMuaWQsCiAgICAgICAgICBnb29kc19uYW1lOiBnb29kcy5uYW1lLAogICAgICAgICAgb3JpZ2luYWxfcHJpY2U6IG9yaWdpbmFsUHJpY2UsCiAgICAgICAgICBmbGFzaF9wcmljZTogZmxhc2hQcmljZSwKICAgICAgICAgIGRpc2NvdW50X3JhdGU6IGRlZmF1bHREaXNjb3VudCwKICAgICAgICAgIHN0b2NrOiAxMDAsCiAgICAgICAgICBsaW1pdF9xdWFudGl0eTogMQogICAgICAgIH0pOwogICAgICB9CiAgICB9LAoKICAgIHJlbW92ZUdvb2RzKGdvb2RzSWQpIHsKICAgICAgY29uc3QgaW5kZXggPSB0aGlzLm5ld1JvdW5kLmdvb2RzX2xpc3QuZmluZEluZGV4KGcgPT4gZy5nb29kc19pZCA9PT0gZ29vZHNJZCk7CiAgICAgIGlmIChpbmRleCA+IC0xKSB7CiAgICAgICAgdGhpcy5uZXdSb3VuZC5nb29kc19saXN0LnNwbGljZShpbmRleCwgMSk7CiAgICAgIH0KICAgIH0sCgogICAgY2FsY3VsYXRlRGlzY291bnRSYXRlKG9yaWdpbmFsUHJpY2UsIGZsYXNoUHJpY2UpIHsKICAgICAgaWYgKCFvcmlnaW5hbFByaWNlIHx8IG9yaWdpbmFsUHJpY2UgPD0gMCB8fCAhZmxhc2hQcmljZSB8fCBmbGFzaFByaWNlIDw9IDApIHJldHVybiAwOwogICAgICBjb25zdCByYXRlID0gTWF0aC5yb3VuZCgoMSAtIGZsYXNoUHJpY2UgLyBvcmlnaW5hbFByaWNlKSAqIDEwMCk7CiAgICAgIHJldHVybiBpc05hTihyYXRlKSA/IDAgOiByYXRlOwogICAgfSwKCiAgICB1cGRhdGVGbGFzaFByaWNlQnlEaXNjb3VudChnb29kc0lkKSB7CiAgICAgIGNvbnN0IHNlbGVjdGVkR29vZHMgPSB0aGlzLmdldFNlbGVjdGVkR29vZHMoZ29vZHNJZCk7CiAgICAgIGlmIChzZWxlY3RlZEdvb2RzICYmIHNlbGVjdGVkR29vZHMub3JpZ2luYWxfcHJpY2UgPiAwKSB7CiAgICAgICAgY29uc3QgZGlzY291bnRSYXRlID0gc2VsZWN0ZWRHb29kcy5kaXNjb3VudF9yYXRlIHx8IDA7CiAgICAgICAgY29uc3QgZmxhc2hQcmljZSA9IE1hdGgucm91bmQoc2VsZWN0ZWRHb29kcy5vcmlnaW5hbF9wcmljZSAqICgxMDAgLSBkaXNjb3VudFJhdGUpIC8gMTAwICogMTAwKSAvIDEwMDsKICAgICAgICBzZWxlY3RlZEdvb2RzLmZsYXNoX3ByaWNlID0gZmxhc2hQcmljZTsKICAgICAgfQogICAgfSwKCiAgICBnZXRDdXJyZW50RGF0ZVRpbWUoKSB7CiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7CiAgICAgIG5vdy5zZXRNaW51dGVzKG5vdy5nZXRNaW51dGVzKCkgLSBub3cuZ2V0VGltZXpvbmVPZmZzZXQoKSk7CiAgICAgIHJldHVybiBub3cudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxNik7CiAgICB9LAoKICAgIGdldEN1cnJlbnREYXRlKCkgewogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOwogICAgICByZXR1cm4gbm93LnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTApOwogICAgfSwKCiAgICBvcGVuQ3JlYXRlTW9kYWwoKSB7CiAgICAgIGNvbnNvbGUubG9nKCfngrnlh7vliJvlu7rmlrDova7mrKHmjInpkq4nKTsKICAgICAgLy8g6K6+572u6buY6K6k5pel5pyf5Li65LuK5aSpCiAgICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDEwKTsKICAgICAgdGhpcy5uZXdSb3VuZC5zdGFydF9kYXRlID0gdG9kYXk7CiAgICAgIHRoaXMubmV3Um91bmQuZW5kX2RhdGUgPSB0b2RheTsKICAgICAgdGhpcy5zaG93QWRkTW9kYWwgPSB0cnVlOwogICAgICBjb25zb2xlLmxvZygnc2hvd0FkZE1vZGFs6K6+572u5Li6OicsIHRoaXMuc2hvd0FkZE1vZGFsKTsKICAgIH0sCgogICAgZm9ybWF0RGF0ZVRpbWUoZGF0ZVRpbWVTdHIpIHsKICAgICAgaWYgKCFkYXRlVGltZVN0cikgcmV0dXJuICcnOwogICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoZGF0ZVRpbWVTdHIpOwogICAgICByZXR1cm4gZGF0ZS50b0xvY2FsZVN0cmluZygnemgtQ04nLCB7CiAgICAgICAgeWVhcjogJ251bWVyaWMnLAogICAgICAgIG1vbnRoOiAnMi1kaWdpdCcsCiAgICAgICAgZGF5OiAnMi1kaWdpdCcsCiAgICAgICAgaG91cjogJzItZGlnaXQnLAogICAgICAgIG1pbnV0ZTogJzItZGlnaXQnCiAgICAgIH0pOwogICAgfSwKCiAgICBmb3JtYXRTbG90VGltZShkYXRlVGltZVN0cikgewogICAgICBpZiAoIWRhdGVUaW1lU3RyKSByZXR1cm4gJyc7CiAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShkYXRlVGltZVN0cik7CiAgICAgIHJldHVybiBkYXRlLnRvTG9jYWxlU3RyaW5nKCd6aC1DTicsIHsKICAgICAgICBtb250aDogJzItZGlnaXQnLAogICAgICAgIGRheTogJzItZGlnaXQnLAogICAgICAgIGhvdXI6ICcyLWRpZ2l0JywKICAgICAgICBtaW51dGU6ICcyLWRpZ2l0JwogICAgICB9KTsKICAgIH0sCgogICAgaXNTbG90SW5QYXN0KHN0YXJ0VGltZSkgewogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOwogICAgICBjb25zdCBzbG90U3RhcnQgPSBuZXcgRGF0ZShzdGFydFRpbWUpOwogICAgICByZXR1cm4gc2xvdFN0YXJ0IDwgbm93OwogICAgfSwKCiAgICBpc1Nsb3RBY3RpdmUoc3RhcnRUaW1lLCBlbmRUaW1lKSB7CiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7CiAgICAgIGNvbnN0IHNsb3RTdGFydCA9IG5ldyBEYXRlKHN0YXJ0VGltZSk7CiAgICAgIGNvbnN0IHNsb3RFbmQgPSBuZXcgRGF0ZShlbmRUaW1lKTsKICAgICAgcmV0dXJuIG5vdyA+PSBzbG90U3RhcnQgJiYgbm93IDw9IHNsb3RFbmQ7CiAgICB9LAoKICAgIGFzeW5jIGNyZWF0ZVJvdW5kKCkgewogICAgICBpZiAoIXRoaXMuY2FuQ3JlYXRlUm91bmQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfor7flrozlloTova7mrKHkv6Hmga8nKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOeUn+aIkOaVtOeCueenkuadgOaXtuauteaVsOaNrgogICAgICBjb25zdCBob3VybHlTbG90cyA9IHRoaXMuaG91cmx5U2xvdFByZXZpZXc7CiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7CgogICAgICAvLyDov4fmu6Tmjonlt7Lov4fmnJ/nmoTml7bmrrUKICAgICAgY29uc3QgdmFsaWRTbG90cyA9IGhvdXJseVNsb3RzLmZpbHRlcihzbG90ID0+IG5ldyBEYXRlKHNsb3Quc3RhcnQpID4gbm93KTsKCiAgICAgIGlmICh2YWxpZFNsb3RzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aJgOmAieaXtumXtOauteWGheayoeacieacieaViOeahOenkuadgOaXtuautScpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgLy8g5aaC5p6c6L2u5qyh5pWw6YeP6L+H5aSa77yM6K+i6Zeu55So5oi356Gu6K6kCiAgICAgIGlmICh2YWxpZFNsb3RzLmxlbmd0aCA+IDUwKSB7CiAgICAgICAgY29uc3QgY29uZmlybWVkID0gY29uZmlybShg5bCG6KaB5Yib5bu6JHt2YWxpZFNsb3RzLmxlbmd0aH3kuKrova7mrKHvvIzov5nlj6/og73pnIDopoHovoPplb/ml7bpl7TjgILmmK/lkKbnu6fnu63vvJ9gKTsKICAgICAgICBpZiAoIWNvbmZpcm1lZCkgewogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLmlzQ3JlYXRpbmcgPSB0cnVlOwogICAgICAgIHRoaXMuY3JlYXRpb25Qcm9ncmVzcy5jdXJyZW50ID0gMDsKICAgICAgICB0aGlzLmNyZWF0aW9uUHJvZ3Jlc3MudG90YWwgPSB2YWxpZFNsb3RzLmxlbmd0aDsKCiAgICAgICAgbGV0IGNyZWF0ZWRDb3VudCA9IDA7CiAgICAgICAgbGV0IGZhaWxlZENvdW50ID0gMDsKICAgICAgICBjb25zdCBmYWlsZWRSZWFzb25zID0gW107CgogICAgICAgIC8vIOaJuemHj+WkhOeQhu+8jOavj+asoeWkhOeQhjEw5Liq6L2u5qyhCiAgICAgICAgY29uc3QgYmF0Y2hTaXplID0gMTA7CiAgICAgICAgZm9yIChsZXQgYmF0Y2hTdGFydCA9IDA7IGJhdGNoU3RhcnQgPCB2YWxpZFNsb3RzLmxlbmd0aDsgYmF0Y2hTdGFydCArPSBiYXRjaFNpemUpIHsKICAgICAgICAgIGNvbnN0IGJhdGNoRW5kID0gTWF0aC5taW4oYmF0Y2hTdGFydCArIGJhdGNoU2l6ZSwgdmFsaWRTbG90cy5sZW5ndGgpOwogICAgICAgICAgY29uc3QgYmF0Y2ggPSB2YWxpZFNsb3RzLnNsaWNlKGJhdGNoU3RhcnQsIGJhdGNoRW5kKTsKCiAgICAgICAgICAvLyDlubbooYzliJvlu7rlvZPliY3mibnmrKHnmoTova7mrKEKICAgICAgICAgIGNvbnN0IGJhdGNoUHJvbWlzZXMgPSBiYXRjaC5tYXAoYXN5bmMgKHNsb3QsIGJhdGNoSW5kZXgpID0+IHsKICAgICAgICAgICAgY29uc3QgZ2xvYmFsSW5kZXggPSBiYXRjaFN0YXJ0ICsgYmF0Y2hJbmRleDsKCiAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgY29uc3Qgcm91bmREYXRhID0gewogICAgICAgICAgICAgICAgcm91bmRfbmFtZTogYCR7dGhpcy5nZW5lcmF0ZWRSb3VuZE5hbWV9IC0g56ysJHtnbG9iYWxJbmRleCArIDF95Zy6YCwKICAgICAgICAgICAgICAgIHN0YXJ0X3RpbWU6IHNsb3Quc3RhcnQsCiAgICAgICAgICAgICAgICBlbmRfdGltZTogc2xvdC5lbmQsCiAgICAgICAgICAgICAgICBpc19ob3VybHlfZmxhc2g6IHRydWUsCiAgICAgICAgICAgICAgICBzbG90X2luZGV4OiBnbG9iYWxJbmRleCArIDEsCiAgICAgICAgICAgICAgICB0b3RhbF9zbG90czogdmFsaWRTbG90cy5sZW5ndGgsCiAgICAgICAgICAgICAgICBnb29kc19saXN0OiB0aGlzLm5ld1JvdW5kLmdvb2RzX2xpc3QubWFwKGdvb2RzID0+ICh7CiAgICAgICAgICAgICAgICAgIGdvb2RzX2lkOiBnb29kcy5nb29kc19pZCwKICAgICAgICAgICAgICAgICAgZ29vZHNfbmFtZTogZ29vZHMuZ29vZHNfbmFtZSwKICAgICAgICAgICAgICAgICAgZ29vZHNfaW1hZ2U6IGdvb2RzLmdvb2RzX2ltYWdlLAogICAgICAgICAgICAgICAgICBvcmlnaW5hbF9wcmljZTogZ29vZHMub3JpZ2luYWxfcHJpY2UsCiAgICAgICAgICAgICAgICAgIGZsYXNoX3ByaWNlOiBnb29kcy5mbGFzaF9wcmljZSwKICAgICAgICAgICAgICAgICAgc3RvY2s6IGdvb2RzLnN0b2NrLAogICAgICAgICAgICAgICAgICBkaXNjb3VudF9yYXRlOiBnb29kcy5kaXNjb3VudF9yYXRlCiAgICAgICAgICAgICAgICB9KSkKICAgICAgICAgICAgICB9OwoKICAgICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuYXhpb3MucG9zdCgnZmxhc2hzYWxlbXVsdGkvY3JlYXRlJywgcm91bmREYXRhKTsKCiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuZXJybm8gPT09IDApIHsKICAgICAgICAgICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGluZGV4OiBnbG9iYWxJbmRleCArIDEgfTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGluZGV4OiBnbG9iYWxJbmRleCArIDEsIGVycm9yOiByZXNwb25zZS5kYXRhLmVycm1zZyB9OwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgaW5kZXg6IGdsb2JhbEluZGV4ICsgMSwgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CgogICAgICAgICAgLy8g562J5b6F5b2T5YmN5om55qyh5a6M5oiQCiAgICAgICAgICBjb25zdCBiYXRjaFJlc3VsdHMgPSBhd2FpdCBQcm9taXNlLmFsbChiYXRjaFByb21pc2VzKTsKCiAgICAgICAgICAvLyDnu5/orqHnu5PmnpwKICAgICAgICAgIGJhdGNoUmVzdWx0cy5mb3JFYWNoKHJlc3VsdCA9PiB7CiAgICAgICAgICAgIHRoaXMuY3JlYXRpb25Qcm9ncmVzcy5jdXJyZW50Kys7CiAgICAgICAgICAgIGlmIChyZXN1bHQuc3VjY2VzcykgewogICAgICAgICAgICAgIGNyZWF0ZWRDb3VudCsrOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIGZhaWxlZENvdW50Kys7CiAgICAgICAgICAgICAgaWYgKGZhaWxlZFJlYXNvbnMubGVuZ3RoIDwgNSkgeyAvLyDlj6rorrDlvZXliY015Liq6ZSZ6K+vCiAgICAgICAgICAgICAgICBmYWlsZWRSZWFzb25zLnB1c2goYOesrCR7cmVzdWx0LmluZGV4feWcujogJHtyZXN1bHQuZXJyb3J9YCk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKCiAgICAgICAgICAvLyDnn63mmoLlu7bov5/vvIzpgb/lhY3mnI3liqHlmajljovlipvov4flpKcKICAgICAgICAgIGlmIChiYXRjaEVuZCA8IHZhbGlkU2xvdHMubGVuZ3RoKSB7CiAgICAgICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDApKTsKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIC8vIOaYvuekuue7k+aenAogICAgICAgIGlmIChjcmVhdGVkQ291bnQgPiAwKSB7CiAgICAgICAgICBsZXQgbWVzc2FnZSA9IGDmiJDlip/liJvlu7oke2NyZWF0ZWRDb3VudH3kuKrmlbTngrnnp5LmnYDova7mrKFgOwogICAgICAgICAgaWYgKGZhaWxlZENvdW50ID4gMCkgewogICAgICAgICAgICBtZXNzYWdlICs9IGDvvIwke2ZhaWxlZENvdW50feS4quWksei0pWA7CiAgICAgICAgICAgIGlmIChmYWlsZWRSZWFzb25zLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ+WIm+W7uuWksei0peeahOi9ruasoTonLCBmYWlsZWRSZWFzb25zKTsKICAgICAgICAgICAgICBtZXNzYWdlICs9IGBcbuS4u+imgemUmeivrzogJHtmYWlsZWRSZWFzb25zWzBdfWA7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2VzcyhtZXNzYWdlKTsKICAgICAgICAgIHRoaXMuY2xvc2VNb2RhbCgpOwogICAgICAgICAgdGhpcy5sb2FkRGF0YSgpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBsZXQgZXJyb3JNZXNzYWdlID0gJ+aJgOaciei9ruasoeWIm+W7uuWksei0pSc7CiAgICAgICAgICBpZiAoZmFpbGVkUmVhc29ucy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgIGVycm9yTWVzc2FnZSArPSBgXG7plJnor6/kv6Hmga86ICR7ZmFpbGVkUmVhc29uc1swXX1gOwogICAgICAgICAgfQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihlcnJvck1lc3NhZ2UpOwogICAgICAgIH0KCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5Yib5bu65pW054K556eS5p2A6L2u5qyh5aSx6LSlOicsIGVycm9yKTsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliJvlu7rov4fnqIvkuK3lj5HnlJ/plJnor686ICcgKyBlcnJvci5tZXNzYWdlKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLmlzQ3JlYXRpbmcgPSBmYWxzZTsKICAgICAgICB0aGlzLmNyZWF0aW9uUHJvZ3Jlc3MuY3VycmVudCA9IDA7CiAgICAgICAgdGhpcy5jcmVhdGlvblByb2dyZXNzLnRvdGFsID0gMDsKICAgICAgfQogICAgfSwKCiAgICBjbG9zZU1vZGFsKCkgewogICAgICB0aGlzLnNob3dBZGRNb2RhbCA9IGZhbHNlOwogICAgICB0aGlzLm5ld1JvdW5kID0gewogICAgICAgIHN0YXJ0X2RhdGU6ICcnLAogICAgICAgIGVuZF9kYXRlOiAnJywKICAgICAgICBnb29kc19saXN0OiBbXQogICAgICB9OwogICAgfSwKCiAgICB2aWV3Um91bmREZXRhaWxzKHJvdW5kKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRSb3VuZCA9IHJvdW5kOwogICAgICB0aGlzLnNob3dEZXRhaWxNb2RhbCA9IHRydWU7CiAgICB9LAoKICAgIGFzeW5jIGNsb3NlUm91bmQocm91bmQpIHsKICAgICAgaWYgKCFjb25maXJtKGDnoa7lrpropoHlhbPpl63ova7mrKEiJHtyb3VuZC5yb3VuZF9uYW1lfSLlkJfvvJ/lhbPpl63lkI7ova7mrKHlsIbnq4vljbPnu5PmnZ/jgIJgKSkgewogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+ato+WcqOWFs+mXrei9ruasoS4uLicpOwogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5heGlvcy5wb3N0KCdmbGFzaHNhbGVtdWx0aS9jbG9zZScsIHsKICAgICAgICAgIHJvdW5kX2lkOiByb3VuZC5pZAogICAgICAgIH0pOwoKICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5lcnJubyA9PT0gMCkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfova7mrKHlt7LlhbPpl60nKTsKICAgICAgICAgIHRoaXMubG9hZERhdGEoKTsgLy8g6YeN5paw5Yqg6L295pWw5o2uCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UuZGF0YS5lcnJtc2cgfHwgJ+WFs+mXreWksei0pScpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCflhbPpl63ova7mrKHlpLHotKU6JywgZXJyb3IpOwogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WFs+mXreWksei0pScpOwogICAgICB9CiAgICB9LAoKCgogICAgZm9ybWF0VGltZShzZWNvbmRzKSB7CiAgICAgIGlmICghc2Vjb25kcyB8fCBzZWNvbmRzIDw9IDApIHJldHVybiAnMDA6MDA6MDAnOwogICAgICBjb25zdCBob3VycyA9IE1hdGguZmxvb3Ioc2Vjb25kcyAvIDM2MDApOwogICAgICBjb25zdCBtaW51dGVzID0gTWF0aC5mbG9vcigoc2Vjb25kcyAlIDM2MDApIC8gNjApOwogICAgICBjb25zdCBzZWNzID0gc2Vjb25kcyAlIDYwOwogICAgICByZXR1cm4gYCR7aG91cnMudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfToke21pbnV0ZXMudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfToke3NlY3MudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfWA7CiAgICB9LAoKICAgIGZvcm1hdERhdGVUaW1lKGRhdGVUaW1lKSB7CiAgICAgIGlmICghZGF0ZVRpbWUpIHJldHVybiAnJzsKICAgICAgcmV0dXJuIG5ldyBEYXRlKGRhdGVUaW1lKS50b0xvY2FsZVN0cmluZygnemgtQ04nKTsKICAgIH0sCgogICAgZ2V0U3RhdHVzVGV4dChzdGF0dXMpIHsKICAgICAgY29uc3Qgc3RhdHVzTWFwID0gewogICAgICAgICd1cGNvbWluZyc6ICfljbPlsIblvIDlp4snLAogICAgICAgICdhY3RpdmUnOiAn6L+b6KGM5LitJywKICAgICAgICAnZW5kZWQnOiAn5bey57uT5p2fJwogICAgICB9OwogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgc3RhdHVzOwogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["FlashSaleMultiPage.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy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file": "FlashSaleMultiPage.vue", "sourceRoot": "src/components/Marketing", "sourcesContent": ["<template>\n  <div class=\"flash-sale-multi-page\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2>限时秒杀管理（多商品轮次）</h2>\n      <button @click=\"openCreateModal\" class=\"btn btn-primary\">\n        创建新轮次\n      </button>\n    </div>\n\n    <!-- 统计卡片 -->\n    <div class=\"stats-cards\">\n      <div class=\"stat-card\">\n        <h3>总轮次</h3>\n        <p class=\"stat-number\">{{ statistics.totalRounds || 0 }}</p>\n      </div>\n      <div class=\"stat-card\">\n        <h3>进行中</h3>\n        <p class=\"stat-number active\">{{ statistics.activeRounds || 0 }}</p>\n      </div>\n      <div class=\"stat-card\">\n        <h3>即将开始</h3>\n        <p class=\"stat-number upcoming\">{{ statistics.upcomingRounds || 0 }}</p>\n      </div>\n      <div class=\"stat-card\">\n        <h3>总销售额</h3>\n        <p class=\"stat-number\">¥{{ (statistics.totalSales || 0).toFixed(2) }}</p>\n      </div>\n    </div>\n\n    <!-- 当前轮次 -->\n    <div class=\"current-rounds\" v-if=\"currentRounds.current && currentRounds.current.length > 0\">\n      <h3>当前进行中的轮次</h3>\n      <div class=\"round-list\">\n        <div v-for=\"round in currentRounds.current\" :key=\"round.id\" class=\"round-item active\">\n          <div class=\"round-info\">\n            <h4>{{ round.round_name }} (轮次 #{{ round.round_number }})</h4>\n            <p>商品数量: {{ round.goods_count }}</p>\n            <p>剩余时间: {{ formatTime(round.countdown) }}</p>\n          </div>\n          <div class=\"goods-preview\">\n            <div v-for=\"goods in round.goods_list.slice(0, 3)\" :key=\"goods.id\" class=\"goods-item\">\n              <img :src=\"goods.goods_image\" :alt=\"goods.goods_name\" />\n              <span>{{ goods.goods_name }}</span>\n              <span class=\"price\">¥{{ goods.flash_price }}</span>\n            </div>\n            <span v-if=\"round.goods_list.length > 3\" class=\"more-goods\">\n              +{{ round.goods_list.length - 3 }}个商品\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 轮次列表 -->\n    <div class=\"rounds-table\">\n      <h3>轮次列表</h3>\n      <table class=\"table\">\n        <thead>\n          <tr>\n            <th>轮次编号</th>\n            <th>轮次名称</th>\n            <th>商品数量</th>\n            <th>总库存</th>\n            <th>已售出</th>\n            <th>开始时间</th>\n            <th>结束时间</th>\n            <th>状态</th>\n            <th>操作</th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr v-for=\"round in roundsList.data\" :key=\"round.id\">\n            <td>#{{ round.round_number }}</td>\n            <td>{{ round.round_name }}</td>\n            <td>{{ round.goods_count }}</td>\n            <td>{{ round.total_stock }}</td>\n            <td>{{ round.total_sold }}</td>\n            <td>{{ formatDateTime(round.start_time) }}</td>\n            <td>{{ formatDateTime(round.end_time) }}</td>\n            <td>\n              <span :class=\"'status-' + round.status\">\n                {{ getStatusText(round.status) }}\n              </span>\n            </td>\n            <td>\n              <div class=\"round-actions\">\n                <button @click=\"viewRoundDetails(round)\" class=\"btn btn-info btn-sm\">查看详情</button>\n                <button\n                  v-if=\"round.status === 'upcoming' || round.status === 'active'\"\n                  @click=\"closeRound(round)\"\n                  class=\"btn btn-warning btn-sm\"\n                >\n                  关闭轮次\n                </button>\n              </div>\n            </td>\n          </tr>\n        </tbody>\n      </table>\n    </div>\n\n    <!-- 创建轮次模态框 -->\n    <div v-if=\"showAddModal\" class=\"modal-overlay\" @click=\"closeModal\">\n      <div class=\"modal-content\" @click.stop>\n        <div class=\"modal-header\">\n          <h3>创建新轮次</h3>\n          <button @click=\"closeModal\" class=\"close-btn\">&times;</button>\n        </div>\n        \n        <div class=\"modal-body\">\n          <!-- 整点秒杀时间设置 -->\n          <div class=\"hourly-flash-settings\">\n            <div class=\"setting-header\">\n              <h4>整点秒杀设置</h4>\n              <p class=\"setting-description\">每小时整点开始，持续40分钟，24小时不间断</p>\n            </div>\n\n            <div class=\"form-row\">\n              <div class=\"form-group\">\n                <label>活动开始日期 *</label>\n                <input\n                  v-model=\"newRound.start_date\"\n                  type=\"date\"\n                  class=\"form-control\"\n                  required\n                />\n              </div>\n              <div class=\"form-group\">\n                <label>活动结束日期 *</label>\n                <input\n                  v-model=\"newRound.end_date\"\n                  type=\"date\"\n                  class=\"form-control\"\n                  :min=\"newRound.start_date\"\n                  required\n                />\n              </div>\n            </div>\n\n            <!-- 自动生成的轮次名称预览 -->\n            <div v-if=\"generatedRoundName\" class=\"round-name-preview\">\n              <h5>轮次名称：</h5>\n              <div class=\"name-display\">{{ generatedRoundName }}</div>\n            </div>\n\n            <!-- 时段预览 -->\n            <div v-if=\"hourlySlotPreview.length > 0\" class=\"slot-preview\">\n              <h5>将生成以下秒杀时段：</h5>\n              <div class=\"slot-list-container\">\n                <div class=\"slot-list\">\n                  <div v-for=\"(slot, index) in hourlySlotPreview.slice(0, 10)\" :key=\"index\" class=\"slot-item\">\n                    <span class=\"slot-number\">第{{ index + 1 }}场</span>\n                    <span class=\"slot-time\">{{ formatSlotTime(slot.start) }} - {{ formatSlotTime(slot.end) }}</span>\n                    <span class=\"slot-duration\">40分钟</span>\n                    <span v-if=\"isSlotInPast(slot.start)\" class=\"slot-status past\">已过期</span>\n                    <span v-else-if=\"isSlotActive(slot.start, slot.end)\" class=\"slot-status active\">进行中</span>\n                    <span v-else class=\"slot-status upcoming\">待开始</span>\n                  </div>\n                  <div v-if=\"hourlySlotPreview.length > 10\" class=\"more-slots\">\n                    ... 还有 {{ hourlySlotPreview.length - 10 }} 个时段\n                  </div>\n                </div>\n              </div>\n              <div class=\"slot-summary\">\n                <span>共 {{ hourlySlotPreview.length }} 个时段</span>\n                <span class=\"valid-slots\">（有效时段：{{ validSlotsCount }} 个）</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- 商品选择 -->\n          <div class=\"form-group\">\n            <label>选择商品 * (点击商品卡片进行选择)</label>\n\n            <!-- 加载状态 -->\n            <div v-if=\"loadingGoods\" class=\"loading-state\">\n              <i class=\"loading-icon\">⏳</i>\n              <span>正在加载商品列表...</span>\n            </div>\n\n            <!-- 商品列表 -->\n            <div v-else-if=\"goodsList && goodsList.length > 0\" class=\"goods-selection-grid\">\n              <div\n                v-for=\"goods in goodsList\"\n                :key=\"goods.id\"\n                class=\"goods-card\"\n                :class=\"{\n                  'selected': isGoodsSelected(goods.id),\n                  'disabled': !goods.can_select\n                }\"\n                @click=\"toggleGoods(goods)\"\n              >\n                <!-- 商品基本信息 -->\n                <div class=\"goods-card-header\">\n                  <div class=\"goods-image-container\">\n                    <img :src=\"goods.list_pic_url\" :alt=\"goods.name\" class=\"goods-card-image\" />\n                    <div v-if=\"isGoodsSelected(goods.id)\" class=\"selected-badge\">\n                      <i class=\"checkmark\">✓</i>\n                    </div>\n                    <div v-if=\"!goods.can_select\" class=\"disabled-overlay\">\n                      <span>已参与其他秒杀</span>\n                    </div>\n                  </div>\n                  <div class=\"goods-card-info\">\n                    <h4 class=\"goods-name\">{{ goods.name }}</h4>\n                    <p class=\"original-price\">原价: ¥{{ goods.retail_price }}</p>\n                    <div v-if=\"!goods.can_select\" class=\"warning-text\">\n                      <i class=\"warning-icon\">⚠</i>\n                      <span>已参与其他秒杀活动</span>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 秒杀设置面板 -->\n                <div v-if=\"isGoodsSelected(goods.id)\" class=\"goods-settings-panel\">\n                  <div class=\"settings-title\">秒杀设置</div>\n                  <div class=\"settings-grid\">\n                    <div class=\"setting-item full-width\">\n                      <label>折扣设置</label>\n                      <div class=\"discount-setting\">\n                        <div class=\"discount-input-group\">\n                          <input\n                            v-model.number=\"getSelectedGoods(goods.id).discount_rate\"\n                            type=\"number\"\n                            step=\"1\"\n                            min=\"10\"\n                            max=\"90\"\n                            class=\"discount-input\"\n                            @input=\"updateFlashPriceByDiscount(goods.id)\"\n                            @click.stop\n                          />\n                          <span class=\"discount-unit\">% OFF</span>\n                        </div>\n                        <div class=\"price-preview\">\n                          原价: ¥{{ parseFloat(goods.retail_price).toFixed(2) }} →\n                          秒杀价: ¥{{ getSelectedGoods(goods.id).flash_price }}\n                        </div>\n                        <div class=\"price-range-hint\" v-if=\"goods.price_range\">\n                          商品价格区间: {{ goods.price_range }}\n                        </div>\n                      </div>\n                    </div>\n\n                    <div class=\"setting-item\">\n                      <label>秒杀库存</label>\n                      <input\n                        v-model.number=\"getSelectedGoods(goods.id).stock\"\n                        type=\"number\"\n                        min=\"1\"\n                        max=\"9999\"\n                        class=\"stock-input\"\n                        @click.stop\n                      />\n                    </div>\n\n                    <div class=\"setting-item\">\n                      <label>限购数量</label>\n                      <input\n                        v-model.number=\"getSelectedGoods(goods.id).limit_quantity\"\n                        type=\"number\"\n                        min=\"1\"\n                        max=\"99\"\n                        class=\"limit-input\"\n                        @click.stop\n                      />\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- 空状态显示 -->\n            <div v-else class=\"empty-state\">\n              <div class=\"empty-icon\">📦</div>\n              <p class=\"empty-text\">暂无可选商品</p>\n              <p class=\"empty-hint\">请确保有上架的商品，且未参与其他秒杀活动</p>\n              <button @click=\"loadGoodsList\" class=\"btn btn-secondary btn-sm\">重新加载</button>\n            </div>\n\n            <!-- 选择提示 -->\n            <div class=\"selection-hint\">\n              <p v-if=\"newRound.goods_list.length === 0\" class=\"hint-text\">\n                <i class=\"info-icon\">ℹ</i>\n                请点击商品卡片选择参与秒杀的商品，可以选择多个商品\n              </p>\n              <p v-else class=\"selected-count\">\n                已选择 <strong>{{ newRound.goods_list.length }}</strong> 个商品\n              </p>\n            </div>\n          </div>\n\n          <!-- 已选商品汇总 -->\n          <div v-if=\"newRound.goods_list.length > 0\" class=\"selected-summary\">\n            <h4>已选择 {{ newRound.goods_list.length }} 个商品</h4>\n            <div class=\"summary-list\">\n              <div v-for=\"goods in newRound.goods_list\" :key=\"goods.goods_id\" class=\"summary-item\">\n                <span>{{ goods.goods_name }}</span>\n                <span>¥{{ goods.flash_price }} ({{ calculateDiscountRate(goods.original_price, goods.flash_price) }}% OFF)</span>\n                <span>库存: {{ goods.stock }}</span>\n                <button @click=\"removeGoods(goods.goods_id)\" class=\"remove-btn\">移除</button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"modal-footer\">\n          <button @click=\"closeModal\" class=\"btn btn-secondary\">取消</button>\n          <button\n            @click=\"createRound\"\n            class=\"btn btn-primary\"\n            :disabled=\"!canCreateRound || isCreating\"\n          >\n            {{ isCreating ? `正在创建... (${creationProgress.current}/${creationProgress.total})` : '创建整点秒杀轮次' }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 轮次详情模态框 -->\n    <div v-if=\"showDetailModal\" class=\"modal-overlay\" @click=\"showDetailModal = false\">\n      <div class=\"modal-content large\" @click.stop>\n        <div class=\"modal-header\">\n          <h3>轮次详情 - {{ selectedRound.round_name }}</h3>\n          <button @click=\"showDetailModal = false\" class=\"close-btn\">&times;</button>\n        </div>\n        \n        <div class=\"modal-body\">\n          <div class=\"round-details\">\n            <div class=\"detail-section\">\n              <h4>基本信息</h4>\n              <p>轮次编号: #{{ selectedRound.round_number }}</p>\n              <p>开始时间: {{ formatDateTime(selectedRound.start_time) }}</p>\n              <p>结束时间: {{ formatDateTime(selectedRound.end_time) }}</p>\n              <p>状态: {{ getStatusText(selectedRound.status) }}</p>\n            </div>\n            \n            <div class=\"detail-section\">\n              <h4>商品列表</h4>\n              <table class=\"table\">\n                <thead>\n                  <tr>\n                    <th>商品</th>\n                    <th>原价</th>\n                    <th>秒杀价</th>\n                    <th>折扣</th>\n                    <th>库存</th>\n                    <th>已售</th>\n                    <th>限购</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr v-for=\"goods in selectedRound.goods_list\" :key=\"goods.id\">\n                    <td>\n                      <div class=\"goods-cell\">\n                        <img :src=\"goods.goods_image\" :alt=\"goods.goods_name\" />\n                        <span>{{ goods.goods_name }}</span>\n                      </div>\n                    </td>\n                    <td>¥{{ goods.original_price }}</td>\n                    <td>¥{{ goods.flash_price }}</td>\n                    <td>{{ goods.discount_rate }}%</td>\n                    <td>{{ goods.stock }}</td>\n                    <td>{{ goods.sold_count }}</td>\n                    <td>{{ goods.limit_quantity }}</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FlashSaleMultiPage',\n  data() {\n    return {\n      statistics: {},\n      currentRounds: { current: [], upcoming: [] },\n      roundsList: { data: [], count: 0 },\n      goodsList: [],\n      loadingGoods: false,\n      showAddModal: false,\n      showDetailModal: false,\n      selectedRound: {},\n      isCreating: false,\n      newRound: {\n        start_date: '',\n        end_date: '',\n        goods_list: []\n      },\n      refreshTimer: null,\n      creationProgress: {\n        current: 0,\n        total: 0\n      }\n    };\n  },\n  \n  computed: {\n    canCreateRound() {\n      const hasStartDate = this.newRound.start_date;\n      const hasEndDate = this.newRound.end_date;\n      const hasGoods = this.newRound.goods_list.length > 0;\n      const goodsValid = this.newRound.goods_list.every(g => g.flash_price > 0 && g.stock > 0);\n      const dateValid = hasStartDate && hasEndDate && new Date(this.newRound.start_date) <= new Date(this.newRound.end_date);\n\n      console.log('canCreateRound检查:', {\n        hasStartDate,\n        hasEndDate,\n        hasGoods,\n        goodsValid,\n        dateValid,\n        goodsList: this.newRound.goods_list\n      });\n\n      return hasStartDate && hasEndDate && hasGoods && goodsValid && dateValid;\n    },\n\n    // 自动生成轮次名称\n    generatedRoundName() {\n      if (!this.newRound.start_date || !this.newRound.end_date) {\n        return '';\n      }\n\n      const startDate = new Date(this.newRound.start_date);\n      const endDate = new Date(this.newRound.end_date);\n\n      if (startDate.getTime() === endDate.getTime()) {\n        // 单日活动\n        const dateStr = startDate.toLocaleDateString('zh-CN', {\n          month: 'long',\n          day: 'numeric'\n        });\n        return `${dateStr}整点秒杀`;\n      } else {\n        // 多日活动\n        const startStr = startDate.toLocaleDateString('zh-CN', {\n          month: 'long',\n          day: 'numeric'\n        });\n        const endStr = endDate.toLocaleDateString('zh-CN', {\n          month: 'long',\n          day: 'numeric'\n        });\n        return `${startStr}至${endStr}整点秒杀`;\n      }\n    },\n\n    // 整点秒杀时段预览（24小时全天候）\n    hourlySlotPreview() {\n      if (!this.newRound.start_date || !this.newRound.end_date) {\n        return [];\n      }\n\n      const slots = [];\n      const startDate = new Date(this.newRound.start_date);\n      const endDate = new Date(this.newRound.end_date);\n\n      // 设置结束日期为当天的23:59:59\n      endDate.setHours(23, 59, 59, 999);\n\n      let currentDate = new Date(startDate);\n      currentDate.setHours(0, 0, 0, 0); // 从00:00开始\n\n      while (currentDate <= endDate) {\n        for (let hour = 0; hour < 24; hour++) {\n          const slotStart = new Date(currentDate);\n          slotStart.setHours(hour, 0, 0, 0);\n\n          const slotEnd = new Date(currentDate);\n          slotEnd.setHours(hour, 40, 0, 0);\n\n          // 检查是否超出结束日期\n          if (slotStart > endDate) break;\n\n          slots.push({\n            start: slotStart.toISOString().slice(0, 19).replace('T', ' '),\n            end: slotEnd.toISOString().slice(0, 19).replace('T', ' '),\n            startTime: slotStart,\n            endTime: slotEnd\n          });\n        }\n\n        // 移动到下一天\n        currentDate.setDate(currentDate.getDate() + 1);\n      }\n\n      return slots;\n    },\n\n    // 有效时段数量（未过期的时段）\n    validSlotsCount() {\n      const now = new Date();\n      return this.hourlySlotPreview.filter(slot => new Date(slot.start) > now).length;\n    }\n  },\n\n  mounted() {\n    console.log('FlashSaleMultiPage组件已挂载');\n    console.log('初始showAddModal值:', this.showAddModal);\n    this.loadData();\n    this.startAutoRefresh();\n  },\n\n  beforeDestroy() {\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer);\n    }\n  },\n\n  methods: {\n    async loadData() {\n      await Promise.all([\n        this.loadStatistics(),\n        this.loadCurrentRounds(),\n        this.loadRoundsList(),\n        this.loadGoodsList()\n      ]);\n    },\n\n    async loadStatistics() {\n      try {\n        const response = await this.axios.get('flashsalemulti/statistics');\n        if (response.data.errno === 0) {\n          this.statistics = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载统计数据失败:', error);\n      }\n    },\n\n    async loadCurrentRounds() {\n      try {\n        const response = await this.axios.get('flashsalemulti/current');\n        if (response.data.errno === 0) {\n          this.currentRounds = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载当前轮次失败:', error);\n      }\n    },\n\n    async loadRoundsList() {\n      try {\n        const response = await this.axios.get('flashsalemulti/list');\n        if (response.data.errno === 0) {\n          this.roundsList = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载轮次列表失败:', error);\n      }\n    },\n\n    async loadGoodsList() {\n      try {\n        this.loadingGoods = true;\n        console.log('开始加载商品列表...');\n\n        const response = await this.axios.get('flashsalemulti/goods');\n        console.log('商品列表API响应:', response.data);\n\n        if (response.data.errno === 0) {\n          this.goodsList = response.data.data || [];\n          console.log('商品列表加载成功，数量:', this.goodsList.length);\n\n          if (this.goodsList.length === 0) {\n            this.$message.warning('暂无可选商品，请确保有上架的商品且未参与其他秒杀活动');\n          }\n        } else {\n          console.error('API返回错误:', response.data.errmsg);\n          this.$message.error(response.data.errmsg || '加载商品列表失败');\n          this.goodsList = [];\n        }\n      } catch (error) {\n        console.error('加载商品列表失败:', error);\n        this.$message.error('网络错误，请检查服务器连接');\n        this.goodsList = [];\n      } finally {\n        this.loadingGoods = false;\n      }\n    },\n\n    startAutoRefresh() {\n      this.refreshTimer = setInterval(() => {\n        this.loadCurrentRounds();\n        this.loadStatistics();\n      }, 30000); // 30秒刷新一次\n    },\n\n    isGoodsSelected(goodsId) {\n      return this.newRound.goods_list.some(g => g.goods_id === goodsId);\n    },\n\n    getSelectedGoods(goodsId) {\n      return this.newRound.goods_list.find(g => g.goods_id === goodsId);\n    },\n\n    toggleGoods(goods) {\n      if (!goods.can_select) return;\n\n      if (this.isGoodsSelected(goods.id)) {\n        this.removeGoods(goods.id);\n      } else {\n        const originalPrice = parseFloat(goods.retail_price) || 0;\n        const defaultDiscount = 20; // 默认20%折扣\n        const flashPrice = Math.round(originalPrice * (100 - defaultDiscount) / 100 * 100) / 100;\n\n        this.newRound.goods_list.push({\n          goods_id: goods.id,\n          goods_name: goods.name,\n          original_price: originalPrice,\n          flash_price: flashPrice,\n          discount_rate: defaultDiscount,\n          stock: 100,\n          limit_quantity: 1\n        });\n      }\n    },\n\n    removeGoods(goodsId) {\n      const index = this.newRound.goods_list.findIndex(g => g.goods_id === goodsId);\n      if (index > -1) {\n        this.newRound.goods_list.splice(index, 1);\n      }\n    },\n\n    calculateDiscountRate(originalPrice, flashPrice) {\n      if (!originalPrice || originalPrice <= 0 || !flashPrice || flashPrice <= 0) return 0;\n      const rate = Math.round((1 - flashPrice / originalPrice) * 100);\n      return isNaN(rate) ? 0 : rate;\n    },\n\n    updateFlashPriceByDiscount(goodsId) {\n      const selectedGoods = this.getSelectedGoods(goodsId);\n      if (selectedGoods && selectedGoods.original_price > 0) {\n        const discountRate = selectedGoods.discount_rate || 0;\n        const flashPrice = Math.round(selectedGoods.original_price * (100 - discountRate) / 100 * 100) / 100;\n        selectedGoods.flash_price = flashPrice;\n      }\n    },\n\n    getCurrentDateTime() {\n      const now = new Date();\n      now.setMinutes(now.getMinutes() - now.getTimezoneOffset());\n      return now.toISOString().slice(0, 16);\n    },\n\n    getCurrentDate() {\n      const now = new Date();\n      return now.toISOString().slice(0, 10);\n    },\n\n    openCreateModal() {\n      console.log('点击创建新轮次按钮');\n      // 设置默认日期为今天\n      const today = new Date().toISOString().slice(0, 10);\n      this.newRound.start_date = today;\n      this.newRound.end_date = today;\n      this.showAddModal = true;\n      console.log('showAddModal设置为:', this.showAddModal);\n    },\n\n    formatDateTime(dateTimeStr) {\n      if (!dateTimeStr) return '';\n      const date = new Date(dateTimeStr);\n      return date.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    formatSlotTime(dateTimeStr) {\n      if (!dateTimeStr) return '';\n      const date = new Date(dateTimeStr);\n      return date.toLocaleString('zh-CN', {\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    isSlotInPast(startTime) {\n      const now = new Date();\n      const slotStart = new Date(startTime);\n      return slotStart < now;\n    },\n\n    isSlotActive(startTime, endTime) {\n      const now = new Date();\n      const slotStart = new Date(startTime);\n      const slotEnd = new Date(endTime);\n      return now >= slotStart && now <= slotEnd;\n    },\n\n    async createRound() {\n      if (!this.canCreateRound) {\n        this.$message.error('请完善轮次信息');\n        return;\n      }\n\n      // 生成整点秒杀时段数据\n      const hourlySlots = this.hourlySlotPreview;\n      const now = new Date();\n\n      // 过滤掉已过期的时段\n      const validSlots = hourlySlots.filter(slot => new Date(slot.start) > now);\n\n      if (validSlots.length === 0) {\n        this.$message.error('所选时间段内没有有效的秒杀时段');\n        return;\n      }\n\n      // 如果轮次数量过多，询问用户确认\n      if (validSlots.length > 50) {\n        const confirmed = confirm(`将要创建${validSlots.length}个轮次，这可能需要较长时间。是否继续？`);\n        if (!confirmed) {\n          return;\n        }\n      }\n\n      try {\n        this.isCreating = true;\n        this.creationProgress.current = 0;\n        this.creationProgress.total = validSlots.length;\n\n        let createdCount = 0;\n        let failedCount = 0;\n        const failedReasons = [];\n\n        // 批量处理，每次处理10个轮次\n        const batchSize = 10;\n        for (let batchStart = 0; batchStart < validSlots.length; batchStart += batchSize) {\n          const batchEnd = Math.min(batchStart + batchSize, validSlots.length);\n          const batch = validSlots.slice(batchStart, batchEnd);\n\n          // 并行创建当前批次的轮次\n          const batchPromises = batch.map(async (slot, batchIndex) => {\n            const globalIndex = batchStart + batchIndex;\n\n            try {\n              const roundData = {\n                round_name: `${this.generatedRoundName} - 第${globalIndex + 1}场`,\n                start_time: slot.start,\n                end_time: slot.end,\n                is_hourly_flash: true,\n                slot_index: globalIndex + 1,\n                total_slots: validSlots.length,\n                goods_list: this.newRound.goods_list.map(goods => ({\n                  goods_id: goods.goods_id,\n                  goods_name: goods.goods_name,\n                  goods_image: goods.goods_image,\n                  original_price: goods.original_price,\n                  flash_price: goods.flash_price,\n                  stock: goods.stock,\n                  discount_rate: goods.discount_rate\n                }))\n              };\n\n              const response = await this.axios.post('flashsalemulti/create', roundData);\n\n              if (response.data.errno === 0) {\n                return { success: true, index: globalIndex + 1 };\n              } else {\n                return { success: false, index: globalIndex + 1, error: response.data.errmsg };\n              }\n            } catch (error) {\n              return { success: false, index: globalIndex + 1, error: error.message };\n            }\n          });\n\n          // 等待当前批次完成\n          const batchResults = await Promise.all(batchPromises);\n\n          // 统计结果\n          batchResults.forEach(result => {\n            this.creationProgress.current++;\n            if (result.success) {\n              createdCount++;\n            } else {\n              failedCount++;\n              if (failedReasons.length < 5) { // 只记录前5个错误\n                failedReasons.push(`第${result.index}场: ${result.error}`);\n              }\n            }\n          });\n\n          // 短暂延迟，避免服务器压力过大\n          if (batchEnd < validSlots.length) {\n            await new Promise(resolve => setTimeout(resolve, 100));\n          }\n        }\n\n        // 显示结果\n        if (createdCount > 0) {\n          let message = `成功创建${createdCount}个整点秒杀轮次`;\n          if (failedCount > 0) {\n            message += `，${failedCount}个失败`;\n            if (failedReasons.length > 0) {\n              console.warn('创建失败的轮次:', failedReasons);\n              message += `\\n主要错误: ${failedReasons[0]}`;\n            }\n          }\n          this.$message.success(message);\n          this.closeModal();\n          this.loadData();\n        } else {\n          let errorMessage = '所有轮次创建失败';\n          if (failedReasons.length > 0) {\n            errorMessage += `\\n错误信息: ${failedReasons[0]}`;\n          }\n          this.$message.error(errorMessage);\n        }\n\n      } catch (error) {\n        console.error('创建整点秒杀轮次失败:', error);\n        this.$message.error('创建过程中发生错误: ' + error.message);\n      } finally {\n        this.isCreating = false;\n        this.creationProgress.current = 0;\n        this.creationProgress.total = 0;\n      }\n    },\n\n    closeModal() {\n      this.showAddModal = false;\n      this.newRound = {\n        start_date: '',\n        end_date: '',\n        goods_list: []\n      };\n    },\n\n    viewRoundDetails(round) {\n      this.selectedRound = round;\n      this.showDetailModal = true;\n    },\n\n    async closeRound(round) {\n      if (!confirm(`确定要关闭轮次\"${round.round_name}\"吗？关闭后轮次将立即结束。`)) {\n        return;\n      }\n\n      try {\n        this.$message.info('正在关闭轮次...');\n        const response = await this.axios.post('flashsalemulti/close', {\n          round_id: round.id\n        });\n\n        if (response.data.errno === 0) {\n          this.$message.success('轮次已关闭');\n          this.loadData(); // 重新加载数据\n        } else {\n          this.$message.error(response.data.errmsg || '关闭失败');\n        }\n      } catch (error) {\n        console.error('关闭轮次失败:', error);\n        this.$message.error('关闭失败');\n      }\n    },\n\n\n\n    formatTime(seconds) {\n      if (!seconds || seconds <= 0) return '00:00:00';\n      const hours = Math.floor(seconds / 3600);\n      const minutes = Math.floor((seconds % 3600) / 60);\n      const secs = seconds % 60;\n      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    },\n\n    formatDateTime(dateTime) {\n      if (!dateTime) return '';\n      return new Date(dateTime).toLocaleString('zh-CN');\n    },\n\n    getStatusText(status) {\n      const statusMap = {\n        'upcoming': '即将开始',\n        'active': '进行中',\n        'ended': '已结束'\n      };\n      return statusMap[status] || status;\n    }\n  }\n};\n</script>\n\n<style scoped>\n.flash-sale-multi-page {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #333;\n}\n\n.btn {\n  padding: 8px 16px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  color: white;\n}\n\n.btn-secondary {\n  background-color: #6c757d;\n  color: white;\n}\n\n.btn-sm {\n  padding: 4px 8px;\n  font-size: 12px;\n}\n\n.round-actions {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.round-actions .btn {\n  margin: 0;\n}\n\n.btn-info {\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n  color: white;\n}\n\n.btn-info:hover {\n  background-color: #138496;\n  border-color: #117a8b;\n}\n\n.btn-warning {\n  background-color: #ffc107;\n  border-color: #ffc107;\n  color: #212529;\n}\n\n.btn-warning:hover {\n  background-color: #e0a800;\n  border-color: #d39e00;\n}\n\n.btn-danger {\n  background-color: #dc3545;\n  border-color: #dc3545;\n  color: white;\n}\n\n.btn-danger:hover {\n  background-color: #c82333;\n  border-color: #bd2130;\n}\n\n/* 整点秒杀设置样式 */\n.hourly-flash-settings {\n  margin-bottom: 20px;\n}\n\n.setting-header {\n  margin-bottom: 15px;\n}\n\n.setting-header h4 {\n  margin: 0 0 5px 0;\n  color: #333;\n}\n\n.setting-description {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.time-range-selector {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.time-separator {\n  color: #666;\n  font-weight: bold;\n}\n\n.slot-preview {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f8f9fa;\n  border-radius: 5px;\n  border: 1px solid #e9ecef;\n}\n\n.slot-preview h5 {\n  margin: 0 0 10px 0;\n  color: #333;\n}\n\n.slot-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.slot-item {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 8px 12px;\n  background-color: white;\n  border-radius: 4px;\n  border: 1px solid #dee2e6;\n}\n\n.slot-number {\n  font-weight: bold;\n  color: #007bff;\n  min-width: 60px;\n}\n\n.slot-time {\n  flex: 1;\n  color: #333;\n}\n\n.slot-duration {\n  color: #28a745;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.slot-summary {\n  margin-top: 10px;\n  padding-top: 10px;\n  border-top: 1px solid #dee2e6;\n  text-align: center;\n  color: #666;\n  font-weight: bold;\n}\n\n.valid-slots {\n  color: #28a745;\n  margin-left: 10px;\n}\n\n.round-name-preview {\n  margin-bottom: 20px;\n}\n\n.round-name-preview h5 {\n  margin-bottom: 8px;\n  color: #333;\n  font-weight: bold;\n}\n\n.name-display {\n  padding: 10px 15px;\n  background-color: #f8f9fa;\n  border: 1px solid #dee2e6;\n  border-radius: 4px;\n  font-size: 16px;\n  font-weight: bold;\n  color: #007bff;\n}\n\n.slot-list-container {\n  max-height: 300px;\n  overflow-y: auto;\n  border: 1px solid #dee2e6;\n  border-radius: 4px;\n}\n\n.slot-status {\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 11px;\n  font-weight: bold;\n}\n\n.slot-status.past {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n\n.slot-status.active {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n.slot-status.upcoming {\n  background-color: #d1ecf1;\n  color: #0c5460;\n}\n\n.more-slots {\n  padding: 10px;\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  background-color: #f8f9fa;\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n/* 统计卡片 */\n.stats-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  text-align: center;\n}\n\n.stat-card h3 {\n  margin: 0 0 10px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.stat-number {\n  font-size: 24px;\n  font-weight: bold;\n  margin: 0;\n  color: #333;\n}\n\n.stat-number.active {\n  color: #28a745;\n}\n\n.stat-number.upcoming {\n  color: #ffc107;\n}\n\n/* 当前轮次 */\n.current-rounds {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.round-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.round-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n}\n\n.round-item.active {\n  border-color: #28a745;\n  background-color: #f8fff9;\n}\n\n.round-info h4 {\n  margin: 0 0 5px 0;\n  color: #333;\n}\n\n.round-info p {\n  margin: 2px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.goods-preview {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.goods-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  width: 80px;\n}\n\n.goods-item img {\n  width: 40px;\n  height: 40px;\n  object-fit: cover;\n  border-radius: 4px;\n  margin-bottom: 4px;\n}\n\n.goods-item span {\n  font-size: 12px;\n  color: #666;\n}\n\n.goods-item .price {\n  color: #e74c3c;\n  font-weight: bold;\n}\n\n.more-goods {\n  color: #007bff;\n  font-size: 12px;\n}\n\n/* 表格 */\n.rounds-table {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.table {\n  width: 100%;\n  border-collapse: collapse;\n  margin-top: 15px;\n}\n\n.table th,\n.table td {\n  padding: 12px;\n  text-align: left;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.table th {\n  background-color: #f8f9fa;\n  font-weight: 600;\n  color: #333;\n}\n\n.status-upcoming {\n  color: #ffc107;\n  font-weight: bold;\n}\n\n.status-active {\n  color: #28a745;\n  font-weight: bold;\n}\n\n.status-ended {\n  color: #6c757d;\n  font-weight: bold;\n}\n\n/* 模态框 */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background: white;\n  border-radius: 8px;\n  width: 90%;\n  max-width: 800px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n\n.modal-content.large {\n  max-width: 1000px;\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.modal-header h3 {\n  margin: 0;\n  color: #333;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  font-size: 24px;\n  cursor: pointer;\n  color: #666;\n}\n\n.modal-body {\n  padding: 20px;\n}\n\n.modal-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n  padding: 20px;\n  border-top: 1px solid #e9ecef;\n}\n\n/* 表单 */\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-row {\n  display: flex;\n  gap: 15px;\n}\n\n.form-row .form-group {\n  flex: 1;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  font-weight: 600;\n  color: #333;\n}\n\n.form-control {\n  width: 100%;\n  padding: 8px 12px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.form-control.small {\n  width: 80px;\n}\n\n.form-control:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n/* 商品选择网格 */\n.goods-selection-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 20px;\n  max-height: 500px;\n  overflow-y: auto;\n  padding: 10px;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background-color: #f8f9fa;\n}\n\n/* 商品卡片 */\n.goods-card {\n  background: white;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  padding: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n.goods-card:hover {\n  border-color: #007bff;\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);\n  transform: translateY(-2px);\n}\n\n.goods-card.selected {\n  border-color: #28a745;\n  background-color: #f8fff9;\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);\n}\n\n.goods-card.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  background-color: #f5f5f5;\n  border-color: #dee2e6;\n}\n\n.goods-card.disabled:hover {\n  transform: none;\n  box-shadow: none;\n  border-color: #dee2e6;\n}\n\n/* 商品卡片头部 */\n.goods-card-header {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 15px;\n}\n\n.goods-image-container {\n  position: relative;\n  margin-right: 15px;\n  flex-shrink: 0;\n}\n\n.goods-card-image {\n  width: 80px;\n  height: 80px;\n  object-fit: cover;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n}\n\n.selected-badge {\n  position: absolute;\n  top: -5px;\n  right: -5px;\n  width: 24px;\n  height: 24px;\n  background-color: #28a745;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2px solid white;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.2);\n}\n\n.checkmark {\n  color: white;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.disabled-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n/* 商品信息 */\n.goods-card-info {\n  flex: 1;\n}\n\n.goods-name {\n  margin: 0 0 8px 0;\n  color: #333;\n  font-size: 16px;\n  font-weight: 600;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.original-price {\n  margin: 0 0 5px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.warning-text {\n  display: flex;\n  align-items: center;\n  color: #dc3545;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.warning-icon {\n  margin-right: 4px;\n  font-size: 14px;\n}\n\n/* 商品设置面板 */\n.goods-settings-panel {\n  border-top: 1px solid #e9ecef;\n  padding-top: 15px;\n  margin-top: 15px;\n}\n\n.settings-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 12px;\n  display: flex;\n  align-items: center;\n}\n\n.settings-title::before {\n  content: \"⚙\";\n  margin-right: 6px;\n  color: #007bff;\n}\n\n.settings-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 12px;\n}\n\n.setting-item {\n  display: flex;\n  flex-direction: column;\n}\n\n.setting-item.full-width {\n  grid-column: 1 / -1;\n}\n\n.setting-item label {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 4px;\n  font-weight: 500;\n}\n\n/* 价格输入组 */\n.price-input-group {\n  display: flex;\n  align-items: center;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  overflow: hidden;\n  background: white;\n}\n\n.currency {\n  background-color: #f8f9fa;\n  padding: 6px 8px;\n  border-right: 1px solid #ddd;\n  font-size: 14px;\n  color: #666;\n}\n\n.price-input {\n  border: none;\n  padding: 6px 8px;\n  font-size: 14px;\n  flex: 1;\n  outline: none;\n}\n\n.price-input:focus {\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.discount-display {\n  margin-top: 4px;\n  font-size: 12px;\n  color: #e74c3c;\n  font-weight: bold;\n  text-align: center;\n  background-color: #fff5f5;\n  padding: 2px 6px;\n  border-radius: 12px;\n  border: 1px solid #fecaca;\n}\n\n/* 库存和限购输入 */\n.stock-input,\n.limit-input {\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  padding: 6px 8px;\n  font-size: 14px;\n  outline: none;\n  background: white;\n}\n\n.stock-input:focus,\n.limit-input:focus {\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n/* 折扣设置样式 */\n.discount-setting {\n  background: #f8f9fa;\n  padding: 12px;\n  border-radius: 6px;\n  border: 1px solid #e9ecef;\n}\n\n.discount-input-group {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.discount-input {\n  width: 80px;\n  padding: 6px 8px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n  text-align: center;\n  font-weight: 600;\n}\n\n.discount-unit {\n  margin-left: 8px;\n  font-size: 14px;\n  font-weight: 600;\n  color: #dc3545;\n}\n\n.price-preview {\n  font-size: 13px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.price-range-hint {\n  font-size: 12px;\n  color: #28a745;\n  font-style: italic;\n}\n\n/* 选择提示 */\n.selection-hint {\n  margin-top: 15px;\n  padding: 12px;\n  background-color: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #007bff;\n}\n\n.hint-text {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n  display: flex;\n  align-items: center;\n}\n\n.info-icon {\n  margin-right: 8px;\n  color: #007bff;\n  font-size: 16px;\n}\n\n.selected-count {\n  margin: 0;\n  color: #333;\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.selected-count strong {\n  color: #28a745;\n  font-size: 16px;\n}\n\n/* 已选商品汇总 */\n.selected-summary {\n  background-color: #f8f9fa;\n  padding: 15px;\n  border-radius: 4px;\n  margin-top: 20px;\n}\n\n.selected-summary h4 {\n  margin: 0 0 15px 0;\n  color: #333;\n}\n\n.summary-list {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.summary-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px;\n  background: white;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.remove-btn {\n  background-color: #dc3545;\n  color: white;\n  border: none;\n  padding: 4px 8px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 12px;\n}\n\n/* 轮次详情 */\n.round-details {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.detail-section h4 {\n  margin: 0 0 15px 0;\n  color: #333;\n  border-bottom: 2px solid #007bff;\n  padding-bottom: 5px;\n}\n\n.detail-section p {\n  margin: 5px 0;\n  color: #666;\n}\n\n/* 加载状态样式 */\n.loading-state {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40px 20px;\n  background: #f8f9fa;\n  border: 2px dashed #dee2e6;\n  border-radius: 8px;\n  color: #6c757d;\n  font-size: 16px;\n}\n\n.loading-state .loading-icon {\n  margin-right: 10px;\n  font-size: 20px;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n/* 空状态样式 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60px 20px;\n  background: #f8f9fa;\n  border: 2px dashed #dee2e6;\n  border-radius: 8px;\n  text-align: center;\n}\n\n.empty-state .empty-icon {\n  font-size: 48px;\n  margin-bottom: 16px;\n  opacity: 0.6;\n}\n\n.empty-state .empty-text {\n  font-size: 18px;\n  font-weight: 500;\n  color: #495057;\n  margin: 0 0 8px 0;\n}\n\n.empty-state .empty-hint {\n  font-size: 14px;\n  color: #6c757d;\n  margin: 0 0 20px 0;\n  line-height: 1.5;\n}\n\n.empty-state .btn-sm {\n  padding: 6px 16px;\n  font-size: 14px;\n}\n\n.goods-cell {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.goods-cell img {\n  width: 40px;\n  height: 40px;\n  object-fit: cover;\n  border-radius: 4px;\n}\n\n.goods-cell span {\n  font-size: 14px;\n}\n</style>\n"]}]}