{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\flashsalerounds.js"], "names": ["Base", "require", "module", "exports", "statisticsAction", "console", "log", "roundModel", "model", "orderModel", "totalRounds", "count", "activeRounds", "where", "status", "upcomingRounds", "endedRounds", "totalOrders", "todayOrders", "created_at", "think", "datetime", "Date", "totalSalesResult", "field", "select", "totalSales", "total_sales", "result", "parseFloat", "success", "error", "fail", "listAction", "page", "get", "limit", "rounds", "order", "countSelect", "now", "data", "for<PERSON>ach", "startTime", "round", "start_time", "endTime", "end_time", "countdown", "Math", "max", "floor", "stockProgress", "stock", "sold_count", "currentAction", "updateExpiredRounds", "currentRounds", "futureTime", "getTime", "current", "upcoming", "total", "length", "createAction", "post", "requiredFields", "configModel", "goodsModel", "goods", "id", "goods_id", "find", "isEmpty", "config", "lastRound", "nextRoundNumber", "round_number", "activeRound", "activeEndTime", "isNaN", "break_duration", "round_duration", "roundData", "goods_name", "name", "goods_image", "list_pic_url", "original_price", "retail_price", "flash_price", "limit_quantity", "toISOString", "slice", "replace", "created_by", "ctx", "state", "userInfo", "roundId", "add", "message", "update", "goodsAction", "is_delete", "is_on_sale", "configAction", "updateConfigAction"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;;AAEAC,OAAOC,OAAP,GAAiB,cAAcH,IAAd,CAAmB;;AAElC;;;;AAIMI,kBAAN,GAAyB;AAAA;;AAAA;AACvB,UAAI;AACFC,gBAAQC,GAAR,CAAY,oBAAZ;;AAEA,cAAMC,aAAa,MAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAMC,aAAa,MAAKD,KAAL,CAAW,mBAAX,CAAnB;;AAEA;AACA,cAAME,cAAc,MAAMH,WAAWI,KAAX,EAA1B;AACA,cAAMC,eAAe,MAAML,WAAWM,KAAX,CAAiB,EAAEC,QAAQ,QAAV,EAAjB,EAAuCH,KAAvC,EAA3B;AACA,cAAMI,iBAAiB,MAAMR,WAAWM,KAAX,CAAiB,EAAEC,QAAQ,UAAV,EAAjB,EAAyCH,KAAzC,EAA7B;AACA,cAAMK,cAAc,MAAMT,WAAWM,KAAX,CAAiB,EAAEC,QAAQ,OAAV,EAAjB,EAAsCH,KAAtC,EAA1B;;AAEA;AACA,cAAMM,cAAc,MAAMR,WAAWE,KAAX,EAA1B;AACA,cAAMO,cAAc,MAAMT,WAAWI,KAAX,CAAiB;AACzCM,sBAAY,CAAC,IAAD,EAAOC,MAAMC,QAAN,CAAe,IAAIC,IAAJ,EAAf,EAA2B,qBAA3B,CAAP;AAD6B,SAAjB,EAEvBX,KAFuB,EAA1B;;AAIA;AACA,cAAMY,mBAAmB,MAAMd,WAAWe,KAAX,CAAiB,kCAAjB,EAAqDC,MAArD,EAA/B;AACA,cAAMC,aAAaH,iBAAiB,CAAjB,KAAuBA,iBAAiB,CAAjB,EAAoBI,WAA3C,GAAyDJ,iBAAiB,CAAjB,EAAoBI,WAA7E,GAA2F,CAA9G;;AAEA,cAAMC,SAAS;AACblB,qBADa;AAEbE,sBAFa;AAGbG,wBAHa;AAIbC,qBAJa;AAKbC,qBALa;AAMbC,qBANa;AAObQ,sBAAYG,WAAWH,UAAX;AAPC,SAAf;;AAUArB,gBAAQC,GAAR,CAAY,OAAZ,EAAqBsB,MAArB;AACA,eAAO,MAAKE,OAAL,CAAaF,MAAb,CAAP;AAED,OAnCD,CAmCE,OAAOG,KAAP,EAAc;AACd1B,gBAAQ0B,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,MAAKC,IAAL,CAAU,UAAV,CAAP;AACD;AAvCsB;AAwCxB;;AAED;;;;AAIMC,YAAN,GAAmB;AAAA;;AAAA;AACjB,UAAI;AACF5B,gBAAQC,GAAR,CAAY,gBAAZ;;AAEA,cAAMC,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAM0B,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,cAAMC,QAAQ,OAAKD,GAAL,CAAS,OAAT,KAAqB,EAAnC;AACA,cAAMrB,SAAS,OAAKqB,GAAL,CAAS,QAAT,CAAf;;AAEA,YAAItB,QAAQ,EAAZ;AACA,YAAIC,MAAJ,EAAY;AACVD,gBAAMC,MAAN,GAAeA,MAAf;AACD;;AAED,cAAMuB,SAAS,MAAM9B,WAAWM,KAAX,CAAiBA,KAAjB,EAClByB,KADkB,CACZ,mBADY,EAElBJ,IAFkB,CAEbA,IAFa,EAEPE,KAFO,EAGlBG,WAHkB,EAArB;;AAKA;AACA,cAAMC,MAAM,IAAIlB,IAAJ,EAAZ;AACAe,eAAOI,IAAP,CAAYC,OAAZ,CAAoB,iBAAS;AAC3B,gBAAMC,YAAY,IAAIrB,IAAJ,CAASsB,MAAMC,UAAf,CAAlB;AACA,gBAAMC,UAAU,IAAIxB,IAAJ,CAASsB,MAAMG,QAAf,CAAhB;;AAEA,cAAIH,MAAM9B,MAAN,KAAiB,UAArB,EAAiC;AAC/B8B,kBAAMI,SAAN,GAAkBC,KAAKC,GAAL,CAAS,CAAT,EAAYD,KAAKE,KAAL,CAAW,CAACR,YAAYH,GAAb,IAAoB,IAA/B,CAAZ,CAAlB;AACD,WAFD,MAEO,IAAII,MAAM9B,MAAN,KAAiB,QAArB,EAA+B;AACpC8B,kBAAMI,SAAN,GAAkBC,KAAKC,GAAL,CAAS,CAAT,EAAYD,KAAKE,KAAL,CAAW,CAACL,UAAUN,GAAX,IAAkB,IAA7B,CAAZ,CAAlB;AACD,WAFM,MAEA;AACLI,kBAAMI,SAAN,GAAkB,CAAlB;AACD;;AAED;AACAJ,gBAAMQ,aAAN,GAAsBR,MAAMS,KAAN,GAAc,CAAd,GACpBJ,KAAKL,KAAL,CAAYA,MAAMU,UAAN,GAAmBV,MAAMS,KAA1B,GAAmC,GAA9C,CADoB,GACiC,GADvD;AAED,SAfD;;AAiBA,eAAO,OAAKvB,OAAL,CAAaO,MAAb,CAAP;AAED,OAvCD,CAuCE,OAAON,KAAP,EAAc;AACd1B,gBAAQ0B,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKC,IAAL,CAAU,UAAV,CAAP;AACD;AA3CgB;AA4ClB;;AAED;;;;AAIMuB,eAAN,GAAsB;AAAA;;AAAA;AACpB,UAAI;AACFlD,gBAAQC,GAAR,CAAY,gBAAZ;;AAEA,cAAMC,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAMgC,MAAM,IAAIlB,IAAJ,EAAZ;;AAEA;AACA,cAAM,OAAKkC,mBAAL,EAAN;;AAEA;AACA,cAAMC,gBAAgB,MAAMlD,WAAWM,KAAX,CAAiB;AAC3CC,kBAAQ,QADmC;AAE3C+B,sBAAY,CAAC,IAAD,EAAOzB,MAAMC,QAAN,CAAemB,GAAf,CAAP,CAF+B;AAG3CO,oBAAU,CAAC,GAAD,EAAM3B,MAAMC,QAAN,CAAemB,GAAf,CAAN;AAHiC,SAAjB,EAIzBF,KAJyB,CAInB,gBAJmB,EAIDb,MAJC,EAA5B;;AAMA;AACA,cAAMiC,aAAa,IAAIpC,IAAJ,CAASkB,IAAImB,OAAJ,KAAgB,KAAK,EAAL,GAAU,IAAnC,CAAnB;AACA,cAAM5C,iBAAiB,MAAMR,WAAWM,KAAX,CAAiB;AAC5CC,kBAAQ,UADoC;AAE5C+B,sBAAY,CAAC,GAAD,EAAMzB,MAAMC,QAAN,CAAemB,GAAf,CAAN,CAFgC;AAG5CK,sBAAY,CAAC,IAAD,EAAOzB,MAAMC,QAAN,CAAeqC,UAAf,CAAP;AAHgC,SAAjB,EAI1BpB,KAJ0B,CAIpB,gBAJoB,EAIFb,MAJE,EAA7B;;AAMA;AACA,SAAC,GAAGgC,aAAJ,EAAmB,GAAG1C,cAAtB,EAAsC2B,OAAtC,CAA8C,iBAAS;AACrD,gBAAMC,YAAY,IAAIrB,IAAJ,CAASsB,MAAMC,UAAf,CAAlB;AACA,gBAAMC,UAAU,IAAIxB,IAAJ,CAASsB,MAAMG,QAAf,CAAhB;;AAEA,cAAIH,MAAM9B,MAAN,KAAiB,UAArB,EAAiC;AAC/B8B,kBAAMI,SAAN,GAAkBC,KAAKC,GAAL,CAAS,CAAT,EAAYD,KAAKE,KAAL,CAAW,CAACR,YAAYH,GAAb,IAAoB,IAA/B,CAAZ,CAAlB;AACD,WAFD,MAEO,IAAII,MAAM9B,MAAN,KAAiB,QAArB,EAA+B;AACpC8B,kBAAMI,SAAN,GAAkBC,KAAKC,GAAL,CAAS,CAAT,EAAYD,KAAKE,KAAL,CAAW,CAACL,UAAUN,GAAX,IAAkB,IAA7B,CAAZ,CAAlB;AACD;;AAED;AACAI,gBAAMQ,aAAN,GAAsBR,MAAMS,KAAN,GAAc,CAAd,GACpBJ,KAAKL,KAAL,CAAYA,MAAMU,UAAN,GAAmBV,MAAMS,KAA1B,GAAmC,GAA9C,CADoB,GACiC,GADvD;AAED,SAbD;;AAeA,cAAMzB,SAAS;AACbgC,mBAASH,aADI;AAEbI,oBAAU9C,cAFG;AAGb+C,iBAAOL,cAAcM,MAAd,GAAuBhD,eAAegD;AAHhC,SAAf;;AAMA,eAAO,OAAKjC,OAAL,CAAaF,MAAb,CAAP;AAED,OAhDD,CAgDE,OAAOG,KAAP,EAAc;AACd1B,gBAAQ0B,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKC,IAAL,CAAU,UAAV,CAAP;AACD;AApDmB;AAqDrB;;AAED;;;;AAIMgC,cAAN,GAAqB;AAAA;;AAAA;AACnB,UAAI;AACF3D,gBAAQC,GAAR,CAAY,eAAZ;;AAEA,cAAMmC,OAAO,OAAKwB,IAAL,EAAb;AACA5D,gBAAQC,GAAR,CAAY,SAAZ,EAAuBmC,IAAvB;;AAEA;AACA,cAAMyB,iBAAiB,CAAC,UAAD,EAAa,aAAb,EAA4B,OAA5B,CAAvB;AACA,aAAK,MAAM1C,KAAX,IAAoB0C,cAApB,EAAoC;AAClC,cAAI,CAACzB,KAAKjB,KAAL,CAAL,EAAkB;AAChB,mBAAO,OAAKQ,IAAL,CAAW,MAAKR,KAAM,EAAtB,CAAP;AACD;AACF;;AAED,cAAMjB,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAM2D,cAAc,OAAK3D,KAAL,CAAW,mBAAX,CAApB;AACA,cAAM4D,aAAa,OAAK5D,KAAL,CAAW,OAAX,CAAnB;;AAEA;AACA,cAAM6D,QAAQ,MAAMD,WAAWvD,KAAX,CAAiB,EAAEyD,IAAI7B,KAAK8B,QAAX,EAAjB,EAAwCC,IAAxC,EAApB;AACA,YAAIpD,MAAMqD,OAAN,CAAcJ,KAAd,CAAJ,EAA0B;AACxB,iBAAO,OAAKrC,IAAL,CAAU,OAAV,CAAP;AACD;;AAED;AACA,cAAM0C,SAAS,MAAMP,YAAYtD,KAAZ,CAAkB,EAAEyD,IAAI,CAAN,EAAlB,EAA6BE,IAA7B,EAArB;AACA,YAAIpD,MAAMqD,OAAN,CAAcC,MAAd,CAAJ,EAA2B;AACzB,iBAAO,OAAK1C,IAAL,CAAU,SAAV,CAAP;AACD;;AAED;AACA,cAAM2C,YAAY,MAAMpE,WAAW+B,KAAX,CAAiB,mBAAjB,EAAsCkC,IAAtC,EAAxB;AACA,cAAMI,kBAAmBD,aAAaA,UAAUE,YAAxB,GAAwCF,UAAUE,YAAV,GAAyB,CAAjE,GAAqE,CAA7F;;AAEA;AACA,cAAMC,cAAc,MAAMvE,WAAWM,KAAX,CAAiB,EAAEC,QAAQ,QAAV,EAAjB,EAAuC0D,IAAvC,EAA1B;AACA,YAAI7B,SAAJ;;AAEA,YAAImC,eAAeA,YAAY/B,QAA/B,EAAyC;AACvC;AACA,gBAAMgC,gBAAgB,IAAIzD,IAAJ,CAASwD,YAAY/B,QAArB,CAAtB;AACA,cAAI,CAACiC,MAAMD,cAAcpB,OAAd,EAAN,CAAL,EAAqC;AACnChB,wBAAY,IAAIrB,IAAJ,CAASyD,cAAcpB,OAAd,KAA0Be,OAAOO,cAAP,GAAwB,IAA3D,CAAZ;AACD,WAFD,MAEO;AACLtC,wBAAY,IAAIrB,IAAJ,CAASA,KAAKkB,GAAL,KAAa,KAAK,IAA3B,CAAZ;AACD;AACF,SARD,MAQO;AACL;AACAG,sBAAY,IAAIrB,IAAJ,CAASA,KAAKkB,GAAL,KAAa,KAAK,IAA3B,CAAZ,CAFK,CAEyC;AAC/C;;AAED,cAAMM,UAAU,IAAIxB,IAAJ,CAASqB,UAAUgB,OAAV,KAAsBe,OAAOQ,cAAP,GAAwB,IAAvD,CAAhB;;AAEA;AACA,YAAIF,MAAMrC,UAAUgB,OAAV,EAAN,KAA8BqB,MAAMlC,QAAQa,OAAR,EAAN,CAAlC,EAA4D;AAC1D,iBAAO,OAAK3B,IAAL,CAAU,QAAV,CAAP;AACD;;AAED,cAAMmD,YAAY;AAChBN,wBAAcD,eADE;AAEhBL,oBAAU9B,KAAK8B,QAFC;AAGhBa,sBAAYf,MAAMgB,IAHF;AAIhBC,uBAAajB,MAAMkB,YAAN,IAAsB,EAJnB;AAKhBC,0BAAgBnB,MAAMoB,YALN;AAMhBC,uBAAajD,KAAKiD,WANF;AAOhBrC,iBAAOZ,KAAKY,KAPI;AAQhBsC,0BAAgBlD,KAAKkD,cAAL,IAAuB,CARvB;AAShB9C,sBAAYF,UAAUiD,WAAV,GAAwBC,KAAxB,CAA8B,CAA9B,EAAiC,EAAjC,EAAqCC,OAArC,CAA6C,GAA7C,EAAkD,GAAlD,CATI;AAUhB/C,oBAAUD,QAAQ8C,WAAR,GAAsBC,KAAtB,CAA4B,CAA5B,EAA+B,EAA/B,EAAmCC,OAAnC,CAA2C,GAA3C,EAAgD,GAAhD,CAVM;AAWhBhF,kBAAQ,UAXQ;AAYhBiF,sBAAY,OAAKC,GAAL,CAASC,KAAT,CAAeC,QAAf,IAA2B,OAAKF,GAAL,CAASC,KAAT,CAAeC,QAAf,CAAwB5B,EAAnD,GAAwD,OAAK0B,GAAL,CAASC,KAAT,CAAeC,QAAf,CAAwB5B,EAAhF,GAAqF;AAZjF,SAAlB;;AAeA,cAAM6B,UAAU,MAAM5F,WAAW6F,GAAX,CAAejB,SAAf,CAAtB;;AAEA,YAAIgB,OAAJ,EAAa;AACX9F,kBAAQC,GAAR,CAAY,YAAZ,EAA0B6F,OAA1B;AACA,iBAAO,OAAKrE,OAAL,CAAa;AAClBwC,gBAAI6B,OADc;AAElBtB,0BAAcD,eAFI;AAGlB/B,wBAAYsC,UAAUtC,UAHJ;AAIlBE,sBAAUoC,UAAUpC;AAJF,WAAb,CAAP;AAMD,SARD,MAQO;AACL,iBAAO,OAAKf,IAAL,CAAU,MAAV,CAAP;AACD;AAEF,OAvFD,CAuFE,OAAOD,KAAP,EAAc;AACd1B,gBAAQ0B,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,eAAO,OAAKC,IAAL,CAAU,WAAWD,MAAMsE,OAA3B,CAAP;AACD;AA3FkB;AA4FpB;;AAED;;;AAGM7C,qBAAN,GAA4B;AAAA;;AAAA;AAC1B,UAAI;AACF,cAAMjD,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAMgC,MAAM,IAAIlB,IAAJ,EAAZ;;AAEA;AACA,cAAMf,WAAWM,KAAX,CAAiB;AACrBC,kBAAQ,UADa;AAErB+B,sBAAY,CAAC,IAAD,EAAOzB,MAAMC,QAAN,CAAemB,GAAf,CAAP;AAFS,SAAjB,EAGH8D,MAHG,CAGI,EAAExF,QAAQ,QAAV,EAHJ,CAAN;;AAKA;AACA,cAAMP,WAAWM,KAAX,CAAiB;AACrBC,kBAAQ,QADa;AAErBiC,oBAAU,CAAC,IAAD,EAAO3B,MAAMC,QAAN,CAAemB,GAAf,CAAP;AAFW,SAAjB,EAGH8D,MAHG,CAGI,EAAExF,QAAQ,OAAV,EAHJ,CAAN;AAKD,OAhBD,CAgBE,OAAOiB,KAAP,EAAc;AACd1B,gBAAQ0B,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACD;AAnByB;AAoB3B;;AAED;;;;AAIMwE,aAAN,GAAoB;AAAA;;AAAA;AAClB,UAAI;AACF,cAAMnC,aAAa,OAAK5D,KAAL,CAAW,OAAX,CAAnB;;AAEA,cAAM6D,QAAQ,MAAMD,WAAWvD,KAAX,CAAiB;AACnC2F,qBAAW,CADwB;AAEnCC,sBAAY;AAFuB,SAAjB,EAGjBjF,KAHiB,CAGX,sCAHW,EAG6BC,MAH7B,EAApB;;AAKA,eAAO,OAAKK,OAAL,CAAauC,KAAb,CAAP;AAED,OAVD,CAUE,OAAOtC,KAAP,EAAc;AACd1B,gBAAQ0B,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKC,IAAL,CAAU,UAAV,CAAP;AACD;AAdiB;AAenB;;AAED;;;;AAIM0E,cAAN,GAAqB;AAAA;;AAAA;AACnB,UAAI;AACF,cAAMvC,cAAc,OAAK3D,KAAL,CAAW,mBAAX,CAApB;AACA,cAAMkE,SAAS,MAAMP,YAAYtD,KAAZ,CAAkB,EAAEyD,IAAI,CAAN,EAAlB,EAA6BE,IAA7B,EAArB;;AAEA,eAAO,OAAK1C,OAAL,CAAa4C,UAAU,EAAvB,CAAP;AAED,OAND,CAME,OAAO3C,KAAP,EAAc;AACd1B,gBAAQ0B,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,eAAO,OAAKC,IAAL,CAAU,QAAV,CAAP;AACD;AAVkB;AAWpB;;AAED;;;;AAIM2E,oBAAN,GAA2B;AAAA;;AAAA;AACzB,UAAI;AACF,cAAMlE,OAAO,OAAKwB,IAAL,EAAb;AACA,cAAME,cAAc,OAAK3D,KAAL,CAAW,mBAAX,CAApB;;AAEA,cAAM2D,YAAYtD,KAAZ,CAAkB,EAAEyD,IAAI,CAAN,EAAlB,EAA6BgC,MAA7B,CAAoC7D,IAApC,CAAN;;AAEA,eAAO,OAAKX,OAAL,CAAa,QAAb,CAAP;AAED,OARD,CAQE,OAAOC,KAAP,EAAc;AACd1B,gBAAQ0B,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,eAAO,OAAKC,IAAL,CAAU,QAAV,CAAP;AACD;AAZwB;AAa1B;AA/UiC,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\flashsalerounds.js", "sourcesContent": ["const Base = require('./base.js');\n\nmodule.exports = class extends Base {\n\n  /**\n   * 获取秒杀统计数据\n   * GET /admin/flashsalerounds/statistics\n   */\n  async statisticsAction() {\n    try {\n      console.log('=== 获取轮次秒杀统计数据 ===');\n\n      const roundModel = this.model('flash_sale_rounds');\n      const orderModel = this.model('flash_sale_orders');\n\n      // 获取轮次统计\n      const totalRounds = await roundModel.count();\n      const activeRounds = await roundModel.where({ status: 'active' }).count();\n      const upcomingRounds = await roundModel.where({ status: 'upcoming' }).count();\n      const endedRounds = await roundModel.where({ status: 'ended' }).count();\n\n      // 获取订单统计\n      const totalOrders = await orderModel.count();\n      const todayOrders = await orderModel.where({\n        created_at: ['>=', think.datetime(new Date(), 'YYYY-MM-DD 00:00:00')]\n      }).count();\n\n      // 计算总销售额\n      const totalSalesResult = await orderModel.field('SUM(total_amount) as total_sales').select();\n      const totalSales = totalSalesResult[0] && totalSalesResult[0].total_sales ? totalSalesResult[0].total_sales : 0;\n\n      const result = {\n        totalRounds,\n        activeRounds,\n        upcomingRounds,\n        endedRounds,\n        totalOrders,\n        todayOrders,\n        totalSales: parseFloat(totalSales)\n      };\n\n      console.log('统计数据:', result);\n      return this.success(result);\n\n    } catch (error) {\n      console.error('获取统计数据失败:', error);\n      return this.fail('获取统计数据失败');\n    }\n  }\n\n  /**\n   * 获取轮次列表\n   * GET /admin/flashsalerounds/list\n   */\n  async listAction() {\n    try {\n      console.log('=== 获取轮次列表 ===');\n\n      const roundModel = this.model('flash_sale_rounds');\n      const page = this.get('page') || 1;\n      const limit = this.get('limit') || 20;\n      const status = this.get('status');\n\n      let where = {};\n      if (status) {\n        where.status = status;\n      }\n\n      const rounds = await roundModel.where(where)\n        .order('round_number DESC')\n        .page(page, limit)\n        .countSelect();\n\n      // 计算倒计时\n      const now = new Date();\n      rounds.data.forEach(round => {\n        const startTime = new Date(round.start_time);\n        const endTime = new Date(round.end_time);\n        \n        if (round.status === 'upcoming') {\n          round.countdown = Math.max(0, Math.floor((startTime - now) / 1000));\n        } else if (round.status === 'active') {\n          round.countdown = Math.max(0, Math.floor((endTime - now) / 1000));\n        } else {\n          round.countdown = 0;\n        }\n\n        // 计算库存进度\n        round.stockProgress = round.stock > 0 ? \n          Math.round((round.sold_count / round.stock) * 100) : 100;\n      });\n\n      return this.success(rounds);\n\n    } catch (error) {\n      console.error('获取轮次列表失败:', error);\n      return this.fail('获取轮次列表失败');\n    }\n  }\n\n  /**\n   * 获取当前和即将开始的轮次\n   * GET /admin/flashsalerounds/current\n   */\n  async currentAction() {\n    try {\n      console.log('=== 获取当前轮次 ===');\n\n      const roundModel = this.model('flash_sale_rounds');\n      const now = new Date();\n\n      // 更新过期轮次状态\n      await this.updateExpiredRounds();\n\n      // 获取当前进行中的轮次\n      const currentRounds = await roundModel.where({\n        status: 'active',\n        start_time: ['<=', think.datetime(now)],\n        end_time: ['>', think.datetime(now)]\n      }).order('start_time ASC').select();\n\n      // 获取即将开始的轮次（未来30分钟内）\n      const futureTime = new Date(now.getTime() + 30 * 60 * 1000);\n      const upcomingRounds = await roundModel.where({\n        status: 'upcoming',\n        start_time: ['>', think.datetime(now)],\n        start_time: ['<=', think.datetime(futureTime)]\n      }).order('start_time ASC').select();\n\n      // 计算倒计时\n      [...currentRounds, ...upcomingRounds].forEach(round => {\n        const startTime = new Date(round.start_time);\n        const endTime = new Date(round.end_time);\n        \n        if (round.status === 'upcoming') {\n          round.countdown = Math.max(0, Math.floor((startTime - now) / 1000));\n        } else if (round.status === 'active') {\n          round.countdown = Math.max(0, Math.floor((endTime - now) / 1000));\n        }\n\n        // 计算库存进度\n        round.stockProgress = round.stock > 0 ? \n          Math.round((round.sold_count / round.stock) * 100) : 100;\n      });\n\n      const result = {\n        current: currentRounds,\n        upcoming: upcomingRounds,\n        total: currentRounds.length + upcomingRounds.length\n      };\n\n      return this.success(result);\n\n    } catch (error) {\n      console.error('获取当前轮次失败:', error);\n      return this.fail('获取当前轮次失败');\n    }\n  }\n\n  /**\n   * 创建新轮次\n   * POST /admin/flashsalerounds/create\n   */\n  async createAction() {\n    try {\n      console.log('=== 创建新轮次 ===');\n\n      const data = this.post();\n      console.log('接收到的数据:', data);\n\n      // 验证必填字段\n      const requiredFields = ['goods_id', 'flash_price', 'stock'];\n      for (const field of requiredFields) {\n        if (!data[field]) {\n          return this.fail(`请填写${field}`);\n        }\n      }\n\n      const roundModel = this.model('flash_sale_rounds');\n      const configModel = this.model('flash_sale_config');\n      const goodsModel = this.model('goods');\n\n      // 获取商品信息\n      const goods = await goodsModel.where({ id: data.goods_id }).find();\n      if (think.isEmpty(goods)) {\n        return this.fail('商品不存在');\n      }\n\n      // 获取系统配置\n      const config = await configModel.where({ id: 1 }).find();\n      if (think.isEmpty(config)) {\n        return this.fail('系统配置不存在');\n      }\n\n      // 获取下一个轮次编号\n      const lastRound = await roundModel.order('round_number DESC').find();\n      const nextRoundNumber = (lastRound && lastRound.round_number) ? lastRound.round_number + 1 : 1;\n\n      // 计算开始时间（如果有进行中的轮次，则在其结束后开始）\n      const activeRound = await roundModel.where({ status: 'active' }).find();\n      let startTime;\n\n      if (activeRound && activeRound.end_time) {\n        // 在当前轮次结束后 + 间隔时间开始\n        const activeEndTime = new Date(activeRound.end_time);\n        if (!isNaN(activeEndTime.getTime())) {\n          startTime = new Date(activeEndTime.getTime() + config.break_duration * 1000);\n        } else {\n          startTime = new Date(Date.now() + 60 * 1000);\n        }\n      } else {\n        // 立即开始（或稍后开始）\n        startTime = new Date(Date.now() + 60 * 1000); // 1分钟后开始\n      }\n\n      const endTime = new Date(startTime.getTime() + config.round_duration * 1000);\n\n      // 验证时间有效性\n      if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {\n        return this.fail('时间计算错误');\n      }\n\n      const roundData = {\n        round_number: nextRoundNumber,\n        goods_id: data.goods_id,\n        goods_name: goods.name,\n        goods_image: goods.list_pic_url || '',\n        original_price: goods.retail_price,\n        flash_price: data.flash_price,\n        stock: data.stock,\n        limit_quantity: data.limit_quantity || 1,\n        start_time: startTime.toISOString().slice(0, 19).replace('T', ' '),\n        end_time: endTime.toISOString().slice(0, 19).replace('T', ' '),\n        status: 'upcoming',\n        created_by: this.ctx.state.userInfo && this.ctx.state.userInfo.id ? this.ctx.state.userInfo.id : 0\n      };\n\n      const roundId = await roundModel.add(roundData);\n\n      if (roundId) {\n        console.log('轮次创建成功，ID:', roundId);\n        return this.success({ \n          id: roundId, \n          round_number: nextRoundNumber,\n          start_time: roundData.start_time,\n          end_time: roundData.end_time\n        });\n      } else {\n        return this.fail('创建失败');\n      }\n\n    } catch (error) {\n      console.error('创建轮次失败:', error);\n      return this.fail('创建失败: ' + error.message);\n    }\n  }\n\n  /**\n   * 更新过期轮次状态\n   */\n  async updateExpiredRounds() {\n    try {\n      const roundModel = this.model('flash_sale_rounds');\n      const now = new Date();\n\n      // 将过期的upcoming轮次设为active\n      await roundModel.where({\n        status: 'upcoming',\n        start_time: ['<=', think.datetime(now)]\n      }).update({ status: 'active' });\n\n      // 将过期的active轮次设为ended\n      await roundModel.where({\n        status: 'active',\n        end_time: ['<=', think.datetime(now)]\n      }).update({ status: 'ended' });\n\n    } catch (error) {\n      console.error('更新轮次状态失败:', error);\n    }\n  }\n\n  /**\n   * 获取商品列表（用于选择）\n   * GET /admin/flashsalerounds/goods\n   */\n  async goodsAction() {\n    try {\n      const goodsModel = this.model('goods');\n      \n      const goods = await goodsModel.where({\n        is_delete: 0,\n        is_on_sale: 1\n      }).field('id, name, list_pic_url, retail_price').select();\n\n      return this.success(goods);\n\n    } catch (error) {\n      console.error('获取商品列表失败:', error);\n      return this.fail('获取商品列表失败');\n    }\n  }\n\n  /**\n   * 获取系统配置\n   * GET /admin/flashsalerounds/config\n   */\n  async configAction() {\n    try {\n      const configModel = this.model('flash_sale_config');\n      const config = await configModel.where({ id: 1 }).find();\n      \n      return this.success(config || {});\n\n    } catch (error) {\n      console.error('获取配置失败:', error);\n      return this.fail('获取配置失败');\n    }\n  }\n\n  /**\n   * 更新系统配置\n   * POST /admin/flashsalerounds/config\n   */\n  async updateConfigAction() {\n    try {\n      const data = this.post();\n      const configModel = this.model('flash_sale_config');\n      \n      await configModel.where({ id: 1 }).update(data);\n      \n      return this.success('配置更新成功');\n\n    } catch (error) {\n      console.error('更新配置失败:', error);\n      return this.fail('更新配置失败');\n    }\n  }\n};\n"]}