function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

module.exports = class extends think.Model {

  /**
   * 批量添加轮次商品
   */
  addRoundGoods(roundId, goodsList) {
    var _this = this;

    return _asyncToGenerator(function* () {
      const goodsData = goodsList.map(function (goods, index) {
        return {
          round_id: roundId,
          goods_id: goods.goods_id,
          goods_name: goods.goods_name,
          goods_image: goods.goods_image || '',
          original_price: goods.original_price,
          flash_price: goods.flash_price,
          discount_rate: goods.discount_rate || _this.calculateDiscountRate(goods.original_price, goods.flash_price),
          stock: goods.stock,
          sold_count: 0,
          limit_quantity: goods.limit_quantity || 1,
          sort_order: index
        };
      });

      return yield _this.addMany(goodsData);
    })();
  }

  /**
   * 计算折扣率
   */
  calculateDiscountRate(originalPrice, flashPrice) {
    if (!originalPrice || originalPrice <= 0) return 0;
    return Math.round((1 - flashPrice / originalPrice) * 100 * 100) / 100; // 保留2位小数
  }

  /**
   * 获取轮次商品列表
   */
  getRoundGoodsList(roundId) {
    var _this2 = this;

    return _asyncToGenerator(function* () {
      return yield _this2.where({ round_id: roundId }).order('sort_order ASC').select();
    })();
  }

  /**
   * 检查商品是否已参与其他活跃轮次
   */
  checkGoodsInActiveRounds(goodsIds) {
    var _this3 = this;

    return _asyncToGenerator(function* () {
      const roundModel = _this3.model('flash_sale_rounds');

      // 查询活跃轮次中的商品
      const activeGoods = yield _this3.alias('rg').join({
        table: 'flash_sale_rounds',
        join: 'inner',
        as: 'r',
        on: ['rg.round_id', 'r.id']
      }).where({
        'rg.goods_id': ['IN', goodsIds],
        'r.status': ['IN', ['upcoming', 'active']]
      }).field('rg.goods_id, r.round_number, r.start_time, r.end_time, r.status').select();

      return activeGoods;
    })();
  }

  /**
   * 检查商品在指定时间段是否有重叠的轮次
   */
  checkGoodsInOverlappingRounds(goodsIds, startTime, endTime) {
    var _this4 = this;

    return _asyncToGenerator(function* () {
      const roundModel = _this4.model('flash_sale_rounds');

      // 查询时间重叠的轮次中的商品
      const overlappingGoods = yield _this4.alias('rg').join({
        table: 'flash_sale_rounds',
        join: 'inner',
        as: 'r',
        on: ['rg.round_id', 'r.id']
      }).where({
        'rg.goods_id': ['IN', goodsIds],
        'r.status': ['IN', ['upcoming', 'active']],
        '_complex': {
          '_logic': 'OR',
          // 新轮次开始时间在现有轮次时间范围内
          'start_overlap': {
            'r.start_time': ['<=', startTime],
            'r.end_time': ['>', startTime]
          },
          // 新轮次结束时间在现有轮次时间范围内
          'end_overlap': {
            'r.start_time': ['<', endTime],
            'r.end_time': ['>=', endTime]
          },
          // 新轮次完全包含现有轮次
          'contains': {
            'r.start_time': ['>=', startTime],
            'r.end_time': ['<=', endTime]
          }
        }
      }).field('rg.goods_id, r.round_number, r.start_time, r.end_time, r.status').select();

      return overlappingGoods;
    })();
  }

  /**
   * 更新商品销售数量
   */
  updateSoldCount(roundGoodsId, quantity) {
    var _this5 = this;

    return _asyncToGenerator(function* () {
      return yield _this5.where({ id: roundGoodsId }).increment('sold_count', quantity);
    })();
  }

  /**
   * 获取商品在轮次中的剩余库存
   */
  getAvailableStock(roundGoodsId) {
    var _this6 = this;

    return _asyncToGenerator(function* () {
      const roundGoods = yield _this6.where({ id: roundGoodsId }).find();
      if (think.isEmpty(roundGoods)) {
        return 0;
      }
      return Math.max(0, roundGoods.stock - roundGoods.sold_count);
    })();
  }

  /**
   * 获取用户在某轮次某商品的购买数量
   */
  getUserPurchaseCount(roundId, goodsId, userId) {
    var _this7 = this;

    return _asyncToGenerator(function* () {
      const orderModel = _this7.model('flash_sale_orders');
      const result = yield orderModel.where({
        round_id: roundId,
        goods_id: goodsId,
        user_id: userId
      }).sum('quantity');

      return result || 0;
    })();
  }
};
//# sourceMappingURL=flash_sale_round_goods.js.map