function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

module.exports = class extends think.Model {
    /**
     * 获取商品的分销配置
     * @param goodsId
     * @returns {Promise.<*>}
     */
    getDistributionConfig(goodsId) {
        var _this = this;

        return _asyncToGenerator(function* () {
            const config = yield _this.where({
                goods_id: goodsId
            }).find();
            return config;
        })();
    }

    /**
     * 设置商品分销配置
     * @param goodsId
     * @param config
     * @returns {Promise.<*>}
     */
    setDistributionConfig(goodsId, config) {
        var _this2 = this;

        return _asyncToGenerator(function* () {
            const existingConfig = yield _this2.where({
                goods_id: goodsId
            }).find();

            const configData = {
                goods_id: goodsId,
                is_distributed: config.is_distributed || 0,
                commission_rate: config.commission_rate || 0,
                commission_type: config.commission_type || 'default',
                update_time: parseInt(new Date().getTime() / 1000)
            };

            if (existingConfig) {
                // 更新现有配置
                return yield _this2.where({
                    goods_id: goodsId
                }).update(configData);
            } else {
                // 创建新配置
                configData.add_time = parseInt(new Date().getTime() / 1000);
                return yield _this2.add(configData);
            }
        })();
    }

    /**
     * 获取所有已分销的商品
     * @returns {Promise.<*>}
     */
    getDistributedGoods() {
        var _this3 = this;

        return _asyncToGenerator(function* () {
            const goods = yield _this3.where({
                is_distributed: 1
            }).select();
            return goods;
        })();
    }

    /**
     * 批量设置分销状态
     * @param goodsIds
     * @param isDistributed
     * @param commissionRate
     * @param commissionType
     * @returns {Promise.<*>}
     */
    batchSetDistribution(goodsIds, isDistributed, commissionRate = 0, commissionType = 'default') {
        var _this4 = this;

        return _asyncToGenerator(function* () {
            const currentTime = parseInt(new Date().getTime() / 1000);
            const results = [];

            for (const goodsId of goodsIds) {
                const existingConfig = yield _this4.where({
                    goods_id: goodsId
                }).find();

                const configData = {
                    goods_id: goodsId,
                    is_distributed: isDistributed,
                    commission_rate: commissionRate,
                    commission_type: commissionType,
                    update_time: currentTime
                };

                if (existingConfig) {
                    // 更新现有配置
                    const result = yield _this4.where({
                        goods_id: goodsId
                    }).update(configData);
                    results.push(result);
                } else {
                    // 创建新配置
                    configData.add_time = currentTime;
                    const result = yield _this4.add(configData);
                    results.push(result);
                }
            }

            return results;
        })();
    }

    /**
     * 计算商品预计佣金
     * @param goodsId
     * @returns {Promise.<*>}
     */
    calculateEstimatedCommission(goodsId) {
        var _this5 = this;

        return _asyncToGenerator(function* () {
            const config = yield _this5.where({
                goods_id: goodsId
            }).find();

            if (!config || !config.is_distributed) {
                return 0;
            }

            const goods = yield _this5.model('goods').where({
                id: goodsId
            }).find();

            if (!goods) {
                return 0;
            }

            const estimatedCommission = parseFloat(goods.retail_price) * config.commission_rate / 100;
            return estimatedCommission.toFixed(2);
        })();
    }

    /**
     * 获取分销统计数据
     * @returns {Promise.<*>}
     */
    getDistributionStats() {
        var _this6 = this;

        return _asyncToGenerator(function* () {
            // 已分销商品数
            const distributedCount = yield _this6.where({
                is_distributed: 1
            }).count();

            // 总佣金池
            const distributedGoods = yield _this6.model('goods').alias('g').join({
                table: 'goods_distribution',
                join: 'inner',
                as: 'd',
                on: ['g.id', 'd.goods_id']
            }).where({
                'g.is_delete': 0,
                'd.is_distributed': 1
            }).field('g.retail_price,d.commission_rate').select();

            let totalCommission = 0;
            for (const item of distributedGoods) {
                totalCommission += parseFloat(item.retail_price) * item.commission_rate / 100;
            }

            return {
                distributedCount: distributedCount,
                totalCommission: totalCommission.toFixed(2)
            };
        })();
    }
};