{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\address.js"], "names": ["Base", "require", "pinyin", "generate", "module", "exports", "getAddressesAction", "userId", "getLoginUserId", "addressList", "model", "where", "user_id", "is_delete", "order", "select", "itemKey", "addressItem", "province_name", "getRegionName", "province_id", "city_name", "city_id", "district_name", "district_id", "full_region", "success", "saveAddressAction", "addressId", "post", "addressData", "name", "mobile", "address", "is_default", "think", "isEmpty", "add", "id", "update", "addressInfo", "find", "deleteAddressAction", "d", "addressDetailAction", "get"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;AACA,MAAME,WAAWF,QAAQ,iBAAR,CAAjB;AACAG,OAAOC,OAAP,GAAiB,cAAcL,IAAd,CAAmB;AAC1BM,sBAAN,GAA2B;AAAA;;AAAA;AAC7B,kBAAMC,SAAS,MAAM,MAAKC,cAAL,EAArB;AACM,kBAAMC,cAAc,MAAM,MAAKC,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAClDC,yBAASL,MADyC;AAElDM,2BAAW;AAFuC,aAA5B,EAGvBC,KAHuB,CAGjB,SAHiB,EAGNC,MAHM,EAA1B;AAIA,gBAAIC,UAAU,CAAd;AACA,iBAAK,MAAMC,WAAX,IAA0BR,WAA1B,EAAuC;AACnCA,4BAAYO,OAAZ,EAAqBE,aAArB,GAAqC,MAAM,MAAKR,KAAL,CAAW,QAAX,EAAqBS,aAArB,CAAmCF,YAAYG,WAA/C,CAA3C;AACAX,4BAAYO,OAAZ,EAAqBK,SAArB,GAAiC,MAAM,MAAKX,KAAL,CAAW,QAAX,EAAqBS,aAArB,CAAmCF,YAAYK,OAA/C,CAAvC;AACAb,4BAAYO,OAAZ,EAAqBO,aAArB,GAAqC,MAAM,MAAKb,KAAL,CAAW,QAAX,EAAqBS,aAArB,CAAmCF,YAAYO,WAA/C,CAA3C;AACAf,4BAAYO,OAAZ,EAAqBS,WAArB,GAAmChB,YAAYO,OAAZ,EAAqBE,aAArB,GAAqCT,YAAYO,OAAZ,EAAqBK,SAA1D,GAAsEZ,YAAYO,OAAZ,EAAqBO,aAA9H;AACAP,2BAAW,CAAX;AACH;AACD,mBAAO,MAAKU,OAAL,CAAajB,WAAb,CAAP;AAduB;AAe1B;AACKkB,qBAAN,GAA0B;AAAA;;AAAA;AACtB,gBAAIC,YAAY,OAAKC,IAAL,CAAU,IAAV,CAAhB;AACN,kBAAMtB,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,kBAAMsB,cAAc;AAChBC,sBAAM,OAAKF,IAAL,CAAU,MAAV,CADU;AAEhBG,wBAAQ,OAAKH,IAAL,CAAU,QAAV,CAFQ;AAGhBT,6BAAa,OAAKS,IAAL,CAAU,aAAV,CAHG;AAIhBP,yBAAS,OAAKO,IAAL,CAAU,SAAV,CAJO;AAKhBL,6BAAa,OAAKK,IAAL,CAAU,aAAV,CALG;AAMhBI,yBAAS,OAAKJ,IAAL,CAAU,SAAV,CANO;AAOhBjB,yBAASL,MAPO;AAQhB2B,4BAAY,OAAKL,IAAL,CAAU,YAAV;AARI,aAApB;AAUA,gBAAIM,MAAMC,OAAN,CAAcR,SAAd,CAAJ,EAA8B;AAC1BA,4BAAY,MAAM,OAAKlB,KAAL,CAAW,SAAX,EAAsB2B,GAAtB,CAA0BP,WAA1B,CAAlB;AACH,aAFD,MAEO;AACH,sBAAM,OAAKpB,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC9B2B,wBAAIV,SAD0B;AAE9BhB,6BAASL;AAFqB,iBAA5B,EAGHgC,MAHG,CAGIT,WAHJ,CAAN;AAIH;AACD;AACA,gBAAI,OAAKD,IAAL,CAAU,YAAV,KAA2B,CAA/B,EAAkC;AAC9B,sBAAM,OAAKnB,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC9B2B,wBAAI,CAAC,IAAD,EAAOV,SAAP,CAD0B;AAE9BhB,6BAASL;AAFqB,iBAA5B,EAGHgC,MAHG,CAGI;AACNL,gCAAY;AADN,iBAHJ,CAAN;AAMH;AACD,kBAAMM,cAAc,MAAM,OAAK9B,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAClD2B,oBAAIV;AAD8C,aAA5B,EAEvBa,IAFuB,EAA1B;AAGA,mBAAO,OAAKf,OAAL,CAAac,WAAb,CAAP;AAjCsB;AAkCzB;AACKE,uBAAN,GAA4B;AAAA;;AAAA;AACxB,kBAAMJ,KAAK,OAAKT,IAAL,CAAU,IAAV,CAAX;AACN,kBAAMtB,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,gBAAImC,IAAI,MAAM,OAAKjC,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AACtCC,yBAASL,MAD6B;AAEtC+B,oBAAIA;AAFkC,aAA5B,EAGXC,MAHW,CAGJ;AACN1B,2BAAW;AADL,aAHI,CAAd;AAMA,mBAAO,OAAKa,OAAL,CAAaiB,CAAb,CAAP;AATwB;AAU3B;AACKC,uBAAN,GAA4B;AAAA;;AAAA;AACxB,kBAAMhB,YAAY,OAAKiB,GAAL,CAAS,IAAT,CAAlB;AACN,kBAAMtC,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,kBAAMgC,cAAc,MAAM,OAAK9B,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAClDC,yBAASL,MADyC;AAElD+B,oBAAIV;AAF8C,aAA5B,EAGvBa,IAHuB,EAA1B;AAIA,gBAAI,CAACN,MAAMC,OAAN,CAAcI,WAAd,CAAL,EAAiC;AAC7BA,4BAAYtB,aAAZ,GAA4B,MAAM,OAAKR,KAAL,CAAW,QAAX,EAAqBS,aAArB,CAAmCqB,YAAYpB,WAA/C,CAAlC;AACAoB,4BAAYnB,SAAZ,GAAwB,MAAM,OAAKX,KAAL,CAAW,QAAX,EAAqBS,aAArB,CAAmCqB,YAAYlB,OAA/C,CAA9B;AACAkB,4BAAYjB,aAAZ,GAA4B,MAAM,OAAKb,KAAL,CAAW,QAAX,EAAqBS,aAArB,CAAmCqB,YAAYhB,WAA/C,CAAlC;AACAgB,4BAAYf,WAAZ,GAA0Be,YAAYtB,aAAZ,GAA4BsB,YAAYnB,SAAxC,GAAoDmB,YAAYjB,aAA1F;AACH;AACD,mBAAO,OAAKG,OAAL,CAAac,WAAb,CAAP;AAbwB;AAc3B;AA7E+B,CAApC", "file": "..\\..\\..\\src\\api\\controller\\address.js", "sourcesContent": ["const Base = require('./base.js');\nconst pinyin = require(\"pinyin\");\nconst generate = require('nanoid/generate');\nmodule.exports = class extends Base {\n    async getAddressesAction() {\n\t\tconst userId = await this.getLoginUserId();\n        const addressList = await this.model('address').where({\n            user_id: userId,\n            is_delete: 0\n        }).order('id desc').select();\n        let itemKey = 0;\n        for (const addressItem of addressList) {\n            addressList[itemKey].province_name = await this.model('region').getRegionName(addressItem.province_id);\n            addressList[itemKey].city_name = await this.model('region').getRegionName(addressItem.city_id);\n            addressList[itemKey].district_name = await this.model('region').getRegionName(addressItem.district_id);\n            addressList[itemKey].full_region = addressList[itemKey].province_name + addressList[itemKey].city_name + addressList[itemKey].district_name;\n            itemKey += 1;\n        }\n        return this.success(addressList);\n    }\n    async saveAddressAction() {\n        let addressId = this.post('id');\n\t\tconst userId = await this.getLoginUserId();\n        const addressData = {\n            name: this.post('name'),\n            mobile: this.post('mobile'),\n            province_id: this.post('province_id'),\n            city_id: this.post('city_id'),\n            district_id: this.post('district_id'),\n            address: this.post('address'),\n            user_id: userId,\n            is_default: this.post('is_default')\n        };\n        if (think.isEmpty(addressId)) {\n            addressId = await this.model('address').add(addressData);\n        } else {\n            await this.model('address').where({\n                id: addressId,\n                user_id: userId\n            }).update(addressData);\n        }\n        // 如果设置为默认，则取消其它的默认\n        if (this.post('is_default') == 1) {\n            await this.model('address').where({\n                id: ['<>', addressId],\n                user_id: userId\n            }).update({\n                is_default: 0\n            });\n        }\n        const addressInfo = await this.model('address').where({\n            id: addressId\n        }).find();\n        return this.success(addressInfo);\n    }\n    async deleteAddressAction() {\n        const id = this.post('id');\n\t\tconst userId = await this.getLoginUserId();\n        let d = await this.model('address').where({\n            user_id: userId,\n            id: id\n        }).update({\n            is_delete: 1\n        });\n        return this.success(d);\n    }\n    async addressDetailAction() {\n        const addressId = this.get('id');\n\t\tconst userId = await this.getLoginUserId();\n        const addressInfo = await this.model('address').where({\n            user_id: userId,\n            id: addressId\n        }).find();\n        if (!think.isEmpty(addressInfo)) {\n            addressInfo.province_name = await this.model('region').getRegionName(addressInfo.province_id);\n            addressInfo.city_name = await this.model('region').getRegionName(addressInfo.city_id);\n            addressInfo.district_name = await this.model('region').getRegionName(addressInfo.district_id);\n            addressInfo.full_region = addressInfo.province_name + addressInfo.city_name + addressInfo.district_name;\n        }\n        return this.success(addressInfo);\n    }\n};"]}