{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\goods.js"], "names": ["Base", "require", "moment", "fs", "path", "qiniu", "module", "exports", "checkGoodsInFlashSale", "goodsId", "activeCampaigns", "model", "where", "goods_id", "status", "select", "length", "campaignNames", "map", "c", "name", "join", "inFlashSale", "message", "campaigns", "activeRounds", "alias", "rounds", "error", "console", "indexAction", "page", "get", "size", "data", "is_delete", "order", "countSelect", "item", "info", "id", "category_id", "find", "category_name", "is_on_sale", "is_index", "product", "ele", "spec", "goods_specification_ids", "value", "success", "getExpressDataAction", "kd", "cate", "kdData", "push", "label", "cateData", "parent_id", "infoData", "copygoodsAction", "post", "insertId", "add", "goodsGallery", "gallery", "img_url", "sort_order", "updateStock", "goods_sn", "goods_number", "log", "update", "updateGoodsNumberAction", "all_goods", "goodsSum", "sum", "think", "timeout", "onsaleAction", "outAction", "dropAction", "sortAction", "index", "saleStatusAction", "sale", "flashSaleCheck", "fail", "checked", "productStatusAction", "product_id", "indexShowStatusAction", "stat", "infoAction", "getAllSpecificationAction", "specInfo", "specOptionsData", "spitem", "getAllCategory1Action", "is_show", "level", "c_data", "newData", "children", "citem", "getAllCategoryAction", "field", "c_item", "storeAction", "values", "specData", "specValue", "cateId", "picUrl", "list_pic_url", "is_new", "update_time", "parseInt", "Date", "getTime", "freight_template_id", "retail_price", "goods_specifition_name_value", "specificationData", "specification_id", "specId", "entries", "url", "add_time", "pro", "goodsNum", "getField", "maxPrice", "Math", "max", "minPrice", "min", "cost", "maxCost", "minCost", "goodsPrice", "costPrice", "cost_price", "min_retail_price", "min_cost_price", "updatePriceAction", "checkSkuAction", "isEmpty", "updateSortAction", "sort", "updateShortNameAction", "short_name", "galleryListAction", "galleryAction", "getGalleryListAction", "galleryData", "pdata", "deleteGalleryFileAction", "limit", "galleryEditAction", "isPost", "deleteListPicUrlAction", "destoryAction", "uploadHttpsImageAction", "accessKey", "config", "secret<PERSON>ey", "domain", "mac", "auth", "digest", "<PERSON>", "conf", "Config", "zoneNum", "zone", "Zone_z0", "Zone_z1", "Zone_z2", "Zone_na0", "Zone_as0", "bucketManager", "rs", "BucketManager", "bucket", "key", "uuid", "uploadQiniu", "Promise", "resolve", "reject", "fetch", "err", "respBody", "respInfo", "statusCode", "e", "httpsUrl", "lastUrl"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;AACA,MAAME,KAAKF,QAAQ,IAAR,CAAX;AACA,MAAMG,OAAOH,QAAQ,MAAR,CAAb;AACA,MAAMI,QAAQJ,QAAQ,OAAR,CAAd;AACAK,OAAOC,OAAP,GAAiB,cAAcP,IAAd,CAAmB;;AAEhC;;;;;AAKMQ,yBAAN,CAA4BC,OAA5B,EAAqC;AAAA;;AAAA;AACjC,gBAAI;AACA;AACA,sBAAMC,kBAAkB,MAAM,MAAKC,KAAL,CAAW,sBAAX,EAAmCC,KAAnC,CAAyC;AACnEC,8BAAUJ,OADyD;AAEnEK,4BAAQ,CAAC,IAAD,EAAO,CAAC,QAAD,EAAW,UAAX,CAAP;AAF2D,iBAAzC,EAG3BC,MAH2B,EAA9B;;AAKA,oBAAIL,gBAAgBM,MAAhB,GAAyB,CAA7B,EAAgC;AAC5B,0BAAMC,gBAAgBP,gBAAgBQ,GAAhB,CAAoB;AAAA,+BAAKC,EAAEC,IAAP;AAAA,qBAApB,EAAiCC,IAAjC,CAAsC,GAAtC,CAAtB;AACA,2BAAO;AACHC,qCAAa,IADV;AAEHC,iCAAU,eAAcN,aAAc,UAFnC;AAGHO,mCAAWd;AAHR,qBAAP;AAKH;;AAED;AACA,sBAAMe,eAAe,MAAM,MAAKd,KAAL,CAAW,mBAAX,EAAgCe,KAAhC,CAAsC,GAAtC,EACtBL,IADsB,CACjB,wBADiB,EACS,sBADT,EAEtBT,KAFsB,CAEhB;AACH,kCAAcH,OADX;AAEH,gCAAY,CAAC,IAAD,EAAO,CAAC,QAAD,EAAW,UAAX,CAAP;AAFT,iBAFgB,EAKpBM,MALoB,EAA3B;;AAOA,oBAAIU,aAAaT,MAAb,GAAsB,CAA1B,EAA6B;AACzB,2BAAO;AACHM,qCAAa,IADV;AAEHC,iCAAU,4BAFP;AAGHI,gCAAQF;AAHL,qBAAP;AAKH;;AAED,uBAAO;AACHH,iCAAa,KADV;AAEHC,6BAAS;AAFN,iBAAP;AAKH,aArCD,CAqCE,OAAOK,KAAP,EAAc;AACZC,wBAAQD,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO;AACHN,iCAAa,KADV;AAEHC,6BAAS,YAFN;AAGHK,2BAAOA,MAAML;AAHV,iBAAP;AAKH;AA7CgC;AA8CpC;;AAED;;;;AAIMO,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAMC,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,OAAKD,GAAL,CAAS,MAAT,CAAb;AACA,kBAAMZ,OAAO,OAAKY,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,kBAAMrB,QAAQ,OAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMuB,OAAO,MAAMvB,MAAMC,KAAN,CAAY;AAC3BQ,sBAAM,CAAC,MAAD,EAAU,IAAGA,IAAK,GAAlB,CADqB;AAE3Be,2BAAW;AAFgB,aAAZ,EAGhBC,KAHgB,CAGV,CAAC,gBAAD,CAHU,EAGUL,IAHV,CAGeA,IAHf,EAGqBE,IAHrB,EAG2BI,WAH3B,EAAnB;AAIA;AACA,iBAAK,MAAMC,IAAX,IAAmBJ,KAAKA,IAAxB,EAA8B;AAC1B,sBAAMK,OAAO,MAAM,OAAK5B,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAC5C4B,wBAAIF,KAAKG;AADmC,iBAA7B,EAEhBC,IAFgB,EAAnB;AAGAJ,qBAAKK,aAAL,GAAqBJ,KAAKnB,IAA1B;AACA,oBAAIkB,KAAKM,UAAL,IAAmB,CAAvB,EAA0B;AACtBN,yBAAKM,UAAL,GAAkB,IAAlB;AACH,iBAFD,MAEO;AACHN,yBAAKM,UAAL,GAAkB,KAAlB;AACH;AACD,oBAAIN,KAAKO,QAAL,IAAiB,CAArB,EAAwB;AACpBP,yBAAKO,QAAL,GAAgB,IAAhB;AACH,iBAFD,MAEO;AACHP,yBAAKO,QAAL,GAAgB,KAAhB;AACH;AACD,oBAAIC,UAAU,MAAM,OAAKnC,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC5CC,8BAAUyB,KAAKE,EAD6B;AAE5CL,+BAAW;AAFiC,iBAA5B,EAGjBpB,MAHiB,EAApB;AAIA,qBAAK,MAAMgC,GAAX,IAAkBD,OAAlB,EAA2B;AACvB,wBAAIE,OAAO,MAAM,OAAKrC,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AACrD4B,4BAAIO,IAAIE,uBAD6C;AAErDd,mCAAW;AAF0C,qBAAxC,EAGdO,IAHc,EAAjB;AAIAK,wBAAIG,KAAJ,GAAYF,KAAKE,KAAjB;AACAH,wBAAIH,UAAJ,GAAiBG,IAAIH,UAAJ,GAAiB,GAAjB,GAAuB,GAAxC;AACH;AACDN,qBAAKQ,OAAL,GAAeA,OAAf;AACH;AACD,mBAAO,OAAKK,OAAL,CAAajB,IAAb,CAAP;AAvCgB;AAwCnB;AACKkB,wBAAN,GAA6B;AAAA;;AAAA;AACzB,gBAAIC,KAAK,EAAT;AACA,gBAAIC,OAAO,EAAX;AACA,kBAAMC,SAAS,MAAM,OAAK5C,KAAL,CAAW,kBAAX,EAA+BC,KAA/B,CAAqC;AACtDuB,2BAAW;AAD2C,aAArC,EAElBpB,MAFkB,EAArB;AAGA,iBAAK,MAAMuB,IAAX,IAAmBiB,MAAnB,EAA2B;AACvBF,mBAAGG,IAAH,CAAQ;AACJN,2BAAOZ,KAAKE,EADR;AAEJiB,2BAAOnB,KAAKlB;AAFR,iBAAR;AAIH;AACD,kBAAMsC,WAAW,MAAM,OAAK/C,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAChD+C,2BAAW;AADqC,aAA7B,EAEpB5C,MAFoB,EAAvB;AAGA,iBAAK,MAAMuB,IAAX,IAAmBoB,QAAnB,EAA6B;AACzBJ,qBAAKE,IAAL,CAAU;AACNN,2BAAOZ,KAAKE,EADN;AAENiB,2BAAOnB,KAAKlB;AAFN,iBAAV;AAIH;AACD,gBAAIwC,WAAW;AACXP,oBAAIA,EADO;AAEXC,sBAAMA;AAFK,aAAf;AAIA,mBAAO,OAAKH,OAAL,CAAaS,QAAb,CAAP;AAzByB;AA0B5B;AACKC,mBAAN,GAAwB;AAAA;;AAAA;AACpB,kBAAMpD,UAAU,OAAKqD,IAAL,CAAU,IAAV,CAAhB;AACA,gBAAI5B,OAAO,MAAM,OAAKvB,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AACvC4B,oBAAI/B;AADmC,aAA1B,EAEdiC,IAFc,EAAjB;AAGA,mBAAOR,KAAKM,EAAZ;AACAN,iBAAKU,UAAL,GAAkB,CAAlB;AACA,gBAAImB,WAAW,MAAM,OAAKpD,KAAL,CAAW,OAAX,EAAoBqD,GAApB,CAAwB9B,IAAxB,CAArB;AACA,gBAAI+B,eAAe,MAAM,OAAKtD,KAAL,CAAW,eAAX,EAA4BC,KAA5B,CAAkC;AACvDC,0BAAUJ,OAD6C;AAEvD0B,2BAAU;AAF6C,aAAlC,EAGtBpB,MAHsB,EAAzB;AAIA,iBAAK,MAAMuB,IAAX,IAAmB2B,YAAnB,EAAiC;AAC7B,oBAAIC,UAAU;AACVC,6BAAS7B,KAAK6B,OADJ;AAEVC,gCAAY9B,KAAK8B,UAFP;AAGVvD,8BAAUkD;AAHA,iBAAd;AAKA,sBAAM,OAAKpD,KAAL,CAAW,eAAX,EAA4BqD,GAA5B,CAAgCE,OAAhC,CAAN;AACH;AACD,mBAAO,OAAKf,OAAL,CAAaY,QAAb,CAAP;AApBoB;AAqBvB;AACKM,eAAN,CAAkBC,QAAlB,EAA4BC,YAA5B,EAA0C;AAAA;;AAAA;AACtC1C,oBAAQ2C,GAAR,CAAY,UAAZ;AACA,kBAAM,OAAK7D,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC9B0D,0BAAUA;AADoB,aAA5B,EAEHG,MAFG,CAEI;AACNF,8BAAcA;AADR,aAFJ,CAAN;AAFsC;AAOzC;AACKG,2BAAN,GAAgC;AAAA;;AAAA;AAC5B,gBAAIC,YAAY,MAAM,OAAKhE,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5CuB,2BAAW,CADiC;AAE5CS,4BAAY;AAFgC,aAA1B,EAGnB7B,MAHmB,EAAtB;AAIA,iBAAK,MAAMuB,IAAX,IAAmBqC,SAAnB,EAA8B;AAC1B,oBAAIC,WAAW,MAAM,OAAKjE,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC7CC,8BAAUyB,KAAKE;AAD8B,iBAA5B,EAElBqC,GAFkB,CAEd,cAFc,CAArB;AAGA,sBAAM,OAAKlE,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5B4B,wBAAIF,KAAKE;AADmB,iBAA1B,EAEHiC,MAFG,CAEI;AACNF,kCAAcK;AADR,iBAFJ,CAAN;AAKA,sBAAME,MAAMC,OAAN,CAAc,IAAd,CAAN;AACH;AACD,mBAAO,OAAK5B,OAAL,EAAP;AAhB4B;AAiB/B;AACK6B,gBAAN,GAAqB;AAAA;;AAAA;AACjB,kBAAMjD,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,OAAKD,GAAL,CAAS,MAAT,CAAb;AACA,kBAAMrB,QAAQ,OAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMuB,OAAO,MAAMvB,MAAMC,KAAN,CAAY;AAC3BuB,2BAAW,CADgB;AAE3BS,4BAAY;AAFe,aAAZ,EAGhBR,KAHgB,CAGV,CAAC,gBAAD,CAHU,EAGUL,IAHV,CAGeA,IAHf,EAGqBE,IAHrB,EAG2BI,WAH3B,EAAnB;AAIA,iBAAK,MAAMC,IAAX,IAAmBJ,KAAKA,IAAxB,EAA8B;AAC1B,sBAAMK,OAAO,MAAM,OAAK5B,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAC5C4B,wBAAIF,KAAKG;AADmC,iBAA7B,EAEhBC,IAFgB,EAAnB;AAGAJ,qBAAKK,aAAL,GAAqBJ,KAAKnB,IAA1B;AACA;AACA;AACA;AACA;AACA,oBAAIkB,KAAKM,UAAL,IAAmB,CAAvB,EAA0B;AACtBN,yBAAKM,UAAL,GAAkB,IAAlB;AACH,iBAFD,MAEO;AACHN,yBAAKM,UAAL,GAAkB,KAAlB;AACH;AACD,oBAAIN,KAAKO,QAAL,IAAiB,CAArB,EAAwB;AACpBP,yBAAKO,QAAL,GAAgB,IAAhB;AACH,iBAFD,MAEO;AACHP,yBAAKO,QAAL,GAAgB,KAAhB;AACH;AACD,oBAAIC,UAAU,MAAM,OAAKnC,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC5CC,8BAAUyB,KAAKE,EAD6B;AAE5CL,+BAAW;AAFiC,iBAA5B,EAGjBpB,MAHiB,EAApB;AAIA,qBAAK,MAAMgC,GAAX,IAAkBD,OAAlB,EAA2B;AACvB,wBAAIE,OAAO,MAAM,OAAKrC,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AACrD4B,4BAAIO,IAAIE,uBAD6C;AAErDd,mCAAW;AAF0C,qBAAxC,EAGdO,IAHc,EAAjB;AAIAK,wBAAIG,KAAJ,GAAYF,KAAKE,KAAjB;AACAH,wBAAIH,UAAJ,GAAiBG,IAAIH,UAAJ,GAAiB,GAAjB,GAAuB,GAAxC;AACH;AACDN,qBAAKQ,OAAL,GAAeA,OAAf;AACH;AACD,mBAAO,OAAKK,OAAL,CAAajB,IAAb,CAAP;AAzCiB;AA0CpB;AACK+C,aAAN,GAAkB;AAAA;;AAAA;AACd,kBAAMlD,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,OAAKD,GAAL,CAAS,MAAT,CAAb;AACA,kBAAMrB,QAAQ,OAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMuB,OAAO,MAAMvB,MAAMC,KAAN,CAAY;AAC3BuB,2BAAW,CADgB;AAE3BoC,8BAAc,CAAC,IAAD,EAAO,CAAP;AAFa,aAAZ,EAGhBnC,KAHgB,CAGV,CAAC,gBAAD,CAHU,EAGUL,IAHV,CAGeA,IAHf,EAGqBE,IAHrB,EAG2BI,WAH3B,EAAnB;AAIA,iBAAK,MAAMC,IAAX,IAAmBJ,KAAKA,IAAxB,EAA8B;AAC1B,sBAAMK,OAAO,MAAM,OAAK5B,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAC5C4B,wBAAIF,KAAKG;AADmC,iBAA7B,EAEhBC,IAFgB,EAAnB;AAGAJ,qBAAKK,aAAL,GAAqBJ,KAAKnB,IAA1B;AACA,oBAAIkB,KAAKM,UAAL,IAAmB,CAAvB,EAA0B;AACtBN,yBAAKM,UAAL,GAAkB,IAAlB;AACH,iBAFD,MAEO;AACHN,yBAAKM,UAAL,GAAkB,KAAlB;AACH;AACD,oBAAIN,KAAKO,QAAL,IAAiB,CAArB,EAAwB;AACpBP,yBAAKO,QAAL,GAAgB,IAAhB;AACH,iBAFD,MAEO;AACHP,yBAAKO,QAAL,GAAgB,KAAhB;AACH;AACD,oBAAIC,UAAU,MAAM,OAAKnC,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC5CC,8BAAUyB,KAAKE,EAD6B;AAE5CL,+BAAW;AAFiC,iBAA5B,EAGjBpB,MAHiB,EAApB;AAIA,qBAAK,MAAMgC,GAAX,IAAkBD,OAAlB,EAA2B;AACvB,wBAAIE,OAAO,MAAM,OAAKrC,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AACrD4B,4BAAIO,IAAIE,uBAD6C;AAErDd,mCAAW;AAF0C,qBAAxC,EAGdO,IAHc,EAAjB;AAIAK,wBAAIG,KAAJ,GAAYF,KAAKE,KAAjB;AACAH,wBAAIH,UAAJ,GAAiBG,IAAIH,UAAJ,GAAiB,GAAjB,GAAuB,GAAxC;AACH;AACDN,qBAAKQ,OAAL,GAAeA,OAAf;AACH;AACD,mBAAO,OAAKK,OAAL,CAAajB,IAAb,CAAP;AArCc;AAsCjB;AACKgD,cAAN,GAAmB;AAAA;;AAAA;AACf,kBAAMnD,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,OAAKD,GAAL,CAAS,MAAT,CAAb;AACA,kBAAMrB,QAAQ,OAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMuB,OAAO,MAAMvB,MAAMC,KAAN,CAAY;AAC3BuB,2BAAW,CADgB;AAE3BS,4BAAY;AAFe,aAAZ,EAGhBR,KAHgB,CAGV,CAAC,SAAD,CAHU,EAGGL,IAHH,CAGQA,IAHR,EAGcE,IAHd,EAGoBI,WAHpB,EAAnB;AAIA,iBAAK,MAAMC,IAAX,IAAmBJ,KAAKA,IAAxB,EAA8B;AAC1B,sBAAMK,OAAO,MAAM,OAAK5B,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAC5C4B,wBAAIF,KAAKG;AADmC,iBAA7B,EAEhBC,IAFgB,EAAnB;AAGAJ,qBAAKK,aAAL,GAAqBJ,KAAKnB,IAA1B;AACA,oBAAIkB,KAAKM,UAAL,IAAmB,CAAvB,EAA0B;AACtBN,yBAAKM,UAAL,GAAkB,IAAlB;AACH,iBAFD,MAEO;AACHN,yBAAKM,UAAL,GAAkB,KAAlB;AACH;AACD,oBAAIN,KAAKO,QAAL,IAAiB,CAArB,EAAwB;AACpBP,yBAAKO,QAAL,GAAgB,IAAhB;AACH,iBAFD,MAEO;AACHP,yBAAKO,QAAL,GAAgB,KAAhB;AACH;AACD,oBAAIC,UAAU,MAAM,OAAKnC,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC5CC,8BAAUyB,KAAKE,EAD6B;AAE5CL,+BAAW;AAFiC,iBAA5B,EAGjBpB,MAHiB,EAApB;AAIA,qBAAK,MAAMgC,GAAX,IAAkBD,OAAlB,EAA2B;AACvB,wBAAIE,OAAO,MAAM,OAAKrC,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AACrD4B,4BAAIO,IAAIE,uBAD6C;AAErDd,mCAAW;AAF0C,qBAAxC,EAGdO,IAHc,EAAjB;AAIAK,wBAAIG,KAAJ,GAAYF,KAAKE,KAAjB;AACAH,wBAAIH,UAAJ,GAAiBG,IAAIH,UAAJ,GAAiB,GAAjB,GAAuB,GAAxC;AACH;AACDN,qBAAKQ,OAAL,GAAeA,OAAf;AACH;AACD,mBAAO,OAAKK,OAAL,CAAajB,IAAb,CAAP;AArCe;AAsClB;AACKiD,cAAN,GAAmB;AAAA;;AAAA;AACf,kBAAMpD,OAAO,QAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,QAAKD,GAAL,CAAS,MAAT,CAAb;AACA,kBAAMrB,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMyE,QAAQ,QAAKpD,GAAL,CAAS,OAAT,CAAd;AACA,gBAAIoD,SAAS,CAAb,EAAgB;AACZ,sBAAMlD,OAAO,MAAMvB,MAAMC,KAAN,CAAY;AAC3BuB,+BAAW;AADgB,iBAAZ,EAEhBC,KAFgB,CAEV,CAAC,kBAAD,CAFU,EAEYL,IAFZ,CAEiBA,IAFjB,EAEuBE,IAFvB,EAE6BI,WAF7B,EAAnB;AAGA,qBAAK,MAAMC,IAAX,IAAmBJ,KAAKA,IAAxB,EAA8B;AAC1B,0BAAMK,OAAO,MAAM,QAAK5B,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAC5C4B,4BAAIF,KAAKG;AADmC,qBAA7B,EAEhBC,IAFgB,EAAnB;AAGAJ,yBAAKK,aAAL,GAAqBJ,KAAKnB,IAA1B;AACA,wBAAIkB,KAAKM,UAAL,IAAmB,CAAvB,EAA0B;AACtBN,6BAAKM,UAAL,GAAkB,IAAlB;AACH,qBAFD,MAEO;AACHN,6BAAKM,UAAL,GAAkB,KAAlB;AACH;AACD,wBAAIN,KAAKO,QAAL,IAAiB,CAArB,EAAwB;AACpBP,6BAAKO,QAAL,GAAgB,IAAhB;AACH,qBAFD,MAEO;AACHP,6BAAKO,QAAL,GAAgB,KAAhB;AACH;AACD,wBAAIC,UAAU,MAAM,QAAKnC,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC5CC,kCAAUyB,KAAKE,EAD6B;AAE5CL,mCAAW;AAFiC,qBAA5B,EAGjBpB,MAHiB,EAApB;AAIA,yBAAK,MAAMgC,GAAX,IAAkBD,OAAlB,EAA2B;AACvB,4BAAIE,OAAO,MAAM,QAAKrC,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AACrD4B,gCAAIO,IAAIE,uBAD6C;AAErDd,uCAAW;AAF0C,yBAAxC,EAGdO,IAHc,EAAjB;AAIAK,4BAAIG,KAAJ,GAAYF,KAAKE,KAAjB;AACAH,4BAAIH,UAAJ,GAAiBG,IAAIH,UAAJ,GAAiB,GAAjB,GAAuB,GAAxC;AACH;AACDN,yBAAKQ,OAAL,GAAeA,OAAf;AACH;AACD,uBAAO,QAAKK,OAAL,CAAajB,IAAb,CAAP;AACH,aAlCD,MAkCO,IAAIkD,SAAS,CAAb,EAAgB;AACnB,sBAAMlD,OAAO,MAAMvB,MAAMC,KAAN,CAAY;AAC3BuB,+BAAW;AADgB,iBAAZ,EAEhBC,KAFgB,CAEV,CAAC,mBAAD,CAFU,EAEaL,IAFb,CAEkBA,IAFlB,EAEwBE,IAFxB,EAE8BI,WAF9B,EAAnB;AAGA,qBAAK,MAAMC,IAAX,IAAmBJ,KAAKA,IAAxB,EAA8B;AAC1B,0BAAMK,OAAO,MAAM,QAAK5B,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAC5C4B,4BAAIF,KAAKG;AADmC,qBAA7B,EAEhBC,IAFgB,EAAnB;AAGAJ,yBAAKK,aAAL,GAAqBJ,KAAKnB,IAA1B;AACA,wBAAIkB,KAAKM,UAAL,IAAmB,CAAvB,EAA0B;AACtBN,6BAAKM,UAAL,GAAkB,IAAlB;AACH,qBAFD,MAEO;AACHN,6BAAKM,UAAL,GAAkB,KAAlB;AACH;AACD,wBAAIN,KAAKO,QAAL,IAAiB,CAArB,EAAwB;AACpBP,6BAAKO,QAAL,GAAgB,IAAhB;AACH,qBAFD,MAEO;AACHP,6BAAKO,QAAL,GAAgB,KAAhB;AACH;AACD,wBAAIC,UAAU,MAAM,QAAKnC,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC5CC,kCAAUyB,KAAKE,EAD6B;AAE5CL,mCAAW;AAFiC,qBAA5B,EAGjBpB,MAHiB,EAApB;AAIA,yBAAK,MAAMgC,GAAX,IAAkBD,OAAlB,EAA2B;AACvB,4BAAIE,OAAO,MAAM,QAAKrC,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AACrD4B,gCAAIO,IAAIE,uBAD6C;AAErDd,uCAAW;AAF0C,yBAAxC,EAGdO,IAHc,EAAjB;AAIAK,4BAAIG,KAAJ,GAAYF,KAAKE,KAAjB;AACAH,4BAAIH,UAAJ,GAAiBG,IAAIH,UAAJ,GAAiB,GAAjB,GAAuB,GAAxC;AACH;AACDN,yBAAKQ,OAAL,GAAeA,OAAf;AACH;AACD,uBAAO,QAAKK,OAAL,CAAajB,IAAb,CAAP;AACH,aAlCM,MAkCA,IAAIkD,SAAS,CAAb,EAAgB;AACnB,sBAAMlD,OAAO,MAAMvB,MAAMC,KAAN,CAAY;AAC3BuB,+BAAW;AADgB,iBAAZ,EAEhBC,KAFgB,CAEV,CAAC,mBAAD,CAFU,EAEaL,IAFb,CAEkBA,IAFlB,EAEwBE,IAFxB,EAE8BI,WAF9B,EAAnB;AAGA,qBAAK,MAAMC,IAAX,IAAmBJ,KAAKA,IAAxB,EAA8B;AAC1B,0BAAMK,OAAO,MAAM,QAAK5B,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAC5C4B,4BAAIF,KAAKG;AADmC,qBAA7B,EAEhBC,IAFgB,EAAnB;AAGAJ,yBAAKK,aAAL,GAAqBJ,KAAKnB,IAA1B;AACA,wBAAIkB,KAAKM,UAAL,IAAmB,CAAvB,EAA0B;AACtBN,6BAAKM,UAAL,GAAkB,IAAlB;AACH,qBAFD,MAEO;AACHN,6BAAKM,UAAL,GAAkB,KAAlB;AACH;AACD,wBAAIN,KAAKO,QAAL,IAAiB,CAArB,EAAwB;AACpBP,6BAAKO,QAAL,GAAgB,IAAhB;AACH,qBAFD,MAEO;AACHP,6BAAKO,QAAL,GAAgB,KAAhB;AACH;AACD,wBAAIC,UAAU,MAAM,QAAKnC,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC5CC,kCAAUyB,KAAKE,EAD6B;AAE5CL,mCAAW;AAFiC,qBAA5B,EAGjBpB,MAHiB,EAApB;AAIA,yBAAK,MAAMgC,GAAX,IAAkBD,OAAlB,EAA2B;AACvB,4BAAIE,OAAO,MAAM,QAAKrC,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AACrD4B,gCAAIO,IAAIE,uBAD6C;AAErDd,uCAAW;AAF0C,yBAAxC,EAGdO,IAHc,EAAjB;AAIAK,4BAAIG,KAAJ,GAAYF,KAAKE,KAAjB;AACAH,4BAAIH,UAAJ,GAAiBG,IAAIH,UAAJ,GAAiB,GAAjB,GAAuB,GAAxC;AACH;AACDN,yBAAKQ,OAAL,GAAeA,OAAf;AACH;AACD,uBAAO,QAAKK,OAAL,CAAajB,IAAb,CAAP;AACH;AA3Gc;AA4GlB;AACKmD,oBAAN,GAAyB;AAAA;;AAAA;AACrB,kBAAM7C,KAAK,QAAKR,GAAL,CAAS,IAAT,CAAX;AACA,kBAAMlB,SAAS,QAAKkB,GAAL,CAAS,QAAT,CAAf;AACA,gBAAIsD,OAAO,CAAX;AACA,gBAAIxE,UAAU,MAAd,EAAsB;AAClBwE,uBAAO,CAAP;AACH;;AAED;AACA,gBAAIA,SAAS,CAAb,EAAgB;AACZ,sBAAMC,iBAAiB,MAAM,QAAK/E,qBAAL,CAA2BgC,EAA3B,CAA7B;AACA,oBAAI+C,eAAejE,WAAnB,EAAgC;AAC5B,2BAAO,QAAKkE,IAAL,CAAU,GAAV,EAAeD,eAAehE,OAA9B,CAAP;AACH;AACJ;;AAED,kBAAMZ,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMA,MAAMC,KAAN,CAAY;AACd4B,oBAAIA;AADU,aAAZ,EAEHiC,MAFG,CAEI;AACN7B,4BAAY0C;AADN,aAFJ,CAAN;AAKA,kBAAM,QAAK3E,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BC,0BAAU2B;AADiB,aAAzB,EAEHiC,MAFG,CAEI;AACN7B,4BAAY0C,IADN;AAENG,yBAASH;AAFH,aAFJ,CAAN;;AAOA,mBAAO,QAAKnC,OAAL,EAAP;AA7BqB;AA8BxB;AACKuC,uBAAN,GAA4B;AAAA;;AAAA;AACxB,kBAAMlD,KAAK,QAAKR,GAAL,CAAS,IAAT,CAAX;AACA,kBAAMlB,SAAS,QAAKkB,GAAL,CAAS,QAAT,CAAf;AACA,kBAAMrB,QAAQ,QAAKA,KAAL,CAAW,SAAX,CAAd;AACA,kBAAMA,MAAMC,KAAN,CAAY;AACd4B,oBAAIA;AADU,aAAZ,EAEHiC,MAFG,CAEI;AACN7B,4BAAY9B;AADN,aAFJ,CAAN;AAKN;AACA,kBAAM,QAAKH,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC9B+E,4BAAYnD,EADkB;AAE9BL,2BAAW;AAFmB,aAAzB,EAGHsC,MAHG,CAGI;AACT7B,4BAAY9B;AADH,aAHJ,CAAN;AAV8B;AAgB3B;AACK8E,yBAAN,GAA8B;AAAA;;AAAA;AAC1B,kBAAMpD,KAAK,QAAKR,GAAL,CAAS,IAAT,CAAX;AACA,kBAAMlB,SAAS,QAAKkB,GAAL,CAAS,QAAT,CAAf;AACA,gBAAI6D,OAAO,CAAX;AACA,gBAAI/E,UAAU,MAAd,EAAsB;AAClB+E,uBAAO,CAAP;AACH;AACD,kBAAMlF,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMA,MAAMC,KAAN,CAAY;AACd4B,oBAAIA;AADU,aAAZ,EAEHiC,MAFG,CAEI;AACN5B,0BAAUgD;AADJ,aAFJ,CAAN;AAR0B;AAa7B;AACKC,cAAN,GAAmB;AAAA;;AAAA;AACf,kBAAMtD,KAAK,QAAKR,GAAL,CAAS,IAAT,CAAX;AACA,kBAAMrB,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMuB,OAAO,MAAMvB,MAAMC,KAAN,CAAY;AAC3B4B,oBAAIA;AADuB,aAAZ,EAEhBE,IAFgB,EAAnB;AAGA,gBAAID,cAAcP,KAAKO,WAAvB;AACA,gBAAImB,WAAW;AACXrB,sBAAML,IADK;AAEXO,6BAAaA;AAFF,aAAf;AAIA,mBAAO,QAAKU,OAAL,CAAaS,QAAb,CAAP;AAXe;AAYlB;AACKmC,6BAAN,GAAkC;AAAA;;AAAA;AAC9B,kBAAMC,WAAW,MAAM,QAAKrF,KAAL,CAAW,eAAX,EAA4BC,KAA5B,CAAkC;AACrD4B,oBAAI,CAAC,GAAD,EAAM,CAAN;AADiD,aAAlC,EAEpBzB,MAFoB,EAAvB;AAGA,gBAAIkF,kBAAkB,EAAtB;AACA,iBAAK,MAAMC,MAAX,IAAqBF,QAArB,EAA+B;AAC3B,oBAAIzD,OAAO;AACPW,2BAAOgD,OAAO1D,EADP;AAEPiB,2BAAOyC,OAAO9E;AAFP,iBAAX;AAIA6E,gCAAgBzC,IAAhB,CAAqBjB,IAArB;AACH;AACD,mBAAO,QAAKY,OAAL,CAAa8C,eAAb,CAAP;AAZ8B;AAajC;AACKE,yBAAN,GAA8B;AAAA;;AAAA;AAAE;AAC5B,kBAAMxF,QAAQ,QAAKA,KAAL,CAAW,UAAX,CAAd;AACA,kBAAMuB,OAAO,MAAMvB,MAAMC,KAAN,CAAY;AAC3BwF,yBAAS,CADkB;AAE3BC,uBAAO;AAFoB,aAAZ,EAGhBtF,MAHgB,EAAnB;AAIA,kBAAMuF,SAAS,MAAM3F,MAAMC,KAAN,CAAY;AAC7BwF,yBAAS,CADoB;AAE7BC,uBAAO;AAFsB,aAAZ,EAGlBtF,MAHkB,EAArB;AAIA,gBAAIwF,UAAU,EAAd;AACA,iBAAK,MAAMjE,IAAX,IAAmBJ,IAAnB,EAAyB;AACrB,oBAAIsE,WAAW,EAAf;AACA,qBAAK,MAAMC,KAAX,IAAoBH,MAApB,EAA4B;AACxB,wBAAIG,MAAM9C,SAAN,IAAmBrB,KAAKE,EAA5B,EAAgC;AAC5BgE,iCAAShD,IAAT,CAAc;AACVN,mCAAOuD,MAAMjE,EADH;AAEViB,mCAAOgD,MAAMrF;AAFH,yBAAd;AAIH;AACJ;AACDmF,wBAAQ/C,IAAR,CAAa;AACTN,2BAAOZ,KAAKE,EADH;AAETiB,2BAAOnB,KAAKlB,IAFH;AAGToF,8BAAUA;AAHD,iBAAb;AAKH;AACD,mBAAO,QAAKrD,OAAL,CAAaoD,OAAb,CAAP;AA3B0B;AA4B7B;AACKG,wBAAN,GAA6B;AAAA;;AAAA;AAAE;AAC3B,kBAAM/F,QAAQ,QAAKA,KAAL,CAAW,UAAX,CAAd;AACA,kBAAMuB,OAAO,MAAMvB,MAAMC,KAAN,CAAY;AAC3BwF,yBAAS,CADkB;AAE3BC,uBAAO;AAFoB,aAAZ,EAGhBM,KAHgB,CAGV,SAHU,EAGC5F,MAHD,EAAnB;AAIA,gBAAIwF,UAAU,EAAd;AACA,iBAAK,MAAMjE,IAAX,IAAmBJ,IAAnB,EAAyB;AACrB,oBAAIsE,WAAW,EAAf;AACA,sBAAMF,SAAS,MAAM3F,MAAMC,KAAN,CAAY;AAC7BwF,6BAAS,CADoB;AAE7BC,2BAAO,IAFsB;AAG7B1C,+BAAWrB,KAAKE;AAHa,iBAAZ,EAIlBmE,KAJkB,CAIZ,SAJY,EAID5F,MAJC,EAArB;AAKA,qBAAK,MAAM6F,MAAX,IAAqBN,MAArB,EAA6B;AACzBE,6BAAShD,IAAT,CAAc;AACVN,+BAAO0D,OAAOpE,EADJ;AAEViB,+BAAOmD,OAAOxF;AAFJ,qBAAd;AAIH;AACDmF,wBAAQ/C,IAAR,CAAa;AACTN,2BAAOZ,KAAKE,EADH;AAETiB,2BAAOnB,KAAKlB,IAFH;AAGToF,8BAAUA;AAHD,iBAAb;AAKH;AACD,mBAAO,QAAKrD,OAAL,CAAaoD,OAAb,CAAP;AA1ByB;AA2B5B;AACKM,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAMC,SAAS,QAAKhD,IAAL,CAAU,MAAV,CAAf;AACA,kBAAMiD,WAAW,QAAKjD,IAAL,CAAU,UAAV,CAAjB;AACA,kBAAMkD,YAAY,QAAKlD,IAAL,CAAU,WAAV,CAAlB;AACA,kBAAMmD,SAAS,QAAKnD,IAAL,CAAU,QAAV,CAAf;AACA,kBAAMnD,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,gBAAIuG,SAASJ,OAAOK,YAApB;AACA,gBAAItG,WAAWiG,OAAOtE,EAAtB;AACAsE,mBAAOrE,WAAP,GAAqBwE,MAArB;AACAH,mBAAOjE,QAAP,GAAkBiE,OAAOjE,QAAP,GAAkB,CAAlB,GAAsB,CAAxC;AACAiE,mBAAOM,MAAP,GAAgBN,OAAOM,MAAP,GAAgB,CAAhB,GAAoB,CAApC;AACA,gBAAI5E,KAAKsE,OAAOtE,EAAhB;AACA,gBAAIA,KAAK,CAAT,EAAY;AACR;AACA,sBAAM+C,iBAAiB,MAAM,QAAK/E,qBAAL,CAA2BgC,EAA3B,CAA7B;AACA,oBAAI+C,eAAejE,WAAnB,EAAgC;AAC5B,2BAAO,QAAKkE,IAAL,CAAU,GAAV,EAAeD,eAAehE,OAA9B,CAAP;AACH;;AAED;AACAuF,uBAAOO,WAAP,GAAqBC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAArB;AACA,sBAAM7G,MAAMC,KAAN,CAAY;AACd4B,wBAAIA;AADU,iBAAZ,EAEHiC,MAFG,CAEIqC,MAFJ,CAAN;AAGA,sBAAM,QAAKnG,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BC,8BAAU2B;AADiB,iBAAzB,EAEHiC,MAFG,CAEI;AACNgB,6BAASqB,OAAOlE,UADV;AAENA,gCAAYkE,OAAOlE,UAFb;AAGNuE,kCAAcD,MAHR;AAINO,yCAAqBX,OAAOW;AAJtB,iBAFJ,CAAN;AAQA,sBAAM,QAAK9G,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC9BC,8BAAU2B;AADoB,iBAA5B,EAEHiC,MAFG,CAEI;AACNtC,+BAAW;AADL,iBAFJ,CAAN;AAKA,sBAAM,QAAKxB,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AAC1CC,8BAAU2B;AADgC,iBAAxC,EAEHiC,MAFG,CAEI;AACNtC,+BAAW;AADL,iBAFJ,CAAN;AAKA,qBAAK,MAAMG,IAAX,IAAmByE,QAAnB,EAA6B;AACzB,wBAAIzE,KAAKE,EAAL,GAAU,CAAd,EAAiB;AACb,8BAAM,QAAK7B,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3B+E,wCAAYrD,KAAKE,EADU;AAE3BL,uCAAW;AAFgB,yBAAzB,EAGHsC,MAHG,CAGI;AACNiD,0CAAcpF,KAAKoF,YADb;AAENC,0DAA8BrF,KAAKY,KAF7B;AAGNoB,sCAAUhC,KAAKgC;AAHT,yBAHJ,CAAN;AAQA,+BAAOhC,KAAKH,SAAZ;AACAG,6BAAKH,SAAL,GAAiB,CAAjB;AACA,8BAAM,QAAKxB,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC9B4B,gCAAIF,KAAKE;AADqB,yBAA5B,EAEHiC,MAFG,CAEInC,IAFJ,CAAN;AAGA,4BAAIsF,oBAAoB;AACpB1E,mCAAOZ,KAAKY,KADQ;AAEpB2E,8CAAkBb,SAFE;AAGpB7E,uCAAW;AAHS,yBAAxB;AAKA,8BAAM,QAAKxB,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AAC1C4B,gCAAIF,KAAKW;AADiC,yBAAxC,EAEHwB,MAFG,CAEImD,iBAFJ,CAAN;AAGH,qBAtBD,MAsBO;AACH,4BAAIA,oBAAoB;AACpB1E,mCAAOZ,KAAKY,KADQ;AAEpBrC,sCAAU2B,EAFU;AAGpBqF,8CAAkBb;AAHE,yBAAxB;AAKA,4BAAIc,SAAS,MAAM,QAAKnH,KAAL,CAAW,qBAAX,EAAkCqD,GAAlC,CAAsC4D,iBAAtC,CAAnB;AACAtF,6BAAKW,uBAAL,GAA+B6E,MAA/B;AACAxF,6BAAKzB,QAAL,GAAgB2B,EAAhB;AACA,8BAAM,QAAK7B,KAAL,CAAW,SAAX,EAAsBqD,GAAtB,CAA0B1B,IAA1B,CAAN;AACH;AACJ;AACV,qBAAI,MAAM,CAAC8C,KAAD,EAAQ9C,IAAR,CAAV,IAA2BwE,OAAO5C,OAAP,CAAe6D,OAAf,EAA3B,EAAoD;AACnD,wBAAGzF,KAAKH,SAAL,IAAkB,CAAlB,IAAuBG,KAAKE,EAAL,GAAU,CAApC,EAAsC;AACrC,8BAAM,QAAK7B,KAAL,CAAW,eAAX,EAA4BC,KAA5B,CAAkC;AACvC4B,gCAAGF,KAAKE;AAD+B,yBAAlC,EAEHiC,MAFG,CAEI;AACTtC,uCAAU;AADD,yBAFJ,CAAN;AAKA,qBAND,MAOK,IAAGG,KAAKH,SAAL,IAAkB,CAAlB,IAAuBG,KAAKE,EAAL,GAAU,CAApC,EAAsC;AAC1C,8BAAM,QAAK7B,KAAL,CAAW,eAAX,EAA4BC,KAA5B,CAAkC;AACvC4B,gCAAGF,KAAKE;AAD+B,yBAAlC,EAEHiC,MAFG,CAEI;AACTL,wCAAWgB;AADF,yBAFJ,CAAN;AAKA,qBANI,MAOA,IAAG9C,KAAKH,SAAL,IAAkB,CAAlB,IAAuBG,KAAKE,EAAL,IAAW,CAArC,EAAuC;AAC3C,8BAAM,QAAK7B,KAAL,CAAW,eAAX,EAA4BqD,GAA5B,CAAgC;AACrCnD,sCAAS2B,EAD4B;AAErC2B,qCAAQ7B,KAAK0F,GAFwB;AAGrC5D,wCAAWgB;AAH0B,yBAAhC,CAAN;AAKA;AACD;AACK,aAxFD,MAwFO;AACH,uBAAO0B,OAAOtE,EAAd;AACA;AACAsE,uBAAOmB,QAAP,GAAkBX,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAAlB;AACA3G,2BAAW,MAAMF,MAAMqD,GAAN,CAAU8C,MAAV,CAAjB;AACA,qBAAK,MAAMxE,IAAX,IAAmByE,QAAnB,EAA6B;AACzB,wBAAIa,oBAAoB;AACpB1E,+BAAOZ,KAAKY,KADQ;AAEpBrC,kCAAUA,QAFU;AAGpBgH,0CAAkBb;AAHE,qBAAxB;AAKA,wBAAIc,SAAS,MAAM,QAAKnH,KAAL,CAAW,qBAAX,EAAkCqD,GAAlC,CAAsC4D,iBAAtC,CAAnB;AACAtF,yBAAKW,uBAAL,GAA+B6E,MAA/B;AACAxF,yBAAKzB,QAAL,GAAgBA,QAAhB;AACAyB,yBAAKM,UAAL,GAAkB,CAAlB;AACA,0BAAM,QAAKjC,KAAL,CAAW,SAAX,EAAsBqD,GAAtB,CAA0B1B,IAA1B,CAAN;AACH;AACV,qBAAI,MAAM,CAAC8C,KAAD,EAAQ9C,IAAR,CAAV,IAA2BwE,OAAO5C,OAAP,CAAe6D,OAAf,EAA3B,EAAoD;AACnD,0BAAM,QAAKpH,KAAL,CAAW,eAAX,EAA4BqD,GAA5B,CAAgC;AACrCnD,kCAASA,QAD4B;AAErCsD,iCAAQ7B,KAAK0F,GAFwB;AAGrC5D,oCAAWgB;AAH0B,qBAAhC,CAAN;AAKA;AACK;AACD,gBAAI8C,MAAM,MAAM,QAAKvH,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AACxCC,0BAAUA,QAD8B;AAExC+B,4BAAY,CAF4B;AAGxCT,2BAAW;AAH6B,aAA5B,EAIbpB,MAJa,EAAhB;AAKA,gBAAImH,IAAIlH,MAAJ,GAAa,CAAjB,EAAoB;AAChB,oBAAImH,WAAW,MAAM,QAAKxH,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC7CC,8BAAUA,QADmC;AAE7C+B,gCAAY,CAFiC;AAG7CT,+BAAW;AAHkC,iBAA5B,EAIlB0C,GAJkB,CAId,cAJc,CAArB;AAKA,oBAAI6C,eAAe,MAAM,QAAK/G,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AACjDC,8BAAUA,QADuC;AAEjD+B,gCAAY,CAFqC;AAGjDT,+BAAW;AAHsC,iBAA5B,EAItBiG,QAJsB,CAIb,cAJa,CAAzB;AAKA,oBAAIC,WAAWC,KAAKC,GAAL,CAAS,GAAGb,YAAZ,CAAf;AACA,oBAAIc,WAAWF,KAAKG,GAAL,CAAS,GAAGf,YAAZ,CAAf;AACA,oBAAIgB,OAAO,MAAM,QAAK/H,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AACzCC,8BAAUA,QAD+B;AAEzC+B,gCAAY,CAF6B;AAGzCT,+BAAW;AAH8B,iBAA5B,EAIdiG,QAJc,CAIL,MAJK,CAAjB;AAKA,oBAAIO,UAAUL,KAAKC,GAAL,CAAS,GAAGG,IAAZ,CAAd;AACA,oBAAIE,UAAUN,KAAKG,GAAL,CAAS,GAAGC,IAAZ,CAAd;AACA,oBAAIG,aAAa,EAAjB;AACA,oBAAGL,YAAYH,QAAf,EAAwB;AACpBQ,iCAAaL,QAAb;AACH,iBAFD,MAGI;AACAK,iCAAaL,WAAW,GAAX,GAAiBH,QAA9B;AACH;AACD,oBAAIS,YAAYF,UAAU,GAAV,GAAgBD,OAAhC;AACA,sBAAM,QAAKhI,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5B4B,wBAAI3B;AADwB,iBAA1B,EAEH4D,MAFG,CAEI;AACNF,kCAAc4D,QADR;AAENT,kCAAcmB,UAFR;AAGNE,gCAAYD,SAHN;AAINE,sCAAkBR,QAJZ;AAKNS,oCAAgBL;AALV,iBAFJ,CAAN;AASH,aArCD,MAqCO;AACH,oBAAIrG,OAAO;AACPgC,kCAAc2D,IAAI,CAAJ,EAAO3D,YADd;AAEPmD,kCAAcQ,IAAI,CAAJ,EAAOR,YAFd;AAGPqB,gCAAYb,IAAI,CAAJ,EAAOQ,IAHZ;AAIPM,sCAAkBd,IAAI,CAAJ,EAAOR,YAJlB;AAKPuB,oCAAgBf,IAAI,CAAJ,EAAOQ;AALhB,iBAAX;AAOA,sBAAM,QAAK/H,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5B4B,wBAAI3B;AADwB,iBAA1B,EAEH4D,MAFG,CAEIlC,IAFJ,CAAN;AAGH;AACD,mBAAO,QAAKY,OAAL,CAAatC,QAAb,CAAP;AAnLgB;AAoLnB;AACKqI,qBAAN,GAA0B;AAAA;;AAAA;AACtB,gBAAIhH,OAAO,QAAK4B,IAAL,CAAU,EAAV,CAAX;AACN,gBAAIjD,WAAWqB,KAAKrB,QAApB;;AAEA;AACM,kBAAM0E,iBAAiB,MAAM,QAAK/E,qBAAL,CAA2BK,QAA3B,CAA7B;AACA,gBAAI0E,eAAejE,WAAnB,EAAgC;AAC5B,uBAAO,QAAKkE,IAAL,CAAU,GAAV,EAAeD,eAAehE,OAA9B,CAAP;AACH;AACD,kBAAM,QAAKZ,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AAC1C4B,oBAAIN,KAAKe;AADiC,aAAxC,EAEHwB,MAFG,CAEI;AACNvB,uBAAOhB,KAAKgB;AADN,aAFJ,CAAN;AAKA,kBAAM,QAAKvC,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC9B4B,oBAAIN,KAAKM;AADqB,aAA5B,EAEHiC,MAFG,CAEIvC,IAFJ,CAAN;AAGN,gBAAIgG,MAAM,MAAM,QAAKvH,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AACxCC,0BAAUA,QAD8B;AAExC+B,4BAAY,CAF4B;AAGxCT,2BAAW;AAH6B,aAA5B,EAIbpB,MAJa,EAAhB;AAKA,gBAAGmH,IAAIlH,MAAJ,IAAc,CAAjB,EAAmB;AAClB,uBAAO,QAAKwE,IAAL,CAAU,GAAV,EAAc,aAAd,CAAP;AACA;AACK,kBAAM,QAAK7E,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3B+E,4BAAYzD,KAAKM,EADU;AAE3BL,2BAAW;AAFgB,aAAzB,EAGHsC,MAHG,CAGI;AACNiD,8BAAcxF,KAAKwF,YADb;AAENC,8CAA8BzF,KAAKgB,KAF7B;AAGNoB,0BAAUpC,KAAKoC;AAHT,aAHJ,CAAN;AAQA,mBAAOpC,KAAKgB,KAAZ;;AAEA,gBAAIgF,IAAIlH,MAAJ,GAAa,CAAjB,EAAoB;AAChB,oBAAImH,WAAW,MAAM,QAAKxH,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC7CC,8BAAUA,QADmC;AAE7C+B,gCAAY,CAFiC;AAG7CT,+BAAW;AAHkC,iBAA5B,EAIlB0C,GAJkB,CAId,cAJc,CAArB;AAKA,oBAAI6C,eAAe,MAAM,QAAK/G,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AACjDC,8BAAUA,QADuC;AAEjD+B,gCAAY,CAFqC;AAGjDT,+BAAW;AAHsC,iBAA5B,EAItBiG,QAJsB,CAIb,cAJa,CAAzB;AAKA,oBAAIC,WAAWC,KAAKC,GAAL,CAAS,GAAGb,YAAZ,CAAf;AACA,oBAAIc,WAAWF,KAAKG,GAAL,CAAS,GAAGf,YAAZ,CAAf;AACA,oBAAIgB,OAAO,MAAM,QAAK/H,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AACzCC,8BAAUA,QAD+B;AAEzC+B,gCAAY,CAF6B;AAGzCT,+BAAW;AAH8B,iBAA5B,EAIdiG,QAJc,CAIL,MAJK,CAAjB;AAKA,oBAAIO,UAAUL,KAAKC,GAAL,CAAS,GAAGG,IAAZ,CAAd;AACA,oBAAIE,UAAUN,KAAKG,GAAL,CAAS,GAAGC,IAAZ,CAAd;AACA,oBAAIG,aAAa,EAAjB;AACA,oBAAGL,YAAYH,QAAf,EAAwB;AACpBQ,iCAAaL,QAAb;AACH,iBAFD,MAGI;AACAK,iCAAaL,WAAW,GAAX,GAAiBH,QAA9B;AACH;AACD,oBAAIS,YAAYF,UAAU,GAAV,GAAgBD,OAAhC;AACA,sBAAM,QAAKhI,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5B4B,wBAAI3B;AADwB,iBAA1B,EAEH4D,MAFG,CAEI;AACNF,kCAAc4D,QADR;AAENT,kCAAcmB,UAFR;AAGNE,gCAAYD,SAHN;AAINE,sCAAkBR,QAJZ;AAKNS,oCAAgBL;AALV,iBAFJ,CAAN;AASH,aArCD,MAqCO,IAAGV,IAAIlH,MAAJ,IAAc,CAAjB,EAAmB;AACtB,oBAAIuB,OAAO;AACPgC,kCAAc2D,IAAI,CAAJ,EAAO3D,YADd;AAEPmD,kCAAcQ,IAAI,CAAJ,EAAOR,YAFd;AAGPqB,gCAAYb,IAAI,CAAJ,EAAOQ,IAHZ;AAIPM,sCAAkBd,IAAI,CAAJ,EAAOR,YAJlB;AAKPuB,oCAAgBf,IAAI,CAAJ,EAAOQ;AALhB,iBAAX;AAOA,sBAAM,QAAK/H,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5B4B,wBAAI3B;AADwB,iBAA1B,EAEH4D,MAFG,CAEIlC,IAFJ,CAAN;AAGH;AACD,mBAAO,QAAKY,OAAL,EAAP;AApFsB;AAqFzB;AACKgG,kBAAN,GAAuB;AAAA;;AAAA;AACnB,kBAAM5G,OAAO,QAAKuB,IAAL,CAAU,MAAV,CAAb;AACA,gBAAIvB,KAAKC,EAAL,GAAU,CAAd,EAAiB;AACb,sBAAM7B,QAAQ,QAAKA,KAAL,CAAW,SAAX,CAAd;AACA,sBAAMuB,OAAO,MAAMvB,MAAMC,KAAN,CAAY;AAC3B4B,wBAAI,CAAC,IAAD,EAAOD,KAAKC,EAAZ,CADuB;AAE3B8B,8BAAU/B,KAAK+B,QAFY;AAG3BnC,+BAAW;AAHgB,iBAAZ,EAIhBO,IAJgB,EAAnB;AAKA,oBAAI,CAACoC,MAAMsE,OAAN,CAAclH,IAAd,CAAL,EAA0B;AACtB,2BAAO,QAAKsD,IAAL,CAAU,GAAV,EAAe,IAAf,CAAP;AACH,iBAFD,MAEO;AACH,2BAAO,QAAKrC,OAAL,EAAP;AACH;AACJ,aAZD,MAYO;AACH,sBAAMxC,QAAQ,QAAKA,KAAL,CAAW,SAAX,CAAd;AACA,sBAAMuB,OAAO,MAAMvB,MAAMC,KAAN,CAAY;AAC3B0D,8BAAU/B,KAAK+B,QADY;AAE3BnC,+BAAW;AAFgB,iBAAZ,EAGhBO,IAHgB,EAAnB;AAIA,oBAAI,CAACoC,MAAMsE,OAAN,CAAclH,IAAd,CAAL,EAA0B;AACtB,2BAAO,QAAKsD,IAAL,CAAU,GAAV,EAAe,IAAf,CAAP;AACH,iBAFD,MAEO;AACH,2BAAO,QAAKrC,OAAL,EAAP;AACH;AACJ;AAzBkB;AA0BtB;AACKkG,oBAAN,GAAyB;AAAA;;AAAA;AACrB,kBAAM7G,KAAK,QAAKsB,IAAL,CAAU,IAAV,CAAX;AACA,kBAAMwF,OAAO,QAAKxF,IAAL,CAAU,MAAV,CAAb;AACA,kBAAMnD,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMuB,OAAO,MAAMvB,MAAMC,KAAN,CAAY;AAC3B4B,oBAAIA;AADuB,aAAZ,EAEhBiC,MAFgB,CAET;AACNL,4BAAYkF;AADN,aAFS,CAAnB;AAKA,mBAAO,QAAKnG,OAAL,CAAajB,IAAb,CAAP;AATqB;AAUxB;AACKqH,yBAAN,GAA8B;AAAA;;AAAA;AAC1B,kBAAM/G,KAAK,QAAKsB,IAAL,CAAU,IAAV,CAAX;AACA,kBAAM0F,aAAa,QAAK1F,IAAL,CAAU,YAAV,CAAnB;AACA,kBAAMnD,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMuB,OAAO,MAAMvB,MAAMC,KAAN,CAAY;AAC3B4B,oBAAIA;AADuB,aAAZ,EAEhBiC,MAFgB,CAET;AACN+E,4BAAYA;AADN,aAFS,CAAnB;AAKA,mBAAO,QAAKrG,OAAL,CAAajB,IAAb,CAAP;AAT0B;AAU7B;AACKuH,qBAAN,GAA0B;AAAA;;AAAA;AACtB,kBAAMjH,KAAK,QAAKR,GAAL,CAAS,IAAT,CAAX;AACA,kBAAMrB,QAAQ,QAAKA,KAAL,CAAW,eAAX,CAAd;AACA,kBAAMuB,OAAO,MAAMvB,MAAMC,KAAN,CAAY;AAC3BC,0BAAU2B,EADiB;AAE3BL,2BAAU;AAFiB,aAAZ,EAGhBpB,MAHgB,EAAnB;AAIA;AACA,mBAAO,QAAKoC,OAAL,CAAajB,IAAb,CAAP;AARsB;AASzB;AACKwH,iBAAN,GAAsB;AAAA;;AAAA;AAClB,kBAAM1B,MAAM,QAAKlE,IAAL,CAAU,KAAV,CAAZ;AACA,kBAAMtB,KAAK,QAAKsB,IAAL,CAAU,UAAV,CAAX;AACA,gBAAIvB,OAAO;AACP1B,0BAAU2B,EADH;AAEP2B,yBAAS6D;AAFF,aAAX;AAIA,kBAAM,QAAKrH,KAAL,CAAW,eAAX,EAA4BqD,GAA5B,CAAgCzB,IAAhC,CAAN;AACA,mBAAO,QAAKY,OAAL,EAAP;AARkB;AASrB;AACKwG,wBAAN,GAA6B;AAAA;;AAAA;AACzB,kBAAMlJ,UAAU,QAAKqD,IAAL,CAAU,SAAV,CAAhB;AACA,kBAAM5B,OAAO,MAAM,QAAKvB,KAAL,CAAW,eAAX,EAA4BC,KAA5B,CAAkC;AACjDC,0BAAUJ,OADuC;AAEjD0B,2BAAU;AAFuC,aAAlC,EAGhBC,KAHgB,CAGV,gBAHU,EAGQrB,MAHR,EAAnB;AAIA,gBAAI6I,cAAc,EAAlB;AACA,iBAAK,MAAMtH,IAAX,IAAmBJ,IAAnB,EAAyB;AACrB,oBAAI2H,QAAQ;AACRrH,wBAAIF,KAAKE,EADD;AAERwF,yBAAK1F,KAAK6B,OAFF;AAGpBhC,+BAAU;AAHU,iBAAZ;AAKAyH,4BAAYpG,IAAZ,CAAiBqG,KAAjB;AACH;AACD,gBAAItH,OAAO;AACPqH,6BAAaA;AADN,aAAX;AAGA,mBAAO,QAAKzG,OAAL,CAAaZ,IAAb,CAAP;AAlByB;AAmB5B;AACKuH,2BAAN,GAAgC;AAAA;;AAAA;AAC5B,kBAAM9B,MAAM,QAAKlE,IAAL,CAAU,KAAV,CAAZ;AACA,kBAAMtB,KAAK,QAAKsB,IAAL,CAAU,IAAV,CAAX;AACA,kBAAM,QAAKnD,KAAL,CAAW,eAAX,EAA4BC,KAA5B,CAAkC;AACpC4B,oBAAIA;AADgC,aAAlC,EAEHuH,KAFG,CAEG,CAFH,EAEMtF,MAFN,CAEa;AACftC,2BAAW;AADI,aAFb,CAAN;AAKA,mBAAO,QAAKgB,OAAL,CAAa,QAAb,CAAP;AAR4B;AAS/B;AACK6G,qBAAN,GAA0B;AAAA;;AAAA;AACtB,gBAAI,CAAC,QAAKC,MAAV,EAAkB;AACd,uBAAO,KAAP;AACH;AACD,kBAAMnD,SAAS,QAAKhD,IAAL,EAAf;AACA,gBAAI5B,OAAO4E,OAAO5E,IAAlB;AACA;AACA,kBAAMvB,QAAQ,QAAKA,KAAL,CAAW,eAAX,CAAd;AACA,iBAAK,MAAM2B,IAAX,IAAmBJ,IAAnB,EAAyB;AACrB,oBAAIM,KAAKF,KAAKE,EAAd;AACA,oBAAI8G,OAAOhC,SAAShF,KAAK8B,UAAd,CAAX;AACA;AACA,sBAAM,QAAKzD,KAAL,CAAW,eAAX,EAA4BC,KAA5B,CAAkC;AACpC4B,wBAAIA;AADgC,iBAAlC,EAEHiC,MAFG,CAEI;AACNL,gCAAYkF;AADN,iBAFJ,CAAN;AAKH;AACD,mBAAO,QAAKnG,OAAL,EAAP;AAlBsB;AAmBzB;AACK+G,0BAAN,GAA+B;AAAA;;AAAA;AAC3B,kBAAM1H,KAAK,QAAKsB,IAAL,CAAU,IAAV,CAAX;AACAjC,oBAAQ2C,GAAR,CAAYhC,EAAZ;AACA,kBAAM,QAAK7B,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5B4B,oBAAIA;AADwB,aAA1B,EAEHuH,KAFG,CAEG,CAFH,EAEMtF,MAFN,CAEa;AACf0C,8BAAc;AADC,aAFb,CAAN;AAKA,mBAAO,QAAKhE,OAAL,EAAP;AAR2B;AAS9B;AACKgH,iBAAN,GAAsB;AAAA;;AAAA;AAClB,kBAAM3H,KAAK,QAAKsB,IAAL,CAAU,IAAV,CAAX;;AAEA;AACA,kBAAMyB,iBAAiB,MAAM,QAAK/E,qBAAL,CAA2BgC,EAA3B,CAA7B;AACA,gBAAI+C,eAAejE,WAAnB,EAAgC;AAC5B,uBAAO,QAAKkE,IAAL,CAAU,GAAV,EAAeD,eAAehE,OAA9B,CAAP;AACH;;AAED,kBAAM,QAAKZ,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5B4B,oBAAIA;AADwB,aAA1B,EAEHuH,KAFG,CAEG,CAFH,EAEMtF,MAFN,CAEa;AACftC,2BAAW;AADI,aAFb,CAAN;AAKA,kBAAM,QAAKxB,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC9BC,0BAAU2B;AADoB,aAA5B,EAEHiC,MAFG,CAEI;AACNtC,2BAAW;AADL,aAFJ,CAAN;AAKA,kBAAM,QAAKxB,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AAC1CC,0BAAU2B;AADgC,aAAxC,EAEHiC,MAFG,CAEI;AACNtC,2BAAW;AADL,aAFJ,CAAN;AAKA,mBAAO,QAAKgB,OAAL,EAAP;AAxBkB;AAyBrB;AACKiH,0BAAN,GAA+B;AAAA;;AAAA;AAC3B,gBAAIpC,MAAM,QAAKlE,IAAL,CAAU,KAAV,CAAV;AACA,gBAAIuG,YAAYvF,MAAMwF,MAAN,CAAa,uBAAb,CAAhB;AACA,gBAAIC,YAAYzF,MAAMwF,MAAN,CAAa,uBAAb,CAAhB;AACA,gBAAIE,SAAS1F,MAAMwF,MAAN,CAAa,mBAAb,CAAb;AACA,gBAAIG,MAAM,IAAIpK,MAAMqK,IAAN,CAAWC,MAAX,CAAkBC,GAAtB,CAA0BP,SAA1B,EAAqCE,SAArC,CAAV;AACA,gBAAID,SAAS,IAAIjK,MAAMwK,IAAN,CAAWC,MAAf,EAAb;AACA,gBAAIC,UAAUjG,MAAMwF,MAAN,CAAa,oBAAb,CAAd;AACA,gBAAGS,WAAW,CAAd,EAAgB;AACZT,uBAAOU,IAAP,GAAc3K,MAAM2K,IAAN,CAAWC,OAAzB;AACH,aAFD,MAGK,IAAGF,WAAW,CAAd,EAAgB;AACjBT,uBAAOU,IAAP,GAAc3K,MAAM2K,IAAN,CAAWE,OAAzB;AACH,aAFI,MAGA,IAAGH,WAAW,CAAd,EAAgB;AACjBT,uBAAOU,IAAP,GAAc3K,MAAM2K,IAAN,CAAWG,OAAzB;AACH,aAFI,MAGA,IAAGJ,WAAW,CAAd,EAAgB;AACjBT,uBAAOU,IAAP,GAAc3K,MAAM2K,IAAN,CAAWI,QAAzB;AACH,aAFI,MAGA,IAAGL,WAAW,CAAd,EAAgB;AACjBT,uBAAOU,IAAP,GAAc3K,MAAM2K,IAAN,CAAWK,QAAzB;AACH;AACD,gBAAIC,gBAAgB,IAAIjL,MAAMkL,EAAN,CAASC,aAAb,CAA2Bf,GAA3B,EAAgCH,MAAhC,CAApB;AACA,gBAAImB,SAAS3G,MAAMwF,MAAN,CAAa,mBAAb,CAAb;AACA,gBAAIoB,MAAM5G,MAAM6G,IAAN,CAAW,EAAX,CAAV;AACA,kBAAM7G,MAAMC,OAAN,CAAc,GAAd,CAAN;AACA,kBAAM6G;AAAA,6CAAc,aAAW;AAC3B,2BAAO,IAAIC,OAAJ,CAAY,UAACC,OAAD,EAAUC,MAAV,EAAqB;AACpC,4BAAI;AACAT,0CAAcU,KAAd,CAAoBhE,GAApB,EAAyByD,MAAzB,EAAiCC,GAAjC,EAAsC,UAASO,GAAT,EAAcC,QAAd,EAAwBC,QAAxB,EAAkC;AACpE,oCAAIF,GAAJ,EAAS;AACLpK,4CAAQ2C,GAAR,CAAYyH,GAAZ;AACA;AACH,iCAHD,MAGO;AACH,wCAAIE,SAASC,UAAT,IAAuB,GAA3B,EAAgC;AAC5BN,gDAAQI,SAASR,GAAjB;AACH,qCAFD,MAEO;AACH7J,gDAAQ2C,GAAR,CAAY2H,SAASC,UAArB;AACH;AACJ;AACJ,6BAXD;AAYH,yBAbD,CAaE,OAAOC,CAAP,EAAU;AACR,mCAAOP,QAAQ,IAAR,CAAP;AACH;AACJ,qBAjBM,CAAP;AAkBH,iBAnBK;;AAAA;AAAA;AAAA;AAAA,gBAAN;AAoBA,kBAAMQ,WAAW,MAAMV,aAAvB;AACA/J,oBAAQ2C,GAAR,CAAY8H,QAAZ;AACA,gBAAIC,UAAU/B,SAAS8B,QAAvB;AACA,mBAAO,QAAKnJ,OAAL,CAAaoJ,OAAb,CAAP;AAlD2B;AAmD9B;AAhgC+B,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\goods.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\nconst fs = require('fs');\nconst path = require(\"path\");\nconst qiniu = require('qiniu');\nmodule.exports = class extends Base {\n\n    /**\n     * 检查商品是否正在参与秒杀活动\n     * @param {number} goodsId 商品ID\n     * @returns {Promise<Object>} 返回检查结果\n     */\n    async checkGoodsInFlashSale(goodsId) {\n        try {\n            // 检查是否有进行中或即将开始的秒杀活动\n            const activeCampaigns = await this.model('flash_sale_campaigns').where({\n                goods_id: goodsId,\n                status: ['IN', ['active', 'upcoming']]\n            }).select();\n\n            if (activeCampaigns.length > 0) {\n                const campaignNames = activeCampaigns.map(c => c.name).join('、');\n                return {\n                    inFlashSale: true,\n                    message: `该商品正在参与秒杀活动：${campaignNames}，无法进行此操作`,\n                    campaigns: activeCampaigns\n                };\n            }\n\n            // 检查是否有进行中的轮次\n            const activeRounds = await this.model('flash_sale_rounds').alias('r')\n                .join('flash_sale_campaigns c', 'r.campaign_id = c.id')\n                .where({\n                    'c.goods_id': goodsId,\n                    'r.status': ['IN', ['active', 'upcoming']]\n                }).select();\n\n            if (activeRounds.length > 0) {\n                return {\n                    inFlashSale: true,\n                    message: `该商品有正在进行或即将开始的秒杀轮次，无法进行此操作`,\n                    rounds: activeRounds\n                };\n            }\n\n            return {\n                inFlashSale: false,\n                message: '商品未参与秒杀活动，可以进行操作'\n            };\n\n        } catch (error) {\n            console.error('检查商品秒杀状态失败:', error);\n            return {\n                inFlashSale: false,\n                message: '检查失败，但允许操作',\n                error: error.message\n            };\n        }\n    }\n\n    /**\n     * index action\n     * @return {Promise} []\n     */\n    async indexAction() {\n        const page = this.get('page') || 1;\n        const size = this.get('size');\n        const name = this.get('name') || '';\n        const model = this.model('goods');\n        const data = await model.where({\n            name: ['like', `%${name}%`],\n            is_delete: 0\n        }).order(['sort_order asc']).page(page, size).countSelect();\n        // let newData = data;\n        for (const item of data.data) {\n            const info = await this.model('category').where({\n                id: item.category_id\n            }).find();\n            item.category_name = info.name;\n            if (item.is_on_sale == 1) {\n                item.is_on_sale = true;\n            } else {\n                item.is_on_sale = false;\n            }\n            if (item.is_index == 1) {\n                item.is_index = true;\n            } else {\n                item.is_index = false;\n            }\n            let product = await this.model('product').where({\n                goods_id: item.id,\n                is_delete: 0\n            }).select();\n            for (const ele of product) {\n                let spec = await this.model('goods_specification').where({\n                    id: ele.goods_specification_ids,\n                    is_delete: 0\n                }).find();\n                ele.value = spec.value;\n                ele.is_on_sale = ele.is_on_sale ? \"1\" : \"0\";\n            }\n            item.product = product;\n        }\n        return this.success(data);\n    }\n    async getExpressDataAction() {\n        let kd = [];\n        let cate = [];\n        const kdData = await this.model('freight_template').where({\n            is_delete: 0\n        }).select();\n        for (const item of kdData) {\n            kd.push({\n                value: item.id,\n                label: item.name\n            })\n        }\n        const cateData = await this.model('category').where({\n            parent_id: 0\n        }).select();\n        for (const item of cateData) {\n            cate.push({\n                value: item.id,\n                label: item.name\n            })\n        }\n        let infoData = {\n            kd: kd,\n            cate: cate\n        };\n        return this.success(infoData);\n    }\n    async copygoodsAction() {\n        const goodsId = this.post('id');\n        let data = await this.model('goods').where({\n            id: goodsId\n        }).find();\n        delete data.id;\n        data.is_on_sale = 0;\n        let insertId = await this.model('goods').add(data);\n        let goodsGallery = await this.model('goods_gallery').where({\n            goods_id: goodsId,\n            is_delete:0,\n        }).select();\n        for (const item of goodsGallery) {\n            let gallery = {\n                img_url: item.img_url,\n                sort_order: item.sort_order,\n                goods_id: insertId\n            }\n            await this.model('goods_gallery').add(gallery);\n        }\n        return this.success(insertId);\n    }\n    async updateStock(goods_sn, goods_number) {\n        console.log('存在，现在就更新');\n        await this.model('product').where({\n            goods_sn: goods_sn\n        }).update({\n            goods_number: goods_number\n        });\n    }\n    async updateGoodsNumberAction() {\n        let all_goods = await this.model('goods').where({\n            is_delete: 0,\n            is_on_sale: 1\n        }).select();\n        for (const item of all_goods) {\n            let goodsSum = await this.model('product').where({\n                goods_id: item.id\n            }).sum('goods_number');\n            await this.model('goods').where({\n                id: item.id\n            }).update({\n                goods_number: goodsSum\n            });\n            await think.timeout(2000);\n        }\n        return this.success();\n    }\n    async onsaleAction() {\n        const page = this.get('page') || 1;\n        const size = this.get('size');\n        const model = this.model('goods');\n        const data = await model.where({\n            is_delete: 0,\n            is_on_sale: 1\n        }).order(['sort_order asc']).page(page, size).countSelect();\n        for (const item of data.data) {\n            const info = await this.model('category').where({\n                id: item.category_id\n            }).find();\n            item.category_name = info.name;\n            // if (info.parent_id != 0) {\n            //     const parentInfo = await this.model('category').where({id: info.parent_id}).find();\n            //     item.category_p_name = parentInfo.name;\n            // }\n            if (item.is_on_sale == 1) {\n                item.is_on_sale = true;\n            } else {\n                item.is_on_sale = false;\n            }\n            if (item.is_index == 1) {\n                item.is_index = true;\n            } else {\n                item.is_index = false;\n            }\n            let product = await this.model('product').where({\n                goods_id: item.id,\n                is_delete: 0\n            }).select();\n            for (const ele of product) {\n                let spec = await this.model('goods_specification').where({\n                    id: ele.goods_specification_ids,\n                    is_delete: 0\n                }).find();\n                ele.value = spec.value;\n                ele.is_on_sale = ele.is_on_sale ? \"1\" : \"0\";\n            }\n            item.product = product;\n        }\n        return this.success(data);\n    }\n    async outAction() {\n        const page = this.get('page') || 1;\n        const size = this.get('size');\n        const model = this.model('goods');\n        const data = await model.where({\n            is_delete: 0,\n            goods_number: ['<=', 0]\n        }).order(['sort_order asc']).page(page, size).countSelect();\n        for (const item of data.data) {\n            const info = await this.model('category').where({\n                id: item.category_id\n            }).find();\n            item.category_name = info.name;\n            if (item.is_on_sale == 1) {\n                item.is_on_sale = true;\n            } else {\n                item.is_on_sale = false;\n            }\n            if (item.is_index == 1) {\n                item.is_index = true;\n            } else {\n                item.is_index = false;\n            }\n            let product = await this.model('product').where({\n                goods_id: item.id,\n                is_delete: 0\n            }).select();\n            for (const ele of product) {\n                let spec = await this.model('goods_specification').where({\n                    id: ele.goods_specification_ids,\n                    is_delete: 0\n                }).find();\n                ele.value = spec.value;\n                ele.is_on_sale = ele.is_on_sale ? \"1\" : \"0\";\n            }\n            item.product = product;\n        }\n        return this.success(data);\n    }\n    async dropAction() {\n        const page = this.get('page') || 1;\n        const size = this.get('size');\n        const model = this.model('goods');\n        const data = await model.where({\n            is_delete: 0,\n            is_on_sale: 0\n        }).order(['id DESC']).page(page, size).countSelect();\n        for (const item of data.data) {\n            const info = await this.model('category').where({\n                id: item.category_id\n            }).find();\n            item.category_name = info.name;\n            if (item.is_on_sale == 1) {\n                item.is_on_sale = true;\n            } else {\n                item.is_on_sale = false;\n            }\n            if (item.is_index == 1) {\n                item.is_index = true;\n            } else {\n                item.is_index = false;\n            }\n            let product = await this.model('product').where({\n                goods_id: item.id,\n                is_delete: 0\n            }).select();\n            for (const ele of product) {\n                let spec = await this.model('goods_specification').where({\n                    id: ele.goods_specification_ids,\n                    is_delete: 0\n                }).find();\n                ele.value = spec.value;\n                ele.is_on_sale = ele.is_on_sale ? \"1\" : \"0\";\n            }\n            item.product = product;\n        }\n        return this.success(data);\n    }\n    async sortAction() {\n        const page = this.get('page') || 1;\n        const size = this.get('size');\n        const model = this.model('goods');\n        const index = this.get('index');\n        if (index == 1) {\n            const data = await model.where({\n                is_delete: 0\n            }).order(['sell_volume DESC']).page(page, size).countSelect();\n            for (const item of data.data) {\n                const info = await this.model('category').where({\n                    id: item.category_id\n                }).find();\n                item.category_name = info.name;\n                if (item.is_on_sale == 1) {\n                    item.is_on_sale = true;\n                } else {\n                    item.is_on_sale = false;\n                }\n                if (item.is_index == 1) {\n                    item.is_index = true;\n                } else {\n                    item.is_index = false;\n                }\n                let product = await this.model('product').where({\n                    goods_id: item.id,\n                    is_delete: 0\n                }).select();\n                for (const ele of product) {\n                    let spec = await this.model('goods_specification').where({\n                        id: ele.goods_specification_ids,\n                        is_delete: 0\n                    }).find();\n                    ele.value = spec.value;\n                    ele.is_on_sale = ele.is_on_sale ? \"1\" : \"0\";\n                }\n                item.product = product;\n            }\n            return this.success(data);\n        } else if (index == 2) {\n            const data = await model.where({\n                is_delete: 0\n            }).order(['retail_price DESC']).page(page, size).countSelect();\n            for (const item of data.data) {\n                const info = await this.model('category').where({\n                    id: item.category_id\n                }).find();\n                item.category_name = info.name;\n                if (item.is_on_sale == 1) {\n                    item.is_on_sale = true;\n                } else {\n                    item.is_on_sale = false;\n                }\n                if (item.is_index == 1) {\n                    item.is_index = true;\n                } else {\n                    item.is_index = false;\n                }\n                let product = await this.model('product').where({\n                    goods_id: item.id,\n                    is_delete: 0\n                }).select();\n                for (const ele of product) {\n                    let spec = await this.model('goods_specification').where({\n                        id: ele.goods_specification_ids,\n                        is_delete: 0\n                    }).find();\n                    ele.value = spec.value;\n                    ele.is_on_sale = ele.is_on_sale ? \"1\" : \"0\";\n                }\n                item.product = product;\n            }\n            return this.success(data);\n        } else if (index == 3) {\n            const data = await model.where({\n                is_delete: 0\n            }).order(['goods_number DESC']).page(page, size).countSelect();\n            for (const item of data.data) {\n                const info = await this.model('category').where({\n                    id: item.category_id\n                }).find();\n                item.category_name = info.name;\n                if (item.is_on_sale == 1) {\n                    item.is_on_sale = true;\n                } else {\n                    item.is_on_sale = false;\n                }\n                if (item.is_index == 1) {\n                    item.is_index = true;\n                } else {\n                    item.is_index = false;\n                }\n                let product = await this.model('product').where({\n                    goods_id: item.id,\n                    is_delete: 0\n                }).select();\n                for (const ele of product) {\n                    let spec = await this.model('goods_specification').where({\n                        id: ele.goods_specification_ids,\n                        is_delete: 0\n                    }).find();\n                    ele.value = spec.value;\n                    ele.is_on_sale = ele.is_on_sale ? \"1\" : \"0\";\n                }\n                item.product = product;\n            }\n            return this.success(data);\n        }\n    }\n    async saleStatusAction() {\n        const id = this.get('id');\n        const status = this.get('status');\n        let sale = 0;\n        if (status == 'true') {\n            sale = 1;\n        }\n\n        // 如果是下架操作，检查商品是否正在参与秒杀\n        if (sale === 0) {\n            const flashSaleCheck = await this.checkGoodsInFlashSale(id);\n            if (flashSaleCheck.inFlashSale) {\n                return this.fail(400, flashSaleCheck.message);\n            }\n        }\n\n        const model = this.model('goods');\n        await model.where({\n            id: id\n        }).update({\n            is_on_sale: sale\n        });\n        await this.model('cart').where({\n            goods_id: id\n        }).update({\n            is_on_sale: sale,\n            checked: sale\n        });\n\n        return this.success();\n    }\n    async productStatusAction() {\n        const id = this.get('id');\n        const status = this.get('status');\n        const model = this.model('product');\n        await model.where({\n            id: id\n        }).update({\n            is_on_sale: status\n        });\n\t\t// 4.14更新\n\t\tawait this.model('cart').where({\n\t\t\tproduct_id: id,\n\t\t\tis_delete: 0\n\t\t}).update({\n\t\t\tis_on_sale: status\n\t\t})\n    }\n    async indexShowStatusAction() {\n        const id = this.get('id');\n        const status = this.get('status');\n        let stat = 0;\n        if (status == 'true') {\n            stat = 1;\n        }\n        const model = this.model('goods');\n        await model.where({\n            id: id\n        }).update({\n            is_index: stat\n        });\n    }\n    async infoAction() {\n        const id = this.get('id');\n        const model = this.model('goods');\n        const data = await model.where({\n            id: id\n        }).find();\n        let category_id = data.category_id;\n        let infoData = {\n            info: data,\n            category_id: category_id,\n        };\n        return this.success(infoData);\n    }\n    async getAllSpecificationAction() {\n        const specInfo = await this.model('specification').where({\n            id: ['>', 0]\n        }).select();\n        let specOptionsData = [];\n        for (const spitem of specInfo) {\n            let info = {\n                value: spitem.id,\n                label: spitem.name\n            };\n            specOptionsData.push(info);\n        }\n        return this.success(specOptionsData);\n    }\n    async getAllCategory1Action() { // 我写的算法\n        const model = this.model('category');\n        const data = await model.where({\n            is_show: 1,\n            level: 'L1'\n        }).select();\n        const c_data = await model.where({\n            is_show: 1,\n            level: 'L2'\n        }).select();\n        let newData = [];\n        for (const item of data) {\n            let children = [];\n            for (const citem of c_data) {\n                if (citem.parent_id == item.id) {\n                    children.push({\n                        value: citem.id,\n                        label: citem.name\n                    })\n                }\n            }\n            newData.push({\n                value: item.id,\n                label: item.name,\n                children: children\n            });\n        }\n        return this.success(newData);\n    }\n    async getAllCategoryAction() { // 老婆的算法\n        const model = this.model('category');\n        const data = await model.where({\n            is_show: 1,\n            level: 'L1'\n        }).field('id,name').select();\n        let newData = [];\n        for (const item of data) {\n            let children = [];\n            const c_data = await model.where({\n                is_show: 1,\n                level: 'L2',\n                parent_id: item.id\n            }).field('id,name').select();\n            for (const c_item of c_data) {\n                children.push({\n                    value: c_item.id,\n                    label: c_item.name\n                })\n            }\n            newData.push({\n                value: item.id,\n                label: item.name,\n                children: children\n            });\n        }\n        return this.success(newData);\n    }\n    async storeAction() {\n        const values = this.post('info');\n        const specData = this.post('specData');\n        const specValue = this.post('specValue');\n        const cateId = this.post('cateId');\n        const model = this.model('goods');\n        let picUrl = values.list_pic_url;\n        let goods_id = values.id;\n        values.category_id = cateId;\n        values.is_index = values.is_index ? 1 : 0;\n        values.is_new = values.is_new ? 1 : 0;\n        let id = values.id;\n        if (id > 0) {\n            // 编辑商品时检查是否正在参与秒杀\n            const flashSaleCheck = await this.checkGoodsInFlashSale(id);\n            if (flashSaleCheck.inFlashSale) {\n                return this.fail(400, flashSaleCheck.message);\n            }\n\n            // 设置更新时间\n            values.update_time = parseInt(new Date().getTime() / 1000);\n            await model.where({\n                id: id\n            }).update(values);\n            await this.model('cart').where({\n                goods_id: id\n            }).update({\n                checked: values.is_on_sale,\n                is_on_sale: values.is_on_sale,\n                list_pic_url: picUrl,\n                freight_template_id: values.freight_template_id\n            });\n            await this.model('product').where({\n                goods_id: id\n            }).update({\n                is_delete: 1\n            });\n            await this.model('goods_specification').where({\n                goods_id: id\n            }).update({\n                is_delete: 1\n            });\n            for (const item of specData) {\n                if (item.id > 0) {\n                    await this.model('cart').where({\n                        product_id: item.id,\n                        is_delete: 0,\n                    }).update({\n                        retail_price: item.retail_price,\n                        goods_specifition_name_value: item.value,\n                        goods_sn: item.goods_sn\n                    });\n                    delete item.is_delete;\n                    item.is_delete = 0;\n                    await this.model('product').where({\n                        id: item.id\n                    }).update(item);\n                    let specificationData = {\n                        value: item.value,\n                        specification_id: specValue,\n                        is_delete: 0\n                    };\n                    await this.model('goods_specification').where({\n                        id: item.goods_specification_ids\n                    }).update(specificationData);\n                } else {\n                    let specificationData = {\n                        value: item.value,\n                        goods_id: id,\n                        specification_id: specValue\n                    }\n                    let specId = await this.model('goods_specification').add(specificationData);\n                    item.goods_specification_ids = specId;\n                    item.goods_id = id;\n                    await this.model('product').add(item);\n                }\n            }\n\t\t\tfor(const [index, item] of values.gallery.entries()){\n\t\t\t\tif(item.is_delete == 1 && item.id > 0){\n\t\t\t\t\tawait this.model('goods_gallery').where({\n\t\t\t\t\t\tid:item.id\n\t\t\t\t\t}).update({\n\t\t\t\t\t\tis_delete:1\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\telse if(item.is_delete == 0 && item.id > 0){\n\t\t\t\t\tawait this.model('goods_gallery').where({\n\t\t\t\t\t\tid:item.id\n\t\t\t\t\t}).update({\n\t\t\t\t\t\tsort_order:index\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\telse if(item.is_delete == 0 && item.id == 0){\n\t\t\t\t\tawait this.model('goods_gallery').add({\n\t\t\t\t\t\tgoods_id:id,\n\t\t\t\t\t\timg_url:item.url,\n\t\t\t\t\t\tsort_order:index\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n        } else {\n            delete values.id;\n            // 设置创建时间\n            values.add_time = parseInt(new Date().getTime() / 1000);\n            goods_id = await model.add(values);\n            for (const item of specData) {\n                let specificationData = {\n                    value: item.value,\n                    goods_id: goods_id,\n                    specification_id: specValue\n                }\n                let specId = await this.model('goods_specification').add(specificationData);\n                item.goods_specification_ids = specId;\n                item.goods_id = goods_id;\n                item.is_on_sale = 1;\n                await this.model('product').add(item);\n            }\n\t\t\tfor(const [index, item] of values.gallery.entries()){\n\t\t\t\tawait this.model('goods_gallery').add({\n\t\t\t\t\tgoods_id:goods_id,\n\t\t\t\t\timg_url:item.url,\n\t\t\t\t\tsort_order:index\n\t\t\t\t})\n\t\t\t}\n        }\n        let pro = await this.model('product').where({\n            goods_id: goods_id,\n            is_on_sale: 1,\n            is_delete: 0\n        }).select();\n        if (pro.length > 1) {\n            let goodsNum = await this.model('product').where({\n                goods_id: goods_id,\n                is_on_sale: 1,\n                is_delete: 0\n            }).sum('goods_number');\n            let retail_price = await this.model('product').where({\n                goods_id: goods_id,\n                is_on_sale: 1,\n                is_delete: 0\n            }).getField('retail_price');\n            let maxPrice = Math.max(...retail_price);\n            let minPrice = Math.min(...retail_price);\n            let cost = await this.model('product').where({\n                goods_id: goods_id,\n                is_on_sale: 1,\n                is_delete: 0\n            }).getField('cost');\n            let maxCost = Math.max(...cost);\n            let minCost = Math.min(...cost);\n            let goodsPrice = '';\n            if(minPrice == maxPrice){\n                goodsPrice = minPrice;\n            }\n            else{\n                goodsPrice = minPrice + '~' + maxPrice;\n            }\n            let costPrice = minCost + '~' + maxCost;\n            await this.model('goods').where({\n                id: goods_id\n            }).update({\n                goods_number: goodsNum,\n                retail_price: goodsPrice,\n                cost_price: costPrice,\n                min_retail_price: minPrice,\n                min_cost_price: minCost,\n            });\n        } else {\n            let info = {\n                goods_number: pro[0].goods_number,\n                retail_price: pro[0].retail_price,\n                cost_price: pro[0].cost,\n                min_retail_price: pro[0].retail_price,\n                min_cost_price: pro[0].cost,\n            }\n            await this.model('goods').where({\n                id: goods_id\n            }).update(info);\n        }\n        return this.success(goods_id);\n    }\n    async updatePriceAction() {\n        let data = this.post('');\n\t\tlet goods_id = data.goods_id;\n\n\t\t// 检查商品是否正在参与秒杀\n        const flashSaleCheck = await this.checkGoodsInFlashSale(goods_id);\n        if (flashSaleCheck.inFlashSale) {\n            return this.fail(400, flashSaleCheck.message);\n        }\n        await this.model('goods_specification').where({\n            id: data.goods_specification_ids\n        }).update({\n            value: data.value\n        });\n        await this.model('product').where({\n            id: data.id\n        }).update(data);\n\t\tlet pro = await this.model('product').where({\n\t\t    goods_id: goods_id,\n\t\t    is_on_sale: 1,\n\t\t    is_delete: 0\n\t\t}).select();\n\t\tif(pro.length == 0){\n\t\t\treturn this.fail(100,'商品的规格数量至少1个')\n\t\t}\n        await this.model('cart').where({\n            product_id: data.id,\n            is_delete: 0,\n        }).update({\n            retail_price: data.retail_price,\n            goods_specifition_name_value: data.value,\n            goods_sn: data.goods_sn\n        });\n        delete data.value;\n     \n        if (pro.length > 1) {\n            let goodsNum = await this.model('product').where({\n                goods_id: goods_id,\n                is_on_sale: 1,\n                is_delete: 0\n            }).sum('goods_number');\n            let retail_price = await this.model('product').where({\n                goods_id: goods_id,\n                is_on_sale: 1,\n                is_delete: 0\n            }).getField('retail_price');\n            let maxPrice = Math.max(...retail_price);\n            let minPrice = Math.min(...retail_price);\n            let cost = await this.model('product').where({\n                goods_id: goods_id,\n                is_on_sale: 1,\n                is_delete: 0\n            }).getField('cost');\n            let maxCost = Math.max(...cost);\n            let minCost = Math.min(...cost);\n            let goodsPrice = '';\n            if(minPrice == maxPrice){\n                goodsPrice = minPrice;\n            }\n            else{\n                goodsPrice = minPrice + '~' + maxPrice;\n            }\n            let costPrice = minCost + '~' + maxCost;\n            await this.model('goods').where({\n                id: goods_id\n            }).update({\n                goods_number: goodsNum,\n                retail_price: goodsPrice,\n                cost_price: costPrice,\n                min_retail_price: minPrice,\n                min_cost_price: minCost,\n            });\n        } else if(pro.length == 1){\n            let info = {\n                goods_number: pro[0].goods_number,\n                retail_price: pro[0].retail_price,\n                cost_price: pro[0].cost,\n                min_retail_price: pro[0].retail_price,\n                min_cost_price: pro[0].cost,\n            }\n            await this.model('goods').where({\n                id: goods_id\n            }).update(info);\n        }\n        return this.success();\n    }\n    async checkSkuAction() {\n        const info = this.post('info');\n        if (info.id > 0) {\n            const model = this.model('product');\n            const data = await model.where({\n                id: ['<>', info.id],\n                goods_sn: info.goods_sn,\n                is_delete: 0\n            }).find();\n            if (!think.isEmpty(data)) {\n                return this.fail(100, '重复')\n            } else {\n                return this.success();\n            }\n        } else {\n            const model = this.model('product');\n            const data = await model.where({\n                goods_sn: info.goods_sn,\n                is_delete: 0\n            }).find();\n            if (!think.isEmpty(data)) {\n                return this.fail(100, '重复')\n            } else {\n                return this.success();\n            }\n        }\n    }\n    async updateSortAction() {\n        const id = this.post('id');\n        const sort = this.post('sort');\n        const model = this.model('goods');\n        const data = await model.where({\n            id: id\n        }).update({\n            sort_order: sort\n        });\n        return this.success(data);\n    }\n    async updateShortNameAction() {\n        const id = this.post('id');\n        const short_name = this.post('short_name');\n        const model = this.model('goods');\n        const data = await model.where({\n            id: id\n        }).update({\n            short_name: short_name\n        });\n        return this.success(data);\n    }\n    async galleryListAction() {\n        const id = this.get('id');\n        const model = this.model('goods_gallery');\n        const data = await model.where({\n            goods_id: id,\n            is_delete:0\n        }).select();\n        // console.log(data);\n        return this.success(data);\n    }\n    async galleryAction() {\n        const url = this.post('url');\n        const id = this.post('goods_id');\n        let info = {\n            goods_id: id,\n            img_url: url\n        }\n        await this.model('goods_gallery').add(info);\n        return this.success();\n    }\n    async getGalleryListAction() {\n        const goodsId = this.post('goodsId');\n        const data = await this.model('goods_gallery').where({\n            goods_id: goodsId,\n            is_delete:0\n        }).order('sort_order asc').select();\n        let galleryData = [];\n        for (const item of data) {\n            let pdata = {\n                id: item.id,\n                url: item.img_url,\n\t\t\t\tis_delete:0,\n            }\n            galleryData.push(pdata);\n        }\n        let info = {\n            galleryData: galleryData,\n        }\n        return this.success(info);\n    }\n    async deleteGalleryFileAction() {\n        const url = this.post('url');\n        const id = this.post('id');\n        await this.model('goods_gallery').where({\n            id: id\n        }).limit(1).update({\n            is_delete: 1\n        });\n        return this.success('文件删除成功');\n    }\n    async galleryEditAction() {\n        if (!this.isPost) {\n            return false;\n        }\n        const values = this.post();\n        let data = values.data;\n        // console.log(data);\n        const model = this.model('goods_gallery');\n        for (const item of data) {\n            let id = item.id;\n            let sort = parseInt(item.sort_order);\n            // console.log(sort);\n            await this.model('goods_gallery').where({\n                id: id\n            }).update({\n                sort_order: sort\n            });\n        }\n        return this.success();\n    }\n    async deleteListPicUrlAction() {\n        const id = this.post('id');\n        console.log(id);\n        await this.model('goods').where({\n            id: id\n        }).limit(1).update({\n            list_pic_url: 0\n        });\n        return this.success();\n    }\n    async destoryAction() {\n        const id = this.post('id');\n\n        // 检查商品是否正在参与秒杀\n        const flashSaleCheck = await this.checkGoodsInFlashSale(id);\n        if (flashSaleCheck.inFlashSale) {\n            return this.fail(400, flashSaleCheck.message);\n        }\n\n        await this.model('goods').where({\n            id: id\n        }).limit(1).update({\n            is_delete: 1\n        });\n        await this.model('product').where({\n            goods_id: id\n        }).update({\n            is_delete: 1\n        });\n        await this.model('goods_specification').where({\n            goods_id: id\n        }).update({\n            is_delete: 1\n        });\n        return this.success();\n    }\n    async uploadHttpsImageAction() {\n        let url = this.post('url');\n        let accessKey = think.config('qiniuHttps.access_key');\n        let secretKey = think.config('qiniuHttps.secret_key');\n        let domain = think.config('qiniuHttps.domain');\n        var mac = new qiniu.auth.digest.Mac(accessKey, secretKey);\n        var config = new qiniu.conf.Config();\n        let zoneNum = think.config('qiniuHttps.zoneNum');\n        if(zoneNum == 0){\n            config.zone = qiniu.zone.Zone_z0;\n        }\n        else if(zoneNum == 1){\n            config.zone = qiniu.zone.Zone_z1;\n        }\n        else if(zoneNum == 2){\n            config.zone = qiniu.zone.Zone_z2;\n        }\n        else if(zoneNum == 3){\n            config.zone = qiniu.zone.Zone_na0;\n        }\n        else if(zoneNum == 4){\n            config.zone = qiniu.zone.Zone_as0;\n        }\n        var bucketManager = new qiniu.rs.BucketManager(mac, config);\n        let bucket = think.config('qiniuHttps.bucket');\n        let key = think.uuid(32);\n        await think.timeout(500);\n        const uploadQiniu = async() => {\n            return new Promise((resolve, reject) => {\n                try {\n                    bucketManager.fetch(url, bucket, key, function(err, respBody, respInfo) {\n                        if (err) {\n                            console.log(err);\n                            //throw err;\n                        } else {\n                            if (respInfo.statusCode == 200) {\n                                resolve(respBody.key)\n                            } else {\n                                console.log(respInfo.statusCode);\n                            }\n                        }\n                    });\n                } catch (e) {\n                    return resolve(null);\n                }\n            })\n        };\n        const httpsUrl = await uploadQiniu();\n        console.log(httpsUrl);\n        let lastUrl = domain + httpsUrl;\n        return this.success(lastUrl);\n    }\n};"]}