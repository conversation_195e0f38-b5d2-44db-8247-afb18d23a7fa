{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\order-exchange.js"], "names": ["Base", "require", "module", "exports", "configAction", "console", "log", "config", "enabled", "dailyLimit", "minOrderAmount", "exchangeRate", "maxPoints", "supportedPlatforms", "success", "error", "fail", "exchangeAction", "orderNumber", "post", "openid", "user", "model", "where", "weixin_openid", "find", "think", "isEmpty", "userId", "id", "nickname", "existingExchange", "order_number", "user_id", "today", "Date", "toISOString", "split", "todayExchangeCount", "created_at", "count", "orderInfo", "validateOrder", "valid", "message", "earnedPoints", "Math", "floor", "amount", "currentTime", "exchangeLogId", "add", "order_platform", "platform", "order_amount", "exchange_points", "exchange_rate", "status", "updated_at", "orderAmount", "recordsAction", "get", "records", "order", "limit", "select", "formattedRecords", "map", "record", "getPlatformName", "date", "points", "startsWith", "random", "includes", "platformNames"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;;AAEAC,OAAOC,OAAP,GAAiB,cAAcH,IAAd,CAAmB;;AAElC;;;;AAIMI,cAAN,GAAqB;AAAA;;AAAA;AACnBC,cAAQC,GAAR,CAAY,kBAAZ;;AAEA,UAAI;AACF;AACA,cAAMC,SAAS;AACbC,mBAAS,IADI;AAEbC,sBAAY,CAFC;AAGbC,0BAAgB,EAHH;AAIbC,wBAAc,GAJD,EAIM;AACnBC,qBAAW,IALE;AAMbC,8BAAoB,CAAC,QAAD,EAAW,OAAX,EAAoB,IAApB,EAA0B,KAA1B;AANP,SAAf;;AASA,eAAO,MAAKC,OAAL,CAAaP,MAAb,CAAP;AACD,OAZD,CAYE,OAAOQ,KAAP,EAAc;AACdV,gBAAQU,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,MAAKC,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACD;AAlBkB;AAmBpB;;AAED;;;;AAIMC,gBAAN,GAAuB;AAAA;;AAAA;AACrBZ,cAAQC,GAAR,CAAY,kBAAZ;;AAEA,YAAMY,cAAc,OAAKC,IAAL,CAAU,aAAV,CAApB;AACA,YAAMC,SAAS,OAAKD,IAAL,CAAU,QAAV,CAAf;;AAEAd,cAAQC,GAAR,CAAY,MAAZ,EAAoBY,WAApB;AACAb,cAAQC,GAAR,CAAY,WAAZ,EAAyBc,MAAzB;;AAEA,UAAI,CAACF,WAAD,IAAgB,CAACE,MAArB,EAA6B;AAC3B,eAAO,OAAKJ,IAAL,CAAU,GAAV,EAAe,cAAf,CAAP;AACD;;AAED,UAAI;AACF;AACA,cAAMK,OAAO,MAAM,OAAKC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC1CC,yBAAeJ;AAD2B,SAAzB,EAEhBK,IAFgB,EAAnB;;AAIA,YAAIC,MAAMC,OAAN,CAAcN,IAAd,CAAJ,EAAyB;AACvB,iBAAO,OAAKL,IAAL,CAAU,GAAV,EAAe,cAAf,CAAP;AACD;;AAED,cAAMY,SAASP,KAAKQ,EAApB;AACAxB,gBAAQC,GAAR,CAAY,OAAZ,EAAqBsB,MAArB,EAA6B,KAA7B,EAAoCP,KAAKS,QAAzC;;AAEA;AACA,cAAMC,mBAAmB,MAAM,OAAKT,KAAL,CAAW,oBAAX,EAAiCC,KAAjC,CAAuC;AACpES,wBAAcd,WADsD;AAEpEe,mBAASL;AAF2D,SAAvC,EAG5BH,IAH4B,EAA/B;;AAKA,YAAI,CAACC,MAAMC,OAAN,CAAcI,gBAAd,CAAL,EAAsC;AACpC,iBAAO,OAAKf,IAAL,CAAU,GAAV,EAAe,YAAf,CAAP;AACD;;AAED;AACA,cAAMkB,QAAQ,IAAIC,IAAJ,GAAWC,WAAX,GAAyBC,KAAzB,CAA+B,GAA/B,EAAoC,CAApC,CAAd;AACA,cAAMC,qBAAqB,MAAM,OAAKhB,KAAL,CAAW,oBAAX,EAAiCC,KAAjC,CAAuC;AACtEU,mBAASL,MAD6D;AAEtEW,sBAAY,CAAC,MAAD,EAAU,GAAEL,KAAM,GAAlB;AAF0D,SAAvC,EAG9BM,KAH8B,EAAjC;;AAKA,YAAIF,sBAAsB,CAA1B,EAA6B;AAC3B,iBAAO,OAAKtB,IAAL,CAAU,GAAV,EAAe,YAAf,CAAP;AACD;;AAED;AACA,cAAMyB,YAAY,MAAM,OAAKC,aAAL,CAAmBxB,WAAnB,CAAxB;AACA,YAAI,CAACuB,UAAUE,KAAf,EAAsB;AACpB,iBAAO,OAAK3B,IAAL,CAAU,GAAV,EAAeyB,UAAUG,OAAV,IAAqB,QAApC,CAAP;AACD;;AAED;AACA,cAAMjC,eAAe,GAArB,CAzCE,CAyCwB;AAC1B,cAAMkC,eAAeC,KAAKC,KAAL,CAAWN,UAAUO,MAAV,GAAmBrC,YAA9B,CAArB;;AAEA,YAAIkC,gBAAgB,CAApB,EAAuB;AACrB,iBAAO,OAAK7B,IAAL,CAAU,GAAV,EAAe,eAAf,CAAP;AACD;;AAED;AACA,cAAMiC,cAAc,IAAId,IAAJ,EAApB;;AAEA;AACA,cAAMe,gBAAgB,MAAM,OAAK5B,KAAL,CAAW,oBAAX,EAAiC6B,GAAjC,CAAqC;AAC/DlB,mBAASL,MADsD;AAE/DI,wBAAcd,WAFiD;AAG/DkC,0BAAgBX,UAAUY,QAHqC;AAI/DC,wBAAcb,UAAUO,MAJuC;AAK/DO,2BAAiBV,YAL8C;AAM/DW,yBAAe7C,YANgD;AAO/D8C,kBAAQ,SAPuD;AAQ/DlB,sBAAYU,WARmD;AAS/DS,sBAAYT;AATmD,SAArC,CAA5B;;AAYA;AACA5C,gBAAQC,GAAR,CAAY,wBAAZ,EAAsCuC,YAAtC;;AAEA,eAAO,OAAK/B,OAAL,CAAa;AAClBA,mBAAS,IADS;AAElB+B,wBAAcA,YAFI;AAGlBc,uBAAalB,UAAUO,MAHL;AAIlBrC,wBAAcA,YAJI;AAKlBiC,mBAAU,UAASC,YAAa;AALd,SAAb,CAAP;AAQD,OA3ED,CA2EE,OAAO9B,KAAP,EAAc;AACdV,gBAAQU,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,eAAO,OAAKC,IAAL,CAAU,GAAV,EAAe,UAAf,CAAP;AACD;AA3FoB;AA4FtB;;AAED;;;;AAIM4C,eAAN,GAAsB;AAAA;;AAAA;AACpBvD,cAAQC,GAAR,CAAY,kBAAZ;;AAEA,YAAMc,SAAS,OAAKyC,GAAL,CAAS,QAAT,CAAf;;AAEA,UAAI,CAACzC,MAAL,EAAa;AACX,eAAO,OAAKJ,IAAL,CAAU,GAAV,EAAe,UAAf,CAAP;AACD;;AAED,UAAI;AACF;AACA,cAAMK,OAAO,MAAM,OAAKC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC1CC,yBAAeJ;AAD2B,SAAzB,EAEhBK,IAFgB,EAAnB;;AAIA,YAAIC,MAAMC,OAAN,CAAcN,IAAd,CAAJ,EAAyB;AACvB,iBAAO,OAAKL,IAAL,CAAU,GAAV,EAAe,OAAf,CAAP;AACD;;AAED;AACA,cAAM8C,UAAU,MAAM,OAAKxC,KAAL,CAAW,oBAAX,EAAiCC,KAAjC,CAAuC;AAC3DU,mBAASZ,KAAKQ;AAD6C,SAAvC,EAEnBkC,KAFmB,CAEb,iBAFa,EAEMC,KAFN,CAEY,EAFZ,EAEgBC,MAFhB,EAAtB;;AAIA;AACA,cAAMC,mBAAmBJ,QAAQK,GAAR,CAAY;AAAA,iBAAW;AAC9CtC,gBAAIuC,OAAOvC,EADmC;AAE9CwB,sBAAU,OAAKgB,eAAL,CAAqBD,OAAOhB,cAA5B,CAFoC;AAG9ClC,yBAAakD,OAAOpC,YAH0B;AAI9CsC,kBAAMF,OAAO7B,UAAP,CAAkBF,KAAlB,CAAwB,GAAxB,EAA6B,CAA7B,CAJwC;AAK9CW,oBAAQoB,OAAOd,YAL+B;AAM9CiB,oBAAQH,OAAOb,eAN+B;AAO9CE,oBAAQW,OAAOX;AAP+B,WAAX;AAAA,SAAZ,CAAzB;;AAUA,eAAO,OAAK3C,OAAL,CAAaoD,gBAAb,CAAP;AAED,OA5BD,CA4BE,OAAOnD,KAAP,EAAc;AACdV,gBAAQU,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKC,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACD;AAxCmB;AAyCrB;;AAED;;;;;AAKM0B,eAAN,CAAoBxB,WAApB,EAAiC;AAAA;AAC/B;AACA;;AAEAb,cAAQC,GAAR,CAAY,OAAZ,EAAqBY,WAArB;;AAEA;AACA,UAAImC,WAAW,SAAf;AACA,UAAIL,SAAS,CAAb;;AAEA,UAAI9B,YAAYsD,UAAZ,CAAuB,IAAvB,KAAgCtD,YAAYsD,UAAZ,CAAuB,IAAvB,CAApC,EAAkE;AAChEnB,mBAAW,QAAX;AACAL,iBAAS,KAAT;AACD,OAHD,MAGO,IAAI9B,YAAYsD,UAAZ,CAAuB,IAAvB,KAAgCtD,YAAYsD,UAAZ,CAAuB,IAAvB,CAApC,EAAkE;AACvEnB,mBAAW,OAAX;AACAL,iBAAS,MAAT;AACD,OAHM,MAGA,IAAI9B,YAAYsD,UAAZ,CAAuB,IAAvB,KAAgCtD,YAAYsD,UAAZ,CAAuB,IAAvB,CAApC,EAAkE;AACvEnB,mBAAW,IAAX;AACAL,iBAAS,MAAT;AACD,OAHM,MAGA,IAAI9B,YAAYsD,UAAZ,CAAuB,KAAvB,KAAiCtD,YAAYsD,UAAZ,CAAuB,KAAvB,CAArC,EAAoE;AACzEnB,mBAAW,KAAX;AACAL,iBAAS,KAAT;AACD,OAHM,MAGA;AACL;AACAK,mBAAW,OAAX;AACAL,iBAASF,KAAKC,KAAL,CAAWD,KAAK2B,MAAL,KAAgB,GAA3B,IAAkC,EAA3C;AACD;;AAED;AACA,UAAIvD,YAAYwD,QAAZ,CAAqB,SAArB,KAAmCxD,YAAYwD,QAAZ,CAAqB,OAArB,CAAvC,EAAsE;AACpE,eAAO;AACL/B,iBAAO,KADF;AAELC,mBAAS;AAFJ,SAAP;AAID;;AAED,UAAII,SAAS,EAAb,EAAiB;AACf,eAAO;AACLL,iBAAO,KADF;AAELC,mBAAS;AAFJ,SAAP;AAID;;AAED,aAAO;AACLD,eAAO,IADF;AAELU,kBAAUA,QAFL;AAGLL,gBAAQA;AAHH,OAAP;AA3C+B;AAgDhC;;AAED;;;;;AAKAqB,kBAAgBhB,QAAhB,EAA0B;AACxB,UAAMsB,gBAAgB;AACpB,gBAAU,IADU;AAEpB,eAAS,IAFW;AAGpB,YAAM,IAHc;AAIpB,aAAO,KAJa;AAKpB,gBAAU,IALU;AAMpB,eAAS;AANW,KAAtB;;AASA,WAAOA,cAActB,QAAd,KAA2B,IAAlC;AACD;AAnPiC,CAApC", "file": "..\\..\\..\\src\\api\\controller\\order-exchange.js", "sourcesContent": ["const Base = require('./base.js');\n\nmodule.exports = class extends Base {\n  \n  /**\n   * 获取兑换配置\n   * GET /api/order-exchange/config\n   */\n  async configAction() {\n    console.log('=== 获取兑换配置请求 ===');\n    \n    try {\n      // 这里可以从数据库获取配置，暂时返回默认配置\n      const config = {\n        enabled: true,\n        dailyLimit: 3,\n        minOrderAmount: 50,\n        exchangeRate: 0.5, // 1元=0.5积分\n        maxPoints: 1000,\n        supportedPlatforms: ['taobao', 'tmall', 'jd', 'pdd']\n      };\n      \n      return this.success(config);\n    } catch (error) {\n      console.error('获取兑换配置失败:', error);\n      return this.fail(500, '获取配置失败');\n    }\n  }\n\n  /**\n   * 执行订单兑换\n   * POST /api/order-exchange/exchange\n   */\n  async exchangeAction() {\n    console.log('=== 订单兑换请求开始 ===');\n    \n    const orderNumber = this.post('orderNumber');\n    const openid = this.post('openid');\n    \n    console.log('订单号:', orderNumber);\n    console.log('用户openid:', openid);\n    \n    if (!orderNumber || !openid) {\n      return this.fail(400, '订单号和用户信息不能为空');\n    }\n    \n    try {\n      // 1. 验证用户是否存在\n      const user = await this.model('user').where({\n        weixin_openid: openid\n      }).find();\n      \n      if (think.isEmpty(user)) {\n        return this.fail(404, '用户不存在，请先完成登录');\n      }\n      \n      const userId = user.id;\n      console.log('用户ID:', userId, '昵称:', user.nickname);\n      \n      // 2. 检查订单号是否已经兑换过\n      const existingExchange = await this.model('order_exchange_log').where({\n        order_number: orderNumber,\n        user_id: userId\n      }).find();\n      \n      if (!think.isEmpty(existingExchange)) {\n        return this.fail(400, '该订单号已经兑换过了');\n      }\n      \n      // 3. 检查今日兑换次数限制\n      const today = new Date().toISOString().split('T')[0];\n      const todayExchangeCount = await this.model('order_exchange_log').where({\n        user_id: userId,\n        created_at: ['LIKE', `${today}%`]\n      }).count();\n      \n      if (todayExchangeCount >= 3) {\n        return this.fail(400, '今日兑换次数已达上限');\n      }\n      \n      // 4. 模拟订单验证（实际应该调用第三方API验证）\n      const orderInfo = await this.validateOrder(orderNumber);\n      if (!orderInfo.valid) {\n        return this.fail(400, orderInfo.message || '订单验证失败');\n      }\n      \n      // 5. 计算积分\n      const exchangeRate = 0.5; // 1元=0.5积分\n      const earnedPoints = Math.floor(orderInfo.amount * exchangeRate);\n      \n      if (earnedPoints <= 0) {\n        return this.fail(400, '订单金额不足，无法兑换积分');\n      }\n      \n      // 6. 开始事务处理\n      const currentTime = new Date();\n      \n      // 记录兑换日志\n      const exchangeLogId = await this.model('order_exchange_log').add({\n        user_id: userId,\n        order_number: orderNumber,\n        order_platform: orderInfo.platform,\n        order_amount: orderInfo.amount,\n        exchange_points: earnedPoints,\n        exchange_rate: exchangeRate,\n        status: 'success',\n        created_at: currentTime,\n        updated_at: currentTime\n      });\n      \n      // 积分系统已移除，但保持兑换记录\n      console.log('兑换成功（积分系统已移除），原本会获得积分:', earnedPoints);\n      \n      return this.success({\n        success: true,\n        earnedPoints: earnedPoints,\n        orderAmount: orderInfo.amount,\n        exchangeRate: exchangeRate,\n        message: `兑换成功！获得${earnedPoints}积分`\n      });\n      \n    } catch (error) {\n      console.error('订单兑换失败:', error);\n      return this.fail(500, '兑换失败，请重试');\n    }\n  }\n\n  /**\n   * 获取兑换记录\n   * GET /api/order-exchange/records\n   */\n  async recordsAction() {\n    console.log('=== 获取兑换记录请求 ===');\n    \n    const openid = this.get('openid');\n    \n    if (!openid) {\n      return this.fail(400, '用户信息不能为空');\n    }\n    \n    try {\n      // 获取用户信息\n      const user = await this.model('user').where({\n        weixin_openid: openid\n      }).find();\n      \n      if (think.isEmpty(user)) {\n        return this.fail(404, '用户不存在');\n      }\n      \n      // 获取兑换记录\n      const records = await this.model('order_exchange_log').where({\n        user_id: user.id\n      }).order('created_at DESC').limit(20).select();\n      \n      // 格式化记录\n      const formattedRecords = records.map(record => ({\n        id: record.id,\n        platform: this.getPlatformName(record.order_platform),\n        orderNumber: record.order_number,\n        date: record.created_at.split(' ')[0],\n        amount: record.order_amount,\n        points: record.exchange_points,\n        status: record.status\n      }));\n      \n      return this.success(formattedRecords);\n      \n    } catch (error) {\n      console.error('获取兑换记录失败:', error);\n      return this.fail(500, '获取记录失败');\n    }\n  }\n\n  /**\n   * 验证订单（模拟）\n   * @param {string} orderNumber 订单号\n   * @returns {object} 验证结果\n   */\n  async validateOrder(orderNumber) {\n    // 这里应该调用第三方API验证订单\n    // 暂时使用模拟逻辑\n    \n    console.log('验证订单:', orderNumber);\n    \n    // 模拟不同平台的订单号格式\n    let platform = 'unknown';\n    let amount = 0;\n    \n    if (orderNumber.startsWith('TB') || orderNumber.startsWith('tb')) {\n      platform = 'taobao';\n      amount = 99.00;\n    } else if (orderNumber.startsWith('TM') || orderNumber.startsWith('tm')) {\n      platform = 'tmall';\n      amount = 158.00;\n    } else if (orderNumber.startsWith('JD') || orderNumber.startsWith('jd')) {\n      platform = 'jd';\n      amount = 299.00;\n    } else if (orderNumber.startsWith('PDD') || orderNumber.startsWith('pdd')) {\n      platform = 'pdd';\n      amount = 45.00;\n    } else {\n      // 通用订单号，随机金额\n      platform = 'other';\n      amount = Math.floor(Math.random() * 200) + 50;\n    }\n    \n    // 模拟验证失败的情况\n    if (orderNumber.includes('invalid') || orderNumber.includes('error')) {\n      return {\n        valid: false,\n        message: '订单不存在或已失效'\n      };\n    }\n    \n    if (amount < 50) {\n      return {\n        valid: false,\n        message: '订单金额低于最低兑换标准（50元）'\n      };\n    }\n    \n    return {\n      valid: true,\n      platform: platform,\n      amount: amount\n    };\n  }\n\n  /**\n   * 获取平台名称\n   * @param {string} platform 平台代码\n   * @returns {string} 平台名称\n   */\n  getPlatformName(platform) {\n    const platformNames = {\n      'taobao': '淘宝',\n      'tmall': '天猫',\n      'jd': '京东',\n      'pdd': '拼多多',\n      'douyin': '抖音',\n      'other': '其他'\n    };\n    \n    return platformNames[platform] || '未知';\n  }\n};\n"]}