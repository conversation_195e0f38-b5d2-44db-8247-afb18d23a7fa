<template>
  <div class="recruitment-rules-page">
    <div class="container mx-auto p-6">
      <!-- 页面标题 -->
      <div class="bg-white rounded-lg shadow-sm border mb-6">
        <div class="px-6 py-4 border-b">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-2xl font-semibold text-gray-900">分销推广</h1>
              <p class="text-sm text-gray-500 mt-1">设置分销商招募规则和条件</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤指示器 -->
      <div class="bg-blue-600 text-white px-6 py-3 rounded-t-lg mb-0">
        <div class="flex items-center">
          <span class="bg-white text-blue-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mr-3">1</span>
          <span class="font-medium">设置招募规则</span>
        </div>
      </div>

      <!-- 招募规则设置 -->
      <div class="bg-white rounded-b-lg shadow-sm border border-t-0 p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-6">招募规则设置</h2>

        <!-- 分销员加入条件 -->
        <div class="mb-8">
          <h3 class="text-base font-medium text-gray-900 mb-4">分销员加入条件：</h3>
          <div class="space-y-4">
            <!-- 条件选择 -->
            <div class="flex items-center space-x-6">
              <label class="flex items-center">
                <input
                  type="radio"
                  v-model="recruitmentRules.joinCondition"
                  value="with_conditions"
                  class="mr-2 text-blue-600"
                />
                <span class="text-sm">有条件</span>
                <span class="ml-2 px-2 py-1 bg-orange-100 text-orange-600 text-xs rounded">仅适用</span>
              </label>
              <label class="flex items-center">
                <input
                  type="radio"
                  v-model="recruitmentRules.joinCondition"
                  value="no_conditions"
                  class="mr-2 text-blue-600"
                />
                <span class="text-sm">无条件</span>
              </label>
            </div>

            <!-- 条件设置区域 -->
            <div v-if="recruitmentRules.joinCondition === 'with_conditions'" class="bg-gray-50 p-4 rounded-lg space-y-4">
              <!-- 需购买指定商品 -->
              <div class="space-y-3">
                <label class="flex items-center">
                  <input
                    type="checkbox"
                    v-model="recruitmentRules.conditions.requirePurchase"
                    class="mr-3 text-blue-600"
                  />
                  <span class="text-sm font-medium">需购买指定商品</span>
                </label>
                <div v-if="recruitmentRules.conditions.requirePurchase" class="ml-6 text-sm text-gray-600">
                  <p>注：请商家以下商品已上架，若商品尚未上架，一旦在商品的用户端经满足条件，无需再次购买。</p>
                </div>
              </div>

              <!-- 自购金额满 -->
              <div class="space-y-3">
                <label class="flex items-center">
                  <input
                    type="checkbox"
                    v-model="recruitmentRules.conditions.requireAmount"
                    class="mr-3 text-blue-600"
                  />
                  <span class="text-sm font-medium">自购金额满</span>
                  <input
                    v-if="recruitmentRules.conditions.requireAmount"
                    type="number"
                    v-model="recruitmentRules.conditions.minAmount"
                    class="ml-2 w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                    placeholder="99.00"
                  />
                  <span v-if="recruitmentRules.conditions.requireAmount" class="ml-1 text-sm">元</span>
                </label>
                <div v-if="recruitmentRules.conditions.requireAmount" class="ml-6 text-sm text-gray-600">
                  <p>客户支付后即可入自购金额，自购金额最低设置为0.01元。</p>
                </div>
              </div>

              <!-- 消费笔数满 -->
              <div class="space-y-3">
                <label class="flex items-center">
                  <input
                    type="checkbox"
                    v-model="recruitmentRules.conditions.requireOrders"
                    class="mr-3 text-blue-600"
                  />
                  <span class="text-sm font-medium">消费笔数满</span>
                  <input
                    v-if="recruitmentRules.conditions.requireOrders"
                    type="number"
                    v-model="recruitmentRules.conditions.minOrders"
                    class="ml-2 w-16 px-2 py-1 border border-gray-300 rounded text-sm"
                    placeholder="1"
                  />
                  <span v-if="recruitmentRules.conditions.requireOrders" class="ml-1 text-sm">笔</span>
                </label>
                <div v-if="recruitmentRules.conditions.requireOrders" class="ml-6 text-sm text-gray-600">
                  <p>客户支付后即可入消费笔数，消费笔数最低设置为1笔。</p>
                </div>
              </div>

              <!-- 提示信息 -->
              <div class="mt-4 p-3 bg-blue-50 rounded border-l-4 border-blue-400">
                <p class="text-sm text-blue-700">
                  以上条件需同时满足，若已满足或成功条件不充足，分销员不会被自动通过。
                  <a href="#" class="text-blue-600 underline">修改设置</a>
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 分销员申请方式 -->
        <div class="mb-8">
          <h3 class="text-base font-medium text-gray-900 mb-4">分销员申请方式：</h3>
          <div class="space-y-4">
            <div class="flex items-center space-x-6">
              <label class="flex items-center">
                <input
                  type="radio"
                  v-model="recruitmentRules.applicationMethod"
                  value="manual_apply"
                  class="mr-2 text-blue-600"
                />
                <span class="text-sm">申请人手动申请</span>
              </label>
              <label class="flex items-center">
                <input
                  type="radio"
                  v-model="recruitmentRules.applicationMethod"
                  value="auto_apply"
                  class="mr-2 text-blue-600"
                />
                <span class="text-sm">满足条件自动申请</span>
              </label>
            </div>

            <!-- 需要填写申请信息 -->
            <div v-if="recruitmentRules.applicationMethod === 'manual_apply'" class="ml-6">
              <label class="flex items-center">
                <input
                  type="checkbox"
                  v-model="recruitmentRules.requireApplicationInfo"
                  class="mr-3 text-blue-600"
                />
                <span class="text-sm">需要填写申请信息</span>
              </label>
            </div>
          </div>
        </div>

        <!-- 商家审核方式 -->
        <div class="mb-8">
          <h3 class="text-base font-medium text-gray-900 mb-4">商家审核方式：</h3>
          <div class="space-y-4">
            <div class="flex items-center space-x-6">
              <label class="flex items-center">
                <input
                  type="radio"
                  v-model="recruitmentRules.auditMethod"
                  value="manual_audit"
                  class="mr-2 text-blue-600"
                />
                <span class="text-sm">人工审核</span>
              </label>
              <label class="flex items-center">
                <input
                  type="radio"
                  v-model="recruitmentRules.auditMethod"
                  value="auto_audit"
                  class="mr-2 text-blue-600"
                />
                <span class="text-sm">无需审核，满足条件自动通过</span>
              </label>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex items-center justify-between pt-6 border-t">
          <button @click="resetRules" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
            重置
          </button>
          <div class="space-x-3">
            <button @click="saveRules" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              保存设置
            </button>
            <button @click="previewRules" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              预览效果
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 预览弹窗 -->
    <div v-if="showPreviewModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">招募规则预览</h3>
          <button @click="showPreviewModal = false" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>

        <div class="space-y-4">
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-2">加入条件</h4>
            <p class="text-sm text-gray-600">
              {{ recruitmentRules.joinCondition === 'with_conditions' ? '有条件加入' : '无条件加入' }}
            </p>
            <div v-if="recruitmentRules.joinCondition === 'with_conditions'" class="mt-2 space-y-1">
              <p v-if="recruitmentRules.conditions.requirePurchase" class="text-sm text-gray-600">
                • 需购买指定商品
              </p>
              <p v-if="recruitmentRules.conditions.requireAmount" class="text-sm text-gray-600">
                • 自购金额满 {{ recruitmentRules.conditions.minAmount }} 元
              </p>
              <p v-if="recruitmentRules.conditions.requireOrders" class="text-sm text-gray-600">
                • 消费笔数满 {{ recruitmentRules.conditions.minOrders }} 笔
              </p>
            </div>
          </div>

          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-2">申请方式</h4>
            <p class="text-sm text-gray-600">
              {{ recruitmentRules.applicationMethod === 'manual_apply' ? '申请人手动申请' : '满足条件自动申请' }}
            </p>
            <p v-if="recruitmentRules.requireApplicationInfo" class="text-sm text-gray-600 mt-1">
              • 需要填写申请信息
            </p>
          </div>

          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-2">审核方式</h4>
            <p class="text-sm text-gray-600">
              {{ recruitmentRules.auditMethod === 'manual_audit' ? '人工审核' : '无需审核，满足条件自动通过' }}
            </p>
          </div>
        </div>

        <div class="flex justify-end mt-6">
          <button @click="showPreviewModal = false" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700">
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getRecruitmentRules,
  saveRecruitmentRules,
  updateRecruitmentRules
} from '@/api/distribution'

export default {
  name: 'RecruitmentRulesPage',
  data() {
    return {
      // 招募规则配置
      recruitmentRules: {
        joinCondition: 'with_conditions', // with_conditions: 有条件, no_conditions: 无条件
        conditions: {
          requirePurchase: false,
          requireAmount: true,
          minAmount: 99.00,
          requireOrders: false,
          minOrders: 1
        },
        applicationMethod: 'manual_apply', // manual_apply: 手动申请, auto_apply: 自动申请
        requireApplicationInfo: false,
        auditMethod: 'auto_audit' // manual_audit: 人工审核, auto_audit: 自动审核
      },
      
      // 弹窗状态
      showPreviewModal: false
    }
  },
  
  mounted() {
    console.log('招募规则页面已加载');
    this.loadRecruitmentRules();
  },
  
  methods: {
    // 加载招募规则
    async loadRecruitmentRules() {
      try {
        const response = await getRecruitmentRules();

        if (response.data && response.data.errno === 0) {
          const rules = response.data.data;
          if (rules) {
            this.recruitmentRules = {
              joinCondition: rules.joinCondition || 'with_conditions',
              conditions: rules.conditions || {
                requirePurchase: false,
                requireAmount: true,
                minAmount: 99.00,
                requireOrders: false,
                minOrders: 1
              },
              applicationMethod: rules.applicationMethod || 'manual_apply',
              requireApplicationInfo: rules.requireApplicationInfo || false,
              auditMethod: rules.auditMethod || 'auto_audit'
            };
          }
          console.log('成功加载招募规则配置');
        } else {
          console.error('加载招募规则失败:', response.data);
          this.$message.error('加载招募规则失败');
        }
      } catch (error) {
        console.error('加载招募规则失败:', error);
        this.$message.error('网络请求失败');
      }
    },
    
    // 保存规则
    async saveRules() {
      try {
        // 验证规则设置
        if (this.recruitmentRules.joinCondition === 'with_conditions') {
          const hasCondition = this.recruitmentRules.conditions.requirePurchase ||
                              this.recruitmentRules.conditions.requireAmount ||
                              this.recruitmentRules.conditions.requireOrders;
          
          if (!hasCondition) {
            this.$message.warning('请至少设置一个加入条件');
            return;
          }
          
          if (this.recruitmentRules.conditions.requireAmount && 
              (!this.recruitmentRules.conditions.minAmount || this.recruitmentRules.conditions.minAmount < 0.01)) {
            this.$message.warning('自购金额最低设置为0.01元');
            return;
          }
          
          if (this.recruitmentRules.conditions.requireOrders && 
              (!this.recruitmentRules.conditions.minOrders || this.recruitmentRules.conditions.minOrders < 1)) {
            this.$message.warning('消费笔数最低设置为1笔');
            return;
          }
        }
        
        // 调用API保存招募规则
        const response = await saveRecruitmentRules(this.recruitmentRules);

        if (response.data && response.data.errno === 0) {
          console.log('保存招募规则成功:', this.recruitmentRules);
          this.$message.success('招募规则保存成功');
        } else {
          console.error('保存招募规则失败:', response.data);
          this.$message.error('保存失败: ' + (response.data.errmsg || '未知错误'));
        }
      } catch (error) {
        console.error('保存招募规则失败:', error);
        this.$message.error('保存失败，请稍后重试');
      }
    },
    
    // 重置规则
    resetRules() {
      this.recruitmentRules = {
        joinCondition: 'with_conditions',
        conditions: {
          requirePurchase: false,
          requireAmount: true,
          minAmount: 99.00,
          requireOrders: false,
          minOrders: 1
        },
        applicationMethod: 'manual_apply',
        requireApplicationInfo: false,
        auditMethod: 'auto_audit'
      };
      this.$message.info('规则已重置为默认设置');
    },
    
    // 预览规则
    previewRules() {
      this.showPreviewModal = true;
    }
  }
}
</script>

<style scoped>
.recruitment-rules-page {
  min-height: 100vh;
  background-color: #f9fafb;
}

.container {
  max-width: 1200px;
}

/* 自定义复选框和单选框样式 */
input[type="checkbox"], input[type="radio"] {
  width: 16px;
  height: 16px;
}

/* 步骤指示器样式 */
.bg-blue-600 {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

/* 条件设置区域样式 */
.bg-gray-50 {
  background-color: #f8fafc;
}

/* 输入框样式 */
input[type="number"] {
  -moz-appearance: textfield;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>
