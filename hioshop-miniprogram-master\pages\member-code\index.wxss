/* 会员码页面样式 - 参考main.html设计 */
.container {
  background: #ffffff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.nav-bar {
  position: fixed;
  top: 0;
  width: 100%;
  height: 88rpx;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  z-index: 100;
}

.nav-left, .nav-right {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 32rpx;
  height: 32rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  padding: 120rpx 40rpx 160rpx 40rpx;
}

/* 个人信息卡片 */
.user-info-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-top: 32rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}

.user-avatar-wrap {
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  border: 4rpx solid #E8A355;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.user-details {
  flex: 1;
  margin-left: 32rpx;
}

.user-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.member-badge {
  background: linear-gradient(135deg, #E8A355, #D4833B);
  color: white;
  font-size: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
}

.member-validity {
  font-size: 24rpx;
  color: #999;
}

/* 会员码展示区域 */
.qr-code-section {
  margin-top: 64rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qr-code-container {
  background: linear-gradient(135deg, #FDF6ED, #FCF3E7);
  padding: 48rpx;
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qr-code-wrapper {
  background: white;
  padding: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.qr-canvas {
  width: 480rpx;
  height: 480rpx;
  border-radius: 8rpx;
}

.qr-loading {
  width: 480rpx;
  height: 480rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 8rpx;
  color: #999;
  font-size: 28rpx;
}

.qr-description {
  margin-top: 32rpx;
  text-align: center;
}

.qr-title {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.qr-subtitle {
  display: block;
  font-size: 24rpx;
  color: #999;
}

/* 会员权益区域 */
.benefits-section {
  background: #FDF6ED;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-top: 64rpx;
}

.benefits-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.crown-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}

.benefits-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.benefit-item {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.benefit-icon-wrap {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #E8A355;
  margin-bottom: 8rpx;
}

.benefit-icon {
  width: 48rpx;
  height: 48rpx;
}

.benefit-text {
  font-size: 20rpx;
  color: #333;
  margin-top: 8rpx;
}

/* 底部功能按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 24rpx 32rpx;
  display: flex;
  gap: 24rpx;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 0;
  white-space: nowrap;
}

.action-btn.primary {
  background: #E8A355;
  color: white;
}

.action-btn.primary:active {
  background: #D4833B;
  transform: scale(0.95);
}

.action-btn.secondary {
  background: transparent;
  color: #E8A355;
  border: 2rpx solid #E8A355;
}

.action-btn.secondary:active {
  background: #FDF6ED;
  transform: scale(0.95);
}

.btn-icon {
  width: 28rpx;
  height: 28rpx;
  flex-shrink: 0;
}

/* 分享弹窗 */
.share-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.share-modal.show {
  opacity: 1;
  visibility: visible;
}

.share-content {
  background: white;
  width: 100%;
  border-radius: 32rpx 32rpx 0 0;
  padding: 40rpx;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.share-modal.show .share-content {
  transform: translateY(0);
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.share-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.close-icon {
  width: 32rpx;
  height: 32rpx;
}

.share-options {
  display: flex;
  justify-content: space-around;
  margin-bottom: 32rpx;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.share-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 16rpx;
}

.share-cancel {
  text-align: center;
  padding: 24rpx;
  background: #f5f5f5;
  border-radius: 16rpx;
  color: #666;
  font-size: 28rpx;
}

/* 成功提示 */
.success-toast {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.success-toast.show {
  opacity: 1;
  visibility: visible;
}
