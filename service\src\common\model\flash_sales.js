module.exports = class extends think.Model {
  
  /**
   * 获取表名
   */
  get tableName() {
    return 'hiolabs_flash_sales';
  }
  
  /**
   * 获取活动状态的秒杀商品
   */
  async getActiveFlashSales() {
    const now = new Date();
    return await this.where({
      status: 'active',
      start_time: ['<=', now],
      end_time: ['>=', now]
    }).select();
  }
  
  /**
   * 获取即将开始的秒杀商品
   */
  async getUpcomingFlashSales() {
    const now = new Date();
    return await this.where({
      status: 'upcoming',
      start_time: ['>', now]
    }).order('start_time ASC').select();
  }
  
  /**
   * 获取已结束的秒杀商品
   */
  async getEndedFlashSales() {
    const now = new Date();
    return await this.where({
      end_time: ['<', now]
    }).select();
  }
  
  /**
   * 更新秒杀状态
   */
  async updateStatus() {
    const now = new Date();
    
    // 将已过期的活动标记为已结束
    await this.where({
      status: ['IN', ['upcoming', 'active']],
      end_time: ['<', now]
    }).update({
      status: 'ended'
    });
    
    // 将到时间的活动标记为进行中
    await this.where({
      status: 'upcoming',
      start_time: ['<=', now],
      end_time: ['>=', now]
    }).update({
      status: 'active'
    });
  }
  
  /**
   * 减少库存
   */
  async decreaseStock(flashSaleId, quantity) {
    return await this.where({
      id: flashSaleId,
      stock: ['>=', quantity]
    }).increment('sold_count', quantity).decrement('stock', quantity);
  }
  
  /**
   * 检查库存是否充足
   */
  async checkStock(flashSaleId, quantity) {
    const flashSale = await this.where({
      id: flashSaleId
    }).find();
    
    if (think.isEmpty(flashSale)) {
      return false;
    }
    
    return flashSale.stock >= quantity;
  }
};
