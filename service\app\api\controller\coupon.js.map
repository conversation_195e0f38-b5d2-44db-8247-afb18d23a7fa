{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\coupon.js"], "names": ["Base", "require", "module", "exports", "getLocalDateTime", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "availableAction", "userId", "getLoginUserId", "error", "console", "log", "now", "whereCondition", "status", "start_time", "end_time", "is_delete", "allCoupons", "model", "where", "select", "Array", "isArray", "map", "id", "c", "name", "type", "start_time_check", "end_time_check", "status_check", "coupons", "order", "length", "result", "coupon", "canReceive", "reason", "receivedCount", "user_id", "coupon_id", "count", "per_user_limit", "hasNewUserCoupon", "alias", "join", "push", "code", "discount_type", "discount_value", "min_amount", "max_discount", "total_quantity", "description", "success", "think", "logger", "fail", "receiveAction", "userInfo", "find", "isEmpty", "decodedNickname", "nickname", "<PERSON><PERSON><PERSON>", "from", "toString", "is_new_user", "couponId", "post", "totalReceived", "couponCode", "generateCouponCode", "expireAt", "valid_days", "add", "coupon_code", "expire_at", "source", "message", "stack", "sql", "myAction", "get", "userCoupons", "field", "userCoupon", "update", "unused", "used", "expired", "for<PERSON>ach", "availableForOrderAction", "amount", "calculatedDiscount", "calculateDiscount", "autoSelectBestAction", "availableCoupons", "getAvailableCouponsForAmount", "bestCoupon", "maxDiscount", "discount", "finalAmount", "Math", "min", "indexAction", "page", "size", "keyword", "_complex", "_logic", "countSelect", "data", "usedCount", "addAction", "existingCoupon", "auto_distribute", "toggleStatusAction", "includes", "updated_at", "deleteAction", "statisticsAction", "total", "active", "received", "timestamp", "random", "substr", "toUpperCase", "getExpireFieldName", "testRecord", "limit", "hasOwnProperty"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;;AAEAC,OAAOC,OAAP,GAAiB,cAAcH,IAAd,CAAmB;;AAElC;;;;AAIAI,qBAAmB;AACjB,UAAMC,OAAO,IAAIC,IAAJ,EAAb;;AAEA;AACA,UAAMC,OAAOF,KAAKG,WAAL,EAAb;AACA,UAAMC,QAAQC,OAAOL,KAAKM,QAAL,KAAkB,CAAzB,EAA4BC,QAA5B,CAAqC,CAArC,EAAwC,GAAxC,CAAd;AACA,UAAMC,MAAMH,OAAOL,KAAKS,OAAL,EAAP,EAAuBF,QAAvB,CAAgC,CAAhC,EAAmC,GAAnC,CAAZ;AACA,UAAMG,QAAQL,OAAOL,KAAKW,QAAL,EAAP,EAAwBJ,QAAxB,CAAiC,CAAjC,EAAoC,GAApC,CAAd;AACA,UAAMK,UAAUP,OAAOL,KAAKa,UAAL,EAAP,EAA0BN,QAA1B,CAAmC,CAAnC,EAAsC,GAAtC,CAAhB;AACA,UAAMO,UAAUT,OAAOL,KAAKe,UAAL,EAAP,EAA0BR,QAA1B,CAAmC,CAAnC,EAAsC,GAAtC,CAAhB;;AAEA,WAAQ,GAAEL,IAAK,IAAGE,KAAM,IAAGI,GAAI,IAAGE,KAAM,IAAGE,OAAQ,IAAGE,OAAQ,EAA9D;AACD;AACD;;;AAGME,iBAAN,GAAwB;AAAA;;AAAA;AACtB,UAAI;AACF;AACA,YAAIC,SAAS,IAAb;AACA,YAAI;AACFA,mBAAS,MAAM,MAAKC,cAAL,EAAf;AACD,SAFD,CAEE,OAAOC,KAAP,EAAc;AACdC,kBAAQC,GAAR,CAAY,cAAZ;AACD;;AAED;AACA;AACA,cAAMC,MAAM,MAAKvB,gBAAL,EAAZ;AACAqB,gBAAQC,GAAR,CAAY,mBAAZ;AACAD,gBAAQC,GAAR,CAAY,OAAZ,EAAqBC,GAArB;AACAF,gBAAQC,GAAR,CAAY,OAAZ,EAAqBJ,MAArB;;AAEA,cAAMM,iBAAiB;AACrBC,kBAAQ,QADa;AAErBC,sBAAY,CAAC,IAAD,EAAOH,GAAP,CAFS;AAGrBI,oBAAU,CAAC,IAAD,EAAOJ,GAAP,CAHW;AAIrBK,qBAAW;AAJU,SAAvB;AAMAP,gBAAQC,GAAR,CAAY,OAAZ,EAAqBE,cAArB;;AAEA;AACA,cAAMK,aAAa,MAAM,MAAKC,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B,EAAEH,WAAW,CAAb,EAA5B,EAA8CI,MAA9C,EAAzB;AACAX,gBAAQC,GAAR,CAAY,mBAAZ,EAAiC,OAAOO,UAAxC;AACAR,gBAAQC,GAAR,CAAY,kBAAZ,EAAgCW,MAAMC,OAAN,CAAcL,UAAd,CAAhC;AACAR,gBAAQC,GAAR,CAAY,iBAAZ,EAA+BO,UAA/B;;AAEA,YAAII,MAAMC,OAAN,CAAcL,UAAd,CAAJ,EAA+B;AAC7BR,kBAAQC,GAAR,CAAY,YAAZ,EAA0BO,WAAWM,GAAX,CAAe;AAAA,mBAAM;AAC7CC,kBAAIC,EAAED,EADuC;AAE7CE,oBAAMD,EAAEC,IAFqC;AAG7CC,oBAAMF,EAAEE,IAHqC;AAI7Cd,sBAAQY,EAAEZ,MAJmC;AAK7CC,0BAAYW,EAAEX,UAL+B;AAM7CC,wBAAUU,EAAEV,QANiC;AAO7CC,yBAAWS,EAAET,SAPgC;AAQ7CY,gCAAkBH,EAAEX,UAAF,IAAgBH,GARW;AAS7CkB,8BAAgBJ,EAAEV,QAAF,IAAcJ,GATe;AAU7CmB,4BAAcL,EAAEZ,MAAF,KAAa;AAVkB,aAAN;AAAA,WAAf,CAA1B;AAYD;;AAED,YAAIkB,UAAU,MAAM,MAAKb,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4BP,cAA5B,EAA4CoB,KAA5C,CAAkD,sDAAlD,EAA0GZ,MAA1G,EAApB;AACAX,gBAAQC,GAAR,CAAY,gBAAZ,EAA8B,OAAOqB,OAArC;AACAtB,gBAAQC,GAAR,CAAY,eAAZ,EAA6BW,MAAMC,OAAN,CAAcS,OAAd,CAA7B;AACAtB,gBAAQC,GAAR,CAAY,cAAZ,EAA4BqB,OAA5B;;AAEA;AACA,YAAI,CAACV,MAAMC,OAAN,CAAcS,OAAd,CAAL,EAA6B;AAC3BtB,kBAAQD,KAAR,CAAc,wBAAd;AACAuB,oBAAU,EAAV;AACD;;AAEDtB,gBAAQC,GAAR,CAAY,cAAZ,EAA4BqB,QAAQE,MAApC;AACA,YAAIF,QAAQE,MAAR,GAAiB,CAArB,EAAwB;AACtBxB,kBAAQC,GAAR,CAAY,UAAZ,EAAwBqB,QAAQR,GAAR,CAAY;AAAA,mBAAM;AACxCC,kBAAIC,EAAED,EADkC;AAExCE,oBAAMD,EAAEC,IAFgC;AAGxCC,oBAAMF,EAAEE,IAHgC;AAIxCd,sBAAQY,EAAEZ,MAJ8B;AAKxCC,0BAAYW,EAAEX,UAL0B;AAMxCC,wBAAUU,EAAEV,QAN4B;AAOxCC,yBAAWS,EAAET;AAP2B,aAAN;AAAA,WAAZ,CAAxB;AASD;;AAED;AACA,YAAIkB,SAAS,EAAb;AACA,YAAIb,MAAMC,OAAN,CAAcS,OAAd,KAA0BA,QAAQE,MAAR,GAAiB,CAA/C,EAAkD;AAChD,eAAK,IAAIE,MAAT,IAAmBJ,OAAnB,EAA4B;AAC1B,gBAAIK,aAAa,IAAjB;AACA,gBAAIC,SAAS,EAAb;AACA,gBAAIC,gBAAgB,CAApB;;AAEA,gBAAIhC,MAAJ,EAAY;AACV;;AAEA;AACAgC,8BAAgB,MAAM,MAAKpB,KAAL,CAAW,cAAX,EAA2BC,KAA3B,CAAiC;AACrDoB,yBAASjC,MAD4C;AAErDkC,2BAAWL,OAAOX;AAFmC,eAAjC,EAGnBiB,KAHmB,EAAtB;;AAKA;AACA,kBAAIH,iBAAiBH,OAAOO,cAA5B,EAA4C;AAC1CN,6BAAa,KAAb;AACAC,yBAAS,KAAT;AACD;;AAED;AACA,kBAAID,cAAcD,OAAOR,IAAP,KAAgB,SAAlC,EAA6C;AAC3C,sBAAMgB,mBAAmB,MAAM,MAAKzB,KAAL,CAAW,cAAX,EAA2B0B,KAA3B,CAAiC,IAAjC,EAC5BC,IAD4B,CACvB,0CADuB,EAE5B1B,KAF4B,CAEtB;AACL,gCAAcb,MADT;AAEL,4BAAU;AAFL,iBAFsB,EAK1BmC,KAL0B,EAA/B;;AAOA,oBAAIE,mBAAmB,CAAvB,EAA0B;AACxBP,+BAAa,KAAb;AACAC,2BAAS,QAAT;AACD;AACF;AACF;;AAEDH,mBAAOY,IAAP,CAAY;AACVtB,kBAAIW,OAAOX,EADD;AAEVE,oBAAMS,OAAOT,IAFH;AAGVqB,oBAAMZ,OAAOY,IAHH;AAIVpB,oBAAMQ,OAAOR,IAJH;AAKVqB,6BAAeb,OAAOa,aALZ;AAMVC,8BAAgBd,OAAOc,cANb;AAOVC,0BAAYf,OAAOe,UAAP,IAAqB,CAPvB;AAQVC,4BAAchB,OAAOgB,YAAP,IAAuB,CAR3B;AASVC,8BAAgBjB,OAAOiB,cATb;AAUVV,8BAAgBP,OAAOO,cAVb;AAWVW,2BAAalB,OAAOkB,WAAP,IAAsB,EAXzB;AAYVvC,0BAAYqB,OAAOrB,UAZT;AAaVC,wBAAUoB,OAAOpB,QAbP;AAcVqB,0BAAYA,UAdF;AAeVC,sBAAQA,MAfE;AAgBVC,6BAAeA;AAhBL,aAAZ;AAkBD;AACF;;AAED,eAAO,MAAKgB,OAAL,CAAapB,MAAb,CAAP;AACD,OAlID,CAkIE,OAAO1B,KAAP,EAAc;AACd+C,cAAMC,MAAN,CAAahD,KAAb,CAAmB,aAAnB,EAAkCA,KAAlC;AACA,eAAO,MAAKiD,IAAL,CAAU,SAAV,CAAP;AACD;AAtIqB;AAuIvB;;AAED;;;AAGMC,eAAN,GAAsB;AAAA;;AAAA;AACpB,UAAI;AACF,cAAMpD,SAAS,MAAM,OAAKC,cAAL,EAArB;AACA,YAAI,CAACD,MAAL,EAAa;AACX,iBAAO,OAAKmD,IAAL,CAAU,MAAV,CAAP;AACD;;AAED;AACAhD,gBAAQC,GAAR,CAAY,kBAAZ;AACAD,gBAAQC,GAAR,CAAY,SAAZ,EAAuBJ,MAAvB,EAA+B,OAA/B,EAAwC,OAAOA,MAA/C;;AAEA,cAAMqD,WAAW,MAAM,OAAKzC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB,EAAEK,IAAIlB,MAAN,EAAzB,EAAyCsD,IAAzC,EAAvB;AACAnD,gBAAQC,GAAR,CAAY,WAAZ,EAAyBiD,QAAzB;;AAEA,YAAIJ,MAAMM,OAAN,CAAcF,QAAd,CAAJ,EAA6B;AAC3BlD,kBAAQC,GAAR,CAAY,OAAZ;AACA,iBAAO,OAAK+C,IAAL,CAAU,OAAV,CAAP;AACD;;AAED;AACA,YAAIK,kBAAkB,EAAtB;AACA,YAAIH,SAASI,QAAb,EAAuB;AACrB,cAAI;AACFD,8BAAkBE,OAAOC,IAAP,CAAYN,SAASI,QAArB,EAA+B,QAA/B,EAAyCG,QAAzC,EAAlB;AACD,WAFD,CAEE,OAAO1D,KAAP,EAAc;AACdC,oBAAQC,GAAR,CAAY,eAAZ,EAA6BiD,SAASI,QAAtC;AACAD,8BAAkBH,SAASI,QAA3B;AACD;AACF;;AAEDtD,gBAAQC,GAAR,CAAY,gBAAZ;AACAD,gBAAQC,GAAR,CAAY,OAAZ,EAAqBJ,MAArB;AACAG,gBAAQC,GAAR,CAAY,OAAZ,EAAqBiD,SAASI,QAA9B;AACAtD,gBAAQC,GAAR,CAAY,OAAZ,EAAqBoD,eAArB;AACArD,gBAAQC,GAAR,CAAY,cAAZ,EAA4BiD,SAASQ,WAArC;;AAEA;;AAEA1D,gBAAQC,GAAR,CAAY,gBAAZ;AACAD,gBAAQC,GAAR,CAAY,aAAZ,EAA2B,CAAC,CAACiD,SAASI,QAAtC;AACAtD,gBAAQC,GAAR,CAAY,kBAAZ,EAAgCoD,eAAhC;AACArD,gBAAQC,GAAR,CAAY,cAAZ,EAA4BiD,SAASQ,WAArC;;AAEA;AACA,YAAI,CAACR,SAASI,QAAV,IACAD,oBAAoB,MADpB,IAEAA,oBAAoB,MAFpB,IAGAA,oBAAoB,MAHxB,EAGgC;AAC9BrD,kBAAQC,GAAR,CAAY,sBAAZ;AACA,iBAAO,OAAK+C,IAAL,CAAU,wBAAV,CAAP;AACD;;AAEDhD,gBAAQC,GAAR,CAAY,UAAZ;;AAEA,cAAM,EAAE0D,QAAF,KAAe,OAAKC,IAAL,EAArB;AACA5D,gBAAQC,GAAR,CAAY,gBAAZ;AACAD,gBAAQC,GAAR,CAAY,WAAZ,EAAyB0D,QAAzB,EAAmC,OAAnC,EAA4C,OAAOA,QAAnD;;AAEA,YAAI,CAACA,QAAL,EAAe;AACb,iBAAO,OAAKX,IAAL,CAAU,MAAV,CAAP;AACD;;AAEDhD,gBAAQC,GAAR,CAAY,iBAAZ;AACA,cAAMyB,SAAS,MAAM,OAAKjB,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC/CK,cAAI4C,QAD2C;AAE/CvD,kBAAQ,QAFuC;AAG/CG,qBAAW;AAHoC,SAA5B,EAIlB4C,IAJkB,EAArB;AAKAnD,gBAAQC,GAAR,CAAY,UAAZ,EAAwByB,MAAxB;;AAEA,YAAIoB,MAAMM,OAAN,CAAc1B,MAAd,CAAJ,EAA2B;AACzB,iBAAO,OAAKsB,IAAL,CAAU,YAAV,CAAP;AACD;;AAED;AACA,cAAM9C,MAAM,IAAIrB,IAAJ,EAAZ;AACA,YAAI,IAAIA,IAAJ,CAAS6C,OAAOrB,UAAhB,IAA8BH,GAA9B,IAAqC,IAAIrB,IAAJ,CAAS6C,OAAOpB,QAAhB,IAA4BJ,GAArE,EAA0E;AACxE,iBAAO,OAAK8C,IAAL,CAAU,WAAV,CAAP;AACD;;AAED;AACA,YAAItB,OAAOR,IAAP,KAAgB,SAApB,EAA+B;AAC7BlB,kBAAQC,GAAR,CAAY,iBAAZ;;AAEA;AACA,gBAAMiC,mBAAmB,MAAM,OAAKzB,KAAL,CAAW,cAAX,EAA2B0B,KAA3B,CAAiC,IAAjC,EAC5BC,IAD4B,CACvB,0CADuB,EAE5B1B,KAF4B,CAEtB;AACL,0BAAcb,MADT;AAEL,sBAAU;AAFL,WAFsB,EAK1BmC,KAL0B,EAA/B;;AAOAhC,kBAAQC,GAAR,CAAY,cAAZ,EAA4BiC,gBAA5B;;AAEA,cAAIA,mBAAmB,CAAvB,EAA0B;AACxBlC,oBAAQC,GAAR,CAAY,oBAAZ;AACA,mBAAO,OAAK+C,IAAL,CAAU,mBAAV,CAAP;AACD;AACDhD,kBAAQC,GAAR,CAAY,mBAAZ;AACD;;AAED;AACAD,gBAAQC,GAAR,CAAY,oBAAZ;AACAD,gBAAQC,GAAR,CAAY,iBAAZ,EAA+BJ,MAA/B,EAAuC,YAAvC,EAAqD8D,QAArD;AACA,cAAM9B,gBAAgB,MAAM,OAAKpB,KAAL,CAAW,cAAX,EAA2BC,KAA3B,CAAiC;AAC3DoB,mBAASjC,MADkD;AAE3DkC,qBAAW4B;AAFgD,SAAjC,EAGzB3B,KAHyB,EAA5B;AAIAhC,gBAAQC,GAAR,CAAY,UAAZ,EAAwB4B,aAAxB;;AAEA,YAAIA,iBAAiBH,OAAOO,cAA5B,EAA4C;AAC1C,iBAAO,OAAKe,IAAL,CAAU,eAAV,CAAP;AACD;;AAED;AACA,YAAItB,OAAOiB,cAAP,GAAwB,CAA5B,EAA+B;AAC7B,gBAAMkB,gBAAgB,MAAM,OAAKpD,KAAL,CAAW,cAAX,EAA2BC,KAA3B,CAAiC;AAC3DqB,uBAAW4B;AADgD,WAAjC,EAEzB3B,KAFyB,EAA5B;;AAIA,cAAI6B,iBAAiBnC,OAAOiB,cAA5B,EAA4C;AAC1C,mBAAO,OAAKK,IAAL,CAAU,SAAV,CAAP;AACD;AACF;;AAED;AACA,cAAMc,aAAa,OAAKC,kBAAL,EAAnB;AACA,cAAMC,WAAWtC,OAAOuC,UAAP,GACf,IAAIpF,IAAJ,CAASA,KAAKqB,GAAL,KAAawB,OAAOuC,UAAP,GAAoB,EAApB,GAAyB,EAAzB,GAA8B,EAA9B,GAAmC,IAAzD,CADe,GAEf,IAAIpF,IAAJ,CAAS6C,OAAOpB,QAAhB,CAFF;;AAIAN,gBAAQC,GAAR,CAAY,qBAAZ;AACAD,gBAAQC,GAAR,CAAY,OAAZ,EAAqBJ,MAArB;AACAG,gBAAQC,GAAR,CAAY,QAAZ,EAAsB0D,QAAtB;AACA3D,gBAAQC,GAAR,CAAY,QAAZ,EAAsB6D,UAAtB;AACA9D,gBAAQC,GAAR,CAAY,OAAZ,EAAqB+D,QAArB;AACAhE,gBAAQC,GAAR,CAAY,QAAZ,EAAsByB,MAAtB;;AAEA,cAAM,OAAKjB,KAAL,CAAW,cAAX,EAA2ByD,GAA3B,CAA+B;AACnCpC,mBAASjC,MAD0B;AAEnCkC,qBAAW4B,QAFwB;AAGnCQ,uBAAaL,UAHsB;AAInCM,qBAAWJ,QAJwB;AAKnCK,kBAAQ;AAL2B,SAA/B,CAAN;;AAQA,eAAO,OAAKxB,OAAL,CAAa,MAAb,CAAP;AACD,OAlJD,CAkJE,OAAO9C,KAAP,EAAc;AACd+C,cAAMC,MAAN,CAAahD,KAAb,CAAmB,UAAnB,EAA+BA,KAA/B;AACAC,gBAAQD,KAAR,CAAc,qBAAd;AACAC,gBAAQD,KAAR,CAAc,OAAd,EAAuBA,MAAMuE,OAA7B;AACAtE,gBAAQD,KAAR,CAAc,OAAd,EAAuBA,MAAMwE,KAA7B;AACAvE,gBAAQD,KAAR,CAAc,OAAd,EAAuBA,MAAMuC,IAA7B;AACAtC,gBAAQD,KAAR,CAAc,QAAd,EAAwBA,MAAMyE,GAA9B;AACAxE,gBAAQD,KAAR,CAAc,kCAAd;AACA,eAAO,OAAKiD,IAAL,CAAU,YAAV,CAAP;AACD;AA5JmB;AA6JrB;;AAED;;;AAGMyB,UAAN,GAAiB;AAAA;;AAAA;AACf,UAAI;AACF,cAAM5E,SAAS,MAAM,OAAKC,cAAL,EAArB;AACA,YAAI,CAACD,MAAL,EAAa;AACX,iBAAO,OAAKmD,IAAL,CAAU,MAAV,CAAP;AACD;;AAED,cAAM,EAAE5C,SAAS,QAAX,KAAwB,OAAKsE,GAAL,EAA9B;;AAEA,cAAMC,cAAc,MAAM,OAAKlE,KAAL,CAAW,cAAX,EAA2B0B,KAA3B,CAAiC,IAAjC,EACvBC,IADuB,CAClB,0CADkB,EAEvB1B,KAFuB,CAEjB;AACL,wBAAcb,MADT;AAEL,uBAAaO,MAFR;AAGL,yBAAe;AAHV,SAFiB,EAOvBwE,KAPuB,CAOjB,sGAPiB,EAQvBrD,KARuB,CAQjB,qBARiB,EASvBZ,MATuB,EAA1B;;AAWA;AACA,cAAMT,MAAM,IAAIrB,IAAJ,EAAZ;AACA,aAAK,IAAIgG,UAAT,IAAuBF,WAAvB,EAAoC;AAClC,cAAIE,WAAWzE,MAAX,KAAsB,QAAtB,IAAkC,IAAIvB,IAAJ,CAASgG,WAAWT,SAApB,IAAiClE,GAAvE,EAA4E;AAC1E;AACA,kBAAM,OAAKO,KAAL,CAAW,cAAX,EAA2BC,KAA3B,CAAiC,EAACK,IAAI8D,WAAW9D,EAAhB,EAAjC,EAAsD+D,MAAtD,CAA6D;AACjE1E,sBAAQ;AADyD,aAA7D,CAAN;AAGAyE,uBAAWzE,MAAX,GAAoB,SAApB;AACD;AACF;;AAED;AACA,cAAMqB,SAAS;AACbsD,kBAAQ,EADK;AAEbC,gBAAM,EAFO;AAGbC,mBAAS;AAHI,SAAf;;AAMAN,oBAAYO,OAAZ,CAAoB,kBAAU;AAC5B,cAAIzD,OAAOC,OAAOtB,MAAd,CAAJ,EAA2B;AACzBqB,mBAAOC,OAAOtB,MAAd,EAAsBiC,IAAtB,CAA2BX,MAA3B;AACD;AACF,SAJD;;AAMA,eAAO,OAAKmB,OAAL,CAAapB,MAAb,CAAP;AACD,OA7CD,CA6CE,OAAO1B,KAAP,EAAc;AACd+C,cAAMC,MAAN,CAAahD,KAAb,CAAmB,YAAnB,EAAiCA,KAAjC;AACA,eAAO,OAAKiD,IAAL,CAAU,SAAV,CAAP;AACD;AAjDc;AAkDhB;;AAED;;;AAGMmC,yBAAN,GAAgC;AAAA;;AAAA;AAC9B,UAAI;AACF,cAAMtF,SAAS,MAAM,OAAKC,cAAL,EAArB;AACA,YAAI,CAACD,MAAL,EAAa;AACX,iBAAO,OAAKmD,IAAL,CAAU,MAAV,CAAP;AACD;;AAED,cAAM,EAAEoC,MAAF,KAAa,OAAKV,GAAL,EAAnB;AACA,YAAI,CAACU,MAAD,IAAWA,UAAU,CAAzB,EAA4B;AAC1B,iBAAO,OAAKpC,IAAL,CAAU,UAAV,CAAP;AACD;;AAED,cAAM2B,cAAc,MAAM,OAAKlE,KAAL,CAAW,cAAX,EAA2B0B,KAA3B,CAAiC,IAAjC,EACvBC,IADuB,CAClB,0CADkB,EAEvB1B,KAFuB,CAEjB;AACL,wBAAcb,MADT;AAEL,uBAAa,QAFR;AAGL,0BAAgB,CAAC,IAAD,EAAO,OAAKlB,gBAAL,EAAP,CAHX;AAIL,0BAAgB,CAAC,IAAD,EAAOyG,MAAP,CAJX;AAKL,yBAAe;AALV,SAFiB,EASvBR,KATuB,CASjB,sGATiB,EAUvBrD,KAVuB,CAUjB,uBAViB,EAWvBZ,MAXuB,EAA1B;;AAaA;AACAgE,oBAAYO,OAAZ,CAAoB,kBAAU;AAC5BxD,iBAAO2D,kBAAP,GAA4B,OAAKC,iBAAL,CAAuB5D,MAAvB,EAA+B0D,MAA/B,CAA5B;AACD,SAFD;;AAIA,eAAO,OAAKvC,OAAL,CAAa8B,WAAb,CAAP;AACD,OA9BD,CA8BE,OAAO5E,KAAP,EAAc;AACd+C,cAAMC,MAAN,CAAahD,KAAb,CAAmB,cAAnB,EAAmCA,KAAnC;AACA,eAAO,OAAKiD,IAAL,CAAU,WAAV,CAAP;AACD;AAlC6B;AAmC/B;;AAED;;;AAGMuC,sBAAN,GAA6B;AAAA;;AAAA;AAC3B,UAAI;AACF,cAAM1F,SAAS,MAAM,OAAKC,cAAL,EAArB;AACA,YAAI,CAACD,MAAL,EAAa;AACX,iBAAO,OAAKmD,IAAL,CAAU,MAAV,CAAP;AACD;;AAED,cAAM,EAAEoC,MAAF,KAAa,OAAKxB,IAAL,EAAnB;AACA,YAAI,CAACwB,MAAD,IAAWA,UAAU,CAAzB,EAA4B;AAC1B,iBAAO,OAAKpC,IAAL,CAAU,UAAV,CAAP;AACD;;AAED,cAAMwC,mBAAmB,MAAM,OAAKC,4BAAL,CAAkC5F,MAAlC,EAA0CuF,MAA1C,CAA/B;;AAEA,YAAIM,aAAa,IAAjB;AACA,YAAIC,cAAc,CAAlB;;AAEA,aAAK,IAAIjE,MAAT,IAAmB8D,gBAAnB,EAAqC;AACnC,gBAAMI,WAAW,OAAKN,iBAAL,CAAuB5D,MAAvB,EAA+B0D,MAA/B,CAAjB;AACA,cAAIQ,WAAWD,WAAf,EAA4B;AAC1BA,0BAAcC,QAAd;AACAF,yBAAahE,MAAb;AACD;AACF;;AAED,eAAO,OAAKmB,OAAL,CAAa;AAClBnB,kBAAQgE,UADU;AAElBE,oBAAUD,WAFQ;AAGlBE,uBAAaT,SAASO;AAHJ,SAAb,CAAP;AAKD,OA7BD,CA6BE,OAAO5F,KAAP,EAAc;AACd+C,cAAMC,MAAN,CAAahD,KAAb,CAAmB,YAAnB,EAAiCA,KAAjC;AACA,eAAO,OAAKiD,IAAL,CAAU,QAAV,CAAP;AACD;AAjC0B;AAkC5B;;AAED;;;AAGAsC,oBAAkB5D,MAAlB,EAA0B0D,MAA1B,EAAkC;AAChC,QAAI1D,OAAOa,aAAP,KAAyB,OAA7B,EAAsC;AACpC,aAAOuD,KAAKC,GAAL,CAASrE,OAAOc,cAAhB,EAAgC4C,MAAhC,CAAP;AACD,KAFD,MAEO;AACL,YAAMQ,WAAWR,UAAU1D,OAAOc,cAAP,GAAwB,GAAlC,CAAjB;AACA,aAAOsD,KAAKC,GAAL,CAASH,QAAT,EAAmBlE,OAAOgB,YAAP,IAAuBkD,QAA1C,CAAP;AACD;AACF;;AAED;;;AAGMH,8BAAN,CAAmC5F,MAAnC,EAA2CuF,MAA3C,EAAmD;AAAA;;AAAA;AACjD,aAAO,MAAM,OAAK3E,KAAL,CAAW,cAAX,EAA2B0B,KAA3B,CAAiC,IAAjC,EACVC,IADU,CACL,0CADK,EAEV1B,KAFU,CAEJ;AACL,sBAAcb,MADT;AAEL,qBAAa,QAFR;AAGL,wBAAgB,CAAC,IAAD,EAAO,OAAKlB,gBAAL,EAAP,CAHX;AAIL,wBAAgB,CAAC,IAAD,EAAOyG,MAAP,CAJX;AAKL,uBAAe;AALV,OAFI,EASVR,KATU,CASJ,WATI,EAUVjE,MAVU,EAAb;AADiD;AAYlD;;AAED;;;AAGMqF,aAAN,GAAoB;AAAA;;AAAA;AAClB,UAAI;AACF,cAAM,EAAEC,OAAO,CAAT,EAAYC,OAAO,EAAnB,EAAuBhF,IAAvB,EAA6Bd,MAA7B,EAAqC+F,OAArC,KAAiD,OAAKzB,GAAL,EAAvD;;AAEA,YAAIhE,QAAQ,EAAEH,WAAW,CAAb,EAAZ;;AAEA,YAAIW,IAAJ,EAAU;AACRR,gBAAMQ,IAAN,GAAaA,IAAb;AACD;;AAED,YAAId,MAAJ,EAAY;AACVM,gBAAMN,MAAN,GAAeA,MAAf;AACD;;AAED,YAAI+F,OAAJ,EAAa;AACXzF,gBAAM0F,QAAN,GAAiB;AACfnF,kBAAM,CAAC,MAAD,EAAU,IAAGkF,OAAQ,GAArB,CADS;AAEf7D,kBAAM,CAAC,MAAD,EAAU,IAAG6D,OAAQ,GAArB,CAFS;AAGfE,oBAAQ;AAHO,WAAjB;AAKD;;AAED,cAAM/E,UAAU,MAAM,OAAKb,KAAL,CAAW,SAAX,EACnBC,KADmB,CACbA,KADa,EAEnBa,KAFmB,CAEb,iBAFa,EAGnB0E,IAHmB,CAGdA,IAHc,EAGRC,IAHQ,EAInBI,WAJmB,EAAtB;;AAMA;AACA,aAAK,IAAI5E,MAAT,IAAmBJ,QAAQiF,IAA3B,EAAiC;AAC/B7E,iBAAOG,aAAP,GAAuB,MAAM,OAAKpB,KAAL,CAAW,cAAX,EAA2BC,KAA3B,CAAiC;AAC5DqB,uBAAWL,OAAOX;AAD0C,WAAjC,EAE1BiB,KAF0B,EAA7B;;AAIAN,iBAAO8E,SAAP,GAAmB,MAAM,OAAK/F,KAAL,CAAW,cAAX,EAA2BC,KAA3B,CAAiC;AACxDqB,uBAAWL,OAAOX,EADsC;AAExDX,oBAAQ;AAFgD,WAAjC,EAGtB4B,KAHsB,EAAzB;AAID;;AAED,eAAO,OAAKa,OAAL,CAAavB,OAAb,CAAP;AACD,OAxCD,CAwCE,OAAOvB,KAAP,EAAc;AACd+C,cAAMC,MAAN,CAAahD,KAAb,CAAmB,YAAnB,EAAiCA,KAAjC;AACA,eAAO,OAAKiD,IAAL,CAAU,QAAV,CAAP;AACD;AA5CiB;AA6CnB;;AAED;;;AAGMyD,WAAN,GAAkB;AAAA;;AAAA;AAChB,UAAI;AACF,cAAMF,OAAO,OAAK3C,IAAL,EAAb;;AAEA;AACA,YAAI,CAAC2C,KAAKtF,IAAN,IAAc,CAACsF,KAAKrF,IAApB,IAA4B,CAACqF,KAAKhE,aAAlC,IAAmD,CAACgE,KAAK/D,cAA7D,EAA6E;AAC3E,iBAAO,OAAKQ,IAAL,CAAU,aAAV,CAAP;AACD;;AAED;AACA,YAAI,CAACuD,KAAKlG,UAAN,IAAoB,CAACkG,KAAKjG,QAA9B,EAAwC;AACtC,iBAAO,OAAK0C,IAAL,CAAU,QAAV,CAAP;AACD;;AAED,YAAI,IAAInE,IAAJ,CAAS0H,KAAKlG,UAAd,KAA6B,IAAIxB,IAAJ,CAAS0H,KAAKjG,QAAd,CAAjC,EAA0D;AACxD,iBAAO,OAAK0C,IAAL,CAAU,cAAV,CAAP;AACD;;AAED;AACA,YAAI,CAACuD,KAAKjE,IAAV,EAAgB;AACdiE,eAAKjE,IAAL,GAAY,OAAKyB,kBAAL,EAAZ;AACD,SAFD,MAEO;AACL;AACA,gBAAM2C,iBAAiB,MAAM,OAAKjG,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AACvD4B,kBAAMiE,KAAKjE,IAD4C;AAEvD/B,uBAAW;AAF4C,WAA5B,EAG1B4C,IAH0B,EAA7B;;AAKA,cAAI,CAACL,MAAMM,OAAN,CAAcsD,cAAd,CAAL,EAAoC;AAClC,mBAAO,OAAK1D,IAAL,CAAU,UAAV,CAAP;AACD;AACF;;AAED;AACAuD,aAAKnG,MAAL,GAAcmG,KAAKnG,MAAL,IAAe,QAA7B;AACAmG,aAAK5D,cAAL,GAAsB4D,KAAK5D,cAAL,IAAuB,CAAC,CAA9C;AACA4D,aAAKtE,cAAL,GAAsBsE,KAAKtE,cAAL,IAAuB,CAA7C;AACAsE,aAAKI,eAAL,GAAuBJ,KAAKI,eAAL,GAAuB,CAAvB,GAA2B,CAAlD;;AAEA,cAAMhD,WAAW,MAAM,OAAKlD,KAAL,CAAW,SAAX,EAAsByD,GAAtB,CAA0BqC,IAA1B,CAAvB;;AAEA,eAAO,OAAK1D,OAAL,CAAa,EAAE9B,IAAI4C,QAAN,EAAgBW,SAAS,MAAzB,EAAb,CAAP;AACD,OAzCD,CAyCE,OAAOvE,KAAP,EAAc;AACd+C,cAAMC,MAAN,CAAahD,KAAb,CAAmB,UAAnB,EAA+BA,KAA/B;AACA,eAAO,OAAKiD,IAAL,CAAU,MAAV,CAAP;AACD;AA7Ce;AA8CjB;;AAED;;;AAGM4D,oBAAN,GAA2B;AAAA;;AAAA;AACzB,UAAI;AACF,cAAM,EAAE7F,EAAF,EAAMX,MAAN,KAAiB,OAAKwD,IAAL,EAAvB;;AAEA,YAAI,CAAC7C,EAAD,IAAO,CAACX,MAAZ,EAAoB;AAClB,iBAAO,OAAK4C,IAAL,CAAU,MAAV,CAAP;AACD;;AAED,YAAI,CAAC,CAAC,QAAD,EAAW,UAAX,EAAuB6D,QAAvB,CAAgCzG,MAAhC,CAAL,EAA8C;AAC5C,iBAAO,OAAK4C,IAAL,CAAU,QAAV,CAAP;AACD;;AAED,cAAMtB,SAAS,MAAM,OAAKjB,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC/CK,cAAIA,EAD2C;AAE/CR,qBAAW;AAFoC,SAA5B,EAGlB4C,IAHkB,EAArB;;AAKA,YAAIL,MAAMM,OAAN,CAAc1B,MAAd,CAAJ,EAA2B;AACzB,iBAAO,OAAKsB,IAAL,CAAU,QAAV,CAAP;AACD;;AAED,cAAM,OAAKvC,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B,EAAEK,IAAIA,EAAN,EAA5B,EAAwC+D,MAAxC,CAA+C;AACnD1E,kBAAQA,MAD2C;AAEnD0G,sBAAY,IAAIjI,IAAJ;AAFuC,SAA/C,CAAN;;AAKA,eAAO,OAAKgE,OAAL,CAAa,QAAb,CAAP;AACD,OA1BD,CA0BE,OAAO9C,KAAP,EAAc;AACd+C,cAAMC,MAAN,CAAahD,KAAb,CAAmB,YAAnB,EAAiCA,KAAjC;AACA,eAAO,OAAKiD,IAAL,CAAU,MAAV,CAAP;AACD;AA9BwB;AA+B1B;;AAED;;;AAGM+D,cAAN,GAAqB;AAAA;;AAAA;AACnB,UAAI;AACF,cAAM,EAAEhG,EAAF,KAAS,QAAK6C,IAAL,EAAf;;AAEA,YAAI,CAAC7C,EAAL,EAAS;AACP,iBAAO,QAAKiC,IAAL,CAAU,MAAV,CAAP;AACD;;AAED,cAAMtB,SAAS,MAAM,QAAKjB,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC/CK,cAAIA,EAD2C;AAE/CR,qBAAW;AAFoC,SAA5B,EAGlB4C,IAHkB,EAArB;;AAKA,YAAIL,MAAMM,OAAN,CAAc1B,MAAd,CAAJ,EAA2B;AACzB,iBAAO,QAAKsB,IAAL,CAAU,QAAV,CAAP;AACD;;AAED;AACA,cAAMnB,gBAAgB,MAAM,QAAKpB,KAAL,CAAW,cAAX,EAA2BC,KAA3B,CAAiC;AAC3DqB,qBAAWhB;AADgD,SAAjC,EAEzBiB,KAFyB,EAA5B;;AAIA,YAAIH,gBAAgB,CAApB,EAAuB;AACrB,iBAAO,QAAKmB,IAAL,CAAU,iBAAV,CAAP;AACD;;AAED,cAAM,QAAKvC,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B,EAAEK,IAAIA,EAAN,EAA5B,EAAwC+D,MAAxC,CAA+C;AACnDvE,qBAAW,CADwC;AAEnDuG,sBAAY,IAAIjI,IAAJ;AAFuC,SAA/C,CAAN;;AAKA,eAAO,QAAKgE,OAAL,CAAa,MAAb,CAAP;AACD,OA/BD,CA+BE,OAAO9C,KAAP,EAAc;AACd+C,cAAMC,MAAN,CAAahD,KAAb,CAAmB,UAAnB,EAA+BA,KAA/B;AACA,eAAO,QAAKiD,IAAL,CAAU,MAAV,CAAP;AACD;AAnCkB;AAoCpB;;AAED;;;AAGMgE,kBAAN,GAAyB;AAAA;;AAAA;AACvB,UAAI;AACF,cAAMC,QAAQ,MAAM,QAAKxG,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B,EAAEH,WAAW,CAAb,EAA5B,EAA8CyB,KAA9C,EAApB;AACA,cAAMkF,SAAS,MAAM,QAAKzG,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC/CH,qBAAW,CADoC;AAE/CH,kBAAQ;AAFuC,SAA5B,EAGlB4B,KAHkB,EAArB;AAIA,cAAMmF,WAAW,MAAM,QAAK1G,KAAL,CAAW,cAAX,EAA2BuB,KAA3B,EAAvB;AACA,cAAMgD,OAAO,MAAM,QAAKvE,KAAL,CAAW,cAAX,EAA2BC,KAA3B,CAAiC;AAClDN,kBAAQ;AAD0C,SAAjC,EAEhB4B,KAFgB,EAAnB;;AAIA,eAAO,QAAKa,OAAL,CAAa;AAClBoE,eADkB;AAElBC,gBAFkB;AAGlBC,kBAHkB;AAIlBnC;AAJkB,SAAb,CAAP;AAMD,OAjBD,CAiBE,OAAOjF,KAAP,EAAc;AACd+C,cAAMC,MAAN,CAAahD,KAAb,CAAmB,WAAnB,EAAgCA,KAAhC;AACA,eAAO,QAAKiD,IAAL,CAAU,QAAV,CAAP;AACD;AArBsB;AAsBxB;;AAED;;;AAGAe,uBAAqB;AACnB,UAAMqD,YAAYvI,KAAKqB,GAAL,GAAWuD,QAAX,CAAoB,EAApB,CAAlB;AACA,UAAM4D,SAASvB,KAAKuB,MAAL,GAAc5D,QAAd,CAAuB,EAAvB,EAA2B6D,MAA3B,CAAkC,CAAlC,EAAqC,CAArC,CAAf;AACA,WAAQ,MAAKF,SAAU,GAAEC,MAAO,EAAzB,CAA2BE,WAA3B,EAAP;AACD;;AAED;;;AAGMC,oBAAN,GAA2B;AAAA;;AAAA;AACzB,UAAI;AACF;AACA,cAAMC,aAAa,MAAM,QAAKhH,KAAL,CAAW,cAAX,EAA2BiH,KAA3B,CAAiC,CAAjC,EAAoCvE,IAApC,EAAzB;AACA,YAAIsE,cAAcA,WAAWE,cAAX,CAA0B,WAA1B,CAAlB,EAA0D;AACxD,iBAAO,WAAP;AACD,SAFD,MAEO,IAAIF,cAAcA,WAAWE,cAAX,CAA0B,YAA1B,CAAlB,EAA2D;AAChE,iBAAO,YAAP;AACD,SAFM,MAEA;AACL;AACA,iBAAO,WAAP;AACD;AACF,OAXD,CAWE,OAAO5H,KAAP,EAAc;AACdC,gBAAQC,GAAR,CAAY,4BAAZ,EAA0CF,MAAMuE,OAAhD;AACA,eAAO,WAAP;AACD;AAfwB;AAgB1B;AA7sBiC,CAApC", "file": "..\\..\\..\\src\\api\\controller\\coupon.js", "sourcesContent": ["const Base = require('./base.js');\n\nmodule.exports = class extends Base {\n\n  /**\n   * 获取本地时间字符串，兼容Windows和Linux\n   * @returns {string} 格式: YYYY-MM-DD HH:mm:ss\n   */\n  getLocalDateTime() {\n    const date = new Date();\n\n    // 使用本地时间，不使用UTC\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    const hours = String(date.getHours()).padStart(2, '0');\n    const minutes = String(date.getMinutes()).padStart(2, '0');\n    const seconds = String(date.getSeconds()).padStart(2, '0');\n\n    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n  }\n  /**\n   * 获取可领取的优惠券列表\n   */\n  async availableAction() {\n    try {\n      // 获取用户ID（如果已登录）\n      let userId = null;\n      try {\n        userId = await this.getLoginUserId();\n      } catch (error) {\n        console.log('用户未登录，显示默认状态');\n      }\n\n      // 获取当前有效的优惠券 - 无论是否登录都显示\n      // 使用本地时间，兼容Windows和Linux\n      const now = this.getLocalDateTime();\n      console.log('=== 优惠券查询调试信息 ===');\n      console.log('当前时间:', now);\n      console.log('用户ID:', userId);\n\n      const whereCondition = {\n        status: 'active',\n        start_time: ['<=', now],\n        end_time: ['>=', now],\n        is_delete: 0\n      };\n      console.log('查询条件:', whereCondition);\n\n      // 先查询所有优惠券，用于调试\n      const allCoupons = await this.model('coupons').where({ is_delete: 0 }).select();\n      console.log('allCoupons查询结果类型:', typeof allCoupons);\n      console.log('allCoupons是否为数组:', Array.isArray(allCoupons));\n      console.log('allCoupons原始数据:', allCoupons);\n\n      if (Array.isArray(allCoupons)) {\n        console.log('数据库中所有优惠券:', allCoupons.map(c => ({\n          id: c.id,\n          name: c.name,\n          type: c.type,\n          status: c.status,\n          start_time: c.start_time,\n          end_time: c.end_time,\n          is_delete: c.is_delete,\n          start_time_check: c.start_time <= now,\n          end_time_check: c.end_time >= now,\n          status_check: c.status === 'active'\n        })));\n      }\n\n      let coupons = await this.model('coupons').where(whereCondition).order('CASE WHEN type = \"newuser\" THEN 0 ELSE 1 END, id ASC').select();\n      console.log('coupons查询结果类型:', typeof coupons);\n      console.log('coupons是否为数组:', Array.isArray(coupons));\n      console.log('coupons原始数据:', coupons);\n\n      // 确保coupons是数组\n      if (!Array.isArray(coupons)) {\n        console.error('coupons查询结果不是数组，设置为空数组');\n        coupons = [];\n      }\n\n      console.log('查询到的有效优惠券数量:', coupons.length);\n      if (coupons.length > 0) {\n        console.log('有效优惠券详情:', coupons.map(c => ({\n          id: c.id,\n          name: c.name,\n          type: c.type,\n          status: c.status,\n          start_time: c.start_time,\n          end_time: c.end_time,\n          is_delete: c.is_delete\n        })));\n      }\n\n      // 为每个优惠券检查用户领取状态\n      let result = [];\n      if (Array.isArray(coupons) && coupons.length > 0) {\n        for (let coupon of coupons) {\n          let canReceive = true;\n          let reason = '';\n          let receivedCount = 0;\n\n          if (userId) {\n            // 用户已登录，检查领取状态\n\n            // 检查用户对该优惠券的领取数量\n            receivedCount = await this.model('user_coupons').where({\n              user_id: userId,\n              coupon_id: coupon.id\n            }).count();\n\n            // 检查是否已达到领取上限\n            if (receivedCount >= coupon.per_user_limit) {\n              canReceive = false;\n              reason = '已领取';\n            }\n\n            // 检查新人券特殊限制\n            if (canReceive && coupon.type === 'newuser') {\n              const hasNewUserCoupon = await this.model('user_coupons').alias('uc')\n                .join('hiolabs_coupons c ON uc.coupon_id = c.id')\n                .where({\n                  'uc.user_id': userId,\n                  'c.type': 'newuser'\n                }).count();\n\n              if (hasNewUserCoupon > 0) {\n                canReceive = false;\n                reason = '已领取新人券';\n              }\n            }\n          }\n\n          result.push({\n            id: coupon.id,\n            name: coupon.name,\n            code: coupon.code,\n            type: coupon.type,\n            discount_type: coupon.discount_type,\n            discount_value: coupon.discount_value,\n            min_amount: coupon.min_amount || 0,\n            max_discount: coupon.max_discount || 0,\n            total_quantity: coupon.total_quantity,\n            per_user_limit: coupon.per_user_limit,\n            description: coupon.description || '',\n            start_time: coupon.start_time,\n            end_time: coupon.end_time,\n            canReceive: canReceive,\n            reason: reason,\n            receivedCount: receivedCount\n          });\n        }\n      }\n\n      return this.success(result);\n    } catch (error) {\n      think.logger.error('获取可领取优惠券失败:', error);\n      return this.fail('获取优惠券失败');\n    }\n  }\n\n  /**\n   * 领取优惠券\n   */\n  async receiveAction() {\n    try {\n      const userId = await this.getLoginUserId();\n      if (!userId) {\n        return this.fail('请先登录');\n      }\n\n      // 检查用户是否完善了个人信息，与订单有礼、签到功能保持一致\n      console.log('=== 开始查询用户信息 ===');\n      console.log('查询用户ID:', userId, 'type:', typeof userId);\n\n      const userInfo = await this.model('user').where({ id: userId }).find();\n      console.log('查询到的用户信息:', userInfo);\n\n      if (think.isEmpty(userInfo)) {\n        console.log('用户不存在');\n        return this.fail('用户不存在');\n      }\n\n      // 解码昵称（数据库中是Base64编码存储的）\n      let decodedNickname = '';\n      if (userInfo.nickname) {\n        try {\n          decodedNickname = Buffer.from(userInfo.nickname, 'base64').toString();\n        } catch (error) {\n          console.log('昵称解码失败，使用原始值:', userInfo.nickname);\n          decodedNickname = userInfo.nickname;\n        }\n      }\n\n      console.log('=== 用户权限验证 ===');\n      console.log('用户ID:', userId);\n      console.log('原始昵称:', userInfo.nickname);\n      console.log('解码昵称:', decodedNickname);\n      console.log('is_new_user:', userInfo.is_new_user);\n\n      // 验证用户信息完整性（根据昵称判断，不使用is_authorized字段）\n\n      console.log('=== 权限验证详情 ===');\n      console.log('nickname存在:', !!userInfo.nickname);\n      console.log('decodedNickname:', decodedNickname);\n      console.log('is_new_user:', userInfo.is_new_user);\n\n      // 检查用户是否已完成授权（基于昵称判断）\n      if (!userInfo.nickname ||\n          decodedNickname === '点我登录' ||\n          decodedNickname === '点击登录' ||\n          decodedNickname === '微信用户') {\n        console.log('用户权限验证失败，原因: 个人信息不完整');\n        return this.fail('请先完善个人信息（头像和昵称）后再领取优惠券');\n      }\n\n      console.log('用户权限验证通过');\n\n      const { couponId } = this.post();\n      console.log('=== 接收到的参数 ===');\n      console.log('couponId:', couponId, 'type:', typeof couponId);\n\n      if (!couponId) {\n        return this.fail('参数错误');\n      }\n\n      console.log('=== 查询优惠券信息 ===');\n      const coupon = await this.model('coupons').where({\n        id: couponId,\n        status: 'active',\n        is_delete: 0\n      }).find();\n      console.log('查询到的优惠券:', coupon);\n\n      if (think.isEmpty(coupon)) {\n        return this.fail('优惠券不存在或已失效');\n      }\n\n      // 检查时间有效性\n      const now = new Date();\n      if (new Date(coupon.start_time) > now || new Date(coupon.end_time) < now) {\n        return this.fail('优惠券不在有效期内');\n      }\n\n      // 检查新人券限制（每人只能领取一张新人券）\n      if (coupon.type === 'newuser') {\n        console.log('=== 检查新人券资格 ===');\n\n        // 检查用户是否已领取过任何新人券\n        const hasNewUserCoupon = await this.model('user_coupons').alias('uc')\n          .join('hiolabs_coupons c ON uc.coupon_id = c.id')\n          .where({\n            'uc.user_id': userId,\n            'c.type': 'newuser'\n          }).count();\n\n        console.log('用户已领取的新人券数量:', hasNewUserCoupon);\n\n        if (hasNewUserCoupon > 0) {\n          console.log('用户已领取过新人券，不符合新人券条件');\n          return this.fail('您已领取过新人券，每人仅限领取一张');\n        }\n        console.log('用户未领取过新人券，符合新人券条件');\n      }\n\n      // 检查用户领取数量限制\n      console.log('=== 检查用户领取数量限制 ===');\n      console.log('查询条件 - user_id:', userId, 'coupon_id:', couponId);\n      const receivedCount = await this.model('user_coupons').where({\n        user_id: userId,\n        coupon_id: couponId\n      }).count();\n      console.log('用户已领取数量:', receivedCount);\n\n      if (receivedCount >= coupon.per_user_limit) {\n        return this.fail('您已达到该优惠券的领取上限');\n      }\n\n      // 检查总量限制\n      if (coupon.total_quantity > 0) {\n        const totalReceived = await this.model('user_coupons').where({\n          coupon_id: couponId\n        }).count();\n\n        if (totalReceived >= coupon.total_quantity) {\n          return this.fail('优惠券已被领完');\n        }\n      }\n\n      // 生成用户优惠券\n      const couponCode = this.generateCouponCode();\n      const expireAt = coupon.valid_days ?\n        new Date(Date.now() + coupon.valid_days * 24 * 60 * 60 * 1000) :\n        new Date(coupon.end_time);\n\n      console.log('=== 准备插入用户优惠券数据 ===');\n      console.log('用户ID:', userId);\n      console.log('优惠券ID:', couponId);\n      console.log('优惠券代码:', couponCode);\n      console.log('过期时间:', expireAt);\n      console.log('优惠券信息:', coupon);\n\n      await this.model('user_coupons').add({\n        user_id: userId,\n        coupon_id: couponId,\n        coupon_code: couponCode,\n        expire_at: expireAt,\n        source: 'manual'\n      });\n\n      return this.success('领取成功');\n    } catch (error) {\n      think.logger.error('领取优惠券失败:', error);\n      console.error('=== 优惠券领取详细错误信息 ===');\n      console.error('错误消息:', error.message);\n      console.error('错误堆栈:', error.stack);\n      console.error('错误代码:', error.code);\n      console.error('SQL错误:', error.sql);\n      console.error('================================');\n      return this.fail('领取失败，请稍后重试');\n    }\n  }\n\n  /**\n   * 获取用户的优惠券列表\n   */\n  async myAction() {\n    try {\n      const userId = await this.getLoginUserId();\n      if (!userId) {\n        return this.fail('请先登录');\n      }\n\n      const { status = 'unused' } = this.get();\n\n      const userCoupons = await this.model('user_coupons').alias('uc')\n        .join('hiolabs_coupons c ON uc.coupon_id = c.id')\n        .where({\n          'uc.user_id': userId,\n          'uc.status': status,\n          'c.is_delete': 0\n        })\n        .field('uc.*, c.name, c.type, c.discount_type, c.discount_value, c.min_amount, c.max_discount, c.description')\n        .order('uc.received_at DESC')\n        .select();\n\n      // 检查过期状态\n      const now = new Date();\n      for (let userCoupon of userCoupons) {\n        if (userCoupon.status === 'unused' && new Date(userCoupon.expire_at) < now) {\n          // 更新为过期状态\n          await this.model('user_coupons').where({id: userCoupon.id}).update({\n            status: 'expired'\n          });\n          userCoupon.status = 'expired';\n        }\n      }\n\n      // 按状态分组\n      const result = {\n        unused: [],\n        used: [],\n        expired: []\n      };\n\n      userCoupons.forEach(coupon => {\n        if (result[coupon.status]) {\n          result[coupon.status].push(coupon);\n        }\n      });\n\n      return this.success(result);\n    } catch (error) {\n      think.logger.error('获取用户优惠券失败:', error);\n      return this.fail('获取优惠券失败');\n    }\n  }\n\n  /**\n   * 获取订单可用的优惠券\n   */\n  async availableForOrderAction() {\n    try {\n      const userId = await this.getLoginUserId();\n      if (!userId) {\n        return this.fail('请先登录');\n      }\n\n      const { amount } = this.get();\n      if (!amount || amount <= 0) {\n        return this.fail('订单金额参数错误');\n      }\n\n      const userCoupons = await this.model('user_coupons').alias('uc')\n        .join('hiolabs_coupons c ON uc.coupon_id = c.id')\n        .where({\n          'uc.user_id': userId,\n          'uc.status': 'unused',\n          'uc.expire_at': ['>=', this.getLocalDateTime()],\n          'c.min_amount': ['<=', amount],\n          'c.is_delete': 0\n        })\n        .field('uc.*, c.name, c.type, c.discount_type, c.discount_value, c.min_amount, c.max_discount, c.description')\n        .order('c.discount_value DESC')\n        .select();\n\n      // 计算每个优惠券的优惠金额\n      userCoupons.forEach(coupon => {\n        coupon.calculatedDiscount = this.calculateDiscount(coupon, amount);\n      });\n\n      return this.success(userCoupons);\n    } catch (error) {\n      think.logger.error('获取订单可用优惠券失败:', error);\n      return this.fail('获取可用优惠券失败');\n    }\n  }\n\n  /**\n   * 自动选择最优优惠券\n   */\n  async autoSelectBestAction() {\n    try {\n      const userId = await this.getLoginUserId();\n      if (!userId) {\n        return this.fail('请先登录');\n      }\n\n      const { amount } = this.post();\n      if (!amount || amount <= 0) {\n        return this.fail('订单金额参数错误');\n      }\n\n      const availableCoupons = await this.getAvailableCouponsForAmount(userId, amount);\n\n      let bestCoupon = null;\n      let maxDiscount = 0;\n\n      for (let coupon of availableCoupons) {\n        const discount = this.calculateDiscount(coupon, amount);\n        if (discount > maxDiscount) {\n          maxDiscount = discount;\n          bestCoupon = coupon;\n        }\n      }\n\n      return this.success({\n        coupon: bestCoupon,\n        discount: maxDiscount,\n        finalAmount: amount - maxDiscount\n      });\n    } catch (error) {\n      think.logger.error('自动选择优惠券失败:', error);\n      return this.fail('自动选择失败');\n    }\n  }\n\n  /**\n   * 计算优惠金额\n   */\n  calculateDiscount(coupon, amount) {\n    if (coupon.discount_type === 'fixed') {\n      return Math.min(coupon.discount_value, amount);\n    } else {\n      const discount = amount * (coupon.discount_value / 100);\n      return Math.min(discount, coupon.max_discount || discount);\n    }\n  }\n\n  /**\n   * 获取指定金额可用的优惠券\n   */\n  async getAvailableCouponsForAmount(userId, amount) {\n    return await this.model('user_coupons').alias('uc')\n      .join('hiolabs_coupons c ON uc.coupon_id = c.id')\n      .where({\n        'uc.user_id': userId,\n        'uc.status': 'unused',\n        'uc.expire_at': ['>=', this.getLocalDateTime()],\n        'c.min_amount': ['<=', amount],\n        'c.is_delete': 0\n      })\n      .field('uc.*, c.*')\n      .select();\n  }\n\n  /**\n   * 管理端 - 获取优惠券列表\n   */\n  async indexAction() {\n    try {\n      const { page = 1, size = 20, type, status, keyword } = this.get();\n\n      let where = { is_delete: 0 };\n\n      if (type) {\n        where.type = type;\n      }\n\n      if (status) {\n        where.status = status;\n      }\n\n      if (keyword) {\n        where._complex = {\n          name: ['LIKE', `%${keyword}%`],\n          code: ['LIKE', `%${keyword}%`],\n          _logic: 'OR'\n        };\n      }\n\n      const coupons = await this.model('coupons')\n        .where(where)\n        .order('created_at DESC')\n        .page(page, size)\n        .countSelect();\n\n      // 为每个优惠券添加统计信息\n      for (let coupon of coupons.data) {\n        coupon.receivedCount = await this.model('user_coupons').where({\n          coupon_id: coupon.id\n        }).count();\n\n        coupon.usedCount = await this.model('user_coupons').where({\n          coupon_id: coupon.id,\n          status: 'used'\n        }).count();\n      }\n\n      return this.success(coupons);\n    } catch (error) {\n      think.logger.error('获取优惠券列表失败:', error);\n      return this.fail('获取列表失败');\n    }\n  }\n\n  /**\n   * 管理端 - 创建优惠券\n   */\n  async addAction() {\n    try {\n      const data = this.post();\n\n      // 验证必填字段\n      if (!data.name || !data.type || !data.discount_type || !data.discount_value) {\n        return this.fail('请填写完整的优惠券信息');\n      }\n\n      // 验证时间\n      if (!data.start_time || !data.end_time) {\n        return this.fail('请设置有效期');\n      }\n\n      if (new Date(data.start_time) >= new Date(data.end_time)) {\n        return this.fail('开始时间必须早于结束时间');\n      }\n\n      // 生成代码（如果没有提供）\n      if (!data.code) {\n        data.code = this.generateCouponCode();\n      } else {\n        // 检查代码是否已存在\n        const existingCoupon = await this.model('coupons').where({\n          code: data.code,\n          is_delete: 0\n        }).find();\n\n        if (!think.isEmpty(existingCoupon)) {\n          return this.fail('优惠券代码已存在');\n        }\n      }\n\n      // 设置默认值\n      data.status = data.status || 'active';\n      data.total_quantity = data.total_quantity || -1;\n      data.per_user_limit = data.per_user_limit || 1;\n      data.auto_distribute = data.auto_distribute ? 1 : 0;\n\n      const couponId = await this.model('coupons').add(data);\n\n      return this.success({ id: couponId, message: '创建成功' });\n    } catch (error) {\n      think.logger.error('创建优惠券失败:', error);\n      return this.fail('创建失败');\n    }\n  }\n\n  /**\n   * 管理端 - 切换优惠券状态\n   */\n  async toggleStatusAction() {\n    try {\n      const { id, status } = this.post();\n\n      if (!id || !status) {\n        return this.fail('参数错误');\n      }\n\n      if (!['active', 'disabled'].includes(status)) {\n        return this.fail('状态参数错误');\n      }\n\n      const coupon = await this.model('coupons').where({\n        id: id,\n        is_delete: 0\n      }).find();\n\n      if (think.isEmpty(coupon)) {\n        return this.fail('优惠券不存在');\n      }\n\n      await this.model('coupons').where({ id: id }).update({\n        status: status,\n        updated_at: new Date()\n      });\n\n      return this.success('状态更新成功');\n    } catch (error) {\n      think.logger.error('切换优惠券状态失败:', error);\n      return this.fail('操作失败');\n    }\n  }\n\n  /**\n   * 管理端 - 删除优惠券\n   */\n  async deleteAction() {\n    try {\n      const { id } = this.post();\n\n      if (!id) {\n        return this.fail('参数错误');\n      }\n\n      const coupon = await this.model('coupons').where({\n        id: id,\n        is_delete: 0\n      }).find();\n\n      if (think.isEmpty(coupon)) {\n        return this.fail('优惠券不存在');\n      }\n\n      // 检查是否有用户已领取\n      const receivedCount = await this.model('user_coupons').where({\n        coupon_id: id\n      }).count();\n\n      if (receivedCount > 0) {\n        return this.fail('该优惠券已有用户领取，无法删除');\n      }\n\n      await this.model('coupons').where({ id: id }).update({\n        is_delete: 1,\n        updated_at: new Date()\n      });\n\n      return this.success('删除成功');\n    } catch (error) {\n      think.logger.error('删除优惠券失败:', error);\n      return this.fail('删除失败');\n    }\n  }\n\n  /**\n   * 管理端 - 获取统计数据\n   */\n  async statisticsAction() {\n    try {\n      const total = await this.model('coupons').where({ is_delete: 0 }).count();\n      const active = await this.model('coupons').where({\n        is_delete: 0,\n        status: 'active'\n      }).count();\n      const received = await this.model('user_coupons').count();\n      const used = await this.model('user_coupons').where({\n        status: 'used'\n      }).count();\n\n      return this.success({\n        total,\n        active,\n        received,\n        used\n      });\n    } catch (error) {\n      think.logger.error('获取统计数据失败:', error);\n      return this.fail('获取统计失败');\n    }\n  }\n\n  /**\n   * 生成优惠券码\n   */\n  generateCouponCode() {\n    const timestamp = Date.now().toString(36);\n    const random = Math.random().toString(36).substr(2, 5);\n    return `CPN${timestamp}${random}`.toUpperCase();\n  }\n\n  /**\n   * 获取过期时间字段名（兼容不同的数据库表结构）\n   */\n  async getExpireFieldName() {\n    try {\n      // 尝试查询一条记录来确定字段名\n      const testRecord = await this.model('user_coupons').limit(1).find();\n      if (testRecord && testRecord.hasOwnProperty('expire_at')) {\n        return 'expire_at';\n      } else if (testRecord && testRecord.hasOwnProperty('expired_at')) {\n        return 'expired_at';\n      } else {\n        // 默认使用 expire_at\n        return 'expire_at';\n      }\n    } catch (error) {\n      console.log('检测过期字段名失败，使用默认值 expire_at:', error.message);\n      return 'expire_at';\n    }\n  }\n};\n"]}