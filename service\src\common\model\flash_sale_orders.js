module.exports = class extends think.Model {
  
  /**
   * 获取表名
   */
  get tableName() {
    return 'hiolabs_flash_sale_orders';
  }
  
  /**
   * 记录秒杀订单
   */
  async recordFlashSaleOrder(flashSaleId, orderId, userId, goodsId, quantity, flashPrice) {
    const totalAmount = quantity * flashPrice;
    
    return await this.add({
      flash_sale_id: flashSaleId,
      order_id: orderId,
      user_id: userId,
      goods_id: goodsId,
      quantity: quantity,
      flash_price: flashPrice,
      total_amount: totalAmount
    });
  }
  
  /**
   * 获取用户在某个秒杀活动中的购买数量
   */
  async getUserPurchaseCount(flashSaleId, userId) {
    const result = await this.where({
      flash_sale_id: flashSaleId,
      user_id: userId
    }).sum('quantity');
    
    return result || 0;
  }
  
  /**
   * 获取秒杀活动的订单统计
   */
  async getFlashSaleOrderStats(flashSaleId) {
    const orderCount = await this.where({
      flash_sale_id: flashSaleId
    }).count();
    
    const totalQuantity = await this.where({
      flash_sale_id: flashSaleId
    }).sum('quantity');
    
    const totalAmount = await this.where({
      flash_sale_id: flashSaleId
    }).sum('total_amount');
    
    return {
      orderCount: orderCount || 0,
      totalQuantity: totalQuantity || 0,
      totalAmount: totalAmount || 0
    };
  }
  
  /**
   * 获取用户的秒杀订单历史
   */
  async getUserFlashSaleOrders(userId, page = 1, size = 10) {
    const offset = (page - 1) * size;
    
    return await this.where({
      user_id: userId
    }).order('created_at DESC').limit(offset, size).select();
  }
};
