const mysql = require('mysql2/promise');

async function createTables() {
  try {
    const connection = await mysql.createConnection({
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: '19841020',
      database: 'hiolabsDB'
    });

    console.log('✅ 数据库连接成功');

    // 创建轮次表
    const createRoundsSQL = `
CREATE TABLE IF NOT EXISTS hiolabs_flash_sale_rounds (
  id int(11) NOT NULL AUTO_INCREMENT COMMENT '轮次ID',
  round_number int(11) NOT NULL COMMENT '轮次编号（全局递增）',
  goods_id int(11) NOT NULL COMMENT '参与秒杀的商品ID',
  goods_name varchar(200) NOT NULL COMMENT '商品名称（冗余字段）',
  goods_image varchar(500) DEFAULT '' COMMENT '商品图片',
  original_price decimal(10,2) NOT NULL COMMENT '原价',
  flash_price decimal(10,2) NOT NULL COMMENT '秒杀价格',
  stock int(11) NOT NULL COMMENT '本轮库存数量',
  sold_count int(11) DEFAULT 0 COMMENT '已售数量',
  limit_quantity int(11) DEFAULT 1 COMMENT '限购数量',
  start_time datetime NOT NULL COMMENT '开始时间',
  end_time datetime NOT NULL COMMENT '结束时间',
  status enum('upcoming','active','ended','cancelled') DEFAULT 'upcoming' COMMENT '状态',
  created_by int(11) DEFAULT 0 COMMENT '创建者ID',
  created_at datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  KEY idx_round_number (round_number),
  KEY idx_goods_id (goods_id),
  KEY idx_status (status),
  KEY idx_start_time (start_time),
  KEY idx_end_time (end_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀轮次表';
`;

    await connection.execute(createRoundsSQL);
    console.log('✅ 轮次表创建成功');

    // 创建订单表
    const createOrdersSQL = `
CREATE TABLE IF NOT EXISTS hiolabs_flash_sale_orders (
  id int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  round_id int(11) NOT NULL COMMENT '轮次ID',
  user_id int(11) NOT NULL COMMENT '用户ID',
  goods_id int(11) NOT NULL COMMENT '商品ID',
  quantity int(11) NOT NULL COMMENT '购买数量',
  unit_price decimal(10,2) NOT NULL COMMENT '单价',
  total_amount decimal(10,2) NOT NULL COMMENT '总金额',
  order_status enum('pending','paid','cancelled','refunded') DEFAULT 'pending' COMMENT '订单状态',
  payment_time datetime NULL COMMENT '支付时间',
  created_at datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  KEY idx_round_id (round_id),
  KEY idx_user_id (user_id),
  KEY idx_goods_id (goods_id),
  KEY idx_order_status (order_status),
  KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀订单表';
`;

    await connection.execute(createOrdersSQL);
    console.log('✅ 订单表创建成功');

    // 创建配置表
    const createConfigSQL = `
CREATE TABLE IF NOT EXISTS hiolabs_flash_sale_config (
  id int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  round_duration int(11) NOT NULL DEFAULT 300 COMMENT '每轮时长（秒）',
  break_duration int(11) NOT NULL DEFAULT 120 COMMENT '轮次间隔（秒）',
  auto_start_next tinyint(1) DEFAULT 1 COMMENT '是否自动开始下一轮',
  daily_start_time time NOT NULL DEFAULT '09:00:00' COMMENT '每日开始时间',
  daily_end_time time NOT NULL DEFAULT '22:00:00' COMMENT '每日结束时间',
  is_enabled tinyint(1) DEFAULT 1 COMMENT '是否启用',
  max_rounds_per_day int(11) DEFAULT 100 COMMENT '每日最大轮次数',
  created_at datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀系统配置表';
`;

    await connection.execute(createConfigSQL);
    console.log('✅ 配置表创建成功');

    // 插入默认配置
    const insertConfigSQL = `
INSERT INTO hiolabs_flash_sale_config (id, round_duration, break_duration, auto_start_next, daily_start_time, daily_end_time, is_enabled, max_rounds_per_day)
VALUES (1, 300, 120, 1, '09:00:00', '22:00:00', 1, 100)
ON DUPLICATE KEY UPDATE
round_duration = VALUES(round_duration),
break_duration = VALUES(break_duration),
auto_start_next = VALUES(auto_start_next),
daily_start_time = VALUES(daily_start_time),
daily_end_time = VALUES(daily_end_time),
is_enabled = VALUES(is_enabled),
max_rounds_per_day = VALUES(max_rounds_per_day);
`;

    await connection.execute(insertConfigSQL);
    console.log('✅ 默认配置插入成功');

    await connection.end();
    console.log('✅ 所有表创建完成');
    
  } catch (error) {
    console.error('❌ 创建失败:', error.message);
    process.exit(1);
  }
}

createTables();
