var util = require('../../../utils/util.js');
var api = require('../../../config/api.js');

Page({
    data: {
        applyId: 0,
        orderId: 0,
        refundInfo: {},
        orderInfo: {},
        orderGoods: [],
        logisticsCompany: '',
        logisticsNo: '',
        statusText: {
            'pending': '待处理',
            'processing': '处理中',
            'approved': '已同意',
            'rejected': '已拒绝',
            'wait_return': '等待退货',
            'returned': '已退货',
            'completed': '已完成'
        },
        statusColor: {
            'pending': '#ff9500',
            'processing': '#007aff',
            'approved': '#4cd964',
            'rejected': '#ff3b30',
            'wait_return': '#8b5cf6',
            'returned': '#10b981',
            'completed': '#8e8e93'
        }
    },

    onLoad: function (options) {
        if (options.applyId) {
            this.setData({
                applyId: options.applyId,
                orderId: options.orderId || 0
            });
            this.getRefundDetail();
        }
    },

    // 获取售后详情
    getRefundDetail: function () {
        let that = this;
        util.request(api.RefundDetail, {
            applyId: that.data.applyId
        }).then(function (res) {
            if (res.errno === 0) {
                that.setData({
                    refundInfo: res.data.refundInfo,
                    orderInfo: res.data.orderInfo,
                    orderGoods: res.data.orderGoods
                });
            } else {
                wx.showToast({
                    title: res.errmsg || '获取详情失败',
                    icon: 'none'
                });
            }
        }).catch(function (err) {
            wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
            });
        });
    },

    // 预览图片
    previewImage: function (e) {
        const current = e.currentTarget.dataset.src;
        const images = this.data.refundInfo.images || [];
        if (images.length > 0) {
            wx.previewImage({
                current: current,
                urls: images
            });
        }
    },

    // 物流公司输入
    onLogisticsCompanyInput: function (e) {
        this.setData({
            logisticsCompany: e.detail.value
        });
    },

    // 物流单号输入
    onLogisticsNoInput: function (e) {
        this.setData({
            logisticsNo: e.detail.value
        });
    },

    // 提交物流信息
    submitLogistics: function () {
        const { logisticsCompany, logisticsNo, applyId } = this.data;

        if (!logisticsCompany.trim()) {
            wx.showToast({
                title: '请输入物流公司',
                icon: 'none'
            });
            return;
        }

        if (!logisticsNo.trim()) {
            wx.showToast({
                title: '请输入物流单号',
                icon: 'none'
            });
            return;
        }

        wx.showLoading({
            title: '提交中...'
        });

        util.request(api.SubmitReturnLogistics, {
            applyId: applyId,
            logisticsCompany: logisticsCompany.trim(),
            logisticsNo: logisticsNo.trim()
        }).then((res) => {
            wx.hideLoading();
            if (res.errno === 0) {
                wx.showToast({
                    title: '提交成功',
                    icon: 'success'
                });
                // 重新获取详情
                this.getRefundDetail();
            } else {
                wx.showToast({
                    title: res.errmsg || '提交失败',
                    icon: 'none'
                });
            }
        }).catch((err) => {
            wx.hideLoading();
            wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
            });
        });
    },

    // 联系客服
    contactService: function () {
        wx.showModal({
            title: '联系客服',
            content: '如有疑问，请联系客服处理',
            showCancel: false
        });
    },

    // 撤销申请（仅待处理状态可撤销）
    cancelRefund: function () {
        const that = this;
        const refundInfo = this.data.refundInfo;
        
        if (refundInfo.status !== 'pending') {
            wx.showToast({
                title: '当前状态不支持撤销',
                icon: 'none'
            });
            return;
        }

        wx.showModal({
            title: '确认撤销',
            content: '确定要撤销售后申请吗？撤销后无法恢复',
            success: function (res) {
                if (res.confirm) {
                    util.request(api.RefundCancel, {
                        applyId: that.data.applyId
                    }, 'POST').then(function (res) {
                        if (res.errno === 0) {
                            wx.showToast({
                                title: '撤销成功',
                                icon: 'success'
                            });
                            setTimeout(() => {
                                wx.navigateBack();
                            }, 1500);
                        } else {
                            wx.showToast({
                                title: res.errmsg || '撤销失败',
                                icon: 'none'
                            });
                        }
                    });
                }
            }
        });
    },

    // 返回订单详情
    backToOrder: function () {
        if (this.data.orderId) {
            wx.redirectTo({
                url: '/pages/ucenter/order-details/index?id=' + this.data.orderId
            });
        } else {
            wx.navigateBack();
        }
    }
});
