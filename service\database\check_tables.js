const mysql = require('mysql2/promise');

async function checkTables() {
  try {
    const connection = await mysql.createConnection({
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: '19841020',
      database: 'hiolabsDB'
    });

    console.log('✅ 数据库连接成功');

    const [tables] = await connection.execute("SHOW TABLES LIKE 'hiolabs_flash_sale_%'");
    
    console.log('📋 秒杀相关表列表:');
    tables.forEach(table => {
      console.log('  -', Object.values(table)[0]);
    });

    // 检查轮次表结构
    try {
      const [rounds] = await connection.execute('SELECT COUNT(*) as count FROM hiolabs_flash_sale_rounds');
      console.log('✅ 轮次表存在，记录数:', rounds[0].count);
    } catch (error) {
      console.log('❌ 轮次表不存在:', error.message);
    }

    // 检查轮次商品表结构
    try {
      const [roundGoods] = await connection.execute('SELECT COUNT(*) as count FROM hiolabs_flash_sale_round_goods');
      console.log('✅ 轮次商品表存在，记录数:', roundGoods[0].count);
    } catch (error) {
      console.log('❌ 轮次商品表不存在:', error.message);
    }

    // 检查配置表结构
    try {
      const [config] = await connection.execute('SELECT COUNT(*) as count FROM hiolabs_flash_sale_config');
      console.log('✅ 配置表存在，记录数:', config[0].count);
    } catch (error) {
      console.log('❌ 配置表不存在:', error.message);
    }

    await connection.end();
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  }
}

checkTables();
