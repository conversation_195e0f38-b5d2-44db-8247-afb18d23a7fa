{"version": 3, "sources": ["..\\..\\..\\src\\common\\model\\flash_sale_orders.js"], "names": ["module", "exports", "think", "Model", "tableName", "recordFlashSaleOrder", "flashSaleId", "orderId", "userId", "goodsId", "quantity", "flashPrice", "totalAmount", "add", "flash_sale_id", "order_id", "user_id", "goods_id", "flash_price", "total_amount", "getUserPurchaseCount", "result", "where", "sum", "getFlashSaleOrderStats", "orderCount", "count", "totalQuantity", "getUserFlashSaleOrders", "page", "size", "offset", "order", "limit", "select"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;;AAEzC;;;AAGA,MAAIC,SAAJ,GAAgB;AACd,WAAO,2BAAP;AACD;;AAED;;;AAGMC,sBAAN,CAA2BC,WAA3B,EAAwCC,OAAxC,EAAiDC,MAAjD,EAAyDC,OAAzD,EAAkEC,QAAlE,EAA4EC,UAA5E,EAAwF;AAAA;;AAAA;AACtF,YAAMC,cAAcF,WAAWC,UAA/B;;AAEA,aAAO,MAAM,MAAKE,GAAL,CAAS;AACpBC,uBAAeR,WADK;AAEpBS,kBAAUR,OAFU;AAGpBS,iBAASR,MAHW;AAIpBS,kBAAUR,OAJU;AAKpBC,kBAAUA,QALU;AAMpBQ,qBAAaP,UANO;AAOpBQ,sBAAcP;AAPM,OAAT,CAAb;AAHsF;AAYvF;;AAED;;;AAGMQ,sBAAN,CAA2Bd,WAA3B,EAAwCE,MAAxC,EAAgD;AAAA;;AAAA;AAC9C,YAAMa,SAAS,MAAM,OAAKC,KAAL,CAAW;AAC9BR,uBAAeR,WADe;AAE9BU,iBAASR;AAFqB,OAAX,EAGlBe,GAHkB,CAGd,UAHc,CAArB;;AAKA,aAAOF,UAAU,CAAjB;AAN8C;AAO/C;;AAED;;;AAGMG,wBAAN,CAA6BlB,WAA7B,EAA0C;AAAA;;AAAA;AACxC,YAAMmB,aAAa,MAAM,OAAKH,KAAL,CAAW;AAClCR,uBAAeR;AADmB,OAAX,EAEtBoB,KAFsB,EAAzB;;AAIA,YAAMC,gBAAgB,MAAM,OAAKL,KAAL,CAAW;AACrCR,uBAAeR;AADsB,OAAX,EAEzBiB,GAFyB,CAErB,UAFqB,CAA5B;;AAIA,YAAMX,cAAc,MAAM,OAAKU,KAAL,CAAW;AACnCR,uBAAeR;AADoB,OAAX,EAEvBiB,GAFuB,CAEnB,cAFmB,CAA1B;;AAIA,aAAO;AACLE,oBAAYA,cAAc,CADrB;AAELE,uBAAeA,iBAAiB,CAF3B;AAGLf,qBAAaA,eAAe;AAHvB,OAAP;AAbwC;AAkBzC;;AAED;;;AAGMgB,wBAAN,CAA6BpB,MAA7B,EAAqCqB,OAAO,CAA5C,EAA+CC,OAAO,EAAtD,EAA0D;AAAA;;AAAA;AACxD,YAAMC,SAAS,CAACF,OAAO,CAAR,IAAaC,IAA5B;;AAEA,aAAO,MAAM,OAAKR,KAAL,CAAW;AACtBN,iBAASR;AADa,OAAX,EAEVwB,KAFU,CAEJ,iBAFI,EAEeC,KAFf,CAEqBF,MAFrB,EAE6BD,IAF7B,EAEmCI,MAFnC,EAAb;AAHwD;AAMzD;AAtEwC,CAA3C", "file": "..\\..\\..\\src\\common\\model\\flash_sale_orders.js", "sourcesContent": ["module.exports = class extends think.Model {\n  \n  /**\n   * 获取表名\n   */\n  get tableName() {\n    return 'hiolabs_flash_sale_orders';\n  }\n  \n  /**\n   * 记录秒杀订单\n   */\n  async recordFlashSaleOrder(flashSaleId, orderId, userId, goodsId, quantity, flashPrice) {\n    const totalAmount = quantity * flashPrice;\n    \n    return await this.add({\n      flash_sale_id: flashSaleId,\n      order_id: orderId,\n      user_id: userId,\n      goods_id: goodsId,\n      quantity: quantity,\n      flash_price: flashPrice,\n      total_amount: totalAmount\n    });\n  }\n  \n  /**\n   * 获取用户在某个秒杀活动中的购买数量\n   */\n  async getUserPurchaseCount(flashSaleId, userId) {\n    const result = await this.where({\n      flash_sale_id: flashSaleId,\n      user_id: userId\n    }).sum('quantity');\n    \n    return result || 0;\n  }\n  \n  /**\n   * 获取秒杀活动的订单统计\n   */\n  async getFlashSaleOrderStats(flashSaleId) {\n    const orderCount = await this.where({\n      flash_sale_id: flashSaleId\n    }).count();\n    \n    const totalQuantity = await this.where({\n      flash_sale_id: flashSaleId\n    }).sum('quantity');\n    \n    const totalAmount = await this.where({\n      flash_sale_id: flashSaleId\n    }).sum('total_amount');\n    \n    return {\n      orderCount: orderCount || 0,\n      totalQuantity: totalQuantity || 0,\n      totalAmount: totalAmount || 0\n    };\n  }\n  \n  /**\n   * 获取用户的秒杀订单历史\n   */\n  async getUserFlashSaleOrders(userId, page = 1, size = 10) {\n    const offset = (page - 1) * size;\n    \n    return await this.where({\n      user_id: userId\n    }).order('created_at DESC').limit(offset, size).select();\n  }\n};\n"]}