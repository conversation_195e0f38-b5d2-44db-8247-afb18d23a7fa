-- 二维码登录相关数据表
-- 执行此SQL文件来创建二维码登录功能所需的数据表

USE hiolabsDB;

-- 二维码登录token表
CREATE TABLE IF NOT EXISTS `hiolabs_qr_login_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `token` varchar(64) NOT NULL COMMENT '二维码token',
  `status` enum('pending','success','expired') DEFAULT 'pending' COMMENT '状态：pending等待扫码，success登录成功，expired已过期',
  `user_id` int(11) DEFAULT NULL COMMENT '扫码用户ID',
  `user_type` enum('promoter','distributor') DEFAULT NULL COMMENT '用户类型：promoter推广员，distributor分销商',
  `create_time` int(11) NOT NULL COMMENT '创建时间戳',
  `expire_time` int(11) NOT NULL COMMENT '过期时间戳',
  `scan_time` int(11) DEFAULT NULL COMMENT '扫码时间戳',
  `login_time` int(11) DEFAULT NULL COMMENT '登录时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `token` (`token`),
  KEY `status` (`status`),
  KEY `expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='二维码登录token表';

-- 清理过期token的存储过程（可选）
DELIMITER $$
CREATE PROCEDURE CleanExpiredQrTokens()
BEGIN
    DELETE FROM hiolabs_qr_login_tokens 
    WHERE expire_time < UNIX_TIMESTAMP() 
    AND status != 'success';
END$$
DELIMITER ;
