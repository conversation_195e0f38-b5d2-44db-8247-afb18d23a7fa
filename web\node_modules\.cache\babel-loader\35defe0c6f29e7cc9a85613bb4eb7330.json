{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js!D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleMultiPage.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleMultiPage.vue", "mtime": 1753847480671}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\babel.config.js", "mtime": 1717470108000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyXA;EACAA;EACAC;IACA;MACAC;MACAC;QAAAC;QAAAC;MAAA;MACAC;QAAAL;QAAAM;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;QACAf;QACAgB;MACA;IACA;EACA;EAEAC;IACAC;MACA;MACA;MACA;MACA;QAAA;MAAA;MACA;MAEAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACApB;MACA;MAEA;IACA;IAEA;IACAqB;MACA;QACA;MACA;MAEA;MACA;MAEA;QACA;QACA;UACAC;UACAC;QACA;QACA;MACA;QACA;QACA;UACAD;UACAC;QACA;QACA;UACAD;UACAC;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MAEA;MACA;MACA;;MAEA;MACAC;MAEA;MACAC;;MAEA;QACA;UACA;UACAC;UAEA;UACAC;;UAEA;UACA;UAEAC;YACAC;YACAC;YACAC;YACAC;UACA;QACA;;QAEA;QACAP;MACA;MAEA;IACA;IAEA;IACAQ;MACA;MACA;QAAA;MAAA;IACA;EACA;EAEAC;IACApB;IACAA;IACA;IACA;EACA;EAEAqB;IACA;MACAC;IACA;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACAC,aACA,wBACA,2BACA,wBACA,sBACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cAAAC;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEA3B;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA4B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cAAAD;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEA3B;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA6B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cAAAF;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEA3B;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA8B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAEA;cACA9B;cAAA;cAAA,OAEA;YAAA;cAAA2B;cACA3B;cAEA;gBACA;gBACAA;gBAEA;kBACA;gBACA;cACA;gBACAA;gBACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAA;cACA;cACA;YAAA;cAAA;cAEA;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA+B;MAAA;MACA;QACA;QACA;MACA;IACA;IAEAC;MACA;QAAA;MAAA;IACA;IAEAC;MACA;QAAA;MAAA;IACA;IAEAC;MACA;MAEA;QACA;MACA;QACA;QACA;QACA;QAEA;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;IACA;IAEAC;MACA;QAAA;MAAA;MACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;QACA;QACA;QACAC;MACA;IACA;IAEAC;MACA;MACAC;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACAjD;MACA;MACA;MACA;MACA;MACA;MACAA;IACA;IAEAkD;MACA;MACA;MACA;QACAC;QACA5C;QACAC;QACA4C;QACAC;MACA;IACA;IAEAC;MACA;MACA;MACA;QACA/C;QACAC;QACA4C;QACAC;MACA;IACA;IAEAE;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA,IACA;gBAAA;gBAAA;cAAA;cACA;cAAA;YAAA;cAIA;cACAC;cACAX,kBAEA;cACAY;gBAAA;cAAA;cAAA,MAEAA;gBAAA;gBAAA;cAAA;cACA;cAAA;YAAA;cAAA,MAKAA;gBAAA;gBAAA;cAAA;cACAC;cAAA,IACAA;gBAAA;gBAAA;cAAA;cAAA;YAAA;cAAA;cAMA;cACA;cACA;cAEAC;cACAC;cACAC,oBAEA;cACAC;cAAA;gBAAA;gBAAA;kBAAA;oBAAA;sBAEAC;sBACAC,gDAEA;sBACAC;wBAAA;0BAAA;0BAAA;4BAAA;8BAAA;gCACAC;gCAAA;gCAGAC;kCACAC;kCACAC;kCACAC;kCACAC;kCACAC;kCACAC;kCACAjF;oCAAA;sCACAyC;sCACAC;sCACAwC;sCACAvC;sCACAC;sCACAE;sCACAD;oCACA;kCAAA;gCACA;gCAAA;gCAAA,OAEA;8BAAA;gCAAAZ;gCAAA,MAEAA;kCAAA;kCAAA;gCAAA;gCAAA,kCACA;kCAAAkD;kCAAAC;gCAAA;8BAAA;gCAAA,kCAEA;kCAAAD;kCAAAC;kCAAAC;gCAAA;8BAAA;gCAAA;gCAAA;8BAAA;gCAAA;gCAAA;gCAAA,kCAGA;kCAAAF;kCAAAC;kCAAAC;gCAAA;8BAAA;8BAAA;gCAAA;4BAAA;0BAAA;wBAAA,CAEA;wBAAA;0BAAA;wBAAA;sBAAA,MAEA;sBAAA;sBAAA,OACAtD;oBAAA;sBAAAuD;sBAEA;sBACAA;wBACA;wBACA;0BACAnB;wBACA;0BACAC;0BACA;4BAAA;4BACAC;0BACA;wBACA;sBACA;;sBAEA;sBAAA,MACAE;wBAAA;wBAAA;sBAAA;sBAAA;sBAAA,OACA;wBAAA;sBAAA;oBAAA;oBAAA;sBAAA;kBAAA;gBAAA;cAAA;cAzDAgB;YAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;YAAA;cAAAA;cAAA;cAAA;YAAA;cA6DA;cACA;gBACAC;gBACA;kBACAA;kBACA;oBACAlF;oBACAkF;kBACA;gBACA;gBACA;gBACA;gBACA;cACA;gBACAC;gBACA;kBACAA;gBACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAGAnF;cACA;YAAA;cAAA;cAEA;cACA;cACA;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEAoF;MACA;MACA;QACA5F;QACAC;QACAC;MACA;IACA;IAEA2F;MACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA,IACAC;gBAAA;gBAAA;cAAA;cAAA;YAAA;cAAA;cAKA;cAAA;cAAA,OACA;gBACAC;cACA;YAAA;cAFA7D;cAIA;gBACA;gBACA;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEA3B;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEAyF;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA,IACAF;gBAAA;gBAAA;cAAA;cAAA;YAAA;cAAA;cAKA;cAAA;cAAA,OACA;gBACAC;cACA;YAAA;cAFA7D;cAIA;gBACA;gBACA;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEA3B;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA0F;MACA;MACA;MACA;MACA;MACA;IACA;EAAA,uEAEAC;IACA;IACA;EACA,sEAEAC;IACA;MACA;MACA;MACA;MACA;IACA;IACA;EACA;AAEA", "names": ["name", "data", "statistics", "currentRounds", "current", "upcoming", "roundsList", "count", "goodsList", "loadingGoods", "showAddModal", "showDetailModal", "selectedRound", "isCreating", "newRound", "start_date", "end_date", "goods_list", "refreshTimer", "creationProgress", "total", "computed", "canCreateRound", "console", "hasStartDate", "hasEndDate", "hasGoods", "goodsValid", "date<PERSON><PERSON>d", "generatedRoundName", "month", "day", "hourlySlotPreview", "endDate", "currentDate", "slotStart", "slotEnd", "slots", "start", "end", "startTime", "endTime", "validSlotsCount", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "loadData", "Promise", "loadStatistics", "response", "loadCurrentRounds", "loadRoundsList", "loadGoodsList", "startAutoRefresh", "isGoodsSelected", "getSelectedGoods", "toggleGoods", "goods_id", "goods_name", "original_price", "flash_price", "discount_rate", "stock", "limit_quantity", "removeGoods", "calculateDiscountRate", "updateFlashPriceByDiscount", "selectedGoods", "getCurrentDateTime", "now", "getCurrentDate", "openCreateModal", "formatDateTime", "year", "hour", "minute", "formatSlotTime", "isSlotInPast", "isSlotActive", "createRound", "hourlySlots", "validSlots", "confirmed", "createdCount", "failedCount", "failedReasons", "batchSize", "batchEnd", "batch", "batchPromises", "globalIndex", "roundData", "round_name", "start_time", "end_time", "is_hourly_flash", "slot_index", "total_slots", "goods_image", "success", "index", "error", "batchResults", "batchStart", "message", "errorMessage", "closeModal", "viewRoundDetails", "closeRound", "confirm", "round_id", "cancelRound", "formatTime", "dateTime", "status"], "sourceRoot": "src/components/Marketing", "sources": ["FlashSaleMultiPage.vue"], "sourcesContent": ["<template>\n  <div class=\"flash-sale-multi-page\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h2>限时秒杀管理（多商品轮次）</h2>\n      <button @click=\"openCreateModal\" class=\"btn btn-primary\">\n        创建新轮次\n      </button>\n    </div>\n\n    <!-- 统计卡片 -->\n    <div class=\"stats-cards\">\n      <div class=\"stat-card\">\n        <h3>总轮次</h3>\n        <p class=\"stat-number\">{{ statistics.totalRounds || 0 }}</p>\n      </div>\n      <div class=\"stat-card\">\n        <h3>进行中</h3>\n        <p class=\"stat-number active\">{{ statistics.activeRounds || 0 }}</p>\n      </div>\n      <div class=\"stat-card\">\n        <h3>即将开始</h3>\n        <p class=\"stat-number upcoming\">{{ statistics.upcomingRounds || 0 }}</p>\n      </div>\n      <div class=\"stat-card\">\n        <h3>总销售额</h3>\n        <p class=\"stat-number\">¥{{ (statistics.totalSales || 0).toFixed(2) }}</p>\n      </div>\n    </div>\n\n    <!-- 当前轮次 -->\n    <div class=\"current-rounds\" v-if=\"currentRounds.current && currentRounds.current.length > 0\">\n      <h3>当前进行中的轮次</h3>\n      <div class=\"round-list\">\n        <div v-for=\"round in currentRounds.current\" :key=\"round.id\" class=\"round-item active\">\n          <div class=\"round-info\">\n            <h4>{{ round.round_name }} (轮次 #{{ round.round_number }})</h4>\n            <p>商品数量: {{ round.goods_count }}</p>\n            <p>剩余时间: {{ formatTime(round.countdown) }}</p>\n          </div>\n          <div class=\"goods-preview\">\n            <div v-for=\"goods in round.goods_list.slice(0, 3)\" :key=\"goods.id\" class=\"goods-item\">\n              <img :src=\"goods.goods_image\" :alt=\"goods.goods_name\" />\n              <span>{{ goods.goods_name }}</span>\n              <span class=\"price\">¥{{ goods.flash_price }}</span>\n            </div>\n            <span v-if=\"round.goods_list.length > 3\" class=\"more-goods\">\n              +{{ round.goods_list.length - 3 }}个商品\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 轮次列表 -->\n    <div class=\"rounds-table\">\n      <h3>轮次列表</h3>\n      <table class=\"table\">\n        <thead>\n          <tr>\n            <th>轮次编号</th>\n            <th>轮次名称</th>\n            <th>商品数量</th>\n            <th>总库存</th>\n            <th>已售出</th>\n            <th>开始时间</th>\n            <th>结束时间</th>\n            <th>状态</th>\n            <th>操作</th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr v-for=\"round in roundsList.data\" :key=\"round.id\">\n            <td>#{{ round.round_number }}</td>\n            <td>{{ round.round_name }}</td>\n            <td>{{ round.goods_count }}</td>\n            <td>{{ round.total_stock }}</td>\n            <td>{{ round.total_sold }}</td>\n            <td>{{ formatDateTime(round.start_time) }}</td>\n            <td>{{ formatDateTime(round.end_time) }}</td>\n            <td>\n              <span :class=\"'status-' + round.status\">\n                {{ getStatusText(round.status) }}\n              </span>\n            </td>\n            <td>\n              <div class=\"round-actions\">\n                <button @click=\"viewRoundDetails(round)\" class=\"btn btn-info btn-sm\">查看详情</button>\n                <button\n                  v-if=\"round.status === 'upcoming' || round.status === 'active'\"\n                  @click=\"closeRound(round)\"\n                  class=\"btn btn-warning btn-sm\"\n                >\n                  关闭轮次\n                </button>\n              </div>\n            </td>\n          </tr>\n        </tbody>\n      </table>\n    </div>\n\n    <!-- 创建轮次模态框 -->\n    <div v-if=\"showAddModal\" class=\"modal-overlay\" @click=\"closeModal\">\n      <div class=\"modal-content\" @click.stop>\n        <div class=\"modal-header\">\n          <h3>创建新轮次</h3>\n          <button @click=\"closeModal\" class=\"close-btn\">&times;</button>\n        </div>\n        \n        <div class=\"modal-body\">\n          <!-- 整点秒杀时间设置 -->\n          <div class=\"hourly-flash-settings\">\n            <div class=\"setting-header\">\n              <h4>整点秒杀设置</h4>\n              <p class=\"setting-description\">每小时整点开始，持续40分钟，24小时不间断</p>\n            </div>\n\n            <div class=\"form-row\">\n              <div class=\"form-group\">\n                <label>活动开始日期 *</label>\n                <input\n                  v-model=\"newRound.start_date\"\n                  type=\"date\"\n                  class=\"form-control\"\n                  required\n                />\n              </div>\n              <div class=\"form-group\">\n                <label>活动结束日期 *</label>\n                <input\n                  v-model=\"newRound.end_date\"\n                  type=\"date\"\n                  class=\"form-control\"\n                  :min=\"newRound.start_date\"\n                  required\n                />\n              </div>\n            </div>\n\n            <!-- 自动生成的轮次名称预览 -->\n            <div v-if=\"generatedRoundName\" class=\"round-name-preview\">\n              <h5>轮次名称：</h5>\n              <div class=\"name-display\">{{ generatedRoundName }}</div>\n            </div>\n\n            <!-- 时段预览 -->\n            <div v-if=\"hourlySlotPreview.length > 0\" class=\"slot-preview\">\n              <h5>将生成以下秒杀时段：</h5>\n              <div class=\"slot-list-container\">\n                <div class=\"slot-list\">\n                  <div v-for=\"(slot, index) in hourlySlotPreview.slice(0, 10)\" :key=\"index\" class=\"slot-item\">\n                    <span class=\"slot-number\">第{{ index + 1 }}场</span>\n                    <span class=\"slot-time\">{{ formatSlotTime(slot.start) }} - {{ formatSlotTime(slot.end) }}</span>\n                    <span class=\"slot-duration\">40分钟</span>\n                    <span v-if=\"isSlotInPast(slot.start)\" class=\"slot-status past\">已过期</span>\n                    <span v-else-if=\"isSlotActive(slot.start, slot.end)\" class=\"slot-status active\">进行中</span>\n                    <span v-else class=\"slot-status upcoming\">待开始</span>\n                  </div>\n                  <div v-if=\"hourlySlotPreview.length > 10\" class=\"more-slots\">\n                    ... 还有 {{ hourlySlotPreview.length - 10 }} 个时段\n                  </div>\n                </div>\n              </div>\n              <div class=\"slot-summary\">\n                <span>共 {{ hourlySlotPreview.length }} 个时段</span>\n                <span class=\"valid-slots\">（有效时段：{{ validSlotsCount }} 个）</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- 商品选择 -->\n          <div class=\"form-group\">\n            <label>选择商品 * (点击商品卡片进行选择)</label>\n\n            <!-- 加载状态 -->\n            <div v-if=\"loadingGoods\" class=\"loading-state\">\n              <i class=\"loading-icon\">⏳</i>\n              <span>正在加载商品列表...</span>\n            </div>\n\n            <!-- 商品列表 -->\n            <div v-else-if=\"goodsList && goodsList.length > 0\" class=\"goods-selection-grid\">\n              <div\n                v-for=\"goods in goodsList\"\n                :key=\"goods.id\"\n                class=\"goods-card\"\n                :class=\"{\n                  'selected': isGoodsSelected(goods.id),\n                  'disabled': !goods.can_select\n                }\"\n                @click=\"toggleGoods(goods)\"\n              >\n                <!-- 商品基本信息 -->\n                <div class=\"goods-card-header\">\n                  <div class=\"goods-image-container\">\n                    <img :src=\"goods.list_pic_url\" :alt=\"goods.name\" class=\"goods-card-image\" />\n                    <div v-if=\"isGoodsSelected(goods.id)\" class=\"selected-badge\">\n                      <i class=\"checkmark\">✓</i>\n                    </div>\n                    <div v-if=\"!goods.can_select\" class=\"disabled-overlay\">\n                      <span>已参与其他秒杀</span>\n                    </div>\n                  </div>\n                  <div class=\"goods-card-info\">\n                    <h4 class=\"goods-name\">{{ goods.name }}</h4>\n                    <p class=\"original-price\">原价: ¥{{ goods.retail_price }}</p>\n                    <div v-if=\"!goods.can_select\" class=\"warning-text\">\n                      <i class=\"warning-icon\">⚠</i>\n                      <span>已参与其他秒杀活动</span>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- 秒杀设置面板 -->\n                <div v-if=\"isGoodsSelected(goods.id)\" class=\"goods-settings-panel\">\n                  <div class=\"settings-title\">秒杀设置</div>\n                  <div class=\"settings-grid\">\n                    <div class=\"setting-item full-width\">\n                      <label>折扣设置</label>\n                      <div class=\"discount-setting\">\n                        <div class=\"discount-input-group\">\n                          <input\n                            v-model.number=\"getSelectedGoods(goods.id).discount_rate\"\n                            type=\"number\"\n                            step=\"1\"\n                            min=\"10\"\n                            max=\"90\"\n                            class=\"discount-input\"\n                            @input=\"updateFlashPriceByDiscount(goods.id)\"\n                            @click.stop\n                          />\n                          <span class=\"discount-unit\">% OFF</span>\n                        </div>\n                        <div class=\"price-preview\">\n                          原价: ¥{{ parseFloat(goods.retail_price).toFixed(2) }} →\n                          秒杀价: ¥{{ getSelectedGoods(goods.id).flash_price }}\n                        </div>\n                        <div class=\"price-range-hint\" v-if=\"goods.price_range\">\n                          商品价格区间: {{ goods.price_range }}\n                        </div>\n                      </div>\n                    </div>\n\n                    <div class=\"setting-item\">\n                      <label>秒杀库存</label>\n                      <input\n                        v-model.number=\"getSelectedGoods(goods.id).stock\"\n                        type=\"number\"\n                        min=\"1\"\n                        max=\"9999\"\n                        class=\"stock-input\"\n                        @click.stop\n                      />\n                    </div>\n\n                    <div class=\"setting-item\">\n                      <label>限购数量</label>\n                      <input\n                        v-model.number=\"getSelectedGoods(goods.id).limit_quantity\"\n                        type=\"number\"\n                        min=\"1\"\n                        max=\"99\"\n                        class=\"limit-input\"\n                        @click.stop\n                      />\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- 空状态显示 -->\n            <div v-else class=\"empty-state\">\n              <div class=\"empty-icon\">📦</div>\n              <p class=\"empty-text\">暂无可选商品</p>\n              <p class=\"empty-hint\">请确保有上架的商品，且未参与其他秒杀活动</p>\n              <button @click=\"loadGoodsList\" class=\"btn btn-secondary btn-sm\">重新加载</button>\n            </div>\n\n            <!-- 选择提示 -->\n            <div class=\"selection-hint\">\n              <p v-if=\"newRound.goods_list.length === 0\" class=\"hint-text\">\n                <i class=\"info-icon\">ℹ</i>\n                请点击商品卡片选择参与秒杀的商品，可以选择多个商品\n              </p>\n              <p v-else class=\"selected-count\">\n                已选择 <strong>{{ newRound.goods_list.length }}</strong> 个商品\n              </p>\n            </div>\n          </div>\n\n          <!-- 已选商品汇总 -->\n          <div v-if=\"newRound.goods_list.length > 0\" class=\"selected-summary\">\n            <h4>已选择 {{ newRound.goods_list.length }} 个商品</h4>\n            <div class=\"summary-list\">\n              <div v-for=\"goods in newRound.goods_list\" :key=\"goods.goods_id\" class=\"summary-item\">\n                <span>{{ goods.goods_name }}</span>\n                <span>¥{{ goods.flash_price }} ({{ calculateDiscountRate(goods.original_price, goods.flash_price) }}% OFF)</span>\n                <span>库存: {{ goods.stock }}</span>\n                <button @click=\"removeGoods(goods.goods_id)\" class=\"remove-btn\">移除</button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"modal-footer\">\n          <button @click=\"closeModal\" class=\"btn btn-secondary\">取消</button>\n          <button\n            @click=\"createRound\"\n            class=\"btn btn-primary\"\n            :disabled=\"!canCreateRound || isCreating\"\n          >\n            {{ isCreating ? `正在创建... (${creationProgress.current}/${creationProgress.total})` : '创建整点秒杀轮次' }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 轮次详情模态框 -->\n    <div v-if=\"showDetailModal\" class=\"modal-overlay\" @click=\"showDetailModal = false\">\n      <div class=\"modal-content large\" @click.stop>\n        <div class=\"modal-header\">\n          <h3>轮次详情 - {{ selectedRound.round_name }}</h3>\n          <button @click=\"showDetailModal = false\" class=\"close-btn\">&times;</button>\n        </div>\n        \n        <div class=\"modal-body\">\n          <div class=\"round-details\">\n            <div class=\"detail-section\">\n              <h4>基本信息</h4>\n              <p>轮次编号: #{{ selectedRound.round_number }}</p>\n              <p>开始时间: {{ formatDateTime(selectedRound.start_time) }}</p>\n              <p>结束时间: {{ formatDateTime(selectedRound.end_time) }}</p>\n              <p>状态: {{ getStatusText(selectedRound.status) }}</p>\n            </div>\n            \n            <div class=\"detail-section\">\n              <h4>商品列表</h4>\n              <table class=\"table\">\n                <thead>\n                  <tr>\n                    <th>商品</th>\n                    <th>原价</th>\n                    <th>秒杀价</th>\n                    <th>折扣</th>\n                    <th>库存</th>\n                    <th>已售</th>\n                    <th>限购</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr v-for=\"goods in selectedRound.goods_list\" :key=\"goods.id\">\n                    <td>\n                      <div class=\"goods-cell\">\n                        <img :src=\"goods.goods_image\" :alt=\"goods.goods_name\" />\n                        <span>{{ goods.goods_name }}</span>\n                      </div>\n                    </td>\n                    <td>¥{{ goods.original_price }}</td>\n                    <td>¥{{ goods.flash_price }}</td>\n                    <td>{{ goods.discount_rate }}%</td>\n                    <td>{{ goods.stock }}</td>\n                    <td>{{ goods.sold_count }}</td>\n                    <td>{{ goods.limit_quantity }}</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FlashSaleMultiPage',\n  data() {\n    return {\n      statistics: {},\n      currentRounds: { current: [], upcoming: [] },\n      roundsList: { data: [], count: 0 },\n      goodsList: [],\n      loadingGoods: false,\n      showAddModal: false,\n      showDetailModal: false,\n      selectedRound: {},\n      isCreating: false,\n      newRound: {\n        start_date: '',\n        end_date: '',\n        goods_list: []\n      },\n      refreshTimer: null,\n      creationProgress: {\n        current: 0,\n        total: 0\n      }\n    };\n  },\n  \n  computed: {\n    canCreateRound() {\n      const hasStartDate = this.newRound.start_date;\n      const hasEndDate = this.newRound.end_date;\n      const hasGoods = this.newRound.goods_list.length > 0;\n      const goodsValid = this.newRound.goods_list.every(g => g.flash_price > 0 && g.stock > 0);\n      const dateValid = hasStartDate && hasEndDate && new Date(this.newRound.start_date) <= new Date(this.newRound.end_date);\n\n      console.log('canCreateRound检查:', {\n        hasStartDate,\n        hasEndDate,\n        hasGoods,\n        goodsValid,\n        dateValid,\n        goodsList: this.newRound.goods_list\n      });\n\n      return hasStartDate && hasEndDate && hasGoods && goodsValid && dateValid;\n    },\n\n    // 自动生成轮次名称\n    generatedRoundName() {\n      if (!this.newRound.start_date || !this.newRound.end_date) {\n        return '';\n      }\n\n      const startDate = new Date(this.newRound.start_date);\n      const endDate = new Date(this.newRound.end_date);\n\n      if (startDate.getTime() === endDate.getTime()) {\n        // 单日活动\n        const dateStr = startDate.toLocaleDateString('zh-CN', {\n          month: 'long',\n          day: 'numeric'\n        });\n        return `${dateStr}整点秒杀`;\n      } else {\n        // 多日活动\n        const startStr = startDate.toLocaleDateString('zh-CN', {\n          month: 'long',\n          day: 'numeric'\n        });\n        const endStr = endDate.toLocaleDateString('zh-CN', {\n          month: 'long',\n          day: 'numeric'\n        });\n        return `${startStr}至${endStr}整点秒杀`;\n      }\n    },\n\n    // 整点秒杀时段预览（24小时全天候）\n    hourlySlotPreview() {\n      if (!this.newRound.start_date || !this.newRound.end_date) {\n        return [];\n      }\n\n      const slots = [];\n      const startDate = new Date(this.newRound.start_date);\n      const endDate = new Date(this.newRound.end_date);\n\n      // 设置结束日期为当天的23:59:59\n      endDate.setHours(23, 59, 59, 999);\n\n      let currentDate = new Date(startDate);\n      currentDate.setHours(0, 0, 0, 0); // 从00:00开始\n\n      while (currentDate <= endDate) {\n        for (let hour = 0; hour < 24; hour++) {\n          const slotStart = new Date(currentDate);\n          slotStart.setHours(hour, 0, 0, 0);\n\n          const slotEnd = new Date(currentDate);\n          slotEnd.setHours(hour, 40, 0, 0);\n\n          // 检查是否超出结束日期\n          if (slotStart > endDate) break;\n\n          slots.push({\n            start: slotStart.toISOString().slice(0, 19).replace('T', ' '),\n            end: slotEnd.toISOString().slice(0, 19).replace('T', ' '),\n            startTime: slotStart,\n            endTime: slotEnd\n          });\n        }\n\n        // 移动到下一天\n        currentDate.setDate(currentDate.getDate() + 1);\n      }\n\n      return slots;\n    },\n\n    // 有效时段数量（未过期的时段）\n    validSlotsCount() {\n      const now = new Date();\n      return this.hourlySlotPreview.filter(slot => new Date(slot.start) > now).length;\n    }\n  },\n\n  mounted() {\n    console.log('FlashSaleMultiPage组件已挂载');\n    console.log('初始showAddModal值:', this.showAddModal);\n    this.loadData();\n    this.startAutoRefresh();\n  },\n\n  beforeDestroy() {\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer);\n    }\n  },\n\n  methods: {\n    async loadData() {\n      await Promise.all([\n        this.loadStatistics(),\n        this.loadCurrentRounds(),\n        this.loadRoundsList(),\n        this.loadGoodsList()\n      ]);\n    },\n\n    async loadStatistics() {\n      try {\n        const response = await this.axios.get('flashsalemulti/statistics');\n        if (response.data.errno === 0) {\n          this.statistics = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载统计数据失败:', error);\n      }\n    },\n\n    async loadCurrentRounds() {\n      try {\n        const response = await this.axios.get('flashsalemulti/current');\n        if (response.data.errno === 0) {\n          this.currentRounds = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载当前轮次失败:', error);\n      }\n    },\n\n    async loadRoundsList() {\n      try {\n        const response = await this.axios.get('flashsalemulti/list');\n        if (response.data.errno === 0) {\n          this.roundsList = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载轮次列表失败:', error);\n      }\n    },\n\n    async loadGoodsList() {\n      try {\n        this.loadingGoods = true;\n        console.log('开始加载商品列表...');\n\n        const response = await this.axios.get('flashsalemulti/goods');\n        console.log('商品列表API响应:', response.data);\n\n        if (response.data.errno === 0) {\n          this.goodsList = response.data.data || [];\n          console.log('商品列表加载成功，数量:', this.goodsList.length);\n\n          if (this.goodsList.length === 0) {\n            this.$message.warning('暂无可选商品，请确保有上架的商品且未参与其他秒杀活动');\n          }\n        } else {\n          console.error('API返回错误:', response.data.errmsg);\n          this.$message.error(response.data.errmsg || '加载商品列表失败');\n          this.goodsList = [];\n        }\n      } catch (error) {\n        console.error('加载商品列表失败:', error);\n        this.$message.error('网络错误，请检查服务器连接');\n        this.goodsList = [];\n      } finally {\n        this.loadingGoods = false;\n      }\n    },\n\n    startAutoRefresh() {\n      this.refreshTimer = setInterval(() => {\n        this.loadCurrentRounds();\n        this.loadStatistics();\n      }, 30000); // 30秒刷新一次\n    },\n\n    isGoodsSelected(goodsId) {\n      return this.newRound.goods_list.some(g => g.goods_id === goodsId);\n    },\n\n    getSelectedGoods(goodsId) {\n      return this.newRound.goods_list.find(g => g.goods_id === goodsId);\n    },\n\n    toggleGoods(goods) {\n      if (!goods.can_select) return;\n\n      if (this.isGoodsSelected(goods.id)) {\n        this.removeGoods(goods.id);\n      } else {\n        const originalPrice = parseFloat(goods.retail_price) || 0;\n        const defaultDiscount = 20; // 默认20%折扣\n        const flashPrice = Math.round(originalPrice * (100 - defaultDiscount) / 100 * 100) / 100;\n\n        this.newRound.goods_list.push({\n          goods_id: goods.id,\n          goods_name: goods.name,\n          original_price: originalPrice,\n          flash_price: flashPrice,\n          discount_rate: defaultDiscount,\n          stock: 100,\n          limit_quantity: 1\n        });\n      }\n    },\n\n    removeGoods(goodsId) {\n      const index = this.newRound.goods_list.findIndex(g => g.goods_id === goodsId);\n      if (index > -1) {\n        this.newRound.goods_list.splice(index, 1);\n      }\n    },\n\n    calculateDiscountRate(originalPrice, flashPrice) {\n      if (!originalPrice || originalPrice <= 0 || !flashPrice || flashPrice <= 0) return 0;\n      const rate = Math.round((1 - flashPrice / originalPrice) * 100);\n      return isNaN(rate) ? 0 : rate;\n    },\n\n    updateFlashPriceByDiscount(goodsId) {\n      const selectedGoods = this.getSelectedGoods(goodsId);\n      if (selectedGoods && selectedGoods.original_price > 0) {\n        const discountRate = selectedGoods.discount_rate || 0;\n        const flashPrice = Math.round(selectedGoods.original_price * (100 - discountRate) / 100 * 100) / 100;\n        selectedGoods.flash_price = flashPrice;\n      }\n    },\n\n    getCurrentDateTime() {\n      const now = new Date();\n      now.setMinutes(now.getMinutes() - now.getTimezoneOffset());\n      return now.toISOString().slice(0, 16);\n    },\n\n    getCurrentDate() {\n      const now = new Date();\n      return now.toISOString().slice(0, 10);\n    },\n\n    openCreateModal() {\n      console.log('点击创建新轮次按钮');\n      // 设置默认日期为今天\n      const today = new Date().toISOString().slice(0, 10);\n      this.newRound.start_date = today;\n      this.newRound.end_date = today;\n      this.showAddModal = true;\n      console.log('showAddModal设置为:', this.showAddModal);\n    },\n\n    formatDateTime(dateTimeStr) {\n      if (!dateTimeStr) return '';\n      const date = new Date(dateTimeStr);\n      return date.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    formatSlotTime(dateTimeStr) {\n      if (!dateTimeStr) return '';\n      const date = new Date(dateTimeStr);\n      return date.toLocaleString('zh-CN', {\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    isSlotInPast(startTime) {\n      const now = new Date();\n      const slotStart = new Date(startTime);\n      return slotStart < now;\n    },\n\n    isSlotActive(startTime, endTime) {\n      const now = new Date();\n      const slotStart = new Date(startTime);\n      const slotEnd = new Date(endTime);\n      return now >= slotStart && now <= slotEnd;\n    },\n\n    async createRound() {\n      if (!this.canCreateRound) {\n        this.$message.error('请完善轮次信息');\n        return;\n      }\n\n      // 生成整点秒杀时段数据\n      const hourlySlots = this.hourlySlotPreview;\n      const now = new Date();\n\n      // 过滤掉已过期的时段\n      const validSlots = hourlySlots.filter(slot => new Date(slot.start) > now);\n\n      if (validSlots.length === 0) {\n        this.$message.error('所选时间段内没有有效的秒杀时段');\n        return;\n      }\n\n      // 如果轮次数量过多，询问用户确认\n      if (validSlots.length > 50) {\n        const confirmed = confirm(`将要创建${validSlots.length}个轮次，这可能需要较长时间。是否继续？`);\n        if (!confirmed) {\n          return;\n        }\n      }\n\n      try {\n        this.isCreating = true;\n        this.creationProgress.current = 0;\n        this.creationProgress.total = validSlots.length;\n\n        let createdCount = 0;\n        let failedCount = 0;\n        const failedReasons = [];\n\n        // 批量处理，每次处理10个轮次\n        const batchSize = 10;\n        for (let batchStart = 0; batchStart < validSlots.length; batchStart += batchSize) {\n          const batchEnd = Math.min(batchStart + batchSize, validSlots.length);\n          const batch = validSlots.slice(batchStart, batchEnd);\n\n          // 并行创建当前批次的轮次\n          const batchPromises = batch.map(async (slot, batchIndex) => {\n            const globalIndex = batchStart + batchIndex;\n\n            try {\n              const roundData = {\n                round_name: `${this.generatedRoundName} - 第${globalIndex + 1}场`,\n                start_time: slot.start,\n                end_time: slot.end,\n                is_hourly_flash: true,\n                slot_index: globalIndex + 1,\n                total_slots: validSlots.length,\n                goods_list: this.newRound.goods_list.map(goods => ({\n                  goods_id: goods.goods_id,\n                  goods_name: goods.goods_name,\n                  goods_image: goods.goods_image,\n                  original_price: goods.original_price,\n                  flash_price: goods.flash_price,\n                  stock: goods.stock,\n                  discount_rate: goods.discount_rate\n                }))\n              };\n\n              const response = await this.axios.post('flashsalemulti/create', roundData);\n\n              if (response.data.errno === 0) {\n                return { success: true, index: globalIndex + 1 };\n              } else {\n                return { success: false, index: globalIndex + 1, error: response.data.errmsg };\n              }\n            } catch (error) {\n              return { success: false, index: globalIndex + 1, error: error.message };\n            }\n          });\n\n          // 等待当前批次完成\n          const batchResults = await Promise.all(batchPromises);\n\n          // 统计结果\n          batchResults.forEach(result => {\n            this.creationProgress.current++;\n            if (result.success) {\n              createdCount++;\n            } else {\n              failedCount++;\n              if (failedReasons.length < 5) { // 只记录前5个错误\n                failedReasons.push(`第${result.index}场: ${result.error}`);\n              }\n            }\n          });\n\n          // 短暂延迟，避免服务器压力过大\n          if (batchEnd < validSlots.length) {\n            await new Promise(resolve => setTimeout(resolve, 100));\n          }\n        }\n\n        // 显示结果\n        if (createdCount > 0) {\n          let message = `成功创建${createdCount}个整点秒杀轮次`;\n          if (failedCount > 0) {\n            message += `，${failedCount}个失败`;\n            if (failedReasons.length > 0) {\n              console.warn('创建失败的轮次:', failedReasons);\n              message += `\\n主要错误: ${failedReasons[0]}`;\n            }\n          }\n          this.$message.success(message);\n          this.closeModal();\n          this.loadData();\n        } else {\n          let errorMessage = '所有轮次创建失败';\n          if (failedReasons.length > 0) {\n            errorMessage += `\\n错误信息: ${failedReasons[0]}`;\n          }\n          this.$message.error(errorMessage);\n        }\n\n      } catch (error) {\n        console.error('创建整点秒杀轮次失败:', error);\n        this.$message.error('创建过程中发生错误: ' + error.message);\n      } finally {\n        this.isCreating = false;\n        this.creationProgress.current = 0;\n        this.creationProgress.total = 0;\n      }\n    },\n\n    closeModal() {\n      this.showAddModal = false;\n      this.newRound = {\n        start_date: '',\n        end_date: '',\n        goods_list: []\n      };\n    },\n\n    viewRoundDetails(round) {\n      this.selectedRound = round;\n      this.showDetailModal = true;\n    },\n\n    async closeRound(round) {\n      if (!confirm(`确定要关闭轮次\"${round.round_name}\"吗？关闭后轮次将立即结束。`)) {\n        return;\n      }\n\n      try {\n        this.$message.info('正在关闭轮次...');\n        const response = await this.axios.post('flashsalemulti/close', {\n          round_id: round.id\n        });\n\n        if (response.data.errno === 0) {\n          this.$message.success('轮次已关闭');\n          this.loadData(); // 重新加载数据\n        } else {\n          this.$message.error(response.data.errmsg || '关闭失败');\n        }\n      } catch (error) {\n        console.error('关闭轮次失败:', error);\n        this.$message.error('关闭失败');\n      }\n    },\n\n    async cancelRound(round) {\n      if (!confirm(`确定要取消轮次\"${round.round_name}\"吗？取消后轮次将无法恢复。`)) {\n        return;\n      }\n\n      try {\n        this.$message.info('正在取消轮次...');\n        const response = await this.axios.post('flashsalemulti/cancel', {\n          round_id: round.id\n        });\n\n        if (response.data.errno === 0) {\n          this.$message.success('轮次已取消');\n          this.loadData(); // 重新加载数据\n        } else {\n          this.$message.error(response.data.errmsg || '取消失败');\n        }\n      } catch (error) {\n        console.error('取消轮次失败:', error);\n        this.$message.error('取消失败');\n      }\n    },\n\n    formatTime(seconds) {\n      if (!seconds || seconds <= 0) return '00:00:00';\n      const hours = Math.floor(seconds / 3600);\n      const minutes = Math.floor((seconds % 3600) / 60);\n      const secs = seconds % 60;\n      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    },\n\n    formatDateTime(dateTime) {\n      if (!dateTime) return '';\n      return new Date(dateTime).toLocaleString('zh-CN');\n    },\n\n    getStatusText(status) {\n      const statusMap = {\n        'upcoming': '即将开始',\n        'active': '进行中',\n        'ended': '已结束',\n        'cancelled': '已取消'\n      };\n      return statusMap[status] || status;\n    }\n  }\n};\n</script>\n\n<style scoped>\n.flash-sale-multi-page {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.page-header h2 {\n  margin: 0;\n  color: #333;\n}\n\n.btn {\n  padding: 8px 16px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  color: white;\n}\n\n.btn-secondary {\n  background-color: #6c757d;\n  color: white;\n}\n\n.btn-sm {\n  padding: 4px 8px;\n  font-size: 12px;\n}\n\n.round-actions {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.round-actions .btn {\n  margin: 0;\n}\n\n.btn-info {\n  background-color: #17a2b8;\n  border-color: #17a2b8;\n  color: white;\n}\n\n.btn-info:hover {\n  background-color: #138496;\n  border-color: #117a8b;\n}\n\n.btn-warning {\n  background-color: #ffc107;\n  border-color: #ffc107;\n  color: #212529;\n}\n\n.btn-warning:hover {\n  background-color: #e0a800;\n  border-color: #d39e00;\n}\n\n.btn-danger {\n  background-color: #dc3545;\n  border-color: #dc3545;\n  color: white;\n}\n\n.btn-danger:hover {\n  background-color: #c82333;\n  border-color: #bd2130;\n}\n\n/* 整点秒杀设置样式 */\n.hourly-flash-settings {\n  margin-bottom: 20px;\n}\n\n.setting-header {\n  margin-bottom: 15px;\n}\n\n.setting-header h4 {\n  margin: 0 0 5px 0;\n  color: #333;\n}\n\n.setting-description {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.time-range-selector {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.time-separator {\n  color: #666;\n  font-weight: bold;\n}\n\n.slot-preview {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f8f9fa;\n  border-radius: 5px;\n  border: 1px solid #e9ecef;\n}\n\n.slot-preview h5 {\n  margin: 0 0 10px 0;\n  color: #333;\n}\n\n.slot-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.slot-item {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 8px 12px;\n  background-color: white;\n  border-radius: 4px;\n  border: 1px solid #dee2e6;\n}\n\n.slot-number {\n  font-weight: bold;\n  color: #007bff;\n  min-width: 60px;\n}\n\n.slot-time {\n  flex: 1;\n  color: #333;\n}\n\n.slot-duration {\n  color: #28a745;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.slot-summary {\n  margin-top: 10px;\n  padding-top: 10px;\n  border-top: 1px solid #dee2e6;\n  text-align: center;\n  color: #666;\n  font-weight: bold;\n}\n\n.valid-slots {\n  color: #28a745;\n  margin-left: 10px;\n}\n\n.round-name-preview {\n  margin-bottom: 20px;\n}\n\n.round-name-preview h5 {\n  margin-bottom: 8px;\n  color: #333;\n  font-weight: bold;\n}\n\n.name-display {\n  padding: 10px 15px;\n  background-color: #f8f9fa;\n  border: 1px solid #dee2e6;\n  border-radius: 4px;\n  font-size: 16px;\n  font-weight: bold;\n  color: #007bff;\n}\n\n.slot-list-container {\n  max-height: 300px;\n  overflow-y: auto;\n  border: 1px solid #dee2e6;\n  border-radius: 4px;\n}\n\n.slot-status {\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 11px;\n  font-weight: bold;\n}\n\n.slot-status.past {\n  background-color: #f8d7da;\n  color: #721c24;\n}\n\n.slot-status.active {\n  background-color: #d4edda;\n  color: #155724;\n}\n\n.slot-status.upcoming {\n  background-color: #d1ecf1;\n  color: #0c5460;\n}\n\n.more-slots {\n  padding: 10px;\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  background-color: #f8f9fa;\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n/* 统计卡片 */\n.stats-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n.stat-card {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  text-align: center;\n}\n\n.stat-card h3 {\n  margin: 0 0 10px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.stat-number {\n  font-size: 24px;\n  font-weight: bold;\n  margin: 0;\n  color: #333;\n}\n\n.stat-number.active {\n  color: #28a745;\n}\n\n.stat-number.upcoming {\n  color: #ffc107;\n}\n\n/* 当前轮次 */\n.current-rounds {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  margin-bottom: 20px;\n}\n\n.round-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.round-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n}\n\n.round-item.active {\n  border-color: #28a745;\n  background-color: #f8fff9;\n}\n\n.round-info h4 {\n  margin: 0 0 5px 0;\n  color: #333;\n}\n\n.round-info p {\n  margin: 2px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.goods-preview {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.goods-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  width: 80px;\n}\n\n.goods-item img {\n  width: 40px;\n  height: 40px;\n  object-fit: cover;\n  border-radius: 4px;\n  margin-bottom: 4px;\n}\n\n.goods-item span {\n  font-size: 12px;\n  color: #666;\n}\n\n.goods-item .price {\n  color: #e74c3c;\n  font-weight: bold;\n}\n\n.more-goods {\n  color: #007bff;\n  font-size: 12px;\n}\n\n/* 表格 */\n.rounds-table {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.table {\n  width: 100%;\n  border-collapse: collapse;\n  margin-top: 15px;\n}\n\n.table th,\n.table td {\n  padding: 12px;\n  text-align: left;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.table th {\n  background-color: #f8f9fa;\n  font-weight: 600;\n  color: #333;\n}\n\n.status-upcoming {\n  color: #ffc107;\n  font-weight: bold;\n}\n\n.status-active {\n  color: #28a745;\n  font-weight: bold;\n}\n\n.status-ended {\n  color: #6c757d;\n  font-weight: bold;\n}\n\n.status-cancelled {\n  color: #dc3545;\n  font-weight: bold;\n}\n\n/* 模态框 */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background: white;\n  border-radius: 8px;\n  width: 90%;\n  max-width: 800px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n\n.modal-content.large {\n  max-width: 1000px;\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.modal-header h3 {\n  margin: 0;\n  color: #333;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  font-size: 24px;\n  cursor: pointer;\n  color: #666;\n}\n\n.modal-body {\n  padding: 20px;\n}\n\n.modal-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n  padding: 20px;\n  border-top: 1px solid #e9ecef;\n}\n\n/* 表单 */\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-row {\n  display: flex;\n  gap: 15px;\n}\n\n.form-row .form-group {\n  flex: 1;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  font-weight: 600;\n  color: #333;\n}\n\n.form-control {\n  width: 100%;\n  padding: 8px 12px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.form-control.small {\n  width: 80px;\n}\n\n.form-control:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n/* 商品选择网格 */\n.goods-selection-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 20px;\n  max-height: 500px;\n  overflow-y: auto;\n  padding: 10px;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background-color: #f8f9fa;\n}\n\n/* 商品卡片 */\n.goods-card {\n  background: white;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  padding: 15px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n.goods-card:hover {\n  border-color: #007bff;\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);\n  transform: translateY(-2px);\n}\n\n.goods-card.selected {\n  border-color: #28a745;\n  background-color: #f8fff9;\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);\n}\n\n.goods-card.disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  background-color: #f5f5f5;\n  border-color: #dee2e6;\n}\n\n.goods-card.disabled:hover {\n  transform: none;\n  box-shadow: none;\n  border-color: #dee2e6;\n}\n\n/* 商品卡片头部 */\n.goods-card-header {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 15px;\n}\n\n.goods-image-container {\n  position: relative;\n  margin-right: 15px;\n  flex-shrink: 0;\n}\n\n.goods-card-image {\n  width: 80px;\n  height: 80px;\n  object-fit: cover;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n}\n\n.selected-badge {\n  position: absolute;\n  top: -5px;\n  right: -5px;\n  width: 24px;\n  height: 24px;\n  background-color: #28a745;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 2px solid white;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.2);\n}\n\n.checkmark {\n  color: white;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.disabled-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n/* 商品信息 */\n.goods-card-info {\n  flex: 1;\n}\n\n.goods-name {\n  margin: 0 0 8px 0;\n  color: #333;\n  font-size: 16px;\n  font-weight: 600;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.original-price {\n  margin: 0 0 5px 0;\n  color: #666;\n  font-size: 14px;\n}\n\n.warning-text {\n  display: flex;\n  align-items: center;\n  color: #dc3545;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n.warning-icon {\n  margin-right: 4px;\n  font-size: 14px;\n}\n\n/* 商品设置面板 */\n.goods-settings-panel {\n  border-top: 1px solid #e9ecef;\n  padding-top: 15px;\n  margin-top: 15px;\n}\n\n.settings-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 12px;\n  display: flex;\n  align-items: center;\n}\n\n.settings-title::before {\n  content: \"⚙\";\n  margin-right: 6px;\n  color: #007bff;\n}\n\n.settings-grid {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 12px;\n}\n\n.setting-item {\n  display: flex;\n  flex-direction: column;\n}\n\n.setting-item.full-width {\n  grid-column: 1 / -1;\n}\n\n.setting-item label {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 4px;\n  font-weight: 500;\n}\n\n/* 价格输入组 */\n.price-input-group {\n  display: flex;\n  align-items: center;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  overflow: hidden;\n  background: white;\n}\n\n.currency {\n  background-color: #f8f9fa;\n  padding: 6px 8px;\n  border-right: 1px solid #ddd;\n  font-size: 14px;\n  color: #666;\n}\n\n.price-input {\n  border: none;\n  padding: 6px 8px;\n  font-size: 14px;\n  flex: 1;\n  outline: none;\n}\n\n.price-input:focus {\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.discount-display {\n  margin-top: 4px;\n  font-size: 12px;\n  color: #e74c3c;\n  font-weight: bold;\n  text-align: center;\n  background-color: #fff5f5;\n  padding: 2px 6px;\n  border-radius: 12px;\n  border: 1px solid #fecaca;\n}\n\n/* 库存和限购输入 */\n.stock-input,\n.limit-input {\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  padding: 6px 8px;\n  font-size: 14px;\n  outline: none;\n  background: white;\n}\n\n.stock-input:focus,\n.limit-input:focus {\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n/* 折扣设置样式 */\n.discount-setting {\n  background: #f8f9fa;\n  padding: 12px;\n  border-radius: 6px;\n  border: 1px solid #e9ecef;\n}\n\n.discount-input-group {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.discount-input {\n  width: 80px;\n  padding: 6px 8px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n  text-align: center;\n  font-weight: 600;\n}\n\n.discount-unit {\n  margin-left: 8px;\n  font-size: 14px;\n  font-weight: 600;\n  color: #dc3545;\n}\n\n.price-preview {\n  font-size: 13px;\n  color: #666;\n  margin-bottom: 4px;\n}\n\n.price-range-hint {\n  font-size: 12px;\n  color: #28a745;\n  font-style: italic;\n}\n\n/* 选择提示 */\n.selection-hint {\n  margin-top: 15px;\n  padding: 12px;\n  background-color: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #007bff;\n}\n\n.hint-text {\n  margin: 0;\n  color: #666;\n  font-size: 14px;\n  display: flex;\n  align-items: center;\n}\n\n.info-icon {\n  margin-right: 8px;\n  color: #007bff;\n  font-size: 16px;\n}\n\n.selected-count {\n  margin: 0;\n  color: #333;\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.selected-count strong {\n  color: #28a745;\n  font-size: 16px;\n}\n\n/* 已选商品汇总 */\n.selected-summary {\n  background-color: #f8f9fa;\n  padding: 15px;\n  border-radius: 4px;\n  margin-top: 20px;\n}\n\n.selected-summary h4 {\n  margin: 0 0 15px 0;\n  color: #333;\n}\n\n.summary-list {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.summary-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px;\n  background: white;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.remove-btn {\n  background-color: #dc3545;\n  color: white;\n  border: none;\n  padding: 4px 8px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 12px;\n}\n\n/* 轮次详情 */\n.round-details {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.detail-section h4 {\n  margin: 0 0 15px 0;\n  color: #333;\n  border-bottom: 2px solid #007bff;\n  padding-bottom: 5px;\n}\n\n.detail-section p {\n  margin: 5px 0;\n  color: #666;\n}\n\n/* 加载状态样式 */\n.loading-state {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40px 20px;\n  background: #f8f9fa;\n  border: 2px dashed #dee2e6;\n  border-radius: 8px;\n  color: #6c757d;\n  font-size: 16px;\n}\n\n.loading-state .loading-icon {\n  margin-right: 10px;\n  font-size: 20px;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n/* 空状态样式 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60px 20px;\n  background: #f8f9fa;\n  border: 2px dashed #dee2e6;\n  border-radius: 8px;\n  text-align: center;\n}\n\n.empty-state .empty-icon {\n  font-size: 48px;\n  margin-bottom: 16px;\n  opacity: 0.6;\n}\n\n.empty-state .empty-text {\n  font-size: 18px;\n  font-weight: 500;\n  color: #495057;\n  margin: 0 0 8px 0;\n}\n\n.empty-state .empty-hint {\n  font-size: 14px;\n  color: #6c757d;\n  margin: 0 0 20px 0;\n  line-height: 1.5;\n}\n\n.empty-state .btn-sm {\n  padding: 6px 16px;\n  font-size: 14px;\n}\n\n.goods-cell {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.goods-cell img {\n  width: 40px;\n  height: 40px;\n  object-fit: cover;\n  border-radius: 4px;\n}\n\n.goods-cell span {\n  font-size: 14px;\n}\n</style>\n"]}]}