const path = require('path');
const fs = require('fs');
const Application = require('thinkjs');

// 跨平台路径处理
const ROOT_PATH = __dirname;
const APP_PATH = path.join(ROOT_PATH, 'app');
const SRC_PATH = path.join(ROOT_PATH, 'src');

// 检查编译后的文件是否存在
function checkCompiledFiles() {
  const authControllerPath = path.join(APP_PATH, 'admin', 'controller', 'auth.js');
  
  if (!fs.existsSync(authControllerPath)) {
    console.log('编译后的文件不存在，正在重新编译...');
    
    // 如果是开发环境，使用babel编译
    if (process.env.NODE_ENV !== 'production') {
      const babel = require('think-babel');
      const watcher = require('think-watcher');
      const notifier = require('node-notifier');
      
      const instance = new Application({
        ROOT_PATH: ROOT_PATH,
        watcher: watcher,
        transpiler: [babel, {
          presets: ['think-node']
        }],
        notifier: notifier.notify.bind(notifier),
        env: 'development'
      });
      
      return instance;
    } else {
      // 生产环境
      const instance = new Application({
        ROOT_PATH: ROOT_PATH,
        proxy: true,
        env: 'production'
      });
      
      return instance;
    }
  }
  
  // 文件存在，正常启动
  if (process.env.NODE_ENV === 'production') {
    const instance = new Application({
      ROOT_PATH: ROOT_PATH,
      proxy: true,
      env: 'production'
    });
    
    return instance;
  } else {
    const babel = require('think-babel');
    const watcher = require('think-watcher');
    const notifier = require('node-notifier');
    
    const instance = new Application({
      ROOT_PATH: ROOT_PATH,
      watcher: watcher,
      transpiler: [babel, {
        presets: ['think-node']
      }],
      notifier: notifier.notify.bind(notifier),
      env: 'development'
    });
    
    return instance;
  }
}

// 启动应用
const instance = checkCompiledFiles();
instance.run();
