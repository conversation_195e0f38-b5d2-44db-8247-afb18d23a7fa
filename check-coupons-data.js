const mysql = require('mysql2/promise');

async function checkCouponsData() {
  const connection = await mysql.createConnection({
    host: '127.0.0.1',
    port: 3306,
    user: 'root',
    password: '123456',
    database: 'hiolabsDB'
  });

  try {
    console.log('=== 检查优惠券数据 ===');
    
    // 检查优惠券表是否存在
    const [tables] = await connection.execute("SHOW TABLES LIKE 'hiolabs_coupons'");
    console.log('优惠券表存在:', tables.length > 0);
    
    if (tables.length > 0) {
      // 查询所有优惠券
      const [coupons] = await connection.execute('SELECT * FROM hiolabs_coupons WHERE is_delete = 0');
      console.log('优惠券总数:', coupons.length);
      
      if (coupons.length > 0) {
        console.log('\n=== 优惠券列表 ===');
        coupons.forEach(coupon => {
          console.log(`ID: ${coupon.id}, 名称: ${coupon.name}, 类型: ${coupon.type}, 状态: ${coupon.status}`);
          console.log(`  优惠值: ${coupon.discount_value}, 最低金额: ${coupon.min_amount}`);
          console.log(`  有效期: ${coupon.start_time} 至 ${coupon.end_time}`);
          console.log('---');
        });
        
        // 检查当前有效的优惠券
        const now = new Date().toISOString().slice(0, 19).replace('T', ' ');
        const [activeCoupons] = await connection.execute(`
          SELECT * FROM hiolabs_coupons 
          WHERE status = 'active' 
          AND start_time <= ? 
          AND end_time >= ? 
          AND is_delete = 0
        `, [now, now]);
        
        console.log('\n=== 当前有效优惠券 ===');
        console.log('有效优惠券数量:', activeCoupons.length);
        activeCoupons.forEach(coupon => {
          console.log(`- ${coupon.name} (${coupon.type}): ${coupon.discount_value}元`);
        });
      }
    }
    
  } catch (error) {
    console.error('检查失败:', error);
  } finally {
    await connection.end();
  }
}

checkCouponsData();
