const mysql = require('mysql2/promise');

async function createTestCoupons() {
  const connection = await mysql.createConnection({
    host: '127.0.0.1',
    port: 3306,
    user: 'root',
    password: '123456',
    database: 'hiolabsDB'
  });

  try {
    console.log('=== 创建测试优惠券 ===');
    
    // 先清空现有数据
    await connection.execute('DELETE FROM hiolabs_coupons');
    console.log('清空现有优惠券数据');
    
    // 创建测试优惠券
    const testCoupons = [
      {
        name: '新用户专享券',
        code: 'NEW2024',
        type: 'newuser',
        discount_type: 'fixed',
        discount_value: 20.00,
        min_amount: 100.00,
        total_quantity: -1,
        per_user_limit: 1,
        start_time: '2024-01-01 00:00:00',
        end_time: '2024-12-31 23:59:59',
        status: 'active',
        description: '新用户注册即可领取，满100元减20元',
        auto_distribute: 1
      },
      {
        name: '满减优惠券50',
        code: 'SAVE50',
        type: 'full_reduction',
        discount_type: 'fixed',
        discount_value: 50.00,
        min_amount: 299.00,
        total_quantity: 1000,
        per_user_limit: 2,
        start_time: '2024-01-01 00:00:00',
        end_time: '2024-12-31 23:59:59',
        status: 'active',
        description: '满299元减50元，限量1000张',
        auto_distribute: 0
      },
      {
        name: '九折优惠券',
        code: 'DISCOUNT10',
        type: 'full_reduction',
        discount_type: 'percent',
        discount_value: 10.00,
        min_amount: 200.00,
        max_discount: 50.00,
        total_quantity: 2000,
        per_user_limit: 3,
        start_time: '2024-01-01 00:00:00',
        end_time: '2024-12-31 23:59:59',
        status: 'active',
        description: '满200元享9折优惠，最高优惠50元',
        auto_distribute: 0
      }
    ];
    
    for (let coupon of testCoupons) {
      const [result] = await connection.execute(`
        INSERT INTO hiolabs_coupons 
        (name, code, type, discount_type, discount_value, min_amount, max_discount, 
         total_quantity, per_user_limit, start_time, end_time, status, description, auto_distribute)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        coupon.name, coupon.code, coupon.type, coupon.discount_type, 
        coupon.discount_value, coupon.min_amount, coupon.max_discount || null,
        coupon.total_quantity, coupon.per_user_limit, coupon.start_time, 
        coupon.end_time, coupon.status, coupon.description, coupon.auto_distribute
      ]);
      
      console.log(`✅ 创建优惠券: ${coupon.name} (ID: ${result.insertId})`);
    }
    
    // 验证创建结果
    const [coupons] = await connection.execute('SELECT * FROM hiolabs_coupons WHERE is_delete = 0');
    console.log(`\n✅ 总共创建了 ${coupons.length} 个优惠券`);
    
    coupons.forEach(coupon => {
      console.log(`- ${coupon.name} (${coupon.type}): ${coupon.discount_value}${coupon.discount_type === 'fixed' ? '元' : '%'}`);
    });
    
  } catch (error) {
    console.error('❌ 创建失败:', error);
  } finally {
    await connection.end();
  }
}

createTestCoupons();
