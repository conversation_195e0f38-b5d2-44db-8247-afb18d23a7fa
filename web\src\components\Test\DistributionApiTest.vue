<template>
  <div class="api-test-page">
    <div class="container mx-auto p-6">
      <h1 class="text-2xl font-bold mb-6">分销API测试页面</h1>
      
      <!-- 测试按钮 -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <button @click="testGetGoodsList" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
          测试商品列表
        </button>
        <button @click="testGetCategories" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
          测试分类列表
        </button>
        <button @click="testGetStats" class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">
          测试统计数据
        </button>
        <button @click="testGetRules" class="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700">
          测试佣金规则
        </button>
      </div>
      
      <!-- 结果显示 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold mb-4">测试结果</h2>
        <div class="bg-gray-100 p-4 rounded">
          <pre class="text-sm overflow-auto">{{ testResult }}</pre>
        </div>
      </div>
      
      <!-- 错误信息 -->
      <div v-if="errorMessage" class="mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <strong>错误:</strong> {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script>
import { 
  getGoodsList, 
  getCategoryList, 
  getDistributionStats,
  getCommissionRules
} from '@/api/distribution'

export default {
  name: 'DistributionApiTest',
  data() {
    return {
      testResult: '点击上方按钮开始测试API...\n注意：所有数据都来自真实的数据库查询，没有任何模拟数据。',
      errorMessage: ''
    }
  },
  methods: {
    async testGetGoodsList() {
      try {
        this.errorMessage = '';
        this.testResult = '正在测试商品列表API...';
        
        const response = await getGoodsList({
          page: 1,
          size: 10
        });
        
        this.testResult = JSON.stringify(response.data, null, 2);
      } catch (error) {
        this.errorMessage = `商品列表API测试失败: ${error.message}`;
        this.testResult = JSON.stringify(error.response?.data || error, null, 2);
      }
    },
    
    async testGetCategories() {
      try {
        this.errorMessage = '';
        this.testResult = '正在测试分类列表API...';
        
        const response = await getCategoryList();
        
        this.testResult = JSON.stringify(response.data, null, 2);
      } catch (error) {
        this.errorMessage = `分类列表API测试失败: ${error.message}`;
        this.testResult = JSON.stringify(error.response?.data || error, null, 2);
      }
    },
    
    async testGetStats() {
      try {
        this.errorMessage = '';
        this.testResult = '正在测试统计数据API...';
        
        const response = await getDistributionStats();
        
        this.testResult = JSON.stringify(response.data, null, 2);
      } catch (error) {
        this.errorMessage = `统计数据API测试失败: ${error.message}`;
        this.testResult = JSON.stringify(error.response?.data || error, null, 2);
      }
    },
    
    async testGetRules() {
      try {
        this.errorMessage = '';
        this.testResult = '正在测试佣金规则API...';
        
        const response = await getCommissionRules();
        
        this.testResult = JSON.stringify(response.data, null, 2);
      } catch (error) {
        this.errorMessage = `佣金规则API测试失败: ${error.message}`;
        this.testResult = JSON.stringify(error.response?.data || error, null, 2);
      }
    }
  }
}
</script>

<style scoped>
.api-test-page {
  min-height: 100vh;
  background-color: #f9fafb;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 400px;
  overflow-y: auto;
}
</style>
