<template>
  <div class="order-gifts-page">
    <!-- 页面头部 -->
    <div class="page-header bg-white shadow-sm border-b">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">订单有礼</h1>
            <p class="text-sm text-gray-500 mt-1">公域订单号兑换积分，引流用户到私域小程序</p>
          </div>
          <div class="flex items-center space-x-3">
            <button @click="showConfigModal = true" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <i class="ri-settings-line mr-2"></i>
              兑换配置
            </button>
            <button @click="exportData" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
              <i class="ri-download-line mr-2"></i>
              导出数据
            </button>
            <button @click="refreshData" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <i class="ri-refresh-line mr-2"></i>
              刷新数据
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="p-6 max-w-7xl mx-auto">
      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="ri-file-list-line text-xl text-blue-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">兑换订单总数</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.totalOrders }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="ri-check-line text-xl text-green-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">成功兑换</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.successfulExchanges }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <i class="ri-coin-line text-xl text-yellow-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">发放积分总数</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.totalPoints }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <i class="ri-user-add-line text-xl text-purple-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">新增用户</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.newUsers }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 筛选和搜索 -->
      <div class="bg-white rounded-lg shadow-sm border mb-6">
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">搜索订单</label>
              <input
                v-model="searchQuery"
                type="text"
                placeholder="输入订单号或用户手机号"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">兑换状态</label>
              <select
                v-model="filterStatus"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">全部状态</option>
                <option value="success">兑换成功</option>
                <option value="failed">兑换失败</option>
                <option value="pending">待处理</option>
                <option value="duplicate">重复兑换</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">订单来源</label>
              <select
                v-model="filterSource"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">全部来源</option>
                <option value="taobao">淘宝</option>
                <option value="tmall">天猫</option>
                <option value="jd">京东</option>
                <option value="pdd">拼多多</option>
                <option value="douyin">抖音</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
              <select
                v-model="filterTime"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">全部时间</option>
                <option value="today">今天</option>
                <option value="week">本周</option>
                <option value="month">本月</option>
                <option value="quarter">本季度</option>
              </select>
            </div>

            <div class="flex items-end">
              <button @click="resetFilters" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                <i class="ri-refresh-line mr-2"></i>
                重置
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 兑换订单列表 -->
      <div class="bg-white rounded-lg shadow-sm border">
        <div class="px-6 py-4 border-b">
          <h2 class="text-lg font-semibold text-gray-900">订单兑换记录</h2>
        </div>

        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单信息</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户信息</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单来源</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单金额</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">兑换积分</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">兑换时间</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <!-- 数据行 -->
              <tr v-for="order in filteredOrders" :key="order.id" class="hover:bg-gray-50">
                <!-- 订单信息 -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ order.orderNumber }}</div>
                    <div class="text-sm text-gray-500">{{ order.orderDate }}</div>
                  </div>
                </td>

                <!-- 用户信息 -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <img :src="order.userAvatar || '/default-avatar.png'" :alt="order.userName" class="w-8 h-8 rounded-full object-cover">
                    <div class="ml-3">
                      <div class="text-sm font-medium text-gray-900">{{ order.userName }}</div>
                      <div class="text-sm text-gray-500">{{ order.userPhone }}</div>
                    </div>
                  </div>
                </td>

                <!-- 订单来源 -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="[
                    'px-2 py-1 text-xs rounded-full',
                    order.source === 'taobao' ? 'bg-orange-100 text-orange-800' :
                    order.source === 'tmall' ? 'bg-red-100 text-red-800' :
                    order.source === 'jd' ? 'bg-red-100 text-red-800' :
                    order.source === 'pdd' ? 'bg-orange-100 text-orange-800' :
                    order.source === 'douyin' ? 'bg-black text-white' :
                    'bg-gray-100 text-gray-800'
                  ]">
                    {{ getSourceLabel(order.source) }}
                  </span>
                </td>

                <!-- 订单金额 -->
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  ¥{{ order.orderAmount }}
                </td>

                <!-- 兑换积分 -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-yellow-600">{{ order.exchangePoints }}</div>
                  <div class="text-xs text-gray-500">{{ order.pointsRule }}</div>
                </td>

                <!-- 兑换时间 -->
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ order.exchangeTime }}
                </td>

                <!-- 状态 -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="[
                    'px-2 py-1 text-xs rounded-full',
                    order.status === 'success' ? 'bg-green-100 text-green-800' :
                    order.status === 'failed' ? 'bg-red-100 text-red-800' :
                    order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  ]">
                    {{ getStatusLabel(order.status) }}
                  </span>
                  <div v-if="order.failReason" class="text-xs text-red-500 mt-1">{{ order.failReason }}</div>
                </td>

                <!-- 操作 -->
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button @click="viewOrderDetail(order)" class="text-blue-600 hover:text-blue-900">详情</button>
                  <button v-if="order.status === 'failed'" @click="retryExchange(order)" class="text-green-600 hover:text-green-900">重试</button>
                  <button v-if="order.status === 'success'" @click="revokePoints(order)" class="text-red-600 hover:text-red-900">撤销</button>
                </td>
              </tr>

              <!-- 空状态 -->
              <tr v-if="filteredOrders.length === 0 && !loading">
                <td colspan="8" class="px-6 py-12 text-center">
                  <div class="flex flex-col items-center">
                    <i class="ri-file-list-line text-4xl text-gray-300 mb-4"></i>
                    <p class="text-gray-500 text-lg mb-2">暂无兑换记录</p>
                    <p class="text-gray-400 text-sm">用户兑换订单后，记录将显示在这里</p>
                  </div>
                </td>
              </tr>

              <!-- 加载状态 -->
              <tr v-if="loading">
                <td colspan="8" class="px-6 py-12 text-center">
                  <div class="flex flex-col items-center">
                    <i class="ri-loader-4-line text-2xl text-blue-500 animate-spin mb-2"></i>
                    <p class="text-gray-500">加载中...</p>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div class="px-6 py-4 border-t bg-gray-50" v-if="pagination.total > 0">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500">
              显示 {{ Math.min(filteredOrders.length, pagination.pageSize) }} 条，共 {{ pagination.total }} 条记录
            </div>
            <div class="flex items-center space-x-2">
              <button
                @click="changePage(pagination.current - 1)"
                :disabled="pagination.current <= 1"
                :class="[
                  'px-3 py-1 border rounded-md text-sm',
                  pagination.current <= 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'hover:bg-gray-100'
                ]"
              >
                上一页
              </button>
              <span class="px-3 py-1 text-sm">{{ pagination.current }} / {{ Math.ceil(pagination.total / pagination.pageSize) }}</span>
              <button
                @click="changePage(pagination.current + 1)"
                :disabled="pagination.current >= Math.ceil(pagination.total / pagination.pageSize)"
                :class="[
                  'px-3 py-1 border rounded-md text-sm',
                  pagination.current >= Math.ceil(pagination.total / pagination.pageSize) ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'hover:bg-gray-100'
                ]"
              >
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 兑换配置弹窗 -->
    <div v-if="showConfigModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-3xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">订单兑换配置</h3>
          <button @click="showConfigModal = false" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>

        <div class="space-y-6">
          <!-- 基础配置 -->
          <div class="border-b pb-4">
            <h4 class="text-md font-medium text-gray-900 mb-4">基础配置</h4>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">功能状态</label>
                <select
                  v-model="exchangeConfig.enabled"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option :value="true">启用</option>
                  <option :value="false">停用</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">每日兑换限制</label>
                <input
                  v-model="exchangeConfig.dailyLimit"
                  type="number"
                  placeholder="每日最多兑换次数"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          <!-- 积分规则 -->
          <div class="border-b pb-4">
            <h4 class="text-md font-medium text-gray-900 mb-4">积分兑换规则</h4>
            <div class="space-y-4">
              <div class="grid grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">最低订单金额</label>
                  <input
                    v-model="exchangeConfig.minOrderAmount"
                    type="number"
                    step="0.01"
                    placeholder="最低金额"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">兑换比例</label>
                  <input
                    v-model="exchangeConfig.exchangeRate"
                    type="number"
                    step="0.01"
                    placeholder="1元=?积分"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">最高兑换积分</label>
                  <input
                    v-model="exchangeConfig.maxPoints"
                    type="number"
                    placeholder="单次最高积分"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 支持平台 -->
          <div class="border-b pb-4">
            <h4 class="text-md font-medium text-gray-900 mb-4">支持的订单来源</h4>
            <div class="grid grid-cols-3 gap-4">
              <label class="flex items-center">
                <input v-model="exchangeConfig.supportedPlatforms" value="taobao" type="checkbox" class="mr-2">
                <span class="text-sm">淘宝</span>
              </label>
              <label class="flex items-center">
                <input v-model="exchangeConfig.supportedPlatforms" value="tmall" type="checkbox" class="mr-2">
                <span class="text-sm">天猫</span>
              </label>
              <label class="flex items-center">
                <input v-model="exchangeConfig.supportedPlatforms" value="jd" type="checkbox" class="mr-2">
                <span class="text-sm">京东</span>
              </label>
              <label class="flex items-center">
                <input v-model="exchangeConfig.supportedPlatforms" value="pdd" type="checkbox" class="mr-2">
                <span class="text-sm">拼多多</span>
              </label>
              <label class="flex items-center">
                <input v-model="exchangeConfig.supportedPlatforms" value="douyin" type="checkbox" class="mr-2">
                <span class="text-sm">抖音</span>
              </label>
            </div>
          </div>

          <div class="flex justify-end space-x-3 pt-4">
            <button @click="showConfigModal = false" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
              取消
            </button>
            <button @click="saveConfig" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
              保存配置
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OrderGiftsPage',
  data() {
    return {
      // 搜索和筛选
      searchQuery: '',
      filterStatus: '',
      filterSource: '',
      filterTime: '',

      // 弹窗状态
      showConfigModal: false,

      // 统计数据
      statistics: {
        totalOrders: 0,
        successfulExchanges: 0,
        totalPoints: 0,
        newUsers: 0
      },

      // 兑换配置
      exchangeConfig: {
        enabled: false,
        dailyLimit: null,
        minOrderAmount: null,
        exchangeRate: null,
        maxPoints: null,
        supportedPlatforms: []
      },

      // 兑换订单列表
      orders: [],

      // 加载状态
      loading: false,

      // 分页信息
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0
      }
    }
  },

  computed: {
    filteredOrders() {
      let filtered = this.orders;

      if (this.searchQuery) {
        filtered = filtered.filter(order =>
          order.orderNumber.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
          order.userPhone.includes(this.searchQuery)
        );
      }

      if (this.filterStatus) {
        filtered = filtered.filter(order => order.status === this.filterStatus);
      }

      if (this.filterSource) {
        filtered = filtered.filter(order => order.source === this.filterSource);
      }

      // 时间筛选逻辑可以在这里添加
      if (this.filterTime) {
        // 根据filterTime值进行时间筛选
      }

      return filtered;
    }
  },

  watch: {
    // 监听搜索和筛选条件变化
    searchQuery() {
      this.pagination.current = 1;
      this.loadOrders();
    },
    filterStatus() {
      this.pagination.current = 1;
      this.loadOrders();
    },
    filterSource() {
      this.pagination.current = 1;
      this.loadOrders();
    },
    filterTime() {
      this.pagination.current = 1;
      this.loadOrders();
    }
  },

  mounted() {
    console.log('订单有礼页面已加载');
    this.loadStatistics();
    this.loadExchangeConfig();
    this.loadOrders();
  },

  methods: {
    // 加载统计数据
    async loadStatistics() {
      try {
        // TODO: 调用API获取统计数据
        // const response = await this.axios.get('/api/order-exchange/statistics');
        // this.statistics = response.data;
        console.log('加载统计数据');
      } catch (error) {
        console.error('加载统计数据失败:', error);
        this.$message.error('加载统计数据失败');
      }
    },

    // 加载兑换配置
    async loadExchangeConfig() {
      try {
        // TODO: 调用API获取兑换配置
        // const response = await this.axios.get('/api/order-exchange/config');
        // this.exchangeConfig = response.data;
        console.log('加载兑换配置');
      } catch (error) {
        console.error('加载兑换配置失败:', error);
        this.$message.error('加载兑换配置失败');
      }
    },

    // 加载订单列表
    async loadOrders() {
      this.loading = true;
      try {
        // TODO: 调用API获取订单列表
        // const params = {
        //   page: this.pagination.current,
        //   pageSize: this.pagination.pageSize,
        //   search: this.searchQuery,
        //   status: this.filterStatus,
        //   source: this.filterSource,
        //   timeRange: this.filterTime
        // };
        // const response = await this.axios.get('/api/order-exchange/records', { params });
        // this.orders = response.data.list;
        // this.pagination.total = response.data.total;
        console.log('加载订单列表');
      } catch (error) {
        console.error('加载订单列表失败:', error);
        this.$message.error('加载订单列表失败');
      } finally {
        this.loading = false;
      }
    },

    // 分页切换
    changePage(page) {
      if (page < 1 || page > Math.ceil(this.pagination.total / this.pagination.pageSize)) {
        return;
      }
      this.pagination.current = page;
      this.loadOrders();
    },

    // 获取来源标签
    getSourceLabel(source) {
      const labels = {
        taobao: '淘宝',
        tmall: '天猫',
        jd: '京东',
        pdd: '拼多多',
        douyin: '抖音'
      };
      return labels[source] || source;
    },

    // 获取状态标签
    getStatusLabel(status) {
      const labels = {
        success: '兑换成功',
        failed: '兑换失败',
        pending: '待处理',
        duplicate: '重复兑换'
      };
      return labels[status] || status;
    },

    // 重置筛选
    resetFilters() {
      this.searchQuery = '';
      this.filterStatus = '';
      this.filterSource = '';
      this.filterTime = '';
      this.pagination.current = 1;
      this.loadOrders();
    },

    // 查看订单详情
    viewOrderDetail(order) {
      console.log('查看订单详情:', order);
      // TODO: 实现订单详情查看功能
      // 可以打开详情弹窗或跳转到详情页面
    },

    // 重试兑换
    async retryExchange(order) {
      try {
        // TODO: 调用API重试兑换
        // await this.axios.post(`/api/order-exchange/retry/${order.id}`);
        console.log('重试兑换:', order);
        this.$message.success('重试成功');
        this.loadOrders();
      } catch (error) {
        console.error('重试失败:', error);
        this.$message.error('重试失败');
      }
    },

    // 撤销积分
    revokePoints(order) {
      this.$confirm('确定要撤销该订单的积分吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // TODO: 调用API撤销积分
          // await this.axios.post(`/api/order-exchange/revoke/${order.id}`);
          console.log('撤销积分:', order);
          this.$message.success('积分撤销成功');
          this.loadOrders();
          this.loadStatistics();
        } catch (error) {
          console.error('撤销失败:', error);
          this.$message.error('撤销失败');
        }
      }).catch(() => {
        this.$message.info('已取消撤销');
      });
    },

    // 保存配置
    async saveConfig() {
      try {
        // TODO: 调用API保存配置
        // await this.axios.post('/api/order-exchange/config', this.exchangeConfig);
        console.log('保存配置:', this.exchangeConfig);
        this.showConfigModal = false;
        this.$message.success('配置保存成功');
      } catch (error) {
        console.error('保存配置失败:', error);
        this.$message.error('保存配置失败');
      }
    },

    // 导出数据
    async exportData() {
      try {
        // TODO: 调用API导出数据
        // const response = await this.axios.get('/api/order-exchange/export', {
        //   params: {
        //     search: this.searchQuery,
        //     status: this.filterStatus,
        //     source: this.filterSource,
        //     timeRange: this.filterTime
        //   },
        //   responseType: 'blob'
        // });
        console.log('导出数据');
        this.$message.success('导出成功');
      } catch (error) {
        console.error('导出失败:', error);
        this.$message.error('导出失败');
      }
    },

    // 刷新数据
    refreshData() {
      this.loadStatistics();
      this.loadOrders();
      this.$message.success('数据刷新成功');
    }
  }
}
</script>

<style scoped>
.order-gifts-page {
  min-height: 100vh;
  background-color: #f9fafb;
}

.page-header {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 表格样式 */
.overflow-x-auto {
  overflow-x: auto;
}

table {
  min-width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-1.md\\:grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
</style>
