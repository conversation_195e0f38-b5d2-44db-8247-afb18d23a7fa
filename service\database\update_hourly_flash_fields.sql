-- 为秒杀轮次表添加整点秒杀相关字段
-- 执行时间：2025-07-30

USE hiolabsdb;

-- 添加整点秒杀标识字段
ALTER TABLE `hiolabs_flash_sale_rounds` 
ADD COLUMN `is_hourly_flash` TINYINT(1) DEFAULT 0 COMMENT '是否为整点秒杀轮次' AFTER `status`;

-- 添加时段索引字段
ALTER TABLE `hiolabs_flash_sale_rounds` 
ADD COLUMN `slot_index` INT(11) DEFAULT NULL COMMENT '时段索引（第几场）' AFTER `is_hourly_flash`;

-- 添加总时段数字段
ALTER TABLE `hiolabs_flash_sale_rounds` 
ADD COLUMN `total_slots` INT(11) DEFAULT NULL COMMENT '总时段数' AFTER `slot_index`;

-- 添加索引优化查询性能
ALTER TABLE `hiolabs_flash_sale_rounds` 
ADD INDEX `idx_hourly_flash` (`is_hourly_flash`, `slot_index`);

-- 查看表结构
DESCRIBE `hiolabs_flash_sale_rounds`;

-- 验证字段添加成功
SELECT 
  COLUMN_NAME, 
  DATA_TYPE, 
  IS_NULLABLE, 
  COLUMN_DEFAULT, 
  COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'hiolabsdb' 
  AND TABLE_NAME = 'hiolabs_flash_sale_rounds' 
  AND COLUMN_NAME IN ('is_hourly_flash', 'slot_index', 'total_slots');
