import axios from 'axios'

// 获取所有商品列表（用于分销池）
export function getGoodsList(params) {
  return axios({
    url: '/distribution/goodsList',
    method: 'get',
    params
  })
}

// 获取商品分类列表
export function getCategoryList() {
  return axios({
    url: '/distribution/categories',
    method: 'get'
  })
}

// 获取分销配置
export function getDistributionConfig(params) {
  return axios({
    url: '/distribution/config',
    method: 'get',
    params
  })
}

// 设置商品分销状态
export function setDistributionStatus(data) {
  return axios({
    url: '/distribution/status',
    method: 'post',
    data
  })
}

// 设置商品佣金
export function setCommission(data) {
  return axios({
    url: '/distribution/commission',
    method: 'post',
    data
  })
}

// 获取分销统计数据
export function getDistributionStats() {
  return axios({
    url: '/distribution/stats',
    method: 'get'
  })
}

// 批量设置分销状态
export function batchSetDistribution(data) {
  return axios({
    url: '/distribution/batch',
    method: 'post',
    data
  })
}

// 获取佣金规则
export function getCommissionRules() {
  return axios({
    url: '/distribution/rules',
    method: 'get'
  })
}

// 更新佣金规则
export function updateCommissionRules(data) {
  return axios({
    url: '/distribution/rules',
    method: 'put',
    data
  })
}

// 获取招募规则
export function getRecruitmentRules() {
  return axios({
    url: '/distribution/recruitment/rules',
    method: 'get'
  })
}

// 保存招募规则
export function saveRecruitmentRules(data) {
  return axios({
    url: '/distribution/recruitment/rules',
    method: 'post',
    data
  })
}

// 更新招募规则
export function updateRecruitmentRules(data) {
  return axios({
    url: '/distribution/recruitment/rules',
    method: 'put',
    data
  })
}

// 获取分销员列表
export function getDistributorList(params) {
  return axios({
    url: '/distribution/distributors',
    method: 'get',
    params
  })
}

// 获取分销员统计数据
export function getDistributorStats() {
  return axios({
    url: '/distribution/distributors/stats',
    method: 'get'
  })
}

// 获取分销员详情
export function getDistributorDetail(id) {
  return axios({
    url: `/distribution/distributors/${id}`,
    method: 'get'
  })
}

// 更新分销员状态
export function updateDistributorStatus(data) {
  return axios({
    url: '/distribution/distributors/status',
    method: 'post',
    data
  })
}

// 更新分销员等级
export function updateDistributorLevel(data) {
  return axios({
    url: '/distribution/distributors/level',
    method: 'post',
    data
  })
}

// 获取分销员排行榜
export function getDistributorRanking(params) {
  return axios({
    url: '/distribution/distributors/ranking',
    method: 'get',
    params
  })
}

// 获取数据概览
export function getOverviewData(params) {
  return axios({
    url: '/distribution/overview',
    method: 'get',
    params
  })
}

// 获取图表数据
export function getChartData(params) {
  return axios({
    url: '/distribution/chart',
    method: 'get',
    params
  })
}

// 获取热门商品
export function getTopProducts(params) {
  return axios({
    url: '/distribution/top-products',
    method: 'get',
    params
  })
}

// 获取顶级分销员
export function getTopDistributors(params) {
  return axios({
    url: '/distribution/top-distributors',
    method: 'get',
    params
  })
}

// 获取实时数据
export function getRealTimeData() {
  return axios({
    url: '/distribution/realtime',
    method: 'get'
  })
}

// 获取分销池概览
export function getPoolOverview(params) {
  return axios({
    url: '/distribution/poolOverview',
    method: 'get',
    params
  })
}

// 智能推荐分销配置
export function getRecommendConfig(goodsId) {
  return axios({
    url: '/distribution/recommendConfig',
    method: 'get',
    params: { goods_id: goodsId }
  })
}

// 批量投入分销池
export function batchAddToPool(data) {
  return axios({
    url: '/distribution/batch',
    method: 'post',
    data: {
      ...data,
      is_distributed: 1
    }
  })
}

// 批量移出分销池
export function batchRemoveFromPool(data) {
  return axios({
    url: '/distribution/batch',
    method: 'post',
    data: {
      ...data,
      is_distributed: 0
    }
  })
}

// 应用佣金模板到商品
export function applyCommissionTemplate(data) {
  return axios({
    url: '/distribution/batch',
    method: 'post',
    data
  })
}
