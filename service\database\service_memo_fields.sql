-- 为订单表添加客服备注相关字段
-- 执行此SQL来扩展客服备注功能

USE hiolabsdb;

-- 为订单表添加客服备注相关字段
ALTER TABLE `hiolabs_order` 
ADD COLUMN `service_flag_color` varchar(20) DEFAULT NULL COMMENT '客服旗子颜色标识：red紧急，orange重要，yellow注意，green正常，blue跟进，purple特殊',
ADD COLUMN `service_priority` tinyint(1) DEFAULT 0 COMMENT '优先级：0普通，1重要，2紧急',
ADD COLUMN `service_updated_at` int(10) DEFAULT NULL COMMENT '客服备注更新时间',
ADD COLUMN `service_updated_by` varchar(50) DEFAULT NULL COMMENT '客服备注更新人';

-- 为现有订单设置默认值（可选）
-- UPDATE `hiolabs_order` SET `service_priority` = 0 WHERE `service_priority` IS NULL;
