# 商品分销池完整设计方案

## 📋 项目概述

基于现有的个人分销和团队分销体系，设计一个统一的商品分销池管理系统，支持多级分销、灵活佣金配置和分销员等级权限管理。

## 🎯 设计目标

1. **统一管理**：将所有商品的分销配置集中管理
2. **多级佣金**：支持个人推广、一级分销、二级分销、团长奖励等多种佣金类型
3. **等级权限**：基于分销员等级控制商品分销权限
4. **灵活配置**：支持单品定制和批量模板应用
5. **系统兼容**：完全兼容现有的个人分销和团队分销体系

## 🏗️ 系统架构分析

### 现有体系梳理

#### 1. 个人分销体系 (`personal_promoters`)
- **等级体系**：1新手 → 2优秀 → 3金牌 → 4钻石
- **奖励机制**：积分制，每单获得积分
- **升级条件**：基于成交单数 (`total_orders`)
- **数据表**：`hiolabs_personal_promoters`、`hiolabs_promoter_levels`

#### 2. 团队分销体系 (`distributors`)
- **等级体系**：普通 → 银牌 → 金牌 → 钻石分销商
- **层级关系**：通过 `parent_id` 建立上下级关系
- **佣金机制**：基础佣金 + 等级加成
- **数据表**：`hiolabs_distributors`、`hiolabs_distributor_levels`

#### 3. 商品分销配置 (`goods_distribution`)
- **基础配置**：是否分销、佣金比例、佣金类型
- **等级限制**：最低分销等级要求
- **状态管理**：启用/禁用状态

## 📊 数据库结构设计

### 1. 扩展现有表结构

#### 1.1 商品分销配置表增强 (`hiolabs_goods_distribution`)

```sql
-- 新增字段
ALTER TABLE `hiolabs_goods_distribution` 
ADD COLUMN `personal_rate` decimal(5,2) DEFAULT '0.00' COMMENT '个人推广佣金比例(%)',
ADD COLUMN `level1_rate` decimal(5,2) DEFAULT '0.00' COMMENT '一级分销佣金比例(%)',
ADD COLUMN `level2_rate` decimal(5,2) DEFAULT '0.00' COMMENT '二级分销佣金比例(%)',
ADD COLUMN `team_leader_rate` decimal(5,2) DEFAULT '0.00' COMMENT '团长额外佣金比例(%)',
ADD COLUMN `min_level_required` tinyint(1) DEFAULT '1' COMMENT '最低分销等级要求',
ADD COLUMN `max_level_allowed` tinyint(1) DEFAULT '4' COMMENT '最高分销等级限制',
ADD COLUMN `priority` int(10) DEFAULT '0' COMMENT '分销优先级',
ADD COLUMN `start_time` int(10) unsigned DEFAULT '0' COMMENT '分销开始时间',
ADD COLUMN `end_time` int(10) unsigned DEFAULT '0' COMMENT '分销结束时间',
ADD COLUMN `daily_limit` int(10) DEFAULT '0' COMMENT '每日分销限额',
ADD COLUMN `total_limit` int(10) DEFAULT '0' COMMENT '总分销限额',
ADD COLUMN `current_count` int(10) DEFAULT '0' COMMENT '当前分销数量';
```

### 2. 新增核心表结构

#### 2.1 分销佣金规则模板表 (`hiolabs_commission_templates`)

```sql
CREATE TABLE `hiolabs_commission_templates` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_code` varchar(50) NOT NULL COMMENT '模板代码',
  `category_id` int(10) unsigned DEFAULT '0' COMMENT '适用分类ID',
  `price_min` decimal(10,2) DEFAULT '0.00' COMMENT '价格区间最小值',
  `price_max` decimal(10,2) DEFAULT '999999.99' COMMENT '价格区间最大值',
  `personal_rate` decimal(5,2) DEFAULT '0.00' COMMENT '个人推广佣金比例',
  `level1_rate` decimal(5,2) DEFAULT '0.00' COMMENT '一级分销佣金比例',
  `level2_rate` decimal(5,2) DEFAULT '0.00' COMMENT '二级分销佣金比例',
  `team_leader_rate` decimal(5,2) DEFAULT '0.00' COMMENT '团长额外佣金比例',
  `min_level_required` tinyint(1) DEFAULT '1' COMMENT '最低等级要求',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认模板',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  PRIMARY KEY (`id`)
) COMMENT='分销佣金规则模板表';
```

#### 2.2 分销员关系表 (`hiolabs_distributor_relations`)

```sql
CREATE TABLE `hiolabs_distributor_relations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL COMMENT '分销员用户ID',
  `parent_id` int(10) unsigned DEFAULT '0' COMMENT '上级分销员用户ID',
  `level` tinyint(1) DEFAULT '1' COMMENT '在关系链中的层级',
  `relation_path` varchar(500) DEFAULT '' COMMENT '关系路径',
  `team_leader_id` int(10) unsigned DEFAULT '0' COMMENT '所属团长ID',
  `join_time` int(10) unsigned NOT NULL COMMENT '建立关系时间',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '关系是否有效',
  PRIMARY KEY (`id`)
) COMMENT='分销员关系表';
```

#### 2.3 分销佣金记录表 (`hiolabs_distribution_commissions`)

```sql
CREATE TABLE `hiolabs_distribution_commissions` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` int(10) unsigned NOT NULL COMMENT '订单ID',
  `goods_id` int(10) unsigned NOT NULL COMMENT '商品ID',
  `buyer_user_id` int(10) unsigned NOT NULL COMMENT '购买者用户ID',
  `promoter_user_id` int(10) unsigned NOT NULL COMMENT '推广员用户ID',
  `commission_type` enum('personal','level1','level2','team_leader') NOT NULL,
  `commission_rate` decimal(5,2) NOT NULL COMMENT '佣金比例',
  `order_amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `commission_amount` decimal(10,2) NOT NULL COMMENT '佣金金额',
  `promoter_level` tinyint(1) NOT NULL COMMENT '推广员等级',
  `status` enum('pending','confirmed','paid','cancelled') DEFAULT 'pending',
  `create_time` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) COMMENT='分销佣金记录表';
```

## 🎨 前端功能设计

### 1. 商品分销池主页面

#### 1.1 统计概览卡片
- **总商品数**：系统中所有商品数量
- **已分销商品**：已加入分销池的商品数量
- **总佣金池**：所有分销商品的预计佣金总额
- **高级商品**：需要高等级权限的商品数量
- **热门商品**：销量超过阈值的分销商品数量

#### 1.2 商品筛选和分类
- **分销状态筛选**：全部/已分销/未分销
- **分类筛选**：按商品分类筛选
- **等级筛选**：按最低等级要求筛选
- **价格区间筛选**：按价格范围筛选
- **搜索功能**：按商品名称搜索

#### 1.3 商品列表展示
- **网格视图**：卡片式展示，适合浏览
- **列表视图**：表格式展示，适合管理

#### 1.4 多级佣金配置
- **个人推广佣金**：直接推广获得的佣金比例
- **一级分销佣金**：直接下级成交获得的佣金比例
- **二级分销佣金**：二级下级成交获得的佣金比例
- **团长额外佣金**：团长级别的额外奖励比例
- **等级权限设置**：最低和最高分销等级限制

### 2. 佣金设置功能

#### 2.1 单品佣金设置
- **快速设置**：使用预设模板
- **自定义设置**：手动配置各级佣金
- **预览计算**：实时显示预计佣金金额
- **等级权限**：设置分销等级要求

#### 2.2 批量佣金设置
- **模板应用**：批量应用佣金模板
- **分类设置**：按分类批量设置
- **价格区间设置**：按价格范围批量设置

#### 2.3 佣金模板管理
- **模板创建**：创建自定义佣金模板
- **模板编辑**：修改现有模板
- **模板应用**：将模板应用到商品

## 🔧 后端API设计

### 1. 商品分销池管理API

#### 1.1 获取商品列表
```javascript
GET /admin/distribution/goodsList
参数：
- page: 页码
- pageSize: 每页数量
- category_id: 分类ID
- is_distributed: 分销状态
- min_level: 最低等级
- keyword: 搜索关键词

响应：
{
  errno: 0,
  data: {
    list: [...],
    total: 100,
    page: 1,
    pageSize: 20
  }
}
```

#### 1.2 设置商品分销状态
```javascript
POST /admin/distribution/setDistributionStatus
参数：
{
  goods_id: 商品ID,
  is_distributed: 是否分销,
  personal_rate: 个人推广佣金,
  level1_rate: 一级分销佣金,
  level2_rate: 二级分销佣金,
  team_leader_rate: 团长额外佣金,
  min_level_required: 最低等级要求
}
```

#### 1.3 批量设置分销状态
```javascript
POST /admin/distribution/batchSetDistribution
参数：
{
  goods_ids: [商品ID数组],
  template_id: 模板ID,
  // 或者直接设置佣金
  personal_rate: 个人推广佣金,
  level1_rate: 一级分销佣金,
  level2_rate: 二级分销佣金
}
```

### 2. 佣金模板管理API

#### 2.1 获取佣金模板列表
```javascript
GET /admin/distribution/templates
响应：
{
  errno: 0,
  data: [
    {
      id: 1,
      template_name: "基础商品模板",
      personal_rate: 8.00,
      level1_rate: 3.00,
      level2_rate: 1.00,
      team_leader_rate: 2.00
    }
  ]
}
```

#### 2.2 创建/更新佣金模板
```javascript
POST /admin/distribution/saveTemplate
参数：
{
  template_name: "模板名称",
  category_id: 分类ID,
  price_min: 最低价格,
  price_max: 最高价格,
  personal_rate: 个人推广佣金,
  level1_rate: 一级分销佣金,
  level2_rate: 二级分销佣金,
  team_leader_rate: 团长额外佣金
}
```

### 3. 分销权限验证API

#### 3.1 检查分销权限
```javascript
POST /api/distribution/checkPermission
参数：
{
  user_id: 用户ID,
  goods_id: 商品ID
}

响应：
{
  errno: 0,
  data: {
    canDistribute: true,
    userLevel: 2,
    requiredLevel: 1,
    commissionRates: {
      personal: 8.00,
      level1: 3.00,
      level2: 1.00
    }
  }
}
```

### 4. 佣金计算API

#### 4.1 计算订单佣金
```javascript
POST /api/distribution/calculateCommission
参数：
{
  order_id: 订单ID,
  promoter_user_id: 推广员用户ID
}

响应：
{
  errno: 0,
  data: {
    commissions: [
      {
        type: "personal",
        user_id: 123,
        rate: 8.00,
        amount: 80.00
      },
      {
        type: "level1", 
        user_id: 456,
        rate: 3.00,
        amount: 30.00
      }
    ],
    total_commission: 110.00
  }
}
```

## 🔄 业务流程设计

### 1. 商品加入分销池流程
```
选择商品 → 设置佣金比例 → 设置等级权限 → 确认加入 → 更新统计
```

### 2. 分销员推广流程
```
分销员分享商品 → 用户访问 → 检查分销权限 → 记录访问 → 用户下单 → 计算佣金 → 发放奖励
```

### 3. 佣金结算流程
```
订单完成 → 计算各级佣金 → 创建佣金记录 → 更新分销员统计 → 定期结算
```

## 🎯 核心特性

### 1. 多级佣金体系
- **个人推广**：8-15% 直接推广佣金
- **一级分销**：3-6% 下级成交佣金  
- **二级分销**：1-3% 二级下级佣金
- **团长奖励**：2-5% 团长额外奖励

### 2. 等级权限控制
- **新手(1级)**：只能推广基础商品
- **优秀(2级)**：可推广大部分商品，获得一级分销权
- **金牌(3级)**：可推广高级商品，获得二级分销权
- **钻石(4级)**：可推广全部商品，可申请成为团长

### 3. 智能佣金模板
- **基础模板**：0-100元商品，适合新手
- **中档模板**：100-500元商品，适合优秀分销员
- **高档模板**：500元以上商品，适合金牌以上

### 4. 灵活配置选项
- **时间限制**：设置分销开始和结束时间
- **数量限制**：设置每日和总分销限额
- **优先级**：设置商品分销优先级
- **标签管理**：商品分销标签分类

## 📈 统计分析功能

### 1. 分销概览统计
- 总商品数、已分销商品数
- 总佣金池、平均佣金率
- 热门商品、高级商品统计

### 2. 分销员业绩统计
- 个人推广业绩
- 团队分销业绩
- 佣金收入统计
- 等级升级进度

### 3. 商品分销分析
- 商品分销转化率
- 各等级分销员偏好
- 佣金设置效果分析
- 分销趋势分析

## ✅ 系统兼容性

### 1. 与现有个人分销系统兼容
- 保持现有积分奖励机制
- 兼容现有等级升级逻辑
- 保持现有数据结构

### 2. 与现有团队分销系统兼容
- 保持现有层级关系
- 兼容现有佣金计算
- 保持现有审核流程

### 3. 数据迁移方案
- 现有分销配置自动迁移
- 历史佣金记录保持不变
- 分销员等级映射转换

## 🔧 详细API接口设计

### 1. 商品分销池核心API

#### 1.1 获取分销统计数据
```javascript
GET /admin/distribution/stats
响应：
{
  errno: 0,
  data: {
    totalProducts: 1250,        // 总商品数
    distributedProducts: 856,   // 已分销商品数
    totalCommission: "125680.50", // 总佣金池
    premiumProducts: 234,       // 高级商品数
    hotProducts: 89,           // 热门商品数
    averageCommissionRate: 12.5, // 平均佣金率
    topCategories: [           // 热门分销分类
      { category_id: 1001, name: "居家生活", count: 156 },
      { category_id: 1002, name: "服装配饰", count: 134 }
    ]
  }
}
```

#### 1.2 获取佣金规则配置
```javascript
GET /admin/distribution/commissionRules
响应：
{
  errno: 0,
  data: {
    defaultRates: {
      personal: 10.0,
      level1: 4.0,
      level2: 2.0,
      teamLeader: 3.0
    },
    levelRequirements: {
      1: { name: "新手", maxPrice: 100 },
      2: { name: "优秀", maxPrice: 500 },
      3: { name: "金牌", maxPrice: 1000 },
      4: { name: "钻石", maxPrice: 999999 }
    },
    templates: [...] // 佣金模板列表
  }
}
```

### 2. 分销权限管理API

#### 2.1 获取用户分销权限
```javascript
GET /api/distribution/userPermissions?user_id=123
响应：
{
  errno: 0,
  data: {
    userLevel: 2,
    levelName: "优秀分销员",
    permissions: {
      canPersonalPromote: true,
      canLevel1Distribute: true,
      canLevel2Distribute: false,
      canBeTeamLeader: false
    },
    accessibleCategories: [1001, 1002, 1003],
    maxProductPrice: 500.00,
    currentStats: {
      totalOrders: 25,
      totalSales: 12500.00,
      totalCommission: 1250.00
    }
  }
}
```

#### 2.2 检查商品分销权限
```javascript
POST /api/distribution/checkProductAccess
参数：
{
  user_id: 123,
  goods_id: 1006002
}

响应：
{
  errno: 0,
  data: {
    canAccess: true,
    reason: "",
    productInfo: {
      name: "轻奢纯棉刺绣水洗四件套",
      price: 899.00,
      minLevelRequired: 2,
      commissionRates: {
        personal: 12.0,
        level1: 5.0,
        level2: 2.5,
        teamLeader: 3.0
      }
    },
    userCommissions: {
      personal: 107.88,  // 用户可获得的个人推广佣金
      level1: 44.95,     // 用户可获得的一级分销佣金
      level2: 0          // 用户等级不够，无法获得二级分销佣金
    }
  }
}
```

### 3. 佣金计算和记录API

#### 3.1 订单佣金分配计算
```javascript
POST /api/distribution/calculateOrderCommissions
参数：
{
  order_id: 12345,
  buyer_user_id: 789,
  promoter_user_id: 123,
  goods_list: [
    {
      goods_id: 1006002,
      quantity: 1,
      price: 899.00
    }
  ]
}

响应：
{
  errno: 0,
  data: {
    commissions: [
      {
        user_id: 123,
        type: "personal",
        goods_id: 1006002,
        rate: 12.0,
        amount: 107.88,
        level: 2
      },
      {
        user_id: 456,  // 123的上级
        type: "level1",
        goods_id: 1006002,
        rate: 5.0,
        amount: 44.95,
        level: 3
      },
      {
        user_id: 789,  // 456的上级
        type: "level2",
        goods_id: 1006002,
        rate: 2.5,
        amount: 22.48,
        level: 4
      }
    ],
    totalCommission: 175.31,
    distributionPath: "123 → 456 → 789"
  }
}
```

#### 3.2 创建佣金记录
```javascript
POST /api/distribution/createCommissionRecords
参数：
{
  order_id: 12345,
  commissions: [...] // 从计算API返回的佣金数据
}

响应：
{
  errno: 0,
  data: {
    recordIds: [1001, 1002, 1003],
    message: "佣金记录创建成功"
  }
}
```

### 4. 分销员关系管理API

#### 4.1 获取分销员关系链
```javascript
GET /api/distribution/relationChain?user_id=123
响应：
{
  errno: 0,
  data: {
    upwardChain: [  // 向上关系链
      {
        user_id: 456,
        nickname: "张团长",
        level: 3,
        relation: "direct_parent"
      },
      {
        user_id: 789,
        nickname: "李总监",
        level: 4,
        relation: "grandparent"
      }
    ],
    downwardChain: [  // 向下关系链
      {
        user_id: 234,
        nickname: "王分销",
        level: 1,
        relation: "direct_child",
        joinTime: 1640995200
      }
    ],
    teamInfo: {
      teamLeaderId: 789,
      teamLeaderName: "李总监",
      teamMemberCount: 25,
      teamTotalSales: 125000.00
    }
  }
}
```

#### 4.2 建立分销员关系
```javascript
POST /api/distribution/establishRelation
参数：
{
  child_user_id: 234,
  parent_user_id: 123,
  source: "share_link" // 关系建立来源
}

响应：
{
  errno: 0,
  data: {
    relationId: 1001,
    message: "分销关系建立成功"
  }
}
```

## 🎨 前端组件设计详细说明

### 1. 商品分销池主页面组件结构

```vue
<template>
  <div class="product-pool-page">
    <!-- 页面头部 -->
    <PageHeader title="商品分销池" />

    <!-- 统计概览 -->
    <StatisticsCards :data="statistics" />

    <!-- 筛选和搜索 -->
    <FilterSection
      v-model:filters="filters"
      :categories="categories"
      :levels="distributorLevels"
      @filter-change="handleFilterChange"
    />

    <!-- 商品列表 -->
    <ProductList
      :products="products"
      :view-mode="viewMode"
      @toggle-distribution="handleToggleDistribution"
      @set-commission="handleSetCommission"
      @batch-operation="handleBatchOperation"
    />

    <!-- 佣金设置弹窗 -->
    <CommissionModal
      v-model:visible="showCommissionModal"
      :product="selectedProduct"
      :templates="commissionTemplates"
      @save="handleSaveCommission"
    />
  </div>
</template>
```

### 2. 多级佣金设置组件

```vue
<template>
  <div class="commission-settings">
    <!-- 快速模板选择 -->
    <div class="template-section">
      <h3>快速应用模板</h3>
      <div class="template-grid">
        <div
          v-for="template in templates"
          :key="template.id"
          class="template-card"
          @click="applyTemplate(template)"
        >
          <h4>{{ template.name }}</h4>
          <div class="rates">
            <span>个人: {{ template.personal_rate }}%</span>
            <span>一级: {{ template.level1_rate }}%</span>
            <span>二级: {{ template.level2_rate }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 自定义佣金设置 -->
    <div class="custom-settings">
      <h3>自定义佣金设置</h3>
      <div class="commission-inputs">
        <div class="input-group">
          <label>个人推广佣金</label>
          <input
            v-model="commissionRates.personal"
            type="number"
            step="0.1"
            min="0"
            max="50"
          />
          <span class="unit">%</span>
        </div>

        <div class="input-group">
          <label>一级分销佣金</label>
          <input
            v-model="commissionRates.level1"
            type="number"
            step="0.1"
            min="0"
            max="20"
          />
          <span class="unit">%</span>
        </div>

        <div class="input-group">
          <label>二级分销佣金</label>
          <input
            v-model="commissionRates.level2"
            type="number"
            step="0.1"
            min="0"
            max="10"
          />
          <span class="unit">%</span>
        </div>

        <div class="input-group">
          <label>团长额外佣金</label>
          <input
            v-model="commissionRates.teamLeader"
            type="number"
            step="0.1"
            min="0"
            max="15"
          />
          <span class="unit">%</span>
        </div>
      </div>
    </div>

    <!-- 等级权限设置 -->
    <div class="level-permissions">
      <h3>分销等级权限</h3>
      <div class="level-settings">
        <div class="input-group">
          <label>最低分销等级</label>
          <select v-model="levelSettings.minLevel">
            <option value="1">新手分销员</option>
            <option value="2">优秀分销员</option>
            <option value="3">金牌分销员</option>
            <option value="4">钻石分销员</option>
          </select>
        </div>

        <div class="input-group">
          <label>最高分销等级</label>
          <select v-model="levelSettings.maxLevel">
            <option value="2">优秀分销员</option>
            <option value="3">金牌分销员</option>
            <option value="4">钻石分销员</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 预计佣金计算 -->
    <div class="commission-preview">
      <h3>预计佣金计算</h3>
      <div class="preview-table">
        <div class="preview-row">
          <span>商品价格: ¥{{ productPrice }}</span>
        </div>
        <div class="preview-row">
          <span>个人推广: ¥{{ calculateCommission('personal') }}</span>
        </div>
        <div class="preview-row">
          <span>一级分销: ¥{{ calculateCommission('level1') }}</span>
        </div>
        <div class="preview-row">
          <span>二级分销: ¥{{ calculateCommission('level2') }}</span>
        </div>
        <div class="preview-row total">
          <span>总佣金: ¥{{ totalCommission }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
```

## 📊 数据流设计

### 1. 商品分销状态管理
```javascript
// Vuex Store 结构
const store = {
  state: {
    products: [],           // 商品列表
    statistics: {},         // 统计数据
    filters: {},           // 筛选条件
    commissionTemplates: [], // 佣金模板
    distributorLevels: []   // 分销员等级
  },

  mutations: {
    SET_PRODUCTS(state, products) {
      state.products = products
    },
    UPDATE_PRODUCT_COMMISSION(state, { productId, commissionData }) {
      const product = state.products.find(p => p.id === productId)
      if (product) {
        Object.assign(product, commissionData)
      }
    }
  },

  actions: {
    async loadProducts({ commit }, filters) {
      const response = await api.getProductList(filters)
      commit('SET_PRODUCTS', response.data.list)
    },

    async setProductCommission({ commit }, { productId, commissionData }) {
      const response = await api.setProductCommission(productId, commissionData)
      if (response.errno === 0) {
        commit('UPDATE_PRODUCT_COMMISSION', { productId, commissionData })
      }
    }
  }
}
```

### 2. 佣金计算逻辑
```javascript
// 佣金计算工具函数
export const CommissionCalculator = {
  // 计算单个商品的各级佣金
  calculateProductCommissions(product, userLevel) {
    const commissions = {}

    // 个人推广佣金（所有等级都可获得）
    commissions.personal = product.price * product.personalRate / 100

    // 一级分销佣金（2级以上可获得）
    if (userLevel >= 2) {
      commissions.level1 = product.price * product.level1Rate / 100
    }

    // 二级分销佣金（3级以上可获得）
    if (userLevel >= 3) {
      commissions.level2 = product.price * product.level2Rate / 100
    }

    // 团长额外佣金（4级可获得）
    if (userLevel >= 4) {
      commissions.teamLeader = product.price * product.teamLeaderRate / 100
    }

    return commissions
  },

  // 计算订单总佣金分配
  calculateOrderDistribution(order, promoterChain) {
    const distributions = []

    order.goods.forEach(goods => {
      // 个人推广佣金给直接推广者
      if (promoterChain.length > 0) {
        distributions.push({
          userId: promoterChain[0].userId,
          type: 'personal',
          amount: goods.price * goods.personalRate / 100
        })
      }

      // 一级分销佣金给上级
      if (promoterChain.length > 1) {
        distributions.push({
          userId: promoterChain[1].userId,
          type: 'level1',
          amount: goods.price * goods.level1Rate / 100
        })
      }

      // 二级分销佣金给上上级
      if (promoterChain.length > 2) {
        distributions.push({
          userId: promoterChain[2].userId,
          type: 'level2',
          amount: goods.price * goods.level2Rate / 100
        })
      }
    })

    return distributions
  }
}
```

这个完整的设计方案涵盖了商品分销池的所有核心功能，确保与现有系统完全兼容，同时提供了强大的多级分销和灵活的佣金配置能力。设计方案已经准备就绪，等待您的确认后再进行具体的代码实现。
