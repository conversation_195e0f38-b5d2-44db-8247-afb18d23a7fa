{"version": 3, "sources": ["..\\..\\..\\src\\api\\model\\refund_apply.js"], "names": ["module", "exports", "think", "Model", "tableName", "getApplyDetail", "applyId", "apply", "where", "id", "find", "isEmpty", "images", "JSON", "parse", "e", "getUserApplyList", "userId", "page", "size", "list", "user_id", "order", "countSelect", "data", "length", "for<PERSON>ach", "item", "updateApplyStatus", "status", "memo", "updateData", "updated_at", "Date", "process_time", "parseInt", "getTime", "complete_time", "admin_memo", "update", "rejectApply", "rejectReason", "reject_reason", "getApplyStats", "stats", "field", "group", "select", "result", "pending", "processing", "approved", "rejected", "completed", "total", "count"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;AACvC;;;AAGA,QAAIC,SAAJ,GAAgB;AACZ,eAAO,sBAAP;AACH;;AAED;;;;;AAKMC,kBAAN,CAAqBC,OAArB,EAA8B;AAAA;;AAAA;AAC1B,kBAAMC,QAAQ,MAAM,MAAKC,KAAL,CAAW,EAAEC,IAAIH,OAAN,EAAX,EAA4BI,IAA5B,EAApB;AACA,gBAAIR,MAAMS,OAAN,CAAcJ,KAAd,CAAJ,EAA0B;AACtB,uBAAO,EAAP;AACH;;AAED;AACA,gBAAIA,MAAMK,MAAV,EAAkB;AACd,oBAAI;AACAL,0BAAMK,MAAN,GAAeC,KAAKC,KAAL,CAAWP,MAAMK,MAAjB,CAAf;AACH,iBAFD,CAEE,OAAOG,CAAP,EAAU;AACRR,0BAAMK,MAAN,GAAe,EAAf;AACH;AACJ,aAND,MAMO;AACHL,sBAAMK,MAAN,GAAe,EAAf;AACH;;AAED,mBAAOL,KAAP;AAjB0B;AAkB7B;;AAED;;;;;;;AAOMS,oBAAN,CAAuBC,MAAvB,EAA+BC,OAAO,CAAtC,EAAyCC,OAAO,EAAhD,EAAoD;AAAA;;AAAA;AAChD,kBAAMC,OAAO,MAAM,OAAKZ,KAAL,CAAW,EAAEa,SAASJ,MAAX,EAAX,EACdK,KADc,CACR,iBADQ,EAEdJ,IAFc,CAETA,IAFS,EAEHC,IAFG,EAGdI,WAHc,EAAnB;;AAKA;AACA,gBAAIH,KAAKI,IAAL,IAAaJ,KAAKI,IAAL,CAAUC,MAAV,GAAmB,CAApC,EAAuC;AACnCL,qBAAKI,IAAL,CAAUE,OAAV,CAAkB,gBAAQ;AACtB,wBAAIC,KAAKf,MAAT,EAAiB;AACb,4BAAI;AACAe,iCAAKf,MAAL,GAAcC,KAAKC,KAAL,CAAWa,KAAKf,MAAhB,CAAd;AACH,yBAFD,CAEE,OAAOG,CAAP,EAAU;AACRY,iCAAKf,MAAL,GAAc,EAAd;AACH;AACJ,qBAND,MAMO;AACHe,6BAAKf,MAAL,GAAc,EAAd;AACH;AACJ,iBAVD;AAWH;;AAED,mBAAOQ,IAAP;AArBgD;AAsBnD;;AAED;;;;;;;AAOMQ,qBAAN,CAAwBtB,OAAxB,EAAiCuB,MAAjC,EAAyCC,OAAO,EAAhD,EAAoD;AAAA;;AAAA;AAChD,kBAAMC,aAAa;AACfF,wBAAQA,MADO;AAEfG,4BAAY,IAAIC,IAAJ;AAFG,aAAnB;;AAKA,gBAAIJ,WAAW,YAAf,EAA6B;AACzBE,2BAAWG,YAAX,GAA0BC,SAAS,IAAIF,IAAJ,GAAWG,OAAX,KAAuB,IAAhC,CAA1B;AACH,aAFD,MAEO,IAAIP,WAAW,WAAf,EAA4B;AAC/BE,2BAAWM,aAAX,GAA2BF,SAAS,IAAIF,IAAJ,GAAWG,OAAX,KAAuB,IAAhC,CAA3B;AACH;;AAED,gBAAIN,IAAJ,EAAU;AACNC,2BAAWO,UAAX,GAAwBR,IAAxB;AACH;;AAED,mBAAO,MAAM,OAAKtB,KAAL,CAAW,EAAEC,IAAIH,OAAN,EAAX,EAA4BiC,MAA5B,CAAmCR,UAAnC,CAAb;AAhBgD;AAiBnD;;AAED;;;;;;AAMMS,eAAN,CAAkBlC,OAAlB,EAA2BmC,YAA3B,EAAyC;AAAA;;AAAA;AACrC,mBAAO,MAAM,OAAKjC,KAAL,CAAW,EAAEC,IAAIH,OAAN,EAAX,EAA4BiC,MAA5B,CAAmC;AAC5CV,wBAAQ,UADoC;AAE5Ca,+BAAeD,YAF6B;AAG5CP,8BAAcC,SAAS,IAAIF,IAAJ,GAAWG,OAAX,KAAuB,IAAhC,CAH8B;AAI5CJ,4BAAY,IAAIC,IAAJ;AAJgC,aAAnC,CAAb;AADqC;AAOxC;;AAED;;;;AAIMU,iBAAN,GAAsB;AAAA;;AAAA;AAClB,kBAAMC,QAAQ,MAAM,OAAKC,KAAL,CAAW,2BAAX,EACfC,KADe,CACT,QADS,EAEfC,MAFe,EAApB;;AAIA,kBAAMC,SAAS;AACXC,yBAAS,CADE;AAEXC,4BAAY,CAFD;AAGXC,0BAAU,CAHC;AAIXC,0BAAU,CAJC;AAKXC,2BAAW,CALA;AAMXC,uBAAO;AANI,aAAf;;AASAV,kBAAMlB,OAAN,CAAc,gBAAQ;AAClBsB,uBAAOrB,KAAKE,MAAZ,IAAsBM,SAASR,KAAK4B,KAAd,CAAtB;AACAP,uBAAOM,KAAP,IAAgBnB,SAASR,KAAK4B,KAAd,CAAhB;AACH,aAHD;;AAKA,mBAAOP,MAAP;AAnBkB;AAoBrB;AAjIsC,CAA3C", "file": "..\\..\\..\\src\\api\\model\\refund_apply.js", "sourcesContent": ["module.exports = class extends think.Model {\n    /**\n     * 获取售后申请表名\n     */\n    get tableName() {\n        return 'hiolabs_refund_apply';\n    }\n\n    /**\n     * 获取售后申请详情\n     * @param {number} applyId 申请ID\n     * @returns {Promise<Object>}\n     */\n    async getApplyDetail(applyId) {\n        const apply = await this.where({ id: applyId }).find();\n        if (think.isEmpty(apply)) {\n            return {};\n        }\n\n        // 解析图片JSON\n        if (apply.images) {\n            try {\n                apply.images = JSON.parse(apply.images);\n            } catch (e) {\n                apply.images = [];\n            }\n        } else {\n            apply.images = [];\n        }\n\n        return apply;\n    }\n\n    /**\n     * 获取用户的售后申请列表\n     * @param {number} userId 用户ID\n     * @param {number} page 页码\n     * @param {number} size 每页数量\n     * @returns {Promise<Object>}\n     */\n    async getUserApplyList(userId, page = 1, size = 10) {\n        const list = await this.where({ user_id: userId })\n            .order('apply_time DESC')\n            .page(page, size)\n            .countSelect();\n\n        // 处理图片字段\n        if (list.data && list.data.length > 0) {\n            list.data.forEach(item => {\n                if (item.images) {\n                    try {\n                        item.images = JSON.parse(item.images);\n                    } catch (e) {\n                        item.images = [];\n                    }\n                } else {\n                    item.images = [];\n                }\n            });\n        }\n\n        return list;\n    }\n\n    /**\n     * 更新申请状态\n     * @param {number} applyId 申请ID\n     * @param {string} status 新状态\n     * @param {string} memo 备注\n     * @returns {Promise<number>}\n     */\n    async updateApplyStatus(applyId, status, memo = '') {\n        const updateData = {\n            status: status,\n            updated_at: new Date()\n        };\n\n        if (status === 'processing') {\n            updateData.process_time = parseInt(new Date().getTime() / 1000);\n        } else if (status === 'completed') {\n            updateData.complete_time = parseInt(new Date().getTime() / 1000);\n        }\n\n        if (memo) {\n            updateData.admin_memo = memo;\n        }\n\n        return await this.where({ id: applyId }).update(updateData);\n    }\n\n    /**\n     * 拒绝申请\n     * @param {number} applyId 申请ID\n     * @param {string} rejectReason 拒绝原因\n     * @returns {Promise<number>}\n     */\n    async rejectApply(applyId, rejectReason) {\n        return await this.where({ id: applyId }).update({\n            status: 'rejected',\n            reject_reason: rejectReason,\n            process_time: parseInt(new Date().getTime() / 1000),\n            updated_at: new Date()\n        });\n    }\n\n    /**\n     * 获取申请统计\n     * @returns {Promise<Object>}\n     */\n    async getApplyStats() {\n        const stats = await this.field('status, COUNT(*) as count')\n            .group('status')\n            .select();\n\n        const result = {\n            pending: 0,\n            processing: 0,\n            approved: 0,\n            rejected: 0,\n            completed: 0,\n            total: 0\n        };\n\n        stats.forEach(item => {\n            result[item.status] = parseInt(item.count);\n            result.total += parseInt(item.count);\n        });\n\n        return result;\n    }\n};\n"]}