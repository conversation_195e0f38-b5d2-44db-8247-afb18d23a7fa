/**
 * 修复佣金数据一致性问题
 * 统一佣金数据同步逻辑，确保各表数据一致
 */

const mysql = require('mysql2/promise');

// 数据库配置（请根据实际情况修改）
const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'hiolabsdb',
    charset: 'utf8mb4'
};

async function fixCommissionConsistency() {
    let connection;
    
    try {
        console.log('=== 修复佣金数据一致性 ===\n');
        
        // 连接数据库
        connection = await mysql.createConnection(dbConfig);
        console.log('✓ 数据库连接成功');
        
        // 1. 备份当前数据
        console.log('\n1. 备份当前数据...');
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS hiolabs_user_commission_backup_${Date.now()} 
            AS SELECT * FROM hiolabs_user_commission
        `);
        console.log('✓ 用户佣金表已备份');
        
        // 2. 获取所有有佣金记录的用户
        console.log('\n2. 重新计算所有用户佣金...');
        const [users] = await connection.execute(`
            SELECT DISTINCT user_id FROM hiolabs_commission_log 
            WHERE commission_type = 'promotion'
        `);
        
        console.log(`找到 ${users.length} 个用户需要重新计算佣金`);
        
        const currentTime = Math.floor(Date.now() / 1000);
        const tenDaysAgo = currentTime - (10 * 24 * 3600);
        
        for (const user of users) {
            const userId = user.user_id;
            
            // 计算该用户的佣金统计
            const [commissionStats] = await connection.execute(`
                SELECT 
                    COALESCE(SUM(CASE WHEN commission_change > 0 THEN commission_change ELSE 0 END), 0) as total_earned,
                    COALESCE(SUM(CASE WHEN commission_change < 0 THEN ABS(commission_change) ELSE 0 END), 0) as total_deducted,
                    COALESCE(SUM(commission_change), 0) as net_commission
                FROM hiolabs_commission_log 
                WHERE user_id = ? AND commission_type = 'promotion' AND status = 'completed'
            `, [userId]);
            
            // 计算冻结佣金（10天内的正数佣金）
            const [frozenStats] = await connection.execute(`
                SELECT COALESCE(SUM(commission_change), 0) as frozen_amount
                FROM hiolabs_commission_log 
                WHERE user_id = ? 
                AND commission_type = 'promotion' 
                AND status = 'completed'
                AND commission_change > 0
                AND UNIX_TIMESTAMP(created_at) > ?
            `, [userId, tenDaysAgo]);
            
            // 计算已提现佣金
            const [withdrawnStats] = await connection.execute(`
                SELECT COALESCE(SUM(commission_change), 0) as withdrawn_amount
                FROM hiolabs_commission_log 
                WHERE user_id = ? 
                AND commission_type = 'promotion' 
                AND commission_status = 'withdrawn'
                AND commission_change > 0
            `, [userId]);
            
            const stats = commissionStats[0];
            const totalCommission = parseFloat(stats.net_commission);
            const frozenCommission = parseFloat(frozenStats[0].frozen_amount);
            const withdrawnCommission = parseFloat(withdrawnStats[0].withdrawn_amount);
            const availableCommission = Math.max(0, totalCommission - frozenCommission - withdrawnCommission);
            
            // 更新或创建用户佣金记录
            const [existingUser] = await connection.execute(`
                SELECT id FROM hiolabs_user_commission WHERE user_id = ?
            `, [userId]);
            
            if (existingUser.length > 0) {
                // 更新现有记录
                await connection.execute(`
                    UPDATE hiolabs_user_commission 
                    SET total_commission = ?,
                        available_commission = ?,
                        frozen_commission = ?,
                        withdrawn_commission = ?,
                        updated_at = NOW()
                    WHERE user_id = ?
                `, [
                    totalCommission.toFixed(2),
                    availableCommission.toFixed(2),
                    frozenCommission.toFixed(2),
                    withdrawnCommission.toFixed(2),
                    userId
                ]);
            } else {
                // 创建新记录
                await connection.execute(`
                    INSERT INTO hiolabs_user_commission 
                    (user_id, total_commission, available_commission, frozen_commission, withdrawn_commission, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, NOW(), NOW())
                `, [
                    userId,
                    totalCommission.toFixed(2),
                    availableCommission.toFixed(2),
                    frozenCommission.toFixed(2),
                    withdrawnCommission.toFixed(2)
                ]);
            }
            
            console.log(`✓ 用户 ${userId}: 总佣金=${totalCommission.toFixed(2)}, 可用=${availableCommission.toFixed(2)}, 冻结=${frozenCommission.toFixed(2)}, 已提现=${withdrawnCommission.toFixed(2)}`);
        }
        
        // 3. 检查推广订单状态一致性
        console.log('\n3. 修复推广订单状态一致性...');
        
        // 查找有佣金日志但推广订单状态不正确的记录
        const [inconsistentOrders] = await connection.execute(`
            SELECT DISTINCT po.id, po.commission_status, cl.commission_status as log_status
            FROM hiolabs_promotion_orders po
            INNER JOIN hiolabs_commission_log cl ON po.id = cl.promotion_order_id
            WHERE po.commission_status != cl.commission_status
            AND cl.commission_type = 'promotion'
        `);
        
        console.log(`发现 ${inconsistentOrders.length} 个状态不一致的推广订单`);
        
        for (const order of inconsistentOrders) {
            // 以佣金日志的状态为准更新推广订单状态
            await connection.execute(`
                UPDATE hiolabs_promotion_orders 
                SET commission_status = ?, update_time = ?
                WHERE id = ?
            `, [order.log_status, currentTime, order.id]);
            
            console.log(`✓ 推广订单 ${order.id} 状态已更新: ${order.commission_status} -> ${order.log_status}`);
        }
        
        // 4. 清理孤立数据
        console.log('\n4. 清理孤立数据...');
        
        // 查找没有对应推广订单的佣金日志
        const [orphanLogs] = await connection.execute(`
            SELECT cl.id, cl.promotion_order_id
            FROM hiolabs_commission_log cl
            LEFT JOIN hiolabs_promotion_orders po ON cl.promotion_order_id = po.id
            WHERE cl.commission_type = 'promotion' 
            AND cl.promotion_order_id IS NOT NULL
            AND po.id IS NULL
        `);
        
        if (orphanLogs.length > 0) {
            console.log(`⚠️  发现 ${orphanLogs.length} 个孤立的佣金日志记录`);
            console.log('建议手动检查这些记录的有效性');
        } else {
            console.log('✓ 没有发现孤立的佣金日志记录');
        }
        
        // 5. 验证修复结果
        console.log('\n5. 验证修复结果...');
        
        const [verifyResult] = await connection.execute(`
            SELECT 
                COUNT(*) as total_users,
                SUM(total_commission) as total_commission_sum,
                SUM(available_commission) as available_commission_sum,
                SUM(frozen_commission) as frozen_commission_sum,
                SUM(withdrawn_commission) as withdrawn_commission_sum
            FROM hiolabs_user_commission
        `);
        
        const result = verifyResult[0];
        console.log('修复后统计:');
        console.log(`  用户总数: ${result.total_users}`);
        console.log(`  总佣金: ${parseFloat(result.total_commission_sum).toFixed(2)}`);
        console.log(`  可用佣金: ${parseFloat(result.available_commission_sum).toFixed(2)}`);
        console.log(`  冻结佣金: ${parseFloat(result.frozen_commission_sum).toFixed(2)}`);
        console.log(`  已提现佣金: ${parseFloat(result.withdrawn_commission_sum).toFixed(2)}`);
        
        console.log('\n=== 修复完成 ===');
        console.log('\n✅ 修复内容:');
        console.log('1. 重新计算所有用户的佣金统计数据');
        console.log('2. 统一佣金账户表的字段值');
        console.log('3. 修复推广订单状态一致性');
        console.log('4. 清理孤立数据');
        console.log('5. 验证数据完整性');
        
        console.log('\n📋 后续建议:');
        console.log('1. 统一使用佣金服务进行所有佣金操作');
        console.log('2. 避免直接更新 hiolabs_user_commission 表');
        console.log('3. 所有佣金变动都应记录到 hiolabs_commission_log 表');
        console.log('4. 定期运行一致性检查脚本');
        
    } catch (error) {
        console.error('修复过程中发生错误:', error.message);
        console.log('\n如果出现错误，可以从备份表恢复数据');
    } finally {
        if (connection) {
            await connection.end();
            console.log('\n数据库连接已关闭');
        }
    }
}

// 运行修复
if (require.main === module) {
    fixCommissionConsistency().catch(console.error);
}

module.exports = { fixCommissionConsistency };
