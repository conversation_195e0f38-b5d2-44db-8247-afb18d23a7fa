<template>
  <div class="data-overview-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>数据概览</h2>
      <p>个人推广数据统计和分析</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-number">{{ stats.total_promoters }}</div>
        <div class="stat-label">总推广员</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ stats.total_visits }}</div>
        <div class="stat-label">总访问次数</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ stats.total_orders }}</div>
        <div class="stat-label">总成交订单</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">¥{{ stats.total_commission }}</div>
        <div class="stat-label">总佣金</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ stats.conversion_rate }}%</div>
        <div class="stat-label">转化率</div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-card">
        <h3>推广趋势</h3>
        <div class="chart-placeholder">
          <p>推广数据趋势图表</p>
          <p>（此处可集成 ECharts 或其他图表库）</p>
        </div>
      </div>
    </div>

    <!-- 排行榜 -->
    <div class="ranking-section">
      <h3>推广员排行榜</h3>
      <el-table :data="topPromoters" stripe>
        <el-table-column prop="rank" label="排名" width="80" />
        <el-table-column label="推广员" width="200">
          <template #default="scope">
            <div class="promoter-info">
              <img :src="scope.row.avatar || '/images/default-avatar.png'" class="avatar" />
              <div>
                <div>{{ scope.row.nickname }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="total_views" label="总浏览" width="100" />
        <el-table-column prop="total_orders" label="总成交" width="100" />
        <el-table-column label="总佣金" width="120">
          <template #default="scope">
            <span class="commission">¥{{ scope.row.total_commission }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DataOverviewPage',
  data() {
    return {
      stats: {
        total_promoters: 0,
        total_visits: 0,
        total_orders: 0,
        total_commission: '0.00',
        conversion_rate: '0.00'
      },
      topPromoters: []
    }
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      try {
        // 这里调用数据概览API
        console.log('加载数据概览');
      } catch (error) {
        console.error('加载数据失败:', error);
        this.$message.error('加载数据失败');
      }
    }
  }
}
</script>

<style scoped>
.data-overview-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-card h3 {
  margin: 0 0 20px 0;
  color: #303133;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  border-radius: 4px;
  color: #909399;
}

.ranking-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ranking-section h3 {
  margin: 0 0 20px 0;
  color: #303133;
}

.promoter-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}



.commission {
  color: #67C23A;
  font-weight: bold;
}
</style>
