{"version": 3, "sources": ["..\\..\\..\\src\\admin\\model\\goods_distribution.js"], "names": ["module", "exports", "think", "Model", "getDistributionConfig", "goodsId", "config", "where", "goods_id", "find", "setDistributionConfig", "existingConfig", "configData", "is_distributed", "commission_rate", "commission_type", "update_time", "parseInt", "Date", "getTime", "update", "add_time", "add", "getDistributedGoods", "goods", "select", "batchSetDistribution", "goodsIds", "isDistributed", "commissionRate", "commissionType", "currentTime", "results", "result", "push", "calculateEstimatedCommission", "model", "id", "estimatedCommission", "parseFloat", "retail_price", "toFixed", "getDistributionStats", "distributedCount", "count", "distributedGoods", "alias", "join", "table", "as", "on", "field", "totalCommission", "item"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;AACvC;;;;;AAKMC,yBAAN,CAA4BC,OAA5B,EAAqC;AAAA;;AAAA;AACjC,kBAAMC,SAAS,MAAM,MAAKC,KAAL,CAAW;AAC5BC,0BAAUH;AADkB,aAAX,EAElBI,IAFkB,EAArB;AAGA,mBAAOH,MAAP;AAJiC;AAKpC;;AAED;;;;;;AAMMI,yBAAN,CAA4BL,OAA5B,EAAqCC,MAArC,EAA6C;AAAA;;AAAA;AACzC,kBAAMK,iBAAiB,MAAM,OAAKJ,KAAL,CAAW;AACpCC,0BAAUH;AAD0B,aAAX,EAE1BI,IAF0B,EAA7B;;AAIA,kBAAMG,aAAa;AACfJ,0BAAUH,OADK;AAEfQ,gCAAgBP,OAAOO,cAAP,IAAyB,CAF1B;AAGfC,iCAAiBR,OAAOQ,eAAP,IAA0B,CAH5B;AAIfC,iCAAiBT,OAAOS,eAAP,IAA0B,SAJ5B;AAKfC,6BAAaC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC;AALE,aAAnB;;AAQA,gBAAIR,cAAJ,EAAoB;AAChB;AACA,uBAAO,MAAM,OAAKJ,KAAL,CAAW;AACpBC,8BAAUH;AADU,iBAAX,EAEVe,MAFU,CAEHR,UAFG,CAAb;AAGH,aALD,MAKO;AACH;AACAA,2BAAWS,QAAX,GAAsBJ,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAAtB;AACA,uBAAO,MAAM,OAAKG,GAAL,CAASV,UAAT,CAAb;AACH;AAtBwC;AAuB5C;;AAED;;;;AAIMW,uBAAN,GAA4B;AAAA;;AAAA;AACxB,kBAAMC,QAAQ,MAAM,OAAKjB,KAAL,CAAW;AAC3BM,gCAAgB;AADW,aAAX,EAEjBY,MAFiB,EAApB;AAGA,mBAAOD,KAAP;AAJwB;AAK3B;;AAED;;;;;;;;AAQME,wBAAN,CAA2BC,QAA3B,EAAqCC,aAArC,EAAoDC,iBAAiB,CAArE,EAAwEC,iBAAiB,SAAzF,EAAoG;AAAA;;AAAA;AAChG,kBAAMC,cAAcd,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACA,kBAAMa,UAAU,EAAhB;;AAEA,iBAAK,MAAM3B,OAAX,IAAsBsB,QAAtB,EAAgC;AAC5B,sBAAMhB,iBAAiB,MAAM,OAAKJ,KAAL,CAAW;AACpCC,8BAAUH;AAD0B,iBAAX,EAE1BI,IAF0B,EAA7B;;AAIA,sBAAMG,aAAa;AACfJ,8BAAUH,OADK;AAEfQ,oCAAgBe,aAFD;AAGfd,qCAAiBe,cAHF;AAIfd,qCAAiBe,cAJF;AAKfd,iCAAae;AALE,iBAAnB;;AAQA,oBAAIpB,cAAJ,EAAoB;AAChB;AACA,0BAAMsB,SAAS,MAAM,OAAK1B,KAAL,CAAW;AAC5BC,kCAAUH;AADkB,qBAAX,EAElBe,MAFkB,CAEXR,UAFW,CAArB;AAGAoB,4BAAQE,IAAR,CAAaD,MAAb;AACH,iBAND,MAMO;AACH;AACArB,+BAAWS,QAAX,GAAsBU,WAAtB;AACA,0BAAME,SAAS,MAAM,OAAKX,GAAL,CAASV,UAAT,CAArB;AACAoB,4BAAQE,IAAR,CAAaD,MAAb;AACH;AACJ;;AAED,mBAAOD,OAAP;AA/BgG;AAgCnG;;AAED;;;;;AAKMG,gCAAN,CAAmC9B,OAAnC,EAA4C;AAAA;;AAAA;AACxC,kBAAMC,SAAS,MAAM,OAAKC,KAAL,CAAW;AAC5BC,0BAAUH;AADkB,aAAX,EAElBI,IAFkB,EAArB;;AAIA,gBAAI,CAACH,MAAD,IAAW,CAACA,OAAOO,cAAvB,EAAuC;AACnC,uBAAO,CAAP;AACH;;AAED,kBAAMW,QAAQ,MAAM,OAAKY,KAAL,CAAW,OAAX,EAAoB7B,KAApB,CAA0B;AAC1C8B,oBAAIhC;AADsC,aAA1B,EAEjBI,IAFiB,EAApB;;AAIA,gBAAI,CAACe,KAAL,EAAY;AACR,uBAAO,CAAP;AACH;;AAED,kBAAMc,sBAAsBC,WAAWf,MAAMgB,YAAjB,IAAiClC,OAAOQ,eAAxC,GAA0D,GAAtF;AACA,mBAAOwB,oBAAoBG,OAApB,CAA4B,CAA5B,CAAP;AAlBwC;AAmB3C;;AAED;;;;AAIMC,wBAAN,GAA6B;AAAA;;AAAA;AACzB;AACA,kBAAMC,mBAAmB,MAAM,OAAKpC,KAAL,CAAW;AACtCM,gCAAgB;AADsB,aAAX,EAE5B+B,KAF4B,EAA/B;;AAIA;AACA,kBAAMC,mBAAmB,MAAM,OAAKT,KAAL,CAAW,OAAX,EAC1BU,KAD0B,CACpB,GADoB,EAE1BC,IAF0B,CAErB;AACFC,uBAAO,oBADL;AAEFD,sBAAM,OAFJ;AAGFE,oBAAI,GAHF;AAIFC,oBAAI,CAAC,MAAD,EAAS,YAAT;AAJF,aAFqB,EAQ1B3C,KAR0B,CAQpB;AACH,+BAAe,CADZ;AAEH,oCAAoB;AAFjB,aARoB,EAY1B4C,KAZ0B,CAYpB,kCAZoB,EAa1B1B,MAb0B,EAA/B;;AAeA,gBAAI2B,kBAAkB,CAAtB;AACA,iBAAK,MAAMC,IAAX,IAAmBR,gBAAnB,EAAqC;AACjCO,mCAAmBb,WAAWc,KAAKb,YAAhB,IAAgCa,KAAKvC,eAArC,GAAuD,GAA1E;AACH;;AAED,mBAAO;AACH6B,kCAAkBA,gBADf;AAEHS,iCAAiBA,gBAAgBX,OAAhB,CAAwB,CAAxB;AAFd,aAAP;AA3ByB;AA+B5B;AA9JsC,CAA3C", "file": "..\\..\\..\\src\\admin\\model\\goods_distribution.js", "sourcesContent": ["module.exports = class extends think.Model {\n    /**\n     * 获取商品的分销配置\n     * @param goodsId\n     * @returns {Promise.<*>}\n     */\n    async getDistributionConfig(goodsId) {\n        const config = await this.where({\n            goods_id: goodsId\n        }).find();\n        return config;\n    }\n\n    /**\n     * 设置商品分销配置\n     * @param goodsId\n     * @param config\n     * @returns {Promise.<*>}\n     */\n    async setDistributionConfig(goodsId, config) {\n        const existingConfig = await this.where({\n            goods_id: goodsId\n        }).find();\n\n        const configData = {\n            goods_id: goodsId,\n            is_distributed: config.is_distributed || 0,\n            commission_rate: config.commission_rate || 0,\n            commission_type: config.commission_type || 'default',\n            update_time: parseInt(new Date().getTime() / 1000)\n        };\n\n        if (existingConfig) {\n            // 更新现有配置\n            return await this.where({\n                goods_id: goodsId\n            }).update(configData);\n        } else {\n            // 创建新配置\n            configData.add_time = parseInt(new Date().getTime() / 1000);\n            return await this.add(configData);\n        }\n    }\n\n    /**\n     * 获取所有已分销的商品\n     * @returns {Promise.<*>}\n     */\n    async getDistributedGoods() {\n        const goods = await this.where({\n            is_distributed: 1\n        }).select();\n        return goods;\n    }\n\n    /**\n     * 批量设置分销状态\n     * @param goodsIds\n     * @param isDistributed\n     * @param commissionRate\n     * @param commissionType\n     * @returns {Promise.<*>}\n     */\n    async batchSetDistribution(goodsIds, isDistributed, commissionRate = 0, commissionType = 'default') {\n        const currentTime = parseInt(new Date().getTime() / 1000);\n        const results = [];\n\n        for (const goodsId of goodsIds) {\n            const existingConfig = await this.where({\n                goods_id: goodsId\n            }).find();\n\n            const configData = {\n                goods_id: goodsId,\n                is_distributed: isDistributed,\n                commission_rate: commissionRate,\n                commission_type: commissionType,\n                update_time: currentTime\n            };\n\n            if (existingConfig) {\n                // 更新现有配置\n                const result = await this.where({\n                    goods_id: goodsId\n                }).update(configData);\n                results.push(result);\n            } else {\n                // 创建新配置\n                configData.add_time = currentTime;\n                const result = await this.add(configData);\n                results.push(result);\n            }\n        }\n\n        return results;\n    }\n\n    /**\n     * 计算商品预计佣金\n     * @param goodsId\n     * @returns {Promise.<*>}\n     */\n    async calculateEstimatedCommission(goodsId) {\n        const config = await this.where({\n            goods_id: goodsId\n        }).find();\n\n        if (!config || !config.is_distributed) {\n            return 0;\n        }\n\n        const goods = await this.model('goods').where({\n            id: goodsId\n        }).find();\n\n        if (!goods) {\n            return 0;\n        }\n\n        const estimatedCommission = parseFloat(goods.retail_price) * config.commission_rate / 100;\n        return estimatedCommission.toFixed(2);\n    }\n\n    /**\n     * 获取分销统计数据\n     * @returns {Promise.<*>}\n     */\n    async getDistributionStats() {\n        // 已分销商品数\n        const distributedCount = await this.where({\n            is_distributed: 1\n        }).count();\n\n        // 总佣金池\n        const distributedGoods = await this.model('goods')\n            .alias('g')\n            .join({\n                table: 'goods_distribution',\n                join: 'inner',\n                as: 'd',\n                on: ['g.id', 'd.goods_id']\n            })\n            .where({\n                'g.is_delete': 0,\n                'd.is_distributed': 1\n            })\n            .field('g.retail_price,d.commission_rate')\n            .select();\n\n        let totalCommission = 0;\n        for (const item of distributedGoods) {\n            totalCommission += parseFloat(item.retail_price) * item.commission_rate / 100;\n        }\n\n        return {\n            distributedCount: distributedCount,\n            totalCommission: totalCommission.toFixed(2)\n        };\n    }\n};\n"]}