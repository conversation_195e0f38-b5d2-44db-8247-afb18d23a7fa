{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\base.js"], "names": ["module", "exports", "think", "Controller", "__before", "token", "ctx", "header", "tokenSerivce", "service", "userId", "getUserId", "url", "includes", "console", "log", "getTime", "parseInt", "Date", "now", "getLoginUserId"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,UAApB,CAA+B;AACzCC,SAAN,GAAiB;AAAA;;AAAA;AAChB;AACA,SAAMC,QAAQ,MAAKC,GAAL,CAASC,MAAT,CAAgB,iBAAhB,KAAsC,MAAKD,GAAL,CAASC,MAAT,CAAgB,iBAAhB,CAAtC,IAA4E,EAA1F;AACA,SAAMC,eAAeN,MAAMO,OAAN,CAAc,OAAd,EAAuB,KAAvB,CAArB;AACAP,SAAMQ,MAAN,GAAe,MAAMF,aAAaG,SAAb,CAAuBN,KAAvB,CAArB;;AAEA;AACA,OAAI,MAAKC,GAAL,CAASM,GAAT,CAAaC,QAAb,CAAsB,UAAtB,CAAJ,EAAuC;AACtCC,YAAQC,GAAR,CAAY,wBAAZ;AACAD,YAAQC,GAAR,CAAY,MAAZ,EAAoB,MAAKT,GAAL,CAASM,GAA7B;AACAE,YAAQC,GAAR,CAAY,WAAZ,EAAyBb,MAAMQ,MAA/B;AACA;AAXe;AAYhB;AACD;;;;AAIAM,WAAU;AACT,SAAOC,SAASC,KAAKC,GAAL,KAAa,IAAtB,CAAP;AACA;AACD;;;;AAIMC,eAAN,GAAuB;AAAA;;AAAA;AACtB,SAAMf,QAAQ,OAAKC,GAAL,CAASC,MAAT,CAAgB,iBAAhB,KAAsC,OAAKD,GAAL,CAASC,MAAT,CAAgB,iBAAhB,CAAtC,IAA4E,EAA1F;AACA,SAAMC,eAAeN,MAAMO,OAAN,CAAc,OAAd,EAAuB,KAAvB,CAArB;AACA,UAAO,MAAMD,aAAaG,SAAb,CAAuBN,KAAvB,CAAb;AAHsB;AAItB;AA7B8C,CAAhD", "file": "..\\..\\..\\src\\api\\controller\\base.js", "sourcesContent": ["module.exports = class extends think.Controller {\r\n\tasync __before() {\r\n\t\t// 根据token值获取用户id\r\n\t\tconst token = this.ctx.header['x-hioshop-token'] || this.ctx.header['X-Hioshop-Token'] || '';\r\n\t\tconst tokenSerivce = think.service('token', 'api');\r\n\t\tthink.userId = await tokenSerivce.getUserId(token);\r\n\r\n\t\t// 只在签到相关请求时打印调试信息\r\n\t\tif (this.ctx.url.includes('/signin/')) {\r\n\t\t\tconsole.log('=== Base控制器Token验证 ===');\r\n\t\t\tconsole.log('URL:', this.ctx.url);\r\n\t\t\tconsole.log('最终userId:', think.userId);\r\n\t\t}\r\n\t}\r\n\t/**\r\n\t * 获取时间戳\r\n\t * @returns {Number}\r\n\t */\r\n\tgetTime() {\r\n\t\treturn parseInt(Date.now() / 1000);\r\n\t}\r\n\t/**\r\n\t * 获取当前登录用户的id\r\n\t * @returns {*}\r\n\t */\r\n\tasync getLoginUserId() {\r\n\t\tconst token = this.ctx.header['x-hioshop-token'] || this.ctx.header['X-Hioshop-Token'] || '';\r\n\t\tconst tokenSerivce = think.service('token', 'api');\r\n\t\treturn await tokenSerivce.getUserId(token);\r\n\t}\r\n};\r\n"]}