function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

/**
 * 旺店通ERP同步服务
 */

const crypto = require('crypto');
const request = require('request-promise');
const wangdianConfig = require('../config/wangdian.js');

class WangDianSync {
    constructor(ctx = null) {
        this.config = wangdianConfig[wangdianConfig.current];
        this.ctx = ctx;
    }

    /**
     * 获取模型实例
     * @param {String} name 模型名称
     * @returns {Object} 模型实例
     */
    model(name) {
        if (this.ctx && this.ctx.model) {
            return this.ctx.model(name);
        }
        // 如果没有上下文，使用think.model
        return think.model(name);
    }

    /**
     * 生成旺店通API签名
     * @param {Object} params 参数对象
     * @param {String} appsecret 密钥
     * @returns {String} 签名
     */
    generateSign(params, appsecret) {
        // 移除sign参数（如果存在）
        const signParams = {};
        for (const key in params) {
            if (key !== 'sign') {
                signParams[key] = params[key];
            }
        }

        // 按key进行字典序排序
        const sortedParams = Object.keys(signParams).sort().map(key => [key, signParams[key]]);

        // 按照旺店通格式处理每个参数
        const formattedParts = [];
        for (const [key, value] of sortedParams) {
            // 计算键名和值的长度
            const keyLength = Buffer.byteLength(key, 'utf8');
            const valueStr = String(value);
            const valueLength = Buffer.byteLength(valueStr, 'utf8');

            // 格式化：键长度-键名:值长度-值
            const keyLengthStr = keyLength.toString().padStart(2, '0');
            const valueLengthStr = valueLength <= 9999 ? valueLength.toString().padStart(4, '0') : valueLength.toString();
            const formattedPart = `${keyLengthStr}-${key}:${valueLengthStr}-${valueStr}`;
            formattedParts.push(formattedPart);
        }

        // 用分号连接，最后一个参数不加分号
        let paramString;
        if (formattedParts.length > 1) {
            paramString = formattedParts.slice(0, -1).join(';') + ';' + formattedParts[formattedParts.length - 1];
        } else {
            paramString = formattedParts[0] || '';
        }

        // 拼接appsecret并MD5加密
        const signString = paramString + appsecret;
        const sign = crypto.createHash('md5').update(signString, 'utf8').digest('hex').toLowerCase();

        return sign;
    }

    /**
     * 调用旺店通API
     * @param {String} endpoint API端点
     * @param {Object} businessParams 业务参数
     * @returns {Promise} API响应
     */
    callApi(endpoint, businessParams) {
        var _this = this;

        return _asyncToGenerator(function* () {
            if (!_this.config.enabled) {
                console.log('[旺店通同步] 功能已禁用，跳过API调用');
                return { success: false, message: '功能已禁用' };
            }

            // 如果baseUrl已包含完整路径，直接使用；否则拼接endpoint
            const url = _this.config.baseUrl.includes('trade_push.php') ? _this.config.baseUrl : `${_this.config.baseUrl}/${endpoint}`;

            // 添加基础参数
            const requestParams = Object.assign({
                sid: _this.config.sid,
                appkey: _this.config.appkey,
                timestamp: Math.floor(Date.now() / 1000).toString()
            }, businessParams);

            // 生成签名
            requestParams.sign = _this.generateSign(requestParams, _this.config.appsecret);

            console.log(`[旺店通同步] 请求URL: ${url}`);
            console.log(`[旺店通同步] 请求参数:`, JSON.stringify(requestParams, null, 2));

            try {
                const response = yield request({
                    method: 'POST',
                    url: url,
                    form: requestParams,
                    json: true,
                    timeout: 30000
                });

                console.log(`[旺店通同步] API响应:`, JSON.stringify(response, null, 2));
                return response;
            } catch (error) {
                console.error(`[旺店通同步] API调用失败:`, error.message);
                throw error;
            }
        })();
    }

    /**
     * 转换订单数据为旺店通格式
     * @param {Object} orderInfo 订单信息
     * @param {Array} orderGoods 订单商品列表
     * @param {Object} userInfo 用户信息（包含昵称）
     * @returns {Object} 旺店通订单格式
     */
    transformOrderData(orderInfo, orderGoods, userInfo = null) {
        const currentTime = new Date();
        // 使用系统本地时间，而不是UTC时间
        const timeStr = currentTime.getFullYear() + '-' + String(currentTime.getMonth() + 1).padStart(2, '0') + '-' + String(currentTime.getDate()).padStart(2, '0') + ' ' + String(currentTime.getHours()).padStart(2, '0') + ':' + String(currentTime.getMinutes()).padStart(2, '0') + ':' + String(currentTime.getSeconds()).padStart(2, '0');

        // 处理买家昵称：使用小程序客户昵称base64编码，如果没有昵称则使用"微信用户"的base64
        let buyerNick = 'user_' + orderInfo.user_id; // 默认值
        if (userInfo && userInfo.nickname) {
            // 用户昵称已经是base64编码，直接使用
            buyerNick = userInfo.nickname;
        } else {
            // 没有昵称，使用"微信用户"的base64编码
            const defaultNickname = Buffer.from('微信用户').toString('base64');
            buyerNick = defaultNickname;
        }

        const wangdianOrder = {
            tid: orderInfo.order_sn,
            platform_id: this.config.platformId,
            trade_status: 30, // 已付款待发货
            pay_status: 2, // 已付款
            delivery_term: 1, // 款到发货
            trade_time: timeStr,
            pay_time: timeStr,
            buyer_nick: buyerNick,
            buyer_email: '',
            receiver_name: orderInfo.consignee,
            receiver_province: orderInfo.province,
            receiver_city: orderInfo.city,
            receiver_district: orderInfo.district,
            receiver_address: orderInfo.address,
            receiver_mobile: orderInfo.mobile,
            receiver_telno: '',
            pay_method: 1, // 在线支付
            logistics_type: 1,
            invoice_kind: 0,
            invoice_title: '',
            invoice_content: '',
            buyer_message: orderInfo.postscript || '',
            seller_memo: `商城订单-${orderInfo.order_sn}`,
            remark_flag: 0,
            seller_flag: 0,
            post_amount: parseFloat(orderInfo.freight_price || 0),
            cod_amount: 0,
            ext_cod_fee: 0,
            other_amount: 0,
            paid: parseFloat(orderInfo.actual_price),
            warehouse_no: this.config.warehouseNo,
            order_list: orderGoods.map((item, index) => ({
                oid: `${orderInfo.order_sn}_${index + 1}`,
                num: parseInt(item.number),
                price: parseFloat(item.retail_price),
                status: 30, // 已付款待发货
                refund_status: 0,
                goods_id: item.goods_id,
                spec_id: item.product_id,
                goods_no: item.goods_sn || item.goods_id.toString(),
                spec_no: item.product_id.toString(),
                goods_name: item.goods_name,
                spec_name: item.goods_specifition_name_value || '默认规格',
                adjust_amount: 0,
                discount: 0,
                share_discount: 0,
                commission: 0,
                remark: '',
                cid: ''
            }))
        };

        return wangdianOrder;
    }

    /**
     * 推送订单到旺店通
     * @param {Object} orderInfo 订单信息
     * @param {Array} orderGoods 订单商品列表
     * @returns {Promise} 推送结果
     */
    pushOrder(orderInfo, orderGoods) {
        var _this2 = this;

        return _asyncToGenerator(function* () {
            try {
                console.log(`[旺店通同步] 开始推送订单: ${orderInfo.order_sn}`);

                // 获取用户信息（包含昵称）
                let userInfo = null;
                try {
                    userInfo = yield _this2.model('user').where({ id: orderInfo.user_id }).find();
                    console.log(`[旺店通同步] 获取用户信息成功，用户ID: ${orderInfo.user_id}`);
                } catch (error) {
                    console.log(`[旺店通同步] 获取用户信息失败: ${error.message}`);
                }

                // 转换订单数据
                const wangdianOrder = _this2.transformOrderData(orderInfo, orderGoods, userInfo);

                // 准备API参数
                const params = {
                    shop_no: _this2.config.shopNo,
                    switch: '0', // 0表示推送订单
                    trade_list: JSON.stringify([wangdianOrder], null, 0).replace(/[\u0080-\uFFFF]/g, function (match) {
                        return '\\u' + ('0000' + match.charCodeAt(0).toString(16)).substr(-4);
                    })
                };

                // 调用API
                const result = yield _this2.callApi('trade_push.php', params);

                // 判断结果
                if (result && result.code === 0) {
                    console.log(`[旺店通同步] 订单推送成功: ${orderInfo.order_sn}`);
                    console.log(`[旺店通同步] 新增订单数: ${result.new_count || 0}`);
                    console.log(`[旺店通同步] 更新订单数: ${result.chg_count || 0}`);
                    return { success: true, message: '订单推送成功', result };
                } else {
                    const message = result ? result.message : '未知错误';
                    console.error(`[旺店通同步] 订单推送失败: ${orderInfo.order_sn}, 错误: ${message}`);
                    return { success: false, message, result };
                }
            } catch (error) {
                console.error(`[旺店通同步] 订单推送异常: ${orderInfo.order_sn}`, error);
                return { success: false, message: error.message, error };
            }
        })();
    }

    /**
     * 推送订单退款状态到旺店通
     * @param {Object} orderInfo 订单信息
     * @param {Array} orderGoods 订单商品列表
     * @returns {Promise} 推送结果
     */
    pushOrderRefund(orderInfo, orderGoods) {
        var _this3 = this;

        return _asyncToGenerator(function* () {
            try {
                console.log(`[旺店通同步] 开始推送订单退款状态: ${orderInfo.order_sn}`);

                // 获取用户信息（包含昵称）
                let userInfo = null;
                try {
                    userInfo = yield _this3.model('user').where({ id: orderInfo.user_id }).find();
                    console.log(`[旺店通同步] 获取用户信息成功，用户ID: ${orderInfo.user_id}`);
                } catch (error) {
                    console.log(`[旺店通同步] 获取用户信息失败: ${error.message}`);
                }

                // 转换订单数据为退款状态
                const wangdianOrder = _this3.transformOrderDataForRefund(orderInfo, orderGoods, userInfo);

                // 准备API参数
                const params = {
                    shop_no: _this3.config.shopNo,
                    switch: '0', // 0表示推送订单（包括状态更新）
                    trade_list: JSON.stringify([wangdianOrder], null, 0).replace(/[\u0080-\uFFFF]/g, function (match) {
                        return '\\u' + ('0000' + match.charCodeAt(0).toString(16)).substr(-4);
                    })
                };

                // 调用API
                const result = yield _this3.callApi('trade_push.php', params);

                // 判断结果
                if (result && result.code === 0) {
                    console.log(`[旺店通同步] 订单退款状态推送成功: ${orderInfo.order_sn}`);
                    console.log(`[旺店通同步] 新增订单数: ${result.new_count || 0}`);
                    console.log(`[旺店通同步] 更新订单数: ${result.chg_count || 0}`);
                    return { success: true, message: '订单退款状态推送成功', result };
                } else {
                    const message = result ? result.message : '未知错误';
                    console.error(`[旺店通同步] 订单退款状态推送失败: ${orderInfo.order_sn}, 错误: ${message}`);
                    return { success: false, message, result };
                }
            } catch (error) {
                console.error(`[旺店通同步] 订单退款状态推送异常: ${orderInfo.order_sn}`, error);
                return { success: false, message: error.message, error };
            }
        })();
    }

    /**
     * 转换订单数据为旺店通退款格式
     * @param {Object} orderInfo 订单信息
     * @param {Array} orderGoods 订单商品列表
     * @param {Object} userInfo 用户信息（包含昵称）
     * @returns {Object} 旺店通退款订单格式
     */
    transformOrderDataForRefund(orderInfo, orderGoods, userInfo = null) {
        // 使用原订单的支付时间，保持时间不变
        let tradeTimeStr = '';
        let payTimeStr = '';

        if (orderInfo.pay_time) {
            // 如果有支付时间，转换为旺店通格式
            const payTime = new Date(orderInfo.pay_time * 1000); // 假设pay_time是时间戳
            tradeTimeStr = payTime.getFullYear() + '-' + String(payTime.getMonth() + 1).padStart(2, '0') + '-' + String(payTime.getDate()).padStart(2, '0') + ' ' + String(payTime.getHours()).padStart(2, '0') + ':' + String(payTime.getMinutes()).padStart(2, '0') + ':' + String(payTime.getSeconds()).padStart(2, '0');
            payTimeStr = tradeTimeStr;
        } else {
            // 如果没有支付时间，使用当前时间
            const currentTime = new Date();
            tradeTimeStr = currentTime.getFullYear() + '-' + String(currentTime.getMonth() + 1).padStart(2, '0') + '-' + String(currentTime.getDate()).padStart(2, '0') + ' ' + String(currentTime.getHours()).padStart(2, '0') + ':' + String(currentTime.getMinutes()).padStart(2, '0') + ':' + String(currentTime.getSeconds()).padStart(2, '0');
            payTimeStr = tradeTimeStr;
        }

        // 处理买家昵称：使用小程序客户昵称base64编码，如果没有昵称则使用"微信用户"的base64
        let buyerNick = 'user_' + orderInfo.user_id; // 默认值
        if (userInfo && userInfo.nickname) {
            // 用户昵称已经是base64编码，直接使用
            buyerNick = userInfo.nickname;
        } else {
            // 没有昵称，使用"微信用户"的base64编码
            const defaultNickname = Buffer.from('微信用户').toString('base64');
            buyerNick = defaultNickname;
        }

        const wangdianOrder = {
            tid: orderInfo.order_sn,
            platform_id: this.config.platformId,
            trade_status: 80, // 已退款
            pay_status: 2, // 保持已付款状态（因为曾经付过款）
            delivery_term: 1, // 款到发货
            trade_time: tradeTimeStr, // 保持原有时间不变
            pay_time: payTimeStr, // 保持原有时间不变
            buyer_nick: buyerNick,
            buyer_email: '',
            receiver_name: orderInfo.consignee,
            receiver_province: orderInfo.province,
            receiver_city: orderInfo.city,
            receiver_district: orderInfo.district,
            receiver_address: orderInfo.address,
            receiver_mobile: orderInfo.mobile,
            receiver_telno: '',
            pay_method: 1, // 在线支付
            logistics_type: 1,
            invoice_kind: 0,
            invoice_title: '',
            invoice_content: '',
            buyer_message: orderInfo.postscript || '',
            seller_memo: `商城订单-${orderInfo.order_sn}-已退款`,
            remark_flag: 0,
            seller_flag: 0,
            post_amount: parseFloat(orderInfo.freight_price || 0),
            cod_amount: 0,
            ext_cod_fee: 0,
            other_amount: 0,
            paid: parseFloat(orderInfo.actual_price), // 保持原金额不变
            warehouse_no: this.config.warehouseNo,
            order_list: orderGoods.map((item, index) => ({
                oid: `${orderInfo.order_sn}_${index + 1}`,
                num: parseInt(item.number),
                price: parseFloat(item.retail_price),
                status: 80, // 已退款
                refund_status: 5, // 已退款完成
                goods_id: item.goods_id,
                spec_id: item.product_id,
                goods_no: item.goods_sn || item.goods_id.toString(),
                spec_no: item.product_id.toString(),
                goods_name: item.goods_name,
                spec_name: item.goods_specifition_name_value || '默认规格',
                adjust_amount: 0,
                discount: 0,
                share_discount: 0,
                commission: 0,
                remark: '',
                cid: ''
            }))
        };

        return wangdianOrder;
    }
}

module.exports = WangDianSync;