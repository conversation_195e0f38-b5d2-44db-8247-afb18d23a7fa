<template>
  <div class="data-overview-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">数据概览</h1>
      <p class="page-subtitle">实时监控核心业务指标</p>
    </div>

    <!-- 时间筛选器 -->
    <div class="time-filter-section">
      <div class="filter-buttons">
        <button
          v-for="option in timeOptions"
          :key="option.value"
          :class="['filter-btn', { active: selectedTimeRange === option.value }]"
          @click="selectTimeRange(option.value)"
        >
          {{ option.label }}
        </button>
      </div>
      
      <!-- 自定义时间选择 -->
      <div v-if="selectedTimeRange === 'custom'" class="custom-time-picker">
        <el-date-picker
          v-model="customDateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="timestamp"
          @change="onCustomTimeChange"
        />
      </div>
    </div>

    <!-- 数据卡片区域 -->
    <div class="metrics-grid">
      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-label">支付金额</span>
          <i class="el-icon-question help-icon" title="已支付订单的实际支付金额总和"></i>
        </div>
        <div class="metric-main">
          <div class="metric-value">{{ formatNumber(overview.paymentAmount) }}</div>
          <div class="metric-unit">元</div>
        </div>
        <div class="metric-change">
          <span class="change-text" :class="getChangeClass(overview.paymentAmountChange)">
            昨日 {{ formatChange(overview.paymentAmountChange) }}
          </span>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-label">订单数</span>
          <i class="el-icon-question help-icon" title="已支付订单的数量统计"></i>
        </div>
        <div class="metric-main">
          <div class="metric-value">{{ formatNumber(overview.orderCount) }}</div>
          <div class="metric-unit">单</div>
        </div>
        <div class="metric-change">
          <span class="change-text" :class="getChangeClass(overview.orderCountChange)">
            昨日 {{ formatChange(overview.orderCountChange) }}
          </span>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-label">商品访客数</span>
          <i class="el-icon-question help-icon" title="商品浏览足迹的去重用户数"></i>
        </div>
        <div class="metric-main">
          <div class="metric-value">{{ formatNumber(overview.visitorCount) }}</div>
          <div class="metric-unit">人</div>
        </div>
        <div class="metric-change">
          <span class="change-text" :class="getChangeClass(overview.visitorCountChange)">
            昨日 {{ formatChange(overview.visitorCountChange) }}
          </span>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-label">支付转化率</span>
          <i class="el-icon-question help-icon" title="支付订单数/商品访客数×100%"></i>
        </div>
        <div class="metric-main">
          <div class="metric-value">{{ overview.conversionRate }}</div>
          <div class="metric-unit">%</div>
        </div>
        <div class="metric-change">
          <span class="change-text" :class="getChangeClass(overview.conversionRateChange)">
            昨日 {{ formatChange(overview.conversionRateChange) }}
          </span>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-label">销售件数</span>
          <i class="el-icon-question help-icon" title="已支付订单的商品总件数"></i>
        </div>
        <div class="metric-main">
          <div class="metric-value">{{ formatNumber(overview.salesVolume) }}</div>
          <div class="metric-unit">件</div>
        </div>
        <div class="metric-change">
          <span class="change-text" :class="getChangeClass(overview.salesVolumeChange)">
            昨日 {{ formatChange(overview.salesVolumeChange) }}
          </span>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-header">
          <span class="metric-label">客单价</span>
          <i class="el-icon-question help-icon" title="支付金额/订单数"></i>
        </div>
        <div class="metric-main">
          <div class="metric-value">{{ formatNumber(overview.avgOrderValue) }}</div>
          <div class="metric-unit">元</div>
        </div>
        <div class="metric-change">
          <span class="change-text" :class="getChangeClass(overview.avgOrderValueChange)">
            昨日 {{ formatChange(overview.avgOrderValueChange) }}
          </span>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="chart-section">
      <div class="chart-header">
        <div class="chart-title-area">
          <h2 class="chart-title">支付金额</h2>
          <div class="chart-controls">
            <el-radio-group v-model="chartType" size="small">
              <el-radio-button label="line">折线图</el-radio-button>
              <el-radio-button label="bar">柱状图</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="chart-subtitle">{{ getChartSubtitle() }}</div>
      </div>
      <div class="chart-container">
        <div ref="paymentChart" class="payment-chart"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'DataOverviewPage',
  data() {
    return {
      selectedTimeRange: 'today',
      customDateRange: null,
      overview: {
        paymentAmount: '0.00',
        paymentAmountChange: 0,
        orderCount: 0,
        orderCountChange: 0,
        visitorCount: 0,
        visitorCountChange: 0,
        conversionRate: 0,
        conversionRateChange: 0,
        salesVolume: 0,
        salesVolumeChange: 0,
        avgOrderValue: '0.00',
        avgOrderValueChange: 0
      },
      chartData: [],
      paymentChart: null,
      loading: false,
      chartType: 'line',
      timeOptions: [
        { label: '实时', value: 'realtime' },
        { label: '今天', value: 'today' },
        { label: '昨天', value: 'yesterday' },
        { label: '近7日', value: '7days' },
        { label: '近30日', value: '30days' },
        { label: '自定义', value: 'custom' }
      ]
    }
  },
  mounted() {
    this.initChart()
    this.loadData()
    // 实时模式下每30秒刷新一次
    this.startAutoRefresh()
  },
  beforeDestroy() {
    if (this.paymentChart) {
      this.paymentChart.dispose()
    }
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
  },
  watch: {
    chartType() {
      this.updateChart()
    }
  },
  methods: {
    // 选择时间范围
    selectTimeRange(range) {
      this.selectedTimeRange = range
      if (range !== 'custom') {
        this.customDateRange = null
      }
      this.loadData()
    },

    // 自定义时间变化
    onCustomTimeChange() {
      if (this.customDateRange && this.customDateRange.length === 2) {
        this.loadData()
      }
    },

    // 获取图表副标题
    getChartSubtitle() {
      const option = this.timeOptions.find(opt => opt.value === this.selectedTimeRange)
      if (this.selectedTimeRange === 'today' || this.selectedTimeRange === 'realtime') {
        return '24小时付款金额分布'
      }
      return `${option?.label || ''}付款金额趋势`
    },

    // 加载数据
    async loadData() {
      this.loading = true
      try {
        await Promise.all([
          this.loadOverviewData(),
          this.loadChartData()
        ])
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 加载概览数据
    async loadOverviewData() {
      const params = {
        timeRange: this.selectedTimeRange
      }
      
      if (this.selectedTimeRange === 'custom' && this.customDateRange) {
        params.startTime = Math.floor(this.customDateRange[0] / 1000)
        params.endTime = Math.floor(this.customDateRange[1] / 1000)
      }

      const response = await this.axios.get('index/getDataOverview', { params })
      if (response.data.errno === 0) {
        this.overview = response.data.data
      }
    },

    // 加载图表数据
    async loadChartData() {
      const params = {
        timeRange: this.selectedTimeRange
      }
      
      if (this.selectedTimeRange === 'custom' && this.customDateRange) {
        params.startTime = Math.floor(this.customDateRange[0] / 1000)
        params.endTime = Math.floor(this.customDateRange[1] / 1000)
      }

      const response = await this.axios.get('index/getPaymentChart', { params })
      if (response.data.errno === 0) {
        this.chartData = response.data.data
        this.updateChart()
      }
    },

    // 初始化图表
    initChart() {
      this.paymentChart = echarts.init(this.$refs.paymentChart)
      this.updateChartOption()

      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        this.paymentChart.resize()
      })
    },

    // 更新图表配置
    updateChartOption() {
      if (!this.paymentChart) return

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: this.chartType === 'line' ? 'cross' : 'shadow'
          },
          formatter: function(params) {
            const data = params[0]
            return `${data.name}<br/>支付金额: ¥${data.value}`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '8%',
          top: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: this.chartType === 'bar',
          data: this.chartData.map(item => item.time),
          axisLine: {
            lineStyle: {
              color: '#e8e8e8'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#999',
            fontSize: 12
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '¥{value}',
            color: '#999',
            fontSize: 12
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: '#f5f5f5'
            }
          }
        },
        series: [{
          name: '支付金额',
          type: this.chartType,
          smooth: this.chartType === 'line',
          data: this.chartData.map(item => item.amount),
          itemStyle: {
            color: this.chartType === 'line' ? '#1890ff' : '#52c41a'
          },
          areaStyle: this.chartType === 'line' ? {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(24, 144, 255, 0.2)'
              }, {
                offset: 1, color: 'rgba(24, 144, 255, 0.05)'
              }]
            }
          } : null
        }]
      }

      this.paymentChart.setOption(option, true)
    },

    // 更新图表
    updateChart() {
      this.updateChartOption()
    },

    // 开始自动刷新
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        if (this.selectedTimeRange === 'realtime') {
          this.loadData()
        }
      }, 30000) // 30秒刷新一次
    },

    // 格式化数字显示
    formatNumber(value) {
      const num = parseFloat(value) || 0
      if (num >= 10000) {
        return (num / 10000).toFixed(2) + '万'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k'
      }
      return num.toLocaleString()
    },

    // 格式化变化百分比
    formatChange(change) {
      const num = parseFloat(change) || 0
      const sign = num >= 0 ? '+' : ''
      return `${sign}${num.toFixed(2)}%`
    },

    // 获取变化样式类
    getChangeClass(change) {
      const num = parseFloat(change) || 0
      if (num > 0) return 'positive'
      if (num < 0) return 'negative'
      return 'neutral'
    }
  }
}
</script>

<style scoped>
.data-overview-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

/* 时间筛选器 */
.time-filter-section {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.filter-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 8px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  color: #6b7280;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-btn:hover {
  border-color: #409EFF;
  color: #409EFF;
}

.filter-btn.active {
  background: #409EFF;
  border-color: #409EFF;
  color: white;
}

.custom-time-picker {
  margin-top: 16px;
}

/* 数据卡片网格 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.metric-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  transition: all 0.2s;
}

.metric-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #e0e0e0;
}

.metric-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.metric-label {
  font-size: 14px;
  color: #666;
  font-weight: 400;
}

.help-icon {
  font-size: 12px;
  color: #ccc;
  cursor: help;
}

.metric-main {
  display: flex;
  align-items: baseline;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-right: 4px;
}

.metric-unit {
  font-size: 14px;
  color: #999;
  font-weight: 400;
}

.metric-change {
  font-size: 12px;
}

.change-text {
  font-weight: 500;
}

.change-text.positive {
  color: #52c41a;
}

.change-text.negative {
  color: #ff4d4f;
}

.change-text.neutral {
  color: #999;
}

/* 图表区域 */
.chart-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.chart-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f5f5f5;
}

.chart-title-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0;
}

.chart-controls {
  display: flex;
  align-items: center;
}

.chart-subtitle {
  font-size: 12px;
  color: #999;
  margin: 0;
}

.chart-container {
  padding: 16px 24px 24px;
  position: relative;
}

.payment-chart {
  width: 100%;
  height: 350px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-overview-container {
    padding: 16px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .metric-card {
    padding: 20px;
  }

  .metric-value {
    font-size: 24px;
  }

  .filter-buttons {
    flex-direction: column;
  }

  .filter-btn {
    width: 100%;
    text-align: center;
  }
}
</style>
