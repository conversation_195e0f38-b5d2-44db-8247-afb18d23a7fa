const Base = require('./base.js');

module.exports = class extends Base {
  /**
   * 获取优惠券列表
   */
  async indexAction() {
    try {
      const { page = 1, pageSize = 20, type, status, search } = this.get();

      let where = { is_delete: 0 };
      if (type) where.type = type;
      if (status) where.status = status;
      if (search) {
        where['name|code'] = ['like', `%${search}%`];
      }

      const list = await this.model('coupons').where(where)
        .page(page, pageSize)
        .order('created_at DESC')
        .countSelect();

      // 统计每个优惠券的使用情况
      for (let coupon of list.data) {
        const stats = await this.getCouponStats(coupon.id);
        coupon.stats = stats;
      }

      return this.success(list);
    } catch (error) {
      think.logger.error('获取优惠券列表失败:', error);
      return this.fail('获取列表失败');
    }
  }

  /**
   * 获取优惠券详情
   */
  async detailAction() {
    try {
      const { id } = this.get();
      if (!id) {
        return this.fail('参数错误');
      }

      const coupon = await this.model('coupons').where({
        id: id,
        is_delete: 0
      }).find();

      if (think.isEmpty(coupon)) {
        return this.fail('优惠券不存在');
      }

      // 获取统计信息
      const stats = await this.getCouponStats(id);
      coupon.stats = stats;

      return this.success(coupon);
    } catch (error) {
      think.logger.error('获取优惠券详情失败:', error);
      return this.fail('获取详情失败');
    }
  }

  /**
   * 创建优惠券
   */
  async addAction() {
    try {
      const data = this.post();

      // 验证必填字段
      if (!data.name || !data.type || !data.discount_type || !data.discount_value) {
        return this.fail('参数不完整');
      }

      // 验证时间
      if (!data.start_time || !data.end_time) {
        return this.fail('请设置有效期');
      }

      if (new Date(data.start_time) >= new Date(data.end_time)) {
        return this.fail('开始时间必须早于结束时间');
      }

      // 生成优惠券代码
      if (!data.code) {
        data.code = this.generateCouponCode();
      } else {
        // 检查代码是否重复
        const existCoupon = await this.model('coupons').where({
          code: data.code,
          is_delete: 0
        }).find();
        if (!think.isEmpty(existCoupon)) {
          return this.fail('优惠券代码已存在');
        }
      }

      // 设置默认值
      data.min_amount = data.min_amount || 0;
      data.total_quantity = data.total_quantity || -1;
      data.per_user_limit = data.per_user_limit || 1;
      data.status = data.status || 'active';
      data.auto_distribute = data.auto_distribute || 0;

      const id = await this.model('coupons').add(data);
      return this.success({ id });
    } catch (error) {
      think.logger.error('创建优惠券失败:', error);
      return this.fail('创建失败');
    }
  }

  /**
   * 更新优惠券
   */
  async updateAction() {
    try {
      const { id } = this.post();
      if (!id) {
        return this.fail('参数错误');
      }

      const data = this.post();
      delete data.id;

      // 检查优惠券是否存在
      const coupon = await this.model('coupons').where({
        id: id,
        is_delete: 0
      }).find();

      if (think.isEmpty(coupon)) {
        return this.fail('优惠券不存在');
      }

      // 如果修改了代码，检查是否重复
      if (data.code && data.code !== coupon.code) {
        const existCoupon = await this.model('coupons').where({
          code: data.code,
          is_delete: 0,
          id: ['!=', id]
        }).find();
        if (!think.isEmpty(existCoupon)) {
          return this.fail('优惠券代码已存在');
        }
      }

      // 验证时间
      if (data.start_time && data.end_time) {
        if (new Date(data.start_time) >= new Date(data.end_time)) {
          return this.fail('开始时间必须早于结束时间');
        }
      }

      await this.model('coupons').where({ id }).update(data);
      return this.success('更新成功');
    } catch (error) {
      think.logger.error('更新优惠券失败:', error);
      return this.fail('更新失败');
    }
  }

  /**
   * 删除优惠券
   */
  async deleteAction() {
    try {
      const { id } = this.post();
      if (!id) {
        return this.fail('参数错误');
      }

      // 检查是否有用户已领取
      const userCouponCount = await this.model('user_coupons').where({
        coupon_id: id
      }).count();

      if (userCouponCount > 0) {
        return this.fail('该优惠券已有用户领取，无法删除');
      }

      await this.model('coupons').where({ id }).update({
        is_delete: 1
      });

      return this.success('删除成功');
    } catch (error) {
      think.logger.error('删除优惠券失败:', error);
      return this.fail('删除失败');
    }
  }

  /**
   * 切换优惠券状态
   */
  async toggleStatusAction() {
    try {
      const { id } = this.post();
      if (!id) {
        return this.fail('参数错误');
      }

      const coupon = await this.model('coupons').where({
        id: id,
        is_delete: 0
      }).find();

      if (think.isEmpty(coupon)) {
        return this.fail('优惠券不存在');
      }

      const newStatus = coupon.status === 'active' ? 'disabled' : 'active';
      await this.model('coupons').where({ id }).update({
        status: newStatus
      });

      return this.success('状态更新成功');
    } catch (error) {
      think.logger.error('切换优惠券状态失败:', error);
      return this.fail('状态更新失败');
    }
  }

  /**
   * 批量发放优惠券
   */
  async batchDistributeAction() {
    try {
      const { couponId, userIds, userType, quantity } = this.post();

      if (!couponId) {
        return this.fail('请选择优惠券');
      }

      const coupon = await this.model('coupons').where({
        id: couponId,
        is_delete: 0
      }).find();

      if (think.isEmpty(coupon)) {
        return this.fail('优惠券不存在');
      }

      let targetUsers = [];

      if (userType === 'all') {
        targetUsers = await this.model('user').where({is_delete: 0}).field('id').select();
      } else if (userType === 'new') {
        targetUsers = await this.model('user').where({
          is_new_user: 1,
          is_delete: 0
        }).field('id').select();
      } else if (userIds && userIds.length > 0) {
        targetUsers = userIds.map(id => ({id}));
      } else {
        return this.fail('请选择发放对象');
      }

      let successCount = 0;
      let failCount = 0;

      for (let user of targetUsers) {
        try {
          // 检查用户是否已达到领取上限
          const receivedCount = await this.model('user_coupons').where({
            user_id: user.id,
            coupon_id: couponId
          }).count();

          if (receivedCount >= coupon.per_user_limit) {
            failCount++;
            continue;
          }

          const couponCode = this.generateCouponCode();
          const expireAt = coupon.valid_days ? 
            new Date(Date.now() + coupon.valid_days * 24 * 60 * 60 * 1000) :
            new Date(coupon.end_time);

          await this.model('user_coupons').add({
            user_id: user.id,
            coupon_id: couponId,
            coupon_code: couponCode,
            expire_at: expireAt,
            source: 'batch'
          });

          successCount++;
        } catch (error) {
          failCount++;
        }
      }

      return this.success({
        message: `发放完成，成功${successCount}个，失败${failCount}个`,
        successCount,
        failCount
      });
    } catch (error) {
      think.logger.error('批量发放优惠券失败:', error);
      return this.fail('批量发放失败');
    }
  }

  /**
   * 获取优惠券统计信息
   */
  async statisticsAction() {
    try {
      const totalCoupons = await this.model('coupons').where({
        is_delete: 0
      }).count();

      const activeCoupons = await this.model('coupons').where({
        status: 'active',
        is_delete: 0
      }).count();

      const claimedCoupons = await this.model('user_coupons').count();

      const usedCoupons = await this.model('user_coupons').where({
        status: 'used'
      }).count();

      const totalDiscount = await this.model('coupon_usage_logs').sum('discount_amount') || 0;

      return this.success({
        totalCoupons,
        activeCoupons,
        claimedCoupons,
        usedCoupons,
        totalDiscount
      });
    } catch (error) {
      think.logger.error('获取优惠券统计失败:', error);
      return this.fail('获取统计失败');
    }
  }

  /**
   * 获取单个优惠券的统计信息
   */
  async getCouponStats(couponId) {
    const totalReceived = await this.model('user_coupons')
      .where({ coupon_id: couponId }).count();

    const totalUsed = await this.model('user_coupons')
      .where({ coupon_id: couponId, status: 'used' }).count();

    const totalDiscount = await this.model('coupon_usage_logs')
      .where({ coupon_id: couponId })
      .sum('discount_amount') || 0;

    return {
      totalReceived,
      totalUsed,
      totalDiscount,
      usageRate: totalReceived > 0 ? (totalUsed / totalReceived * 100).toFixed(2) : 0
    };
  }

  /**
   * 生成优惠券代码
   */
  generateCouponCode() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `CPN${timestamp}${random}`.toUpperCase();
  }
};
