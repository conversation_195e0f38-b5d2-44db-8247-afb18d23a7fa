const Base = require('./base.js');
const moment = require('moment');
const FlashSaleScheduler = require('../../common/service/flashsale_scheduler.js');

module.exports = class extends Base {

  /**
   * 获取秒杀统计数据
   * GET /admin/flashsale/statistics
   */
  async statisticsAction() {
    try {
      console.log('=== 获取秒杀统计数据 ===');

      const campaignModel = this.model('flash_sale_campaigns');
      const roundModel = this.model('flash_sale_rounds');
      const orderModel = this.model('flash_sale_orders');

      // 获取总活动数
      const totalCampaigns = await campaignModel.count();

      // 获取进行中的活动数
      const activeCampaigns = await campaignModel.where({
        status: 'active'
      }).count();

      // 获取今日轮次数
      const today = moment().format('YYYY-MM-DD');
      const todayRounds = await roundModel.where({
        start_time: ['>=', today + ' 00:00:00'],
        start_time: ['<', moment(today).add(1, 'day').format('YYYY-MM-DD') + ' 00:00:00']
      }).count();

      // 获取秒杀订单数
      const flashSaleOrders = await orderModel.count();

      // 获取秒杀销售额
      const salesResult = await orderModel.sum('total_amount');
      const flashSaleSales = salesResult || 0;

      const statistics = {
        totalCampaigns,
        activeCampaigns,
        todayRounds,
        flashSaleOrders,
        flashSaleSales: flashSaleSales.toFixed(2)
      };

      console.log('统计数据:', statistics);
      return this.success(statistics);

    } catch (error) {
      console.error('获取统计数据失败:', error);
      return this.fail('获取统计数据失败');
    }
  }

  /**
   * 获取当前和即将开始的轮次
   * GET /admin/flashsale/rounds
   */
  async roundsAction() {
    try {
      console.log('=== 获取秒杀轮次列表 ===');

      const scheduler = new FlashSaleScheduler();

      // 获取当前进行中的轮次
      const currentRounds = await scheduler.getCurrentRounds(this.model.bind(this));

      // 获取即将开始的轮次（未来30分钟内）
      const upcomingRounds = await scheduler.getUpcomingRounds(this.model.bind(this), 30);

      // 结束过期的轮次
      await scheduler.endExpiredRounds(this.model.bind(this));

      const result = {
        current: currentRounds,
        upcoming: upcomingRounds,
        total: currentRounds.length + upcomingRounds.length
      };

      console.log('轮次数据:', result);
      return this.success(result);

    } catch (error) {
      console.error('获取轮次列表失败:', error);
      return this.fail('获取轮次列表失败');
    }
  }

  /**
   * 获取秒杀活动列表
   * GET /admin/flashsale/campaigns
   */
  async campaignsAction() {
    try {
      console.log('=== 获取秒杀活动列表 ===');

      const search = this.get('search') || '';
      const status = this.get('status') || '';
      const page = parseInt(this.get('page')) || 1;
      const limit = parseInt(this.get('limit')) || 20;

      const campaignModel = this.model('flash_sale_campaigns');
      const goodsModel = this.model('goods');
      const roundModel = this.model('flash_sale_rounds');

      let where = {};

      // 状态筛选
      if (status) {
        where.status = status;
      }

      // 获取活动列表
      const campaigns = await campaignModel.where(where)
        .order('created_at DESC')
        .page(page, limit)
        .select();

      const result = [];

      // 为每个活动获取商品信息和轮次统计
      for (const campaign of campaigns) {
        const goods = await goodsModel.where({ id: campaign.goods_id }).find();

        if (!think.isEmpty(goods)) {
          // 搜索过滤
          if (search && !goods.name.toLowerCase().includes(search.toLowerCase())) {
            continue;
          }

          // 获取轮次统计
          const totalRounds = await roundModel.where({ campaign_id: campaign.id }).count();
          const activeRounds = await roundModel.where({
            campaign_id: campaign.id,
            status: 'active'
          }).count();
          const upcomingRounds = await roundModel.where({
            campaign_id: campaign.id,
            status: 'upcoming'
          }).count();

          // 计算总销量
          const soldResult = await roundModel.where({ campaign_id: campaign.id }).sum('sold_count');
          const totalSold = soldResult || 0;

          const item = {
            id: campaign.id,
            name: campaign.name,
            goodsName: goods.name,
            goodsImage: goods.list_pic_url || goods.primary_pic_url || '',
            originalPrice: campaign.original_price,
            flashPrice: campaign.flash_price,
            totalStock: campaign.total_stock,
            stockPerRound: campaign.stock_per_round,
            totalSold: totalSold,
            limitQuantity: campaign.limit_quantity,
            startDate: campaign.start_date,
            endDate: campaign.end_date,
            dailyStartTime: campaign.daily_start_time,
            dailyEndTime: campaign.daily_end_time,
            roundDuration: campaign.round_duration,
            breakDuration: campaign.break_duration,
            status: campaign.status,
            totalRounds: totalRounds,
            activeRounds: activeRounds,
            upcomingRounds: upcomingRounds,
            createdAt: campaign.created_at
          };

          result.push(item);
        }
      }

      console.log('活动列表:', result.length, '个活动');
      return this.success(result);

    } catch (error) {
      console.error('获取活动列表失败:', error);
      return this.fail('获取活动列表失败');
    }
  }

  /**
   * 创建秒杀活动
   * POST /admin/flashsale/create
   */
  async createAction() {
    try {
      console.log('=== 创建秒杀活动 ===');

      const data = this.post();
      console.log('创建数据:', data);

      // 验证必填字段
      if (!data.name || !data.goodsId || !data.flashPrice || !data.totalStock || !data.stockPerRound) {
        return this.fail('请填写完整的活动信息');
      }

      // 验证日期
      if (!data.startDate || !data.endDate) {
        return this.fail('请设置活动日期');
      }

      if (new Date(data.startDate) >= new Date(data.endDate)) {
        return this.fail('开始日期必须早于结束日期');
      }

      const campaignModel = this.model('flash_sale_campaigns');
      const goodsModel = this.model('goods');

      // 检查商品是否存在
      const goods = await goodsModel.where({ id: data.goodsId }).find();
      if (think.isEmpty(goods)) {
        return this.fail('商品不存在');
      }

      // 检查商品是否已经有进行中的活动
      const existingCampaign = await campaignModel.where({
        goods_id: data.goodsId,
        status: ['IN', ['active', 'draft']]
      }).find();

      if (!think.isEmpty(existingCampaign)) {
        return this.fail('该商品已有进行中的秒杀活动');
      }

      // 创建活动配置
      const campaignData = {
        name: data.name,
        goods_id: data.goodsId,
        flash_price: data.flashPrice,
        original_price: data.originalPrice || goods.retail_price,
        total_stock: data.totalStock,
        stock_per_round: data.stockPerRound,
        limit_quantity: data.limitQuantity || 1,
        start_date: data.startDate,
        end_date: data.endDate,
        daily_start_time: data.dailyStartTime || '09:00:00',
        daily_end_time: data.dailyEndTime || '22:00:00',
        round_duration: data.roundDuration || 300, // 默认5分钟
        break_duration: data.breakDuration || 0,    // 默认无间隔
        status: 'draft',
        auto_start: data.autoStart || 1
      };

      const campaignId = await campaignModel.add(campaignData);

      if (campaignId) {
        console.log('秒杀活动创建成功，ID:', campaignId);

        // 如果设置为自动开始，则生成轮次
        if (data.autoStart) {
          const campaign = await campaignModel.where({ id: campaignId }).find();
          const scheduler = new FlashSaleScheduler();
          const roundCount = await scheduler.generateRounds(campaign, this.model.bind(this));

          // 更新状态为active
          await campaignModel.where({ id: campaignId }).update({ status: 'active' });

          console.log(`自动生成了 ${roundCount} 个轮次`);
        }

        return this.success({ id: campaignId });
      } else {
        return this.fail('创建失败');
      }

    } catch (error) {
      console.error('创建秒杀活动失败:', error);
      return this.fail('创建失败: ' + error.message);
    }
  }

  /**
   * 启动秒杀活动
   * POST /admin/flashsale/start
   */
  async startAction() {
    try {
      const campaignId = this.post('campaignId');
      if (!campaignId) {
        return this.fail('请提供活动ID');
      }

      const campaignModel = this.model('flash_sale_campaigns');
      const campaign = await campaignModel.where({ id: campaignId }).find();

      if (think.isEmpty(campaign)) {
        return this.fail('活动不存在');
      }

      if (campaign.status !== 'draft') {
        return this.fail('只能启动草稿状态的活动');
      }

      // 生成轮次
      const scheduler = new FlashSaleScheduler();
      const roundCount = await scheduler.generateRounds(campaign, this.model.bind(this));

      // 更新状态
      await campaignModel.where({ id: campaignId }).update({ status: 'active' });

      console.log(`活动启动成功，生成了 ${roundCount} 个轮次`);
      return this.success({ roundCount });

    } catch (error) {
      console.error('启动活动失败:', error);
      return this.fail('启动失败: ' + error.message);
    }
  }
};
