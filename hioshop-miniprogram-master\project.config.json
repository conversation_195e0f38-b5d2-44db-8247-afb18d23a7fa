{"description": "项目配置文件。", "setting": {"urlCheck": false, "es6": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "checkInvalidKey": true, "checkSiteMap": false, "uploadWithSourceMap": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "compileWorklet": false, "uglifyFileName": false, "enhance": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "libVersion": "3.8.9", "appid": "wx919ca2ec612e6ecb", "projectname": "%E7%BE%8E%E6%B1%90%E7%BC%98", "isGameTourist": false, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "plugin": {"current": -1, "list": []}, "game": {"currentL": -1, "list": []}, "miniprogram": {"current": 2, "list": []}}, "packOptions": {"ignore": [], "include": []}, "editorSetting": {}}