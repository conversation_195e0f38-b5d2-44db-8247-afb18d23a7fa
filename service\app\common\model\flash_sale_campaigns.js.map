{"version": 3, "sources": ["..\\..\\..\\src\\common\\model\\flash_sale_campaigns.js"], "names": ["module", "exports", "think", "Model", "getCampaignInfo", "campaignId", "campaign", "where", "id", "find", "isEmpty", "goodsModel", "model", "goods", "goods_id", "canParticipate", "reason", "status", "now", "Date", "startDate", "start_date", "endDate", "end_date", "getUserRecord", "userId", "recordModel", "campaign_id", "user_id", "updateUserRecord", "quantity", "existing", "add", "total_purchased", "last_purchase_time", "update"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;;AAEzC;;;AAGMC,iBAAN,CAAsBC,UAAtB,EAAkC;AAAA;;AAAA;AAChC,YAAMC,WAAW,MAAM,MAAKC,KAAL,CAAW,EAAEC,IAAIH,UAAN,EAAX,EAA+BI,IAA/B,EAAvB;AACA,UAAIP,MAAMQ,OAAN,CAAcJ,QAAd,CAAJ,EAA6B;AAC3B,eAAO,IAAP;AACD;;AAED;AACA,YAAMK,aAAa,MAAKC,KAAL,CAAW,OAAX,CAAnB;AACA,YAAMC,QAAQ,MAAMF,WAAWJ,KAAX,CAAiB,EAAEC,IAAIF,SAASQ,QAAf,EAAjB,EAA4CL,IAA5C,EAApB;;AAEA,+BACKH,QADL;AAEEO,eAAOA;AAFT;AAVgC;AAcjC;;AAED;;;AAGME,gBAAN,CAAqBV,UAArB,EAAiC;AAAA;;AAAA;AAC/B,YAAMC,WAAW,MAAM,OAAKC,KAAL,CAAW,EAAEC,IAAIH,UAAN,EAAX,EAA+BI,IAA/B,EAAvB;AACA,UAAIP,MAAMQ,OAAN,CAAcJ,QAAd,CAAJ,EAA6B;AAC3B,eAAO,EAAES,gBAAgB,KAAlB,EAAyBC,QAAQ,OAAjC,EAAP;AACD;;AAED,UAAIV,SAASW,MAAT,KAAoB,QAAxB,EAAkC;AAChC,eAAO,EAAEF,gBAAgB,KAAlB,EAAyBC,QAAQ,WAAjC,EAAP;AACD;;AAED,YAAME,MAAM,IAAIC,IAAJ,EAAZ;AACA,YAAMC,YAAY,IAAID,IAAJ,CAASb,SAASe,UAAlB,CAAlB;AACA,YAAMC,UAAU,IAAIH,IAAJ,CAASb,SAASiB,QAAT,GAAoB,WAA7B,CAAhB;;AAEA,UAAIL,MAAME,SAAN,IAAmBF,MAAMI,OAA7B,EAAsC;AACpC,eAAO,EAAEP,gBAAgB,KAAlB,EAAyBC,QAAQ,WAAjC,EAAP;AACD;;AAED,aAAO,EAAED,gBAAgB,IAAlB,EAAP;AAlB+B;AAmBhC;;AAED;;;AAGMS,eAAN,CAAoBnB,UAApB,EAAgCoB,MAAhC,EAAwC;AAAA;;AAAA;AACtC,YAAMC,cAAc,OAAKd,KAAL,CAAW,yBAAX,CAApB;AACA,aAAO,MAAMc,YAAYnB,KAAZ,CAAkB;AAC7BoB,qBAAatB,UADgB;AAE7BuB,iBAASH;AAFoB,OAAlB,EAGVhB,IAHU,EAAb;AAFsC;AAMvC;;AAED;;;AAGMoB,kBAAN,CAAuBxB,UAAvB,EAAmCoB,MAAnC,EAA2CK,QAA3C,EAAqD;AAAA;;AAAA;AACnD,YAAMJ,cAAc,OAAKd,KAAL,CAAW,yBAAX,CAApB;;AAEA,YAAMmB,WAAW,MAAML,YAAYnB,KAAZ,CAAkB;AACvCoB,qBAAatB,UAD0B;AAEvCuB,iBAASH;AAF8B,OAAlB,EAGpBhB,IAHoB,EAAvB;;AAKA,UAAIP,MAAMQ,OAAN,CAAcqB,QAAd,CAAJ,EAA6B;AAC3B;AACA,eAAO,MAAML,YAAYM,GAAZ,CAAgB;AAC3BL,uBAAatB,UADc;AAE3BuB,mBAASH,MAFkB;AAG3BQ,2BAAiBH,QAHU;AAI3BI,8BAAoB,IAAIf,IAAJ;AAJO,SAAhB,CAAb;AAMD,OARD,MAQO;AACL;AACA,eAAO,MAAMO,YAAYnB,KAAZ,CAAkB;AAC7BoB,uBAAatB,UADgB;AAE7BuB,mBAASH;AAFoB,SAAlB,EAGVU,MAHU,CAGH;AACRF,2BAAiBF,SAASE,eAAT,GAA2BH,QADpC;AAERI,8BAAoB,IAAIf,IAAJ;AAFZ,SAHG,CAAb;AAOD;AAzBkD;AA0BpD;AArFwC,CAA3C", "file": "..\\..\\..\\src\\common\\model\\flash_sale_campaigns.js", "sourcesContent": ["module.exports = class extends think.Model {\n  \n  /**\n   * 获取活动的基本信息\n   */\n  async getCampaignInfo(campaignId) {\n    const campaign = await this.where({ id: campaignId }).find();\n    if (think.isEmpty(campaign)) {\n      return null;\n    }\n    \n    // 获取商品信息\n    const goodsModel = this.model('goods');\n    const goods = await goodsModel.where({ id: campaign.goods_id }).find();\n    \n    return {\n      ...campaign,\n      goods: goods\n    };\n  }\n  \n  /**\n   * 检查活动是否可以参与\n   */\n  async canParticipate(campaignId) {\n    const campaign = await this.where({ id: campaignId }).find();\n    if (think.isEmpty(campaign)) {\n      return { canParticipate: false, reason: '活动不存在' };\n    }\n    \n    if (campaign.status !== 'active') {\n      return { canParticipate: false, reason: '活动未开始或已结束' };\n    }\n    \n    const now = new Date();\n    const startDate = new Date(campaign.start_date);\n    const endDate = new Date(campaign.end_date + ' 23:59:59');\n    \n    if (now < startDate || now > endDate) {\n      return { canParticipate: false, reason: '不在活动时间范围内' };\n    }\n    \n    return { canParticipate: true };\n  }\n  \n  /**\n   * 获取用户在活动中的参与记录\n   */\n  async getUserRecord(campaignId, userId) {\n    const recordModel = this.model('flash_sale_user_records');\n    return await recordModel.where({\n      campaign_id: campaignId,\n      user_id: userId\n    }).find();\n  }\n  \n  /**\n   * 更新用户参与记录\n   */\n  async updateUserRecord(campaignId, userId, quantity) {\n    const recordModel = this.model('flash_sale_user_records');\n    \n    const existing = await recordModel.where({\n      campaign_id: campaignId,\n      user_id: userId\n    }).find();\n    \n    if (think.isEmpty(existing)) {\n      // 创建新记录\n      return await recordModel.add({\n        campaign_id: campaignId,\n        user_id: userId,\n        total_purchased: quantity,\n        last_purchase_time: new Date()\n      });\n    } else {\n      // 更新现有记录\n      return await recordModel.where({\n        campaign_id: campaignId,\n        user_id: userId\n      }).update({\n        total_purchased: existing.total_purchased + quantity,\n        last_purchase_time: new Date()\n      });\n    }\n  }\n};\n"]}