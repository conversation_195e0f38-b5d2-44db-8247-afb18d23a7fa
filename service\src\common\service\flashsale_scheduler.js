/**
 * 秒杀轮次自动生成和调度服务
 * 负责生成每5分钟的秒杀轮次
 */

const moment = require('moment');

module.exports = class FlashSaleScheduler {
  
  /**
   * 为指定活动生成轮次
   * @param {Object} campaign 活动配置
   * @param {Object} model 数据模型
   */
  async generateRounds(campaign, model) {
    try {
      console.log(`=== 为活动 ${campaign.name} 生成轮次 ===`);
      
      const roundModel = model('flash_sale_rounds');
      
      // 计算总天数
      const startDate = moment(campaign.start_date);
      const endDate = moment(campaign.end_date);
      const totalDays = endDate.diff(startDate, 'days') + 1;
      
      console.log(`活动周期：${totalDays} 天`);
      
      let roundNumber = 1;
      const rounds = [];
      
      // 遍历每一天
      for (let day = 0; day < totalDays; day++) {
        const currentDate = moment(startDate).add(day, 'days');
        
        // 构建当天的开始和结束时间
        const dayStart = moment(currentDate).set({
          hour: moment(campaign.daily_start_time, 'HH:mm:ss').hour(),
          minute: moment(campaign.daily_start_time, 'HH:mm:ss').minute(),
          second: 0,
          millisecond: 0
        });
        
        const dayEnd = moment(currentDate).set({
          hour: moment(campaign.daily_end_time, 'HH:mm:ss').hour(),
          minute: moment(campaign.daily_end_time, 'HH:mm:ss').minute(),
          second: 0,
          millisecond: 0
        });
        
        console.log(`生成 ${currentDate.format('YYYY-MM-DD')} 的轮次`);
        
        // 在当天时间范围内生成轮次
        let currentTime = moment(dayStart);
        
        while (currentTime.isBefore(dayEnd)) {
          const roundStart = moment(currentTime);
          const roundEnd = moment(currentTime).add(campaign.round_duration, 'seconds');
          
          // 如果轮次结束时间超过当天结束时间，则截止到当天结束
          if (roundEnd.isAfter(dayEnd)) {
            break;
          }
          
          const round = {
            campaign_id: campaign.id,
            round_number: roundNumber,
            start_time: roundStart.format('YYYY-MM-DD HH:mm:ss'),
            end_time: roundEnd.format('YYYY-MM-DD HH:mm:ss'),
            stock: campaign.stock_per_round,
            sold_count: 0,
            status: roundStart.isAfter(moment()) ? 'upcoming' : 'ended'
          };
          
          rounds.push(round);
          roundNumber++;
          
          // 移动到下一个轮次开始时间
          currentTime.add(campaign.round_duration + campaign.break_duration, 'seconds');
        }
      }
      
      console.log(`总共生成 ${rounds.length} 个轮次`);
      
      // 批量插入轮次
      if (rounds.length > 0) {
        // 先删除该活动的现有轮次
        await roundModel.where({ campaign_id: campaign.id }).delete();
        
        // 批量插入新轮次
        for (const round of rounds) {
          await roundModel.add(round);
        }
        
        console.log(`✅ 成功生成 ${rounds.length} 个轮次`);
      }
      
      return rounds.length;
      
    } catch (error) {
      console.error('生成轮次失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取当前进行中的轮次
   * @param {Object} model 数据模型
   */
  async getCurrentRounds(model) {
    try {
      const roundModel = model('flash_sale_rounds');
      const campaignModel = model('flash_sale_campaigns');
      
      const now = moment().format('YYYY-MM-DD HH:mm:ss');
      
      // 获取当前时间的活跃轮次
      const activeRounds = await roundModel.where({
        start_time: ['<=', now],
        end_time: ['>', now],
        status: 'upcoming'
      }).select();
      
      // 更新状态为active
      for (const round of activeRounds) {
        await roundModel.where({ id: round.id }).update({ status: 'active' });
        round.status = 'active';
      }
      
      // 获取活动信息
      const rounds = [];
      for (const round of activeRounds) {
        const campaign = await campaignModel.where({ id: round.campaign_id }).find();
        if (campaign && campaign.status === 'active') {
          rounds.push({
            ...round,
            campaign: campaign
          });
        }
      }
      
      return rounds;
      
    } catch (error) {
      console.error('获取当前轮次失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取即将开始的轮次
   * @param {Object} model 数据模型
   * @param {number} minutes 未来多少分钟内
   */
  async getUpcomingRounds(model, minutes = 30) {
    try {
      const roundModel = model('flash_sale_rounds');
      const campaignModel = model('flash_sale_campaigns');
      
      const now = moment();
      const futureTime = moment().add(minutes, 'minutes');
      
      const upcomingRounds = await roundModel.where({
        start_time: ['BETWEEN', [now.format('YYYY-MM-DD HH:mm:ss'), futureTime.format('YYYY-MM-DD HH:mm:ss')]],
        status: 'upcoming'
      }).order('start_time ASC').select();
      
      // 获取活动信息
      const rounds = [];
      for (const round of upcomingRounds) {
        const campaign = await campaignModel.where({ id: round.campaign_id }).find();
        if (campaign && campaign.status === 'active') {
          rounds.push({
            ...round,
            campaign: campaign,
            countdown: moment(round.start_time).diff(now, 'seconds')
          });
        }
      }
      
      return rounds;
      
    } catch (error) {
      console.error('获取即将开始的轮次失败:', error);
      throw error;
    }
  }
  
  /**
   * 结束过期的轮次
   * @param {Object} model 数据模型
   */
  async endExpiredRounds(model) {
    try {
      const roundModel = model('flash_sale_rounds');
      const now = moment().format('YYYY-MM-DD HH:mm:ss');
      
      const result = await roundModel.where({
        end_time: ['<', now],
        status: ['IN', ['upcoming', 'active']]
      }).update({ status: 'ended' });
      
      console.log(`结束了 ${result} 个过期轮次`);
      return result;
      
    } catch (error) {
      console.error('结束过期轮次失败:', error);
      throw error;
    }
  }
  
  /**
   * 检查并更新库存
   * @param {number} roundId 轮次ID
   * @param {number} quantity 购买数量
   * @param {Object} model 数据模型
   */
  async checkAndUpdateStock(roundId, quantity, model) {
    try {
      const roundModel = model('flash_sale_rounds');
      
      // 获取轮次信息
      const round = await roundModel.where({ id: roundId }).find();
      if (!round) {
        throw new Error('轮次不存在');
      }
      
      // 检查库存
      const remainingStock = round.stock - round.sold_count;
      if (remainingStock < quantity) {
        throw new Error('库存不足');
      }
      
      // 更新已售数量
      const result = await roundModel.where({ id: roundId }).increment('sold_count', quantity);
      
      return result;
      
    } catch (error) {
      console.error('更新库存失败:', error);
      throw error;
    }
  }
};
