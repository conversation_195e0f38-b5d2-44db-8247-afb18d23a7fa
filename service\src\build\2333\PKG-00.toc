('D:\\py-ide\\hioshop-miniprogram-master\\service\\src\\build\\2333\\2333.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True},
 [('PYZ-00.pyz',
   'D:\\py-ide\\hioshop-miniprogram-master\\service\\src\\build\\2333\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\py-ide\\hioshop-miniprogram-master\\service\\src\\build\\2333\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\py-ide\\hioshop-miniprogram-master\\service\\src\\build\\2333\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\py-ide\\hioshop-miniprogram-master\\service\\src\\build\\2333\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\py-ide\\hioshop-miniprogram-master\\service\\src\\build\\2333\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\py-ide\\hioshop-miniprogram-master\\service\\src\\build\\2333\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('2333',
   'D:\\py-ide\\hioshop-miniprogram-master\\service\\src\\2333.py',
   'PYSOURCE'),
  ('python37.dll', 'D:\\Program Files\\Python37\\python37.dll', 'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\Program Files\\Python37\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('ucrtbase.dll', 'D:\\ProgramData\\Miniconda3\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('pythonnet\\runtime\\Python.Runtime.dll',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pythonnet\\runtime\\Python.Runtime.dll',
   'BINARY'),
  ('clr_loader\\ffi\\dlls\\amd64\\ClrLoader.dll',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\clr_loader\\ffi\\dlls\\amd64\\ClrLoader.dll',
   'BINARY'),
  ('clr_loader\\ffi\\dlls\\x86\\ClrLoader.dll',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\clr_loader\\ffi\\dlls\\x86\\ClrLoader.dll',
   'BINARY'),
  ('select.pyd', 'D:\\Program Files\\Python37\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'D:\\Program Files\\Python37\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\Program Files\\Python37\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Program Files\\Python37\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Program Files\\Python37\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'D:\\Program Files\\Python37\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'D:\\Program Files\\Python37\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'D:\\Program Files\\Python37\\lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp37-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\_cffi_backend.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Program Files\\Python37\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('simplejson\\_speedups.cp37-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\simplejson\\_speedups.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_brotli.cp37-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\_brotli.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_openssl.pyd',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_openssl.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.pyd',
   'D:\\Program Files\\Python37\\lib\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('win32\\win32wnet.pyd',
   'D:\\Program Files\\Python37\\lib\\site-packages\\win32\\win32wnet.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Program Files\\Python37\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp37-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\markupsafe\\_speedups.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('pywin32_system32\\pywintypes37.dll',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pywin32_system32\\pywintypes37.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'D:\\Program Files\\Python37\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'D:\\Program Files\\Python37\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('MSVCP140.dll', 'D:\\ProgramData\\Miniconda3\\MSVCP140.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\ProgramData\\Miniconda3\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('python3.dll', 'D:\\Program Files\\Python37\\python3.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\ProgramData\\Miniconda3\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('base_library.zip',
   'D:\\py-ide\\hioshop-miniprogram-master\\service\\src\\build\\2333\\base_library.zip',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\Program Files\\Python37\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('cryptography-3.2.1.dist-info\\METADATA',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-3.2.1.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\RECORD',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\RECORD',
   'DATA'),
  ('cryptography-3.2.1.dist-info\\LICENSE',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-3.2.1.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\top_level.txt',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\top_level.txt',
   'DATA'),
  ('cryptography-3.2.1.dist-info\\top_level.txt',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-3.2.1.dist-info\\top_level.txt',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\Program Files\\Python37\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('cryptography-3.2.1.dist-info\\LICENSE.BSD',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-3.2.1.dist-info\\LICENSE.BSD',
   'DATA'),
  ('cryptography-3.2.1.dist-info\\WHEEL',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-3.2.1.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-3.2.1.dist-info\\LICENSE.APACHE',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-3.2.1.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-3.2.1.dist-info\\RECORD',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-3.2.1.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\WHEEL',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-3.2.1.dist-info\\INSTALLER',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-3.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-3.2.1.dist-info\\AUTHORS.rst',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-3.2.1.dist-info\\AUTHORS.rst',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\METADATA',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\METADATA',
   'DATA'),
  ('cryptography-3.2.1.dist-info\\LICENSE.PSF',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-3.2.1.dist-info\\LICENSE.PSF',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\LICENSE',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-6.7.0.dist-info\\INSTALLER',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\importlib_metadata-6.7.0.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.42.0.dist-info\\RECORD',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\wheel-0.42.0.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.42.0.dist-info\\WHEEL',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\wheel-0.42.0.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.42.0.dist-info\\INSTALLER',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\wheel-0.42.0.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.42.0.dist-info\\entry_points.txt',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\wheel-0.42.0.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.42.0.dist-info\\METADATA',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\wheel-0.42.0.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.42.0.dist-info\\LICENSE.txt',
   'd:\\program '
   'files\\python37\\lib\\site-packages\\wheel-0.42.0.dist-info\\LICENSE.txt',
   'DATA')],
 False,
 False,
 False,
 [],
 None,
 None,
 None)
