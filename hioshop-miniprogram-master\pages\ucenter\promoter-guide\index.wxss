/* 推广员介绍页面样式 - 与小程序整体风格保持一致 */
page {
  min-height: 100%;
  background-color: #f5f5f5;
}

.container {
  min-height: 100%;
  padding-bottom: 40rpx;
}

/* 顶部横幅区域 - 使用与"我的"页面相同的渐变色 */
.hero-section {
  background: linear-gradient(135deg, #D4A373 0%, #E9C46A 50%, #F4A261 100%);
  padding: 60rpx 30rpx 80rpx;
  color: white;
  position: relative;
}

.hero-content {
  text-align: center;
}

.hero-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.hero-desc {
  font-size: 28rpx;
  opacity: 0.8;
  margin-bottom: 60rpx;
}

/* 收益亮点 */
.highlight-stats {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx;
  padding: 40rpx 20rpx;
  backdrop-filter: blur(10rpx);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 通用区块样式 */
.benefits-section,
.steps-section,
.cases-section,
.faq-section {
  margin: 40rpx 30rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
}

/* 三大收益卡片 */
.benefits-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20rpx;
}

.benefit-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.benefit-content {
  width: 100%;
}

.benefit-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.benefit-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.benefit-highlight {
  font-size: 24rpx;
  color: #D4A373;
  background: rgba(212, 163, 115, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

/* 三步流程 */
.steps-container {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.step-item {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  background: #D4A373;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
}

.step-arrow {
  text-align: center;
  font-size: 40rpx;
  color: #D4A373;
  margin: 20rpx 0;
}

/* 成功案例 */
.cases-list {
  background: white;
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.case-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.case-item:last-child {
  border-bottom: none;
}

.case-avatar {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #D4A373 0%, #E9C46A 100%);
  border-radius: 50%;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.case-info {
  flex: 1;
}

.case-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.case-desc {
  font-size: 24rpx;
  color: #666;
}

.case-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #D4A373;
}

.animated-amount {
  transition: all 0.1s ease-out;
}

/* 常见问题 */
.faq-list {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.faq-item {
  border-bottom: 1rpx solid #f0f0f0;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  font-size: 28rpx;
  color: #333;
}

.faq-arrow {
  font-size: 24rpx;
  color: #999;
  transition: transform 0.3s;
}

.faq-arrow.expanded {
  transform: rotate(180deg);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s;
  background: #f8f9fa;
}

.faq-answer.show {
  max-height: 200rpx;
}

.faq-answer text {
  display: block;
  padding: 30rpx;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 平台数据 */
.platform-stats {
  background: white;
  margin: 40rpx 30rpx;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.platform-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
}

.platform-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.platform-item {
  text-align: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 15rpx;
}

.platform-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #D4A373;
  margin-bottom: 10rpx;
}

.platform-label {
  font-size: 24rpx;
  color: #666;
}

/* 底部行动按钮 */
.action-section {
  margin: 60rpx 30rpx 40rpx;
  text-align: center;
}

.start-btn {
  background: linear-gradient(135deg, #D4A373 0%, #E9C46A 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 30rpx 60rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 30rpx rgba(212, 163, 115, 0.3);
  margin-bottom: 20rpx;
}

.start-btn[disabled] {
  background: #ccc;
  box-shadow: none;
}

.action-tip {
  font-size: 24rpx;
  color: #999;
}
