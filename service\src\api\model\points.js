module.exports = class extends think.Model {

  get tableName() {
    return 'user_points';
  }

  /**
   * 获取用户积分信息
   */
  async getUserPoints(userId) {
    try {
      let userPoints = await this.model('user_points').where({
        user_id: userId
      }).find();

      // 如果用户没有积分记录，创建一个
      if (think.isEmpty(userPoints)) {
        const pointsId = await this.model('user_points').add({
          user_id: userId,
          total_points: 0,
          available_points: 0,
          used_points: 0,
          created_at: new Date(),
          updated_at: new Date()
        });
        
        userPoints = {
          id: pointsId,
          user_id: userId,
          total_points: 0,
          available_points: 0,
          used_points: 0
        };
      }

      return userPoints;
    } catch (error) {
      console.error('获取用户积分失败:', error);
      return null;
    }
  }

  /**
   * 增加用户积分
   */
  async addUserPoints(userId, points, type = 'signin', sourceId = null, description = '') {
    try {
      // 开始事务
      return await this.model('user_points').transaction(async () => {
        // 1. 获取当前积分
        const userPoints = await this.getUserPoints(userId);
        if (!userPoints) {
          throw new Error('获取用户积分失败');
        }

        // 2. 更新积分
        const newTotalPoints = userPoints.total_points + points;
        const newAvailablePoints = userPoints.available_points + points;

        await this.model('user_points').where({
          user_id: userId
        }).update({
          total_points: newTotalPoints,
          available_points: newAvailablePoints,
          updated_at: new Date()
        });

        // 3. 记录积分日志
        await this.model('points_log').add({
          user_id: userId,
          points_change: points,
          points_type: type,
          source_id: sourceId,
          description: description,
          balance_after: newTotalPoints,
          created_at: new Date()
        });

        return {
          success: true,
          totalPoints: newTotalPoints,
          availablePoints: newAvailablePoints
        };
      });
    } catch (error) {
      console.error('增加用户积分失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 消费用户积分
   */
  async consumeUserPoints(userId, points, type = 'consume', sourceId = null, description = '') {
    try {
      // 开始事务
      return await this.model('user_points').transaction(async () => {
        // 1. 获取当前积分
        const userPoints = await this.getUserPoints(userId);
        if (!userPoints) {
          throw new Error('获取用户积分失败');
        }

        // 2. 检查积分是否足够
        if (userPoints.available_points < points) {
          throw new Error('积分不足');
        }

        // 3. 更新积分
        const newAvailablePoints = userPoints.available_points - points;
        const newUsedPoints = userPoints.used_points + points;

        await this.model('user_points').where({
          user_id: userId
        }).update({
          available_points: newAvailablePoints,
          used_points: newUsedPoints,
          updated_at: new Date()
        });

        // 4. 记录积分日志
        await this.model('points_log').add({
          user_id: userId,
          points_change: -points, // 负数表示消费
          points_type: type,
          source_id: sourceId,
          description: description,
          balance_after: userPoints.total_points, // 总积分不变
          created_at: new Date()
        });

        return {
          success: true,
          totalPoints: userPoints.total_points,
          availablePoints: newAvailablePoints,
          usedPoints: newUsedPoints
        };
      });
    } catch (error) {
      console.error('消费用户积分失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取用户积分日志
   */
  async getUserPointsLog(userId, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;
      
      const logs = await this.model('points_log').where({
        user_id: userId
      }).field('points_change, points_type, description, balance_after, created_at')
        .order('created_at DESC')
        .limit(offset, limit)
        .select();

      const total = await this.model('points_log').where({
        user_id: userId
      }).count();

      return {
        logs: logs,
        total: total,
        page: page,
        limit: limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      console.error('获取积分日志失败:', error);
      return null;
    }
  }

  /**
   * 获取积分统计信息
   */
  async getPointsStats(userId) {
    try {
      // 获取用户积分信息
      const userPoints = await this.getUserPoints(userId);
      
      // 获取本月获得积分
      const currentMonth = this.getCurrentMonth();
      const monthEarned = await this.model('points_log').where({
        user_id: userId,
        points_change: ['>', 0],
        created_at: ['LIKE', `${currentMonth}%`]
      }).sum('points_change');

      // 获取本月消费积分
      const monthUsed = await this.model('points_log').where({
        user_id: userId,
        points_change: ['<', 0],
        created_at: ['LIKE', `${currentMonth}%`]
      }).sum('points_change');

      // 获取签到获得的积分
      const signinEarned = await this.model('points_log').where({
        user_id: userId,
        points_type: ['IN', ['signin', 'bonus']]
      }).sum('points_change');

      return {
        totalPoints: userPoints.total_points,
        availablePoints: userPoints.available_points,
        usedPoints: userPoints.used_points,
        monthEarned: monthEarned || 0,
        monthUsed: Math.abs(monthUsed || 0),
        signinEarned: signinEarned || 0
      };
    } catch (error) {
      console.error('获取积分统计失败:', error);
      return null;
    }
  }

  /**
   * 获取积分排行榜
   */
  async getPointsRanking(limit = 10) {
    try {
      const sql = `
        SELECT 
          up.user_id,
          u.nickname,
          u.avatar,
          up.total_points,
          up.available_points
        FROM user_points up
        LEFT JOIN user u ON up.user_id = u.id
        WHERE up.total_points > 0
        ORDER BY up.total_points DESC
        LIMIT ${limit}
      `;

      const rankings = await this.model('user_points').query(sql);
      return rankings;
    } catch (error) {
      console.error('获取积分排行榜失败:', error);
      return [];
    }
  }

  /**
   * 系统积分统计
   */
  async getSystemPointsStats() {
    try {
      // 总积分发放量
      const totalIssued = await this.model('points_log').where({
        points_change: ['>', 0]
      }).sum('points_change');

      // 总积分消费量
      const totalConsumed = await this.model('points_log').where({
        points_change: ['<', 0]
      }).sum('points_change');

      // 当前流通积分
      const totalCirculation = await this.model('user_points').sum('available_points');

      // 今日发放积分
      const today = this.getCurrentDate();
      const todayIssued = await this.model('points_log').where({
        points_change: ['>', 0],
        created_at: ['LIKE', `${today}%`]
      }).sum('points_change');

      return {
        totalIssued: totalIssued || 0,
        totalConsumed: Math.abs(totalConsumed || 0),
        totalCirculation: totalCirculation || 0,
        todayIssued: todayIssued || 0
      };
    } catch (error) {
      console.error('获取系统积分统计失败:', error);
      return null;
    }
  }

  /**
   * 获取当前月份 YYYY-MM
   */
  getCurrentMonth() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  }

  /**
   * 获取当前日期 YYYY-MM-DD
   */
  getCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
};
