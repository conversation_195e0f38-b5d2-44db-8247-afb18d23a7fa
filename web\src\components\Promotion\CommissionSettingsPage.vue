<template>
  <div class="commission-settings-page">
    <div class="page-header bg-white shadow-sm border-b">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">分销佣金</h1>
            <p class="text-sm text-gray-500 mt-1">设置分销佣金比例和规则</p>
          </div>
          <div class="flex items-center space-x-3">
            <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <i class="ri-save-line mr-2"></i>
              保存设置
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="p-6">
      <div class="max-w-4xl mx-auto">
        <div class="bg-gradient-to-br from-green-50 to-teal-100 rounded-xl p-8 text-center">
          <div class="w-24 h-24 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center">
            <i class="ri-money-dollar-circle-line text-4xl text-green-600"></i>
          </div>
          <h2 class="text-2xl font-bold text-gray-900 mb-4">分销佣金设置</h2>
          <p class="text-lg text-gray-600 mb-6">当前功能正在开发中，敬请期待</p>
          <div class="mt-6 text-center">
            <p class="text-sm text-gray-500">预计完成时间：<span class="font-medium text-gray-700">2024年1月</span></p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CommissionSettingsPage',
  mounted() {
    console.log('分销佣金页面已加载');
  }
}
</script>

<style scoped>
.commission-settings-page {
  min-height: 100vh;
  background-color: #f9fafb;
}
.page-header {
  position: sticky;
  top: 0;
  z-index: 10;
}
</style>
