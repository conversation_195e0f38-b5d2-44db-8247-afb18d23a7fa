const Base = require('./base.js');
const fs = require('fs');
const _ = require('lodash');
const moment = require('moment');

module.exports = class extends Base {

    /**
     * 获取用户积分信息
     * GET /api/user/points
     */
    async pointsAction() {
        try {
            const userId = await this.getLoginUserId();

            console.log('=== 获取用户积分信息（已移除积分系统，返回默认值）===');
            console.log('用户ID:', userId);

            // 积分系统已移除，直接返回默认值以保持API兼容性
            const pointsInfo = {
                points: 0,
                totalPoints: 0,
                availablePoints: 0,
                usedPoints: 0
            };

            console.log('返回默认积分信息:', pointsInfo);
            return this.success(pointsInfo);

        } catch (error) {
            console.error('获取用户积分信息失败:', error);
            return this.fail('获取积分信息失败: ' + error.message);
        }
    }
};