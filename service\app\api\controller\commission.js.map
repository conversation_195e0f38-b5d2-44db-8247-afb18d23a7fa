{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\commission.js"], "names": ["Base", "require", "module", "exports", "infoAction", "userId", "getLoginUserId", "console", "log", "commissionService", "service", "commissionInfo", "calculateUserCommission", "commissionDetails", "getCommissionDetails", "success", "message", "error", "fail", "withdrawAction", "withdrawAmount", "parseFloat", "post", "withdrawMethod", "availableCommission", "withdrawResult", "processWithdraw", "withdrawId", "amount", "method", "currentTime", "parseInt", "Date", "getTime", "Error", "markCommissionsAsWithdrawn", "model", "where", "user_id", "update", "withdrawn_commission", "updated_at", "add", "status", "apply_time", "create_time", "tenDaysAgo", "availableCommissions", "commission_type", "commission_status", "settle_time", "commission_change", "order", "select", "remainingAmount", "commission", "commissionAmount", "id", "withdrawsAction", "page", "get", "pageSize", "withdraws", "countSelect"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;;AAEAC,OAAOC,OAAP,GAAiB,cAAcH,IAAd,CAAmB;;AAEhC;;;;AAIMI,cAAN,GAAmB;AAAA;;AAAA;AACf,gBAAI;AACA,sBAAMC,SAAS,MAAM,MAAKC,cAAL,EAArB;;AAEAC,wBAAQC,GAAR,CAAY,kBAAZ;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBH,MAArB;;AAEA;AACA,sBAAMI,oBAAoB,MAAKC,OAAL,CAAa,YAAb,CAA1B;AACA,sBAAMC,iBAAiB,MAAMF,kBAAkBG,uBAAlB,CAA0CP,MAA1C,CAA7B;;AAEA;AACA,sBAAMQ,oBAAoB,MAAMJ,kBAAkBK,oBAAlB,CAAuCT,MAAvC,EAA+C,EAA/C,CAAhC;;AAEA,uBAAO,MAAKU,OAAL,CAAa;AAChBJ,oCAAgBA,cADA;AAEhBE,uCAAmBA,iBAFH;AAGhBG,6BAAS;AAHO,iBAAb,CAAP;AAMH,aAnBD,CAmBE,OAAOC,KAAP,EAAc;AACZV,wBAAQU,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO,MAAKC,IAAL,CAAU,eAAeD,MAAMD,OAA/B,CAAP;AACH;AAvBc;AAwBlB;;AAED;;;;AAIMG,kBAAN,GAAuB;AAAA;;AAAA;AACnB,gBAAI;AACA,sBAAMd,SAAS,MAAM,OAAKC,cAAL,EAArB;AACA,sBAAMc,iBAAiBC,WAAW,OAAKC,IAAL,CAAU,QAAV,KAAuB,CAAlC,CAAvB;AACA,sBAAMC,iBAAiB,OAAKD,IAAL,CAAU,QAAV,KAAuB,QAA9C,CAHA,CAGwD;;AAExDf,wBAAQC,GAAR,CAAY,gBAAZ;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBH,MAArB,EAA6B,OAA7B,EAAsCe,cAAtC,EAAsD,OAAtD,EAA+DG,cAA/D;;AAEA,oBAAIH,kBAAkB,CAAtB,EAAyB;AACrB,2BAAO,OAAKF,IAAL,CAAU,WAAV,CAAP;AACH;;AAED;AACA,sBAAMT,oBAAoB,OAAKC,OAAL,CAAa,YAAb,CAA1B;AACA,sBAAMC,iBAAiB,MAAMF,kBAAkBG,uBAAlB,CAA0CP,MAA1C,CAA7B;;AAEA,oBAAIe,iBAAiBT,eAAea,mBAApC,EAAyD;AACrD,2BAAO,OAAKN,IAAL,CAAW,qBAAoBP,eAAea,mBAAoB,GAAlE,CAAP;AACH;;AAED;AACA,sBAAMC,iBAAiB,MAAM,OAAKC,eAAL,CAAqBrB,MAArB,EAA6Be,cAA7B,EAA6CG,cAA7C,CAA7B;;AAEA,oBAAIE,eAAeV,OAAnB,EAA4B;AACxB,2BAAO,OAAKA,OAAL,CAAa;AAChBC,iCAAS,QADO;AAEhBW,oCAAYF,eAAeE,UAFX;AAGhBC,gCAAQR;AAHQ,qBAAb,CAAP;AAKH,iBAND,MAMO;AACH,2BAAO,OAAKF,IAAL,CAAUO,eAAeT,OAAzB,CAAP;AACH;AAEJ,aAjCD,CAiCE,OAAOC,KAAP,EAAc;AACZV,wBAAQU,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,OAAKC,IAAL,CAAU,aAAaD,MAAMD,OAA7B,CAAP;AACH;AArCkB;AAsCtB;;AAED;;;AAGMU,mBAAN,CAAsBrB,MAAtB,EAA8BuB,MAA9B,EAAsCC,MAAtC,EAA8C;AAAA;;AAAA;AAC1C,gBAAI;AACA,sBAAMC,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;;AAEA;AACA,sBAAMxB,oBAAoB,OAAKC,OAAL,CAAa,YAAb,CAA1B;AACA,sBAAMC,iBAAiB,MAAMF,kBAAkBG,uBAAlB,CAA0CP,MAA1C,CAA7B;;AAEA,oBAAIuB,SAASjB,eAAea,mBAA5B,EAAiD;AAC7C,0BAAM,IAAIU,KAAJ,CAAW,oBAAmBvB,eAAea,mBAAoB,GAAjE,CAAN;AACH;;AAED;AACA,sBAAM,OAAKW,0BAAL,CAAgC9B,MAAhC,EAAwCuB,MAAxC,CAAN;;AAEA;AACA,sBAAM,OAAKQ,KAAL,CAAW,iBAAX,EAA8BC,KAA9B,CAAoC;AACtCC,6BAASjC;AAD6B,iBAApC,EAEHkC,MAFG,CAEI;AACNC,0CAAsB,CAAC,KAAD,EAAS,0BAAyBZ,MAAO,EAAzC,CADhB;AAENa,gCAAY,IAAIT,IAAJ;AAFN,iBAFJ,CAAN;;AAOA;AACA;;AAEA;AACA,sBAAML,aAAa,MAAM,OAAKS,KAAL,CAAW,cAAX,EAA2BM,GAA3B,CAA+B;AACpDJ,6BAASjC,MAD2C;AAEpDuB,4BAAQA,MAF4C;AAGpDC,4BAAQA,MAH4C;AAIpDc,4BAAQ,SAJ4C;AAKpDC,gCAAYd,WALwC;AAMpDe,iCAAaf;AANuC,iBAA/B,CAAzB;;AASAvB,wBAAQC,GAAR,CAAY,gBAAZ,EAA8BmB,UAA9B;AACA,uBAAO,EAAEZ,SAAS,IAAX,EAAiBY,YAAYA,UAA7B,EAAP;AAEH,aAtCD,CAsCE,OAAOV,KAAP,EAAc;AACZV,wBAAQU,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,uBAAO,EAAEF,SAAS,KAAX,EAAkBC,SAASC,MAAMD,OAAjC,EAAP;AACH;AA1CyC;AA2C7C;;AAED;;;AAGMmB,8BAAN,CAAiC9B,MAAjC,EAAyCe,cAAzC,EAAyD;AAAA;;AAAA;AACrD,kBAAMU,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACA,kBAAMa,aAAahB,cAAe,KAAK,EAAL,GAAU,IAA5C;;AAEA;AACA,kBAAMiB,uBAAuB,MAAM,OAAKX,KAAL,CAAW,gBAAX,EAA6BC,KAA7B,CAAmC;AAClEC,yBAASjC,MADyD;AAElE2C,iCAAiB,WAFiD;AAGlEC,mCAAmB,SAH+C;AAIlEC,6BAAa,CAAC,IAAD,EAAOJ,UAAP,CAJqD;AAKlEK,mCAAmB,CAAC,GAAD,EAAM,CAAN,CAL+C,CAKtC;AALsC,aAAnC,EAMhCC,KANgC,CAM1B,iBAN0B,EAMPC,MANO,EAAnC;;AAQA,gBAAIC,kBAAkBlC,cAAtB;;AAEA;AACA,iBAAK,MAAMmC,UAAX,IAAyBR,oBAAzB,EAA+C;AAC3C,oBAAIO,mBAAmB,CAAvB,EAA0B;;AAE1B,sBAAME,mBAAmBnC,WAAWkC,WAAWJ,iBAAtB,CAAzB;;AAEA,oBAAIK,oBAAoBF,eAAxB,EAAyC;AACrC;AACA,0BAAM,OAAKlB,KAAL,CAAW,gBAAX,EAA6BC,KAA7B,CAAmC;AACrCoB,4BAAIF,WAAWE;AADsB,qBAAnC,EAEHlB,MAFG,CAEI;AACNU,2CAAmB;AADb,qBAFJ,CAAN;;AAMAK,uCAAmBE,gBAAnB;AACH,iBATD,MASO;AACH;AACA,0BAAM,OAAKpB,KAAL,CAAW,gBAAX,EAA6BC,KAA7B,CAAmC;AACrCoB,4BAAIF,WAAWE;AADsB,qBAAnC,EAEHlB,MAFG,CAEI;AACNU,2CAAmB;AADb,qBAFJ,CAAN;;AAMAK,sCAAkB,CAAlB;AACH;AACJ;AAxCoD;AAyCxD;;AAED;;;;AAIMI,mBAAN,GAAwB;AAAA;;AAAA;AACpB,gBAAI;AACA,sBAAMrD,SAAS,MAAM,OAAKC,cAAL,EAArB;AACA,sBAAMqD,OAAO5B,SAAS,OAAK6B,GAAL,CAAS,MAAT,KAAoB,CAA7B,CAAb;AACA,sBAAMC,WAAW9B,SAAS,OAAK6B,GAAL,CAAS,UAAT,KAAwB,EAAjC,CAAjB;;AAEArD,wBAAQC,GAAR,CAAY,kBAAZ;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBH,MAArB,EAA6B,KAA7B,EAAoCsD,IAApC,EAA0C,KAA1C,EAAiDE,QAAjD;;AAEA;AACA,sBAAMC,YAAY,MAAM,OAAK1B,KAAL,CAAW,cAAX,EAA2BC,KAA3B,CAAiC;AACrDC,6BAASjC;AAD4C,iBAAjC,EAErB+C,KAFqB,CAEf,kBAFe,EAEKO,IAFL,CAEUA,IAFV,EAEgBE,QAFhB,EAE0BE,WAF1B,EAAxB;;AAIA,uBAAO,OAAKhD,OAAL,CAAa;AAChB+C,+BAAWA,SADK;AAEhB9C,6BAAS;AAFO,iBAAb,CAAP;AAKH,aAlBD,CAkBE,OAAOC,KAAP,EAAc;AACZV,wBAAQU,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,OAAKC,IAAL,CAAU,eAAeD,MAAMD,OAA/B,CAAP;AACH;AAtBmB;AAuBvB;AArM+B,CAApC", "file": "..\\..\\..\\src\\api\\controller\\commission.js", "sourcesContent": ["const Base = require('./base.js');\n\nmodule.exports = class extends Base {\n    \n    /**\n     * 获取用户佣金信息\n     * GET /api/commission/info\n     */\n    async infoAction() {\n        try {\n            const userId = await this.getLoginUserId();\n            \n            console.log('=== 获取用户佣金信息 ===');\n            console.log('用户ID:', userId);\n            \n            // 使用统一的佣金计算服务\n            const commissionService = this.service('commission');\n            const commissionInfo = await commissionService.calculateUserCommission(userId);\n            \n            // 使用佣金服务获取佣金明细\n            const commissionDetails = await commissionService.getCommissionDetails(userId, 20);\n            \n            return this.success({\n                commissionInfo: commissionInfo,\n                commissionDetails: commissionDetails,\n                message: '获取佣金信息成功'\n            });\n            \n        } catch (error) {\n            console.error('获取用户佣金信息失败:', error);\n            return this.fail('获取佣金信息失败: ' + error.message);\n        }\n    }\n    \n    /**\n     * 申请提现\n     * POST /api/commission/withdraw\n     */\n    async withdrawAction() {\n        try {\n            const userId = await this.getLoginUserId();\n            const withdrawAmount = parseFloat(this.post('amount') || 0);\n            const withdrawMethod = this.post('method') || 'wechat'; // 提现方式\n            \n            console.log('=== 用户申请提现 ===');\n            console.log('用户ID:', userId, '提现金额:', withdrawAmount, '提现方式:', withdrawMethod);\n            \n            if (withdrawAmount <= 0) {\n                return this.fail('提现金额必须大于0');\n            }\n            \n            // 使用统一的佣金计算服务检查可提现金额\n            const commissionService = this.service('commission');\n            const commissionInfo = await commissionService.calculateUserCommission(userId);\n            \n            if (withdrawAmount > commissionInfo.availableCommission) {\n                return this.fail(`提现金额超过可提现余额，当前可提现：${commissionInfo.availableCommission}元`);\n            }\n            \n            // 执行提现\n            const withdrawResult = await this.processWithdraw(userId, withdrawAmount, withdrawMethod);\n            \n            if (withdrawResult.success) {\n                return this.success({\n                    message: '提现申请成功',\n                    withdrawId: withdrawResult.withdrawId,\n                    amount: withdrawAmount\n                });\n            } else {\n                return this.fail(withdrawResult.message);\n            }\n            \n        } catch (error) {\n            console.error('用户提现申请失败:', error);\n            return this.fail('提现申请失败: ' + error.message);\n        }\n    }\n    \n    /**\n     * 处理提现\n     */\n    async processWithdraw(userId, amount, method) {\n        try {\n            const currentTime = parseInt(new Date().getTime() / 1000);\n\n            // 1. 再次检查可提现金额（防止并发问题）\n            const commissionService = this.service('commission');\n            const commissionInfo = await commissionService.calculateUserCommission(userId);\n\n            if (amount > commissionInfo.availableCommission) {\n                throw new Error(`提现金额超过可用余额，当前可提现：${commissionInfo.availableCommission}元`);\n            }\n\n            // 2. 标记相关佣金为已提现状态\n            await this.markCommissionsAsWithdrawn(userId, amount);\n\n            // 3. 更新用户佣金账户（只更新withdrawn_commission，其他字段通过实时计算）\n            await this.model('user_commission').where({\n                user_id: userId\n            }).update({\n                withdrawn_commission: ['exp', `withdrawn_commission + ${amount}`],\n                updated_at: new Date()\n            });\n\n            // 注意：available_commission 通过 calculateUserCommission 方法实时计算\n            // 因为已经标记了相关佣金为已提现状态，实时计算会自动减少可用余额\n\n            // 4. 创建提现记录\n            const withdrawId = await this.model('withdraw_log').add({\n                user_id: userId,\n                amount: amount,\n                method: method,\n                status: 'pending',\n                apply_time: currentTime,\n                create_time: currentTime\n            });\n\n            console.log('✅ 提现处理成功，提现ID:', withdrawId);\n            return { success: true, withdrawId: withdrawId };\n\n        } catch (error) {\n            console.error('处理提现失败:', error);\n            return { success: false, message: error.message };\n        }\n    }\n    \n    /**\n     * 标记佣金为已提现状态\n     */\n    async markCommissionsAsWithdrawn(userId, withdrawAmount) {\n        const currentTime = parseInt(new Date().getTime() / 1000);\n        const tenDaysAgo = currentTime - (10 * 24 * 3600);\n        \n        // 查询可提现的佣金记录（10天前发放的，按时间顺序）\n        const availableCommissions = await this.model('commission_log').where({\n            user_id: userId,\n            commission_type: 'promotion',\n            commission_status: 'settled',\n            settle_time: ['<=', tenDaysAgo],\n            commission_change: ['>', 0] // 只查询正数（获得的佣金）\n        }).order('settle_time ASC').select();\n        \n        let remainingAmount = withdrawAmount;\n        \n        // 按时间顺序标记佣金为已提现\n        for (const commission of availableCommissions) {\n            if (remainingAmount <= 0) break;\n            \n            const commissionAmount = parseFloat(commission.commission_change);\n            \n            if (commissionAmount <= remainingAmount) {\n                // 整条记录标记为已提现\n                await this.model('commission_log').where({\n                    id: commission.id\n                }).update({\n                    commission_status: 'withdrawn'\n                });\n                \n                remainingAmount -= commissionAmount;\n            } else {\n                // 部分提现（这种情况比较复杂，暂时简化处理）\n                await this.model('commission_log').where({\n                    id: commission.id\n                }).update({\n                    commission_status: 'withdrawn'\n                });\n                \n                remainingAmount = 0;\n            }\n        }\n    }\n    \n    /**\n     * 获取提现记录\n     * GET /api/commission/withdraws\n     */\n    async withdrawsAction() {\n        try {\n            const userId = await this.getLoginUserId();\n            const page = parseInt(this.get('page') || 1);\n            const pageSize = parseInt(this.get('pageSize') || 20);\n            \n            console.log('=== 获取用户提现记录 ===');\n            console.log('用户ID:', userId, '页码:', page, '每页:', pageSize);\n            \n            // 查询提现记录\n            const withdraws = await this.model('withdraw_log').where({\n                user_id: userId\n            }).order('create_time DESC').page(page, pageSize).countSelect();\n            \n            return this.success({\n                withdraws: withdraws,\n                message: '获取提现记录成功'\n            });\n            \n        } catch (error) {\n            console.error('获取提现记录失败:', error);\n            return this.fail('获取提现记录失败: ' + error.message);\n        }\n    }\n};\n"]}