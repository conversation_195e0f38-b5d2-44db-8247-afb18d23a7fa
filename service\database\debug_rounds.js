const mysql = require('mysql2/promise');

async function debugRounds() {
  const connection = await mysql.createConnection({
    host: '127.0.0.1',
    port: 3306,
    user: 'root',
    password: '19841020',
    database: 'hiolabsDB'
  });

  try {
    console.log('✅ 数据库连接成功');

    // 查看轮次表数据
    console.log('\n=== 轮次表数据 ===');
    const [rounds] = await connection.execute('SELECT * FROM hiolabs_flash_sale_rounds ORDER BY round_number DESC');
    console.log('轮次数量:', rounds.length);
    rounds.forEach(round => {
      console.log(`轮次 ${round.round_number}: ${round.round_name} (${round.status}) - ${round.start_time} 到 ${round.end_time}`);
    });

    // 查看轮次商品表数据
    console.log('\n=== 轮次商品表数据 ===');
    const [roundGoods] = await connection.execute('SELECT * FROM hiolabs_flash_sale_round_goods ORDER BY round_id, sort_order');
    console.log('轮次商品数量:', roundGoods.length);
    roundGoods.forEach(goods => {
      console.log(`轮次 ${goods.round_id}: 商品 ${goods.goods_id} - ${goods.goods_name} (¥${goods.flash_price})`);
    });

    // 测试JOIN查询
    console.log('\n=== 测试JOIN查询 ===');
    const [joinResult] = await connection.execute(`
      SELECT r.*, COUNT(rg.id) as goods_count
      FROM hiolabs_flash_sale_rounds r
      LEFT JOIN hiolabs_flash_sale_round_goods rg ON r.id = rg.round_id
      GROUP BY r.id
      ORDER BY r.round_number DESC
    `);
    console.log('JOIN查询结果数量:', joinResult.length);
    joinResult.forEach(round => {
      console.log(`轮次 ${round.round_number}: ${round.round_name} - 商品数量: ${round.goods_count}`);
    });

  } catch (error) {
    console.error('❌ 调试失败:', error);
  } finally {
    await connection.end();
    console.log('✅ 数据库连接已关闭');
  }
}

debugRounds();
