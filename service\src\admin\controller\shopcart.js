const Base = require('./base.js');
const moment = require('moment');
module.exports = class extends Base {
    /**
     * index action
     * @return {Promise} []
     */
    async indexAction() {
        const page = this.get('page') || 1;
        const size = this.get('size') || 10;
        const name = this.get('name') || '';

        // 使用JOIN查询，只获取用户存在的购物车记录
        const model = this.model('cart');
        const data = await model.alias('c')
            .join({
                table: 'user',
                join: 'inner',  // 使用inner join过滤掉用户不存在的记录
                as: 'u',
                on: ['c.user_id', 'u.id']
            })
            .where({
                'c.goods_name': ['like', `%${name}%`]
            })
            .field('c.*, u.nickname as user_nickname')
            .order(['c.id DESC'])
            .page(page, size)
            .countSelect();

        for (const item of data.data) {
            item.add_time = moment.unix(item.add_time).format('YYYY-MM-DD HH:mm:ss');
            // 直接使用JOIN查询得到的nickname，无需再次查询用户表
            item.nickname = Buffer.from(item.user_nickname, 'base64').toString();
        }

        return this.success(data);
    }

    // 删除购物车项
    async deleteAction() {
        const id = this.get('id');
        if (!id) {
            return this.fail('缺少购物车ID参数');
        }

        try {
            // 软删除，设置is_delete为1
            await this.model('cart').where({ id: id }).update({ is_delete: 1 });
            return this.success('删除成功');
        } catch (error) {
            console.log('删除购物车项失败:', error.message);
            return this.fail('删除失败');
        }
    }

};
