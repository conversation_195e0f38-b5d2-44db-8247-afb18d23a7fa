{"version": 3, "sources": ["..\\..\\..\\src\\admin\\model\\order_express.js"], "names": ["moment", "require", "_", "rp", "fs", "http", "path", "module", "exports", "think", "Model", "tableName", "tablePrefix", "getLatestOrderExpress", "orderId", "returnExpressInfo", "shipper_code", "shipper_name", "logistic_code", "is_finish", "request_time", "traces", "orderExpress", "where", "order_id", "find", "isEmpty", "datetime", "JSON", "parse", "ExpressSerivce", "service", "latestExpressInfo", "queryExpress", "nowTime", "parseInt", "Date", "getTime", "updateData", "update_time", "request_count", "success", "is<PERSON><PERSON><PERSON>", "stringify", "id", "update", "getLatestOrderExpressByAli", "currentTime", "console", "log", "info", "model", "fail", "expressInfo", "updateTime", "com", "shipperCode", "expressNo", "code", "substring", "shipperName", "sfLastNo", "config", "lastExpressInfo", "getExpressInfo", "deliverystatus", "newUpdateTime", "getDeliverystatus", "issign", "list", "dataInfo", "express_status", "express", "appCode", "options", "method", "url", "headers", "sessionData", "result", "status", "getMianExpress", "senderInfo", "receiverInfo", "expressType", "ShipperCode", "ExpType", "CustomerName", "MonthCode", "GoodsName", "testSwitch", "name", "MemberID", "CustomerPwd", "SendSite", "SendStaff", "CustomArea", "WareHouseID", "TransType", "LogisticCode", "ThrOrderCode", "OrderCode", "PayType", "IsReturnSignBill", "OperateRequire", "Cost", "OtherCost", "Receiver", "Company", "Name", "Tel", "Mobile", "PostCode", "ProvinceName", "CityName", "ExpAreaName", "Address", "Sender", "IsNotice", "Quantity", "Remark", "Commodity", "GoodsCode", "Goodsquantity", "GoodsPrice", "GoodsWeight", "GoodsDesc", "GoodsVol", "IsReturnPrintTemplate", "IsSendMessage", "TemplateSize", "PackingType", "DeliveryMethod", "error", "remark", "expressValue", "express_value", "AddService", "order_sn", "mianExpress", "send_time", "unix", "format", "printExpress", "buildForm"], "mappings": ";;AAAA,MAAMA,SAASC,QAAQ,QAAR,CAAf;AACA,MAAMC,IAAID,QAAQ,QAAR,CAAV;AACA,MAAME,KAAKF,QAAQ,iBAAR,CAAX;AACA,MAAMG,KAAKH,QAAQ,IAAR,CAAX;AACA,MAAMI,OAAOJ,QAAQ,MAAR,CAAb;AACA,MAAMK,OAAOL,QAAQ,MAAR,CAAb;;AAEAM,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;AACvC,QAAIC,SAAJ,GAAgB;AACZ,eAAO,KAAKC,WAAL,GAAmB,eAA1B;AACH;AACD;;;;;AAKMC,yBAAN,CAA4BC,OAA5B,EAAqC;AAAA;;AAAA;AAAE;AACnC,kBAAMC,oBAAoB;AACtBC,8BAAc,EADQ;AAEtBC,8BAAc,EAFQ;AAGtBC,+BAAe,EAHO;AAItBC,2BAAW,CAJW;AAKtBC,8BAAc,CALQ;AAMtBC,wBAAQ;AANc,aAA1B;AAQA,kBAAMC,eAAe,MAAM,MAAKC,KAAL,CAAW;AAClCC,0BAAUV;AADwB,aAAX,EAExBW,IAFwB,EAA3B;AAGA,gBAAIhB,MAAMiB,OAAN,CAAcJ,YAAd,CAAJ,EAAiC;AAC7B,uBAAOP,iBAAP;AACH;AACD,gBAAIN,MAAMiB,OAAN,CAAcJ,aAAaN,YAA3B,KAA4CP,MAAMiB,OAAN,CAAcJ,aAAaJ,aAA3B,CAAhD,EAA2F;AACvF,uBAAOH,iBAAP;AACH;AACDA,8BAAkBC,YAAlB,GAAiCM,aAAaN,YAA9C;AACAD,8BAAkBE,YAAlB,GAAiCK,aAAaL,YAA9C;AACAF,8BAAkBG,aAAlB,GAAkCI,aAAaJ,aAA/C;AACAH,8BAAkBI,SAAlB,GAA8BG,aAAaH,SAA3C;AACAJ,8BAAkBK,YAAlB,GAAiCX,MAAMkB,QAAN,CAAeL,aAAaF,YAAb,GAA4B,IAA3C,CAAjC;AACAL,8BAAkBM,MAAlB,GAA2BZ,MAAMiB,OAAN,CAAcJ,aAAaD,MAA3B,IAAqC,EAArC,GAA0CO,KAAKC,KAAL,CAAWP,aAAaD,MAAxB,CAArE;AACA;AACA,gBAAIC,aAAaH,SAAjB,EAA4B;AACxB,uBAAOJ,iBAAP;AACH;AACD;AACA,kBAAMe,iBAAiBrB,MAAMsB,OAAN,CAAc,SAAd,EAAyB,KAAzB,CAAvB;AACA,kBAAMC,oBAAoB,MAAMF,eAAeG,YAAf,CAA4BX,aAAaN,YAAzC,EAAuDM,aAAaJ,aAApE,CAAhC;AACA,kBAAMgB,UAAUC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAAhB;AACA,kBAAMC,aAAa;AACflB,8BAAcc,OADC;AAEfK,6BAAaL,OAFE;AAGfM,+BAAe,CAAC,KAAD,EAAQ,iBAAR;AAHA,aAAnB;AAKA,gBAAIR,kBAAkBS,OAAtB,EAA+B;AAC3B1B,kCAAkBM,MAAlB,GAA2BW,kBAAkBX,MAA7C;AACAN,kCAAkBI,SAAlB,GAA8Ba,kBAAkBU,QAAhD;AACA;AACAJ,2BAAWjB,MAAX,GAAoBO,KAAKe,SAAL,CAAeX,kBAAkBX,MAAjC,CAApB;AACAN,kCAAkBK,YAAlB,GAAiCX,MAAMkB,QAAN,CAAeO,UAAU,IAAzB,CAAjC;AACAI,2BAAWnB,SAAX,GAAuBa,kBAAkBU,QAAzC;AACH;AACD,kBAAM,MAAKnB,KAAL,CAAW;AACbqB,oBAAItB,aAAasB;AADJ,aAAX,EAEHC,MAFG,CAEIP,UAFJ,CAAN;AAGA,mBAAOvB,iBAAP;AAhDiC;AAiDpC;AACD;;;;;AAKM+B,8BAAN,CAAiChC,OAAjC,EAA0C;AAAA;;AAAA;AACtC;;AAEA,kBAAMiC,cAAcZ,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACAW,oBAAQC,GAAR,CAAY,2CAAZ;AACA,gBAAIC,OAAO,MAAM,OAAKC,KAAL,CAAW,eAAX,EAA4B5B,KAA5B,CAAkC;AAC/CC,0BAAUV;AADqC,aAAlC,EAEdW,IAFc,EAAjB;AAGA,gBAAIhB,MAAMiB,OAAN,CAAcwB,IAAd,CAAJ,EAAyB;AACrB,uBAAO,OAAKE,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACH;AACD,kBAAMC,cAAc,MAAM,OAAKF,KAAL,CAAW,eAAX,EAA4B5B,KAA5B,CAAkC;AACxDC,0BAAUV;AAD8C,aAAlC,EAEvBW,IAFuB,EAA1B;AAGA;AACA,gBAAI6B,aAAaJ,KAAKX,WAAtB;AACA,gBAAIgB,MAAM,CAACR,cAAcO,UAAf,IAA6B,EAAvC;AACA,gBAAInC,YAAY+B,KAAK/B,SAArB;AACA,gBAAIA,aAAa,CAAjB,EAAoB;AAChB,uBAAOkC,WAAP;AACH,aAFD,MAEO,IAAIC,cAAc,CAAd,IAAmBC,MAAM,EAA7B,EAAiC;AACpC,uBAAOF,WAAP;AACH,aAFM,MAEA;AACH,oBAAIG,cAAcH,YAAYrC,YAA9B;AACA,oBAAIyC,YAAYJ,YAAYnC,aAA5B;AACA,oBAAIwC,OAAOF,YAAYG,SAAZ,CAAsB,CAAtB,EAAyB,CAAzB,CAAX;AACA,oBAAIC,cAAc,EAAlB;AACT,oBAAIC,WAAWpD,MAAMqD,MAAN,CAAa,qBAAb,CAAf;AACS,oBAAIJ,QAAQ,IAAZ,EAAkB;AACdE,kCAAc,WAAd;AACAH,gCAAYA,YAAY,GAAZ,GAAgBI,QAA5B,CAFc,CAEwB;AACzC,iBAHD,MAGO;AACHD,kCAAcJ,WAAd;AACH;AACD,oBAAIO,kBAAkB,MAAM,OAAKC,cAAL,CAAoBJ,WAApB,EAAiCH,SAAjC,CAA5B;AACAT,wBAAQC,GAAR,CAAYc,eAAZ;AACA,oBAAIE,iBAAiBF,gBAAgBE,cAArC;AACA,oBAAIC,gBAAgBH,gBAAgBT,UAApC;AACAY,gCAAgB/B,SAAS,IAAIC,IAAJ,CAAS8B,aAAT,EAAwB7B,OAAxB,KAAoC,IAA7C,CAAhB;AACA4B,iCAAiB,MAAM,OAAKE,iBAAL,CAAuBF,cAAvB,CAAvB;AACAjB,wBAAQC,GAAR,CAAYgB,cAAZ;AACA,oBAAIG,SAASL,gBAAgBK,MAA7B;AACA,oBAAI/C,SAAS0C,gBAAgBM,IAA7B;AACAhD,yBAASO,KAAKe,SAAL,CAAetB,MAAf,CAAT;AACA2B,wBAAQC,GAAR,CAAY5B,MAAZ;AACA,oBAAIiD,WAAW;AACXC,oCAAgBN,cADL;AAEX9C,+BAAWiD,MAFA;AAGX/C,4BAAQA,MAHG;AAIXkB,iCAAa2B;AAJF,iBAAf;AAMAlB,wBAAQC,GAAR,CAAY,cAAZ;AACA,sBAAM,OAAKE,KAAL,CAAW,eAAX,EAA4B5B,KAA5B,CAAkC;AACpCC,8BAAUV;AAD0B,iBAAlC,EAEH+B,MAFG,CAEIyB,QAFJ,CAAN;AAGA,oBAAIE,UAAU,MAAM,OAAKrB,KAAL,CAAW,eAAX,EAA4B5B,KAA5B,CAAkC;AAClDC,8BAAUV;AADwC,iBAAlC,EAEjBW,IAFiB,EAApB;AAGA,uBAAO+C,OAAP;AACH;AACD;AA5DsC;AA6DzC;AACKR,kBAAN,CAAqBJ,WAArB,EAAkCH,SAAlC,EAA6C;AAAA;AACzCT,oBAAQC,GAAR,CAAY,WAAZ;AACN,gBAAIwB,UAAU,aAAYhE,MAAMqD,MAAN,CAAa,oBAAb,CAA1B;AACM,kBAAMY,UAAU;AACZC,wBAAQ,KADI;AAEZC,qBAAK,gDAAgDnB,SAAhD,GAA4D,QAA5D,GAAuEG,WAFhE;AAGZiB,yBAAS;AACL,oCAAgB,iCADX;AAEL,qCAAiBJ;AAFZ;AAHG,aAAhB;AAQA,gBAAIK,cAAc,MAAM3E,GAAGuE,OAAH,CAAxB;AACAI,0BAAclD,KAAKC,KAAL,CAAWiD,WAAX,CAAd;AACA,mBAAOA,YAAYC,MAAnB;AAbyC;AAc5C;AACKZ,qBAAN,CAAwBa,MAAxB,EAAgC;AAAA;AAC5B,gBAAIA,UAAU,CAAd,EAAiB;AACb,uBAAO,UAAP;AACH,aAFD,MAEO,IAAIA,UAAU,CAAd,EAAiB;AACpB,uBAAO,KAAP;AACH,aAFM,MAEA,IAAIA,UAAU,CAAd,EAAiB;AACpB,uBAAO,MAAP;AACH,aAFM,MAEA,IAAIA,UAAU,CAAd,EAAiB;AACpB,uBAAO,KAAP;AACH,aAFM,MAEA,IAAIA,UAAU,CAAd,EAAiB;AACpB,uBAAO,oCAAP;AACH,aAFM,MAEA,IAAIA,UAAU,CAAd,EAAiB;AACpB,uBAAO,wCAAP;AACH,aAFM,MAEA,IAAIA,UAAU,CAAd,EAAiB;AACpB,uBAAO,MAAP;AACH;AAf2B;AAgB/B;AACKC,kBAAN,CAAqBnE,OAArB,EAA8BoE,UAA9B,EAA0CC,YAA1C,EAAwDC,WAAxD,EAAqE;AAAA;;AAAA;AACjE;AACA,gBAAIC,cAAc,EAAlB;AACA,gBAAIC,UAAU,CAAd;AACA,gBAAIC,eAAe,EAAnB,CAJiE,CAI1C;AACvB,gBAAIC,YAAY,EAAhB,CALiE,CAK7C;AACpB,gBAAIC,YAAY,EAAhB;AACA;AACA,gBAAIC,aAAa,CAAjB,CARiE,CAQ7C;AACpB,gBAAIA,cAAc,CAAlB,EAAqB;AACjB,oBAAIN,cAAc,CAAlB,EAAqB;AACjBI,gCAAY,EAAZ;AACH,iBAFD,MAEO;AACHD,mCAAe,SAAf;AACAC,gCAAY,kBAAZ;AACH;AACJ,aAPD,MAOO;AACH,oBAAIJ,cAAc,CAAlB,EAAqB;AACjB,wBAAIA,eAAe,CAAf,IAAoBA,eAAe,CAAvC,EAA0C;AACtC,4BAAIlC,OAAO,MAAM,OAAKC,KAAL,CAAW,SAAX,EAAsB5B,KAAtB,CAA4B;AACzCoE,kCAAM;AADmC,yBAA5B,EAEdlE,IAFc,EAAjB;AAGA+D,oCAAYtC,KAAKsC,SAAjB;AACH,qBALD,MAKO,IAAIJ,eAAe,CAAnB,EAAsB;AACzB,4BAAIlC,OAAO,MAAM,OAAKC,KAAL,CAAW,SAAX,EAAsB5B,KAAtB,CAA4B;AACzCoE,kCAAM;AADmC,yBAA5B,EAEdlE,IAFc,EAAjB;AAGA+D,oCAAYtC,KAAKsC,SAAjB;AACH;AACJ,iBAZD,MAYO;AACH,wBAAItC,OAAO,MAAM,OAAKC,KAAL,CAAW,SAAX,EAAsB5B,KAAtB,CAA4B;AACzCmC,8BAAM;AADmC,qBAA5B,EAEdjC,IAFc,EAAjB;AAGA8D,mCAAerC,KAAKqC,YAApB;AACAC,gCAAYtC,KAAKsC,SAAjB;AACH;AACJ;AACD;AACA;AACA,gBAAIzE,oBAAoB,EAAxB;AACA,gBAAIqE,eAAe,CAAnB,EAAsB;AAAE;AACpBC,8BAAc,IAAd;AACAI,4BAAY,IAAZ;AACH,aAHD,MAGO,IAAIL,eAAe,CAAnB,EAAsB;AAAE;AAC3BC,8BAAc,IAAd;AACAI,4BAAY,KAAZ;AACH,aAHM,MAGA,IAAIL,eAAe,CAAnB,EAAsB;AAAE;AAC3BE,0BAAU,CAAV;AACAD,8BAAc,IAAd;AACAI,4BAAY,KAAZ;AACH,aAJM,MAIA,IAAIL,eAAe,CAAnB,EAAsB;AAAE;AAC3BC,8BAAc,KAAd;AACAI,4BAAY,KAAZ;AACH;AACD1E,gCAAoB;AAChB6E,0BAAU,EADM,EACF;AACd;AACAC,6BAAa,EAHG;AAIhBC,0BAAU,EAJM;AAKhBC,2BAAW,EALK;AAMhB;AACAR,8BAAcA,YAPE;AAQhBC,2BAAWA,SARK;AAShBQ,4BAAY,EATI,EASA;AAChBC,6BAAa,EAVG,EAUC;AACjBC,2BAAW,CAXK,EAWF;AACdb,6BAAaA,WAZG,EAYU;AAC1Bc,8BAAc,EAbE,EAaE;AAClBC,8BAAc,EAdE,EAcE;AAClBC,2BAAW,EAfK,EAeD;AACfC,yBAAS,CAhBO,EAgBJ;AACZhB,yBAASA,OAjBO,EAiBE;AAClBiB,kCAAkB,EAlBF,EAkBM;AACtBC,gCAAgB,EAnBA,EAmBI;AACpBC,sBAAM,CApBU,EAoBP;AACTC,2BAAW,CArBK,EAqBF;AACdC,0BAAU;AACNC,6BAAS,EADH,EACO;AACbC,0BAAM,EAFA,EAEI;AACVC,yBAAK,EAHC,EAGG;AACTC,4BAAQ,EAJF,EAIM;AACZC,8BAAU,EALJ,EAKQ;AACdC,kCAAc,EANR,EAMY;AAClBC,8BAAU,EAPJ,EAOQ;AACdC,iCAAa,EARP,EAQW;AACjBC,6BAAS,EATH,CASM;AATN,iBAtBM;AAiChBC,wBAAQ;AACJT,6BAAS,EADL,EACS;AACbC,0BAAM,EAFF,EAEM;AACVC,yBAAK,EAHD,EAGK;AACTC,4BAAQ,EAJJ,EAIQ;AACZC,8BAAU,EALN,EAKU;AACdC,kCAAc,EANV,EAMc;AAClBC,8BAAU,EAPN,EAOU;AACdC,iCAAa,EART,EAQa;AACjBC,6BAAS,EATL,CASQ;AATR,iBAjCQ;AA4ChBE,0BAAU,CA5CM,EA4CH;AACbC,0BAAU,CA7CM,EA6CH;AACbC,wBAAQ,EA9CQ,EA8CJ;AACZC,2BAAW,CAAC;AACRhC,+BAAWA,SADH,EACc;AACtBiC,+BAAW,EAFH,EAEO;AACfC,mCAAe,CAHP,EAGU;AAClBC,gCAAY,CAJJ,EAIO;AACfC,iCAAa,CALL,EAKQ;AAChBC,+BAAW,EANH,EAMO;AACfC,8BAAU,CAPF,CAOI;AAPJ,iBAAD,CA/CK;AAwDhBC,uCAAuB,CAxDP,EAwDU;AAC1BC,+BAAe,CAzDC,EAyDE;AAClBC,8BAAc,EA1DE,EA0DE;AAClBC,6BAAa,CA3DG,EA2DA;AAChBC,gCAAgB,CA5DA,CA4DE;AA5DF,aAApB;AA8DA,kBAAM9G,eAAe,MAAM,OAAK6B,KAAL,CAAW,OAAX,EAAoB5B,KAApB,CAA0B;AACjDqB,oBAAI9B;AAD6C,aAA1B,EAExBW,IAFwB,EAA3B;AAGA,gBAAIhB,MAAMiB,OAAN,CAAcJ,YAAd,CAAJ,EAAiC;AAC7B,oBAAI+G,QAAQ,GAAZ;AACA,uBAAOA,KAAP;AACH;AACDtH,8BAAkByG,MAAlB,GAA2BlG,aAAagH,MAAxC;AACA,gBAAIC,eAAe,GAAnB;AACA,gBAAInD,eAAe,CAAnB,EAAsB;AAAE;AACpBmD,+BAAejH,aAAakH,aAA5B;AACAzH,kCAAkB0H,UAAlB,GAA+B,CAAC;AAC5B,4BAAQ,QADoB,EACV;AAClB,6BAASF,YAFmB;AAG5B,kCAAc;AAHc,iBAAD,CAA/B;AAKH;AACD;AACAxH,8BAAkBsF,SAAlB,GAA8B/E,aAAaoH,QAA3C;AACA;AACA3H,8BAAkB4F,QAAlB,GAA6BxB,YAA7B;AACApE,8BAAkBsG,MAAlB,GAA2BnC,UAA3B;AACA,kBAAMpD,iBAAiBrB,MAAMsB,OAAN,CAAc,SAAd,EAAyB,KAAzB,CAAvB;AACA,kBAAMC,oBAAoB,MAAMF,eAAe6G,WAAf,CAA2B5H,iBAA3B,CAAhC;AACA;AACA,kBAAMgC,cAAcZ,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACAL,8BAAkB4G,SAAlB,GAA8B5I,OAAO6I,IAAP,CAAY9F,WAAZ,EAAyB+F,MAAzB,CAAgC,YAAhC,CAA9B;AACA9G,8BAAkBuG,YAAlB,GAAiCA,YAAjC;AACAvG,8BAAkBsG,MAAlB,GAA2BhH,aAAagH,MAAxC;AACAtG,8BAAkBoD,WAAlB,GAAgCA,WAAhC;AACApD,8BAAkBwD,SAAlB,GAA8BA,SAA9B;AACA,mBAAOxD,iBAAP;AAnJiE;AAoJpE;AACK+G,gBAAN,GAAqB;AAAA;AACjB,kBAAMjH,iBAAiBrB,MAAMsB,OAAN,CAAc,SAAd,EAAyB,KAAzB,CAAvB;AACA,kBAAMC,oBAAoB,MAAMF,eAAekH,SAAf,EAAhC;AACA,mBAAOhH,iBAAP;AAHiB;AAIpB;AAvTsC,CAA3C", "file": "..\\..\\..\\src\\admin\\model\\order_express.js", "sourcesContent": ["const moment = require('moment');\nconst _ = require('lodash');\nconst rp = require('request-promise');\nconst fs = require('fs');\nconst http = require(\"http\");\nconst path = require('path');\n\nmodule.exports = class extends think.Model {\n    get tableName() {\n        return this.tablePrefix + 'order_express';\n    }\n    /**\n     * 获取最新的订单物流信息  快递鸟，不能查顺丰\n     * @param orderId\n     * @returns {Promise.<*>}\n     */\n    async getLatestOrderExpress(orderId) { // 这个是快递鸟的接口，查不了顺丰。顺丰用阿里云的付费接口，在order.js中已经写好\n        const returnExpressInfo = {\n            shipper_code: '',\n            shipper_name: '',\n            logistic_code: '',\n            is_finish: 0,\n            request_time: 0,\n            traces: []\n        };\n        const orderExpress = await this.where({\n            order_id: orderId\n        }).find();\n        if (think.isEmpty(orderExpress)) {\n            return returnExpressInfo;\n        }\n        if (think.isEmpty(orderExpress.shipper_code) || think.isEmpty(orderExpress.logistic_code)) {\n            return returnExpressInfo;\n        }\n        returnExpressInfo.shipper_code = orderExpress.shipper_code;\n        returnExpressInfo.shipper_name = orderExpress.shipper_name;\n        returnExpressInfo.logistic_code = orderExpress.logistic_code;\n        returnExpressInfo.is_finish = orderExpress.is_finish;\n        returnExpressInfo.request_time = think.datetime(orderExpress.request_time * 1000);\n        returnExpressInfo.traces = think.isEmpty(orderExpress.traces) ? [] : JSON.parse(orderExpress.traces);\n        // 如果物流配送已完成，直接返回\n        if (orderExpress.is_finish) {\n            return returnExpressInfo;\n        }\n        // 查询最新物流信息\n        const ExpressSerivce = think.service('express', 'api');\n        const latestExpressInfo = await ExpressSerivce.queryExpress(orderExpress.shipper_code, orderExpress.logistic_code);\n        const nowTime = parseInt(new Date().getTime() / 1000);\n        const updateData = {\n            request_time: nowTime,\n            update_time: nowTime,\n            request_count: ['EXP', 'request_count+1']\n        };\n        if (latestExpressInfo.success) {\n            returnExpressInfo.traces = latestExpressInfo.traces;\n            returnExpressInfo.is_finish = latestExpressInfo.isFinish;\n            // 查询成功则更新订单物流信息\n            updateData.traces = JSON.stringify(latestExpressInfo.traces);\n            returnExpressInfo.request_time = think.datetime(nowTime * 1000);\n            updateData.is_finish = latestExpressInfo.isFinish;\n        }\n        await this.where({\n            id: orderExpress.id\n        }).update(updateData);\n        return returnExpressInfo;\n    }\n    /**\n     * 获取最新的订单物流信息  阿里云快递api 收费\n     * @param orderId\n     * @returns {Promise.<*>}\n     */\n    async getLatestOrderExpressByAli(orderId) {\n        // let aliexpress = think.config('aliexpress');\n\t\t\n        const currentTime = parseInt(new Date().getTime() / 1000);\n        console.log('==============orderExpress===============');\n        let info = await this.model('order_express').where({\n            order_id: orderId\n        }).find();\n        if (think.isEmpty(info)) {\n            return this.fail(400, '暂无物流信息');\n        }\n        const expressInfo = await this.model('order_express').where({\n            order_id: orderId\n        }).find();\n        // 如果is_finish == 1；或者 updateTime 小于 10分钟，\n        let updateTime = info.update_time;\n        let com = (currentTime - updateTime) / 60;\n        let is_finish = info.is_finish;\n        if (is_finish == 1) {\n            return expressInfo;\n        } else if (updateTime != 0 && com < 20) {\n            return expressInfo;\n        } else {\n            let shipperCode = expressInfo.shipper_code;\n            let expressNo = expressInfo.logistic_code;\n            let code = shipperCode.substring(0, 2);\n            let shipperName = '';\n\t\t\tlet sfLastNo = think.config('aliexpress.sfLastNo');\n            if (code == \"SF\") {\n                shipperName = \"SFEXPRESS\";\n                expressNo = expressNo + ':'+sfLastNo; // 这个要根据自己的寄件时的手机末四位\n            } else {\n                shipperName = shipperCode;\n            }\n            let lastExpressInfo = await this.getExpressInfo(shipperName, expressNo);\n            console.log(lastExpressInfo);\n            let deliverystatus = lastExpressInfo.deliverystatus;\n            let newUpdateTime = lastExpressInfo.updateTime;\n            newUpdateTime = parseInt(new Date(newUpdateTime).getTime() / 1000);\n            deliverystatus = await this.getDeliverystatus(deliverystatus);\n            console.log(deliverystatus);\n            let issign = lastExpressInfo.issign;\n            let traces = lastExpressInfo.list;\n            traces = JSON.stringify(traces);\n            console.log(traces);\n            let dataInfo = {\n                express_status: deliverystatus,\n                is_finish: issign,\n                traces: traces,\n                update_time: newUpdateTime\n            }\n            console.log('出发1222222221');\n            await this.model('order_express').where({\n                order_id: orderId\n            }).update(dataInfo);\n            let express = await this.model('order_express').where({\n                order_id: orderId\n            }).find();\n            return express;\n        }\n        // return this.success(latestExpressInfo);\n    }\n    async getExpressInfo(shipperName, expressNo) {\n        console.log('出发1111111');\n\t\tlet appCode = \"APPCODE \"+ think.config('aliexpress.appcode');\n        const options = {\n            method: 'GET',\n            url: 'http://wuliu.market.alicloudapi.com/kdi?no=' + expressNo + '&type=' + shipperName,\n            headers: {\n                \"Content-Type\": \"application/json; charset=utf-8\",\n                \"Authorization\": appCode\n            }\n        };\n        let sessionData = await rp(options);\n        sessionData = JSON.parse(sessionData);\n        return sessionData.result;\n    }\n    async getDeliverystatus(status) {\n        if (status == 0) {\n            return '快递收件(揽件)';\n        } else if (status == 1) {\n            return '在途中';\n        } else if (status == 2) {\n            return '正在派件';\n        } else if (status == 3) {\n            return '已签收';\n        } else if (status == 4) {\n            return '派送失败(无法联系到收件人或客户要求择日派送，地址不详或手机号不清)';\n        } else if (status == 5) {\n            return '疑难件(收件人拒绝签收，地址有误或不能送达派送区域，收费等原因无法正常派送)';\n        } else if (status == 6) {\n            return '退件签收';\n        }\n    }\n    async getMianExpress(orderId, senderInfo, receiverInfo, expressType) {\n        // 开始了\n        let ShipperCode = '';\n        let ExpType = 1;\n        let CustomerName = ''; // 圆通需要\n        let MonthCode = ''; // 圆通需要浙江省舟山市普陀区沈家门分部\n        let GoodsName = '';\n        // 测试开关 start\n        let testSwitch = 1; // 正式的时候，将这个设置为0\n        if (testSwitch == 1) {\n            if (expressType < 4) {\n                MonthCode = ''\n            } else {\n                CustomerName = 'testyto';\n                MonthCode = 'testytomonthcode';\n            }\n        } else {\n            if (expressType < 4) {\n                if (expressType == 1 || expressType == 2) {\n                    let info = await this.model('shipper').where({\n                        name: '顺丰速运'\n                    }).find();\n                    MonthCode = info.MonthCode;\n                } else if (expressType == 3) {\n                    let info = await this.model('shipper').where({\n                        name: '顺丰特惠'\n                    }).find();\n                    MonthCode = info.MonthCode;\n                }\n            } else {\n                let info = await this.model('shipper').where({\n                    code: 'YTO'\n                }).find();\n                CustomerName = info.CustomerName;\n                MonthCode = info.MonthCode;\n            }\n        }\n        // 测试开关 end\n        // 根据expressType 设置不同都属性\n        let returnExpressInfo = {};\n        if (expressType == 1) { // 顺丰保价：海鲜、梭子蟹\n            ShipperCode = 'SF';\n            GoodsName = '海鲜';\n        } else if (expressType == 2) { // 顺丰不保价：外省腌制品等\n            ShipperCode = 'SF';\n            GoodsName = '海产品';\n        } else if (expressType == 3) { // 顺丰特惠，江浙沪皖干货\n            ExpType = 2;\n            ShipperCode = 'SF';\n            GoodsName = '海干货';\n        } else if (expressType == 4) { // 圆通\n            ShipperCode = 'YTO';\n            GoodsName = '海产品';\n        }\n        returnExpressInfo = {\n            MemberID: '', //ERP系统、电商平台等系统或平台类型用户的会员ID或店铺账号等唯一性标识，用于区分其用户\n            // 电子面单客户号，需要下载《快递鸟电子面单客户号参数对照表.xlsx》，参考对应字段传值\n            CustomerPwd: '',\n            SendSite: '',\n            SendStaff: '',\n            // 圆通要传这个两个值\n            CustomerName: CustomerName,\n            MonthCode: MonthCode,\n            CustomArea: '', //商家自定义区域\n            WareHouseID: '', //发货仓编码\n            TransType: 1, // 1陆运，2空运，默认不填为1\n            ShipperCode: ShipperCode, // 必填项!!!-------------\n            LogisticCode: '', //快递单号(仅宅急送可用)\n            ThrOrderCode: '', //第三方订单号 (ShipperCode为JD且ExpType为1时必填)\n            OrderCode: '', //订单编号(自定义，不可重复)  必填项!!!----------\n            PayType: 3, // 邮费支付方式:1-现付，2-到付，3-月结，4-第三方支付(仅SF支持)  必填项!!!----------\n            ExpType: ExpType, // 快递类型：1-标准快件 2-特惠，干货用这个 ,详细快递类型参考《快递公司快递业务类型.xlsx》 必填项!!!----------\n            IsReturnSignBill: '', //是否要求签回单 1-  要求 0-不要求\n            OperateRequire: '', // 签回单操作要求(如：签名、盖章、身份证复印件等)\n            Cost: 0, // 快递运费\n            OtherCost: 0, // 其他费用\n            Receiver: {\n                Company: '', //收件人公司\n                Name: '', //收件人 必填项!!!-------------\n                Tel: '', // 电话与手机，必填一个\n                Mobile: '', //电话与手机，必填一个\n                PostCode: '', // 收件人邮编\n                ProvinceName: '', //收件省 必填项!!!-------------(如广东省，不要缺少“省”；如是直辖市，请直接传北京、上海等； 如是自治区，请直接传广西壮族自治区等)\n                CityName: '', //收件市 必填项!!!------------- (如深圳市，不要缺少“市”； 如果是市辖区，请直接传北京市、上海市等)\n                ExpAreaName: '', //收件区/县 必填项!!!-------------(如福田区，不要缺少“区”或“县”)\n                Address: '' // 收件人详细地址 必填项!!!-------------\n            },\n            Sender: {\n                Company: '', // 发件人公司\n                Name: '', //发件人 必填项!!!-------------\n                Tel: '', //电话与手机，必填一个 必填项!!!-------------\n                Mobile: '', //电话与手机，必填一个 必填项!!!-------------\n                PostCode: '', //发件地邮编(ShipperCode为EMS、YZPY、YZBK时必填)\n                ProvinceName: '', //发件省 必填项!!!-------------\n                CityName: '', //发件市 必填项!!!-------------\n                ExpAreaName: '', //发件区/县 必填项!!!------------\n                Address: '' /// 发件人详细地址 必填项!!!------\n            },\n            IsNotice: 1, //是否通知快递员上门揽件 0-   通知 1-   不通知 不填则默认为1\n            Quantity: 1, //必填项!!!------包裹数(最多支持30件) 一个包裹对应一个运单号，如果是大于1个包裹，返回则按照子母件的方式返回母运单号和子运单号\n            Remark: '', //备注\n            Commodity: [{\n                GoodsName: GoodsName, // 商品名称 必填项!!!------\n                GoodsCode: '', //商品编码\n                Goodsquantity: 0, //商品数量\n                GoodsPrice: 0, //商品价格\n                GoodsWeight: 0, //商品重量kg\n                GoodsDesc: '', //商品描述\n                GoodsVol: 0 //商品体积m3\n            }],\n            IsReturnPrintTemplate: 0, //返回电子面单模板：0-不需要；1-需要 todo\n            IsSendMessage: 0, //是否订阅短信：0-不需要；1-需要\n            TemplateSize: '', //模板规格(默认的模板无需传值，非默认模板传对应模板尺寸)\n            PackingType: 0, //包装类型(快运字段)默认为0； 0-    纸 1-    纤 2-    木 3-    托膜 4-   木托 99-其他\n            DeliveryMethod: 2 //送货方式(快运字段)默认为0； 0-  自提 1-   送货上门（不含上楼） 2-   送货上楼\n        };\n        const orderExpress = await this.model('order').where({\n            id: orderId\n        }).find();\n        if (think.isEmpty(orderExpress)) {\n            let error = 400;\n            return error;\n        }\n        returnExpressInfo.Remark = orderExpress.remark;\n        let expressValue = '0';\n        if (expressType == 1) { // 顺丰保价：海鲜、梭子蟹\n            expressValue = orderExpress.express_value;\n            returnExpressInfo.AddService = [{\n                \"Name\": \"INSURE\", //保价\n                \"Value\": expressValue,\n                \"CustomerID\": '0'\n            }, ]\n        }\n        // 这里就是重新生成订单号，然后记得存入order表中去 todo,这里有点问题，不应该去直接生成新的订单号，而应该让打单员选择\n        returnExpressInfo.OrderCode = orderExpress.order_sn;\n        // 这里就是重新生成订单号，然后记得存入order表中去 todo,这里有点问题，不应该去直接生成新的订单号，而应该让打单员选择\n        returnExpressInfo.Receiver = receiverInfo;\n        returnExpressInfo.Sender = senderInfo;\n        const ExpressSerivce = think.service('express', 'api');\n        const latestExpressInfo = await ExpressSerivce.mianExpress(returnExpressInfo);\n        // 将保价金额和时间回传。\n        const currentTime = parseInt(new Date().getTime() / 1000);\n        latestExpressInfo.send_time = moment.unix(currentTime).format('YYYY-MM-DD');\n        latestExpressInfo.expressValue = expressValue;\n        latestExpressInfo.remark = orderExpress.remark;\n        latestExpressInfo.expressType = expressType;\n        latestExpressInfo.MonthCode = MonthCode;\n        return latestExpressInfo;\n    }\n    async printExpress() {\n        const ExpressSerivce = think.service('express', 'api');\n        const latestExpressInfo = await ExpressSerivce.buildForm();\n        return latestExpressInfo;\n    }\n};"]}