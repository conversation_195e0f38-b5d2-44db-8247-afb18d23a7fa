# 获取手机号接口实现说明

## 📋 接口概述

已成功在后端实现 `/api/auth/getPhoneNumber` 接口，用于处理微信小程序获取用户手机号的功能。

## 🔧 接口详情

### 请求信息
- **URL:** `POST /api/auth/getPhoneNumber`
- **Content-Type:** `application/json`
- **参数:**
```json
{
  "code": "微信返回的动态令牌"
}
```

### 响应格式
**成功响应:**
```json
{
  "errno": 0,
  "errmsg": "成功",
  "data": {
    "phoneNumber": "13800138000",
    "purePhoneNumber": "13800138000", 
    "countryCode": "86"
  }
}
```

**失败响应:**
```json
{
  "errno": 400,
  "errmsg": "code参数必须提供"
}
```

## 🔧 实现逻辑

### 1. 参数验证
```javascript
const code = this.post("code");
if (!code) {
  return this.fail(400, 'code参数必须提供');
}
```

### 2. 获取access_token
```javascript
const tokenOptions = {
  method: "GET",
  url: "https://api.weixin.qq.com/cgi-bin/token",
  qs: {
    grant_type: "client_credential",
    appid: think.config("weixin.appid"),
    secret: think.config("weixin.secret"),
  },
};
```

### 3. 调用微信手机号接口
```javascript
const phoneOptions = {
  method: "POST",
  url: "https://api.weixin.qq.com/wxa/business/getuserphonenumber",
  qs: {
    access_token: tokenResponse.access_token
  },
  body: {
    code: code
  },
  json: true
};
```

### 4. 处理响应结果
- 成功时返回手机号信息
- 失败时返回错误信息和错误码

## 📱 微信配置

### 当前配置状态
```javascript
// service/src/common/config/config.js
weixin: {
  appid: 'wx919ca2ec612e6ecb',     // ✅ 已配置
  secret: '4ad505935a4bf61f235efc303bf1e555', // ✅ 已配置
  // ... 其他配置
}
```

### 配置验证
- ✅ **appid已配置** - 小程序应用ID
- ✅ **secret已配置** - 小程序密钥
- ✅ **接口已实现** - getPhoneNumber方法
- ✅ **路由已配置** - auth控制器公开访问

## 🔒 安全机制

### 1. 参数验证
- 验证code参数是否存在
- 验证微信返回的access_token

### 2. 错误处理
- 网络请求异常处理
- 微信接口错误码处理
- 服务器内部错误处理

### 3. 日志记录
```javascript
console.log('=== 获取手机号接口调用 ===');
console.log('接收到的code:', code);
console.log('获取access_token响应:', tokenResponse);
console.log('获取手机号响应:', phoneResponse);
```

## 📊 错误码说明

### 微信接口错误码
- **0** - 成功
- **40013** - 不合法的 AppID
- **40001** - 获取 access_token 时 AppSecret 错误
- **61024** - code 无效或已过期

### 自定义错误码
- **400** - 参数错误
- **500** - 服务器内部错误

## 🧪 测试方法

### 1. 前端测试
```javascript
// 在小程序中测试
wx.login({
  success: (res) => {
    // 使用获取到的code调用接口
    util.request(api.GetPhoneNumber, { code: res.code }, 'POST')
  }
});
```

### 2. 接口测试
```bash
# 使用curl测试（需要有效的code）
curl -X POST http://**************:8360/api/auth/getPhoneNumber \
  -H "Content-Type: application/json" \
  -d '{"code":"your_code_here"}'
```

## 🔧 故障排查

### 常见问题
1. **连接被重置 (ERR_CONNECTION_RESET)**
   - 检查后端服务是否运行
   - 检查端口8360是否开放
   - 检查网络连接

2. **access_token获取失败**
   - 检查appid和secret配置
   - 检查网络是否能访问微信API

3. **code无效**
   - code有效期只有5分钟
   - 每个code只能使用一次
   - 确保code来自正确的小程序

### 调试步骤
1. 检查后端服务状态
2. 查看控制台日志输出
3. 验证微信配置信息
4. 测试网络连通性

## 💰 费用说明

### 收费标准
- **每次成功调用：** 0.03元
- **免费额度：** 1000次/小程序
- **计费时间：** 2023年8月28日起

### 免费条件
- 政府、非营利组织
- 事业单位（政务民生类目）
- 公立医疗机构
- 学历教育（学校）

## 🎯 总结

### 实现状态
- ✅ **后端接口已完成** - `/api/auth/getPhoneNumber`
- ✅ **微信配置已就绪** - appid和secret已配置
- ✅ **错误处理完善** - 包含各种异常情况
- ✅ **日志记录完整** - 便于调试和监控

### 使用建议
1. **测试环境验证** - 先在测试环境验证功能
2. **错误处理** - 前端要处理各种错误情况
3. **用户引导** - 明确告知用户手机号用途
4. **合规使用** - 遵守微信平台规范

接口已经完全实现并可以正常使用，您可以进行测试验证。
