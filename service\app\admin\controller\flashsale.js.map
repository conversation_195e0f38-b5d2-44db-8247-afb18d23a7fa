{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\flashsale.js"], "names": ["Base", "require", "moment", "FlashSaleScheduler", "module", "exports", "statisticsAction", "console", "log", "campaignModel", "model", "roundModel", "orderModel", "totalCampaigns", "count", "activeCampaigns", "where", "status", "today", "format", "todayRounds", "start_time", "add", "flashSaleOrders", "salesResult", "sum", "flashSaleSales", "statistics", "toFixed", "success", "error", "fail", "roundsAction", "scheduler", "currentRounds", "getCurrentRounds", "bind", "upcomingRounds", "getUpcomingRounds", "endExpiredRounds", "result", "current", "upcoming", "total", "length", "campaignsAction", "search", "get", "page", "parseInt", "limit", "goodsModel", "campaigns", "order", "select", "campaign", "goods", "id", "goods_id", "find", "think", "isEmpty", "name", "toLowerCase", "includes", "totalRounds", "campaign_id", "activeRounds", "soldResult", "totalSold", "item", "goodsName", "goodsImage", "list_pic_url", "primary_pic_url", "originalPrice", "original_price", "flashPrice", "flash_price", "totalStock", "total_stock", "stockPerRound", "stock_per_round", "limitQuantity", "limit_quantity", "startDate", "start_date", "endDate", "end_date", "dailyStartTime", "daily_start_time", "dailyEndTime", "daily_end_time", "roundDuration", "round_duration", "breakDuration", "break_duration", "createdAt", "created_at", "push", "createAction", "data", "post", "goodsId", "Date", "existingCampaign", "campaignData", "retail_price", "auto_start", "autoStart", "campaignId", "roundCount", "generateRounds", "update", "message", "startAction"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;AACA,MAAME,qBAAqBF,QAAQ,6CAAR,CAA3B;;AAEAG,OAAOC,OAAP,GAAiB,cAAcL,IAAd,CAAmB;;AAElC;;;;AAIMM,kBAAN,GAAyB;AAAA;;AAAA;AACvB,UAAI;AACFC,gBAAQC,GAAR,CAAY,kBAAZ;;AAEA,cAAMC,gBAAgB,MAAKC,KAAL,CAAW,sBAAX,CAAtB;AACA,cAAMC,aAAa,MAAKD,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAME,aAAa,MAAKF,KAAL,CAAW,mBAAX,CAAnB;;AAEA;AACA,cAAMG,iBAAiB,MAAMJ,cAAcK,KAAd,EAA7B;;AAEA;AACA,cAAMC,kBAAkB,MAAMN,cAAcO,KAAd,CAAoB;AAChDC,kBAAQ;AADwC,SAApB,EAE3BH,KAF2B,EAA9B;;AAIA;AACA,cAAMI,QAAQhB,SAASiB,MAAT,CAAgB,YAAhB,CAAd;AACA,cAAMC,cAAc,MAAMT,WAAWK,KAAX,CAAiB;AACzCK,sBAAY,CAAC,IAAD,EAAOH,QAAQ,WAAf,CAD6B;AAEzCG,sBAAY,CAAC,GAAD,EAAMnB,OAAOgB,KAAP,EAAcI,GAAd,CAAkB,CAAlB,EAAqB,KAArB,EAA4BH,MAA5B,CAAmC,YAAnC,IAAmD,WAAzD;AAF6B,SAAjB,EAGvBL,KAHuB,EAA1B;;AAKA;AACA,cAAMS,kBAAkB,MAAMX,WAAWE,KAAX,EAA9B;;AAEA;AACA,cAAMU,cAAc,MAAMZ,WAAWa,GAAX,CAAe,cAAf,CAA1B;AACA,cAAMC,iBAAiBF,eAAe,CAAtC;;AAEA,cAAMG,aAAa;AACjBd,wBADiB;AAEjBE,yBAFiB;AAGjBK,qBAHiB;AAIjBG,yBAJiB;AAKjBG,0BAAgBA,eAAeE,OAAf,CAAuB,CAAvB;AALC,SAAnB;;AAQArB,gBAAQC,GAAR,CAAY,OAAZ,EAAqBmB,UAArB;AACA,eAAO,MAAKE,OAAL,CAAaF,UAAb,CAAP;AAED,OAxCD,CAwCE,OAAOG,KAAP,EAAc;AACdvB,gBAAQuB,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,MAAKC,IAAL,CAAU,UAAV,CAAP;AACD;AA5CsB;AA6CxB;;AAED;;;;AAIMC,cAAN,GAAqB;AAAA;;AAAA;AACnB,UAAI;AACFzB,gBAAQC,GAAR,CAAY,kBAAZ;;AAEA,cAAMyB,YAAY,IAAI9B,kBAAJ,EAAlB;;AAEA;AACA,cAAM+B,gBAAgB,MAAMD,UAAUE,gBAAV,CAA2B,OAAKzB,KAAL,CAAW0B,IAAX,QAA3B,CAA5B;;AAEA;AACA,cAAMC,iBAAiB,MAAMJ,UAAUK,iBAAV,CAA4B,OAAK5B,KAAL,CAAW0B,IAAX,QAA5B,EAAmD,EAAnD,CAA7B;;AAEA;AACA,cAAMH,UAAUM,gBAAV,CAA2B,OAAK7B,KAAL,CAAW0B,IAAX,QAA3B,CAAN;;AAEA,cAAMI,SAAS;AACbC,mBAASP,aADI;AAEbQ,oBAAUL,cAFG;AAGbM,iBAAOT,cAAcU,MAAd,GAAuBP,eAAeO;AAHhC,SAAf;;AAMArC,gBAAQC,GAAR,CAAY,OAAZ,EAAqBgC,MAArB;AACA,eAAO,OAAKX,OAAL,CAAaW,MAAb,CAAP;AAED,OAvBD,CAuBE,OAAOV,KAAP,EAAc;AACdvB,gBAAQuB,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKC,IAAL,CAAU,UAAV,CAAP;AACD;AA3BkB;AA4BpB;;AAED;;;;AAIMc,iBAAN,GAAwB;AAAA;;AAAA;AACtB,UAAI;AACFtC,gBAAQC,GAAR,CAAY,kBAAZ;;AAEA,cAAMsC,SAAS,OAAKC,GAAL,CAAS,QAAT,KAAsB,EAArC;AACA,cAAM9B,SAAS,OAAK8B,GAAL,CAAS,QAAT,KAAsB,EAArC;AACA,cAAMC,OAAOC,SAAS,OAAKF,GAAL,CAAS,MAAT,CAAT,KAA8B,CAA3C;AACA,cAAMG,QAAQD,SAAS,OAAKF,GAAL,CAAS,OAAT,CAAT,KAA+B,EAA7C;;AAEA,cAAMtC,gBAAgB,OAAKC,KAAL,CAAW,sBAAX,CAAtB;AACA,cAAMyC,aAAa,OAAKzC,KAAL,CAAW,OAAX,CAAnB;AACA,cAAMC,aAAa,OAAKD,KAAL,CAAW,mBAAX,CAAnB;;AAEA,YAAIM,QAAQ,EAAZ;;AAEA;AACA,YAAIC,MAAJ,EAAY;AACVD,gBAAMC,MAAN,GAAeA,MAAf;AACD;;AAED;AACA,cAAMmC,YAAY,MAAM3C,cAAcO,KAAd,CAAoBA,KAApB,EACrBqC,KADqB,CACf,iBADe,EAErBL,IAFqB,CAEhBA,IAFgB,EAEVE,KAFU,EAGrBI,MAHqB,EAAxB;;AAKA,cAAMd,SAAS,EAAf;;AAEA;AACA,aAAK,MAAMe,QAAX,IAAuBH,SAAvB,EAAkC;AAChC,gBAAMI,QAAQ,MAAML,WAAWnC,KAAX,CAAiB,EAAEyC,IAAIF,SAASG,QAAf,EAAjB,EAA4CC,IAA5C,EAApB;;AAEA,cAAI,CAACC,MAAMC,OAAN,CAAcL,KAAd,CAAL,EAA2B;AACzB;AACA,gBAAIV,UAAU,CAACU,MAAMM,IAAN,CAAWC,WAAX,GAAyBC,QAAzB,CAAkClB,OAAOiB,WAAP,EAAlC,CAAf,EAAwE;AACtE;AACD;;AAED;AACA,kBAAME,cAAc,MAAMtD,WAAWK,KAAX,CAAiB,EAAEkD,aAAaX,SAASE,EAAxB,EAAjB,EAA+C3C,KAA/C,EAA1B;AACA,kBAAMqD,eAAe,MAAMxD,WAAWK,KAAX,CAAiB;AAC1CkD,2BAAaX,SAASE,EADoB;AAE1CxC,sBAAQ;AAFkC,aAAjB,EAGxBH,KAHwB,EAA3B;AAIA,kBAAMuB,iBAAiB,MAAM1B,WAAWK,KAAX,CAAiB;AAC5CkD,2BAAaX,SAASE,EADsB;AAE5CxC,sBAAQ;AAFoC,aAAjB,EAG1BH,KAH0B,EAA7B;;AAKA;AACA,kBAAMsD,aAAa,MAAMzD,WAAWK,KAAX,CAAiB,EAAEkD,aAAaX,SAASE,EAAxB,EAAjB,EAA+ChC,GAA/C,CAAmD,YAAnD,CAAzB;AACA,kBAAM4C,YAAYD,cAAc,CAAhC;;AAEA,kBAAME,OAAO;AACXb,kBAAIF,SAASE,EADF;AAEXK,oBAAMP,SAASO,IAFJ;AAGXS,yBAAWf,MAAMM,IAHN;AAIXU,0BAAYhB,MAAMiB,YAAN,IAAsBjB,MAAMkB,eAA5B,IAA+C,EAJhD;AAKXC,6BAAepB,SAASqB,cALb;AAMXC,0BAAYtB,SAASuB,WANV;AAOXC,0BAAYxB,SAASyB,WAPV;AAQXC,6BAAe1B,SAAS2B,eARb;AASXb,yBAAWA,SATA;AAUXc,6BAAe5B,SAAS6B,cAVb;AAWXC,yBAAW9B,SAAS+B,UAXT;AAYXC,uBAAShC,SAASiC,QAZP;AAaXC,8BAAgBlC,SAASmC,gBAbd;AAcXC,4BAAcpC,SAASqC,cAdZ;AAeXC,6BAAetC,SAASuC,cAfb;AAgBXC,6BAAexC,SAASyC,cAhBb;AAiBX/E,sBAAQsC,SAAStC,MAjBN;AAkBXgD,2BAAaA,WAlBF;AAmBXE,4BAAcA,YAnBH;AAoBX9B,8BAAgBA,cApBL;AAqBX4D,yBAAW1C,SAAS2C;AArBT,aAAb;;AAwBA1D,mBAAO2D,IAAP,CAAY7B,IAAZ;AACD;AACF;;AAED/D,gBAAQC,GAAR,CAAY,OAAZ,EAAqBgC,OAAOI,MAA5B,EAAoC,KAApC;AACA,eAAO,OAAKf,OAAL,CAAaW,MAAb,CAAP;AAED,OAnFD,CAmFE,OAAOV,KAAP,EAAc;AACdvB,gBAAQuB,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKC,IAAL,CAAU,UAAV,CAAP;AACD;AAvFqB;AAwFvB;;AAED;;;;AAIMqE,cAAN,GAAqB;AAAA;;AAAA;AACnB,UAAI;AACF7F,gBAAQC,GAAR,CAAY,gBAAZ;;AAEA,cAAM6F,OAAO,OAAKC,IAAL,EAAb;AACA/F,gBAAQC,GAAR,CAAY,OAAZ,EAAqB6F,IAArB;;AAEA;AACA,YAAI,CAACA,KAAKvC,IAAN,IAAc,CAACuC,KAAKE,OAApB,IAA+B,CAACF,KAAKxB,UAArC,IAAmD,CAACwB,KAAKtB,UAAzD,IAAuE,CAACsB,KAAKpB,aAAjF,EAAgG;AAC9F,iBAAO,OAAKlD,IAAL,CAAU,YAAV,CAAP;AACD;;AAED;AACA,YAAI,CAACsE,KAAKhB,SAAN,IAAmB,CAACgB,KAAKd,OAA7B,EAAsC;AACpC,iBAAO,OAAKxD,IAAL,CAAU,SAAV,CAAP;AACD;;AAED,YAAI,IAAIyE,IAAJ,CAASH,KAAKhB,SAAd,KAA4B,IAAImB,IAAJ,CAASH,KAAKd,OAAd,CAAhC,EAAwD;AACtD,iBAAO,OAAKxD,IAAL,CAAU,cAAV,CAAP;AACD;;AAED,cAAMtB,gBAAgB,OAAKC,KAAL,CAAW,sBAAX,CAAtB;AACA,cAAMyC,aAAa,OAAKzC,KAAL,CAAW,OAAX,CAAnB;;AAEA;AACA,cAAM8C,QAAQ,MAAML,WAAWnC,KAAX,CAAiB,EAAEyC,IAAI4C,KAAKE,OAAX,EAAjB,EAAuC5C,IAAvC,EAApB;AACA,YAAIC,MAAMC,OAAN,CAAcL,KAAd,CAAJ,EAA0B;AACxB,iBAAO,OAAKzB,IAAL,CAAU,OAAV,CAAP;AACD;;AAED;AACA,cAAM0E,mBAAmB,MAAMhG,cAAcO,KAAd,CAAoB;AACjD0C,oBAAU2C,KAAKE,OADkC;AAEjDtF,kBAAQ,CAAC,IAAD,EAAO,CAAC,QAAD,EAAW,OAAX,CAAP;AAFyC,SAApB,EAG5B0C,IAH4B,EAA/B;;AAKA,YAAI,CAACC,MAAMC,OAAN,CAAc4C,gBAAd,CAAL,EAAsC;AACpC,iBAAO,OAAK1E,IAAL,CAAU,eAAV,CAAP;AACD;;AAED;AACA,cAAM2E,eAAe;AACnB5C,gBAAMuC,KAAKvC,IADQ;AAEnBJ,oBAAU2C,KAAKE,OAFI;AAGnBzB,uBAAauB,KAAKxB,UAHC;AAInBD,0BAAgByB,KAAK1B,aAAL,IAAsBnB,MAAMmD,YAJzB;AAKnB3B,uBAAaqB,KAAKtB,UALC;AAMnBG,2BAAiBmB,KAAKpB,aANH;AAOnBG,0BAAgBiB,KAAKlB,aAAL,IAAsB,CAPnB;AAQnBG,sBAAYe,KAAKhB,SARE;AASnBG,oBAAUa,KAAKd,OATI;AAUnBG,4BAAkBW,KAAKZ,cAAL,IAAuB,UAVtB;AAWnBG,0BAAgBS,KAAKV,YAAL,IAAqB,UAXlB;AAYnBG,0BAAgBO,KAAKR,aAAL,IAAsB,GAZnB,EAYwB;AAC3CG,0BAAgBK,KAAKN,aAAL,IAAsB,CAbnB,EAayB;AAC5C9E,kBAAQ,OAdW;AAenB2F,sBAAYP,KAAKQ,SAAL,IAAkB;AAfX,SAArB;;AAkBA,cAAMC,aAAa,MAAMrG,cAAca,GAAd,CAAkBoF,YAAlB,CAAzB;;AAEA,YAAII,UAAJ,EAAgB;AACdvG,kBAAQC,GAAR,CAAY,cAAZ,EAA4BsG,UAA5B;;AAEA;AACA,cAAIT,KAAKQ,SAAT,EAAoB;AAClB,kBAAMtD,WAAW,MAAM9C,cAAcO,KAAd,CAAoB,EAAEyC,IAAIqD,UAAN,EAApB,EAAwCnD,IAAxC,EAAvB;AACA,kBAAM1B,YAAY,IAAI9B,kBAAJ,EAAlB;AACA,kBAAM4G,aAAa,MAAM9E,UAAU+E,cAAV,CAAyBzD,QAAzB,EAAmC,OAAK7C,KAAL,CAAW0B,IAAX,QAAnC,CAAzB;;AAEA;AACA,kBAAM3B,cAAcO,KAAd,CAAoB,EAAEyC,IAAIqD,UAAN,EAApB,EAAwCG,MAAxC,CAA+C,EAAEhG,QAAQ,QAAV,EAA/C,CAAN;;AAEAV,oBAAQC,GAAR,CAAa,SAAQuG,UAAW,MAAhC;AACD;;AAED,iBAAO,OAAKlF,OAAL,CAAa,EAAE4B,IAAIqD,UAAN,EAAb,CAAP;AACD,SAhBD,MAgBO;AACL,iBAAO,OAAK/E,IAAL,CAAU,MAAV,CAAP;AACD;AAEF,OAhFD,CAgFE,OAAOD,KAAP,EAAc;AACdvB,gBAAQuB,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKC,IAAL,CAAU,WAAWD,MAAMoF,OAA3B,CAAP;AACD;AApFkB;AAqFpB;;AAED;;;;AAIMC,aAAN,GAAoB;AAAA;;AAAA;AAClB,UAAI;AACF,cAAML,aAAa,OAAKR,IAAL,CAAU,YAAV,CAAnB;AACA,YAAI,CAACQ,UAAL,EAAiB;AACf,iBAAO,OAAK/E,IAAL,CAAU,SAAV,CAAP;AACD;;AAED,cAAMtB,gBAAgB,OAAKC,KAAL,CAAW,sBAAX,CAAtB;AACA,cAAM6C,WAAW,MAAM9C,cAAcO,KAAd,CAAoB,EAAEyC,IAAIqD,UAAN,EAApB,EAAwCnD,IAAxC,EAAvB;;AAEA,YAAIC,MAAMC,OAAN,CAAcN,QAAd,CAAJ,EAA6B;AAC3B,iBAAO,OAAKxB,IAAL,CAAU,OAAV,CAAP;AACD;;AAED,YAAIwB,SAAStC,MAAT,KAAoB,OAAxB,EAAiC;AAC/B,iBAAO,OAAKc,IAAL,CAAU,aAAV,CAAP;AACD;;AAED;AACA,cAAME,YAAY,IAAI9B,kBAAJ,EAAlB;AACA,cAAM4G,aAAa,MAAM9E,UAAU+E,cAAV,CAAyBzD,QAAzB,EAAmC,OAAK7C,KAAL,CAAW0B,IAAX,QAAnC,CAAzB;;AAEA;AACA,cAAM3B,cAAcO,KAAd,CAAoB,EAAEyC,IAAIqD,UAAN,EAApB,EAAwCG,MAAxC,CAA+C,EAAEhG,QAAQ,QAAV,EAA/C,CAAN;;AAEAV,gBAAQC,GAAR,CAAa,cAAauG,UAAW,MAArC;AACA,eAAO,OAAKlF,OAAL,CAAa,EAAEkF,UAAF,EAAb,CAAP;AAED,OA3BD,CA2BE,OAAOjF,KAAP,EAAc;AACdvB,gBAAQuB,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,eAAO,OAAKC,IAAL,CAAU,WAAWD,MAAMoF,OAA3B,CAAP;AACD;AA/BiB;AAgCnB;AApTiC,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\flashsale.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\nconst FlashSaleScheduler = require('../../common/service/flashsale_scheduler.js');\n\nmodule.exports = class extends Base {\n\n  /**\n   * 获取秒杀统计数据\n   * GET /admin/flashsale/statistics\n   */\n  async statisticsAction() {\n    try {\n      console.log('=== 获取秒杀统计数据 ===');\n\n      const campaignModel = this.model('flash_sale_campaigns');\n      const roundModel = this.model('flash_sale_rounds');\n      const orderModel = this.model('flash_sale_orders');\n\n      // 获取总活动数\n      const totalCampaigns = await campaignModel.count();\n\n      // 获取进行中的活动数\n      const activeCampaigns = await campaignModel.where({\n        status: 'active'\n      }).count();\n\n      // 获取今日轮次数\n      const today = moment().format('YYYY-MM-DD');\n      const todayRounds = await roundModel.where({\n        start_time: ['>=', today + ' 00:00:00'],\n        start_time: ['<', moment(today).add(1, 'day').format('YYYY-MM-DD') + ' 00:00:00']\n      }).count();\n\n      // 获取秒杀订单数\n      const flashSaleOrders = await orderModel.count();\n\n      // 获取秒杀销售额\n      const salesResult = await orderModel.sum('total_amount');\n      const flashSaleSales = salesResult || 0;\n\n      const statistics = {\n        totalCampaigns,\n        activeCampaigns,\n        todayRounds,\n        flashSaleOrders,\n        flashSaleSales: flashSaleSales.toFixed(2)\n      };\n\n      console.log('统计数据:', statistics);\n      return this.success(statistics);\n\n    } catch (error) {\n      console.error('获取统计数据失败:', error);\n      return this.fail('获取统计数据失败');\n    }\n  }\n\n  /**\n   * 获取当前和即将开始的轮次\n   * GET /admin/flashsale/rounds\n   */\n  async roundsAction() {\n    try {\n      console.log('=== 获取秒杀轮次列表 ===');\n\n      const scheduler = new FlashSaleScheduler();\n\n      // 获取当前进行中的轮次\n      const currentRounds = await scheduler.getCurrentRounds(this.model.bind(this));\n\n      // 获取即将开始的轮次（未来30分钟内）\n      const upcomingRounds = await scheduler.getUpcomingRounds(this.model.bind(this), 30);\n\n      // 结束过期的轮次\n      await scheduler.endExpiredRounds(this.model.bind(this));\n\n      const result = {\n        current: currentRounds,\n        upcoming: upcomingRounds,\n        total: currentRounds.length + upcomingRounds.length\n      };\n\n      console.log('轮次数据:', result);\n      return this.success(result);\n\n    } catch (error) {\n      console.error('获取轮次列表失败:', error);\n      return this.fail('获取轮次列表失败');\n    }\n  }\n\n  /**\n   * 获取秒杀活动列表\n   * GET /admin/flashsale/campaigns\n   */\n  async campaignsAction() {\n    try {\n      console.log('=== 获取秒杀活动列表 ===');\n\n      const search = this.get('search') || '';\n      const status = this.get('status') || '';\n      const page = parseInt(this.get('page')) || 1;\n      const limit = parseInt(this.get('limit')) || 20;\n\n      const campaignModel = this.model('flash_sale_campaigns');\n      const goodsModel = this.model('goods');\n      const roundModel = this.model('flash_sale_rounds');\n\n      let where = {};\n\n      // 状态筛选\n      if (status) {\n        where.status = status;\n      }\n\n      // 获取活动列表\n      const campaigns = await campaignModel.where(where)\n        .order('created_at DESC')\n        .page(page, limit)\n        .select();\n\n      const result = [];\n\n      // 为每个活动获取商品信息和轮次统计\n      for (const campaign of campaigns) {\n        const goods = await goodsModel.where({ id: campaign.goods_id }).find();\n\n        if (!think.isEmpty(goods)) {\n          // 搜索过滤\n          if (search && !goods.name.toLowerCase().includes(search.toLowerCase())) {\n            continue;\n          }\n\n          // 获取轮次统计\n          const totalRounds = await roundModel.where({ campaign_id: campaign.id }).count();\n          const activeRounds = await roundModel.where({\n            campaign_id: campaign.id,\n            status: 'active'\n          }).count();\n          const upcomingRounds = await roundModel.where({\n            campaign_id: campaign.id,\n            status: 'upcoming'\n          }).count();\n\n          // 计算总销量\n          const soldResult = await roundModel.where({ campaign_id: campaign.id }).sum('sold_count');\n          const totalSold = soldResult || 0;\n\n          const item = {\n            id: campaign.id,\n            name: campaign.name,\n            goodsName: goods.name,\n            goodsImage: goods.list_pic_url || goods.primary_pic_url || '',\n            originalPrice: campaign.original_price,\n            flashPrice: campaign.flash_price,\n            totalStock: campaign.total_stock,\n            stockPerRound: campaign.stock_per_round,\n            totalSold: totalSold,\n            limitQuantity: campaign.limit_quantity,\n            startDate: campaign.start_date,\n            endDate: campaign.end_date,\n            dailyStartTime: campaign.daily_start_time,\n            dailyEndTime: campaign.daily_end_time,\n            roundDuration: campaign.round_duration,\n            breakDuration: campaign.break_duration,\n            status: campaign.status,\n            totalRounds: totalRounds,\n            activeRounds: activeRounds,\n            upcomingRounds: upcomingRounds,\n            createdAt: campaign.created_at\n          };\n\n          result.push(item);\n        }\n      }\n\n      console.log('活动列表:', result.length, '个活动');\n      return this.success(result);\n\n    } catch (error) {\n      console.error('获取活动列表失败:', error);\n      return this.fail('获取活动列表失败');\n    }\n  }\n\n  /**\n   * 创建秒杀活动\n   * POST /admin/flashsale/create\n   */\n  async createAction() {\n    try {\n      console.log('=== 创建秒杀活动 ===');\n\n      const data = this.post();\n      console.log('创建数据:', data);\n\n      // 验证必填字段\n      if (!data.name || !data.goodsId || !data.flashPrice || !data.totalStock || !data.stockPerRound) {\n        return this.fail('请填写完整的活动信息');\n      }\n\n      // 验证日期\n      if (!data.startDate || !data.endDate) {\n        return this.fail('请设置活动日期');\n      }\n\n      if (new Date(data.startDate) >= new Date(data.endDate)) {\n        return this.fail('开始日期必须早于结束日期');\n      }\n\n      const campaignModel = this.model('flash_sale_campaigns');\n      const goodsModel = this.model('goods');\n\n      // 检查商品是否存在\n      const goods = await goodsModel.where({ id: data.goodsId }).find();\n      if (think.isEmpty(goods)) {\n        return this.fail('商品不存在');\n      }\n\n      // 检查商品是否已经有进行中的活动\n      const existingCampaign = await campaignModel.where({\n        goods_id: data.goodsId,\n        status: ['IN', ['active', 'draft']]\n      }).find();\n\n      if (!think.isEmpty(existingCampaign)) {\n        return this.fail('该商品已有进行中的秒杀活动');\n      }\n\n      // 创建活动配置\n      const campaignData = {\n        name: data.name,\n        goods_id: data.goodsId,\n        flash_price: data.flashPrice,\n        original_price: data.originalPrice || goods.retail_price,\n        total_stock: data.totalStock,\n        stock_per_round: data.stockPerRound,\n        limit_quantity: data.limitQuantity || 1,\n        start_date: data.startDate,\n        end_date: data.endDate,\n        daily_start_time: data.dailyStartTime || '09:00:00',\n        daily_end_time: data.dailyEndTime || '22:00:00',\n        round_duration: data.roundDuration || 300, // 默认5分钟\n        break_duration: data.breakDuration || 0,    // 默认无间隔\n        status: 'draft',\n        auto_start: data.autoStart || 1\n      };\n\n      const campaignId = await campaignModel.add(campaignData);\n\n      if (campaignId) {\n        console.log('秒杀活动创建成功，ID:', campaignId);\n\n        // 如果设置为自动开始，则生成轮次\n        if (data.autoStart) {\n          const campaign = await campaignModel.where({ id: campaignId }).find();\n          const scheduler = new FlashSaleScheduler();\n          const roundCount = await scheduler.generateRounds(campaign, this.model.bind(this));\n\n          // 更新状态为active\n          await campaignModel.where({ id: campaignId }).update({ status: 'active' });\n\n          console.log(`自动生成了 ${roundCount} 个轮次`);\n        }\n\n        return this.success({ id: campaignId });\n      } else {\n        return this.fail('创建失败');\n      }\n\n    } catch (error) {\n      console.error('创建秒杀活动失败:', error);\n      return this.fail('创建失败: ' + error.message);\n    }\n  }\n\n  /**\n   * 启动秒杀活动\n   * POST /admin/flashsale/start\n   */\n  async startAction() {\n    try {\n      const campaignId = this.post('campaignId');\n      if (!campaignId) {\n        return this.fail('请提供活动ID');\n      }\n\n      const campaignModel = this.model('flash_sale_campaigns');\n      const campaign = await campaignModel.where({ id: campaignId }).find();\n\n      if (think.isEmpty(campaign)) {\n        return this.fail('活动不存在');\n      }\n\n      if (campaign.status !== 'draft') {\n        return this.fail('只能启动草稿状态的活动');\n      }\n\n      // 生成轮次\n      const scheduler = new FlashSaleScheduler();\n      const roundCount = await scheduler.generateRounds(campaign, this.model.bind(this));\n\n      // 更新状态\n      await campaignModel.where({ id: campaignId }).update({ status: 'active' });\n\n      console.log(`活动启动成功，生成了 ${roundCount} 个轮次`);\n      return this.success({ roundCount });\n\n    } catch (error) {\n      console.error('启动活动失败:', error);\n      return this.fail('启动失败: ' + error.message);\n    }\n  }\n};\n"]}