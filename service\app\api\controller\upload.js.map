{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\upload.js"], "names": ["Base", "require", "fs", "path", "module", "exports", "uploadAvatarAction", "file", "fileType", "type", "splice<PERSON>ength", "lastIndexOf", "fileTypeText", "slice", "think", "isEmpty", "fail", "that", "name", "uuid", "filename", "is", "createReadStream", "os", "createWriteStream", "ROOT_PATH", "pipe", "success", "fileUrl"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,KAAKD,QAAQ,IAAR,CAAX;AACA,MAAME,OAAOF,QAAQ,MAAR,CAAb;;AAEAG,OAAOC,OAAP,GAAiB,cAAcL,IAAd,CAAmB;AAC5BM,oBAAN,GAA2B;AAAA;;AAAA;AACzB,YAAMC,OAAO,MAAKA,IAAL,CAAU,aAAV,CAAb;AACA,UAAIC,WAAWD,KAAKE,IAApB;AACA,YAAMC,eAAeF,SAASG,WAAT,CAAqB,GAArB,CAArB;AACA,UAAIC,eAAeJ,SAASK,KAAT,CAAeH,eAAe,CAA9B,CAAnB;AACA,UAAII,MAAMC,OAAN,CAAcR,IAAd,CAAJ,EAAyB;AACvB,eAAO,MAAKS,IAAL,CAAU,MAAV,CAAP;AACD;AACD,YAAMC,YAAN;AACA,UAAIC,OAAOJ,MAAMK,IAAN,CAAW,EAAX,IAAiB,GAAjB,GAAuBP,YAAlC;AACA,YAAMQ,WAAW,2BAA2BF,IAA5C;AACA,YAAMG,KAAKnB,GAAGoB,gBAAH,CAAoBf,KAAKJ,IAAzB,CAAX;AACA,YAAMoB,KAAKrB,GAAGsB,iBAAH,CAAqBV,MAAMW,SAAN,GAAkB,MAAlB,GAA2BL,QAAhD,CAAX;AACAC,SAAGK,IAAH,CAAQH,EAAR;AACA,aAAON,KAAKU,OAAL,CAAa;AAClBT,cAAMA,IADY;AAElBU,iBAASR;AAFS,OAAb,CAAP;AAdyB;AAkB1B;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA/BkC,CAApC", "file": "..\\..\\..\\src\\api\\controller\\upload.js", "sourcesContent": ["const Base = require(\"./base.js\");\nconst fs = require(\"fs\");\nconst path = require(\"path\");\n\nmodule.exports = class extends Base {\n  async uploadAvatarAction() {\n    const file = this.file('upload_file');\n    let fileType = file.type;\n    const spliceLength = fileType.lastIndexOf(\"/\");\n    let fileTypeText = fileType.slice(spliceLength + 1);\n    if (think.isEmpty(file)) {\n      return this.fail(\"保存失败\");\n    }\n    const that = this;\n    let name = think.uuid(32) + \".\" + fileTypeText;\n    const filename = \"/static/upload/avatar/\" + name;\n    const is = fs.createReadStream(file.path);\n    const os = fs.createWriteStream(think.ROOT_PATH + \"/www\" + filename);\n    is.pipe(os);\n    return that.success({\n      name: name,\n      fileUrl: filename,\n    });\n  }\n\n  // async deleteFileAction() {\n  //     const url = this.post('para');\n  //     let newUrl = url.lastIndexOf(\"/\");\n  //     let fileName = url.substring(newUrl + 1);\n  //     let delePath = './www/static/upload/goods/detail/' + fileName;\n  //     fs.unlink(delePath, function (err) {\n  //         if (err) throw err;\n  //         return false;\n  //     });\n  //     return this.success('文件删除成功');\n  // }\n};\n"]}