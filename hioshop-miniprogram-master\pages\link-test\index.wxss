/* 链接测试页面样式 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20rpx;
}

/* 顶部标题 */
.header {
  text-align: center;
  padding: 40rpx 20rpx;
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.1);
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 链接列表 */
.link-list {
  margin-bottom: 40rpx;
}

.link-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  border-left: 8rpx solid #667eea;
}

.link-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.15);
}

.link-content {
  flex: 1;
}

.link-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.link-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.link-url {
  font-size: 22rpx;
  color: #999;
  background: #f8f9fa;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  font-family: 'Courier New', monospace;
  word-break: break-all;
}

.link-arrow {
  font-size: 36rpx;
  color: #667eea;
  font-weight: bold;
  margin-left: 20rpx;
}

/* 特殊功能区域 */
.special-actions {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.action-btn {
  width: 100%;
  padding: 24rpx;
  border-radius: 16rpx;
  border: none;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn:last-child {
  margin-bottom: 0;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-btn.secondary {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #8b4513;
}

.action-btn.backend-test {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #2c3e50;
}

.action-btn:active {
  transform: scale(0.98);
}

.btn-icon {
  margin-right: 16rpx;
  font-size: 32rpx;
}

.btn-text {
  font-size: 28rpx;
}

/* 说明信息 */
.info-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.1);
}

.info-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.info-content {
  display: flex;
  flex-direction: column;
}

.info-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
  padding-left: 20rpx;
  position: relative;
}

.info-item:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 8rpx;
  background: #667eea;
  border-radius: 50%;
}

/* 底部操作 */
.bottom-actions {
  padding: 20rpx 0;
  text-align: center;
}

.home-btn {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
  border: none;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.home-btn:active {
  transform: scale(0.95);
}

.home-icon {
  margin-right: 12rpx;
  font-size: 30rpx;
}
