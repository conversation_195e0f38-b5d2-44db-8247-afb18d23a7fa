/*
 优惠券系统数据库表设计
 Date: 2024-06-24
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for hiolabs_coupons
-- ----------------------------
DROP TABLE IF EXISTS `hiolabs_coupons`;
CREATE TABLE `hiolabs_coupons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '优惠券名称',
  `code` varchar(50) DEFAULT NULL COMMENT '优惠券代码',
  `type` enum('newuser','full_reduction') NOT NULL COMMENT '类型：newuser新人券，full_reduction满减券',
  `discount_type` enum('fixed','percent') NOT NULL COMMENT '优惠类型：fixed固定金额，percent百分比',
  `discount_value` decimal(10,2) NOT NULL COMMENT '优惠值',
  `min_amount` decimal(10,2) DEFAULT 0.00 COMMENT '最低消费金额',
  `max_discount` decimal(10,2) DEFAULT NULL COMMENT '最大优惠金额(百分比券用)',
  `total_quantity` int(11) DEFAULT -1 COMMENT '发放总数量，-1为无限制',
  `per_user_limit` int(11) DEFAULT 1 COMMENT '每用户限领数量',
  `start_time` datetime NOT NULL COMMENT '有效期开始时间',
  `end_time` datetime NOT NULL COMMENT '有效期结束时间',
  `valid_days` int(11) DEFAULT NULL COMMENT '领取后有效天数，NULL表示使用固定时间',
  `status` enum('active','disabled','expired') DEFAULT 'active' COMMENT '状态',
  `description` text COMMENT '使用说明',
  `auto_distribute` tinyint(1) DEFAULT 0 COMMENT '是否自动发放给新用户',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` tinyint(1) DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_type_status` (`type`, `status`),
  KEY `idx_time` (`start_time`, `end_time`),
  KEY `idx_auto_distribute` (`auto_distribute`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='优惠券模板表';

-- ----------------------------
-- Table structure for hiolabs_user_coupons
-- ----------------------------
DROP TABLE IF EXISTS `hiolabs_user_coupons`;
CREATE TABLE `hiolabs_user_coupons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `coupon_id` int(11) NOT NULL COMMENT '优惠券模板ID',
  `coupon_code` varchar(50) NOT NULL COMMENT '优惠券唯一码',
  `status` enum('unused','used','expired') DEFAULT 'unused' COMMENT '状态',
  `received_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
  `used_at` timestamp NULL COMMENT '使用时间',
  `expire_at` timestamp NOT NULL COMMENT '过期时间',
  `order_id` int(11) DEFAULT NULL COMMENT '使用的订单ID',
  `source` enum('manual','auto','batch') DEFAULT 'manual' COMMENT '获取来源：manual手动领取，auto自动发放，batch批量发放',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_coupon_code` (`coupon_code`),
  KEY `idx_user_status` (`user_id`, `status`),
  KEY `idx_coupon_id` (`coupon_id`),
  KEY `idx_expire` (`expire_at`),
  KEY `idx_order` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户优惠券表';

-- ----------------------------
-- Table structure for hiolabs_coupon_usage_logs
-- ----------------------------
DROP TABLE IF EXISTS `hiolabs_coupon_usage_logs`;
CREATE TABLE `hiolabs_coupon_usage_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `coupon_id` int(11) NOT NULL,
  `user_coupon_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `original_amount` decimal(10,2) NOT NULL COMMENT '原订单金额',
  `discount_amount` decimal(10,2) NOT NULL COMMENT '优惠金额',
  `final_amount` decimal(10,2) NOT NULL COMMENT '最终金额',
  `used_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_order` (`user_id`, `order_id`),
  KEY `idx_coupon` (`coupon_id`),
  KEY `idx_user_coupon` (`user_coupon_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='优惠券使用记录表';

-- ----------------------------
-- 为现有用户表添加新人标识字段
-- ----------------------------
ALTER TABLE `hiolabs_user` ADD COLUMN `is_new_user` tinyint(1) DEFAULT 1 COMMENT '是否新用户，1是0否';

-- ----------------------------
-- 为现有订单表添加优惠券相关字段
-- ----------------------------
ALTER TABLE `hiolabs_order` ADD COLUMN `user_coupon_id` int(11) DEFAULT NULL COMMENT '使用的用户优惠券ID';
ALTER TABLE `hiolabs_order` ADD COLUMN `discount_amount` decimal(10,2) DEFAULT 0.00 COMMENT '优惠金额';
ALTER TABLE `hiolabs_order` ADD COLUMN `original_amount` decimal(10,2) DEFAULT 0.00 COMMENT '原始金额';

-- ----------------------------
-- 初始化一些示例优惠券
-- ----------------------------
INSERT INTO `hiolabs_coupons` (`name`, `code`, `type`, `discount_type`, `discount_value`, `min_amount`, `total_quantity`, `per_user_limit`, `start_time`, `end_time`, `valid_days`, `status`, `description`, `auto_distribute`) VALUES
('新用户专享券', 'NEW2024', 'newuser', 'fixed', 20.00, 100.00, -1, 1, '2024-01-01 00:00:00', '2024-12-31 23:59:59', 30, 'active', '新用户注册即可领取，满100元减20元', 1),
('满减优惠券50', 'SAVE50', 'full_reduction', 'fixed', 50.00, 299.00, 1000, 2, '2024-01-01 00:00:00', '2024-12-31 23:59:59', NULL, 'active', '满299元减50元，限量1000张', 0),
('满减优惠券100', 'SAVE100', 'full_reduction', 'fixed', 100.00, 599.00, 500, 1, '2024-01-01 00:00:00', '2024-12-31 23:59:59', NULL, 'active', '满599元减100元，限量500张', 0),
('九折优惠券', 'DISCOUNT10', 'full_reduction', 'percent', 10.00, 200.00, 2000, 3, '2024-01-01 00:00:00', '2024-12-31 23:59:59', 7, 'active', '满200元享9折优惠，最高优惠50元', 0);

-- 更新九折优惠券的最大优惠金额
UPDATE `hiolabs_coupons` SET `max_discount` = 50.00 WHERE `code` = 'DISCOUNT10';

SET FOREIGN_KEY_CHECKS = 1;
