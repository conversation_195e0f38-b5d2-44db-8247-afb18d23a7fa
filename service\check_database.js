const mysql = require('mysql2/promise');

async function checkDatabase() {
    const connection = await mysql.createConnection({
        host: '127.0.0.1',
        port: 3306,
        user: 'root',
        password: '19841020',
        database: 'hiolabsDB'
    });

    try {
        console.log('=== 连接数据库成功 ===');
        
        // 查看所有表
        console.log('\n=== 数据库中的所有表 ===');
        const [tables] = await connection.execute('SHOW TABLES');
        tables.forEach(table => {
            console.log(Object.values(table)[0]);
        });

        // 检查用户表结构
        console.log('\n=== hiolabs_user 表结构 ===');
        try {
            const [userColumns] = await connection.execute('DESCRIBE hiolabs_user');
            userColumns.forEach(col => {
                console.log(`${col.Field} - ${col.Type} - ${col.Null} - ${col.Key} - ${col.Default}`);
            });
        } catch (error) {
            console.log('hiolabs_user 表不存在');
        }

        // 检查订单表结构
        console.log('\n=== hiolabs_order 表结构 ===');
        try {
            const [orderColumns] = await connection.execute('DESCRIBE hiolabs_order');
            orderColumns.forEach(col => {
                console.log(`${col.Field} - ${col.Type} - ${col.Null} - ${col.Key} - ${col.Default}`);
            });
        } catch (error) {
            console.log('hiolabs_order 表不存在');
        }

        // 检查商品表结构
        console.log('\n=== hiolabs_goods 表结构 ===');
        try {
            const [goodsColumns] = await connection.execute('DESCRIBE hiolabs_goods');
            goodsColumns.forEach(col => {
                console.log(`${col.Field} - ${col.Type} - ${col.Null} - ${col.Key} - ${col.Default}`);
            });
        } catch (error) {
            console.log('hiolabs_goods 表不存在');
        }

        // 检查是否已有分享记录表
        console.log('\n=== 检查分享记录相关表 ===');
        try {
            const [shareRecords] = await connection.execute('DESCRIBE hiolabs_share_records');
            console.log('hiolabs_share_records 表已存在:');
            shareRecords.forEach(col => {
                console.log(`${col.Field} - ${col.Type} - ${col.Null} - ${col.Key} - ${col.Default}`);
            });
        } catch (error) {
            console.log('hiolabs_share_records 表不存在，需要创建');
        }

        try {
            const [shareStats] = await connection.execute('DESCRIBE hiolabs_share_stats');
            console.log('hiolabs_share_stats 表已存在:');
            shareStats.forEach(col => {
                console.log(`${col.Field} - ${col.Type} - ${col.Null} - ${col.Key} - ${col.Default}`);
            });
        } catch (error) {
            console.log('hiolabs_share_stats 表不存在，需要创建');
        }

        // 检查订单表是否有分享相关字段
        console.log('\n=== 检查订单表分享字段 ===');
        try {
            const [orderColumns] = await connection.execute('DESCRIBE hiolabs_order');
            const hasShareRecordId = orderColumns.some(col => col.Field === 'share_record_id');
            const hasSharerUserId = orderColumns.some(col => col.Field === 'sharer_user_id');
            
            console.log('share_record_id 字段:', hasShareRecordId ? '已存在' : '不存在');
            console.log('sharer_user_id 字段:', hasSharerUserId ? '已存在' : '不存在');
        } catch (error) {
            console.log('无法检查订单表字段');
        }

        // 查看用户表示例数据
        console.log('\n=== 用户表示例数据 ===');
        try {
            const [users] = await connection.execute('SELECT id, nickname, mobile FROM hiolabs_user LIMIT 3');
            users.forEach(user => {
                console.log(`ID: ${user.id}, 昵称: ${user.nickname}, 手机: ${user.mobile}`);
            });
        } catch (error) {
            console.log('无法查询用户数据');
        }

        // 查看商品表示例数据
        console.log('\n=== 商品表示例数据 ===');
        try {
            const [goods] = await connection.execute('SELECT id, name, list_pic_url FROM hiolabs_goods LIMIT 3');
            goods.forEach(item => {
                console.log(`ID: ${item.id}, 名称: ${item.name}`);
            });
        } catch (error) {
            console.log('无法查询商品数据');
        }

    } catch (error) {
        console.error('数据库操作失败:', error);
    } finally {
        await connection.end();
        console.log('\n=== 数据库连接已关闭 ===');
    }
}

checkDatabase().catch(console.error);
