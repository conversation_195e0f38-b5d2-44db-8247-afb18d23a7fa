{"_from": "iconv-lite@^0.6.3", "_id": "iconv-lite@0.6.3", "_inBundle": false, "_integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "_location": "/iconv-lite", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "iconv-lite@^0.6.3", "name": "iconv-lite", "escapedName": "iconv-lite", "rawSpec": "^0.6.3", "saveSpec": null, "fetchSpec": "^0.6.3"}, "_requiredBy": ["/mysql2"], "_resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "_shasum": "a52f80bf38da1952eb5c681790719871a1a72501", "_spec": "iconv-lite@^0.6.3", "_where": "D:\\py-ide\\hioshop-miniprogram-master\\node_modules\\mysql2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": {"stream": false}, "bugs": {"url": "https://github.com/ashtuchkin/iconv-lite/issues"}, "bundleDependencies": false, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "deprecated": false, "description": "Convert character encodings in pure javascript.", "devDependencies": {"async": "^3.2.0", "c8": "^7.2.0", "errto": "^0.2.1", "iconv": "^2.3.5", "mocha": "^3.5.3", "request": "^2.88.2", "semver": "^6.3.0", "unorm": "^1.6.0"}, "engines": {"node": ">=0.10.0"}, "homepage": "https://github.com/ashtuchkin/iconv-lite", "keywords": ["iconv", "convert", "charset", "icu"], "license": "MIT", "main": "./lib/index.js", "name": "iconv-lite", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "scripts": {"coverage": "c8 _mocha --grep .", "test": "mocha --reporter spec --grep ."}, "typings": "./lib/index.d.ts", "version": "0.6.3"}