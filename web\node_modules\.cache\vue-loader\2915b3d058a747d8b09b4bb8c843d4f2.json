{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleRoundsPage.vue?vue&type=template&id=52ebf3fc&scoped=true&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleRoundsPage.vue", "mtime": 1753735541601}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}