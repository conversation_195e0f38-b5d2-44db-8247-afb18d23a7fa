var util = require('../../../utils/util.js');
var api = require('../../../config/api.js');
var auth = require('../../../utils/auth.js');

Page({
  data: {
    commissionInfo: {
      totalCommission: '0.00',
      availableCommission: '0.00',
      frozenCommission: '0.00',
      withdrawnCommission: '0.00'
    },
    commissionDetails: [],
    withdrawRecords: [],
    showWithdrawModal: false,
    withdrawAmount: '',
    withdrawMethod: 'wechat'
  },

  onLoad: function (options) {
    // 检查授权
    if (!auth.isFullyAuthorized()) {
      wx.showModal({
        title: '需要登录',
        content: '查看佣金需要先完善个人信息',
        confirmText: '立即设置',
        showCancel: false,
        success: (res) => {
          wx.navigateTo({
            url: '/pages/app-auth/index?from=commission'
          });
        }
      });
      return;
    }

    this.loadCommissionData();
  },

  onShow: function () {
    // 每次显示页面时刷新数据
    if (auth.isFullyAuthorized()) {
      this.loadCommissionData();
    }
  },

  // 加载佣金数据
  loadCommissionData: function() {
    this.getCommissionInfo();
    this.getCommissionDetails();
    this.getWithdrawRecords();
  },

  // 获取佣金信息
  getCommissionInfo: function() {
    let that = this;
    wx.showLoading({ title: '加载中...' });
    
    util.request(api.CommissionInfo).then(function (res) {
      wx.hideLoading();
      if (res.errno === 0) {
        that.setData({
          commissionInfo: res.data.commissionInfo || {
            totalCommission: '0.00',
            availableCommission: '0.00',
            frozenCommission: '0.00',
            withdrawnCommission: '0.00'
          }
        });
      } else {
        wx.showToast({
          title: res.errmsg || '获取佣金信息失败',
          icon: 'none'
        });
      }
    }).catch(function(err) {
      wx.hideLoading();
      console.log('获取佣金信息失败:', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // 获取佣金明细
  getCommissionDetails: function() {
    let that = this;
    
    util.request(api.CommissionInfo).then(function (res) {
      if (res.errno === 0) {
        // 格式化佣金明细数据
        const details = res.data.commissionDetails || [];
        const formattedDetails = details.map(item => {
          return {
            ...item,
            createTime: that.formatTime(item.createTime),
            amount: Math.abs(item.amount).toFixed(2)
          };
        });
        
        that.setData({
          commissionDetails: formattedDetails
        });
      }
    }).catch(function(err) {
      console.log('获取佣金明细失败:', err);
    });
  },

  // 获取提现记录
  getWithdrawRecords: function() {
    let that = this;
    
    util.request(api.CommissionWithdraws, { page: 1, pageSize: 5 }).then(function (res) {
      if (res.errno === 0) {
        const records = res.data.withdraws.data || [];
        const formattedRecords = records.map(item => {
          let statusText = '';
          switch(item.status) {
            case 'pending': statusText = '待处理'; break;
            case 'processing': statusText = '处理中'; break;
            case 'success': statusText = '已到账'; break;
            case 'failed': statusText = '失败'; break;
            default: statusText = '未知';
          }
          
          return {
            ...item,
            applyTime: that.formatTime(item.apply_time),
            statusText: statusText
          };
        });
        
        that.setData({
          withdrawRecords: formattedRecords
        });
      }
    }).catch(function(err) {
      console.log('获取提现记录失败:', err);
    });
  },

  // 显示提现弹窗
  showWithdrawModal: function() {
    if (parseFloat(this.data.commissionInfo.availableCommission) <= 0) {
      wx.showToast({
        title: '暂无可提现佣金',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      showWithdrawModal: true,
      withdrawAmount: ''
    });
  },

  // 隐藏提现弹窗
  hideWithdrawModal: function() {
    this.setData({
      showWithdrawModal: false,
      withdrawAmount: ''
    });
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 阻止点击弹窗内容时关闭弹窗
  },

  // 提现金额输入
  onWithdrawAmountInput: function(e) {
    this.setData({
      withdrawAmount: e.detail.value
    });
  },

  // 选择提现方式
  selectWithdrawMethod: function(e) {
    this.setData({
      withdrawMethod: e.currentTarget.dataset.method
    });
  },

  // 确认提现
  confirmWithdraw: function() {
    const amount = parseFloat(this.data.withdrawAmount);
    const availableAmount = parseFloat(this.data.commissionInfo.availableCommission);
    
    if (!amount || amount <= 0) {
      wx.showToast({
        title: '请输入有效的提现金额',
        icon: 'none'
      });
      return;
    }
    
    if (amount < 1) {
      wx.showToast({
        title: '提现金额最低1元',
        icon: 'none'
      });
      return;
    }
    
    if (amount > availableAmount) {
      wx.showToast({
        title: '提现金额超过可用余额',
        icon: 'none'
      });
      return;
    }
    
    let that = this;
    wx.showLoading({ title: '提交中...' });
    
    util.request(api.CommissionWithdraw, {
      amount: amount,
      method: this.data.withdrawMethod
    }, 'POST').then(function (res) {
      wx.hideLoading();
      if (res.errno === 0) {
        wx.showToast({
          title: '提现申请成功',
          icon: 'success'
        });
        
        that.hideWithdrawModal();
        that.loadCommissionData(); // 刷新数据
      } else {
        wx.showToast({
          title: res.errmsg || '提现申请失败',
          icon: 'none'
        });
      }
    }).catch(function(err) {
      wx.hideLoading();
      console.log('提现申请失败:', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // 跳转到提现记录页面
  toWithdrawRecords: function() {
    wx.navigateTo({
      url: '/pages/ucenter/withdraw-records/index'
    });
  },

  // 显示筛选弹窗
  showFilterModal: function() {
    wx.showToast({
      title: '筛选功能开发中',
      icon: 'none'
    });
  },

  // 格式化时间
  formatTime: function(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp * 1000);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadCommissionData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  }
});
