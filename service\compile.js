const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');

// 跨平台路径处理
const ROOT_PATH = __dirname;
const APP_PATH = path.join(ROOT_PATH, 'app');
const SRC_PATH = path.join(ROOT_PATH, 'src');

console.log('开始编译项目...');
console.log('源码路径:', SRC_PATH);
console.log('输出路径:', APP_PATH);

try {
  // 检查源码目录是否存在
  if (!fs.existsSync(SRC_PATH)) {
    console.error('源码目录不存在:', SRC_PATH);
    process.exit(1);
  }

  // 创建输出目录（如果不存在）
  if (!fs.existsSync(APP_PATH)) {
    fs.mkdirSync(APP_PATH, { recursive: true });
    console.log('创建输出目录:', APP_PATH);
  }

  // 执行babel编译
  const babelCmd = `npx babel --no-babelrc src/ --presets think-node --out-dir app/`;
  console.log('执行编译命令:', babelCmd);
  
  execSync(babelCmd, { 
    stdio: 'inherit',
    cwd: ROOT_PATH
  });

  console.log('编译完成！');

  // 验证关键文件是否编译成功
  const authControllerPath = path.join(APP_PATH, 'admin', 'controller', 'auth.js');
  if (fs.existsSync(authControllerPath)) {
    console.log('✓ auth.js 编译成功');
  } else {
    console.error('✗ auth.js 编译失败');
  }

  const baseControllerPath = path.join(APP_PATH, 'admin', 'controller', 'base.js');
  if (fs.existsSync(baseControllerPath)) {
    console.log('✓ base.js 编译成功');
  } else {
    console.error('✗ base.js 编译失败');
  }

} catch (error) {
  console.error('编译失败:', error.message);
  process.exit(1);
}
