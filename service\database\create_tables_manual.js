const mysql = require('mysql2/promise');

async function createTables() {
  try {
    const connection = await mysql.createConnection({
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: '19841020',
      database: 'hiolabsDB'
    });

    console.log('✅ 数据库连接成功');

    // 1. 创建轮次表
    const createRoundsSQL = `
    CREATE TABLE IF NOT EXISTS hiolabs_flash_sale_rounds (
      id int(11) NOT NULL AUTO_INCREMENT COMMENT '轮次ID',
      round_number int(11) NOT NULL COMMENT '轮次编号（全局递增）',
      round_name varchar(100) DEFAULT NULL COMMENT '轮次名称',
      start_time datetime NOT NULL COMMENT '开始时间',
      end_time datetime NOT NULL COMMENT '结束时间',
      status enum('upcoming','active','ended','cancelled') DEFAULT 'upcoming' COMMENT '状态：即将开始，进行中，已结束，已取消',
      created_by int(11) DEFAULT 0 COMMENT '创建者ID',
      created_at datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (id),
      UNIQUE KEY uk_round_number (round_number),
      KEY idx_status (status),
      KEY idx_start_time (start_time),
      KEY idx_end_time (end_time),
      KEY idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀轮次表'`;

    console.log('创建轮次表...');
    await connection.execute(createRoundsSQL);
    console.log('✅ 轮次表创建成功');

    // 2. 创建轮次商品表
    const createRoundGoodsSQL = `
    CREATE TABLE IF NOT EXISTS \`hiolabs_flash_sale_round_goods\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
      \`round_id\` int(11) NOT NULL COMMENT '轮次ID',
      \`goods_id\` int(11) NOT NULL COMMENT '商品ID',
      \`goods_name\` varchar(200) NOT NULL COMMENT '商品名称（冗余字段）',
      \`goods_image\` varchar(500) DEFAULT NULL COMMENT '商品图片（冗余字段）',
      \`original_price\` decimal(10,2) NOT NULL COMMENT '商品原价',
      \`flash_price\` decimal(10,2) NOT NULL COMMENT '秒杀价格',
      \`discount_rate\` decimal(5,2) DEFAULT NULL COMMENT '折扣率（%）',
      \`stock\` int(11) NOT NULL COMMENT '本轮库存数量',
      \`sold_count\` int(11) DEFAULT 0 COMMENT '已售数量',
      \`limit_quantity\` int(11) DEFAULT 1 COMMENT '每人限购数量',
      \`sort_order\` int(11) DEFAULT 0 COMMENT '排序',
      \`created_at\` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      PRIMARY KEY (\`id\`),
      UNIQUE KEY \`uk_round_goods\` (\`round_id\`, \`goods_id\`),
      KEY \`idx_round_id\` (\`round_id\`),
      KEY \`idx_goods_id\` (\`goods_id\`),
      KEY \`idx_sort_order\` (\`sort_order\`),
      FOREIGN KEY (\`round_id\`) REFERENCES \`hiolabs_flash_sale_rounds\`(\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀轮次商品表'`;

    console.log('创建轮次商品表...');
    await connection.execute(createRoundGoodsSQL);
    console.log('✅ 轮次商品表创建成功');

    // 3. 创建订单表
    const createOrdersSQL = `
    CREATE TABLE IF NOT EXISTS \`hiolabs_flash_sale_orders\` (
      \`id\` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
      \`round_id\` int(11) NOT NULL COMMENT '轮次ID',
      \`round_goods_id\` int(11) NOT NULL COMMENT '轮次商品ID',
      \`order_id\` int(11) NOT NULL COMMENT '订单ID',
      \`user_id\` int(11) NOT NULL COMMENT '用户ID',
      \`goods_id\` int(11) NOT NULL COMMENT '商品ID',
      \`quantity\` int(11) NOT NULL COMMENT '购买数量',
      \`flash_price\` decimal(10,2) NOT NULL COMMENT '秒杀价格',
      \`total_amount\` decimal(10,2) NOT NULL COMMENT '总金额',
      \`created_at\` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      PRIMARY KEY (\`id\`),
      KEY \`idx_round_id\` (\`round_id\`),
      KEY \`idx_round_goods_id\` (\`round_goods_id\`),
      KEY \`idx_order_id\` (\`order_id\`),
      KEY \`idx_user_id\` (\`user_id\`),
      KEY \`idx_goods_id\` (\`goods_id\`),
      KEY \`idx_created_at\` (\`created_at\`),
      FOREIGN KEY (\`round_id\`) REFERENCES \`hiolabs_flash_sale_rounds\`(\`id\`) ON DELETE CASCADE,
      FOREIGN KEY (\`round_goods_id\`) REFERENCES \`hiolabs_flash_sale_round_goods\`(\`id\`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀订单记录表'`;

    console.log('创建订单表...');
    await connection.execute(createOrdersSQL);
    console.log('✅ 订单表创建成功');

    console.log('✅ 所有表创建完成');

    await connection.end();
    console.log('✅ 数据库连接已关闭');
    
  } catch (error) {
    console.error('❌ 创建表失败:', error.message);
    process.exit(1);
  }
}

createTables();
