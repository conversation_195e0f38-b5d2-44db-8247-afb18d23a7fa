module.exports = class extends think.Controller {
  async __before() {
    // 根据token值获取用户id
    think.token = this.ctx.header['x-hioshop-token'] || this.ctx.header['X-Hioshop-Token'] || '';
    console.log('=== Token验证开始 ===');
    console.log('Token:', think.token);

    const tokenSerivce = think.service('token', 'admin');
    think.userId = await tokenSerivce.getUserId();

    console.log('用户ID:', think.userId);

    // 只允许登录操作
    if (this.ctx.controller != 'auth') {
      if (think.userId <= 0 || think.userId == undefined) {
        console.log('Token验证失败，用户未登录');
        return this.fail(401, '请先登录');
      }
    }
    console.log('Token验证通过');
  }
};