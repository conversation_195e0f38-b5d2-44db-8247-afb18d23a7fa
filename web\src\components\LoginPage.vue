<template>
  <div class="login">
    <!-- 背景装饰元素 -->
    <div class="background-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
    </div>

    <div class="login-container">
      <div class="login-box">
        <!-- Logo区域 -->
        <div class="logo-section">
          <div class="logo-wrapper">
            <img src="static/images/loading.gif" alt="Logo" class="logo-img" />
          </div>
          <h1 class="brand-title">美汐缘</h1>
          <p class="brand-subtitle">欢迎回来</p>
        </div>

        <!-- 登录方式切换 -->
        <div class="login-tabs">
          <div class="tab-buttons">
            <button
              :class="['tab-button', { active: loginType === 'password' }]"
              @click="switchLoginType('password')"
            >
              <i class="el-icon-user"></i>
              账号登录
            </button>
            <button
              :class="['tab-button', { active: loginType === 'qrcode' }]"
              @click="switchLoginType('qrcode')"
            >
              <i class="el-icon-mobile-phone"></i>
              扫码登录
            </button>
          </div>
        </div>

        <!-- 表单区域 -->
        <div class="form-section">
          <!-- 账号密码登录 -->
          <div v-if="loginType === 'password'" class="password-login">
            <el-form ref="form" :model="form" :rules="rules" label-position="top" class="login-form">
              <el-form-item label="" prop="username" class="form-item">
                <div class="input-wrapper">
                  <i class="el-icon-user input-icon"></i>
                  <el-input
                    v-model="form.username"
                    placeholder="请输入用户名"
                    class="custom-input"
                    size="large"
                  ></el-input>
                </div>
              </el-form-item>

              <el-form-item label="" prop="password" class="form-item">
                <div class="input-wrapper">
                  <i class="el-icon-lock input-icon"></i>
                  <el-input
                    type="password"
                    v-model="form.password"
                    placeholder="请输入密码"
                    @keyup.enter.native="startLogin"
                    class="custom-input"
                    size="large"
                    show-password
                  ></el-input>
                </div>
              </el-form-item>

              <el-form-item class="form-item">
                <el-button
                  type="primary"
                  @click="startLogin"
                  :disabled="loading"
                  class="login-button"
                  size="large"
                >
                  <span v-if="!loading">
                    <i class="el-icon-right"></i>
                    立即登录
                  </span>
                  <span v-else>
                    <i class="el-icon-loading"></i>
                    登录中...
                  </span>
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 二维码登录 -->
          <div v-if="loginType === 'qrcode'" class="qrcode-login">
            <div class="qrcode-container">
              <div v-if="qrCodeLoading" class="qrcode-loading">
                <i class="el-icon-loading"></i>
                <p>生成二维码中...</p>
              </div>
              <div v-else-if="qrCodeData" class="qrcode-content">
                <div class="qrcode-wrapper">
                  <canvas ref="qrCanvas" class="qrcode-canvas"></canvas>
                </div>
                <div class="qrcode-info">
                  <h4>使用小程序扫码登录</h4>
                  <p>打开美汐缘小程序，扫描上方二维码</p>
                  <div class="qrcode-status">
                    <span v-if="qrStatus === 'pending'" class="status-pending">
                      <i class="el-icon-time"></i>
                      等待扫码...
                    </span>
                    <span v-else-if="qrStatus === 'scanned'" class="status-scanned">
                      <i class="el-icon-success"></i>
                      扫码成功，请在手机上确认
                    </span>
                    <span v-else-if="qrStatus === 'expired'" class="status-expired">
                      <i class="el-icon-warning"></i>
                      二维码已过期
                    </span>
                  </div>
                </div>
              </div>
              <div v-else class="qrcode-error">
                <i class="el-icon-warning"></i>
                <p>二维码生成失败</p>
              </div>
            </div>
            <div class="qrcode-actions">
              <el-button
                @click="refreshQrCode"
                :loading="qrCodeLoading"
                type="text"
                class="refresh-button"
              >
                <i class="el-icon-refresh"></i>
                刷新二维码
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import api from "@/config/api";
import QRCode from 'qrcode';

export default {
  data() {
    return {
      root: "",
      loginType: "password", // password 或 qrcode
      form: {
        username: "",
        password: "",
      },
      rules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          { min: 6, message: "密码不得低于6个字符", trigger: "blur" },
        ],
      },
      loading: false,
      // 二维码相关
      qrCodeLoading: false,
      qrCodeData: null,
      qrStatus: 'pending', // pending, scanned, expired
      qrCheckTimer: null,
    };
  },
  components: {},
  methods: {
    // 切换登录方式
    switchLoginType(type) {
      this.loginType = type;
      if (type === 'qrcode') {
        this.$nextTick(() => {
          this.generateQrCode();
        });
      } else {
        this.clearQrTimer();
      }
    },

    // 账号密码登录
    startLogin() {
      this.$refs["form"].validate((valid) => {
        if (!valid) {
          return false;
        }
        this.loading = true;
        let root = this.root;
        this.axios
          .post(root + "auth/login", {
            username: this.form.username,
            password: this.form.password,
          })
          .then((res) => {
            this.loading = false;
            let call = res.data;
            if (call.errno === 0) {
              const token = call.data.token;
              const userInfo = call.data.userInfo;

              localStorage.setItem("token", token);
              localStorage.setItem("userInfo", JSON.stringify(userInfo));
              this.axios.defaults.headers.common["X-Hioshop-Token"] = token;

              console.log("登录成功，导航到欢迎页面");
              this.redirectAfterLogin(userInfo);
            } else {
              console.error("登录失败:", call);
              this.$message({
                type: "error",
                message: call.errmsg + " (错误码: " + call.errno + ")",
              });
            }
          })
          .catch((err) => {
            this.loading = false;
            console.error("登录请求失败:", err);
            this.$message.error("登录请求失败，请检查网络连接");
          });
      });
    },

    // 生成二维码
    async generateQrCode() {
      this.qrCodeLoading = true;
      this.qrStatus = 'pending';

      try {
        const response = await this.axios.get(this.root + 'auth/generateQrCode');
        if (response.data.errno === 0) {
          this.qrCodeData = response.data.data;
          this.qrCodeLoading = false; // 先关闭loading状态

          // 使用setTimeout确保DOM完全更新
          setTimeout(async () => {
            await this.renderQrCode(this.qrCodeData.qrContent);
          }, 100);

          this.startQrStatusCheck();
        } else {
          this.$message.error('生成二维码失败: ' + response.data.errmsg);
          this.qrCodeLoading = false;
        }
      } catch (error) {
        console.error('生成二维码失败:', error);
        this.$message.error('生成二维码失败，请重试');
        this.qrCodeLoading = false;
      }
    },

    // 渲染二维码到canvas
    async renderQrCode(content) {
      try {
        console.log('开始渲染二维码，内容:', content);
        console.log('当前loginType:', this.loginType);
        console.log('qrCodeData:', this.qrCodeData);
        console.log('qrCodeLoading:', this.qrCodeLoading);

        const canvas = this.$refs.qrCanvas;
        console.log('Canvas元素:', canvas);
        console.log('所有refs:', this.$refs);

        if (canvas) {
          await QRCode.toCanvas(canvas, content, {
            width: 200,
            margin: 2,
            color: {
              dark: '#000000',
              light: '#FFFFFF'
            }
          });
          console.log('二维码渲染成功');
        } else {
          console.error('Canvas元素未找到');
          // 尝试通过querySelector查找
          const canvasElement = document.querySelector('.qrcode-canvas');
          console.log('通过querySelector找到的canvas:', canvasElement);
        }
      } catch (error) {
        console.error('渲染二维码失败:', error);
      }
    },

    // 开始检查二维码状态
    startQrStatusCheck() {
      this.clearQrTimer();
      this.qrCheckTimer = setInterval(async () => {
        try {
          const response = await this.axios.get(
            this.root + 'auth/checkQrStatus',
            { params: { token: this.qrCodeData.qrToken } }
          );

          if (response.data.errno === 0) {
            const result = response.data.data;
            if (result.status === 'success') {
              this.clearQrTimer();
              this.handleQrLoginSuccess(result);
            } else if (result.status === 'expired') {
              this.clearQrTimer();
              this.qrStatus = 'expired';
              this.$message.warning('二维码已过期，请刷新');
            }
          }
        } catch (error) {
          console.error('检查二维码状态失败:', error);
        }
      }, 2000); // 每2秒检查一次
    },

    // 处理二维码登录成功
    handleQrLoginSuccess(result) {
      localStorage.setItem("token", result.token);
      localStorage.setItem("userInfo", JSON.stringify(result.userInfo));
      this.axios.defaults.headers.common["X-Hioshop-Token"] = result.token;

      this.$message.success('登录成功');
      this.redirectAfterLogin(result.userInfo);
    },

    // 登录后跳转
    redirectAfterLogin(userInfo) {
      // 根据用户角色跳转到不同页面
      if (userInfo.role === 'promoter') {
        this.$router.push({ path: "/promotion/personal" });
      } else if (userInfo.role === 'distributor') {
        this.$router.push({ path: "/promotion/team" });
      } else {
        this.$router.push({ path: "/dashboard/welcome" });
      }
    },

    // 刷新二维码
    refreshQrCode() {
      this.generateQrCode();
    },

    // 清除定时器
    clearQrTimer() {
      if (this.qrCheckTimer) {
        clearInterval(this.qrCheckTimer);
        this.qrCheckTimer = null;
      }
    }
  },
  mounted() {
    this.root = api.rootUrl;
  },
  beforeDestroy() {
    this.clearQrTimer();
  },
};
</script>
<style scoped>
/* 主容器 */
.login {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* 登录容器 */
.login-container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 420px;
  padding: 20px;
}

/* 登录卡片 */
.login-box {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;
}

.login-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
}

/* Logo区域 */
.logo-section {
  padding: 40px 40px 20px;
  text-align: center;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

.logo-wrapper {
  margin-bottom: 20px;
}

.logo-img {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
  transition: transform 0.3s ease;
}

.logo-img:hover {
  transform: scale(1.1) rotate(5deg);
}

.brand-title {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
  font-weight: 400;
}

/* 登录方式切换 */
.login-tabs {
  padding: 0 40px 20px;
}

.tab-buttons {
  display: flex;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 4px;
}

.tab-button {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.tab-button.active {
  background: white;
  color: #667eea;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-button:hover:not(.active) {
  color: #333;
}

/* 表单区域 */
.form-section {
  padding: 20px 40px 40px;
}

.login-form {
  width: 100%;
}

.form-item {
  margin-bottom: 24px;
}

.form-item:last-child {
  margin-bottom: 0;
}

/* 输入框样式 */
.input-wrapper {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #a0aec0;
  font-size: 18px;
  z-index: 3;
  transition: color 0.3s ease;
}

.input-wrapper:focus-within .input-icon {
  color: #667eea;
}

/* 自定义输入框 */
.custom-input {
  width: 100%;
}

.custom-input >>> .el-input__inner {
  height: 52px;
  padding-left: 48px;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.custom-input >>> .el-input__inner:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: rgba(255, 255, 255, 1);
}

.custom-input >>> .el-input__inner::placeholder {
  color: #a0aec0;
  font-weight: 400;
}

/* 密码显示按钮 */
.custom-input >>> .el-input__suffix {
  right: 16px;
}

.custom-input >>> .el-input__suffix-inner {
  color: #a0aec0;
  transition: color 0.3s ease;
}

.custom-input >>> .el-input__suffix-inner:hover {
  color: #667eea;
}

/* 登录按钮 */
.login-button {
  width: 100%;
  height: 52px;
  border-radius: 16px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-button:hover:not(.is-disabled) {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
}

.login-button:active:not(.is-disabled) {
  transform: translateY(0);
}

/* 禁用状态样式 */
.login-button.is-disabled {
  opacity: 0.8;
  cursor: not-allowed;
  transform: none;
}

/* 二维码登录样式 */
.qrcode-login {
  text-align: center;
}

.qrcode-container {
  margin-bottom: 20px;
}

.qrcode-loading {
  padding: 60px 20px;
  color: #666;
}

.qrcode-loading i {
  font-size: 24px;
  margin-bottom: 10px;
  display: block;
}

.qrcode-content {
  padding: 20px;
}

.qrcode-wrapper {
  display: inline-block;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.qrcode-canvas {
  display: block;
}

.qrcode-info h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

.qrcode-info p {
  margin: 0 0 16px 0;
  color: #666;
  font-size: 14px;
}

.qrcode-status {
  font-size: 14px;
}

.status-pending {
  color: #409EFF;
}

.status-scanned {
  color: #67C23A;
}

.status-expired {
  color: #F56C6C;
}

.qrcode-error {
  padding: 60px 20px;
  color: #F56C6C;
}

.qrcode-error i {
  font-size: 24px;
  margin-bottom: 10px;
  display: block;
}

.qrcode-actions {
  text-align: center;
}

.refresh-button {
  color: #667eea !important;
  font-size: 14px;
}

.refresh-button:hover {
  color: #5a6fd8 !important;
}

.login-button >>> .el-button {
  background: transparent;
  border: none;
  color: white;
  height: 100%;
  width: 100%;
}

.login-button >>> .el-button:hover {
  background: transparent;
  border: none;
}

.login-button >>> .el-button:focus {
  background: transparent;
  border: none;
}

.login-button >>> .el-button span {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.login-button >>> .el-button i {
  font-size: 16px;
}

/* 表单验证错误样式 */
.form-item >>> .el-form-item__error {
  color: #e53e3e;
  font-size: 14px;
  margin-top: 8px;
  padding-left: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    max-width: 100%;
    padding: 16px;
  }

  .login-box {
    border-radius: 20px;
  }

  .logo-section {
    padding: 32px 24px 16px;
  }

  .form-section {
    padding: 16px 24px 32px;
  }

  .brand-title {
    font-size: 24px;
  }

  .brand-subtitle {
    font-size: 14px;
  }

  .floating-shape {
    display: none;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 12px;
  }

  .logo-section {
    padding: 24px 20px 12px;
  }

  .form-section {
    padding: 12px 20px 24px;
  }

  .logo-img {
    width: 56px;
    height: 56px;
  }

  .brand-title {
    font-size: 22px;
  }

  .custom-input >>> .el-input__inner {
    height: 48px;
    font-size: 15px;
  }

  .login-button {
    height: 48px;
    font-size: 15px;
  }
}

/* 加载动画优化 */
.login-button >>> .el-icon-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 表单项动画 */
.form-item {
  animation: slideInUp 0.6s ease-out;
}

.form-item:nth-child(1) {
  animation-delay: 0.1s;
}

.form-item:nth-child(2) {
  animation-delay: 0.2s;
}

.form-item:nth-child(3) {
  animation-delay: 0.3s;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Logo区域动画 */
.logo-section {
  animation: fadeInDown 0.8s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
