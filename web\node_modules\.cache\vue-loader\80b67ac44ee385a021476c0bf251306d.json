{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleMultiPage.vue?vue&type=template&id=c8ad4aac&scoped=true&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleMultiPage.vue", "mtime": 1753847480671}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}