{"version": 3, "sources": ["..\\..\\..\\src\\common\\service\\points.js"], "names": ["module", "exports", "think", "Service", "addCommission", "userId", "commission", "type", "description", "sourceId", "console", "log", "Error", "userCommission", "model", "where", "user_id", "find", "isEmpty", "total_commission", "available_commission", "frozen_commission", "withdrawn_commission", "add", "newTotalCommission", "parseFloat", "newAvailableCommission", "update", "toFixed", "updated_at", "Date", "commission_change", "commission_type", "source_id", "balance_after", "status", "created_at", "success", "oldBalance", "newBalance", "commissionAdded", "error", "addPromotionCommission", "orderId", "result", "updatePromoterCommissionStats", "distributeMultiLevelCommission", "commissionData", "goodsId", "goodsName", "orderAmount", "promoterUserId", "level1UserId", "level2UserId", "teamLeaderUserId", "distributionRates", "getDistributionCommissionRates", "personal_rate", "level1_rate", "level2_rate", "team_leader_rate", "results", "personalCommission", "push", "level", "rate", "level1Commission", "level2Commission", "teamLeaderCommission", "length", "promoter", "newOrderCount", "parseInt", "order_count", "last_order_time", "getTime", "update_time", "addUpgradeBonus", "newLevel", "bonusCommission", "updatePromoterStats", "points", "currentTime", "currentMonth", "getMonth", "currentYear", "getFullYear", "newTotalPoints", "promoterUpdateTime", "isCurrentMonth", "newMonthPoints", "month_commission", "checkPromoterLevelUp", "currentLevel", "totalOrders", "total_orders", "levels", "order", "select", "bonusPoints", "min_orders", "max_orders", "bonus_points", "upgraded", "getUserPoints", "userPoints", "total_points", "available_points", "used_points", "distributionConfig", "goods_id", "is_distributed", "formatDistributionRates", "addShareCommissionWithParent", "parentUserId", "shareSource", "message", "orderInfo", "id", "goodsInfo", "promoter_id", "promoter_user_id", "parent_promoter_user_id", "buyer_user_id", "order_id", "order_sn", "goods_name", "name", "goods_price", "shop_price", "order_amount", "commission_rate", "commission_amount", "personal_commission", "level1_commission", "level2_commission", "team_leader_commission", "share_source", "commission_status", "is_first_order", "settle_time", "withdraw_time", "refund_time", "create_time", "parentCommission", "addShareCommission", "commissionRate", "commissionAmount", "commission_source", "pending", "amount", "commissionService", "service", "settleCommission", "increment"], "mappings": ";;AAAA;;;;;AAKAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,OAApB,CAA4B;;AAE3C;;;;;;;;AAQMC,eAAN,CAAoBC,MAApB,EAA4BC,UAA5B,EAAwCC,OAAO,WAA/C,EAA4DC,cAAc,EAA1E,EAA8EC,WAAW,IAAzF,EAA+F;AAAA;;AAAA;AAC7F,UAAI;AACFC,gBAAQC,GAAR,CAAa,WAAUN,MAAO,OAAMC,UAAW,UAA/C;AACAI,gBAAQC,GAAR,CAAY,KAAZ,EAAmBJ,IAAnB,EAAyB,KAAzB,EAAgCC,WAAhC;;AAEA;AACA,YAAI,CAACH,MAAD,IAAWC,cAAc,CAA7B,EAAgC;AAC9B,gBAAM,IAAIM,KAAJ,CAAU,eAAV,CAAN;AACD;;AAED;AACA,YAAIC,iBAAiB,MAAM,MAAKC,KAAL,CAAW,iBAAX,EAA8BC,KAA9B,CAAoC,EAAEC,SAASX,MAAX,EAApC,EAAyDY,IAAzD,EAA3B;;AAEA,YAAIf,MAAMgB,OAAN,CAAcL,cAAd,CAAJ,EAAmC;AACjC;AACAA,2BAAiB;AACfG,qBAASX,MADM;AAEfc,8BAAkB,CAFH;AAGfC,kCAAsB,CAHP;AAIfC,+BAAmB,CAJJ;AAKfC,kCAAsB;AALP,WAAjB;AAOA,gBAAM,MAAKR,KAAL,CAAW,iBAAX,EAA8BS,GAA9B,CAAkCV,cAAlC,CAAN;AACAA,2BAAiB,MAAM,MAAKC,KAAL,CAAW,iBAAX,EAA8BC,KAA9B,CAAoC,EAAEC,SAASX,MAAX,EAApC,EAAyDY,IAAzD,EAAvB;AACD;;AAED;AACA,cAAMO,qBAAqBC,WAAWZ,eAAeM,gBAA1B,IAA8CM,WAAWnB,UAAX,CAAzE;AACA,cAAMoB,yBAAyBD,WAAWZ,eAAeO,oBAA1B,IAAkDK,WAAWnB,UAAX,CAAjF;;AAEA;AACA,cAAM,MAAKQ,KAAL,CAAW,iBAAX,EAA8BC,KAA9B,CAAoC,EAAEC,SAASX,MAAX,EAApC,EAAyDsB,MAAzD,CAAgE;AACpER,4BAAkBK,mBAAmBI,OAAnB,CAA2B,CAA3B,CADkD;AAEpER,gCAAsBM,uBAAuBE,OAAvB,CAA+B,CAA/B,CAF8C;AAGpEC,sBAAY,IAAIC,IAAJ;AAHwD,SAAhE,CAAN;;AAMA;AACA,cAAM,MAAKhB,KAAL,CAAW,gBAAX,EAA6BS,GAA7B,CAAiC;AACrCP,mBAASX,MAD4B;AAErC0B,6BAAmBN,WAAWnB,UAAX,EAAuBsB,OAAvB,CAA+B,CAA/B,CAFkB;AAGrCI,2BAAiBzB,IAHoB;AAIrC0B,qBAAWxB,QAJ0B;AAKrCD,uBAAaA,WALwB;AAMrC0B,yBAAeR,uBAAuBE,OAAvB,CAA+B,CAA/B,CANsB;AAOrCO,kBAAQ,WAP6B;AAQrCC,sBAAY,IAAIN,IAAJ;AARyB,SAAjC,CAAN;;AAWApB,gBAAQC,GAAR,CAAa,aAAYE,eAAeO,oBAAqB,OAAMM,uBAAuBE,OAAvB,CAA+B,CAA/B,CAAkC,EAArG;;AAEA,eAAO;AACLS,mBAAS,IADJ;AAELC,sBAAYb,WAAWZ,eAAeO,oBAA1B,CAFP;AAGLmB,sBAAYd,WAAWC,uBAAuBE,OAAvB,CAA+B,CAA/B,CAAX,CAHP;AAILY,2BAAiBf,WAAWnB,UAAX;AAJZ,SAAP;AAOD,OAzDD,CAyDE,OAAOmC,KAAP,EAAc;AACd/B,gBAAQ+B,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,cAAMA,KAAN;AACD;AA7D4F;AA8D9F;;AAED;;;;;;;AAOMC,wBAAN,CAA6BrC,MAA7B,EAAqCC,UAArC,EAAiDqC,UAAU,IAA3D,EAAiEnC,cAAc,MAA/E,EAAuF;AAAA;;AAAA;AACrF,UAAI;AACFE,gBAAQC,GAAR,CAAa,WAAUN,MAAO,WAAUC,UAAW,QAAnD;;AAEA;AACA,cAAMsC,SAAS,MAAM,OAAKxC,aAAL,CAAmBC,MAAnB,EAA2BC,UAA3B,EAAuC,WAAvC,EAAoDE,WAApD,EAAiEmC,OAAjE,CAArB;;AAEA;AACA,cAAM,OAAKE,6BAAL,CAAmCxC,MAAnC,EAA2CC,UAA3C,CAAN;;AAEA,eAAOsC,MAAP;AAED,OAXD,CAWE,OAAOH,KAAP,EAAc;AACd/B,gBAAQ+B,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,cAAMA,KAAN;AACD;AAfoF;AAgBtF;;AAED;;;;AAIMK,gCAAN,CAAqCC,cAArC,EAAqD;AAAA;;AAAA;AACnD,UAAI;AACF,cAAM;AACJJ,iBADI;AAEJK,iBAFI;AAGJC,mBAHI;AAIJC,qBAJI;AAKJC,wBALI;AAMJC,yBAAe,IANX;AAOJC,yBAAe,IAPX;AAQJC,6BAAmB;AARf,YASFP,cATJ;;AAWArC,gBAAQC,GAAR,CAAY,6BAAZ;AACAD,gBAAQC,GAAR,CAAY,OAAZ,EAAqBgC,OAArB,EAA8B,OAA9B,EAAuCK,OAAvC;;AAEA;AACA,cAAMO,oBAAoB,MAAM,OAAKC,8BAAL,CAAoCR,OAApC,CAAhC;AACAtC,gBAAQC,GAAR,CAAY,UAAZ,EAAwB4C,iBAAxB;;AAEA,cAAM,EAAEE,aAAF,EAAiBC,WAAjB,EAA8BC,WAA9B,EAA2CC,gBAA3C,KAAgEL,iBAAtE;;AAEA,cAAMM,UAAU,EAAhB;;AAEA;AACA,YAAIV,kBAAkBM,gBAAgB,CAAtC,EAAyC;AACvC,gBAAMK,qBAAqB,CAACZ,cAAcO,aAAd,GAA8B,GAA/B,EAAoC7B,OAApC,CAA4C,CAA5C,CAA3B;AACA,gBAAMgB,SAAS,MAAM,OAAKF,sBAAL,CACnBS,cADmB,EAEnBW,kBAFmB,EAGnBnB,OAHmB,EAIlB,OAAMM,SAAU,QAJE,CAArB;AAMAY,kBAAQE,IAAR,CAAa;AACX1D,oBAAQ8C,cADG;AAEXa,mBAAO,UAFI;AAGX1D,wBAAYwD,kBAHD;AAIXG,kBAAMR,aAJK;AAKXb,oBAAQA;AALG,WAAb;AAOD;;AAED;AACA,YAAIQ,gBAAgBM,cAAc,CAAlC,EAAqC;AACnC,gBAAMQ,mBAAmB,CAAChB,cAAcQ,WAAd,GAA4B,GAA7B,EAAkC9B,OAAlC,CAA0C,CAA1C,CAAzB;AACA,gBAAMgB,SAAS,MAAM,OAAKF,sBAAL,CACnBU,YADmB,EAEnBc,gBAFmB,EAGnBvB,OAHmB,EAIlB,SAAQM,SAAU,QAJA,CAArB;AAMAY,kBAAQE,IAAR,CAAa;AACX1D,oBAAQ+C,YADG;AAEXY,mBAAO,QAFI;AAGX1D,wBAAY4D,gBAHD;AAIXD,kBAAMP,WAJK;AAKXd,oBAAQA;AALG,WAAb;AAOD;;AAED;AACA,YAAIS,gBAAgBM,cAAc,CAAlC,EAAqC;AACnC,gBAAMQ,mBAAmB,CAACjB,cAAcS,WAAd,GAA4B,GAA7B,EAAkC/B,OAAlC,CAA0C,CAA1C,CAAzB;AACA,gBAAMgB,SAAS,MAAM,OAAKF,sBAAL,CACnBW,YADmB,EAEnBc,gBAFmB,EAGnBxB,OAHmB,EAIlB,WAAUM,SAAU,QAJF,CAArB;AAMAY,kBAAQE,IAAR,CAAa;AACX1D,oBAAQgD,YADG;AAEXW,mBAAO,QAFI;AAGX1D,wBAAY6D,gBAHD;AAIXF,kBAAMN,WAJK;AAKXf,oBAAQA;AALG,WAAb;AAOD;;AAED;AACA,YAAIU,oBAAoBM,mBAAmB,CAA3C,EAA8C;AAC5C,gBAAMQ,uBAAuB,CAAClB,cAAcU,gBAAd,GAAiC,GAAlC,EAAuChC,OAAvC,CAA+C,CAA/C,CAA7B;AACA,gBAAMgB,SAAS,MAAM,OAAKF,sBAAL,CACnBY,gBADmB,EAEnBc,oBAFmB,EAGnBzB,OAHmB,EAIlB,SAAQM,SAAU,QAJA,CAArB;AAMAY,kBAAQE,IAAR,CAAa;AACX1D,oBAAQiD,gBADG;AAEXU,mBAAO,aAFI;AAGX1D,wBAAY8D,oBAHD;AAIXH,kBAAML,gBAJK;AAKXhB,oBAAQA;AALG,WAAb;AAOD;;AAEDlC,gBAAQC,GAAR,CAAY,2BAAZ,EAAyCkD,QAAQQ,MAAjD,EAAyD,KAAzD;AACA,eAAOR,OAAP;AAED,OAlGD,CAkGE,OAAOpB,KAAP,EAAc;AACd/B,gBAAQ+B,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,cAAMA,KAAN;AACD;AAtGkD;AAuGpD;;AAED;;;;;AAKMI,+BAAN,CAAoCxC,MAApC,EAA4CC,UAA5C,EAAwD;AAAA;;AAAA;AACtD,UAAI;AACF;AACA,cAAMgE,WAAW,MAAM,OAAKxD,KAAL,CAAW,oBAAX,EAAiCC,KAAjC,CAAuC,EAAEC,SAASX,MAAX,EAAvC,EAA4DY,IAA5D,EAAvB;;AAEA,YAAI,CAACf,MAAMgB,OAAN,CAAcoD,QAAd,CAAL,EAA8B;AAC5B,gBAAM9C,qBAAqBC,WAAW6C,SAASnD,gBAAT,IAA6B,CAAxC,IAA6CM,WAAWnB,UAAX,CAAxE;AACA,gBAAMiE,gBAAgBC,SAASF,SAASG,WAAT,IAAwB,CAAjC,IAAsC,CAA5D;;AAEA,gBAAM,OAAK3D,KAAL,CAAW,oBAAX,EAAiCC,KAAjC,CAAuC,EAAEC,SAASX,MAAX,EAAvC,EAA4DsB,MAA5D,CAAmE;AACvER,8BAAkBK,mBAAmBI,OAAnB,CAA2B,CAA3B,CADqD;AAEvE6C,yBAAaF,aAF0D;AAGvEG,6BAAiBF,SAAS,IAAI1C,IAAJ,GAAW6C,OAAX,KAAuB,IAAhC,CAHsD;AAIvEC,yBAAaJ,SAAS,IAAI1C,IAAJ,GAAW6C,OAAX,KAAuB,IAAhC;AAJ0D,WAAnE,CAAN;;AAOAjE,kBAAQC,GAAR,CAAa,OAAMN,MAAO,cAAamB,mBAAmBI,OAAnB,CAA2B,CAA3B,CAA8B,UAAS2C,aAAc,EAA5F;AACD;AACF,OAjBD,CAiBE,OAAO9B,KAAP,EAAc;AACd/B,gBAAQ+B,KAAR,CAAc,cAAd,EAA8BA,KAA9B;AACD;AApBqD;AAqBvD;;AAED;;;;;;AAMMoC,iBAAN,CAAsBxE,MAAtB,EAA8ByE,QAA9B,EAAwCC,eAAxC,EAAyD;AAAA;;AAAA;AACvD,UAAI;AACF,YAAIA,mBAAmB,CAAvB,EAA0B;AACxBrE,kBAAQC,GAAR,CAAY,aAAZ;AACA,iBAAO,EAAE0B,SAAS,IAAX,EAAiBG,iBAAiB,CAAlC,EAAP;AACD;;AAED9B,gBAAQC,GAAR,CAAa,WAAUN,MAAO,UAASyE,QAAS,SAAQC,eAAgB,UAAxE;;AAEA,cAAMvE,cAAe,QAAOsE,QAAS,IAArC;AACA,cAAMlC,SAAS,MAAM,OAAKxC,aAAL,CAAmBC,MAAnB,EAA2B0E,eAA3B,EAA4C,OAA5C,EAAqDvE,WAArD,EAAkEsE,QAAlE,CAArB;;AAEA;AACA,cAAM,OAAKjC,6BAAL,CAAmCxC,MAAnC,EAA2C0E,eAA3C,CAAN;;AAEA,eAAOnC,MAAP;AAED,OAhBD,CAgBE,OAAOH,KAAP,EAAc;AACd/B,gBAAQ+B,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,cAAMA,KAAN;AACD;AApBsD;AAqBxD;;AAED;;;;;AAKMuC,qBAAN,CAA0B3E,MAA1B,EAAkC4E,MAAlC,EAA0C;AAAA;;AAAA;AACxC,UAAI;AACF,cAAMC,cAAcV,SAAS,IAAI1C,IAAJ,GAAW6C,OAAX,KAAuB,IAAhC,CAApB;AACA,cAAMQ,eAAe,IAAIrD,IAAJ,GAAWsD,QAAX,KAAwB,CAA7C;AACA,cAAMC,cAAc,IAAIvD,IAAJ,GAAWwD,WAAX,EAApB;;AAEA;AACA,cAAMhB,WAAW,MAAM,OAAKxD,KAAL,CAAW,oBAAX,EAAiCC,KAAjC,CAAuC,EAAEC,SAASX,MAAX,EAAvC,EAA4DY,IAA5D,EAAvB;;AAEA,YAAIf,MAAMgB,OAAN,CAAcoD,QAAd,CAAJ,EAA6B;AAC3B5D,kBAAQC,GAAR,CAAY,iBAAZ;AACA;AACD;;AAED;AACA,cAAM4E,iBAAiBf,SAASF,SAASnD,gBAAT,IAA6B,CAAtC,IAA2C8D,MAAlE;;AAEA;AACA,cAAMO,qBAAqB,IAAI1D,IAAJ,CAASwC,SAASM,WAAT,GAAuB,IAAhC,CAA3B;AACA,cAAMa,iBAAiBD,mBAAmBJ,QAAnB,KAAgC,CAAhC,KAAsCD,YAAtC,IACDK,mBAAmBF,WAAnB,OAAqCD,WAD3D;;AAGA,YAAIK,cAAJ;AACA,YAAID,cAAJ,EAAoB;AAClBC,2BAAiBlB,SAASF,SAASqB,gBAAT,IAA6B,CAAtC,IAA2CV,MAA5D;AACD,SAFD,MAEO;AACL;AACAS,2BAAiBT,MAAjB;AACD;;AAED;AACA,cAAM,OAAKnE,KAAL,CAAW,oBAAX,EAAiCC,KAAjC,CAAuC,EAAEC,SAASX,MAAX,EAAvC,EAA4DsB,MAA5D,CAAmE;AACvER,4BAAkBoE,cADqD;AAEvEI,4BAAkBD,cAFqD;AAGvEd,uBAAaM;AAH0D,SAAnE,CAAN;;AAMAxE,gBAAQC,GAAR,CAAa,oBAAmB4E,cAAe,SAAQG,cAAe,EAAtE;AAED,OAtCD,CAsCE,OAAOjD,KAAP,EAAc;AACd/B,gBAAQ+B,KAAR,CAAc,YAAd,EAA4BA,KAA5B;AACA,cAAMA,KAAN;AACD;AA1CuC;AA2CzC;;AAED;;;;AAIMmD,sBAAN,CAA2BvF,MAA3B,EAAmC;AAAA;;AAAA;AACjC,UAAI;AACFK,gBAAQC,GAAR,CAAa,aAAYN,MAAO,WAAhC;;AAEA;AACA,cAAMiE,WAAW,MAAM,OAAKxD,KAAL,CAAW,oBAAX,EAAiCC,KAAjC,CAAuC,EAAEC,SAASX,MAAX,EAAvC,EAA4DY,IAA5D,EAAvB;AACA,YAAIf,MAAMgB,OAAN,CAAcoD,QAAd,CAAJ,EAA6B;AAC3B5D,kBAAQC,GAAR,CAAY,UAAZ;AACA;AACD;;AAED,cAAMkF,eAAevB,SAASN,KAA9B;AACA,cAAM8B,cAAcxB,SAASyB,YAA7B;;AAEA;AACA,cAAMC,SAAS,MAAM,OAAKlF,KAAL,CAAW,iBAAX,EAA8BmF,KAA9B,CAAoC,WAApC,EAAiDC,MAAjD,EAArB;;AAEA;AACA,YAAIpB,WAAWe,YAAf;AACA,YAAIM,cAAc,CAAlB;;AAEA,aAAK,IAAInC,KAAT,IAAkBgC,MAAlB,EAA0B;AACxB,cAAIhC,MAAMA,KAAN,GAAc6B,YAAd,IACAC,eAAe9B,MAAMoC,UADrB,KAECpC,MAAMqC,UAAN,KAAqB,CAArB,IAA0BP,eAAe9B,MAAMqC,UAFhD,CAAJ,EAEiE;AAC/DvB,uBAAWd,MAAMA,KAAjB;AACAmC,0BAAc1E,WAAWuC,MAAMsC,YAAjB,KAAkC,CAAhD,CAF+D,CAEZ;AACpD;AACF;;AAED;AACA,YAAIxB,WAAWe,YAAf,EAA6B;AAC3BnF,kBAAQC,GAAR,CAAa,YAAWkF,YAAa,OAAMf,QAAS,EAApD;;AAEA;AACA,gBAAM,OAAKhE,KAAL,CAAW,oBAAX,EAAiCC,KAAjC,CAAuC,EAAEC,SAASX,MAAX,EAAvC,EAA4DsB,MAA5D,CAAmE;AACvEqC,mBAAOc,QADgE;AAEvEF,yBAAaJ,SAAS,IAAI1C,IAAJ,GAAW6C,OAAX,KAAuB,IAAhC;AAF0D,WAAnE,CAAN;;AAKA;AACA,cAAIwB,cAAc,CAAlB,EAAqB;AACnB,kBAAM,OAAKtB,eAAL,CAAqBxE,MAArB,EAA6ByE,QAA7B,EAAuCqB,WAAvC,CAAN;AACD;;AAEDzF,kBAAQC,GAAR,CAAa,kBAAiBwF,WAAY,GAA1C;AACA,iBAAO,EAAEI,UAAU,IAAZ,EAAkBzB,QAAlB,EAA4BqB,WAA5B,EAAP;AACD;;AAEDzF,gBAAQC,GAAR,CAAY,OAAZ;AACA,eAAO,EAAE4F,UAAU,KAAZ,EAAmBV,YAAnB,EAAP;AAED,OAnDD,CAmDE,OAAOpD,KAAP,EAAc;AACd/B,gBAAQ+B,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,cAAMA,KAAN;AACD;AAvDgC;AAwDlC;;AAED;;;;AAIM+D,eAAN,CAAoBnG,MAApB,EAA4B;AAAA;;AAAA;AAC1B,UAAI;AACF,cAAMoG,aAAa,MAAM,OAAK3F,KAAL,CAAW,aAAX,EAA0BC,KAA1B,CAAgC,EAAEC,SAASX,MAAX,EAAhC,EAAqDY,IAArD,EAAzB;;AAEA,YAAIf,MAAMgB,OAAN,CAAcuF,UAAd,CAAJ,EAA+B;AAC7B,iBAAO;AACLC,0BAAc,CADT;AAELC,8BAAkB,CAFb;AAGLC,yBAAa;AAHR,WAAP;AAKD;;AAED,eAAO;AACLF,wBAAclC,SAASiC,WAAWC,YAApB,CADT;AAELC,4BAAkBnC,SAASiC,WAAWE,gBAApB,CAFb;AAGLC,uBAAapC,SAASiC,WAAWG,WAApB;AAHR,SAAP;AAMD,OAjBD,CAiBE,OAAOnE,KAAP,EAAc;AACd/B,gBAAQ+B,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,cAAMA,KAAN;AACD;AArByB;AAsB3B;;AAED;;;;AAIMe,gCAAN,CAAqCR,OAArC,EAA8C;AAAA;;AAAA;AAC5C,UAAI;AACF;AACA,cAAM6D,qBAAqB,MAAM,OAAK/F,KAAL,CAAW,oBAAX,EAAiCC,KAAjC,CAAuC;AACtE+F,oBAAU9D,OAD4D;AAEtE+D,0BAAgB;AAFsD,SAAvC,EAG9B9F,IAH8B,EAAjC;;AAKA,YAAIf,MAAMgB,OAAN,CAAc2F,kBAAd,CAAJ,EAAuC;AACrCnG,kBAAQC,GAAR,CAAY,gBAAZ;AACA,iBAAO,IAAP;AACD;;AAED,eAAO,OAAKqG,uBAAL,CAA6BH,kBAA7B,CAAP;AACD,OAbD,CAaE,OAAOpE,KAAP,EAAc;AACd/B,gBAAQ+B,KAAR,CAAc,cAAd,EAA8BA,KAA9B;AACA,eAAO,IAAP;AACD;AAjB2C;AAkB7C;;AAED;;;;AAIAuE,0BAAwBH,kBAAxB,EAA4C;AAC1C,WAAO;AACLpD,qBAAehC,WAAWoF,mBAAmBpD,aAAnB,IAAoC,IAA/C,CADV;AAELC,mBAAajC,WAAWoF,mBAAmBnD,WAAnB,IAAkC,IAA7C,CAFR;AAGLC,mBAAalC,WAAWoF,mBAAmBlD,WAAnB,IAAkC,IAA7C,CAHR;AAILC,wBAAkBnC,WAAWoF,mBAAmBjD,gBAAnB,IAAuC,IAAlD;AAJb,KAAP;AAMD;;AAED;;;;;;;;;AASMqD,8BAAN,CAAmC5G,MAAnC,EAA2CsC,OAA3C,EAAoDK,OAApD,EAA6DE,WAA7D,EAA0EgE,YAA1E,EAAwFC,cAAc,aAAtG,EAAqH;AAAA;;AAAA;AACnH,UAAI;AACFzG,gBAAQC,GAAR,CAAa,2BAAb;AACAD,gBAAQC,GAAR,CAAY,UAAZ,EAAwBN,MAAxB,EAAgC,OAAhC,EAAyCsC,OAAzC,EAAkD,OAAlD,EAA2DK,OAA3D,EAAoE,OAApE,EAA6EE,WAA7E;AACAxC,gBAAQC,GAAR,CAAY,WAAZ,EAAyBuG,YAAzB;AACAxG,gBAAQC,GAAR,CAAY,0BAAZ;;AAEA;AACA,cAAM4C,oBAAoB,MAAM,QAAKC,8BAAL,CAAoCR,OAApC,CAAhC;;AAEA,YAAI,CAACO,iBAAL,EAAwB;AACtB7C,kBAAQC,GAAR,CAAY,gBAAZ;AACA,iBAAO,EAAE0B,SAAS,KAAX,EAAkB+E,SAAS,SAA3B,EAAP;AACD;;AAED1G,gBAAQC,GAAR,CAAY,UAAZ,EAAwB4C,iBAAxB;;AAEA;AACA,cAAMO,qBAAqBrC,WAAW,CAACyB,cAAcK,kBAAkBE,aAAhC,GAAgD,GAAjD,EAAsD7B,OAAtD,CAA8D,CAA9D,CAAX,CAA3B;AACA,cAAMsC,mBAAmBgD,eAAezF,WAAW,CAACyB,cAAcK,kBAAkBG,WAAhC,GAA8C,GAA/C,EAAoD9B,OAApD,CAA4D,CAA5D,CAAX,CAAf,GAA4F,CAArH;;AAEAlB,gBAAQC,GAAR,CAAa,WAAUmD,kBAAmB,QAAOI,gBAAiB,GAAlE;;AAEA;AACA,cAAMmD,YAAY,MAAM,QAAKvG,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B,EAAEuG,IAAI3E,OAAN,EAA1B,EAA2C1B,IAA3C,EAAxB;AACA,cAAMsG,YAAY,MAAM,QAAKzG,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B,EAAEuG,IAAItE,OAAN,EAA1B,EAA2C/B,IAA3C,EAAxB;;AAEA;AACA,cAAM,QAAKH,KAAL,CAAW,kBAAX,EAA+BS,GAA/B,CAAmC;AACvCiG,uBAAa,CAD0B;AAEvCC,4BAAkBpH,MAFqB;AAGvCqH,mCAAyBR,YAHc;AAIvCS,yBAAeN,UAAUrG,OAJc;AAKvC4G,oBAAUjF,OAL6B;AAMvCkF,oBAAUR,UAAUQ,QANmB;AAOvCf,oBAAU9D,OAP6B;AAQvC8E,sBAAYP,UAAUQ,IARiB;AASvCC,uBAAaT,UAAUU,UATgB;AAUvCC,wBAAchF,WAVyB;AAWvCiF,2BAAiB5E,kBAAkBE,aAXI;AAYvC2E,6BAAmBtE,kBAZoB;AAavCuE,+BAAqBvE,kBAbkB;AAcvCwE,6BAAmBpE,gBAdoB;AAevCqE,6BAAmB,CAfoB;AAgBvCC,kCAAwB,CAhBe;AAiBvCC,wBAActB,WAjByB;AAkBvChF,kBAAQ,SAlB+B;AAmBvCuG,6BAAmB,SAnBoB,EAmBT;AAC9BC,0BAAgB,CApBuB;AAqBvCC,uBAAa,CArB0B;AAsBvCC,yBAAe,CAtBwB;AAuBvCC,uBAAa,CAvB0B;AAwBvCC,uBAAavE,SAAS,IAAI1C,IAAJ,GAAW6C,OAAX,KAAuB,IAAhC,CAxB0B;AAyBvCC,uBAAaJ,SAAS,IAAI1C,IAAJ,GAAW6C,OAAX,KAAuB,IAAhC;AAzB0B,SAAnC,CAAN;;AA4BAjE,gBAAQC,GAAR,CAAY,wBAAZ;;AAEA,eAAO;AACL0B,mBAAS,IADJ;AAEL/B,sBAAYwD,kBAFP;AAGLkF,4BAAkB9E,gBAHb;AAILkD,mBAAS;AAJJ,SAAP;AAOD,OAhED,CAgEE,OAAO3E,KAAP,EAAc;AACd/B,gBAAQ+B,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,eAAO,EAAEJ,SAAS,KAAX,EAAkB+E,SAAS3E,MAAM2E,OAAjC,EAAP;AACD;AApEkH;AAqEpH;;AAED;;;;;;;;AAQM6B,oBAAN,CAAyB5I,MAAzB,EAAiCsC,OAAjC,EAA0CK,OAA1C,EAAmDE,WAAnD,EAAgEiE,cAAc,aAA9E,EAA6F;AAAA;;AAAA;AAC3F,UAAI;AACFzG,gBAAQC,GAAR,CAAa,2BAAb;AACAD,gBAAQC,GAAR,CAAY,QAAZ,EAAsBN,MAAtB,EAA8B,OAA9B,EAAuCsC,OAAvC,EAAgD,OAAhD,EAAyDK,OAAzD,EAAkE,OAAlE,EAA2EE,WAA3E;;AAEA;AACA,cAAMK,oBAAoB,MAAM,QAAKC,8BAAL,CAAoCR,OAApC,CAAhC;;AAEA,YAAI,CAACO,iBAAL,EAAwB;AACtB7C,kBAAQC,GAAR,CAAY,gBAAZ;AACA,iBAAO,EAAE0B,SAAS,KAAX,EAAkB+E,SAAS,SAA3B,EAAP;AACD;;AAED1G,gBAAQC,GAAR,CAAY,UAAZ,EAAwB4C,iBAAxB;;AAEA;AACA,cAAM2F,iBAAiB3F,kBAAkBE,aAAzC;AACA,YAAIyF,kBAAkB,CAAtB,EAAyB;AACvBxI,kBAAQC,GAAR,CAAY,oBAAZ;AACA,iBAAO,EAAE0B,SAAS,KAAX,EAAkB+E,SAAS,aAA3B,EAAP;AACD;;AAED;AACA,cAAM+B,mBAAmB1H,WAAW,CAACyB,cAAcgG,cAAd,GAA+B,GAAhC,EAAqCtH,OAArC,CAA6C,CAA7C,CAAX,CAAzB;AACAlB,gBAAQC,GAAR,CAAa,YAAWuI,cAAe,YAAWC,gBAAiB,GAAnE;;AAEA;;AAEA;AACA,cAAM9B,YAAY,MAAM,QAAKvG,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B,EAAEuG,IAAI3E,OAAN,EAA1B,EAA2C1B,IAA3C,EAAxB;AACA,cAAMsG,YAAY,MAAM,QAAKzG,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B,EAAEuG,IAAItE,OAAN,EAA1B,EAA2C/B,IAA3C,EAAxB;;AAEA,cAAM,QAAKH,KAAL,CAAW,kBAAX,EAA+BS,GAA/B,CAAmC;AACvCiG,uBAAa,CAD0B,EACvB;AAChBC,4BAAkBpH,MAFqB;AAGvCsH,yBAAeN,UAAUrG,OAHc;AAIvC4G,oBAAUjF,OAJ6B;AAKvCkF,oBAAUR,UAAUQ,QALmB;AAMvCf,oBAAU9D,OAN6B;AAOvC8E,sBAAYP,UAAUQ,IAPiB;AAQvCC,uBAAaT,UAAUU,UARgB;AASvCC,wBAAchF,WATyB;AAUvCiF,2BAAiBe,cAVsB;AAWvCd,6BAAmBe,gBAXoB;AAYvCd,+BAAqBc,gBAZkB,EAYA;AACvCb,6BAAmB,IAboB;AAcvCC,6BAAmB,IAdoB;AAevCC,kCAAwB,IAfe;AAgBvCC,wBAActB,WAhByB;AAiBvCiC,6BAAmB,OAjBoB,EAiBX;AAC5BjH,kBAAQ,SAlB+B,EAkBpB;AACnBwG,0BAAgB,CAnBuB;AAoBvCC,uBAAa,CApB0B,EAoBvB;AAChBG,uBAAavE,SAAS,IAAI1C,IAAJ,GAAW6C,OAAX,KAAuB,IAAhC,CArB0B;AAsBvCC,uBAAaJ,SAAS,IAAI1C,IAAJ,GAAW6C,OAAX,KAAuB,IAAhC;AAtB0B,SAAnC,CAAN;;AAyBAjE,gBAAQC,GAAR,CAAY,wBAAZ;AACA,eAAO,EAAE0B,SAAS,IAAX,EAAiB/B,YAAY6I,gBAA7B,EAA+CvG,QAAQ,EAAEyG,SAAS,IAAX,EAAvD,EAAP;AAED,OA3DD,CA2DE,OAAO5G,KAAP,EAAc;AACd/B,gBAAQ+B,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,cAAMA,KAAN;AACD;AA/D0F;AAgE5F;;AAED;;;;;;;;AAQMrC,eAAN,CAAoBC,MAApB,EAA4BiJ,MAA5B,EAAoC/I,OAAO,WAA3C,EAAwDE,WAAW,IAAnE,EAAyED,cAAc,EAAvF,EAA2F;AAAA;;AAAA;AACzF,UAAI;AACFE,gBAAQC,GAAR,CAAa,sBAAb;AACAD,gBAAQC,GAAR,CAAY,OAAZ,EAAqBN,MAArB,EAA6B,KAA7B,EAAoCiJ,MAApC,EAA4C,KAA5C,EAAmD/I,IAAnD;;AAEA;AACA,cAAMgJ,oBAAoB,QAAKC,OAAL,CAAa,YAAb,CAA1B;;AAEA;AACA,cAAM5G,SAAS,MAAM2G,kBAAkBE,gBAAlB,CACnBpJ,MADmB,EAEnBiJ,MAFmB,EAGnB7I,QAHmB,EAGT;AACV,YAJmB,EAIT;AACVD,mBALmB,CAArB;;AAQA;AACA,cAAM8D,WAAW,MAAM,QAAKxD,KAAL,CAAW,oBAAX,EAAiCC,KAAjC,CAAuC,EAAEC,SAASX,MAAX,EAAvC,EAA4DY,IAA5D,EAAvB;AACA,YAAI,CAACf,MAAMgB,OAAN,CAAcoD,QAAd,CAAL,EAA8B;AAC5B,gBAAM,QAAKxD,KAAL,CAAW,oBAAX,EAAiCC,KAAjC,CAAuC,EAAEC,SAASX,MAAX,EAAvC,EAA4DqJ,SAA5D,CAAsE,kBAAtE,EAA0FJ,MAA1F,CAAN;AACA,gBAAM,QAAKxI,KAAL,CAAW,oBAAX,EAAiCC,KAAjC,CAAuC,EAAEC,SAASX,MAAX,EAAvC,EAA4DqJ,SAA5D,CAAsE,kBAAtE,EAA0FJ,MAA1F,CAAN;AACD;;AAED5I,gBAAQC,GAAR,CAAa,eAAcN,MAAO,MAAKiJ,MAAO,KAA9C;AACA,eAAO,EAAEjH,SAAS,IAAX,EAAiBO,QAAQA,MAAzB,EAAP;AAED,OA1BD,CA0BE,OAAOH,KAAP,EAAc;AACd/B,gBAAQ+B,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,cAAMA,KAAN;AACD;AA9BwF;AA+B1F;AA3nB0C,CAA7C", "file": "..\\..\\..\\src\\common\\service\\points.js", "sourcesContent": ["/**\n * 佣金服务\n * 统一管理用户佣金的增减操作\n */\n\nmodule.exports = class extends think.Service {\n\n  /**\n   * 给用户增加佣金\n   * @param {number} userId 用户ID\n   * @param {number} commission 佣金金额\n   * @param {string} type 佣金类型：promotion,bonus,admin,withdraw\n   * @param {string} description 描述\n   * @param {number} sourceId 来源ID（可选）\n   */\n  async addCommission(userId, commission, type = 'promotion', description = '', sourceId = null) {\n    try {\n      console.log(`=== 给用户 ${userId} 增加 ${commission} 元佣金 ===`);\n      console.log('类型:', type, '描述:', description);\n\n      // 验证参数\n      if (!userId || commission <= 0) {\n        throw new Error('用户ID和佣金金额必须有效');\n      }\n\n      // 获取用户当前佣金\n      let userCommission = await this.model('user_commission').where({ user_id: userId }).find();\n\n      if (think.isEmpty(userCommission)) {\n        // 如果用户佣金记录不存在，创建新记录\n        userCommission = {\n          user_id: userId,\n          total_commission: 0,\n          available_commission: 0,\n          frozen_commission: 0,\n          withdrawn_commission: 0\n        };\n        await this.model('user_commission').add(userCommission);\n        userCommission = await this.model('user_commission').where({ user_id: userId }).find();\n      }\n\n      // 计算新的佣金余额\n      const newTotalCommission = parseFloat(userCommission.total_commission) + parseFloat(commission);\n      const newAvailableCommission = parseFloat(userCommission.available_commission) + parseFloat(commission);\n\n      // 更新用户佣金\n      await this.model('user_commission').where({ user_id: userId }).update({\n        total_commission: newTotalCommission.toFixed(2),\n        available_commission: newAvailableCommission.toFixed(2),\n        updated_at: new Date()\n      });\n\n      // 记录佣金变动日志\n      await this.model('commission_log').add({\n        user_id: userId,\n        commission_change: parseFloat(commission).toFixed(2),\n        commission_type: type,\n        source_id: sourceId,\n        description: description,\n        balance_after: newAvailableCommission.toFixed(2),\n        status: 'completed',\n        created_at: new Date()\n      });\n\n      console.log(`✅ 佣金增加成功: ${userCommission.available_commission} -> ${newAvailableCommission.toFixed(2)}`);\n\n      return {\n        success: true,\n        oldBalance: parseFloat(userCommission.available_commission),\n        newBalance: parseFloat(newAvailableCommission.toFixed(2)),\n        commissionAdded: parseFloat(commission)\n      };\n\n    } catch (error) {\n      console.error('增加佣金失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 推广员获得推广佣金\n   * @param {number} userId 推广员用户ID\n   * @param {number} commission 佣金金额\n   * @param {number} orderId 订单ID（可选）\n   * @param {string} description 描述\n   */\n  async addPromotionCommission(userId, commission, orderId = null, description = '推广佣金') {\n    try {\n      console.log(`=== 推广员 ${userId} 获得推广佣金 ${commission} 元 ===`);\n\n      // 给用户增加佣金\n      const result = await this.addCommission(userId, commission, 'promotion', description, orderId);\n\n      // 更新推广员统计数据\n      await this.updatePromoterCommissionStats(userId, commission);\n\n      return result;\n\n    } catch (error) {\n      console.error('添加推广佣金失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 多级分销佣金发放（基于分销池配置）\n   * @param {object} commissionData 佣金数据\n   */\n  async distributeMultiLevelCommission(commissionData) {\n    try {\n      const {\n        orderId,\n        goodsId,\n        goodsName,\n        orderAmount,\n        promoterUserId,\n        level1UserId = null,\n        level2UserId = null,\n        teamLeaderUserId = null\n      } = commissionData;\n\n      console.log('=== 开始多级分销佣金发放（基于分销池配置） ===');\n      console.log('订单ID:', orderId, '商品ID:', goodsId);\n\n      // 获取分销池佣金配置\n      const distributionRates = await this.getDistributionCommissionRates(goodsId);\n      console.log('分销池佣金配置:', distributionRates);\n\n      const { personal_rate, level1_rate, level2_rate, team_leader_rate } = distributionRates;\n\n      const results = [];\n\n      // 1. 个人佣金（直接推广员）\n      if (promoterUserId && personal_rate > 0) {\n        const personalCommission = (orderAmount * personal_rate / 100).toFixed(2);\n        const result = await this.addPromotionCommission(\n          promoterUserId,\n          personalCommission,\n          orderId,\n          `推广商品${goodsName}获得个人佣金`\n        );\n        results.push({\n          userId: promoterUserId,\n          level: 'personal',\n          commission: personalCommission,\n          rate: personal_rate,\n          result: result\n        });\n      }\n\n      // 2. 一级分销佣金（从推广订单记录中获取）\n      if (level1UserId && level1_rate > 0) {\n        const level1Commission = (orderAmount * level1_rate / 100).toFixed(2);\n        const result = await this.addPromotionCommission(\n          level1UserId,\n          level1Commission,\n          orderId,\n          `下级推广商品${goodsName}获得一级佣金`\n        );\n        results.push({\n          userId: level1UserId,\n          level: 'level1',\n          commission: level1Commission,\n          rate: level1_rate,\n          result: result\n        });\n      }\n\n      // 3. 二级分销佣金（从推广订单记录中获取）\n      if (level2UserId && level2_rate > 0) {\n        const level2Commission = (orderAmount * level2_rate / 100).toFixed(2);\n        const result = await this.addPromotionCommission(\n          level2UserId,\n          level2Commission,\n          orderId,\n          `二级下级推广商品${goodsName}获得二级佣金`\n        );\n        results.push({\n          userId: level2UserId,\n          level: 'level2',\n          commission: level2Commission,\n          rate: level2_rate,\n          result: result\n        });\n      }\n\n      // 4. 团长佣金\n      if (teamLeaderUserId && team_leader_rate > 0) {\n        const teamLeaderCommission = (orderAmount * team_leader_rate / 100).toFixed(2);\n        const result = await this.addPromotionCommission(\n          teamLeaderUserId,\n          teamLeaderCommission,\n          orderId,\n          `团队推广商品${goodsName}获得团长佣金`\n        );\n        results.push({\n          userId: teamLeaderUserId,\n          level: 'team_leader',\n          commission: teamLeaderCommission,\n          rate: team_leader_rate,\n          result: result\n        });\n      }\n\n      console.log('✅ 多级分销佣金发放完成（基于分销池配置），共发放', results.length, '笔佣金');\n      return results;\n\n    } catch (error) {\n      console.error('多级分销佣金发放失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 更新推广员佣金统计\n   * @param {number} userId 用户ID\n   * @param {number} commission 佣金金额\n   */\n  async updatePromoterCommissionStats(userId, commission) {\n    try {\n      // 更新推广员表中的佣金统计字段\n      const promoter = await this.model('personal_promoters').where({ user_id: userId }).find();\n\n      if (!think.isEmpty(promoter)) {\n        const newTotalCommission = parseFloat(promoter.total_commission || 0) + parseFloat(commission);\n        const newOrderCount = parseInt(promoter.order_count || 0) + 1;\n\n        await this.model('personal_promoters').where({ user_id: userId }).update({\n          total_commission: newTotalCommission.toFixed(2),\n          order_count: newOrderCount,\n          last_order_time: parseInt(new Date().getTime() / 1000),\n          update_time: parseInt(new Date().getTime() / 1000)\n        });\n\n        console.log(`推广员 ${userId} 统计更新: 总佣金 ${newTotalCommission.toFixed(2)} 元，订单数 ${newOrderCount}`);\n      }\n    } catch (error) {\n      console.error('更新推广员佣金统计失败:', error);\n    }\n  }\n\n  /**\n   * 推广员升级奖励佣金\n   * @param {number} userId 推广员用户ID\n   * @param {number} newLevel 新等级\n   * @param {number} bonusCommission 奖励佣金\n   */\n  async addUpgradeBonus(userId, newLevel, bonusCommission) {\n    try {\n      if (bonusCommission <= 0) {\n        console.log('升级奖励佣金为0，跳过');\n        return { success: true, commissionAdded: 0 };\n      }\n\n      console.log(`=== 推广员 ${userId} 升级到等级 ${newLevel}，获得奖励 ${bonusCommission} 元佣金 ===`);\n\n      const description = `升级到等级${newLevel}奖励`;\n      const result = await this.addCommission(userId, bonusCommission, 'bonus', description, newLevel);\n\n      // 更新推广员统计数据\n      await this.updatePromoterCommissionStats(userId, bonusCommission);\n\n      return result;\n\n    } catch (error) {\n      console.error('添加升级奖励佣金失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 更新推广员统计数据\n   * @param {number} userId 用户ID\n   * @param {number} points 积分数量\n   */\n  async updatePromoterStats(userId, points) {\n    try {\n      const currentTime = parseInt(new Date().getTime() / 1000);\n      const currentMonth = new Date().getMonth() + 1;\n      const currentYear = new Date().getFullYear();\n\n      // 查找推广员记录\n      const promoter = await this.model('personal_promoters').where({ user_id: userId }).find();\n      \n      if (think.isEmpty(promoter)) {\n        console.log('推广员记录不存在，跳过统计更新');\n        return;\n      }\n\n      // 更新总积分\n      const newTotalPoints = parseInt(promoter.total_commission || 0) + points;\n      \n      // 检查是否是当月\n      const promoterUpdateTime = new Date(promoter.update_time * 1000);\n      const isCurrentMonth = promoterUpdateTime.getMonth() + 1 === currentMonth && \n                            promoterUpdateTime.getFullYear() === currentYear;\n\n      let newMonthPoints;\n      if (isCurrentMonth) {\n        newMonthPoints = parseInt(promoter.month_commission || 0) + points;\n      } else {\n        // 新月份，重置月度积分\n        newMonthPoints = points;\n      }\n\n      // 更新推广员统计\n      await this.model('personal_promoters').where({ user_id: userId }).update({\n        total_commission: newTotalPoints,\n        month_commission: newMonthPoints,\n        update_time: currentTime\n      });\n\n      console.log(`✅ 推广员统计更新成功: 总积分=${newTotalPoints}, 月积分=${newMonthPoints}`);\n\n    } catch (error) {\n      console.error('更新推广员统计失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 检查并处理推广员等级升级\n   * @param {number} userId 用户ID\n   */\n  async checkPromoterLevelUp(userId) {\n    try {\n      console.log(`=== 检查推广员 ${userId} 等级升级 ===`);\n\n      // 获取推广员信息\n      const promoter = await this.model('personal_promoters').where({ user_id: userId }).find();\n      if (think.isEmpty(promoter)) {\n        console.log('推广员记录不存在');\n        return;\n      }\n\n      const currentLevel = promoter.level;\n      const totalOrders = promoter.total_orders;\n\n      // 获取所有等级配置\n      const levels = await this.model('promoter_levels').order('level ASC').select();\n      \n      // 找到符合条件的最高等级\n      let newLevel = currentLevel;\n      let bonusPoints = 0;\n\n      for (let level of levels) {\n        if (level.level > currentLevel &&\n            totalOrders >= level.min_orders &&\n            (level.max_orders === 0 || totalOrders <= level.max_orders)) {\n          newLevel = level.level;\n          bonusPoints = parseFloat(level.bonus_points) || 0; // 升级奖励佣金\n        }\n      }\n\n      // 如果等级有提升\n      if (newLevel > currentLevel) {\n        console.log(`推广员等级提升: ${currentLevel} -> ${newLevel}`);\n\n        // 更新推广员等级\n        await this.model('personal_promoters').where({ user_id: userId }).update({\n          level: newLevel,\n          update_time: parseInt(new Date().getTime() / 1000)\n        });\n\n        // 发放升级奖励佣金\n        if (bonusPoints > 0) {\n          await this.addUpgradeBonus(userId, newLevel, bonusPoints);\n        }\n\n        console.log(`✅ 等级升级完成，奖励佣金: ${bonusPoints}元`);\n        return { upgraded: true, newLevel, bonusPoints };\n      }\n\n      console.log('等级无变化');\n      return { upgraded: false, currentLevel };\n\n    } catch (error) {\n      console.error('检查等级升级失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 获取用户积分余额\n   * @param {number} userId 用户ID\n   */\n  async getUserPoints(userId) {\n    try {\n      const userPoints = await this.model('user_points').where({ user_id: userId }).find();\n      \n      if (think.isEmpty(userPoints)) {\n        return {\n          total_points: 0,\n          available_points: 0,\n          used_points: 0\n        };\n      }\n\n      return {\n        total_points: parseInt(userPoints.total_points),\n        available_points: parseInt(userPoints.available_points),\n        used_points: parseInt(userPoints.used_points)\n      };\n\n    } catch (error) {\n      console.error('获取用户积分失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 获取商品分销池的佣金配置\n   * @param {number} goodsId 商品ID\n   */\n  async getDistributionCommissionRates(goodsId) {\n    try {\n      // 获取商品分销配置\n      const distributionConfig = await this.model('goods_distribution').where({\n        goods_id: goodsId,\n        is_distributed: 1\n      }).find();\n\n      if (think.isEmpty(distributionConfig)) {\n        console.log('商品未开启分销，无法获得佣金');\n        return null;\n      }\n\n      return this.formatDistributionRates(distributionConfig);\n    } catch (error) {\n      console.error('获取分销池佣金配置失败:', error);\n      return null;\n    }\n  }\n\n  /**\n   * 格式化分销池佣金比例\n   * @param {object} distributionConfig 分销配置\n   */\n  formatDistributionRates(distributionConfig) {\n    return {\n      personal_rate: parseFloat(distributionConfig.personal_rate || 8.00),\n      level1_rate: parseFloat(distributionConfig.level1_rate || 3.00),\n      level2_rate: parseFloat(distributionConfig.level2_rate || 1.00),\n      team_leader_rate: parseFloat(distributionConfig.team_leader_rate || 2.00)\n    };\n  }\n\n  /**\n   * 分享有礼佣金发放（基于分销池配置，支持上级推广员）\n   * @param {number} userId 分享者用户ID\n   * @param {number} orderId 订单ID\n   * @param {number} goodsId 商品ID\n   * @param {number} orderAmount 订单金额\n   * @param {number} parentUserId 上级推广员用户ID\n   * @param {string} shareSource 分享来源\n   */\n  async addShareCommissionWithParent(userId, orderId, goodsId, orderAmount, parentUserId, shareSource = 'miniprogram') {\n    try {\n      console.log(`=== 分享有礼佣金记录（支持上级推广员） ===`);\n      console.log('当前分享者ID:', userId, '订单ID:', orderId, '商品ID:', goodsId, '订单金额:', orderAmount);\n      console.log('分享者的上级ID:', parentUserId);\n      console.log('注意：佣金基于当前分享者，与购买者的上级关系无关');\n\n      // 1. 获取商品分销池的佣金配置\n      const distributionRates = await this.getDistributionCommissionRates(goodsId);\n\n      if (!distributionRates) {\n        console.log('商品未开启分销，无法获得佣金');\n        return { success: false, message: '商品未开启分销' };\n      }\n\n      console.log('分销池佣金配置:', distributionRates);\n\n      // 2. 计算佣金\n      const personalCommission = parseFloat((orderAmount * distributionRates.personal_rate / 100).toFixed(2));\n      const level1Commission = parentUserId ? parseFloat((orderAmount * distributionRates.level1_rate / 100).toFixed(2)) : 0;\n\n      console.log(`佣金计算: 个人${personalCommission}元, 上级${level1Commission}元`);\n\n      // 3. 记录推广订单，等确认收货后发放\n      const orderInfo = await this.model('order').where({ id: orderId }).find();\n      const goodsInfo = await this.model('goods').where({ id: goodsId }).find();\n\n      // 记录直接推广员的佣金\n      await this.model('promotion_orders').add({\n        promoter_id: 0,\n        promoter_user_id: userId,\n        parent_promoter_user_id: parentUserId,\n        buyer_user_id: orderInfo.user_id,\n        order_id: orderId,\n        order_sn: orderInfo.order_sn,\n        goods_id: goodsId,\n        goods_name: goodsInfo.name,\n        goods_price: goodsInfo.shop_price,\n        order_amount: orderAmount,\n        commission_rate: distributionRates.personal_rate,\n        commission_amount: personalCommission,\n        personal_commission: personalCommission,\n        level1_commission: level1Commission,\n        level2_commission: 0,\n        team_leader_commission: 0,\n        share_source: shareSource,\n        status: 'pending',\n        commission_status: 'pending', // 佣金状态：待发放\n        is_first_order: 0,\n        settle_time: 0,\n        withdraw_time: 0,\n        refund_time: 0,\n        create_time: parseInt(new Date().getTime() / 1000),\n        update_time: parseInt(new Date().getTime() / 1000)\n      });\n\n      console.log('✅ 分享有礼佣金记录完成，等待确认收货后发放');\n\n      return {\n        success: true,\n        commission: personalCommission,\n        parentCommission: level1Commission,\n        message: '分享有礼佣金记录成功'\n      };\n\n    } catch (error) {\n      console.error('分享有礼佣金记录失败:', error);\n      return { success: false, message: error.message };\n    }\n  }\n\n  /**\n   * 分享有礼佣金发放（基于分销池配置）\n   * @param {number} userId 分享者用户ID\n   * @param {number} orderId 订单ID\n   * @param {number} goodsId 商品ID\n   * @param {number} orderAmount 订单金额\n   * @param {string} shareSource 分享来源\n   */\n  async addShareCommission(userId, orderId, goodsId, orderAmount, shareSource = 'miniprogram') {\n    try {\n      console.log(`=== 分享有礼佣金记录（确认收货后发放） ===`);\n      console.log('分享者ID:', userId, '订单ID:', orderId, '商品ID:', goodsId, '订单金额:', orderAmount);\n\n      // 1. 获取商品分销池的佣金配置\n      const distributionRates = await this.getDistributionCommissionRates(goodsId);\n\n      if (!distributionRates) {\n        console.log('商品未开启分销，无法获得佣金');\n        return { success: false, message: '商品未开启分销' };\n      }\n\n      console.log('分销池佣金配置:', distributionRates);\n\n      // 2. 使用分销池的个人佣金比例\n      const commissionRate = distributionRates.personal_rate;\n      if (commissionRate <= 0) {\n        console.log('分销池个人佣金比例为0，无法获得佣金');\n        return { success: false, message: '分销池个人佣金比例为0' };\n      }\n\n      // 3. 计算佣金金额\n      const commissionAmount = parseFloat((orderAmount * commissionRate / 100).toFixed(2));\n      console.log(`分销池佣金比例: ${commissionRate}%, 佣金金额: ${commissionAmount}元`);\n\n      // 4. 不再发放佣金，只记录推广订单，等确认收货后发放\n\n      // 5. 在推广订单表中记录分享佣金（等待确认收货后发放）\n      const orderInfo = await this.model('order').where({ id: orderId }).find();\n      const goodsInfo = await this.model('goods').where({ id: goodsId }).find();\n\n      await this.model('promotion_orders').add({\n        promoter_id: 0, // 分享有礼没有推广员ID\n        promoter_user_id: userId,\n        buyer_user_id: orderInfo.user_id,\n        order_id: orderId,\n        order_sn: orderInfo.order_sn,\n        goods_id: goodsId,\n        goods_name: goodsInfo.name,\n        goods_price: goodsInfo.shop_price,\n        order_amount: orderAmount,\n        commission_rate: commissionRate,\n        commission_amount: commissionAmount,\n        personal_commission: commissionAmount, // 分享有礼只有个人佣金\n        level1_commission: 0.00,\n        level2_commission: 0.00,\n        team_leader_commission: 0.00,\n        share_source: shareSource,\n        commission_source: 'share', // 标识为分享有礼佣金\n        status: 'pending', // 等待确认收货后发放\n        is_first_order: 0,\n        settle_time: 0, // 确认收货时更新\n        create_time: parseInt(new Date().getTime() / 1000),\n        update_time: parseInt(new Date().getTime() / 1000)\n      });\n\n      console.log('✅ 分享有礼佣金记录成功，等待确认收货后发放');\n      return { success: true, commission: commissionAmount, result: { pending: true } };\n\n    } catch (error) {\n      console.error('分享有礼佣金发放失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 通用佣金发放方法（使用统一的佣金服务）\n   * @param {number} userId 用户ID\n   * @param {number} amount 佣金金额\n   * @param {string} type 佣金类型\n   * @param {number} sourceId 来源ID\n   * @param {string} description 描述\n   */\n  async addCommission(userId, amount, type = 'promotion', sourceId = null, description = '') {\n    try {\n      console.log(`=== 使用统一佣金服务发放佣金 ===`);\n      console.log('用户ID:', userId, '金额:', amount, '类型:', type);\n\n      // 使用统一的佣金服务，而不是直接操作数据库\n      const commissionService = this.service('commission');\n\n      // 调用佣金服务的发放方法\n      const result = await commissionService.settleCommission(\n        userId,\n        amount,\n        sourceId, // 订单ID\n        null,     // 推广订单ID，这里暂时为null\n        description\n      );\n\n      // 更新推广员统计（如果存在）\n      const promoter = await this.model('personal_promoters').where({ user_id: userId }).find();\n      if (!think.isEmpty(promoter)) {\n        await this.model('personal_promoters').where({ user_id: userId }).increment('total_commission', amount);\n        await this.model('personal_promoters').where({ user_id: userId }).increment('month_commission', amount);\n      }\n\n      console.log(`✅ 佣金发放成功: 用户${userId} 获得${amount}元佣金`);\n      return { success: true, result: result };\n\n    } catch (error) {\n      console.error('佣金发放失败:', error);\n      throw error;\n    }\n  }\n};\n"]}