{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\coupon.js"], "names": ["Base", "require", "module", "exports", "indexAction", "page", "pageSize", "type", "status", "search", "get", "where", "is_delete", "list", "model", "order", "countSelect", "coupon", "data", "stats", "getCouponStats", "id", "success", "error", "think", "logger", "fail", "detailAction", "find", "isEmpty", "addAction", "post", "name", "discount_type", "discount_value", "start_time", "end_time", "Date", "code", "generateCouponCode", "existCoupon", "min_amount", "total_quantity", "per_user_limit", "auto_distribute", "add", "updateAction", "update", "deleteAction", "userCouponCount", "coupon_id", "count", "toggleStatusAction", "newStatus", "batchDistributeAction", "couponId", "userIds", "userType", "quantity", "targetUsers", "field", "select", "is_new_user", "length", "map", "successCount", "failCount", "user", "receivedCount", "user_id", "couponCode", "expireAt", "valid_days", "now", "coupon_code", "expire_at", "source", "message", "statisticsAction", "totalCoupons", "activeCoupons", "claimedCoupons", "usedCoupons", "totalDiscount", "sum", "totalReceived", "totalUsed", "usageRate", "toFixed", "timestamp", "toString", "random", "Math", "substr", "toUpperCase"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;;AAEAC,OAAOC,OAAP,GAAiB,cAAcH,IAAd,CAAmB;AAClC;;;AAGMI,aAAN,GAAoB;AAAA;;AAAA;AAClB,UAAI;AACF,cAAM,EAAEC,OAAO,CAAT,EAAYC,WAAW,EAAvB,EAA2BC,IAA3B,EAAiCC,MAAjC,EAAyCC,MAAzC,KAAoD,MAAKC,GAAL,EAA1D;;AAEA,YAAIC,QAAQ,EAAEC,WAAW,CAAb,EAAZ;AACA,YAAIL,IAAJ,EAAUI,MAAMJ,IAAN,GAAaA,IAAb;AACV,YAAIC,MAAJ,EAAYG,MAAMH,MAAN,GAAeA,MAAf;AACZ,YAAIC,MAAJ,EAAY;AACVE,gBAAM,WAAN,IAAqB,CAAC,MAAD,EAAU,IAAGF,MAAO,GAApB,CAArB;AACD;;AAED,cAAMI,OAAO,MAAM,MAAKC,KAAL,CAAW,SAAX,EAAsBH,KAAtB,CAA4BA,KAA5B,EAChBN,IADgB,CACXA,IADW,EACLC,QADK,EAEhBS,KAFgB,CAEV,iBAFU,EAGhBC,WAHgB,EAAnB;;AAKA;AACA,aAAK,IAAIC,MAAT,IAAmBJ,KAAKK,IAAxB,EAA8B;AAC5B,gBAAMC,QAAQ,MAAM,MAAKC,cAAL,CAAoBH,OAAOI,EAA3B,CAApB;AACAJ,iBAAOE,KAAP,GAAeA,KAAf;AACD;;AAED,eAAO,MAAKG,OAAL,CAAaT,IAAb,CAAP;AACD,OAtBD,CAsBE,OAAOU,KAAP,EAAc;AACdC,cAAMC,MAAN,CAAaF,KAAb,CAAmB,YAAnB,EAAiCA,KAAjC;AACA,eAAO,MAAKG,IAAL,CAAU,QAAV,CAAP;AACD;AA1BiB;AA2BnB;;AAED;;;AAGMC,cAAN,GAAqB;AAAA;;AAAA;AACnB,UAAI;AACF,cAAM,EAAEN,EAAF,KAAS,OAAKX,GAAL,EAAf;AACA,YAAI,CAACW,EAAL,EAAS;AACP,iBAAO,OAAKK,IAAL,CAAU,MAAV,CAAP;AACD;;AAED,cAAMT,SAAS,MAAM,OAAKH,KAAL,CAAW,SAAX,EAAsBH,KAAtB,CAA4B;AAC/CU,cAAIA,EAD2C;AAE/CT,qBAAW;AAFoC,SAA5B,EAGlBgB,IAHkB,EAArB;;AAKA,YAAIJ,MAAMK,OAAN,CAAcZ,MAAd,CAAJ,EAA2B;AACzB,iBAAO,OAAKS,IAAL,CAAU,QAAV,CAAP;AACD;;AAED;AACA,cAAMP,QAAQ,MAAM,OAAKC,cAAL,CAAoBC,EAApB,CAApB;AACAJ,eAAOE,KAAP,GAAeA,KAAf;;AAEA,eAAO,OAAKG,OAAL,CAAaL,MAAb,CAAP;AACD,OApBD,CAoBE,OAAOM,KAAP,EAAc;AACdC,cAAMC,MAAN,CAAaF,KAAb,CAAmB,YAAnB,EAAiCA,KAAjC;AACA,eAAO,OAAKG,IAAL,CAAU,QAAV,CAAP;AACD;AAxBkB;AAyBpB;;AAED;;;AAGMI,WAAN,GAAkB;AAAA;;AAAA;AAChB,UAAI;AACF,cAAMZ,OAAO,OAAKa,IAAL,EAAb;;AAEA;AACA,YAAI,CAACb,KAAKc,IAAN,IAAc,CAACd,KAAKX,IAApB,IAA4B,CAACW,KAAKe,aAAlC,IAAmD,CAACf,KAAKgB,cAA7D,EAA6E;AAC3E,iBAAO,OAAKR,IAAL,CAAU,OAAV,CAAP;AACD;;AAED;AACA,YAAI,CAACR,KAAKiB,UAAN,IAAoB,CAACjB,KAAKkB,QAA9B,EAAwC;AACtC,iBAAO,OAAKV,IAAL,CAAU,QAAV,CAAP;AACD;;AAED,YAAI,IAAIW,IAAJ,CAASnB,KAAKiB,UAAd,KAA6B,IAAIE,IAAJ,CAASnB,KAAKkB,QAAd,CAAjC,EAA0D;AACxD,iBAAO,OAAKV,IAAL,CAAU,cAAV,CAAP;AACD;;AAED;AACA,YAAI,CAACR,KAAKoB,IAAV,EAAgB;AACdpB,eAAKoB,IAAL,GAAY,OAAKC,kBAAL,EAAZ;AACD,SAFD,MAEO;AACL;AACA,gBAAMC,cAAc,MAAM,OAAK1B,KAAL,CAAW,SAAX,EAAsBH,KAAtB,CAA4B;AACpD2B,kBAAMpB,KAAKoB,IADyC;AAEpD1B,uBAAW;AAFyC,WAA5B,EAGvBgB,IAHuB,EAA1B;AAIA,cAAI,CAACJ,MAAMK,OAAN,CAAcW,WAAd,CAAL,EAAiC;AAC/B,mBAAO,OAAKd,IAAL,CAAU,UAAV,CAAP;AACD;AACF;;AAED;AACAR,aAAKuB,UAAL,GAAkBvB,KAAKuB,UAAL,IAAmB,CAArC;AACAvB,aAAKwB,cAAL,GAAsBxB,KAAKwB,cAAL,IAAuB,CAAC,CAA9C;AACAxB,aAAKyB,cAAL,GAAsBzB,KAAKyB,cAAL,IAAuB,CAA7C;AACAzB,aAAKV,MAAL,GAAcU,KAAKV,MAAL,IAAe,QAA7B;AACAU,aAAK0B,eAAL,GAAuB1B,KAAK0B,eAAL,IAAwB,CAA/C;;AAEA,cAAMvB,KAAK,MAAM,OAAKP,KAAL,CAAW,SAAX,EAAsB+B,GAAtB,CAA0B3B,IAA1B,CAAjB;AACA,eAAO,OAAKI,OAAL,CAAa,EAAED,EAAF,EAAb,CAAP;AACD,OAxCD,CAwCE,OAAOE,KAAP,EAAc;AACdC,cAAMC,MAAN,CAAaF,KAAb,CAAmB,UAAnB,EAA+BA,KAA/B;AACA,eAAO,OAAKG,IAAL,CAAU,MAAV,CAAP;AACD;AA5Ce;AA6CjB;;AAED;;;AAGMoB,cAAN,GAAqB;AAAA;;AAAA;AACnB,UAAI;AACF,cAAM,EAAEzB,EAAF,KAAS,OAAKU,IAAL,EAAf;AACA,YAAI,CAACV,EAAL,EAAS;AACP,iBAAO,OAAKK,IAAL,CAAU,MAAV,CAAP;AACD;;AAED,cAAMR,OAAO,OAAKa,IAAL,EAAb;AACA,eAAOb,KAAKG,EAAZ;;AAEA;AACA,cAAMJ,SAAS,MAAM,OAAKH,KAAL,CAAW,SAAX,EAAsBH,KAAtB,CAA4B;AAC/CU,cAAIA,EAD2C;AAE/CT,qBAAW;AAFoC,SAA5B,EAGlBgB,IAHkB,EAArB;;AAKA,YAAIJ,MAAMK,OAAN,CAAcZ,MAAd,CAAJ,EAA2B;AACzB,iBAAO,OAAKS,IAAL,CAAU,QAAV,CAAP;AACD;;AAED;AACA,YAAIR,KAAKoB,IAAL,IAAapB,KAAKoB,IAAL,KAAcrB,OAAOqB,IAAtC,EAA4C;AAC1C,gBAAME,cAAc,MAAM,OAAK1B,KAAL,CAAW,SAAX,EAAsBH,KAAtB,CAA4B;AACpD2B,kBAAMpB,KAAKoB,IADyC;AAEpD1B,uBAAW,CAFyC;AAGpDS,gBAAI,CAAC,IAAD,EAAOA,EAAP;AAHgD,WAA5B,EAIvBO,IAJuB,EAA1B;AAKA,cAAI,CAACJ,MAAMK,OAAN,CAAcW,WAAd,CAAL,EAAiC;AAC/B,mBAAO,OAAKd,IAAL,CAAU,UAAV,CAAP;AACD;AACF;;AAED;AACA,YAAIR,KAAKiB,UAAL,IAAmBjB,KAAKkB,QAA5B,EAAsC;AACpC,cAAI,IAAIC,IAAJ,CAASnB,KAAKiB,UAAd,KAA6B,IAAIE,IAAJ,CAASnB,KAAKkB,QAAd,CAAjC,EAA0D;AACxD,mBAAO,OAAKV,IAAL,CAAU,cAAV,CAAP;AACD;AACF;;AAED,cAAM,OAAKZ,KAAL,CAAW,SAAX,EAAsBH,KAAtB,CAA4B,EAAEU,EAAF,EAA5B,EAAoC0B,MAApC,CAA2C7B,IAA3C,CAAN;AACA,eAAO,OAAKI,OAAL,CAAa,MAAb,CAAP;AACD,OAxCD,CAwCE,OAAOC,KAAP,EAAc;AACdC,cAAMC,MAAN,CAAaF,KAAb,CAAmB,UAAnB,EAA+BA,KAA/B;AACA,eAAO,OAAKG,IAAL,CAAU,MAAV,CAAP;AACD;AA5CkB;AA6CpB;;AAED;;;AAGMsB,cAAN,GAAqB;AAAA;;AAAA;AACnB,UAAI;AACF,cAAM,EAAE3B,EAAF,KAAS,OAAKU,IAAL,EAAf;AACA,YAAI,CAACV,EAAL,EAAS;AACP,iBAAO,OAAKK,IAAL,CAAU,MAAV,CAAP;AACD;;AAED;AACA,cAAMuB,kBAAkB,MAAM,OAAKnC,KAAL,CAAW,cAAX,EAA2BH,KAA3B,CAAiC;AAC7DuC,qBAAW7B;AADkD,SAAjC,EAE3B8B,KAF2B,EAA9B;;AAIA,YAAIF,kBAAkB,CAAtB,EAAyB;AACvB,iBAAO,OAAKvB,IAAL,CAAU,iBAAV,CAAP;AACD;;AAED,cAAM,OAAKZ,KAAL,CAAW,SAAX,EAAsBH,KAAtB,CAA4B,EAAEU,EAAF,EAA5B,EAAoC0B,MAApC,CAA2C;AAC/CnC,qBAAW;AADoC,SAA3C,CAAN;;AAIA,eAAO,OAAKU,OAAL,CAAa,MAAb,CAAP;AACD,OApBD,CAoBE,OAAOC,KAAP,EAAc;AACdC,cAAMC,MAAN,CAAaF,KAAb,CAAmB,UAAnB,EAA+BA,KAA/B;AACA,eAAO,OAAKG,IAAL,CAAU,MAAV,CAAP;AACD;AAxBkB;AAyBpB;;AAED;;;AAGM0B,oBAAN,GAA2B;AAAA;;AAAA;AACzB,UAAI;AACF,cAAM,EAAE/B,EAAF,KAAS,OAAKU,IAAL,EAAf;AACA,YAAI,CAACV,EAAL,EAAS;AACP,iBAAO,OAAKK,IAAL,CAAU,MAAV,CAAP;AACD;;AAED,cAAMT,SAAS,MAAM,OAAKH,KAAL,CAAW,SAAX,EAAsBH,KAAtB,CAA4B;AAC/CU,cAAIA,EAD2C;AAE/CT,qBAAW;AAFoC,SAA5B,EAGlBgB,IAHkB,EAArB;;AAKA,YAAIJ,MAAMK,OAAN,CAAcZ,MAAd,CAAJ,EAA2B;AACzB,iBAAO,OAAKS,IAAL,CAAU,QAAV,CAAP;AACD;;AAED,cAAM2B,YAAYpC,OAAOT,MAAP,KAAkB,QAAlB,GAA6B,UAA7B,GAA0C,QAA5D;AACA,cAAM,OAAKM,KAAL,CAAW,SAAX,EAAsBH,KAAtB,CAA4B,EAAEU,EAAF,EAA5B,EAAoC0B,MAApC,CAA2C;AAC/CvC,kBAAQ6C;AADuC,SAA3C,CAAN;;AAIA,eAAO,OAAK/B,OAAL,CAAa,QAAb,CAAP;AACD,OArBD,CAqBE,OAAOC,KAAP,EAAc;AACdC,cAAMC,MAAN,CAAaF,KAAb,CAAmB,YAAnB,EAAiCA,KAAjC;AACA,eAAO,OAAKG,IAAL,CAAU,QAAV,CAAP;AACD;AAzBwB;AA0B1B;;AAED;;;AAGM4B,uBAAN,GAA8B;AAAA;;AAAA;AAC5B,UAAI;AACF,cAAM,EAAEC,QAAF,EAAYC,OAAZ,EAAqBC,QAArB,EAA+BC,QAA/B,KAA4C,OAAK3B,IAAL,EAAlD;;AAEA,YAAI,CAACwB,QAAL,EAAe;AACb,iBAAO,OAAK7B,IAAL,CAAU,QAAV,CAAP;AACD;;AAED,cAAMT,SAAS,MAAM,OAAKH,KAAL,CAAW,SAAX,EAAsBH,KAAtB,CAA4B;AAC/CU,cAAIkC,QAD2C;AAE/C3C,qBAAW;AAFoC,SAA5B,EAGlBgB,IAHkB,EAArB;;AAKA,YAAIJ,MAAMK,OAAN,CAAcZ,MAAd,CAAJ,EAA2B;AACzB,iBAAO,OAAKS,IAAL,CAAU,QAAV,CAAP;AACD;;AAED,YAAIiC,cAAc,EAAlB;;AAEA,YAAIF,aAAa,KAAjB,EAAwB;AACtBE,wBAAc,MAAM,OAAK7C,KAAL,CAAW,MAAX,EAAmBH,KAAnB,CAAyB,EAACC,WAAW,CAAZ,EAAzB,EAAyCgD,KAAzC,CAA+C,IAA/C,EAAqDC,MAArD,EAApB;AACD,SAFD,MAEO,IAAIJ,aAAa,KAAjB,EAAwB;AAC7BE,wBAAc,MAAM,OAAK7C,KAAL,CAAW,MAAX,EAAmBH,KAAnB,CAAyB;AAC3CmD,yBAAa,CAD8B;AAE3ClD,uBAAW;AAFgC,WAAzB,EAGjBgD,KAHiB,CAGX,IAHW,EAGLC,MAHK,EAApB;AAID,SALM,MAKA,IAAIL,WAAWA,QAAQO,MAAR,GAAiB,CAAhC,EAAmC;AACxCJ,wBAAcH,QAAQQ,GAAR,CAAY;AAAA,mBAAO,EAAC3C,EAAD,EAAP;AAAA,WAAZ,CAAd;AACD,SAFM,MAEA;AACL,iBAAO,OAAKK,IAAL,CAAU,SAAV,CAAP;AACD;;AAED,YAAIuC,eAAe,CAAnB;AACA,YAAIC,YAAY,CAAhB;;AAEA,aAAK,IAAIC,IAAT,IAAiBR,WAAjB,EAA8B;AAC5B,cAAI;AACF;AACA,kBAAMS,gBAAgB,MAAM,OAAKtD,KAAL,CAAW,cAAX,EAA2BH,KAA3B,CAAiC;AAC3D0D,uBAASF,KAAK9C,EAD6C;AAE3D6B,yBAAWK;AAFgD,aAAjC,EAGzBJ,KAHyB,EAA5B;;AAKA,gBAAIiB,iBAAiBnD,OAAO0B,cAA5B,EAA4C;AAC1CuB;AACA;AACD;;AAED,kBAAMI,aAAa,OAAK/B,kBAAL,EAAnB;AACA,kBAAMgC,WAAWtD,OAAOuD,UAAP,GACf,IAAInC,IAAJ,CAASA,KAAKoC,GAAL,KAAaxD,OAAOuD,UAAP,GAAoB,EAApB,GAAyB,EAAzB,GAA8B,EAA9B,GAAmC,IAAzD,CADe,GAEf,IAAInC,IAAJ,CAASpB,OAAOmB,QAAhB,CAFF;;AAIA,kBAAM,OAAKtB,KAAL,CAAW,cAAX,EAA2B+B,GAA3B,CAA+B;AACnCwB,uBAASF,KAAK9C,EADqB;AAEnC6B,yBAAWK,QAFwB;AAGnCmB,2BAAaJ,UAHsB;AAInCK,yBAAWJ,QAJwB;AAKnCK,sBAAQ;AAL2B,aAA/B,CAAN;;AAQAX;AACD,WA1BD,CA0BE,OAAO1C,KAAP,EAAc;AACd2C;AACD;AACF;;AAED,eAAO,OAAK5C,OAAL,CAAa;AAClBuD,mBAAU,UAASZ,YAAa,OAAMC,SAAU,GAD9B;AAElBD,sBAFkB;AAGlBC;AAHkB,SAAb,CAAP;AAKD,OAvED,CAuEE,OAAO3C,KAAP,EAAc;AACdC,cAAMC,MAAN,CAAaF,KAAb,CAAmB,YAAnB,EAAiCA,KAAjC;AACA,eAAO,OAAKG,IAAL,CAAU,QAAV,CAAP;AACD;AA3E2B;AA4E7B;;AAED;;;AAGMoD,kBAAN,GAAyB;AAAA;;AAAA;AACvB,UAAI;AACF,cAAMC,eAAe,MAAM,OAAKjE,KAAL,CAAW,SAAX,EAAsBH,KAAtB,CAA4B;AACrDC,qBAAW;AAD0C,SAA5B,EAExBuC,KAFwB,EAA3B;;AAIA,cAAM6B,gBAAgB,MAAM,OAAKlE,KAAL,CAAW,SAAX,EAAsBH,KAAtB,CAA4B;AACtDH,kBAAQ,QAD8C;AAEtDI,qBAAW;AAF2C,SAA5B,EAGzBuC,KAHyB,EAA5B;;AAKA,cAAM8B,iBAAiB,MAAM,OAAKnE,KAAL,CAAW,cAAX,EAA2BqC,KAA3B,EAA7B;;AAEA,cAAM+B,cAAc,MAAM,OAAKpE,KAAL,CAAW,cAAX,EAA2BH,KAA3B,CAAiC;AACzDH,kBAAQ;AADiD,SAAjC,EAEvB2C,KAFuB,EAA1B;;AAIA,cAAMgC,gBAAgB,OAAM,OAAKrE,KAAL,CAAW,mBAAX,EAAgCsE,GAAhC,CAAoC,iBAApC,CAAN,KAAgE,CAAtF;;AAEA,eAAO,OAAK9D,OAAL,CAAa;AAClByD,sBADkB;AAElBC,uBAFkB;AAGlBC,wBAHkB;AAIlBC,qBAJkB;AAKlBC;AALkB,SAAb,CAAP;AAOD,OAzBD,CAyBE,OAAO5D,KAAP,EAAc;AACdC,cAAMC,MAAN,CAAaF,KAAb,CAAmB,YAAnB,EAAiCA,KAAjC;AACA,eAAO,OAAKG,IAAL,CAAU,QAAV,CAAP;AACD;AA7BsB;AA8BxB;;AAED;;;AAGMN,gBAAN,CAAqBmC,QAArB,EAA+B;AAAA;;AAAA;AAC7B,YAAM8B,gBAAgB,MAAM,OAAKvE,KAAL,CAAW,cAAX,EACzBH,KADyB,CACnB,EAAEuC,WAAWK,QAAb,EADmB,EACMJ,KADN,EAA5B;;AAGA,YAAMmC,YAAY,MAAM,OAAKxE,KAAL,CAAW,cAAX,EACrBH,KADqB,CACf,EAAEuC,WAAWK,QAAb,EAAuB/C,QAAQ,MAA/B,EADe,EAC0B2C,KAD1B,EAAxB;;AAGA,YAAMgC,gBAAgB,OAAM,OAAKrE,KAAL,CAAW,mBAAX,EACzBH,KADyB,CACnB,EAAEuC,WAAWK,QAAb,EADmB,EAEzB6B,GAFyB,CAErB,iBAFqB,CAAN,KAEO,CAF7B;;AAIA,aAAO;AACLC,qBADK;AAELC,iBAFK;AAGLH,qBAHK;AAILI,mBAAWF,gBAAgB,CAAhB,GAAoB,CAACC,YAAYD,aAAZ,GAA4B,GAA7B,EAAkCG,OAAlC,CAA0C,CAA1C,CAApB,GAAmE;AAJzE,OAAP;AAX6B;AAiB9B;;AAED;;;AAGAjD,uBAAqB;AACnB,UAAMkD,YAAYpD,KAAKoC,GAAL,GAAWiB,QAAX,CAAoB,EAApB,CAAlB;AACA,UAAMC,SAASC,KAAKD,MAAL,GAAcD,QAAd,CAAuB,EAAvB,EAA2BG,MAA3B,CAAkC,CAAlC,EAAqC,CAArC,CAAf;AACA,WAAQ,MAAKJ,SAAU,GAAEE,MAAO,EAAzB,CAA2BG,WAA3B,EAAP;AACD;AAjXiC,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\coupon.js", "sourcesContent": ["const Base = require('./base.js');\n\nmodule.exports = class extends Base {\n  /**\n   * 获取优惠券列表\n   */\n  async indexAction() {\n    try {\n      const { page = 1, pageSize = 20, type, status, search } = this.get();\n\n      let where = { is_delete: 0 };\n      if (type) where.type = type;\n      if (status) where.status = status;\n      if (search) {\n        where['name|code'] = ['like', `%${search}%`];\n      }\n\n      const list = await this.model('coupons').where(where)\n        .page(page, pageSize)\n        .order('created_at DESC')\n        .countSelect();\n\n      // 统计每个优惠券的使用情况\n      for (let coupon of list.data) {\n        const stats = await this.getCouponStats(coupon.id);\n        coupon.stats = stats;\n      }\n\n      return this.success(list);\n    } catch (error) {\n      think.logger.error('获取优惠券列表失败:', error);\n      return this.fail('获取列表失败');\n    }\n  }\n\n  /**\n   * 获取优惠券详情\n   */\n  async detailAction() {\n    try {\n      const { id } = this.get();\n      if (!id) {\n        return this.fail('参数错误');\n      }\n\n      const coupon = await this.model('coupons').where({\n        id: id,\n        is_delete: 0\n      }).find();\n\n      if (think.isEmpty(coupon)) {\n        return this.fail('优惠券不存在');\n      }\n\n      // 获取统计信息\n      const stats = await this.getCouponStats(id);\n      coupon.stats = stats;\n\n      return this.success(coupon);\n    } catch (error) {\n      think.logger.error('获取优惠券详情失败:', error);\n      return this.fail('获取详情失败');\n    }\n  }\n\n  /**\n   * 创建优惠券\n   */\n  async addAction() {\n    try {\n      const data = this.post();\n\n      // 验证必填字段\n      if (!data.name || !data.type || !data.discount_type || !data.discount_value) {\n        return this.fail('参数不完整');\n      }\n\n      // 验证时间\n      if (!data.start_time || !data.end_time) {\n        return this.fail('请设置有效期');\n      }\n\n      if (new Date(data.start_time) >= new Date(data.end_time)) {\n        return this.fail('开始时间必须早于结束时间');\n      }\n\n      // 生成优惠券代码\n      if (!data.code) {\n        data.code = this.generateCouponCode();\n      } else {\n        // 检查代码是否重复\n        const existCoupon = await this.model('coupons').where({\n          code: data.code,\n          is_delete: 0\n        }).find();\n        if (!think.isEmpty(existCoupon)) {\n          return this.fail('优惠券代码已存在');\n        }\n      }\n\n      // 设置默认值\n      data.min_amount = data.min_amount || 0;\n      data.total_quantity = data.total_quantity || -1;\n      data.per_user_limit = data.per_user_limit || 1;\n      data.status = data.status || 'active';\n      data.auto_distribute = data.auto_distribute || 0;\n\n      const id = await this.model('coupons').add(data);\n      return this.success({ id });\n    } catch (error) {\n      think.logger.error('创建优惠券失败:', error);\n      return this.fail('创建失败');\n    }\n  }\n\n  /**\n   * 更新优惠券\n   */\n  async updateAction() {\n    try {\n      const { id } = this.post();\n      if (!id) {\n        return this.fail('参数错误');\n      }\n\n      const data = this.post();\n      delete data.id;\n\n      // 检查优惠券是否存在\n      const coupon = await this.model('coupons').where({\n        id: id,\n        is_delete: 0\n      }).find();\n\n      if (think.isEmpty(coupon)) {\n        return this.fail('优惠券不存在');\n      }\n\n      // 如果修改了代码，检查是否重复\n      if (data.code && data.code !== coupon.code) {\n        const existCoupon = await this.model('coupons').where({\n          code: data.code,\n          is_delete: 0,\n          id: ['!=', id]\n        }).find();\n        if (!think.isEmpty(existCoupon)) {\n          return this.fail('优惠券代码已存在');\n        }\n      }\n\n      // 验证时间\n      if (data.start_time && data.end_time) {\n        if (new Date(data.start_time) >= new Date(data.end_time)) {\n          return this.fail('开始时间必须早于结束时间');\n        }\n      }\n\n      await this.model('coupons').where({ id }).update(data);\n      return this.success('更新成功');\n    } catch (error) {\n      think.logger.error('更新优惠券失败:', error);\n      return this.fail('更新失败');\n    }\n  }\n\n  /**\n   * 删除优惠券\n   */\n  async deleteAction() {\n    try {\n      const { id } = this.post();\n      if (!id) {\n        return this.fail('参数错误');\n      }\n\n      // 检查是否有用户已领取\n      const userCouponCount = await this.model('user_coupons').where({\n        coupon_id: id\n      }).count();\n\n      if (userCouponCount > 0) {\n        return this.fail('该优惠券已有用户领取，无法删除');\n      }\n\n      await this.model('coupons').where({ id }).update({\n        is_delete: 1\n      });\n\n      return this.success('删除成功');\n    } catch (error) {\n      think.logger.error('删除优惠券失败:', error);\n      return this.fail('删除失败');\n    }\n  }\n\n  /**\n   * 切换优惠券状态\n   */\n  async toggleStatusAction() {\n    try {\n      const { id } = this.post();\n      if (!id) {\n        return this.fail('参数错误');\n      }\n\n      const coupon = await this.model('coupons').where({\n        id: id,\n        is_delete: 0\n      }).find();\n\n      if (think.isEmpty(coupon)) {\n        return this.fail('优惠券不存在');\n      }\n\n      const newStatus = coupon.status === 'active' ? 'disabled' : 'active';\n      await this.model('coupons').where({ id }).update({\n        status: newStatus\n      });\n\n      return this.success('状态更新成功');\n    } catch (error) {\n      think.logger.error('切换优惠券状态失败:', error);\n      return this.fail('状态更新失败');\n    }\n  }\n\n  /**\n   * 批量发放优惠券\n   */\n  async batchDistributeAction() {\n    try {\n      const { couponId, userIds, userType, quantity } = this.post();\n\n      if (!couponId) {\n        return this.fail('请选择优惠券');\n      }\n\n      const coupon = await this.model('coupons').where({\n        id: couponId,\n        is_delete: 0\n      }).find();\n\n      if (think.isEmpty(coupon)) {\n        return this.fail('优惠券不存在');\n      }\n\n      let targetUsers = [];\n\n      if (userType === 'all') {\n        targetUsers = await this.model('user').where({is_delete: 0}).field('id').select();\n      } else if (userType === 'new') {\n        targetUsers = await this.model('user').where({\n          is_new_user: 1,\n          is_delete: 0\n        }).field('id').select();\n      } else if (userIds && userIds.length > 0) {\n        targetUsers = userIds.map(id => ({id}));\n      } else {\n        return this.fail('请选择发放对象');\n      }\n\n      let successCount = 0;\n      let failCount = 0;\n\n      for (let user of targetUsers) {\n        try {\n          // 检查用户是否已达到领取上限\n          const receivedCount = await this.model('user_coupons').where({\n            user_id: user.id,\n            coupon_id: couponId\n          }).count();\n\n          if (receivedCount >= coupon.per_user_limit) {\n            failCount++;\n            continue;\n          }\n\n          const couponCode = this.generateCouponCode();\n          const expireAt = coupon.valid_days ? \n            new Date(Date.now() + coupon.valid_days * 24 * 60 * 60 * 1000) :\n            new Date(coupon.end_time);\n\n          await this.model('user_coupons').add({\n            user_id: user.id,\n            coupon_id: couponId,\n            coupon_code: couponCode,\n            expire_at: expireAt,\n            source: 'batch'\n          });\n\n          successCount++;\n        } catch (error) {\n          failCount++;\n        }\n      }\n\n      return this.success({\n        message: `发放完成，成功${successCount}个，失败${failCount}个`,\n        successCount,\n        failCount\n      });\n    } catch (error) {\n      think.logger.error('批量发放优惠券失败:', error);\n      return this.fail('批量发放失败');\n    }\n  }\n\n  /**\n   * 获取优惠券统计信息\n   */\n  async statisticsAction() {\n    try {\n      const totalCoupons = await this.model('coupons').where({\n        is_delete: 0\n      }).count();\n\n      const activeCoupons = await this.model('coupons').where({\n        status: 'active',\n        is_delete: 0\n      }).count();\n\n      const claimedCoupons = await this.model('user_coupons').count();\n\n      const usedCoupons = await this.model('user_coupons').where({\n        status: 'used'\n      }).count();\n\n      const totalDiscount = await this.model('coupon_usage_logs').sum('discount_amount') || 0;\n\n      return this.success({\n        totalCoupons,\n        activeCoupons,\n        claimedCoupons,\n        usedCoupons,\n        totalDiscount\n      });\n    } catch (error) {\n      think.logger.error('获取优惠券统计失败:', error);\n      return this.fail('获取统计失败');\n    }\n  }\n\n  /**\n   * 获取单个优惠券的统计信息\n   */\n  async getCouponStats(couponId) {\n    const totalReceived = await this.model('user_coupons')\n      .where({ coupon_id: couponId }).count();\n\n    const totalUsed = await this.model('user_coupons')\n      .where({ coupon_id: couponId, status: 'used' }).count();\n\n    const totalDiscount = await this.model('coupon_usage_logs')\n      .where({ coupon_id: couponId })\n      .sum('discount_amount') || 0;\n\n    return {\n      totalReceived,\n      totalUsed,\n      totalDiscount,\n      usageRate: totalReceived > 0 ? (totalUsed / totalReceived * 100).toFixed(2) : 0\n    };\n  }\n\n  /**\n   * 生成优惠券代码\n   */\n  generateCouponCode() {\n    const timestamp = Date.now().toString(36);\n    const random = Math.random().toString(36).substr(2, 5);\n    return `CPN${timestamp}${random}`.toUpperCase();\n  }\n};\n"]}