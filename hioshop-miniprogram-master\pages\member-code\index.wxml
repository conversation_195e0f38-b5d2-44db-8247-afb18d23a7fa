<view class="container">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <view class="nav-left" bindtap="goBack">
      <image class="back-icon" src="/images/icon/arrow-left.png"></image>
    </view>
    <view class="nav-title">会员码</view>
    <view class="nav-right"></view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 个人信息区域 -->
    <view class="user-info-card">
      <view class="user-avatar-wrap">
        <image class="user-avatar" src="{{userInfo.avatar || '/images/icon/default_avatar_big.jpg'}}" mode="aspectFill"></image>
      </view>
      <view class="user-details">
        <view class="user-name-row">
          <text class="user-name">{{userInfo.nickname || '会员用户'}}</text>
          <view class="member-badge">黄金会员</view>
        </view>
        <view class="member-validity">会员有效期至：{{validityDate}}</view>
      </view>
    </view>

    <!-- 会员码展示区域 -->
    <view class="qr-code-section">
      <view class="qr-code-container">
        <view class="qr-code-wrapper">
          <canvas canvas-id="qrcode" class="qr-canvas" wx:if="{{qrCodeGenerated}}"></canvas>
          <view class="qr-loading" wx:else>
            <text>二维码生成中...</text>
          </view>
        </view>
      </view>
      <view class="qr-description">
        <text class="qr-title">会员专属码</text>
        <text class="qr-subtitle">向商家出示此码即可享受会员权益</text>
      </view>
    </view>

    <!-- 会员权益提示 -->
    <view class="benefits-section">
      <view class="benefits-header">
        <image class="crown-icon" src="/images/icon/crown.png"></image>
        <text class="benefits-title">会员专享权益</text>
      </view>
      <view class="benefits-grid">
        <view class="benefit-item">
          <view class="benefit-icon-wrap">
            <image class="benefit-icon" src="/images/icon/discount.png"></image>
          </view>
          <text class="benefit-text">专属折扣</text>
        </view>
        <view class="benefit-item">
          <view class="benefit-icon-wrap">
            <image class="benefit-icon" src="/images/icon/gift.png"></image>
          </view>
          <text class="benefit-text">生日礼包</text>
        </view>
        <view class="benefit-item">
          <view class="benefit-icon-wrap">
            <image class="benefit-icon" src="/images/icon/service.png"></image>
          </view>
          <text class="benefit-text">专属客服</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部功能按钮 -->
  <view class="bottom-actions">
    <view class="action-btn primary" bindtap="saveQrCode">
      <image class="btn-icon" src="/images/icon/download.png"></image>
      <text>保存二维码</text>
    </view>
    <view class="action-btn secondary" bindtap="shareQrCode">
      <image class="btn-icon" src="/images/icon/share.png"></image>
      <text>分享</text>
    </view>
  </view>

  <!-- 分享弹窗 -->
  <view class="share-modal {{showShareModal ? 'show' : ''}}" bindtap="hideShareModal">
    <view class="share-content" catchtap="stopPropagation">
      <view class="share-header">
        <text class="share-title">分享到</text>
        <image class="close-icon" src="/images/icon/close.png" bindtap="hideShareModal"></image>
      </view>
      <view class="share-options">
        <view class="share-option" bindtap="shareToFriend">
          <image class="share-icon" src="/images/icon/wechat.png"></image>
          <text>微信好友</text>
        </view>
        <view class="share-option" bindtap="shareToMoments">
          <image class="share-icon" src="/images/icon/moments.png"></image>
          <text>朋友圈</text>
        </view>
      </view>
      <view class="share-cancel" bindtap="hideShareModal">取消</view>
    </view>
  </view>

  <!-- 成功提示 -->
  <view class="success-toast {{showSuccessToast ? 'show' : ''}}">
    <text>{{toastMessage}}</text>
  </view>
</view>
