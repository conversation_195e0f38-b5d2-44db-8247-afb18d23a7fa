<template>
  <div class="data-overview-page">
    <!-- 页面头部 -->
    <div class="page-header bg-white shadow-sm border-b">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">数据概览</h1>
            <p class="text-sm text-gray-500 mt-1">分销系统核心数据汇总与趋势分析</p>
          </div>
          <div class="flex items-center space-x-3">
            <select
              v-model="selectedPeriod"
              @change="loadData"
              class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="7">近7天</option>
              <option value="30">近30天</option>
              <option value="90">近90天</option>
            </select>
            <button @click="refreshData" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <i class="ri-refresh-line mr-2"></i>
              刷新数据
            </button>
            <button @click="exportReport" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <i class="ri-download-line mr-2"></i>
              导出报告
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="p-6">
      <!-- 核心指标卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- 总销售额 -->
        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-6 text-white">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-blue-100 text-sm">总销售额</p>
              <p class="text-3xl font-bold mt-2">¥{{ formatNumber(overview.totalSales) }}</p>
              <div class="flex items-center mt-3">
                <i :class="['ri-arrow-up-line text-sm mr-1', overview.salesGrowth >= 0 ? 'text-green-300' : 'text-red-300']"></i>
                <span class="text-sm text-blue-100">{{ Math.abs(overview.salesGrowth) }}% 较上期</span>
              </div>
            </div>
            <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <i class="ri-money-dollar-circle-line text-2xl"></i>
            </div>
          </div>
        </div>

        <!-- 总佣金 -->
        <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-6 text-white">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-green-100 text-sm">总佣金</p>
              <p class="text-3xl font-bold mt-2">¥{{ formatNumber(overview.totalCommission) }}</p>
              <div class="flex items-center mt-3">
                <i :class="['ri-arrow-up-line text-sm mr-1', overview.commissionGrowth >= 0 ? 'text-green-300' : 'text-red-300']"></i>
                <span class="text-sm text-green-100">{{ Math.abs(overview.commissionGrowth) }}% 较上期</span>
              </div>
            </div>
            <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <i class="ri-hand-coin-line text-2xl"></i>
            </div>
          </div>
        </div>

        <!-- 活跃分销员 -->
        <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl p-6 text-white">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-purple-100 text-sm">活跃分销员</p>
              <p class="text-3xl font-bold mt-2">{{ overview.activeDistributors }}</p>
              <div class="flex items-center mt-3">
                <i :class="['ri-arrow-up-line text-sm mr-1', overview.distributorGrowth >= 0 ? 'text-green-300' : 'text-red-300']"></i>
                <span class="text-sm text-purple-100">{{ Math.abs(overview.distributorGrowth) }}% 较上期</span>
              </div>
            </div>
            <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <i class="ri-team-line text-2xl"></i>
            </div>
          </div>
        </div>

        <!-- 订单转化率 -->
        <div class="bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl p-6 text-white">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-orange-100 text-sm">订单转化率</p>
              <p class="text-3xl font-bold mt-2">{{ overview.conversionRate }}%</p>
              <div class="flex items-center mt-3">
                <i :class="['ri-arrow-up-line text-sm mr-1', overview.conversionGrowth >= 0 ? 'text-green-300' : 'text-red-300']"></i>
                <span class="text-sm text-orange-100">{{ Math.abs(overview.conversionGrowth) }}% 较上期</span>
              </div>
            </div>
            <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <i class="ri-line-chart-line text-2xl"></i>
            </div>
          </div>
        </div>
      </div>
      <!-- 图表区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- 销售趋势图 -->
        <div class="bg-white rounded-xl shadow-sm border p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">销售趋势</h3>
            <div class="flex items-center space-x-2">
              <button
                v-for="type in chartTypes"
                :key="type.value"
                @click="salesChartType = type.value"
                :class="[
                  'px-3 py-1 text-sm rounded-lg transition-colors',
                  salesChartType === type.value
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                ]"
              >
                {{ type.label }}
              </button>
            </div>
          </div>
          <div class="h-80">
            <canvas ref="salesChart"></canvas>
          </div>
        </div>

        <!-- 分销员等级分布 -->
        <div class="bg-white rounded-xl shadow-sm border p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">分销员等级分布</h3>
            <div class="text-sm text-gray-500">总计 {{ overview.totalDistributors }} 人</div>
          </div>
          <div class="h-80">
            <canvas ref="levelChart"></canvas>
          </div>
        </div>
      </div>
      <!-- 详细数据表格 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- 热门商品排行 -->
        <div class="bg-white rounded-xl shadow-sm border p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-6">热门商品排行</h3>
          <div class="space-y-4">
            <div
              v-for="(product, index) in topProducts"
              :key="product.id"
              class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
            >
              <div class="flex items-center">
                <div
                  :class="[
                    'w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-4',
                    index === 0 ? 'bg-yellow-100 text-yellow-800' :
                    index === 1 ? 'bg-gray-100 text-gray-800' :
                    index === 2 ? 'bg-orange-100 text-orange-800' :
                    'bg-blue-100 text-blue-800'
                  ]"
                >
                  {{ index + 1 }}
                </div>
                <img
                  :src="product.image"
                  :alt="product.name"
                  class="w-12 h-12 rounded-lg object-cover mr-4"
                />
                <div>
                  <p class="font-medium text-gray-900">{{ product.name }}</p>
                  <p class="text-sm text-gray-500">销量: {{ product.sales }}</p>
                </div>
              </div>
              <div class="text-right">
                <p class="font-medium text-green-600">¥{{ product.revenue }}</p>
                <p class="text-sm text-gray-500">佣金: ¥{{ product.commission }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 顶级分销员 -->
        <div class="bg-white rounded-xl shadow-sm border p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-6">顶级分销员</h3>
          <div class="space-y-4">
            <div
              v-for="(distributor, index) in topDistributors"
              :key="distributor.id"
              class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
            >
              <div class="flex items-center">
                <div
                  :class="[
                    'w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-4',
                    index === 0 ? 'bg-yellow-100 text-yellow-800' :
                    index === 1 ? 'bg-gray-100 text-gray-800' :
                    index === 2 ? 'bg-orange-100 text-orange-800' :
                    'bg-blue-100 text-blue-800'
                  ]"
                >
                  {{ index + 1 }}
                </div>
                <img
                  :src="distributor.avatar"
                  :alt="distributor.name"
                  class="w-12 h-12 rounded-full object-cover mr-4"
                />
                <div>
                  <p class="font-medium text-gray-900">{{ distributor.name }}</p>
                  <p class="text-sm text-gray-500">{{ distributor.level }}</p>
                </div>
              </div>
              <div class="text-right">
                <p class="font-medium text-blue-600">¥{{ distributor.sales }}</p>
                <p class="text-sm text-gray-500">佣金: ¥{{ distributor.commission }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 实时数据流 -->
      <div class="bg-white rounded-xl shadow-sm border p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-900">实时数据流</h3>
          <div class="flex items-center text-sm text-gray-500">
            <div class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
            实时更新
          </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <p class="text-2xl font-bold text-blue-600">{{ realTimeData.todayOrders }}</p>
            <p class="text-sm text-gray-600 mt-1">今日订单</p>
          </div>
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <p class="text-2xl font-bold text-green-600">¥{{ formatNumber(realTimeData.todayRevenue) }}</p>
            <p class="text-sm text-gray-600 mt-1">今日收入</p>
          </div>
          <div class="text-center p-4 bg-purple-50 rounded-lg">
            <p class="text-2xl font-bold text-purple-600">{{ realTimeData.activeUsers }}</p>
            <p class="text-sm text-gray-600 mt-1">在线用户</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'
import { getOverviewData, getChartData, getTopProducts, getTopDistributors } from '@/api/distribution'

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

export default {
  name: 'DataOverviewPage',
  data() {
    return {
      // 时间周期
      selectedPeriod: '30',

      // 概览数据
      overview: {
        totalSales: 0,
        salesGrowth: 0,
        totalCommission: 0,
        commissionGrowth: 0,
        activeDistributors: 0,
        distributorGrowth: 0,
        conversionRate: 0,
        conversionGrowth: 0,
        totalDistributors: 0
      },

      // 图表类型
      chartTypes: [
        { label: '销售额', value: 'sales' },
        { label: '订单数', value: 'orders' },
        { label: '佣金', value: 'commission' }
      ],
      salesChartType: 'sales',

      // 图表实例
      salesChartInstance: null,
      levelChartInstance: null,

      // 热门商品
      topProducts: [],

      // 顶级分销员
      topDistributors: [],

      // 实时数据
      realTimeData: {
        todayOrders: 0,
        todayRevenue: 0,
        activeUsers: 0
      }
    }
  },

  mounted() {
    console.log('数据概览页面已加载');
    this.loadData();
    this.initCharts();
    this.startRealTimeUpdate();
  },

  beforeDestroy() {
    // 清理图表实例
    if (this.salesChartInstance) {
      this.salesChartInstance.destroy();
    }
    if (this.levelChartInstance) {
      this.levelChartInstance.destroy();
    }
  },

  methods: {
    // 加载数据
    async loadData() {
      try {
        await Promise.all([
          this.loadOverviewData(),
          this.loadTopProducts(),
          this.loadTopDistributors(),
          this.updateCharts()
        ]);
      } catch (error) {
        console.error('加载数据失败:', error);
        this.loadMockData();
      }
    },

    // 加载概览数据
    async loadOverviewData() {
      try {
        const response = await getOverviewData({ period: this.selectedPeriod });

        if (response.data && response.data.errno === 0) {
          this.overview = response.data.data;
        } else {
          throw new Error('API返回错误');
        }
      } catch (error) {
        console.error('加载概览数据失败:', error);
        // 使用模拟数据
        this.overview = {
          totalSales: 1856420,
          salesGrowth: 12.5,
          totalCommission: 185642,
          commissionGrowth: 8.3,
          activeDistributors: 156,
          distributorGrowth: 15.2,
          conversionRate: 3.8,
          conversionGrowth: 2.1,
          totalDistributors: 189
        };
      }
    },

    // 加载热门商品
    async loadTopProducts() {
      try {
        const response = await getTopProducts({ period: this.selectedPeriod, limit: 5 });

        if (response.data && response.data.errno === 0) {
          this.topProducts = response.data.data;
        } else {
          throw new Error('API返回错误');
        }
      } catch (error) {
        console.error('加载热门商品失败:', error);
        // 使用模拟数据
        this.topProducts = [
          {
            id: 1,
            name: '轻奢纯棉刺绣水洗四件套',
            image: 'http://yanxuan.nosdn.127.net/8ab2d3287af0cefa2cc539e40600621d.png',
            sales: 168,
            revenue: '150,832.00',
            commission: '22,624.80'
          },
          {
            id: 2,
            name: '秋冬保暖加厚澳洲羊毛被',
            image: 'http://yanxuan.nosdn.127.net/66425d1ed50b3968fed27c822fdd32e0.png',
            sales: 142,
            revenue: '65,178.00',
            commission: '7,821.36'
          },
          {
            id: 3,
            name: '双宫茧桑蚕丝被 空调被',
            image: 'http://yanxuan.nosdn.127.net/583812520c68ca7995b6fac4c67ae2c7.png',
            sales: 98,
            revenue: '68,502.00',
            commission: '10,275.30'
          }
        ];
      }
    },

    // 加载顶级分销员
    async loadTopDistributors() {
      try {
        const response = await getTopDistributors({ period: this.selectedPeriod, limit: 5 });

        if (response.data && response.data.errno === 0) {
          this.topDistributors = response.data.data;
        } else {
          throw new Error('API返回错误');
        }
      } catch (error) {
        console.error('加载顶级分销员失败:', error);
        // 使用模拟数据
        this.topDistributors = [
          {
            id: 1,
            name: '张团长',
            avatar: 'https://ui-avatars.com/api/?name=张团长&background=random&size=128',
            level: '团长',
            sales: '125,680.00',
            commission: '18,852.00'
          },
          {
            id: 2,
            name: '李分销',
            avatar: 'https://ui-avatars.com/api/?name=李分销&background=random&size=128',
            level: '一级分销',
            sales: '98,420.00',
            commission: '11,810.40'
          },
          {
            id: 3,
            name: '王小销',
            avatar: 'https://ui-avatars.com/api/?name=王小销&background=random&size=128',
            level: '二级分销',
            sales: '76,350.00',
            commission: '6,108.00'
          }
        ];
      }
    },

    // 初始化图表
    initCharts() {
      this.$nextTick(() => {
        this.initSalesChart();
        this.initLevelChart();
      });
    },

    // 初始化销售趋势图
    initSalesChart() {
      const ctx = this.$refs.salesChart;
      if (!ctx) return;

      // 模拟数据
      const labels = [];
      const data = [];
      const now = new Date();

      for (let i = parseInt(this.selectedPeriod) - 1; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
        data.push(Math.floor(Math.random() * 50000) + 10000);
      }

      this.salesChartInstance = new ChartJS(ctx, {
        type: 'line',
        data: {
          labels: labels,
          datasets: [{
            label: '销售额',
            data: data,
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                color: 'rgba(0, 0, 0, 0.05)'
              },
              ticks: {
                callback: function(value) {
                  return '¥' + (value / 1000).toFixed(0) + 'k';
                }
              }
            },
            x: {
              grid: {
                display: false
              }
            }
          }
        }
      });
    },

    // 初始化等级分布图
    initLevelChart() {
      const ctx = this.$refs.levelChart;
      if (!ctx) return;

      this.levelChartInstance = new ChartJS(ctx, {
        type: 'doughnut',
        data: {
          labels: ['团长', '一级分销', '二级分销'],
          datasets: [{
            data: [23, 67, 99],
            backgroundColor: [
              'rgb(239, 68, 68)',
              'rgb(59, 130, 246)',
              'rgb(34, 197, 94)'
            ],
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                padding: 20,
                usePointStyle: true
              }
            }
          }
        }
      });
    },

    // 开始实时数据更新
    startRealTimeUpdate() {
      // 初始化实时数据
      this.updateRealTimeData();

      // 每30秒更新一次实时数据
      setInterval(() => {
        this.updateRealTimeData();
      }, 30000);
    },

    // 更新实时数据
    updateRealTimeData() {
      // 模拟实时数据
      this.realTimeData = {
        todayOrders: Math.floor(Math.random() * 100) + 50,
        todayRevenue: Math.floor(Math.random() * 50000) + 20000,
        activeUsers: Math.floor(Math.random() * 50) + 20
      };
    },

    // 格式化数字
    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
      }
      return num.toString();
    },

    // 刷新数据
    async refreshData() {
      await this.loadData();
      this.$message.success('数据刷新成功');
    },

    // 导出报告
    exportReport() {
      console.log('导出数据报告');
      this.$message.info('报告导出功能开发中');
    }
  },

  watch: {
    // 监听图表类型变化
    salesChartType() {
      this.updateCharts();
    }
  }
}
</script>

<style scoped>
.data-overview-page {
  min-height: 100vh;
  background-color: #f9fafb;
}

.page-header {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 渐变卡片样式 */
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-from), var(--tw-gradient-to));
}

/* 动画效果 */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* 图表容器 */
.h-80 {
  height: 20rem;
}

/* 卡片悬停效果 */
.bg-white:hover {
  transform: translateY(-2px);
  transition: all 0.3s ease;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 排名徽章 */
.w-8.h-8 {
  transition: all 0.3s ease;
}

.w-8.h-8:hover {
  transform: scale(1.1);
}

/* 实时数据指示器 */
.w-2.h-2 {
  width: 0.5rem;
  height: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

/* 图标样式 */
.ri-arrow-up-line {
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

.ri-arrow-down-line {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}

/* 数据卡片特殊效果 */
.bg-blue-50:hover,
.bg-green-50:hover,
.bg-purple-50:hover {
  background-color: rgba(59, 130, 246, 0.1);
  transition: background-color 0.3s ease;
}

/* 图表按钮组 */
.space-x-2 > * + * {
  margin-left: 0.5rem;
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
