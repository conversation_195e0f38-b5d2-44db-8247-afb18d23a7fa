{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\weChat.js"], "names": ["Base", "require", "crypto", "checkSignature", "params", "key", "timestamp", "nonce", "sort", "join", "sha1", "createHash", "update", "a", "digest", "b", "signature", "module", "exports", "receiveAction", "model", "add", "name", "rule_content", "success", "notifyAction", "data", "post", "ToUserName", "FromUserName", "CreateTime", "MsgType", "Content", "MsgId", "tokenServer", "think", "service", "token", "getAccessToken", "sendTextMessage", "Event", "console", "log"], "mappings": ";;AAAA;AACA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;AACA;;;AAGA,SAASE,cAAT,CAAwBC,MAAxB,EAAgC;AAC5B;AACA,QAAIC,MAAM,CAAC,gCAAD,EAAmCD,OAAOE,SAA1C,EAAqDF,OAAOG,KAA5D,EAAmEC,IAAnE,GAA0EC,IAA1E,CAA+E,EAA/E,CAAV;AACA;AACA,QAAIC,OAAOR,OAAOS,UAAP,CAAkB,MAAlB,CAAX;AACA;AACAD,SAAKE,MAAL,CAAYP,GAAZ;AACA,QAAIQ,IAAIH,KAAKI,MAAL,CAAY,KAAZ,CAAR;AACA,QAAIC,IAAIX,OAAOY,SAAf;AACA,QAAIH,KAAKE,CAAT,EAAY;AACR,eAAO,IAAP;AACH;AACD;AACH;AACDE,OAAOC,OAAP,GAAiB,cAAclB,IAAd,CAAmB;AAC1BmB,iBAAN,GAAsB;AAAA;;AAAA;AAClB;AACA,kBAAM,MAAKC,KAAL,CAAW,OAAX,EAAoBC,GAApB,CAAwB;AAC1BC,sBAAM,CADoB;AAE1BC,8BAAc;AAFY,aAAxB,CAAN;AAIA,mBAAO,MAAKC,OAAL,CAAa,MAAb,CAAP;AANkB;AAOrB;AACKC,gBAAN,GAAqB;AAAA;;AAAA;AACjB;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAM,OAAKL,KAAL,CAAW,OAAX,EAAoBC,GAApB,CAAwB;AAC1BC,sBAAM,CADoB;AAE1BC,8BAAc;AAFY,aAAxB,CAAN;AAIA,mBAAO,OAAKC,OAAL,CAAa,MAAb,CAAP;AACA,kBAAME,OAAO,OAAKC,IAAL,CAAU,EAAV,CAAb;AACA,kBAAM;AACFC,0BADE;AAEFC,4BAFE;AAGFC,0BAHE;AAIFC,uBAJE;AAKFC,uBALE;AAMFC;AANE,gBAOFP,IAPJ;AAQA;AACA,kBAAMQ,cAAcC,MAAMC,OAAN,CAAc,QAAd,EAAwB,KAAxB,CAApB;AACA,kBAAMC,QAAQ,MAAMH,YAAYI,cAAZ,EAApB;AACA;AACA,oBAAQZ,KAAKK,OAAb;AACI,qBAAK,MAAL;AACI;AAAE;AACE,8BAAMG,YAAYK,eAAZ,CAA4Bb,IAA5B,EAAkCW,KAAlC,CAAN;AACA;AACH;AACD;AACA;AACA;AACA;AACA;AACJ,qBAAK,OAAL;AACI;AACI,4BAAIX,KAAKc,KAAL,IAAc,wBAAlB,EAA4C,CAAE;AAC1C;AACA;AACH,yBAHD,MAGO,IAAId,KAAKc,KAAL,IAAc,mBAAlB,EAAuC;AAAE;AAC5CC,oCAAQC,GAAR,CAAY,UAAZ;AACH;AACD;AACH;AApBT;AAsBA;AAnDiB;AAoDpB;AA7D+B,CAApC", "file": "..\\..\\..\\src\\api\\controller\\weChat.js", "sourcesContent": ["/* eslint-disable no-multi-spaces */\nconst Base = require('./base.js');\nconst crypto = require(\"crypto\");\n/**\n *  检验signature对请求进行校验\n */\nfunction checkSignature(params) {\n    //token 就是自己填写的令牌\n    var key = ['huixianshuichanhuixianshuichan', params.timestamp, params.nonce].sort().join('');\n    //将token （自己设置的） 、timestamp（时间戳）、nonce（随机数）三个参数进行字典排序\n    var sha1 = crypto.createHash('sha1');\n    //将上面三个字符串拼接成一个字符串再进行sha1加密\n    sha1.update(key);\n    let a = sha1.digest('hex');\n    let b = params.signature;\n    if (a == b) {\n        return true;\n    }\n    //将加密后的字符串与signature进行对比，若成功，返回success。如果通过验证，则，注释掉这个函数\n}\nmodule.exports = class extends Base {\n    async receiveAction() {\n        // const value = this.post('value');\n        await this.model('rules').add({\n            name: 9,\n            rule_content: '哈哈'\n        });\n        return this.success('haha');\n    }\n    async notifyAction() {\n        /**\n         *  服务器配置校验，校验后，注释掉！\n         */\n        // const info = this.get(\"\");\n        // if (checkSignature(info)){\n        //     return this.json(info.echostr);\n        // }\n        // else {\n        //     return this.fail(\"error\");\n        // }\n        await this.model('rules').add({\n            name: 9,\n            rule_content: '哈哈'\n        });\n        return this.success('haha');\n        const data = this.post(\"\");\n        const {\n            ToUserName,\n            FromUserName,\n            CreateTime,\n            MsgType,\n            Content,\n            MsgId\n        } = data;\n        // console.log(\"data: \", JSON.stringify(data));\n        const tokenServer = think.service('weixin', 'api');\n        const token = await tokenServer.getAccessToken();\n        // console.log(token);\n        switch (data.MsgType) {\n            case 'text':\n                { //用户在客服会话中发送文本消息\n                    await tokenServer.sendTextMessage(data, token);\n                    break;\n                }\n                // case 'image': { //用户在客服会话中发送图片消息\n                //     await sendImageMessage(data.MediaId, data, access_token);\n                //     await tokenServer.sendImageMessage(data, token);\n                //     break;\n                // }\n            case 'event':\n                {\n                    if (data.Event == 'user_enter_tempsession') { //用户在小程序“客服会话按钮”进入客服会话,在聊天框进入不会有此事件\n                        // await tokenServer.sendTextMessage(data, token);\n                        // await sendTextMessage(\"您有什么问题吗?\", data, access_token);\n                    } else if (data.Event == 'kf_create_session') { //网页客服进入回话\n                        console.log('网页客服进入回话');\n                    }\n                    break;\n                }\n        }\n        //  https://www.jianshu.com/p/3d59ae5e69ab\n    }\n};"]}