/**
 * 秒杀轮次状态更新定时任务
 * 每分钟执行一次，检查并更新轮次状态
 */

const FlashSaleMultiScheduler = require('../service/flash_sale_multi_scheduler');

module.exports = class extends think.Service {
  
  /**
   * 执行定时任务
   */
  async run() {
    try {
      console.log('🔄 开始执行秒杀轮次状态更新任务...');
      
      const scheduler = new FlashSaleMultiScheduler();
      
      // 更新轮次状态
      const result = await scheduler.updateRoundStatus(this.model);
      
      // 每小时清理一次过期数据（当分钟数为0时）
      const now = new Date();
      if (now.getMinutes() === 0) {
        console.log('🧹 开始清理过期数据...');
        const cleanedCount = await scheduler.cleanupExpiredData(this.model, 30);
        console.log(`✅ 清理完成，删除了 ${cleanedCount} 个过期轮次`);
      }
      
      // 输出统计信息
      const stats = await scheduler.getActiveRoundsStats(this.model);
      console.log('📊 当前轮次状态统计:', stats);
      
      console.log('✅ 秒杀轮次状态更新任务完成');
      
    } catch (error) {
      console.error('❌ 秒杀轮次状态更新任务失败:', error);
    }
  }
};
