{"version": 3, "sources": ["..\\..\\..\\src\\api\\model\\user_coupons.js"], "names": ["module", "exports", "think", "Model", "tableName"], "mappings": "AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;AACzC,MAAIC,SAAJ,GAAgB;AACd,WAAO,sBAAP;AACD;AAHwC,CAA3C", "file": "..\\..\\..\\src\\api\\model\\user_coupons.js", "sourcesContent": ["module.exports = class extends think.Model {\n  get tableName() {\n    return 'hiolabs_user_coupons';\n  }\n};\n"]}