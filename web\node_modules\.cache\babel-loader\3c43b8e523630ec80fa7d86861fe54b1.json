{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\config\\api.js", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\config\\api.js", "mtime": 1753710583183}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\babel.config.js", "mtime": 1717470108000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly9jb25zdCByb290VXJsID0gJ2h0dHBzOi8vaHQucnhranNkai5jb20vYWRtaW4vJzsKdmFyIHJvb3RVcmwgPSAnaHR0cDovLzEyNy4wLjAuMTo4MzYwL2FkbWluLyc7CnZhciBhcGkgPSB7CiAgcm9vdFVybDogcm9vdFVybCwKICAvLyBleHByZXNzOiB7CiAgLy8gICAgIC8vIOW/q+mAkueJqea1geS/oeaBr+afpeivouS9v+eUqOeahOaYr+W/q+mAkum4n+aOpeWPo++8jOeUs+ivt+WcsOWdgO+8mmh0dHA6Ly93d3cua2RuaWFvLmNvbS8KICAvLyAgICAgYXBwaWQ6ICcxMjMnLCAvLyDlr7nlupTlv6vpgJLpuJ/nlKjmiLflkI7lj7Ag55So5oi3SUQKICAvLyAgICAgYXBwa2V5OiAnMTIzMTIzJywgLy8g5a+55bqU5b+r6YCS6bif55So5oi35ZCO5Y+wIEFQSSBrZXkKICAvLyAgICAgcmVxdWVzdF91cmw6ICdodHRwOi8vYXBpLmtkbmlhby5jYy9FYnVzaW5lc3MvRWJ1c2luZXNzT3JkZXJIYW5kbGUuYXNweCcKICAvLyB9LAogIC8vIDQuMTnmm7TmlrDvvIznianmtYHmn6Xor6LkuI3pnIDopoHku6XkuIrphY3nva7vvIzlj6rpnIDopoHlnKhzZXJ2ZXLnmoRjb25maWfphY3nva7pmL/ph4zkupHnianmtYHmjqXlj6PlsLHlj6/ku6UKICBxaW5pdTogJ2h0dHBzOi8vdXAtejEucWluaXVwLmNvbScKICAvLyDor7fmoLnmja7oh6rlt7HliJvlu7rnmoTkuIPniZvnmoTljLrln5/ov5vooYzorr7nva7vvJoKICAvLyBodHRwczovL2RldmVsb3Blci5xaW5pdS5jb20va29kby9tYW51YWwvMTY3MS9yZWdpb24tZW5kcG9pbnQKICAvLyDljY7kuJwJICBodHRwKHMpOi8vdXAucWluaXVwLmNvbQogIC8vIOWNjuWMlwkgIGh0dHAocyk6Ly91cC16MS5xaW5pdXAuY29tCiAgLy8g5Y2O5Y2XCSAgaHR0cChzKTovL3VwLXoyLnFpbml1cC5jb20KICAvLyDljJfnvo4JICBodHRwKHMpOi8vdXAtbmEwLnFpbml1cC5jb20KICAvLyDkuJzljZfkupogaHR0cChzKTovL3VwLWFzMC5xaW5pdXAuY29tCn07CgovLyBpbXBvcnQgYXBpIGZyb20gJy4vY29uZmlnL2FwaScKLy8gQXhpb3MuZGVmYXVsdHMuYmFzZVVSTCA9IGFwaS5yb290VXJsOwoKZXhwb3J0IGRlZmF1bHQgYXBpOw=="}, {"version": 3, "names": ["rootUrl", "api", "qiniu"], "sources": ["D:/py-ide/hioshop-miniprogram-master/web/src/config/api.js"], "sourcesContent": ["//const rootUrl = 'https://ht.rxkjsdj.com/admin/';\nconst rootUrl = 'http://127.0.0.1:8360/admin/';\nconst api = {\n    rootUrl : rootUrl,\n    // express: {\n    //     // 快递物流信息查询使用的是快递鸟接口，申请地址：http://www.kdniao.com/\n    //     appid: '123', // 对应快递鸟用户后台 用户ID\n    //     appkey: '123123', // 对应快递鸟用户后台 API key\n    //     request_url: 'http://api.kdniao.cc/Ebusiness/EbusinessOrderHandle.aspx'\n    // },\n\t// 4.19更新，物流查询不需要以上配置，只需要在server的config配置阿里云物流接口就可以\n    qiniu: 'https://up-z1.qiniup.com',\n    // 请根据自己创建的七牛的区域进行设置：\n    // https://developer.qiniu.com/kodo/manual/1671/region-endpoint\n\t// 华东\t  http(s)://up.qiniup.com\n\t// 华北\t  http(s)://up-z1.qiniup.com\n\t// 华南\t  http(s)://up-z2.qiniup.com\n\t// 北美\t  http(s)://up-na0.qiniup.com\n\t// 东南亚 http(s)://up-as0.qiniup.com\n};\n\n\n// import api from './config/api'\n// Axios.defaults.baseURL = api.rootUrl;\n\nexport default api\n"], "mappings": "AAAA;AACA,IAAMA,OAAO,GAAG,8BAA8B;AAC9C,IAAMC,GAAG,GAAG;EACRD,OAAO,EAAGA,OAAO;EACjB;EACA;EACA;EACA;EACA;EACA;EACH;EACGE,KAAK,EAAE;EACP;EACA;EACH;EACA;EACA;EACA;EACA;AACD,CAAC;;AAGD;AACA;;AAEA,eAAeD,GAAG"}]}