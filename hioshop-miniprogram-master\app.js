var util = require('./utils/util.js');
var api = require('./config/api.js');
App({
  data: {
    deviceInfo: {}
  },
  onLaunch: function () {
    this.data.deviceInfo = wx.getSystemInfoSync();
    console.log(this.data.deviceInfo);
    // 展示本地存储能力
    var logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 检查是否已有完整的用户信息
    const existingUserInfo = wx.getStorageSync('userInfo');
    const existingToken = wx.getStorageSync('token');

    console.log('=== App启动检查 ===');
    console.log('现有用户信息:', existingUserInfo);
    console.log('现有Token:', existingToken);

    // 只有在没有用户信息时才进行静默登录
    if (!existingUserInfo || !existingToken) {
      console.log('没有用户信息，执行静默登录');
      wx.login({
        success: (res) => {
          util.request(api.AuthLoginByWeixin, {
            code: res.code
          }, 'POST').then(function (res) {
            if (res.errno === 0) {
              let userInfo = res.data.userInfo;
              console.log('静默登录成功，保存用户信息:', userInfo);
              wx.setStorageSync('token', res.data.token);
              wx.setStorageSync('userInfo', userInfo);
              wx.setStorageSync('openid', res.data.openid);
            }
          });
        },
      });
    } else {
      console.log('已有用户信息，跳过静默登录');
      // 将现有信息设置到全局数据中
      this.globalData.userInfo = existingUserInfo;
      this.globalData.token = existingToken;
    }
    let that = this;
    wx.getSystemInfo({ //  获取页面的有关信息
      success: function (res) {
        wx.setStorageSync('systemInfo', res)
        var ww = res.windowWidth;
        var hh = res.windowHeight;
        that.globalData.ww = ww;
        that.globalData.hh = hh;
      }
    });
  },
  globalData: {
    userInfo: {
      nickname: '点我登录',
      username: '点击登录',
      avatar: '/images/icon/default_avatar_big.jpg'
    },
    token: '',
  }
})