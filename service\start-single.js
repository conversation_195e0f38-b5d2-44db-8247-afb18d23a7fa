const Application = require('thinkjs');
const path = require('path');

const ROOT_PATH = __dirname;

// 单进程启动，便于调试
const instance = new Application({
  ROOT_PATH: ROOT_PATH,
  env: 'development',
  workers: 1 // 单进程
});

// 添加错误处理
process.on('uncaughtException', (err) => {
  console.error('=== 未捕获的异常 ===');
  console.error('错误信息:', err.message);
  console.error('错误堆栈:', err.stack);
  console.error('错误代码:', err.code);
  console.error('==================');
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('=== 未处理的Promise拒绝 ===');
  console.error('原因:', reason);
  console.error('Promise:', promise);
  console.error('========================');
});

console.log('启动单进程模式...');
instance.run();
