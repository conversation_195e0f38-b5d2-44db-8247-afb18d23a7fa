#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库结构和内容导出工具
根据项目配置自动导出数据库结构和数据到文本文件
"""

import pymysql
import datetime
import os
import json

# 数据库配置（从项目配置中获取）
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': '19841020',
    'database': 'hiolabsDB',
    'charset': 'utf8mb4'
}

# 表前缀
TABLE_PREFIX = 'hiolabs_'

def connect_database():
    """连接数据库"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print(f"✅ 成功连接到数据库: {DB_CONFIG['database']}")
        return connection
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def get_all_tables(connection):
    """获取所有表名"""
    try:
        with connection.cursor() as cursor:
            # 获取所有表名
            cursor.execute("SHOW TABLES")
            tables = [table[0] for table in cursor.fetchall()]
            
            # 过滤出项目相关的表（带前缀的表）
            project_tables = [table for table in tables if table.startswith(TABLE_PREFIX)]
            
            print(f"📊 找到 {len(project_tables)} 个项目表")
            return project_tables
    except Exception as e:
        print(f"❌ 获取表列表失败: {e}")
        return []

def get_table_structure(connection, table_name):
    """获取表结构"""
    try:
        with connection.cursor() as cursor:
            # 获取表结构
            cursor.execute(f"DESCRIBE `{table_name}`")
            columns = cursor.fetchall()
            
            # 获取建表语句
            cursor.execute(f"SHOW CREATE TABLE `{table_name}`")
            create_sql = cursor.fetchone()[1]
            
            return {
                'columns': columns,
                'create_sql': create_sql
            }
    except Exception as e:
        print(f"❌ 获取表 {table_name} 结构失败: {e}")
        return None

def get_table_data(connection, table_name, limit=100):
    """获取表数据（限制条数避免文件过大）"""
    try:
        with connection.cursor() as cursor:
            # 获取总记录数
            cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
            total_count = cursor.fetchone()[0]
            
            # 获取前N条记录
            cursor.execute(f"SELECT * FROM `{table_name}` LIMIT {limit}")
            data = cursor.fetchall()
            
            # 获取列名
            cursor.execute(f"DESCRIBE `{table_name}`")
            columns = [col[0] for col in cursor.fetchall()]
            
            return {
                'total_count': total_count,
                'columns': columns,
                'data': data,
                'limited': total_count > limit
            }
    except Exception as e:
        print(f"❌ 获取表 {table_name} 数据失败: {e}")
        return None

def export_database_info():
    """导出数据库信息到文件"""
    connection = connect_database()
    if not connection:
        return
    
    try:
        # 获取所有表
        tables = get_all_tables(connection)
        if not tables:
            print("❌ 没有找到项目相关的表")
            return
        
        # 创建输出文件
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"database_export_{timestamp}.txt"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            # 写入文件头
            f.write("=" * 80 + "\n")
            f.write("数据库结构和内容导出报告\n")
            f.write("=" * 80 + "\n")
            f.write(f"导出时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据库: {DB_CONFIG['database']}\n")
            f.write(f"主机: {DB_CONFIG['host']}:{DB_CONFIG['port']}\n")
            f.write(f"表前缀: {TABLE_PREFIX}\n")
            f.write(f"总表数: {len(tables)}\n")
            f.write("=" * 80 + "\n\n")
            
            # 写入表列表
            f.write("📋 表列表:\n")
            f.write("-" * 40 + "\n")
            for i, table in enumerate(tables, 1):
                f.write(f"{i:2d}. {table}\n")
            f.write("\n")
            
            # 遍历每个表
            for table_name in tables:
                print(f"📊 正在处理表: {table_name}")
                
                f.write("=" * 80 + "\n")
                f.write(f"表名: {table_name}\n")
                f.write("=" * 80 + "\n")
                
                # 获取表结构
                structure = get_table_structure(connection, table_name)
                if structure:
                    f.write("\n📋 表结构:\n")
                    f.write("-" * 40 + "\n")
                    f.write("字段名\t\t类型\t\t\t空值\t键\t默认值\t额外\n")
                    f.write("-" * 80 + "\n")
                    
                    for col in structure['columns']:
                        field, type_, null, key, default, extra = col
                        f.write(f"{field:<15}\t{type_:<15}\t{null:<5}\t{key:<5}\t{str(default):<10}\t{extra}\n")
                    
                    f.write(f"\n📝 建表语句:\n")
                    f.write("-" * 40 + "\n")
                    f.write(structure['create_sql'] + "\n\n")
                
                # 获取表数据
                data_info = get_table_data(connection, table_name)
                if data_info:
                    f.write(f"📊 数据统计:\n")
                    f.write("-" * 40 + "\n")
                    f.write(f"总记录数: {data_info['total_count']}\n")
                    f.write(f"显示记录数: {len(data_info['data'])}\n")
                    if data_info['limited']:
                        f.write("⚠️  注意: 数据已截断，仅显示前100条记录\n")
                    f.write("\n")
                    
                    if data_info['data']:
                        f.write("📋 数据内容:\n")
                        f.write("-" * 40 + "\n")
                        
                        # 写入列标题
                        headers = data_info['columns']
                        f.write("\t".join(headers) + "\n")
                        f.write("-" * 80 + "\n")
                        
                        # 写入数据行
                        for row in data_info['data']:
                            row_str = []
                            for item in row:
                                if item is None:
                                    row_str.append("NULL")
                                elif isinstance(item, datetime.datetime):
                                    row_str.append(item.strftime('%Y-%m-%d %H:%M:%S'))
                                elif isinstance(item, datetime.date):
                                    row_str.append(item.strftime('%Y-%m-%d'))
                                else:
                                    row_str.append(str(item))
                            f.write("\t".join(row_str) + "\n")
                    else:
                        f.write("📋 数据内容: 表为空\n")
                
                f.write("\n\n")
            
            # 写入文件尾
            f.write("=" * 80 + "\n")
            f.write("导出完成\n")
            f.write("=" * 80 + "\n")
        
        print(f"✅ 数据库信息已导出到文件: {output_file}")
        print(f"📁 文件大小: {os.path.getsize(output_file) / 1024:.2f} KB")
        
        # 生成简要统计信息
        generate_summary(tables, output_file)
        
    except Exception as e:
        print(f"❌ 导出过程中发生错误: {e}")
    finally:
        connection.close()
        print("🔐 数据库连接已关闭")

def get_table_relationships(connection, tables):
    """分析表之间的关系"""
    relationships = []
    try:
        with connection.cursor() as cursor:
            for table in tables:
                # 获取外键信息
                cursor.execute(f"""
                    SELECT
                        COLUMN_NAME,
                        REFERENCED_TABLE_NAME,
                        REFERENCED_COLUMN_NAME
                    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                    WHERE TABLE_SCHEMA = '{DB_CONFIG['database']}'
                    AND TABLE_NAME = '{table}'
                    AND REFERENCED_TABLE_NAME IS NOT NULL
                """)

                foreign_keys = cursor.fetchall()
                for fk in foreign_keys:
                    relationships.append({
                        'from_table': table,
                        'from_column': fk[0],
                        'to_table': fk[1],
                        'to_column': fk[2]
                    })
    except Exception as e:
        print(f"❌ 分析表关系失败: {e}")

    return relationships

def generate_summary(tables, main_file):
    """生成简要统计信息"""
    try:
        summary_file = main_file.replace('.txt', '_summary.txt')
        connection = connect_database()

        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("数据库简要统计报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"数据库: {DB_CONFIG['database']}\n")
            f.write(f"主机: {DB_CONFIG['host']}:{DB_CONFIG['port']}\n")
            f.write(f"表前缀: {TABLE_PREFIX}\n")
            f.write(f"总表数: {len(tables)}\n")
            f.write(f"导出时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # 表分类统计
            f.write("📊 表分类统计:\n")
            f.write("-" * 30 + "\n")

            categories = {
                '用户管理': ['user', 'admin', 'member'],
                '商品管理': ['goods', 'product', 'category', 'specification', 'brand'],
                '订单管理': ['order', 'cart', 'pay'],
                '内容管理': ['ad', 'notice', 'article', 'feedback'],
                '系统设置': ['settings', 'config', 'region'],
                '营销功能': ['coupon', 'promotion', 'sign_in', 'points'],
                '其他功能': []
            }

            categorized_tables = {cat: [] for cat in categories.keys()}

            for table in tables:
                table_short = table.replace(TABLE_PREFIX, '')
                categorized = False

                for category, keywords in categories.items():
                    if category == '其他功能':
                        continue
                    for keyword in keywords:
                        if keyword in table_short:
                            categorized_tables[category].append(table)
                            categorized = True
                            break
                    if categorized:
                        break

                if not categorized:
                    categorized_tables['其他功能'].append(table)

            for category, cat_tables in categorized_tables.items():
                if cat_tables:
                    f.write(f"\n{category} ({len(cat_tables)}个表):\n")
                    for table in cat_tables:
                        f.write(f"  • {table}\n")

            # 数据量统计
            if connection:
                f.write(f"\n📈 数据量统计:\n")
                f.write("-" * 30 + "\n")
                total_records = 0

                with connection.cursor() as cursor:
                    for table in tables:
                        try:
                            cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
                            count = cursor.fetchone()[0]
                            total_records += count
                            f.write(f"{table:<30} {count:>10} 条记录\n")
                        except:
                            f.write(f"{table:<30} {'ERROR':>10}\n")

                f.write("-" * 42 + "\n")
                f.write(f"{'总计':<30} {total_records:>10} 条记录\n")

                # 表关系分析
                relationships = get_table_relationships(connection, tables)
                if relationships:
                    f.write(f"\n🔗 表关系分析:\n")
                    f.write("-" * 30 + "\n")
                    for rel in relationships:
                        f.write(f"{rel['from_table']}.{rel['from_column']} -> {rel['to_table']}.{rel['to_column']}\n")

                connection.close()

        print(f"📊 简要统计已生成: {summary_file}")

    except Exception as e:
        print(f"❌ 生成统计信息失败: {e}")

if __name__ == "__main__":
    print("🚀 开始导出数据库信息...")
    print("-" * 50)
    export_database_info()
    print("-" * 50)
    print("✨ 导出完成!")
