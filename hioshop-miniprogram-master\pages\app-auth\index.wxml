<view class="container">
    <!-- 顶部装饰 -->
    <view class="header-decoration">
        <view class="decoration-circle circle-1"></view>
        <view class="decoration-circle circle-2"></view>
        <view class="decoration-circle circle-3"></view>
    </view>

    <!-- 主标题区域 -->
    <view class="title-section">
        <view class="main-title">完善个人信息</view>
        <view class="sub-title">请输入昵称，头像可选</view>
    </view>

    <!-- 头像昵称设置卡片 -->
    <view class="profile-card">
        <!-- 头像设置区域 -->
        <view class="avatar-section">
            <view class="section-label">
                <text class="label-icon">👤</text>
                <text class="label-text">选择头像（可选）</text>
                <text class="status-badge" wx:if="{{avatarUrl !== '/images/icon/default_avatar_big.jpg'}}">✓</text>
            </view>
            <button
                class="avatar-btn"
                open-type="chooseAvatar"
                bindchooseavatar="onChooseAvatar">
                <image class="avatar-img" src="{{avatarUrl}}" mode="aspectFill"></image>
                <view class="avatar-overlay">
                    <text class="overlay-text">{{avatarUrl === '/images/icon/default_avatar_big.jpg' ? '点击选择' : '重新选择'}}</text>
                </view>
            </button>
        </view>

        <!-- 分割线 -->
        <view class="divider"></view>

        <!-- 昵称设置区域 -->
        <view class="nickname-section">
            <view class="section-label">
                <text class="label-icon">✏️</text>
                <text class="label-text">输入昵称（必填）</text>
                <text class="status-badge" wx:if="{{nickname.length > 0}}">✓</text>
            </view>
            <view class="input-wrapper">
                <input
                    class="nickname-input"
                    type="nickname"
                    placeholder="请输入您的昵称"
                    value="{{nickname}}"
                    bindinput="onNicknameInput"
                    maxlength="20"
                    placeholder-class="input-placeholder"
                />
                <view class="input-counter">{{nickname.length}}/20</view>
            </view>
        </view>

        <!-- 分割线 -->
        <view class="divider"></view>

        <!-- 手机号设置区域 -->
        <view class="phone-section">
            <view class="section-label">
                <text class="label-icon">📱</text>
                <text class="label-text">获取手机号（可选）</text>
                <text class="status-badge" wx:if="{{phoneNumber}}">✓</text>
            </view>
            <button
                class="phone-btn"
                open-type="getPhoneNumber"
                bindgetphonenumber="getPhoneNumber">
                <text class="phone-btn-text">{{phoneNumber ? '已获取手机号' : '点击获取手机号'}}</text>
            </button>
            <view class="phone-display" wx:if="{{phoneNumber}}">
                <text class="phone-text">{{phoneNumber}}</text>
            </view>
        </view>
    </view>

    <!-- 完成按钮 -->
    <view class="action-section">
        <button
            class="complete-btn {{canComplete ? 'active' : 'disabled'}}"
            bindtap="completeAuth"
            disabled="{{!canComplete}}">
            <text class="btn-text">完成设置</text>
            <text class="btn-icon" wx:if="{{canComplete}}">→</text>
        </button>
    </view>

    <!-- 底部提示 -->
    <view class="footer-tips">
        <view class="tip-item">
            <text class="tip-icon">✏️</text>
            <text class="tip-text">昵称为必填项，头像可稍后设置</text>
        </view>
        <view class="tip-item">
            <text class="tip-icon">⚙️</text>
            <text class="tip-text">可随时在个人中心修改</text>
        </view>
    </view>


</view>