const util = require('../../utils/util.js');
const api = require('../../config/api.js');
const user = require('../../services/user.js');
const auth = require('../../utils/auth.js');
//获取应用实例
const app = getApp()

// 默认头像
const defaultAvatarUrl = '/images/icon/default_avatar_big.jpg';

Page({
  data: {
    avatarUrl: defaultAvatarUrl,
    nickname: '',
    phoneNumber: '',
    canComplete: false
  },
  onLoad: function (options) {

  },
  onShow: function () {
    // 使用统一的权限判断逻辑
    if (auth.isFullyAuthorized()) {
      // 已经授权，根据来源页面决定跳转方式
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const from = currentPage.options.from;

      if (from === 'ucenter') {
        wx.switchTab({
          url: '/pages/ucenter/index/index'
        });
      } else if (from === 'order-exchange') {
        wx.navigateBack();
      } else if (from === 'coupon-receive') {
        // 从优惠券领取跳转过来，返回首页
        wx.switchTab({
          url: '/pages/index/index'
        });
      } else {
        wx.navigateBack();
      }
    };
  },
  getUserInfo: function (e) {
    console.log('getUserInfo事件触发:', e);

    // 检查用户是否授权
    if (e.detail.userInfo) {
      // 用户同意授权
      let that = this;

      // 显示加载中
      wx.showLoading({
        title: '正在登录...',
      });

      // 获取登录code
      wx.login({
        success: function(loginRes) {
          if (loginRes.code) {
            console.log('获取code成功:', loginRes.code);

            // 构建登录参数
            let loginParams = {
              code: loginRes.code,
              userInfo: e.detail.userInfo,
              encryptedData: e.detail.encryptedData,
              iv: e.detail.iv,
              signature: e.detail.signature,
              rawData: e.detail.rawData
            };

            // 调用登录接口
            that.postLogin(loginParams);
          } else {
            wx.hideLoading();
            console.log('获取code失败:', loginRes);
            wx.showToast({
              title: '登录失败，请重试',
              icon: 'none'
            });
          }
        },
        fail: function(err) {
          wx.hideLoading();
          console.log('wx.login失败:', err);
          wx.showToast({
            title: '登录失败，请重试',
            icon: 'none'
          });
        }
      });
    } else {
      // 用户拒绝授权
      console.log('用户拒绝授权');
      wx.showToast({
        title: '需要您的授权才能继续',
        icon: 'none'
      });
    }
  },

  getUserProfile: function (e) {
    console.log('=== getUserProfile 被调用 ===');
    console.log('事件对象:', e);

    let that = this;

    // 直接调用getUserProfile，不需要先调用wx.login
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (profileRes) => {
        console.log('getUserProfile成功:', profileRes);

        // 显示加载中
        wx.showLoading({
          title: '正在登录...',
        });

        // 获取用户信息后再调用wx.login
        wx.login({
          success: (loginRes) => {
            console.log('wx.login成功，code:', loginRes.code);

            let loginParams = {
              code: loginRes.code,
              userInfo: profileRes.userInfo,
              encryptedData: profileRes.encryptedData,
              iv: profileRes.iv,
              rawData: profileRes.rawData,
              signature: profileRes.signature
            };

            // 调用登录接口
            that.postLogin(loginParams);
          },
          fail: (loginErr) => {
            wx.hideLoading();
            console.log('wx.login失败:', loginErr);
            wx.showToast({
              title: '登录失败，请重试',
              icon: 'none'
            });
          }
        });
      },
      fail: (err) => {
        console.log('getUserProfile失败:', err);
        wx.showToast({
          title: '需要您的授权才能继续使用',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },
  postLogin(loginParams) {
    let that = this;
    console.log('=== 前端授权登录调试 ===');
    console.log('准备发送登录请求，参数:', loginParams);
    console.log('loginParams.userInfo:', loginParams.userInfo);

    // 显示加载中已在调用方法中显示，这里不需要重复显示

    // 根据参数格式调整请求参数
    let requestData = {};
    if (loginParams.userInfo) {
      // 使用getUserInfo方式登录或getUserProfile方式登录
      requestData = {
        code: loginParams.code,
        userInfo: loginParams.userInfo,
        encryptedData: loginParams.encryptedData,
        iv: loginParams.iv
      };
      console.log('发送授权登录数据:', requestData);
    } else {
      // 使用getUserProfile方式登录但没有userInfo（旧版本兼容）
      requestData = {
        info: loginParams
      };
      console.log('发送静默登录数据:', requestData);
    }

    util.request(api.AuthLoginByWeixin, requestData, 'POST').then(function (res) {
      // 隐藏加载提示
      wx.hideLoading();

      console.log('登录响应:', res);

      if (res.errno === 0) {
        // 保存用户信息、token和openid
        wx.setStorageSync('userInfo', res.data.userInfo);
        wx.setStorageSync('token', res.data.token);
        wx.setStorageSync('openid', res.data.openid);
        app.globalData.userInfo = res.data.userInfo;
        app.globalData.token = res.data.token;
        app.globalData.openid = res.data.openid;

        let is_new = res.data.is_new; //服务器返回的数据；

        // 显示登录成功提示
        wx.showToast({
          title: is_new == 1 ? '登录成功' : '欢迎回来',
          icon: 'success',
          duration: 2000
        });

        // 延迟返回，让用户看到提示
        setTimeout(function() {
          // 根据来源页面决定跳转方式
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1];
          const from = currentPage.options.from;

          if (from === 'ucenter') {
            // 如果是从个人中心跳转过来的，使用switchTab跳转回个人中心
            wx.switchTab({
              url: '/pages/ucenter/index/index'
            });
          } else if (from === 'order-exchange') {
            // 如果是从订单兑换页面跳转过来的，返回订单兑换页面
            wx.navigateBack();
          } else {
            // 其他情况使用navigateBack返回
            wx.navigateBack();
          }
        }, 1500);
      } else {
        // 登录失败
        wx.showToast({
          title: res.errmsg || '登录失败',
          icon: 'none',
          duration: 2000
        });
      }
    }).catch(function(err) {
      // 隐藏加载提示
      wx.hideLoading();

      console.log('登录请求失败:', err);

      // 显示错误提示
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none',
        duration: 2000
      });
    });
  },
  goBack: function () {
    wx.navigateBack();
  },

  // 头像选择回调
  onChooseAvatar: function(e) {
    console.log('=== 头像选择成功 ===');
    console.log('选择的头像:', e.detail);

    const { avatarUrl } = e.detail;
    this.setData({
      avatarUrl: avatarUrl
    });

    this.checkCanComplete();
  },

  // 昵称输入回调
  onNicknameInput: function(e) {
    console.log('昵称输入:', e.detail.value);
    this.setData({
      nickname: e.detail.value
    });
    this.checkCanComplete();
  },

  // 获取手机号回调
  getPhoneNumber: function(e) {
    console.log('=== 获取手机号 ===');
    console.log('手机号回调:', e.detail);

    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      // 用户同意授权
      const code = e.detail.code;
      console.log('获取到code:', code);

      // 显示加载提示
      wx.showLoading({
        title: '获取手机号中...'
      });

      // 调用后端接口，用code换取手机号
      util.request(api.GetPhoneNumber, { code: code }, 'POST').then((res) => {
        wx.hideLoading();

        if (res.errno === 0) {
          // 获取手机号成功
          const phoneNumber = res.data.phoneNumber;
          console.log('获取手机号成功:', phoneNumber);

          this.setData({
            phoneNumber: phoneNumber
          });

          wx.showToast({
            title: '手机号获取成功',
            icon: 'success'
          });
        } else {
          // 获取手机号失败
          console.log('获取手机号失败:', res.errmsg);
          wx.showToast({
            title: res.errmsg || '获取手机号失败',
            icon: 'none'
          });
        }
      }).catch((err) => {
        wx.hideLoading();
        console.log('获取手机号请求失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      });
    } else {
      // 用户拒绝授权
      console.log('用户拒绝授权手机号');
      wx.showToast({
        title: '您取消了手机号授权',
        icon: 'none'
      });
    }
  },

  // 检查是否可以完成
  checkCanComplete: function() {
    const { nickname } = this.data;
    // 只要求昵称，头像为可选
    const canComplete = nickname.trim().length > 0;
    this.setData({
      canComplete: canComplete
    });
  },

  // 完成授权
  completeAuth: function() {
    const { avatarUrl, nickname, phoneNumber } = this.data;

    if (!this.data.canComplete) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      });
      return;
    }

    console.log('=== 完成授权设置 ===');
    console.log('头像:', avatarUrl);
    console.log('昵称:', nickname);

    // 显示加载中
    wx.showLoading({
      title: '正在登录...',
    });

    // 调用wx.login获取code
    wx.login({
      success: (loginRes) => {
        console.log('wx.login成功，code:', loginRes.code);

        // 构造用户信息对象
        const userInfo = {
          nickName: nickname.trim(),
          avatarUrl: avatarUrl,
          phoneNumber: phoneNumber || '', // 包含手机号
          gender: 0,
          language: 'zh_CN',
          city: '',
          province: '',
          country: '',
          is_demote: false // 标记为非降级信息
        };

        const loginParams = {
          code: loginRes.code,
          userInfo: userInfo,
          encryptedData: '',
          iv: '',
          rawData: JSON.stringify(userInfo),
          signature: ''
        };

        console.log('发送登录请求:', loginParams);
        // 调用登录接口
        this.postLogin(loginParams);
      },
      fail: (loginErr) => {
        wx.hideLoading();
        console.log('wx.login失败:', loginErr);
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      }
    });
  }
})