function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

module.exports = class extends think.Model {

  /**
   * 获取轮次详细信息
   */
  getRoundInfo(roundId) {
    var _this = this;

    return _asyncToGenerator(function* () {
      const round = yield _this.where({ id: roundId }).find();
      if (think.isEmpty(round)) {
        return null;
      }

      // 获取活动信息
      const campaignModel = _this.model('flash_sale_campaigns');
      const campaign = yield campaignModel.where({ id: round.campaign_id }).find();

      // 获取商品信息
      const goodsModel = _this.model('goods');
      const goods = yield goodsModel.where({ id: campaign.goods_id }).find();

      return Object.assign({}, round, {
        campaign: campaign,
        goods: goods
      });
    })();
  }

  /**
   * 检查轮次是否可以购买
   */
  canPurchase(roundId, quantity = 1) {
    var _this2 = this;

    return _asyncToGenerator(function* () {
      const round = yield _this2.where({ id: roundId }).find();
      if (think.isEmpty(round)) {
        return { canPurchase: false, reason: '轮次不存在' };
      }

      if (round.status !== 'active') {
        return { canPurchase: false, reason: '轮次未开始或已结束' };
      }

      const remainingStock = round.stock - round.sold_count;
      if (remainingStock < quantity) {
        return { canPurchase: false, reason: '库存不足' };
      }

      return { canPurchase: true, remainingStock };
    })();
  }

  /**
   * 原子性减库存操作
   */
  decreaseStock(roundId, quantity) {
    var _this3 = this;

    return _asyncToGenerator(function* () {
      try {
        // 使用数据库级别的原子操作
        const result = yield _this3.query(`
        UPDATE ${_this3.tablePrefix}flash_sale_rounds 
        SET sold_count = sold_count + ${quantity}
        WHERE id = ${roundId} 
        AND (stock - sold_count) >= ${quantity}
        AND status = 'active'
      `);

        if (result.affectedRows === 0) {
          throw new Error('库存不足或轮次状态异常');
        }

        return true;
      } catch (error) {
        console.error('减库存失败:', error);
        throw error;
      }
    })();
  }

  /**
   * 获取当前活跃的轮次
   */
  getActiveRounds() {
    var _this4 = this;

    return _asyncToGenerator(function* () {
      const now = new Date();
      const nowStr = now.toISOString().slice(0, 19).replace('T', ' ');

      return yield _this4.where({
        start_time: ['<=', nowStr],
        end_time: ['>', nowStr],
        status: 'active'
      }).select();
    })();
  }

  /**
   * 获取即将开始的轮次
   */
  getUpcomingRounds(minutes = 30) {
    var _this5 = this;

    return _asyncToGenerator(function* () {
      const now = new Date();
      const future = new Date(now.getTime() + minutes * 60000);

      const nowStr = now.toISOString().slice(0, 19).replace('T', ' ');
      const futureStr = future.toISOString().slice(0, 19).replace('T', ' ');

      return yield _this5.where({
        start_time: ['BETWEEN', [nowStr, futureStr]],
        status: 'upcoming'
      }).order('start_time ASC').select();
    })();
  }

  /**
   * 更新轮次状态
   */
  updateStatus(roundId, status) {
    var _this6 = this;

    return _asyncToGenerator(function* () {
      return yield _this6.where({ id: roundId }).update({ status });
    })();
  }

  /**
   * 批量更新过期轮次状态
   */
  endExpiredRounds() {
    var _this7 = this;

    return _asyncToGenerator(function* () {
      const now = new Date();
      const nowStr = now.toISOString().slice(0, 19).replace('T', ' ');

      return yield _this7.where({
        end_time: ['<', nowStr],
        status: ['IN', ['upcoming', 'active']]
      }).update({ status: 'ended' });
    })();
  }
};
//# sourceMappingURL=flash_sale_rounds.js.map