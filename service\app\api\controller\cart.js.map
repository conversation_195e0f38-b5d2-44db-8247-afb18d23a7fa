{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\cart.js"], "names": ["Base", "require", "moment", "pinyin", "module", "exports", "getCart", "type", "userId", "getLoginUserId", "cartList", "model", "where", "user_id", "is_delete", "is_fast", "select", "goodsCount", "goodsAmount", "checkedGoodsCount", "checkedGoodsAmount", "numberChange", "cartItem", "product", "id", "product_id", "find", "think", "isEmpty", "update", "retail_price", "productNum", "goods_number", "is_on_sale", "checked", "number", "Number", "info", "goods_id", "field", "list_pic_url", "weight_count", "goods_weight", "add_price", "cAmount", "toFixed", "aAmount", "cartTotal", "indexAction", "success", "addAgain", "goodsId", "productId", "currentTime", "parseInt", "Date", "getTime", "goodsInfo", "fail", "productInfo", "cartInfo", "goodsSepcifitionValue", "goods_specification_ids", "split", "getField", "cartData", "goods_sn", "goods_name", "name", "goods_aka", "freight_template_id", "goods_specifition_name_value", "join", "goods_specifition_ids", "add_time", "add", "addAction", "post", "addType", "increment", "updateAction", "checkedAction", "toString", "isChecked", "deleteAction", "goodsCountAction", "checkoutAction", "orderFrom", "get", "addressId", "goodsMoney", "freightPrice", "outStock", "getAgainCart", "checkedGoodsList", "filter", "v", "item", "againGoods", "order_id", "againGoodsCount", "checkedAddress", "allAddresses", "order", "length", "addr", "is_default", "province_id", "cartGoods", "freightTempArray", "freightData", "money", "freight_type", "province_name", "getRegionName", "city_name", "city_id", "district_name", "district_id", "full_region", "ex", "template_id", "area", "freight_price", "groupData", "group_id", "free_by_number", "free_by_money", "templateInfo", "start", "start_fee", "add_fee", "goodsTotalPrice", "orderTotalPrice", "def", "actualPrice", "num"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;AACA,MAAME,SAASF,QAAQ,QAAR,CAAf;AACAG,OAAOC,OAAP,GAAiB,cAAcL,IAAd,CAAmB;AAC1BM,WAAN,CAAcC,IAAd,EAAoB;AAAA;;AAAA;AACtB,kBAAMC,SAAS,MAAM,MAAKC,cAAL,EAArB;AACM,gBAAIC,WAAW,EAAf;AACA,gBAAGH,QAAQ,CAAX,EAAa;AACTG,2BAAW,MAAM,MAAKC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACtCC,6BAASL,MAD6B;AAEtCM,+BAAW,CAF2B;AAGtCC,6BAAS;AAH6B,iBAAzB,EAIdC,MAJc,EAAjB;AAKH,aAND,MAOI;AACAN,2BAAW,MAAM,MAAKC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACtCC,6BAASL,MAD6B;AAEtCM,+BAAW,CAF2B;AAGtCC,6BAAS;AAH6B,iBAAzB,EAIdC,MAJc,EAAjB;AAKH;AACD;AACA,gBAAIC,aAAa,CAAjB;AACA,gBAAIC,cAAc,CAAlB;AACA,gBAAIC,oBAAoB,CAAxB;AACA,gBAAIC,qBAAqB,CAAzB;AACA,gBAAIC,eAAe,CAAnB;AACA,iBAAK,MAAMC,QAAX,IAAuBZ,QAAvB,EAAiC;AAC7B,oBAAIa,UAAU,MAAM,MAAKZ,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC5CY,wBAAIF,SAASG,UAD+B;AAE5CX,+BAAW;AAFiC,iBAA5B,EAGjBY,IAHiB,EAApB;AAIA,oBAAIC,MAAMC,OAAN,CAAcL,OAAd,CAAJ,EAA4B;AACxB,0BAAM,MAAKZ,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3Ba,oCAAYH,SAASG,UADM;AAE3BZ,iCAASL,MAFkB;AAG3BM,mCAAW;AAHgB,qBAAzB,EAIHe,MAJG,CAII;AACNf,mCAAW;AADL,qBAJJ,CAAN;AAOH,iBARD,MAQO;AACH,wBAAIgB,eAAeP,QAAQO,YAA3B;AACA,wBAAIC,aAAaR,QAAQS,YAAzB;AACZ;AACY,wBAAID,cAAc,CAAd,IAAmBR,QAAQU,UAAR,IAAsB,CAA7C,EAAgD;AAC5C,8BAAM,MAAKtB,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3Ba,wCAAYH,SAASG,UADM;AAE3BZ,qCAASL,MAFkB;AAG3B0B,qCAAS,CAHkB;AAI3BpB,uCAAW;AAJgB,yBAAzB,EAKHe,MALG,CAKI;AACNK,qCAAS;AADH,yBALJ,CAAN;AAQAZ,iCAASa,MAAT,GAAkB,CAAlB;AACH,qBAVD,MAUO,IAAIJ,aAAa,CAAb,IAAkBA,aAAaT,SAASa,MAA5C,EAAoD;AACvDb,iCAASa,MAAT,GAAkBJ,UAAlB;AACAV,uCAAe,CAAf;AACH,qBAHM,MAGA,IAAIU,aAAa,CAAb,IAAkBT,SAASa,MAAT,IAAmB,CAAzC,EAA4C;AAC/Cb,iCAASa,MAAT,GAAkB,CAAlB;AACAd,uCAAe,CAAf;AACH;AACDJ,kCAAcK,SAASa,MAAvB;AACAjB,mCAAeI,SAASa,MAAT,GAAkBL,YAAjC;AACAR,6BAASQ,YAAT,GAAwBA,YAAxB;AACA,wBAAI,CAACH,MAAMC,OAAN,CAAcN,SAASY,OAAT,IAAoBH,aAAa,CAA/C,CAAL,EAAwD;AACpDZ,6CAAqBG,SAASa,MAA9B;AACAf,8CAAsBE,SAASa,MAAT,GAAkBC,OAAON,YAAP,CAAxC;AACH;AACD;AACA,wBAAIO,OAAO,MAAM,MAAK1B,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AACvCY,4BAAIF,SAASgB;AAD0B,qBAA1B,EAEdC,KAFc,CAER,cAFQ,EAEQb,IAFR,EAAjB;AAGAJ,6BAASkB,YAAT,GAAwBH,KAAKG,YAA7B;AACAlB,6BAASmB,YAAT,GAAwBnB,SAASa,MAAT,GAAkBC,OAAOd,SAASoB,YAAhB,CAA1C;AACA,0BAAM,MAAK/B,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3Ba,oCAAYH,SAASG,UADM;AAE3BZ,iCAASL,MAFkB;AAG3BM,mCAAW;AAHgB,qBAAzB,EAIHe,MAJG,CAII;AACNM,gCAAQb,SAASa,MADX;AAENQ,mCAAUb;AAFJ,qBAJJ,CAAN;AAQH;AACJ;AACD,gBAAIc,UAAUxB,mBAAmByB,OAAnB,CAA2B,CAA3B,CAAd;AACA,gBAAIC,UAAU1B,kBAAd;AACA,mBAAO;AACHV,0BAAUA,QADP;AAEHqC,2BAAW;AACP9B,gCAAYA,UADL;AAEPC,iCAAaA,YAAY2B,OAAZ,CAAoB,CAApB,CAFN;AAGP1B,uCAAmBA,iBAHZ;AAIPC,wCAAoBwB,OAJb;AAKP/B,6BAASL,MALF;AAMPa,kCAAcA;AANP;AAFR,aAAP;AAlFgB;AA6FnB;AACD;;;;AAIM2B,eAAN,GAAoB;AAAA;;AAAA;AAChB,mBAAO,OAAKC,OAAL,EAAa,MAAM,OAAK3C,OAAL,CAAa,CAAb,CAAnB,EAAP;AADgB;AAEnB;AACK4C,YAAN,CAAeC,OAAf,EAAwBC,SAAxB,EAAmCjB,MAAnC,EAA2C;AAAA;;AAAA;AAC7C,kBAAM3B,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,kBAAM4C,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACA,kBAAMC,YAAY,MAAM,OAAK9C,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC9CY,oBAAI2B;AAD0C,aAA1B,EAErBzB,IAFqB,EAAxB;AAGA,gBAAIC,MAAMC,OAAN,CAAc6B,SAAd,KAA4BA,UAAUxB,UAAV,IAAwB,CAAxD,EAA2D;AACvD,uBAAO,OAAKyB,IAAL,CAAU,GAAV,EAAe,OAAf,CAAP;AACH;AACD;AACA;AACA,kBAAMC,cAAc,MAAM,OAAKhD,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAClDY,oBAAI4B;AAD8C,aAA5B,EAEvB1B,IAFuB,EAA1B;AAGA;AACA,gBAAIC,MAAMC,OAAN,CAAc+B,WAAd,KAA8BA,YAAY3B,YAAZ,GAA2BG,MAA7D,EAAqE;AACjE,uBAAO,OAAKuB,IAAL,CAAU,GAAV,EAAe,MAAf,CAAP;AACH;AACD;AACA,kBAAME,WAAW,MAAM,OAAKjD,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC5CC,yBAASL,MADmC;AAE5CiB,4BAAY2B,SAFgC;AAG5CtC,2BAAW;AAHiC,aAAzB,EAIpBY,IAJoB,EAAvB;AAKA,gBAAII,eAAe6B,YAAY7B,YAA/B;AACA,gBAAIH,MAAMC,OAAN,CAAcgC,QAAd,CAAJ,EAA6B;AACzB;AACA;AACA,oBAAIC,wBAAwB,EAA5B;AACA,oBAAI,CAAClC,MAAMC,OAAN,CAAc+B,YAAYG,uBAA1B,CAAL,EAAyD;AACrDD,4CAAwB,MAAM,OAAKlD,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AAClE0B,kCAAUqB,YAAYrB,QAD4C;AAElExB,mCAAW,CAFuD;AAGlEU,4BAAI;AACA,kCAAMmC,YAAYG,uBAAZ,CAAoCC,KAApC,CAA0C,GAA1C;AADN;AAH8D,qBAAxC,EAM3BC,QAN2B,CAMlB,OANkB,CAA9B;AAOH;AACD;AACA,sBAAMC,WAAW;AACb3B,8BAAUqB,YAAYrB,QADT;AAEbb,gCAAY2B,SAFC;AAGbc,8BAAUP,YAAYO,QAHT;AAIbC,gCAAYV,UAAUW,IAJT;AAKbC,+BAAWV,YAAYQ,UALV;AAMb;AACAG,yCAAqBb,UAAUa,mBAPlB;AAQb9B,kCAAciB,UAAUjB,YARX;AASbL,4BAAQA,MATK;AAUbtB,6BAASL,MAVI;AAWbsB,kCAAcA,YAXD;AAYba,+BAAWb,YAZE;AAabyC,kDAA8BV,sBAAsBW,IAAtB,CAA2B,GAA3B,CAbjB;AAcbC,2CAAuBd,YAAYG,uBAdtB;AAeb5B,6BAAS,CAfI;AAgBbwC,8BAAUrB;AAhBG,iBAAjB;AAkBA,sBAAM,OAAK1C,KAAL,CAAW,MAAX,EAAmBgE,GAAnB,CAAuBV,QAAvB,CAAN;AACH,aAjCD,MAiCO;AACH;AACA,oBAAIN,YAAY3B,YAAZ,GAA4BG,SAASyB,SAASzB,MAAlD,EAA2D;AACvD,2BAAO,OAAKuB,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACH;AACD,sBAAM,OAAK/C,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BC,6BAASL,MADkB;AAE3BiB,gCAAY2B,SAFe;AAG3BtC,+BAAW,CAHgB;AAI3BU,wBAAIoC,SAASpC;AAJc,iBAAzB,EAKHK,MALG,CAKI;AACNC,kCAAcA,YADR;AAENI,6BAAS,CAFH;AAGNC,4BAAQA;AAHF,iBALJ,CAAN;AAUH;AAzEsC;AA0E1C;AACD;;;;AAIMyC,aAAN,GAAkB;AAAA;;AAAA;AACd,kBAAMzB,UAAU,OAAK0B,IAAL,CAAU,SAAV,CAAhB;AACN,kBAAMrE,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,kBAAM2C,YAAY,OAAKyB,IAAL,CAAU,WAAV,CAAlB;AACA,kBAAM1C,SAAS,OAAK0C,IAAL,CAAU,QAAV,CAAf;AACA,kBAAMC,UAAU,OAAKD,IAAL,CAAU,SAAV,CAAhB;AACA,kBAAMxB,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACA;AACA,kBAAMC,YAAY,MAAM,OAAK9C,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC9CY,oBAAI2B;AAD0C,aAA1B,EAErBzB,IAFqB,EAAxB;AAGA,gBAAIC,MAAMC,OAAN,CAAc6B,SAAd,KAA4BA,UAAUxB,UAAV,IAAwB,CAAxD,EAA2D;AACvD,uBAAO,OAAKyB,IAAL,CAAU,GAAV,EAAe,OAAf,CAAP;AACH;AACD;AACA;AACA,kBAAMC,cAAc,MAAM,OAAKhD,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAClDY,oBAAI4B;AAD8C,aAA5B,EAEvB1B,IAFuB,EAA1B;AAGA;AACA,gBAAIC,MAAMC,OAAN,CAAc+B,WAAd,KAA8BA,YAAY3B,YAAZ,GAA2BG,MAA7D,EAAqE;AACjE,uBAAO,OAAKuB,IAAL,CAAU,GAAV,EAAe,MAAf,CAAP;AACH;AACD;AACA,kBAAME,WAAW,MAAM,OAAKjD,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC5CC,yBAASL,MADmC;AAE5CiB,4BAAY2B,SAFgC;AAG5CtC,2BAAW;AAHiC,aAAzB,EAIpBY,IAJoB,EAAvB;AAKA,gBAAII,eAAe6B,YAAY7B,YAA/B;AACA,gBAAIgD,WAAW,CAAf,EAAkB;AACd,sBAAM,OAAKnE,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BE,+BAAW,CADgB;AAE3BD,6BAASL;AAFkB,iBAAzB,EAGHqB,MAHG,CAGI;AACNK,6BAAS;AADH,iBAHJ,CAAN;AAMA,oBAAI2B,wBAAwB,EAA5B;AACA,oBAAI,CAAClC,MAAMC,OAAN,CAAc+B,YAAYG,uBAA1B,CAAL,EAAyD;AACrDD,4CAAwB,MAAM,OAAKlD,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AAClE0B,kCAAUqB,YAAYrB,QAD4C;AAElExB,mCAAW,CAFuD;AAGlEU,4BAAI;AACA,kCAAMmC,YAAYG,uBAAZ,CAAoCC,KAApC,CAA0C,GAA1C;AADN;AAH8D,qBAAxC,EAM3BC,QAN2B,CAMlB,OANkB,CAA9B;AAOH;AACD;AACA,sBAAMC,WAAW;AACb3B,8BAAUqB,YAAYrB,QADT;AAEbb,gCAAY2B,SAFC;AAGbc,8BAAUP,YAAYO,QAHT;AAIbC,gCAAYV,UAAUW,IAJT;AAKbC,+BAAWV,YAAYQ,UALV;AAMb;AACAG,yCAAqBb,UAAUa,mBAPlB;AAQb9B,kCAAciB,UAAUjB,YARX;AASbL,4BAAQA,MATK;AAUbtB,6BAASL,MAVI;AAWbsB,kCAAcA,YAXD;AAYba,+BAAWb,YAZE;AAabyC,kDAA8BV,sBAAsBW,IAAtB,CAA2B,GAA3B,CAbjB;AAcbC,2CAAuBd,YAAYG,uBAdtB;AAeb5B,6BAAS,CAfI;AAgBbwC,8BAAUrB,WAhBG;AAiBbtC,6BAAS;AAjBI,iBAAjB;AAmBA,sBAAM,OAAKJ,KAAL,CAAW,MAAX,EAAmBgE,GAAnB,CAAuBV,QAAvB,CAAN;AACA,uBAAO,OAAKhB,OAAL,EAAa,MAAM,OAAK3C,OAAL,CAAa,CAAb,CAAnB,EAAP;AACH,aAvCD,MAuCO;AACH,oBAAIqB,MAAMC,OAAN,CAAcgC,QAAd,CAAJ,EAA6B;AACzB;AACA;AACA,wBAAIC,wBAAwB,EAA5B;AACA,wBAAI,CAAClC,MAAMC,OAAN,CAAc+B,YAAYG,uBAA1B,CAAL,EAAyD;AACrDD,gDAAwB,MAAM,OAAKlD,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AAClE0B,sCAAUqB,YAAYrB,QAD4C;AAElExB,uCAAW,CAFuD;AAGlEU,gCAAI;AACA,sCAAMmC,YAAYG,uBAAZ,CAAoCC,KAApC,CAA0C,GAA1C;AADN;AAH8D,yBAAxC,EAM3BC,QAN2B,CAMlB,OANkB,CAA9B;AAOH;AACD;AACA,0BAAMC,WAAW;AACb3B,kCAAUqB,YAAYrB,QADT;AAEbb,oCAAY2B,SAFC;AAGbc,kCAAUP,YAAYO,QAHT;AAIbC,oCAAYV,UAAUW,IAJT;AAKbC,mCAAWV,YAAYQ,UALV;AAMb;AACAG,6CAAqBb,UAAUa,mBAPlB;AAQb9B,sCAAciB,UAAUjB,YARX;AASbL,gCAAQA,MATK;AAUbtB,iCAASL,MAVI;AAWbsB,sCAAcA,YAXD;AAYba,mCAAWb,YAZE;AAabyC,sDAA8BV,sBAAsBW,IAAtB,CAA2B,GAA3B,CAbjB;AAcbC,+CAAuBd,YAAYG,uBAdtB;AAeb5B,iCAAS,CAfI;AAgBbwC,kCAAUrB;AAhBG,qBAAjB;AAkBA,0BAAM,OAAK1C,KAAL,CAAW,MAAX,EAAmBgE,GAAnB,CAAuBV,QAAvB,CAAN;AACH,iBAjCD,MAiCO;AACH;AACA,wBAAIN,YAAY3B,YAAZ,GAA4BG,SAASyB,SAASzB,MAAlD,EAA2D;AACvD,+BAAO,OAAKuB,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACH;AACD,0BAAM,OAAK/C,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BC,iCAASL,MADkB;AAE3BiB,oCAAY2B,SAFe;AAG3BtC,mCAAW,CAHgB;AAI3BU,4BAAIoC,SAASpC;AAJc,qBAAzB,EAKHK,MALG,CAKI;AACNC,sCAAcA;AADR,qBALJ,CAAN;AAQA,0BAAM,OAAKnB,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BC,iCAASL,MADkB;AAE3BiB,oCAAY2B,SAFe;AAG3BtC,mCAAW,CAHgB;AAI3BU,4BAAIoC,SAASpC;AAJc,qBAAzB,EAKHuD,SALG,CAKO,QALP,EAKiB5C,MALjB,CAAN;AAMH;AACD,uBAAO,OAAKc,OAAL,EAAa,MAAM,OAAK3C,OAAL,CAAa,CAAb,CAAnB,EAAP;AACH;AA5Ha;AA6HjB;AACD;AACM0E,gBAAN,GAAqB;AAAA;;AAAA;AACjB,kBAAM5B,YAAY,OAAKyB,IAAL,CAAU,WAAV,CAAlB,CADiB,CACyB;AAC1C,kBAAMrD,KAAK,OAAKqD,IAAL,CAAU,IAAV,CAAX,CAFiB,CAEW;AAC5B,kBAAM1C,SAASmB,SAAS,OAAKuB,IAAL,CAAU,QAAV,CAAT,CAAf,CAHiB,CAG6B;AAC9C;AACA,kBAAMlB,cAAc,MAAM,OAAKhD,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAClDY,oBAAI4B,SAD8C;AAElDtC,2BAAW;AAFuC,aAA5B,EAGvBY,IAHuB,EAA1B;AAIA,gBAAIC,MAAMC,OAAN,CAAc+B,WAAd,KAA8BA,YAAY3B,YAAZ,GAA2BG,MAA7D,EAAqE;AACjE,uBAAO,OAAKuB,IAAL,CAAU,GAAV,EAAe,MAAf,CAAP;AACH;AACD;AACA,kBAAME,WAAW,MAAM,OAAKjD,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC5CY,oBAAIA,EADwC;AAE5CV,2BAAW;AAFiC,aAAzB,EAGpBY,IAHoB,EAAvB;AAIA;AACA,gBAAIkC,SAASnC,UAAT,KAAwB2B,SAA5B,EAAuC;AACnC,sBAAM,OAAKzC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BY,wBAAIA,EADuB;AAE3BV,+BAAW;AAFgB,iBAAzB,EAGHe,MAHG,CAGI;AACNM,4BAAQA;AADF,iBAHJ,CAAN;AAMA,uBAAO,OAAKc,OAAL,EAAa,MAAM,OAAK3C,OAAL,CAAa,CAAb,CAAnB,EAAP;AACH;AA1BgB;AA2BpB;AACD;AACM2E,iBAAN,GAAsB;AAAA;;AAAA;AACxB,kBAAMzE,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,gBAAI2C,YAAY,OAAKyB,IAAL,CAAU,YAAV,EAAwBK,QAAxB,EAAhB;AACA,kBAAMC,YAAY,OAAKN,IAAL,CAAU,WAAV,CAAlB;AACA,gBAAIlD,MAAMC,OAAN,CAAcwB,SAAd,CAAJ,EAA8B;AAC1B,uBAAO,OAAKM,IAAL,CAAU,MAAV,CAAP;AACH;AACDN,wBAAYA,UAAUW,KAAV,CAAgB,GAAhB,CAAZ;AACA,kBAAM,OAAKpD,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3Ba,4BAAY;AACR,0BAAM2B;AADE,iBADe;AAI3BvC,yBAASL,MAJkB;AAK3BM,2BAAW;AALgB,aAAzB,EAMHe,MANG,CAMI;AACNK,yBAASoB,SAAS6B,SAAT;AADH,aANJ,CAAN;AASA,mBAAO,OAAKlC,OAAL,EAAa,MAAM,OAAK3C,OAAL,CAAa,CAAb,CAAnB,EAAP;AAjBkB;AAkBrB;AACD;AACM8E,gBAAN,GAAqB;AAAA;;AAAA;AACjB,gBAAIhC,YAAY,OAAKyB,IAAL,CAAU,YAAV,CAAhB;AACN,kBAAMrE,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,gBAAIkB,MAAMC,OAAN,CAAcwB,SAAd,CAAJ,EAA8B;AAC1B,uBAAO,OAAKM,IAAL,CAAU,MAAV,CAAP;AACH;AACD,kBAAM,OAAK/C,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3Ba,4BAAY2B,SADe;AAE3BvC,yBAASL,MAFkB;AAG3BM,2BAAW;AAHgB,aAAzB,EAIHe,MAJG,CAII;AACNf,2BAAW;AADL,aAJJ,CAAN;AAOA,mBAAO,OAAKmC,OAAL,EAAa,MAAM,OAAK3C,OAAL,CAAa,CAAb,CAAnB,EAAP;AACA;AAdiB;AAepB;AACD;AACM+E,oBAAN,GAAyB;AAAA;;AAAA;AACrB,kBAAMpB,WAAW,MAAM,OAAK3D,OAAL,CAAa,CAAb,CAAvB;AACN,kBAAME,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,kBAAM,OAAKE,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BC,yBAASL,MADkB;AAE3BM,2BAAW,CAFgB;AAG3BC,yBAAS;AAHkB,aAAzB,EAIHc,MAJG,CAII;AACNf,2BAAW;AADL,aAJJ,CAAN;AAOA,mBAAO,OAAKmC,OAAL,CAAa;AAChBF,2BAAW;AACP9B,gCAAYgD,SAASlB,SAAT,CAAmB9B;AADxB;AADK,aAAb,CAAP;AAVqB;AAexB;AACD;;;;AAIMqE,kBAAN,GAAuB;AAAA;;AAAA;AACnB,kBAAMjC,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACN,kBAAMhD,SAAS,MAAM,OAAKC,cAAL,EAArB;AACM,gBAAI8E,YAAY,OAAKC,GAAL,CAAS,WAAT,CAAhB;AACA,kBAAMjF,OAAO,OAAKiF,GAAL,CAAS,MAAT,CAAb,CAJmB,CAIY;AAC/B,kBAAMC,YAAY,OAAKD,GAAL,CAAS,WAAT,CAAlB,CALmB,CAKsB;AACzC,kBAAMV,UAAU,OAAKU,GAAL,CAAS,SAAT,CAAhB;AACA,gBAAIvE,aAAa,CAAjB,CAPmB,CAOC;AACpB,gBAAIyE,aAAa,CAAjB,CARmB,CAQC;AACpB,gBAAIC,eAAe,CAAnB;AACA,gBAAIC,WAAW,CAAf;AACA,gBAAI3B,WAAW,EAAf;AACA;AACA,gBAAI1D,QAAQ,CAAZ,EAAe;AACX,oBAAIuE,WAAW,CAAf,EAAkB;AACdb,+BAAW,MAAM,OAAK3D,OAAL,CAAa,CAAb,CAAjB;AACH,iBAFD,MAEO,IAAIwE,WAAW,CAAf,EAAkB;AACrBb,+BAAW,MAAM,OAAK3D,OAAL,CAAa,CAAb,CAAjB;AACH,iBAFM,MAEA,IAAIwE,WAAW,CAAf,EAAkB;AACrBb,+BAAW,MAAM,OAAK4B,YAAL,CAAkBN,SAAlB,CAAjB;AACH;AACJ;AACD,kBAAMO,mBAAmB7B,SAASvD,QAAT,CAAkBqF,MAAlB,CAAyB,UAASC,CAAT,EAAY;AAC1D,uBAAOA,EAAE9D,OAAF,KAAc,CAArB;AACH,aAFwB,CAAzB;AAGA,iBAAK,MAAM+D,IAAX,IAAmBH,gBAAnB,EAAqC;AACjC7E,6BAAaA,aAAagF,KAAK9D,MAA/B;AACAuD,6BAAaA,aAAaO,KAAK9D,MAAL,GAAc8D,KAAKnE,YAA7C;AACA,oBAAImE,KAAKjE,YAAL,IAAqB,CAArB,IAA0BiE,KAAKhE,UAAL,IAAmB,CAAjD,EAAoD;AAChD2D,+BAAWxD,OAAOwD,QAAP,IAAmB,CAA9B;AACH;AACJ;AACD,gBAAId,WAAW,CAAf,EAAkB;AACd,oBAAIoB,aAAa,MAAM,OAAKvF,KAAL,CAAW,aAAX,EAA0BC,KAA1B,CAAgC;AACnDuF,8BAAUZ;AADyC,iBAAhC,EAEpBvE,MAFoB,EAAvB;AAGA,oBAAIoF,kBAAkB,CAAtB;AACA,qBAAK,MAAMH,IAAX,IAAmBC,UAAnB,EAA+B;AAC3BE,sCAAkBA,kBAAkBH,KAAK9D,MAAzC;AACH;AACD,oBAAIlB,cAAcmF,eAAlB,EAAmC;AAC/BR,+BAAW,CAAX;AACH;AACJ;AACD;AACA,gBAAIS,iBAAiB,IAArB;AACA,gBAAIZ,aAAa,EAAb,IAAmBA,aAAa,CAApC,EAAuC;AACnC;;AAEA;AACA,sBAAMa,eAAe,MAAM,OAAK3F,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AACnDC,6BAASL,MAD0C;AAEnDM,+BAAW;AAFwC,iBAA5B,EAGxByF,KAHwB,CAGlB,SAHkB,EAGPvF,MAHO,EAA3B;;AAKA,oBAAIsF,aAAaE,MAAb,KAAwB,CAA5B,EAA+B;AAC3B;AACAH,qCAAiB,IAAjB;AACH,iBAHD,MAGO,IAAIC,aAAaE,MAAb,KAAwB,CAA5B,EAA+B;AAClC;AACAH,qCAAiBC,aAAa,CAAb,CAAjB;AACH,iBAHM,MAGA;AACH;AACAD,qCAAiBC,aAAa5E,IAAb,CAAkB;AAAA,+BAAQ+E,KAAKC,UAAL,KAAoB,CAA5B;AAAA,qBAAlB,CAAjB;;AAEA;AACA,wBAAI,CAACL,cAAL,EAAqB;AACjBA,yCAAiBC,aAAa,CAAb,CAAjB;AACH;AACJ;AACJ,aAxBD,MAwBO;AACH;AACAD,iCAAiB,MAAM,OAAK1F,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC/CY,wBAAIiE,SAD2C;AAE/C5E,6BAASL,MAFsC;AAG3DM,+BAAU;AAHiD,iBAA5B,EAIpBY,IAJoB,EAAvB;AAKH;AACD,gBAAI,CAACC,MAAMC,OAAN,CAAcyE,cAAd,CAAL,EAAoC;AAChC;AACA;AACA;AACA,oBAAIM,cAAcN,eAAeM,WAAjC;AACA;AACA;AACA,oBAAIC,YAAYd,gBAAhB;AACA,oBAAIe,mBAAmB,MAAM,OAAKlG,KAAL,CAAW,kBAAX,EAA+BC,KAA/B,CAAqC;AAC9DE,+BAAW;AADmD,iBAArC,EAE1BE,MAF0B,EAA7B;AAGA,oBAAI8F,cAAc,EAAlB;AACA,qBAAK,MAAMb,IAAX,IAAmBY,gBAAnB,EAAqC;AACjCC,gCAAYb,IAAZ,IAAoB;AAChBzE,4BAAIqF,iBAAiBZ,IAAjB,EAAuBzE,EADX;AAEhBW,gCAAQ,CAFQ;AAGhB4E,+BAAO,CAHS;AAIhBrE,sCAAc,CAJE;AAKhBsE,sCAAcH,iBAAiBZ,IAAjB,EAAuBe;AALrB,qBAApB;AAOH;AACD;AACA;AACA,qBAAK,MAAMf,IAAX,IAAmBa,WAAnB,EAAgC;AAC5B,yBAAK,MAAMxF,QAAX,IAAuBsF,SAAvB,EAAkC;AAC9B,4BAAIX,KAAKzE,EAAL,IAAWF,SAASgD,mBAAxB,EAA6C;AACzC;AACA2B,iCAAK9D,MAAL,GAAc8D,KAAK9D,MAAL,GAAcb,SAASa,MAArC;AACA8D,iCAAKc,KAAL,GAAad,KAAKc,KAAL,GAAazF,SAASa,MAAT,GAAkBb,SAASQ,YAArD;AACAmE,iCAAKvD,YAAL,GAAoBuD,KAAKvD,YAAL,GAAoBpB,SAASa,MAAT,GAAkBb,SAASoB,YAAnE;AACH;AACJ;AACJ;AACD2D,+BAAeY,aAAf,GAA+B,MAAM,OAAKtG,KAAL,CAAW,QAAX,EAAqBuG,aAArB,CAAmCb,eAAeM,WAAlD,CAArC;AACAN,+BAAec,SAAf,GAA2B,MAAM,OAAKxG,KAAL,CAAW,QAAX,EAAqBuG,aAArB,CAAmCb,eAAee,OAAlD,CAAjC;AACAf,+BAAegB,aAAf,GAA+B,MAAM,OAAK1G,KAAL,CAAW,QAAX,EAAqBuG,aAArB,CAAmCb,eAAeiB,WAAlD,CAArC;AACAjB,+BAAekB,WAAf,GAA6BlB,eAAeY,aAAf,GAA+BZ,eAAec,SAA9C,GAA0Dd,eAAegB,aAAtG;AACA,qBAAK,MAAMpB,IAAX,IAAmBa,WAAnB,EAAgC;AAC5B,wBAAIb,KAAK9D,MAAL,IAAe,CAAnB,EAAsB;AAClB;AACH;AACD,wBAAIqF,KAAK,MAAM,OAAK7G,KAAL,CAAW,yBAAX,EAAsCC,KAAtC,CAA4C;AACvD6G,qCAAaxB,KAAKzE,EADqC;AAEvDkG,8BAAMf,WAFiD;AAGvD7F,mCAAW;AAH4C,qBAA5C,EAIZY,IAJY,EAAf;AAKA,wBAAIiG,gBAAgB,CAApB;AACA,wBAAI,CAAChG,MAAMC,OAAN,CAAc4F,EAAd,CAAL,EAAwB;AACpB;AACA,4BAAII,YAAY,MAAM,OAAKjH,KAAL,CAAW,wBAAX,EAAqCC,KAArC,CAA2C;AAC7DY,gCAAIgG,GAAGK,QADsD;AAE7D/G,uCAAU;AAFmD,yBAA3C,EAGnBY,IAHmB,EAAtB;AAIA;AACA,4BAAIoG,iBAAiBF,UAAUE,cAA/B;AACA,4BAAIC,gBAAgBH,UAAUG,aAA9B;AACA;AACA,4BAAIC,eAAe,MAAM,OAAKrH,KAAL,CAAW,kBAAX,EAA+BC,KAA/B,CAAqC;AAC1DY,gCAAIyE,KAAKzE,EADiD;AAE1DV,uCAAW;AAF+C,yBAArC,EAGtBY,IAHsB,EAAzB;AAIA,4BAAIsF,eAAegB,aAAahB,YAAhC;AACA,4BAAIA,gBAAgB,CAApB,EAAuB;AACnB,gCAAIf,KAAK9D,MAAL,GAAcyF,UAAUK,KAA5B,EAAmC;AAAE;AACjCN,gDAAgBC,UAAUK,KAAV,GAAkBL,UAAUM,SAA5B,GAAwC,CAACjC,KAAK9D,MAAL,GAAc,CAAf,IAAoByF,UAAUO,OAAtF,CAD+B,CACgE;AAClG,6BAFD,MAEO;AACHR,gDAAgBC,UAAUK,KAAV,GAAkBL,UAAUM,SAA5C;AACH;AACJ,yBAND,MAMO,IAAIlB,gBAAgB,CAApB,EAAuB;AAC1B,gCAAIf,KAAKvD,YAAL,GAAoBkF,UAAUK,KAAlC,EAAyC;AAAE;AACvCN,gDAAgBC,UAAUK,KAAV,GAAkBL,UAAUM,SAA5B,GAAwC,CAACjC,KAAKvD,YAAL,GAAoB,CAArB,IAA0BkF,UAAUO,OAA5F,CADqC,CACgE;AACxG,6BAFD,MAEO;AACHR,gDAAgBC,UAAUK,KAAV,GAAkBL,UAAUM,SAA5C;AACH;AACJ;AACD,4BAAIJ,iBAAiB,CAArB,EAAwB;AACpB,gCAAI7B,KAAK9D,MAAL,IAAe2F,cAAnB,EAAmC;AAC/BH,gDAAgB,CAAhB;AACH;AACJ;AACD,4BAAII,gBAAgB,CAApB,EAAuB;AACnB,gCAAI9B,KAAKc,KAAL,IAAcgB,aAAlB,EAAiC;AAC7BJ,gDAAgB,CAAhB;AACH;AACJ;AACJ,qBAtCD,MAsCO;AACH;AACA,4BAAIC,YAAY,MAAM,OAAKjH,KAAL,CAAW,wBAAX,EAAqCC,KAArC,CAA2C;AAC7D6G,yCAAaxB,KAAKzE,EAD2C;AAE7DkG,kCAAM,CAFuD;AAG7D5G,uCAAU;AAHmD,yBAA3C,EAInBY,IAJmB,EAAtB;AAKA,4BAAIoG,iBAAiBF,UAAUE,cAA/B;AACA,4BAAIC,gBAAgBH,UAAUG,aAA9B;AACA,4BAAIC,eAAe,MAAM,OAAKrH,KAAL,CAAW,kBAAX,EAA+BC,KAA/B,CAAqC;AAC1DY,gCAAIyE,KAAKzE,EADiD;AAE1DV,uCAAW;AAF+C,yBAArC,EAGtBY,IAHsB,EAAzB;AAIA,4BAAIsF,eAAegB,aAAahB,YAAhC;AACA,4BAAIA,gBAAgB,CAApB,EAAuB;AACnB,gCAAIf,KAAK9D,MAAL,GAAcyF,UAAUK,KAA5B,EAAmC;AAAE;AACjCN,gDAAgBC,UAAUK,KAAV,GAAkBL,UAAUM,SAA5B,GAAwC,CAACjC,KAAK9D,MAAL,GAAc,CAAf,IAAoByF,UAAUO,OAAtF,CAD+B,CACgE;AAClG,6BAFD,MAEO;AACHR,gDAAgBC,UAAUK,KAAV,GAAkBL,UAAUM,SAA5C;AACH;AACJ,yBAND,MAMO,IAAIlB,gBAAgB,CAApB,EAAuB;AAC1B,gCAAIf,KAAKvD,YAAL,GAAoBkF,UAAUK,KAAlC,EAAyC;AAAE;AACvCN,gDAAgBC,UAAUK,KAAV,GAAkBL,UAAUM,SAA5B,GAAwC,CAACjC,KAAKvD,YAAL,GAAoB,CAArB,IAA0BkF,UAAUO,OAA5F,CADqC,CACgE;AACxG,6BAFD,MAEO;AACHR,gDAAgBC,UAAUK,KAAV,GAAkBL,UAAUM,SAA5C;AACH;AACJ;AACD,4BAAIJ,iBAAiB,CAArB,EAAwB;AACpB,gCAAI7B,KAAK9D,MAAL,IAAe2F,cAAnB,EAAmC;AAC/BH,gDAAgB,CAAhB;AACH;AACJ;AACD,4BAAII,gBAAgB,CAApB,EAAuB;AACnB,gCAAI9B,KAAKc,KAAL,IAAcgB,aAAlB,EAAiC;AAC7BJ,gDAAgB,CAAhB;AACH;AACJ;AACJ;AACbhC,mCAAeA,eAAegC,aAAf,GAA6BhC,YAA7B,GAA0CgC,aAAzD;AACY;AACA;AACH;AACJ,aA/HD,MA+HO;AACHtB,iCAAiB,CAAjB;AACH;AACD;AACA,gBAAI+B,kBAAkBnE,SAASlB,SAAT,CAAmB3B,kBAAzC,CAjNmB,CAiN0C;AAC7D;AACA,gBAAI2F,QAAQ9C,SAASlB,SAAT,CAAmB3B,kBAA/B;AACA,gBAAIiH,kBAAkB,CAAtB;AACA,gBAAIC,MAAM,MAAM,OAAK3H,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AACzCY,oBAAI;AADqC,aAA7B,EAEbE,IAFa,EAAhB;AAGA2G,8BAAkBjG,OAAO2E,KAAP,IAAgB3E,OAAOuD,YAAP,CAAlC,CAxNmB,CAwNoC;AACvD,kBAAM4C,cAAcF,eAApB,CAzNmB,CAyNkB;AACrC,gBAAIhH,eAAe4C,SAASlB,SAAT,CAAmB1B,YAAtC;AACA,mBAAO,OAAK4B,OAAL,CAAa;AAChBoD,gCAAgBA,cADA;AAEhBV,8BAAcA,YAFE;AAGhBG,kCAAkBA,gBAHF;AAIhBsC,iCAAiBA,eAJD;AAKhBC,iCAAiBA,gBAAgBxF,OAAhB,CAAwB,CAAxB,CALD;AAMhB0F,6BAAaA,YAAY1F,OAAZ,CAAoB,CAApB,CANG;AAOhB5B,4BAAYA,UAPI;AAQhB2E,0BAAUA,QARM;AAShBvE,8BAAcA;AATE,aAAb,CAAP;AA3NmB;AAsOtB;AACKwE,gBAAN,CAAmBN,SAAnB,EAA8B;AAAA;;AAAA;AAChC,kBAAM/E,SAAS,MAAM,QAAKC,cAAL,EAArB;AACM,kBAAMyF,aAAa,MAAM,QAAKvF,KAAL,CAAW,aAAX,EAA0BC,KAA1B,CAAgC;AACrDuF,0BAAUZ;AAD2C,aAAhC,EAEtBvE,MAFsB,EAAzB;AAGA,kBAAM,QAAKL,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BE,2BAAW,CADgB;AAE3BD,yBAASL;AAFkB,aAAzB,EAGHqB,MAHG,CAGI;AACNK,yBAAS;AADH,aAHJ,CAAN;AAMA,iBAAK,MAAM+D,IAAX,IAAmBC,UAAnB,EAA+B;AAC3B,sBAAM,QAAKhD,QAAL,CAAc+C,KAAK3D,QAAnB,EAA6B2D,KAAKxE,UAAlC,EAA8CwE,KAAK9D,MAAnD,CAAN;AACH;AACD,kBAAMzB,WAAW,MAAM,QAAKC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC5CC,yBAASL,MADmC;AAE5CO,yBAAS,CAFmC;AAG5CD,2BAAW;AAHiC,aAAzB,EAIpBE,MAJoB,EAAvB;AAKA;AACA,gBAAIC,aAAa,CAAjB;AACA,gBAAIC,cAAc,CAAlB;AACA,gBAAIC,oBAAoB,CAAxB;AACA,gBAAIC,qBAAqB,CAAzB;AACA,iBAAK,MAAME,QAAX,IAAuBZ,QAAvB,EAAiC;AAC7BO,8BAAcK,SAASa,MAAvB;AACAjB,+BAAeI,SAASa,MAAT,GAAkBb,SAASQ,YAA1C;AACA,oBAAI,CAACH,MAAMC,OAAN,CAAcN,SAASY,OAAvB,CAAL,EAAsC;AAClCf,yCAAqBG,SAASa,MAA9B;AACAf,0CAAsBE,SAASa,MAAT,GAAkBC,OAAOd,SAASQ,YAAhB,CAAxC;AACH;AACD;AACA,oBAAIO,OAAO,MAAM,QAAK1B,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AACvCY,wBAAIF,SAASgB;AAD0B,iBAA1B,EAEdC,KAFc,CAER,sCAFQ,EAEgCb,IAFhC,EAAjB;AAGA;AACA,oBAAI8G,MAAMnG,KAAKL,YAAf;AACA,oBAAIwG,OAAO,CAAX,EAAc;AACV,0BAAM,QAAK7H,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3Ba,oCAAYH,SAASG,UADM;AAE3BZ,iCAASL,MAFkB;AAG3B0B,iCAAS,CAHkB;AAI3BpB,mCAAW;AAJgB,qBAAzB,EAKHe,MALG,CAKI;AACNK,iCAAS;AADH,qBALJ,CAAN;AAQH;AACDZ,yBAASkB,YAAT,GAAwBH,KAAKG,YAA7B;AACAlB,yBAASU,YAAT,GAAwBK,KAAKL,YAA7B;AACAV,yBAASmB,YAAT,GAAwBnB,SAASa,MAAT,GAAkBC,OAAOd,SAASoB,YAAhB,CAA1C;AACH;AACD,gBAAIE,UAAUxB,mBAAmByB,OAAnB,CAA2B,CAA3B,CAAd;AACA,gBAAIC,UAAU1B,kBAAd;AACA,mBAAO;AACHV,0BAAUA,QADP;AAEHqC,2BAAW;AACP9B,gCAAYA,UADL;AAEPC,iCAAaA,YAAY2B,OAAZ,CAAoB,CAApB,CAFN;AAGP1B,uCAAmBA,iBAHZ;AAIPC,wCAAoBwB,OAJb;AAKP/B,6BAASL;AALF;AAFR,aAAP;AArD0B;AA+D7B;AAhrB+B,CAApC", "file": "..\\..\\..\\src\\api\\controller\\cart.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\nconst pinyin = require(\"pinyin\");\nmodule.exports = class extends Base {\n    async getCart(type) {\n\t\tconst userId = await this.getLoginUserId();\n        let cartList = [];\n        if(type == 0){\n            cartList = await this.model('cart').where({\n                user_id: userId,\n                is_delete: 0,\n                is_fast: 0,\n            }).select();\n        }\n        else{\n            cartList = await this.model('cart').where({\n                user_id: userId,\n                is_delete: 0,\n                is_fast: 1\n            }).select();\n        }\n        // 获取购物车统计信息\n        let goodsCount = 0;\n        let goodsAmount = 0;\n        let checkedGoodsCount = 0;\n        let checkedGoodsAmount = 0;\n        let numberChange = 0;\n        for (const cartItem of cartList) {\n            let product = await this.model('product').where({\n                id: cartItem.product_id,\n                is_delete: 0\n            }).find();\n            if (think.isEmpty(product)) {\n                await this.model('cart').where({\n                    product_id: cartItem.product_id,\n                    user_id: userId,\n                    is_delete: 0,\n                }).update({\n                    is_delete: 1\n                });\n            } else {\n                let retail_price = product.retail_price;\n                let productNum = product.goods_number;\n\t\t\t\t// 4.14 更新\n                if (productNum <= 0 || product.is_on_sale == 0) {\n                    await this.model('cart').where({\n                        product_id: cartItem.product_id,\n                        user_id: userId,\n                        checked: 1,\n                        is_delete: 0,\n                    }).update({\n                        checked: 0\n                    });\n                    cartItem.number = 0;\n                } else if (productNum > 0 && productNum < cartItem.number) {\n                    cartItem.number = productNum;\n                    numberChange = 1;\n                } else if (productNum > 0 && cartItem.number == 0) {\n                    cartItem.number = 1;\n                    numberChange = 1;\n                }\n                goodsCount += cartItem.number;\n                goodsAmount += cartItem.number * retail_price;\n                cartItem.retail_price = retail_price;\n                if (!think.isEmpty(cartItem.checked && productNum > 0)) {\n                    checkedGoodsCount += cartItem.number;\n                    checkedGoodsAmount += cartItem.number * Number(retail_price);\n                }\n                // 查找商品的图片\n                let info = await this.model('goods').where({\n                    id: cartItem.goods_id\n                }).field('list_pic_url').find();\n                cartItem.list_pic_url = info.list_pic_url;\n                cartItem.weight_count = cartItem.number * Number(cartItem.goods_weight);\n                await this.model('cart').where({\n                    product_id: cartItem.product_id,\n                    user_id: userId,\n                    is_delete: 0,\n                }).update({\n                    number: cartItem.number,\n                    add_price:retail_price\n                })\n            }\n        }\n        let cAmount = checkedGoodsAmount.toFixed(2);\n        let aAmount = checkedGoodsAmount;\n        return {\n            cartList: cartList,\n            cartTotal: {\n                goodsCount: goodsCount,\n                goodsAmount: goodsAmount.toFixed(2),\n                checkedGoodsCount: checkedGoodsCount,\n                checkedGoodsAmount: cAmount,\n                user_id: userId,\n                numberChange: numberChange\n            }\n        };\n    }\n    /**\n     * 获取购物车信息，所有对购物车的增删改操作，都要重新返回购物车的信息\n     * @return {Promise} []\n     */\n    async indexAction() {\n        return this.success(await this.getCart(0));\n    }\n    async addAgain(goodsId, productId, number) {\n\t\tconst userId = await this.getLoginUserId();\n        const currentTime = parseInt(new Date().getTime() / 1000);\n        const goodsInfo = await this.model('goods').where({\n            id: goodsId\n        }).find();\n        if (think.isEmpty(goodsInfo) || goodsInfo.is_on_sale == 0) {\n            return this.fail(400, '商品已下架');\n        }\n        // 取得规格的信息,判断规格库存\n        // const productInfo = await this.model('product').where({goods_id: goodsId, id: productId}).find();\n        const productInfo = await this.model('product').where({\n            id: productId\n        }).find();\n        // let productId = productInfo.id;\n        if (think.isEmpty(productInfo) || productInfo.goods_number < number) {\n            return this.fail(400, '库存不足');\n        }\n        // 判断购物车中是否存在此规格商品\n        const cartInfo = await this.model('cart').where({\n            user_id: userId,\n            product_id: productId,\n            is_delete: 0\n        }).find();\n        let retail_price = productInfo.retail_price;\n        if (think.isEmpty(cartInfo)) {\n            // 添加操作\n            // 添加规格名和值\n            let goodsSepcifitionValue = [];\n            if (!think.isEmpty(productInfo.goods_specification_ids)) {\n                goodsSepcifitionValue = await this.model('goods_specification').where({\n                    goods_id: productInfo.goods_id,\n                    is_delete: 0,\n                    id: {\n                        'in': productInfo.goods_specification_ids.split('_')\n                    }\n                }).getField('value');\n            }\n            // 添加到购物车\n            const cartData = {\n                goods_id: productInfo.goods_id,\n                product_id: productId,\n                goods_sn: productInfo.goods_sn,\n                goods_name: goodsInfo.name,\n                goods_aka: productInfo.goods_name,\n                // goods_weight: 移除重量字段\n                freight_template_id: goodsInfo.freight_template_id,\n                list_pic_url: goodsInfo.list_pic_url,\n                number: number,\n                user_id: userId,\n                retail_price: retail_price,\n                add_price: retail_price,\n                goods_specifition_name_value: goodsSepcifitionValue.join(';'),\n                goods_specifition_ids: productInfo.goods_specification_ids,\n                checked: 1,\n                add_time: currentTime\n            };\n            await this.model('cart').add(cartData);\n        } else {\n            // 如果已经存在购物车中，则数量增加\n            if (productInfo.goods_number < (number + cartInfo.number)) {\n                return this.fail(400, '库存都不够啦');\n            }\n            await this.model('cart').where({\n                user_id: userId,\n                product_id: productId,\n                is_delete: 0,\n                id: cartInfo.id\n            }).update({\n                retail_price: retail_price,\n                checked: 1,\n                number: number\n            });\n        }\n    }\n    /**\n     * 添加商品到购物车\n     * @returns {Promise.<*>}\n     */\n    async addAction() {\n        const goodsId = this.post('goodsId');\n\t\tconst userId = await this.getLoginUserId();\n        const productId = this.post('productId');\n        const number = this.post('number');\n        const addType = this.post('addType');\n        const currentTime = parseInt(new Date().getTime() / 1000);\n        // 判断商品是否可以购买\n        const goodsInfo = await this.model('goods').where({\n            id: goodsId\n        }).find();\n        if (think.isEmpty(goodsInfo) || goodsInfo.is_on_sale == 0) {\n            return this.fail(400, '商品已下架');\n        }\n        // 取得规格的信息,判断规格库存\n        // const productInfo = await this.model('product').where({goods_id: goodsId, id: productId}).find();\n        const productInfo = await this.model('product').where({\n            id: productId\n        }).find();\n        // let productId = productInfo.id;\n        if (think.isEmpty(productInfo) || productInfo.goods_number < number) {\n            return this.fail(400, '库存不足');\n        }\n        // 判断购物车中是否存在此规格商品\n        const cartInfo = await this.model('cart').where({\n            user_id: userId,\n            product_id: productId,\n            is_delete: 0\n        }).find();\n        let retail_price = productInfo.retail_price;\n        if (addType == 1) {\n            await this.model('cart').where({\n                is_delete: 0,\n                user_id: userId\n            }).update({\n                checked: 0\n            });\n            let goodsSepcifitionValue = [];\n            if (!think.isEmpty(productInfo.goods_specification_ids)) {\n                goodsSepcifitionValue = await this.model('goods_specification').where({\n                    goods_id: productInfo.goods_id,\n                    is_delete: 0,\n                    id: {\n                        'in': productInfo.goods_specification_ids.split('_')\n                    }\n                }).getField('value');\n            }\n            // 添加到购物车\n            const cartData = {\n                goods_id: productInfo.goods_id,\n                product_id: productId,\n                goods_sn: productInfo.goods_sn,\n                goods_name: goodsInfo.name,\n                goods_aka: productInfo.goods_name,\n                // goods_weight: 移除重量字段\n                freight_template_id: goodsInfo.freight_template_id,\n                list_pic_url: goodsInfo.list_pic_url,\n                number: number,\n                user_id: userId,\n                retail_price: retail_price,\n                add_price: retail_price,\n                goods_specifition_name_value: goodsSepcifitionValue.join(';'),\n                goods_specifition_ids: productInfo.goods_specification_ids,\n                checked: 1,\n                add_time: currentTime,\n                is_fast: 1\n            };\n            await this.model('cart').add(cartData);\n            return this.success(await this.getCart(1));\n        } else {\n            if (think.isEmpty(cartInfo)) {\n                // 添加操作\n                // 添加规格名和值\n                let goodsSepcifitionValue = [];\n                if (!think.isEmpty(productInfo.goods_specification_ids)) {\n                    goodsSepcifitionValue = await this.model('goods_specification').where({\n                        goods_id: productInfo.goods_id,\n                        is_delete: 0,\n                        id: {\n                            'in': productInfo.goods_specification_ids.split('_')\n                        }\n                    }).getField('value');\n                }\n                // 添加到购物车\n                const cartData = {\n                    goods_id: productInfo.goods_id,\n                    product_id: productId,\n                    goods_sn: productInfo.goods_sn,\n                    goods_name: goodsInfo.name,\n                    goods_aka: productInfo.goods_name,\n                    // goods_weight: 移除重量字段\n                    freight_template_id: goodsInfo.freight_template_id,\n                    list_pic_url: goodsInfo.list_pic_url,\n                    number: number,\n                    user_id: userId,\n                    retail_price: retail_price,\n                    add_price: retail_price,\n                    goods_specifition_name_value: goodsSepcifitionValue.join(';'),\n                    goods_specifition_ids: productInfo.goods_specification_ids,\n                    checked: 1,\n                    add_time: currentTime\n                };\n                await this.model('cart').add(cartData);\n            } else {\n                // 如果已经存在购物车中，则数量增加\n                if (productInfo.goods_number < (number + cartInfo.number)) {\n                    return this.fail(400, '库存都不够啦');\n                }\n                await this.model('cart').where({\n                    user_id: userId,\n                    product_id: productId,\n                    is_delete: 0,\n                    id: cartInfo.id\n                }).update({\n                    retail_price: retail_price\n                });\n                await this.model('cart').where({\n                    user_id: userId,\n                    product_id: productId,\n                    is_delete: 0,\n                    id: cartInfo.id\n                }).increment('number', number);\n            }\n            return this.success(await this.getCart(0));\n        }\n    }\n    // 更新指定的购物车信息\n    async updateAction() {\n        const productId = this.post('productId'); // 新的product_id\n        const id = this.post('id'); // cart.id\n        const number = parseInt(this.post('number')); // 不是\n        // 取得规格的信息,判断规格库存\n        const productInfo = await this.model('product').where({\n            id: productId,\n            is_delete: 0,\n        }).find();\n        if (think.isEmpty(productInfo) || productInfo.goods_number < number) {\n            return this.fail(400, '库存不足');\n        }\n        // 判断是否已经存在product_id购物车商品\n        const cartInfo = await this.model('cart').where({\n            id: id,\n            is_delete: 0\n        }).find();\n        // 只是更新number\n        if (cartInfo.product_id === productId) {\n            await this.model('cart').where({\n                id: id,\n                is_delete: 0\n            }).update({\n                number: number\n            });\n            return this.success(await this.getCart(0));\n        }\n    }\n    // 是否选择商品，如果已经选择，则取消选择，批量操作\n    async checkedAction() {\n\t\tconst userId = await this.getLoginUserId();\n        let productId = this.post('productIds').toString();\n        const isChecked = this.post('isChecked');\n        if (think.isEmpty(productId)) {\n            return this.fail('删除出错');\n        }\n        productId = productId.split(',');\n        await this.model('cart').where({\n            product_id: {\n                'in': productId\n            },\n            user_id: userId,\n            is_delete: 0\n        }).update({\n            checked: parseInt(isChecked)\n        });\n        return this.success(await this.getCart(0));\n    }\n    // 删除选中的购物车商品，批量删除\n    async deleteAction() {\n        let productId = this.post('productIds');\n\t\tconst userId = await this.getLoginUserId();\n        if (think.isEmpty(productId)) {\n            return this.fail('删除出错');\n        }\n        await this.model('cart').where({\n            product_id: productId,\n            user_id: userId,\n            is_delete: 0\n        }).update({\n            is_delete: 1\n        });\n        return this.success(await this.getCart(0));\n        // return this.success(productId);\n    }\n    // 获取购物车商品的总件件数\n    async goodsCountAction() {\n        const cartData = await this.getCart(0);\n\t\tconst userId = await this.getLoginUserId();\n        await this.model('cart').where({\n            user_id: userId,\n            is_delete: 0,\n            is_fast: 1\n        }).update({\n            is_delete: 1\n        });\n        return this.success({\n            cartTotal: {\n                goodsCount: cartData.cartTotal.goodsCount\n            }\n        });\n    }\n    /**\n     * 订单提交前的检验和填写相关订单信息\n     * @returns {Promise.<void>}\n     */\n    async checkoutAction() {\n        const currentTime = parseInt(new Date().getTime() / 1000);\n\t\tconst userId = await this.getLoginUserId();\n        let orderFrom = this.get('orderFrom');\n        const type = this.get('type'); // 是否团购\n        const addressId = this.get('addressId'); // 收货地址id\n        const addType = this.get('addType');\n        let goodsCount = 0; // 购物车的数量\n        let goodsMoney = 0; // 购物车的总价\n        let freightPrice = 0;\n        let outStock = 0;\n        let cartData = '';\n        // 获取要购买的商品\n        if (type == 0) {\n            if (addType == 0) {\n                cartData = await this.getCart(0);\n            } else if (addType == 1) {\n                cartData = await this.getCart(1);\n            } else if (addType == 2) {\n                cartData = await this.getAgainCart(orderFrom);\n            }\n        }\n        const checkedGoodsList = cartData.cartList.filter(function(v) {\n            return v.checked === 1;\n        });\n        for (const item of checkedGoodsList) {\n            goodsCount = goodsCount + item.number;\n            goodsMoney = goodsMoney + item.number * item.retail_price;\n            if (item.goods_number <= 0 || item.is_on_sale == 0) {\n                outStock = Number(outStock) + 1;\n            }\n        }\n        if (addType == 2) {\n            let againGoods = await this.model('order_goods').where({\n                order_id: orderFrom\n            }).select();\n            let againGoodsCount = 0;\n            for (const item of againGoods) {\n                againGoodsCount = againGoodsCount + item.number;\n            }\n            if (goodsCount != againGoodsCount) {\n                outStock = 1;\n            }\n        }\n        // 选择的收货地址\n        let checkedAddress = null;\n        if (addressId == '' || addressId == 0) {\n            // 没有指定地址ID，需要自动选择地址\n\n            // 首先获取用户的所有地址\n            const allAddresses = await this.model('address').where({\n                user_id: userId,\n                is_delete: 0\n            }).order('id desc').select();\n\n            if (allAddresses.length === 0) {\n                // 没有任何地址\n                checkedAddress = null;\n            } else if (allAddresses.length === 1) {\n                // 只有一个地址，自动选择这个地址\n                checkedAddress = allAddresses[0];\n            } else {\n                // 有多个地址，优先选择默认地址\n                checkedAddress = allAddresses.find(addr => addr.is_default === 1);\n\n                // 如果没有默认地址，选择最新添加的地址（第一个，因为按id desc排序）\n                if (!checkedAddress) {\n                    checkedAddress = allAddresses[0];\n                }\n            }\n        } else {\n            // 指定了地址ID，直接查找该地址\n            checkedAddress = await this.model('address').where({\n                id: addressId,\n                user_id: userId,\n\t\t\t\tis_delete:0\n            }).find();\n        }\n        if (!think.isEmpty(checkedAddress)) {\n            // 运费开始\n            // 先将促销规则中符合满件包邮或者满金额包邮的规则找到；\n            // 先看看是不是属于偏远地区。\n            let province_id = checkedAddress.province_id;\n            // 得到数组了，然后去判断这两个商品符不符合要求\n            // 先用这个goods数组去遍历\n            let cartGoods = checkedGoodsList;\n            let freightTempArray = await this.model('freight_template').where({\n                is_delete: 0\n            }).select();\n            let freightData = [];\n            for (const item in freightTempArray) {\n                freightData[item] = {\n                    id: freightTempArray[item].id,\n                    number: 0,\n                    money: 0,\n                    goods_weight: 0,\n                    freight_type: freightTempArray[item].freight_type\n                }\n            }\n            // 按件计算和按重量计算的区别是：按件，只要算goods_number就可以了，按重量要goods_number*goods_weight\n            // checkedGoodsList = [{goods_id:1,number5},{goods_id:2,number:3},{goods_id:3,number:2}]\n            for (const item of freightData) {\n                for (const cartItem of cartGoods) {\n                    if (item.id == cartItem.freight_template_id) {\n                        // 这个在判断，购物车中的商品是否属于这个运费模版，如果是，则加一，但是，这里要先判断下，这个商品是否符合满件包邮或满金额包邮，如果是包邮的，那么要去掉\n                        item.number = item.number + cartItem.number;\n                        item.money = item.money + cartItem.number * cartItem.retail_price;\n                        item.goods_weight = item.goods_weight + cartItem.number * cartItem.goods_weight;\n                    }\n                }\n            }\n            checkedAddress.province_name = await this.model('region').getRegionName(checkedAddress.province_id);\n            checkedAddress.city_name = await this.model('region').getRegionName(checkedAddress.city_id);\n            checkedAddress.district_name = await this.model('region').getRegionName(checkedAddress.district_id);\n            checkedAddress.full_region = checkedAddress.province_name + checkedAddress.city_name + checkedAddress.district_name;\n            for (const item of freightData) {\n                if (item.number == 0) {\n                    continue;\n                }\n                let ex = await this.model('freight_template_detail').where({\n                    template_id: item.id,\n                    area: province_id,\n                    is_delete: 0,\n                }).find();\n                let freight_price = 0;\n                if (!think.isEmpty(ex)) {\n                    // console.log('第一层：非默认邮费算法');\n                    let groupData = await this.model('freight_template_group').where({\n                        id: ex.group_id,\n                        is_delete:0\n                    }).find();\n                    // 不为空，说明有模板，那么应用模板，先去判断是否符合指定的包邮条件，不满足，那么根据type 是按件还是按重量\n                    let free_by_number = groupData.free_by_number;\n                    let free_by_money = groupData.free_by_money;\n                    // 4种情况，1、free_by_number > 0  2,free_by_money > 0  3,free_by_number free_by_money > 0,4都等于0\n                    let templateInfo = await this.model('freight_template').where({\n                        id: item.id,\n                        is_delete: 0,\n                    }).find();\n                    let freight_type = templateInfo.freight_type;\n                    if (freight_type == 0) {\n                        if (item.number > groupData.start) { // 说明大于首件了\n                            freight_price = groupData.start * groupData.start_fee + (item.number - 1) * groupData.add_fee; // todo 如果续件是2怎么办？？？\n                        } else {\n                            freight_price = groupData.start * groupData.start_fee;\n                        }\n                    } else if (freight_type == 1) {\n                        if (item.goods_weight > groupData.start) { // 说明大于首件了\n                            freight_price = groupData.start * groupData.start_fee + (item.goods_weight - 1) * groupData.add_fee; // todo 如果续件是2怎么办？？？\n                        } else {\n                            freight_price = groupData.start * groupData.start_fee;\n                        }\n                    }\n                    if (free_by_number > 0) {\n                        if (item.number >= free_by_number) {\n                            freight_price = 0;\n                        }\n                    }\n                    if (free_by_money > 0) {\n                        if (item.money >= free_by_money) {\n                            freight_price = 0;\n                        }\n                    }\n                } else {\n                    // console.log('第二层：使用默认的邮费算法');\n                    let groupData = await this.model('freight_template_group').where({\n                        template_id: item.id,\n                        area: 0,\n                        is_delete:0,\n                    }).find();\n                    let free_by_number = groupData.free_by_number;\n                    let free_by_money = groupData.free_by_money;\n                    let templateInfo = await this.model('freight_template').where({\n                        id: item.id,\n                        is_delete: 0,\n                    }).find();\n                    let freight_type = templateInfo.freight_type;\n                    if (freight_type == 0) {\n                        if (item.number > groupData.start) { // 说明大于首件了\n                            freight_price = groupData.start * groupData.start_fee + (item.number - 1) * groupData.add_fee; // todo 如果续件是2怎么办？？？\n                        } else {\n                            freight_price = groupData.start * groupData.start_fee;\n                        }\n                    } else if (freight_type == 1) {\n                        if (item.goods_weight > groupData.start) { // 说明大于首件了\n                            freight_price = groupData.start * groupData.start_fee + (item.goods_weight - 1) * groupData.add_fee; // todo 如果续件是2怎么办？？？\n                        } else {\n                            freight_price = groupData.start * groupData.start_fee;\n                        }\n                    }\n                    if (free_by_number > 0) {\n                        if (item.number >= free_by_number) {\n                            freight_price = 0;\n                        }\n                    }\n                    if (free_by_money > 0) {\n                        if (item.money >= free_by_money) {\n                            freight_price = 0;\n                        }\n                    }\n                }\n\t\t\t\tfreightPrice = freightPrice > freight_price?freightPrice:freight_price\n                // freightPrice = freightPrice + freight_price;\n                // 会得到 几个数组，然后用省id去遍历在哪个数组\n            }\n        } else {\n            checkedAddress = 0;\n        }\n        // 计算订单的费用\n        let goodsTotalPrice = cartData.cartTotal.checkedGoodsAmount; // 商品总价\n        // 获取是否有可用红包\n        let money = cartData.cartTotal.checkedGoodsAmount;\n        let orderTotalPrice = 0;\n        let def = await this.model('settings').where({\n            id: 1\n        }).find();\n        orderTotalPrice = Number(money) + Number(freightPrice) // 订单的总价\n        const actualPrice = orderTotalPrice; // 减去其它支付的金额后，要实际支付的金额\n        let numberChange = cartData.cartTotal.numberChange;\n        return this.success({\n            checkedAddress: checkedAddress,\n            freightPrice: freightPrice,\n            checkedGoodsList: checkedGoodsList,\n            goodsTotalPrice: goodsTotalPrice,\n            orderTotalPrice: orderTotalPrice.toFixed(2),\n            actualPrice: actualPrice.toFixed(2),\n            goodsCount: goodsCount,\n            outStock: outStock,\n            numberChange: numberChange,\n        });\n    }\n    async getAgainCart(orderFrom) {\n\t\tconst userId = await this.getLoginUserId();\n        const againGoods = await this.model('order_goods').where({\n            order_id: orderFrom\n        }).select();\n        await this.model('cart').where({\n            is_delete: 0,\n            user_id: userId\n        }).update({\n            checked: 0\n        });\n        for (const item of againGoods) {\n            await this.addAgain(item.goods_id, item.product_id, item.number);\n        }\n        const cartList = await this.model('cart').where({\n            user_id: userId,\n            is_fast: 0,\n            is_delete: 0\n        }).select();\n        // 获取购物车统计信息\n        let goodsCount = 0;\n        let goodsAmount = 0;\n        let checkedGoodsCount = 0;\n        let checkedGoodsAmount = 0;\n        for (const cartItem of cartList) {\n            goodsCount += cartItem.number;\n            goodsAmount += cartItem.number * cartItem.retail_price;\n            if (!think.isEmpty(cartItem.checked)) {\n                checkedGoodsCount += cartItem.number;\n                checkedGoodsAmount += cartItem.number * Number(cartItem.retail_price);\n            }\n            // 查找商品的图片\n            let info = await this.model('goods').where({\n                id: cartItem.goods_id\n            }).field('list_pic_url,goods_number,goods_unit').find();\n            // cartItem.list_pic_url = await this.model('goods').where({id: cartItem.goods_id}).getField('list_pic_url', true);\n            let num = info.goods_number;\n            if (num <= 0) {\n                await this.model('cart').where({\n                    product_id: cartItem.product_id,\n                    user_id: userId,\n                    checked: 1,\n                    is_delete: 0,\n                }).update({\n                    checked: 0\n                });\n            }\n            cartItem.list_pic_url = info.list_pic_url;\n            cartItem.goods_number = info.goods_number;\n            cartItem.weight_count = cartItem.number * Number(cartItem.goods_weight);\n        }\n        let cAmount = checkedGoodsAmount.toFixed(2);\n        let aAmount = checkedGoodsAmount;\n        return {\n            cartList: cartList,\n            cartTotal: {\n                goodsCount: goodsCount,\n                goodsAmount: goodsAmount.toFixed(2),\n                checkedGoodsCount: checkedGoodsCount,\n                checkedGoodsAmount: cAmount,\n                user_id: userId\n            }\n        };\n    }\n};"]}