{"admin": {"port": 8360, "workers": 0, "stickyCluster": false, "startServerTimeout": 3000, "reloadSignal": "SIGUSR2", "processKillTimeout": 10000, "jsonpCallbackField": "callback", "jsonContentType": "application/json", "jsonpContentType": "application/javascript", "errnoField": "errno", "errmsgField": "errmsg", "defaultErrno": 1000, "validateDefaultErrno": 1001, "default_module": "api", "weixin": {"appid": "wx919ca2ec612e6ecb", "secret": "4ad505935a4bf61f235efc303bf1e555", "mch_id": "1718527327", "partner_key": "APIV3JackC554487624asde32223UUsq", "notify_url": "https://ht.rxkjsdj.com/api/pay/notify", "refund_notify_url": "https://ht.rxkjsdj.com/api/pay/refund_notify", "cert_path": "./cert/apiclient_cert.pem", "key_path": "./cert/apiclient_key.pem", "shipping_management": {"enabled": true, "auto_sync": true, "msg_jump_path": "/pages/order/detail"}}, "express": {"appid": "12312312", "appkey": "123123123123123123123123", "request_url": "http://api.kdniao.com/Ebusiness/EbusinessOrderHandle.aspx"}, "mianexpress": {"appid": "123123", "appkey": "123123-4e61236-94cb5297309a", "request_url": "http://testapi.kdniao.com:8081/api/EOrderService", "print_url": "http://sandboxapi.kdniao.com:8080/kdniaosandbox/gateway/exterfaceInvoke.json", "ip_server_url": "http://www.kdniao.com/External/GetIp.aspx"}, "qiniu": {"access_key": "4Dey1S8bxqZjgpoo6h2r_W2MFkirvedK7vysREcP", "secret_key": "T8dG712sMGPNRmUiqyT9RfTC3WSEki44YOS0_I0b", "bucket": "<PERSON><PERSON><PERSON>", "domain": "http://img.rxkjsdj.com/"}, "qiniuHttps": {"access_key": "4Dey1S8bxqZjgpoo6h2r_W2MFkirvedK7vysREcP", "secret_key": "T8dG712sMGPNRmUiqyT9RfTC3WSEki44YOS0_I0b", "bucket": "<PERSON><PERSON><PERSON>", "domain": "http://img.rxkjsdj.com/", "zoneNum": 1}, "aliexpress": {"url": "http://wuliu.market.alicloudapi.com/kdi", "appcode": "67ba959aa7a84c37bb4ece2bc8683fbb"}, "templateId": {"deliveryId": "w6AMCJ0nVWTsFasdasdgnlNlmCf9TTDmG6_U"}, "cache": {"type": "file", "file": {"timeout": 86400000, "cachePath": "D:\\py-ide\\hioshop-miniprogram-master\\service\\runtime\\cache", "pathDepth": 1, "gcInterval": 86400000}}, "model": {"type": "mysql", "mysql": {"logConnect": true, "logSql": true, "database": "hiolabsDB", "prefix": "hiolabs_", "encoding": "utf8mb4", "host": "127.0.0.1", "port": "3306", "user": "root", "password": "19841020", "dateStrings": true}}, "logger": {"type": "console", "console": {}, "file": {"backups": 10, "absolute": true, "maxLogSize": 51200, "filename": "D:\\py-ide\\hioshop-miniprogram-master\\service\\logs\\app.log"}, "dateFile": {"level": "ALL", "absolute": true, "pattern": "-yyyy-MM-dd", "alwaysIncludePattern": true, "filename": "D:\\py-ide\\hioshop-miniprogram-master\\service\\logs\\app.log"}}, "view": {"type": "nunjucks", "nunjucks": {"viewPath": "D:\\py-ide\\hioshop-miniprogram-master\\service\\view", "sep": "_", "extname": ".html", "options": {}}}}, "api": {"port": 8360, "workers": 0, "stickyCluster": false, "startServerTimeout": 3000, "reloadSignal": "SIGUSR2", "processKillTimeout": 10000, "jsonpCallbackField": "callback", "jsonContentType": "application/json", "jsonpContentType": "application/javascript", "errnoField": "errno", "errmsgField": "errmsg", "defaultErrno": 1000, "validateDefaultErrno": 1001, "default_module": "api", "weixin": {"appid": "wx919ca2ec612e6ecb", "secret": "4ad505935a4bf61f235efc303bf1e555", "mch_id": "1718527327", "partner_key": "APIV3JackC554487624asde32223UUsq", "notify_url": "https://ht.rxkjsdj.com/api/pay/notify", "refund_notify_url": "https://ht.rxkjsdj.com/api/pay/refund_notify", "cert_path": "./cert/apiclient_cert.pem", "key_path": "./cert/apiclient_key.pem", "shipping_management": {"enabled": true, "auto_sync": true, "msg_jump_path": "/pages/order/detail"}}, "express": {"appid": "12312312", "appkey": "123123123123123123123123", "request_url": "http://api.kdniao.com/Ebusiness/EbusinessOrderHandle.aspx"}, "mianexpress": {"appid": "123123", "appkey": "123123-4e61236-94cb5297309a", "request_url": "http://testapi.kdniao.com:8081/api/EOrderService", "print_url": "http://sandboxapi.kdniao.com:8080/kdniaosandbox/gateway/exterfaceInvoke.json", "ip_server_url": "http://www.kdniao.com/External/GetIp.aspx"}, "qiniu": {"access_key": "4Dey1S8bxqZjgpoo6h2r_W2MFkirvedK7vysREcP", "secret_key": "T8dG712sMGPNRmUiqyT9RfTC3WSEki44YOS0_I0b", "bucket": "<PERSON><PERSON><PERSON>", "domain": "http://img.rxkjsdj.com/"}, "qiniuHttps": {"access_key": "4Dey1S8bxqZjgpoo6h2r_W2MFkirvedK7vysREcP", "secret_key": "T8dG712sMGPNRmUiqyT9RfTC3WSEki44YOS0_I0b", "bucket": "<PERSON><PERSON><PERSON>", "domain": "http://img.rxkjsdj.com/", "zoneNum": 1}, "aliexpress": {"url": "http://wuliu.market.alicloudapi.com/kdi", "appcode": "67ba959aa7a84c37bb4ece2bc8683fbb"}, "templateId": {"deliveryId": "w6AMCJ0nVWTsFasdasdgnlNlmCf9TTDmG6_U"}, "publicController": ["index", "catalog", "auth", "goods", "search", "region", "address", "signin"], "publicAction": ["cart/index", "cart/add", "cart/checked", "cart/update", "cart/delete", "cart/goodscount", "pay/notify"], "cache": {"type": "file", "file": {"timeout": 86400000, "cachePath": "D:\\py-ide\\hioshop-miniprogram-master\\service\\runtime\\cache", "pathDepth": 1, "gcInterval": 86400000}}, "model": {"type": "mysql", "mysql": {"logConnect": true, "logSql": true, "database": "hiolabsDB", "prefix": "hiolabs_", "encoding": "utf8mb4", "host": "127.0.0.1", "port": "3306", "user": "root", "password": "19841020", "dateStrings": true}}, "logger": {"type": "console", "console": {}, "file": {"backups": 10, "absolute": true, "maxLogSize": 51200, "filename": "D:\\py-ide\\hioshop-miniprogram-master\\service\\logs\\app.log"}, "dateFile": {"level": "ALL", "absolute": true, "pattern": "-yyyy-MM-dd", "alwaysIncludePattern": true, "filename": "D:\\py-ide\\hioshop-miniprogram-master\\service\\logs\\app.log"}}, "view": {"type": "nunjucks", "nunjucks": {"viewPath": "D:\\py-ide\\hioshop-miniprogram-master\\service\\view", "sep": "_", "extname": ".html", "options": {}}}}, "common": {"port": 8360, "workers": 0, "stickyCluster": false, "startServerTimeout": 3000, "reloadSignal": "SIGUSR2", "processKillTimeout": 10000, "jsonpCallbackField": "callback", "jsonContentType": "application/json", "jsonpContentType": "application/javascript", "errnoField": "errno", "errmsgField": "errmsg", "defaultErrno": 1000, "validateDefaultErrno": 1001, "default_module": "api", "weixin": {"appid": "wx919ca2ec612e6ecb", "secret": "4ad505935a4bf61f235efc303bf1e555", "mch_id": "1718527327", "partner_key": "APIV3JackC554487624asde32223UUsq", "notify_url": "https://ht.rxkjsdj.com/api/pay/notify", "refund_notify_url": "https://ht.rxkjsdj.com/api/pay/refund_notify", "cert_path": "./cert/apiclient_cert.pem", "key_path": "./cert/apiclient_key.pem", "shipping_management": {"enabled": true, "auto_sync": true, "msg_jump_path": "/pages/order/detail"}}, "express": {"appid": "12312312", "appkey": "123123123123123123123123", "request_url": "http://api.kdniao.com/Ebusiness/EbusinessOrderHandle.aspx"}, "mianexpress": {"appid": "123123", "appkey": "123123-4e61236-94cb5297309a", "request_url": "http://testapi.kdniao.com:8081/api/EOrderService", "print_url": "http://sandboxapi.kdniao.com:8080/kdniaosandbox/gateway/exterfaceInvoke.json", "ip_server_url": "http://www.kdniao.com/External/GetIp.aspx"}, "qiniu": {"access_key": "4Dey1S8bxqZjgpoo6h2r_W2MFkirvedK7vysREcP", "secret_key": "T8dG712sMGPNRmUiqyT9RfTC3WSEki44YOS0_I0b", "bucket": "<PERSON><PERSON><PERSON>", "domain": "http://img.rxkjsdj.com/"}, "qiniuHttps": {"access_key": "4Dey1S8bxqZjgpoo6h2r_W2MFkirvedK7vysREcP", "secret_key": "T8dG712sMGPNRmUiqyT9RfTC3WSEki44YOS0_I0b", "bucket": "<PERSON><PERSON><PERSON>", "domain": "http://img.rxkjsdj.com/", "zoneNum": 1}, "aliexpress": {"url": "http://wuliu.market.alicloudapi.com/kdi", "appcode": "67ba959aa7a84c37bb4ece2bc8683fbb"}, "templateId": {"deliveryId": "w6AMCJ0nVWTsFasdasdgnlNlmCf9TTDmG6_U"}, "cache": {"type": "file", "file": {"timeout": 86400000, "cachePath": "D:\\py-ide\\hioshop-miniprogram-master\\service\\runtime\\cache", "pathDepth": 1, "gcInterval": 86400000}}, "model": {"type": "mysql", "mysql": {"logConnect": true, "logSql": true, "database": "hiolabsDB", "prefix": "hiolabs_", "encoding": "utf8mb4", "host": "127.0.0.1", "port": "3306", "user": "root", "password": "19841020", "dateStrings": true}}, "logger": {"type": "console", "console": {}, "file": {"backups": 10, "absolute": true, "maxLogSize": 51200, "filename": "D:\\py-ide\\hioshop-miniprogram-master\\service\\logs\\app.log"}, "dateFile": {"level": "ALL", "absolute": true, "pattern": "-yyyy-MM-dd", "alwaysIncludePattern": true, "filename": "D:\\py-ide\\hioshop-miniprogram-master\\service\\logs\\app.log"}}, "view": {"type": "nunjucks", "nunjucks": {"viewPath": "D:\\py-ide\\hioshop-miniprogram-master\\service\\view", "sep": "_", "extname": ".html", "options": {}}}}, "config": {"port": 8360, "workers": 0, "stickyCluster": false, "startServerTimeout": 3000, "reloadSignal": "SIGUSR2", "processKillTimeout": 10000, "jsonpCallbackField": "callback", "jsonContentType": "application/json", "jsonpContentType": "application/javascript", "errnoField": "errno", "errmsgField": "errmsg", "defaultErrno": 1000, "validateDefaultErrno": 1001, "default_module": "api", "weixin": {"appid": "wx919ca2ec612e6ecb", "secret": "4ad505935a4bf61f235efc303bf1e555", "mch_id": "1718527327", "partner_key": "APIV3JackC554487624asde32223UUsq", "notify_url": "https://ht.rxkjsdj.com/api/pay/notify", "refund_notify_url": "https://ht.rxkjsdj.com/api/pay/refund_notify", "cert_path": "./cert/apiclient_cert.pem", "key_path": "./cert/apiclient_key.pem", "shipping_management": {"enabled": true, "auto_sync": true, "msg_jump_path": "/pages/order/detail"}}, "express": {"appid": "12312312", "appkey": "123123123123123123123123", "request_url": "http://api.kdniao.com/Ebusiness/EbusinessOrderHandle.aspx"}, "mianexpress": {"appid": "123123", "appkey": "123123-4e61236-94cb5297309a", "request_url": "http://testapi.kdniao.com:8081/api/EOrderService", "print_url": "http://sandboxapi.kdniao.com:8080/kdniaosandbox/gateway/exterfaceInvoke.json", "ip_server_url": "http://www.kdniao.com/External/GetIp.aspx"}, "qiniu": {"access_key": "4Dey1S8bxqZjgpoo6h2r_W2MFkirvedK7vysREcP", "secret_key": "T8dG712sMGPNRmUiqyT9RfTC3WSEki44YOS0_I0b", "bucket": "<PERSON><PERSON><PERSON>", "domain": "http://img.rxkjsdj.com/"}, "qiniuHttps": {"access_key": "4Dey1S8bxqZjgpoo6h2r_W2MFkirvedK7vysREcP", "secret_key": "T8dG712sMGPNRmUiqyT9RfTC3WSEki44YOS0_I0b", "bucket": "<PERSON><PERSON><PERSON>", "domain": "http://img.rxkjsdj.com/", "zoneNum": 1}, "aliexpress": {"url": "http://wuliu.market.alicloudapi.com/kdi", "appcode": "67ba959aa7a84c37bb4ece2bc8683fbb"}, "templateId": {"deliveryId": "w6AMCJ0nVWTsFasdasdgnlNlmCf9TTDmG6_U"}, "cache": {"type": "file", "file": {"timeout": 86400000, "cachePath": "D:\\py-ide\\hioshop-miniprogram-master\\service\\runtime\\cache", "pathDepth": 1, "gcInterval": 86400000}}, "model": {"type": "mysql", "mysql": {"logConnect": true, "logSql": true, "database": "hiolabsDB", "prefix": "hiolabs_", "encoding": "utf8mb4", "host": "127.0.0.1", "port": "3306", "user": "root", "password": "19841020", "dateStrings": true}}, "logger": {"type": "console", "console": {}, "file": {"backups": 10, "absolute": true, "maxLogSize": 51200, "filename": "D:\\py-ide\\hioshop-miniprogram-master\\service\\logs\\app.log"}, "dateFile": {"level": "ALL", "absolute": true, "pattern": "-yyyy-MM-dd", "alwaysIncludePattern": true, "filename": "D:\\py-ide\\hioshop-miniprogram-master\\service\\logs\\app.log"}}, "view": {"type": "nunjucks", "nunjucks": {"viewPath": "D:\\py-ide\\hioshop-miniprogram-master\\service\\view", "sep": "_", "extname": ".html", "options": {}}}}}