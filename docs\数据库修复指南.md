# 分销池数据库修复指南

## 🚨 问题描述

在运行分销池功能时遇到以下错误：

1. **字段不存在错误**: `Unknown column 'add_time' in 'field list'`
2. **表不存在错误**: `Table 'hiolabsdb.hiolabs_goods_distribution' doesn't exist`

## 🔧 解决方案

### 方法一：执行修复SQL脚本（推荐）

1. **找到修复脚本**
   ```
   service/database/fix_distribution_pool.sql
   ```

2. **连接数据库**
   使用MySQL客户端或phpMyAdmin连接到您的数据库

3. **执行修复脚本**
   ```sql
   -- 在MySQL客户端中执行
   source /path/to/service/database/fix_distribution_pool.sql;
   
   -- 或者直接复制粘贴SQL内容执行
   ```

### 方法二：手动执行SQL命令

如果无法执行整个脚本，可以分步执行：

#### 1. 修复商品表add_time字段
```sql
-- 检查字段是否存在，如果不存在则添加
ALTER TABLE `hiolabs_goods` 
ADD COLUMN `add_time` int(10) unsigned NOT NULL DEFAULT 0 COMMENT "创建时间";

-- 更新现有数据的时间戳
UPDATE `hiolabs_goods` SET `add_time` = UNIX_TIMESTAMP() WHERE `add_time` = 0;
```

#### 2. 创建分销配置表
```sql
CREATE TABLE IF NOT EXISTS `hiolabs_goods_distribution` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_id` int(10) unsigned NOT NULL COMMENT '商品ID',
  `is_distributed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已分销 0:未分销 1:已分销',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '佣金比例（百分比）',
  `commission_type` varchar(20) NOT NULL DEFAULT 'default' COMMENT '佣金类型',
  `personal_rate` decimal(5,2) DEFAULT '0.00' COMMENT '个人推广佣金比例(%)',
  `level1_rate` decimal(5,2) DEFAULT '0.00' COMMENT '一级分销佣金比例(%)',
  `level2_rate` decimal(5,2) DEFAULT '0.00' COMMENT '二级分销佣金比例(%)',
  `team_leader_rate` decimal(5,2) DEFAULT '0.00' COMMENT '团长额外佣金比例(%)',
  `min_level_required` tinyint(1) DEFAULT '1' COMMENT '最低分销等级要求',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `goods_id` (`goods_id`),
  KEY `is_distributed` (`is_distributed`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分销配置表';
```

#### 3. 创建佣金模板表
```sql
CREATE TABLE IF NOT EXISTS `hiolabs_commission_templates` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_code` varchar(50) NOT NULL COMMENT '模板代码',
  `personal_rate` decimal(5,2) DEFAULT '0.00' COMMENT '个人推广佣金比例',
  `level1_rate` decimal(5,2) DEFAULT '0.00' COMMENT '一级分销佣金比例',
  `level2_rate` decimal(5,2) DEFAULT '0.00' COMMENT '二级分销佣金比例',
  `team_leader_rate` decimal(5,2) DEFAULT '0.00' COMMENT '团长额外佣金比例',
  `min_level_required` tinyint(1) DEFAULT '1' COMMENT '最低等级要求',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认模板',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_code` (`template_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销佣金规则模板表';
```

#### 4. 插入默认模板数据
```sql
INSERT IGNORE INTO `hiolabs_commission_templates` 
(`template_name`, `template_code`, `personal_rate`, `level1_rate`, `level2_rate`, `team_leader_rate`, `min_level_required`, `is_default`, `is_active`, `create_time`, `update_time`) 
VALUES
('基础商品模板', 'basic', 8.00, 3.00, 1.00, 2.00, 1, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('中档商品模板', 'medium', 10.00, 4.00, 2.00, 3.00, 2, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('高档商品模板', 'premium', 15.00, 6.00, 3.00, 5.00, 3, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
```

## ✅ 验证修复结果

执行完修复后，可以通过以下SQL验证：

```sql
-- 1. 检查商品表add_time字段
DESCRIBE hiolabs_goods;

-- 2. 检查分销配置表是否存在
SHOW TABLES LIKE 'hiolabs_goods_distribution';

-- 3. 检查表结构
DESCRIBE hiolabs_goods_distribution;

-- 4. 检查模板数据
SELECT * FROM hiolabs_commission_templates;
```

## 🚀 重启服务

修复完数据库后：

1. **重启后端服务**
   ```bash
   cd service
   npm restart
   # 或者
   pm2 restart hioshop
   ```

2. **刷新前端页面**
   重新访问分销池管理页面

## 📝 注意事项

1. **备份数据库**: 执行修复前请先备份数据库
2. **检查权限**: 确保数据库用户有CREATE TABLE和ALTER TABLE权限
3. **字符集**: 确保数据库使用utf8mb4字符集
4. **时区**: 注意时间戳的时区设置

## 🔍 常见问题

### Q: 执行SQL时提示权限不足
A: 请使用具有管理员权限的数据库用户执行

### Q: 表已存在但结构不完整
A: 可以先删除表再重新创建，或者使用ALTER TABLE添加缺失字段

### Q: 时间戳显示异常
A: 检查服务器时区设置，确保与数据库时区一致

修复完成后，分销池功能应该可以正常使用了！
