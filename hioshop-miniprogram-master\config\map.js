/**
 * 地图服务配置
 */

module.exports = {
  // 腾讯地图API配置
  tencent: {
    // 逆地理编码API密钥
    // 请在腾讯位置服务官网申请：https://lbs.qq.com/
    apiKey: 'ZTTBZ-ULKY3-K5V35-RZC4N-IVOYS-N5FBF',
    
    // 逆地理编码API地址
    geocoderUrl: 'https://apis.map.qq.com/ws/geocoder/v1/',
    
    // 其他配置
    timeout: 10000, // 请求超时时间（毫秒）
    retryCount: 2   // 重试次数
  },
  
  // 百度地图API配置（备用）
  baidu: {
    apiKey: 'YOUR_BAIDU_MAP_API_KEY',
    geocoderUrl: 'https://api.map.baidu.com/reverse_geocoding/v3/'
  },
  
  // 高德地图API配置（备用）
  amap: {
    apiKey: 'YOUR_AMAP_API_KEY', 
    geocoderUrl: 'https://restapi.amap.com/v3/geocode/regeo'
  }
};
