{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\specification.js"], "names": ["Base", "require", "module", "exports", "checkGoodsInFlashSale", "goodsId", "activeCampaigns", "model", "where", "goods_id", "status", "select", "length", "campaignNames", "map", "c", "name", "join", "inFlashSale", "message", "campaigns", "error", "console", "indexAction", "data", "id", "success", "getGoodsSpecAction", "post", "is_delete", "specData", "specification_id", "item", "goods_spec_id", "goods_specification_ids", "specValueData", "find", "value", "dataInfo", "specValue", "productUpdateAction", "goods_number", "goods_weight", "goods_sn", "retail_price", "cost", "idData", "field", "flashSaleCheck", "fail", "updateInfo", "update", "goods_specification_id", "info", "pro", "goodsNum", "sum", "maxPrice", "max", "minPrice", "min", "maxCost", "minCost", "goodsPrice", "costPrice", "cost_price", "min_retail_price", "min_cost_price", "productDeleAction", "productId", "limit", "delete", "lastData", "delePrimarySpecAction", "detailAction", "addAction", "sort", "sort_order", "add", "checkSnAction", "sn", "updateAction", "deleteAction", "goods_spec", "log"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACAC,OAAOC,OAAP,GAAiB,cAAcH,IAAd,CAAmB;;AAEhC;;;;;AAKMI,yBAAN,CAA4BC,OAA5B,EAAqC;AAAA;;AAAA;AACjC,gBAAI;AACA;AACA,sBAAMC,kBAAkB,MAAM,MAAKC,KAAL,CAAW,sBAAX,EAAmCC,KAAnC,CAAyC;AACnEC,8BAAUJ,OADyD;AAEnEK,4BAAQ,CAAC,IAAD,EAAO,CAAC,QAAD,EAAW,UAAX,CAAP;AAF2D,iBAAzC,EAG3BC,MAH2B,EAA9B;;AAKA,oBAAIL,gBAAgBM,MAAhB,GAAyB,CAA7B,EAAgC;AAC5B,0BAAMC,gBAAgBP,gBAAgBQ,GAAhB,CAAoB;AAAA,+BAAKC,EAAEC,IAAP;AAAA,qBAApB,EAAiCC,IAAjC,CAAsC,GAAtC,CAAtB;AACA,2BAAO;AACHC,qCAAa,IADV;AAEHC,iCAAU,eAAcN,aAAc,SAFnC;AAGHO,mCAAWd;AAHR,qBAAP;AAKH;;AAED,uBAAO;AACHY,iCAAa,KADV;AAEHC,6BAAS;AAFN,iBAAP;AAKH,aArBD,CAqBE,OAAOE,KAAP,EAAc;AACZC,wBAAQD,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO;AACHH,iCAAa,KADV;AAEHC,6BAAS,YAFN;AAGHE,2BAAOA,MAAMF;AAHV,iBAAP;AAKH;AA7BgC;AA8BpC;;AAED;;;;AAIMI,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAMhB,QAAQ,OAAKA,KAAL,CAAW,eAAX,CAAd;AACA,kBAAMiB,OAAO,MAAMjB,MAAMC,KAAN,CAAY;AAC3BiB,oBAAI,CAAC,GAAD,EAAM,CAAN;AADuB,aAAZ,EAEhBd,MAFgB,EAAnB;AAGA,mBAAO,OAAKe,OAAL,CAAaF,IAAb,CAAP;AALgB;AAMnB;AACKG,sBAAN,GAA2B;AAAA;;AAAA;AACvB,kBAAMF,KAAK,OAAKG,IAAL,CAAU,IAAV,CAAX;AACA,kBAAMrB,QAAQ,OAAKA,KAAL,CAAW,SAAX,CAAd;AACA,kBAAMiB,OAAO,MAAMjB,MAAMC,KAAN,CAAY;AAC3BC,0BAAUgB,EADiB;AAE3BI,2BAAW;AAFgB,aAAZ,EAGhBlB,MAHgB,EAAnB;AAIA;AACA,gBAAImB,WAAW,EAAf;AACA,gBAAIC,mBAAmB,CAAvB;AACA,iBAAK,MAAMC,IAAX,IAAmBR,IAAnB,EAAyB;AACrB,oBAAIS,gBAAgBD,KAAKE,uBAAzB;AACA,oBAAIC,gBAAgB,MAAM,OAAK5B,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AAC9DiB,wBAAIQ,aAD0D;AAE9DJ,+BAAW;AAFmD,iBAAxC,EAGvBO,IAHuB,EAA1B;AAIAL,mCAAmBI,cAAcJ,gBAAjC;AACAC,qBAAKK,KAAL,GAAaF,cAAcE,KAA3B;AACH;AACD,gBAAIC,WAAW;AACXR,0BAAUN,IADC;AAEXe,2BAAWR;AAFA,aAAf;AAIA,mBAAO,OAAKL,OAAL,CAAaY,QAAb,CAAP;AAvBuB;AAwB1B;AACKE,uBAAN,GAA4B;AAAA;;AAAA;AACxB,kBAAMC,eAAe,OAAKb,IAAL,CAAU,cAAV,CAArB;AACA,kBAAMc,eAAe,OAAKd,IAAL,CAAU,cAAV,CAArB;AACA,kBAAMe,WAAW,OAAKf,IAAL,CAAU,UAAV,CAAjB;AACA,kBAAMgB,eAAe,OAAKhB,IAAL,CAAU,cAAV,CAArB;AACA,kBAAMiB,OAAO,OAAKjB,IAAL,CAAU,MAAV,CAAb;AACA,kBAAMS,QAAQ,OAAKT,IAAL,CAAU,OAAV,CAAd;;AAEA;AACA,kBAAMrB,QAAQ,OAAKA,KAAL,CAAW,SAAX,CAAd;AACA,gBAAIuC,SAAS,MAAMvC,MAAMC,KAAN,CAAY;AAC3BmC,0BAAUA;AADiB,aAAZ,EAEhBI,KAFgB,CAEV,kCAFU,EAE0BX,IAF1B,EAAnB;AAGA,gBAAI3B,WAAWqC,OAAOrC,QAAtB;;AAEA;AACA,kBAAMuC,iBAAiB,MAAM,OAAK5C,qBAAL,CAA2BK,QAA3B,CAA7B;AACA,gBAAIuC,eAAe9B,WAAnB,EAAgC;AAC5B,uBAAO,OAAK+B,IAAL,CAAU,GAAV,EAAeD,eAAe7B,OAA9B,CAAP;AACH;;AAED,gBAAI+B,aAAa;AACbT,8BAAcA,YADD;AAEbC,8BAAcA,YAFD;AAGbG,sBAAMA,IAHO;AAIbD,8BAAcA;AAJD,aAAjB;AAMA,kBAAM,OAAKrC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BmC,0BAAUA;AADiB,aAAzB,EAEHQ,MAFG,CAEI;AACNP,8BAAcA;AADR,aAFJ,CAAN;;AAMA,kBAAMrC,MAAMC,KAAN,CAAY;AACdmC,0BAAUA;AADI,aAAZ,EAEHQ,MAFG,CAEID,UAFJ,CAAN;;AAIA,gBAAIE,yBAAyBN,OAAOZ,uBAApC;AACA,gBAAImB,OAAO,MAAM,OAAK9C,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AACrDiB,oBAAI2B;AADiD,aAAxC,EAEdD,MAFc,CAEP;AACNd,uBAAOA;AADD,aAFO,CAAjB;AAKA;AACA,gBAAIiB,MAAM,MAAM,OAAK/C,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AACxCC,0BAAUA;AAD8B,aAA5B,EAEbE,MAFa,EAAhB;AAGA,gBAAI2C,IAAI1C,MAAJ,GAAa,CAAjB,EAAoB;AAChB,oBAAI2C,WAAW,MAAM,OAAKhD,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC7CC,8BAAUA;AADmC,iBAA5B,EAElB+C,GAFkB,CAEd,cAFc,CAArB;AAGA,oBAAIC,WAAW,MAAM,OAAKlD,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC7CC,8BAAUA;AADmC,iBAA5B,EAElBiD,GAFkB,CAEd,cAFc,CAArB;AAGA,oBAAIC,WAAW,MAAM,OAAKpD,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC7CC,8BAAUA;AADmC,iBAA5B,EAElBmD,GAFkB,CAEd,cAFc,CAArB;AAGA,oBAAIC,UAAU,MAAM,OAAKtD,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC5CC,8BAAUA;AADkC,iBAA5B,EAEjBiD,GAFiB,CAEb,MAFa,CAApB;AAGA,oBAAII,UAAU,MAAM,OAAKvD,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC5CC,8BAAUA;AADkC,iBAA5B,EAEjBmD,GAFiB,CAEb,MAFa,CAApB;AAGA,oBAAIG,aAAaJ,WAAW,GAAX,GAAiBF,QAAlC;AACA,oBAAIO,YAAYF,UAAU,GAAV,GAAgBD,OAAhC;AACA,sBAAM,OAAKtD,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5BiB,wBAAIhB;AADwB,iBAA1B,EAEH0C,MAFG,CAEI;AACNV,kCAAcc,QADR;AAENX,kCAAcmB,UAFR;AAGNE,gCAAYD,SAHN;AAINE,sCAAkBP,QAJZ;AAKNQ,oCAAgBL;AALV,iBAFJ,CAAN;AASH,aA3BD,MA2BO;AACH,sBAAM,OAAKvD,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5BiB,wBAAIhB;AADwB,iBAA1B,EAEH0C,MAFG,CAEI;AACNV,kCAAcA,YADR;AAENG,kCAAcA,YAFR;AAGNqB,gCAAYpB,IAHN;AAINqB,sCAAkBtB,YAJZ;AAKNuB,oCAAgBtB;AALV,iBAFJ,CAAN;AASH;AACD,mBAAO,OAAKnB,OAAL,CAAa2B,IAAb,CAAP;AArFwB;AAsF3B;AACKe,qBAAN,GAA0B;AAAA;;AAAA;AACtB,kBAAMC,YAAY,OAAKzC,IAAL,CAAU,IAAV,CAAlB;AACA,kBAAMrB,QAAQ,OAAKA,KAAL,CAAW,SAAX,CAAd;AACA,gBAAIuC,SAAS,MAAMvC,MAAMC,KAAN,CAAY;AAC3BiB,oBAAI4C;AADuB,aAAZ,EAEhBtB,KAFgB,CAEV,kCAFU,EAE0BX,IAF1B,EAAnB;AAGA,gBAAIgB,yBAAyBN,OAAOZ,uBAApC;AACA,gBAAIzB,WAAWqC,OAAOrC,QAAtB;AACA,kBAAMF,MAAMC,KAAN,CAAY;AACdiB,oBAAI4C;AADU,aAAZ,EAEHC,KAFG,CAEG,CAFH,EAEMC,MAFN,EAAN;AAGA,gBAAIlB,OAAO,MAAM,OAAK9C,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AACrDiB,oBAAI2B;AADiD,aAAxC,EAEdkB,KAFc,CAER,CAFQ,EAELC,MAFK,EAAjB;AAGA,gBAAIC,WAAW,MAAMjE,MAAMC,KAAN,CAAY;AAC7BC,0BAAUA;AADmB,aAAZ,EAElBE,MAFkB,EAArB;AAGA,gBAAI6D,SAAS5D,MAAT,IAAmB,CAAvB,EAA0B;AACtB,oBAAI2C,WAAW,MAAM,OAAKhD,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC7CC,8BAAUA;AADmC,iBAA5B,EAElB+C,GAFkB,CAEd,cAFc,CAArB;AAGA,oBAAIO,aAAa,MAAM,OAAKxD,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC/CC,8BAAUA;AADqC,iBAA5B,EAEpBmD,GAFoB,CAEhB,cAFgB,CAAvB;AAGA,sBAAM,OAAKrD,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5BiB,wBAAIhB;AADwB,iBAA1B,EAEH0C,MAFG,CAEI;AACNV,kCAAcc,QADR;AAENX,kCAAcmB;AAFR,iBAFJ,CAAN;AAMH;AACD,mBAAO,OAAKrC,OAAL,CAAa2B,IAAb,CAAP;AA/BsB;AAgCzB;AACKoB,yBAAN,GAA8B;AAAA;;AAAA;AAC1B,kBAAMhE,WAAW,OAAKmB,IAAL,CAAU,IAAV,CAAjB;AACA,kBAAMrB,QAAQ,OAAKA,KAAL,CAAW,SAAX,CAAd;AACA,kBAAMA,MAAMC,KAAN,CAAY;AACdC,0BAAUA;AADI,aAAZ,EAEH8D,MAFG,EAAN;AAGA,gBAAIlB,OAAO,MAAM,OAAK9C,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AACrDC,0BAAUA;AAD2C,aAAxC,EAEd8D,MAFc,EAAjB;AAGA,kBAAM,OAAKhE,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5BiB,oBAAIhB;AADwB,aAA1B,EAEH0C,MAFG,CAEI;AACNV,8BAAc,CADR;AAENG,8BAAc;AAFR,aAFJ,CAAN;AAMA,mBAAO,OAAKlB,OAAL,CAAa2B,IAAb,CAAP;AAf0B;AAgB7B;AACKqB,gBAAN,GAAoB;AAAA;;AAAA;AAChB,gBAAIjD,KAAK,OAAKG,IAAL,CAAU,IAAV,CAAT;AACA,gBAAIyB,OAAO,MAAM,OAAK9C,KAAL,CAAW,eAAX,EAA4BC,KAA5B,CAAkC;AAC/CiB,oBAAGA;AAD4C,aAAlC,EAEdW,IAFc,EAAjB;AAGA,mBAAO,OAAKV,OAAL,CAAa2B,IAAb,CAAP;AALgB;AAMnB;AACKsB,aAAN,GAAkB;AAAA;;AAAA;AACd,kBAAMtC,QAAQ,OAAKT,IAAL,CAAU,MAAV,CAAd;AACA,kBAAMgD,OAAO,OAAKhD,IAAL,CAAU,YAAV,CAAb;AACA,gBAAIyB,OAAO;AACPrC,sBAAMqB,KADC;AAEPwC,4BAAYD;AAFL,aAAX;AAIA,kBAAMrE,QAAQ,OAAKA,KAAL,CAAW,eAAX,CAAd;AACA,kBAAMiB,OAAO,MAAMjB,MAAMuE,GAAN,CAAUzB,IAAV,CAAnB;AACA,mBAAO,OAAK3B,OAAL,CAAaF,IAAb,CAAP;AATc;AAUjB;AACKuD,iBAAN,GAAsB;AAAA;;AAAA;AAClB,kBAAMC,KAAK,OAAKpD,IAAL,CAAU,IAAV,CAAX;AACA,kBAAMrB,QAAQ,OAAKA,KAAL,CAAW,SAAX,CAAd;AACA,kBAAMiB,OAAO,MAAMjB,MAAMC,KAAN,CAAY;AAC3BmC,0BAAUqC;AADiB,aAAZ,EAEhBrE,MAFgB,EAAnB;AAGA,gBAAIa,KAAKZ,MAAL,GAAc,CAAlB,EAAqB;AACjB,uBAAO,OAAKqC,IAAL,CAAU,OAAV,CAAP;AACH,aAFD,MAEO;AACH,uBAAO,OAAKvB,OAAL,CAAaF,IAAb,CAAP;AACH;AAViB;AAWrB;AACKyD,gBAAN,GAAqB;AAAA;;AAAA;AACjB,kBAAMxD,KAAK,QAAKG,IAAL,CAAU,IAAV,CAAX;AACA,kBAAMS,QAAQ,QAAKT,IAAL,CAAU,MAAV,CAAd;AACA,kBAAMgD,OAAO,QAAKhD,IAAL,CAAU,YAAV,CAAb;AACA,gBAAIyB,OAAO;AACPrC,sBAAMqB,KADC;AAEPwC,4BAAYD;AAFL,aAAX;AAIA,kBAAMrE,QAAQ,QAAKA,KAAL,CAAW,eAAX,CAAd;AACA,kBAAMiB,OAAO,MAAMjB,MAAMC,KAAN,CAAY;AAC3BiB,oBAAIA;AADuB,aAAZ,EAEhB0B,MAFgB,CAETE,IAFS,CAAnB;AAGA,mBAAO,QAAK3B,OAAL,CAAaF,IAAb,CAAP;AAZiB;AAapB;AACK0D,gBAAN,GAAqB;AAAA;;AAAA;AACjB,kBAAMzD,KAAK,QAAKG,IAAL,CAAU,IAAV,CAAX;AACA,kBAAMuD,aAAa,MAAM,QAAK5E,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC;AAC7DuB,kCAAkBN,EAD2C;AAE7DI,2BAAW;AAFkD,aAAxC,EAGtBlB,MAHsB,EAAzB;AAIAW,oBAAQ8D,GAAR,CAAYD,UAAZ;AACA,gBAAIA,WAAWvE,MAAX,GAAoB,CAAxB,EAA2B;AACvB,uBAAO,QAAKqC,IAAL,CAAU,eAAV,CAAP;AACH,aAFD,MAEO;AACH,sBAAM1C,QAAQ,QAAKA,KAAL,CAAW,eAAX,CAAd;AACA,sBAAMiB,OAAO,MAAMjB,MAAMC,KAAN,CAAY;AAC3BiB,wBAAIA;AADuB,iBAAZ,EAEhB6C,KAFgB,CAEV,CAFU,EAEPC,MAFO,EAAnB;AAGA,uBAAO,QAAK7C,OAAL,CAAaF,IAAb,CAAP;AACH;AAfgB;AAgBpB;AAhR+B,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\specification.js", "sourcesContent": ["const Base = require('./base.js');\nmodule.exports = class extends Base {\n\n    /**\n     * 检查商品是否正在参与秒杀活动\n     * @param {number} goodsId 商品ID\n     * @returns {Promise<Object>} 返回检查结果\n     */\n    async checkGoodsInFlashSale(goodsId) {\n        try {\n            // 检查是否有进行中或即将开始的秒杀活动\n            const activeCampaigns = await this.model('flash_sale_campaigns').where({\n                goods_id: goodsId,\n                status: ['IN', ['active', 'upcoming']]\n            }).select();\n\n            if (activeCampaigns.length > 0) {\n                const campaignNames = activeCampaigns.map(c => c.name).join('、');\n                return {\n                    inFlashSale: true,\n                    message: `该商品正在参与秒杀活动：${campaignNames}，无法修改价格`,\n                    campaigns: activeCampaigns\n                };\n            }\n\n            return {\n                inFlashSale: false,\n                message: '商品未参与秒杀活动，可以修改价格'\n            };\n\n        } catch (error) {\n            console.error('检查商品秒杀状态失败:', error);\n            return {\n                inFlashSale: false,\n                message: '检查失败，但允许操作',\n                error: error.message\n            };\n        }\n    }\n\n    /**\n     * index action\n     * @return {Promise} []\n     */\n    async indexAction() {\n        const model = this.model('specification');\n        const data = await model.where({\n            id: ['>', 0]\n        }).select();\n        return this.success(data);\n    }\n    async getGoodsSpecAction() {\n        const id = this.post('id');\n        const model = this.model('product');\n        const data = await model.where({\n            goods_id: id,\n            is_delete: 0\n        }).select();\n        //TODO 这里只有一层，以后如果有多重型号，如一件商品既有颜色又有尺寸时，这里的代码是不对的。以后再写。\n        let specData = [];\n        let specification_id = 0;\n        for (const item of data) {\n            let goods_spec_id = item.goods_specification_ids;\n            let specValueData = await this.model('goods_specification').where({\n                id: goods_spec_id,\n                is_delete: 0\n            }).find();\n            specification_id = specValueData.specification_id;\n            item.value = specValueData.value;\n        }\n        let dataInfo = {\n            specData: data,\n            specValue: specification_id\n        };\n        return this.success(dataInfo);\n    }\n    async productUpdateAction() {\n        const goods_number = this.post('goods_number');\n        const goods_weight = this.post('goods_weight');\n        const goods_sn = this.post('goods_sn');\n        const retail_price = this.post('retail_price');\n        const cost = this.post('cost');\n        const value = this.post('value');\n\n        // 先获取商品ID用于检查秒杀状态\n        const model = this.model('product');\n        let idData = await model.where({\n            goods_sn: goods_sn\n        }).field('goods_specification_ids,goods_id').find();\n        let goods_id = idData.goods_id;\n\n        // 检查商品是否正在参与秒杀\n        const flashSaleCheck = await this.checkGoodsInFlashSale(goods_id);\n        if (flashSaleCheck.inFlashSale) {\n            return this.fail(400, flashSaleCheck.message);\n        }\n\n        let updateInfo = {\n            goods_number: goods_number,\n            goods_weight: goods_weight,\n            cost: cost,\n            retail_price: retail_price\n        }\n        await this.model('cart').where({\n            goods_sn: goods_sn\n        }).update({\n            retail_price: retail_price\n        });\n\n        await model.where({\n            goods_sn: goods_sn\n        }).update(updateInfo);\n\n        let goods_specification_id = idData.goods_specification_ids\n        let info = await this.model('goods_specification').where({\n            id: goods_specification_id\n        }).update({\n            value: value\n        });\n        // todo 价格显示为区间\n        let pro = await this.model('product').where({\n            goods_id: goods_id\n        }).select();\n        if (pro.length > 1) {\n            let goodsNum = await this.model('product').where({\n                goods_id: goods_id\n            }).sum('goods_number');\n            let maxPrice = await this.model('product').where({\n                goods_id: goods_id\n            }).max('retail_price');\n            let minPrice = await this.model('product').where({\n                goods_id: goods_id\n            }).min('retail_price');\n            let maxCost = await this.model('product').where({\n                goods_id: goods_id\n            }).max('cost');\n            let minCost = await this.model('product').where({\n                goods_id: goods_id\n            }).min('cost');\n            let goodsPrice = minPrice + '-' + maxPrice;\n            let costPrice = minCost + '-' + maxCost;\n            await this.model('goods').where({\n                id: goods_id\n            }).update({\n                goods_number: goodsNum,\n                retail_price: goodsPrice,\n                cost_price: costPrice,\n                min_retail_price: minPrice,\n                min_cost_price: minCost,\n            });\n        } else {\n            await this.model('goods').where({\n                id: goods_id\n            }).update({\n                goods_number: goods_number,\n                retail_price: retail_price,\n                cost_price: cost,\n                min_retail_price: retail_price,\n                min_cost_price: cost,\n            });\n        }\n        return this.success(info);\n    }\n    async productDeleAction() {\n        const productId = this.post('id');\n        const model = this.model('product');\n        let idData = await model.where({\n            id: productId\n        }).field('goods_specification_ids,goods_id').find();\n        let goods_specification_id = idData.goods_specification_ids;\n        let goods_id = idData.goods_id;\n        await model.where({\n            id: productId\n        }).limit(1).delete();\n        let info = await this.model('goods_specification').where({\n            id: goods_specification_id\n        }).limit(1).delete();\n        let lastData = await model.where({\n            goods_id: goods_id\n        }).select();\n        if (lastData.length != 0) {\n            let goodsNum = await this.model('product').where({\n                goods_id: goods_id\n            }).sum('goods_number');\n            let goodsPrice = await this.model('product').where({\n                goods_id: goods_id\n            }).min('retail_price');\n            await this.model('goods').where({\n                id: goods_id\n            }).update({\n                goods_number: goodsNum,\n                retail_price: goodsPrice\n            });\n        }\n        return this.success(info);\n    }\n    async delePrimarySpecAction() {\n        const goods_id = this.post('id');\n        const model = this.model('product');\n        await model.where({\n            goods_id: goods_id\n        }).delete();\n        let info = await this.model('goods_specification').where({\n            goods_id: goods_id\n        }).delete();\n        await this.model('goods').where({\n            id: goods_id\n        }).update({\n            goods_number: 0,\n            retail_price: 0\n        });\n        return this.success(info);\n    }\n    async detailAction(){\n        let id = this.post('id');\n        let info = await this.model('specification').where({\n            id:id\n        }).find();\n        return this.success(info);\n    }\n    async addAction() {\n        const value = this.post('name');\n        const sort = this.post('sort_order');\n        let info = {\n            name: value,\n            sort_order: sort\n        }\n        const model = this.model('specification');\n        const data = await model.add(info);\n        return this.success(data);\n    }\n    async checkSnAction() {\n        const sn = this.post('sn');\n        const model = this.model('product');\n        const data = await model.where({\n            goods_sn: sn\n        }).select();\n        if (data.length > 0) {\n            return this.fail('sn已存在');\n        } else {\n            return this.success(data);\n        }\n    }\n    async updateAction() {\n        const id = this.post('id');\n        const value = this.post('name');\n        const sort = this.post('sort_order');\n        let info = {\n            name: value,\n            sort_order: sort\n        }\n        const model = this.model('specification');\n        const data = await model.where({\n            id: id\n        }).update(info);\n        return this.success(data);\n    }\n    async deleteAction() {\n        const id = this.post('id');\n        const goods_spec = await this.model('goods_specification').where({\n            specification_id: id,\n            is_delete: 0\n        }).select();\n        console.log(goods_spec);\n        if (goods_spec.length > 0) {\n            return this.fail('该型号下有商品，暂不能删除')\n        } else {\n            const model = this.model('specification');\n            const data = await model.where({\n                id: id\n            }).limit(1).delete();\n            return this.success(data);\n        }\n    }\n};"]}