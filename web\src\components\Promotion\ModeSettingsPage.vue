<template>
  <div class="mode-settings-page">
    <!-- 页面头部 -->
    <div class="page-header bg-white shadow-sm border-b">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">分销模式</h1>
            <p class="text-sm text-gray-500 mt-1">配置分销模式和等级方案</p>
          </div>
          <div class="flex items-center space-x-3">
            <button @click="saveSettings" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <i class="ri-save-line mr-2"></i>
              保存设置
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="p-6 max-w-6xl mx-auto">
      <!-- 分销员等级方案 -->
      <div class="bg-white rounded-lg shadow-sm border mb-6">
        <div class="px-6 py-4 border-b">
          <h2 class="text-lg font-semibold text-gray-900">分销员等级方案</h2>
          <p class="text-sm text-gray-500 mt-1">设置不同等级的分销员及其佣金比例</p>
        </div>

        <div class="p-6">
          <!-- 当前方案显示 -->
          <div class="mb-6">
            <div class="flex items-center space-x-4 mb-4">
              <span class="text-sm text-gray-600">当前方案:</span>
              <div class="flex space-x-2">
                <button
                  v-for="condition in conditions"
                  :key="condition.key"
                  @click="activeCondition = condition.key"
                  :class="[
                    'px-3 py-1 text-sm rounded-md transition-colors',
                    activeCondition === condition.key
                      ? 'bg-blue-100 text-blue-700 border border-blue-300'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  ]"
                >
                  {{ condition.label }}
                </button>
              </div>
            </div>
          </div>

          <!-- 等级列表 -->
          <div class="space-y-4">
            <div
              v-for="(level, index) in distributorLevels"
              :key="index"
              class="border rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                <!-- 等级名 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">等级名</label>
                  <input
                    v-model="level.name"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入等级名称"
                  />
                </div>

                <!-- 佣金比例 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">佣金比例</label>
                  <div class="flex items-center">
                    <input
                      v-model="level.commission"
                      type="number"
                      step="0.01"
                      min="0"
                      max="100"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="0.00"
                    />
                    <span class="ml-2 text-sm text-gray-500">%</span>
                  </div>
                </div>

                <!-- 升级条件 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">升级条件</label>
                  <input
                    v-model="level.requirement"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="设置升级条件"
                  />
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-center space-x-2">
                  <button
                    @click="addLevel(index + 1)"
                    class="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
                  >
                    <i class="ri-add-line"></i>
                  </button>
                  <button
                    v-if="distributorLevels.length > 1"
                    @click="removeLevel(index)"
                    class="px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm"
                  >
                    <i class="ri-delete-bin-line"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 添加等级按钮 -->
          <div class="mt-4">
            <button
              @click="addLevel()"
              class="px-4 py-2 border-2 border-dashed border-gray-300 text-gray-600 rounded-lg hover:border-blue-400 hover:text-blue-600 transition-colors w-full"
            >
              <i class="ri-add-line mr-2"></i>
              添加新等级
            </button>
          </div>

          <!-- 提示信息 -->
          <div class="mt-4 p-4 bg-blue-50 rounded-lg">
            <div class="flex items-start">
              <i class="ri-information-line text-blue-600 mt-0.5 mr-2"></i>
              <div class="text-sm text-blue-800">
                <p class="font-medium mb-1">设置说明：</p>
                <ul class="list-disc list-inside space-y-1 text-blue-700">
                  <li>可对每个商品设置佣金，设置后将覆盖等级佣金。</li>
                  <li>建议按照业绩或推广能力设置不同等级。</li>
                  <li>佣金比例建议控制在合理范围内。</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 更多设置 -->
      <div class="bg-white rounded-lg shadow-sm border mb-6">
        <div class="px-6 py-4 border-b">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900">更多设置</h2>
            <button
              @click="showMoreSettings = !showMoreSettings"
              class="text-blue-600 hover:text-blue-700 text-sm flex items-center"
            >
              {{ showMoreSettings ? '收起' : '展开' }}
              <i :class="['ri-arrow-down-s-line ml-1 transition-transform', showMoreSettings ? 'rotate-180' : '']"></i>
            </button>
          </div>
        </div>

        <div v-show="showMoreSettings" class="p-6 space-y-6">
          <!-- 分销员名称 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">分销员名称:</label>
            <input
              v-model="settings.distributorName"
              type="text"
              class="w-64 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="分销员"
            />
            <p class="text-xs text-gray-500 mt-1">自定义分销员称谓，如推广员、代理商等</p>
          </div>

          <!-- 分享返利 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-3">分享返利:</label>
            <div class="space-y-3">
              <label class="flex items-center">
                <input
                  v-model="settings.shareReward.type"
                  type="radio"
                  value="percentage"
                  class="mr-2 text-blue-600"
                />
                <span class="text-sm text-gray-700">按比例</span>
              </label>
              <label class="flex items-center">
                <input
                  v-model="settings.shareReward.type"
                  type="radio"
                  value="fixed"
                  class="mr-2 text-blue-600"
                />
                <span class="text-sm text-gray-700">固定金额</span>
              </label>
            </div>
          </div>

          <!-- 分享标识 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-3">分享标识:</label>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div
                v-for="(badge, index) in shareBadges"
                :key="index"
                @click="settings.selectedBadge = index"
                :class="[
                  'border-2 rounded-lg p-3 cursor-pointer transition-all',
                  settings.selectedBadge === index
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                ]"
              >
                <div class="w-16 h-16 mx-auto mb-2 bg-gradient-to-br rounded-lg flex items-center justify-center text-white font-bold text-lg"
                     :style="{ background: badge.gradient }">
                  {{ badge.text }}
                </div>
                <p class="text-xs text-center text-gray-600">{{ badge.name }}</p>
              </div>
            </div>
          </div>

          <!-- 开启条件查看分销 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-3">开启条件查看分销:</label>
            <div class="space-y-3">
              <label class="flex items-center">
                <input
                  v-model="settings.viewCondition"
                  type="radio"
                  value="show"
                  class="mr-2 text-blue-600"
                />
                <span class="text-sm text-gray-700">显示</span>
              </label>
              <label class="flex items-center">
                <input
                  v-model="settings.viewCondition"
                  type="radio"
                  value="hide"
                  class="mr-2 text-blue-600"
                />
                <span class="text-sm text-gray-700">不显示</span>
              </label>
            </div>
            <p class="text-xs text-gray-500 mt-2">
              当前设置为：分销员可以查看其推广的客户信息（佣金公开透明，有利于推广）
            </p>
          </div>
        </div>
      </div>

      <!-- 分销海报设置 -->
      <div class="bg-white rounded-lg shadow-sm border">
        <div class="px-6 py-4 border-b">
          <h2 class="text-lg font-semibold text-gray-900">分销海报设置</h2>
          <p class="text-sm text-gray-500 mt-1">自定义分销推广海报样式</p>
        </div>

        <div class="p-6">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 海报模板选择 -->
            <div>
              <h3 class="text-sm font-medium text-gray-700 mb-4">选择海报模板</h3>
              <div class="grid grid-cols-2 gap-4">
                <div
                  v-for="(template, index) in posterTemplates"
                  :key="index"
                  @click="settings.selectedPoster = index"
                  :class="[
                    'border-2 rounded-lg p-4 cursor-pointer transition-all',
                    settings.selectedPoster === index
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  ]"
                >
                  <div class="aspect-[3/4] bg-gradient-to-br rounded-lg mb-3 flex items-center justify-center text-white font-bold"
                       :style="{ background: template.gradient }">
                    <i :class="template.icon + ' text-3xl'"></i>
                  </div>
                  <p class="text-sm text-center text-gray-700">{{ template.name }}</p>
                </div>
              </div>
            </div>

            <!-- 海报预览 -->
            <div>
              <h3 class="text-sm font-medium text-gray-700 mb-4">海报预览</h3>
              <div class="border rounded-lg p-4 bg-gray-50">
                <div class="aspect-[3/4] bg-white rounded-lg shadow-sm flex items-center justify-center">
                  <div class="text-center text-gray-500">
                    <i class="ri-image-line text-4xl mb-2"></i>
                    <p class="text-sm">海报预览</p>
                    <p class="text-xs">选择模板后显示预览</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 海报设置选项 -->
          <div class="mt-6 pt-6 border-t">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">海报标题</label>
                <input
                  v-model="settings.posterTitle"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入海报标题"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">海报副标题</label>
                <input
                  v-model="settings.posterSubtitle"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入海报副标题"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModeSettingsPage',
  data() {
    return {
      // 当前激活的条件
      activeCondition: 'condition',

      // 条件选项
      conditions: [
        { key: 'condition', label: '按条件' },
        { key: 'manual', label: '修改' }
      ],

      // 分销员等级配置
      distributorLevels: [
        {
          name: '普通分销员',
          commission: 0.00,
          requirement: '(1/10)'
        }
      ],

      // 显示更多设置
      showMoreSettings: false,

      // 系统设置
      settings: {
        distributorName: '分销员',
        shareReward: {
          type: 'percentage'
        },
        selectedBadge: 0,
        viewCondition: 'show',
        selectedPoster: 0,
        posterTitle: '',
        posterSubtitle: ''
      },

      // 分享标识选项
      shareBadges: [
        {
          text: '赚',
          name: '橙色标识',
          gradient: 'linear-gradient(135deg, #ff9500 0%, #ff6b00 100%)'
        },
        {
          text: '分享',
          name: '红色标识',
          gradient: 'linear-gradient(135deg, #ff4757 0%, #ff3838 100%)'
        },
        {
          text: '分享',
          name: '粉色标识',
          gradient: 'linear-gradient(135deg, #ff6b9d 0%, #ff4757 100%)'
        },
        {
          text: '自定义',
          name: '自定义',
          gradient: 'linear-gradient(135deg, #a4b0be 0%, #747d8c 100%)'
        }
      ],

      // 海报模板
      posterTemplates: [
        {
          name: '简约风格',
          icon: 'ri-layout-line',
          gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        },
        {
          name: '商务风格',
          icon: 'ri-briefcase-line',
          gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
        },
        {
          name: '时尚风格',
          icon: 'ri-palette-line',
          gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
        },
        {
          name: '经典风格',
          icon: 'ri-star-line',
          gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
        }
      ]
    }
  },

  methods: {
    // 添加等级
    addLevel(index = null) {
      const newLevel = {
        name: '',
        commission: 0.00,
        requirement: ''
      };

      if (index !== null) {
        this.distributorLevels.splice(index, 0, newLevel);
      } else {
        this.distributorLevels.push(newLevel);
      }
    },

    // 删除等级
    removeLevel(index) {
      if (this.distributorLevels.length > 1) {
        this.distributorLevels.splice(index, 1);
      }
    },

    // 保存设置
    saveSettings() {
      // 验证数据
      const hasEmptyLevel = this.distributorLevels.some(level =>
        !level.name.trim() || level.commission === '' || level.commission < 0
      );

      if (hasEmptyLevel) {
        alert('请完善所有等级信息');
        return;
      }

      // 模拟保存
      console.log('保存分销设置:', {
        levels: this.distributorLevels,
        settings: this.settings
      });

      // 显示成功消息
      alert('分销设置保存成功');
    }
  },

  mounted() {
    console.log('分销模式页面已加载');
  }
}
</script>

<style scoped>
.mode-settings-page {
  min-height: 100vh;
  background-color: #f9fafb;
}

.page-header {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 输入框聚焦效果 */
input:focus {
  outline: none;
  ring: 2px;
  ring-color: #3b82f6;
  border-color: transparent;
}

/* 单选按钮样式 */
input[type="radio"]:checked {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

/* 按钮悬停效果 */
.hover\:bg-blue-700:hover {
  background-color: #1d4ed8;
}

.hover\:bg-red-700:hover {
  background-color: #b91c1c;
}

.hover\:bg-green-700:hover {
  background-color: #15803d;
}

/* 过渡动画 */
.transition-all {
  transition: all 0.3s ease;
}

.transition-colors {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

.transition-transform {
  transition: transform 0.2s ease;
}

.rotate-180 {
  transform: rotate(180deg);
}

/* 卡片悬停效果 */
.hover\:shadow-md:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 边框虚线悬停效果 */
.hover\:border-blue-400:hover {
  border-color: #60a5fa;
}

.hover\:text-blue-600:hover {
  color: #2563eb;
}

/* 网格布局响应式 */
@media (max-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 表单元素间距 */
.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

/* 图标样式 */
.ri-add-line,
.ri-delete-bin-line,
.ri-save-line,
.ri-arrow-down-s-line,
.ri-image-line,
.ri-layout-line,
.ri-briefcase-line,
.ri-palette-line,
.ri-star-line,
.ri-information-line {
  font-size: inherit;
}
</style>
