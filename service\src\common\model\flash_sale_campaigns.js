module.exports = class extends think.Model {
  
  /**
   * 获取活动的基本信息
   */
  async getCampaignInfo(campaignId) {
    const campaign = await this.where({ id: campaignId }).find();
    if (think.isEmpty(campaign)) {
      return null;
    }
    
    // 获取商品信息
    const goodsModel = this.model('goods');
    const goods = await goodsModel.where({ id: campaign.goods_id }).find();
    
    return {
      ...campaign,
      goods: goods
    };
  }
  
  /**
   * 检查活动是否可以参与
   */
  async canParticipate(campaignId) {
    const campaign = await this.where({ id: campaignId }).find();
    if (think.isEmpty(campaign)) {
      return { canParticipate: false, reason: '活动不存在' };
    }
    
    if (campaign.status !== 'active') {
      return { canParticipate: false, reason: '活动未开始或已结束' };
    }
    
    const now = new Date();
    const startDate = new Date(campaign.start_date);
    const endDate = new Date(campaign.end_date + ' 23:59:59');
    
    if (now < startDate || now > endDate) {
      return { canParticipate: false, reason: '不在活动时间范围内' };
    }
    
    return { canParticipate: true };
  }
  
  /**
   * 获取用户在活动中的参与记录
   */
  async getUserRecord(campaignId, userId) {
    const recordModel = this.model('flash_sale_user_records');
    return await recordModel.where({
      campaign_id: campaignId,
      user_id: userId
    }).find();
  }
  
  /**
   * 更新用户参与记录
   */
  async updateUserRecord(campaignId, userId, quantity) {
    const recordModel = this.model('flash_sale_user_records');
    
    const existing = await recordModel.where({
      campaign_id: campaignId,
      user_id: userId
    }).find();
    
    if (think.isEmpty(existing)) {
      // 创建新记录
      return await recordModel.add({
        campaign_id: campaignId,
        user_id: userId,
        total_purchased: quantity,
        last_purchase_time: new Date()
      });
    } else {
      // 更新现有记录
      return await recordModel.where({
        campaign_id: campaignId,
        user_id: userId
      }).update({
        total_purchased: existing.total_purchased + quantity,
        last_purchase_time: new Date()
      });
    }
  }
};
