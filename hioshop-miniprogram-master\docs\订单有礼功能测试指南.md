# 订单有礼功能测试指南

## 功能概述

订单有礼功能允许用户通过输入第三方平台（淘宝、京东、拼多多等）的订单号来兑换积分，是一个引流用户到私域的营销工具。

**重要更新：** 现在订单有礼功能采用与签到功能相同的严格权限验证，用户必须完善个人信息（头像和昵称）后才能使用兑换功能。

## 权限验证机制

### 严格的用户信息验证
订单有礼功能现在使用 `auth.isFullyAuthorized()` 进行严格的权限验证，以下用户将被要求完善信息：

- ❌ **没有token或userInfo**
- ❌ **昵称为"点我登录"或"点击登录"**（占位信息）
- ❌ **昵称为"微信用户"**（静默登录的默认昵称）
- ❌ **is_authorized为false或0**（未授权标识）

只有满足以下条件的用户才能使用兑换功能：
- ✅ **有完整的token和userInfo**
- ✅ **昵称不是默认占位符**
- ✅ **is_authorized为true**
- ✅ **用户已主动完善过头像和昵称**

## 测试前准备

### 1. 数据库配置
确保已执行数据库脚本：
```sql
-- 执行 service/database/order_exchange_tables.sql
```

### 2. 后端服务
确保后端服务正常运行，包含以下API接口：
- `GET /api/order-exchange/config` - 获取兑换配置
- `POST /api/order-exchange/exchange` - 执行订单兑换
- `GET /api/order-exchange/records` - 获取兑换记录

## 测试用例

### 测试用例1：未完善信息用户访问
**步骤：**
1. 清除小程序数据或使用静默登录用户
2. 打开订单兑换页面
3. 观察页面显示内容
4. 点击"立即完善"按钮

**预期结果：**
- 页面显示完善信息提示区域，包含：
  - 用户图标 👤
  - 标题："完善个人信息"
  - 描述："为了使用订单兑换功能，请先设置您的头像和昵称"
  - "立即完善"按钮
- 不显示订单号输入框和兑换按钮
- 不显示兑换记录按钮
- 点击"立即完善"跳转到 `/pages/app-auth/index?from=order-exchange`

### 测试用例2：用户完善信息流程
**步骤：**
1. 从完善信息提示页面点击"立即完善"
2. 在授权页面选择头像（可选）
3. 输入昵称（必填）
4. 点击"完成设置"
5. 自动返回订单兑换页面

**预期结果：**
- 用户信息保存成功，`is_authorized` 设置为 `true`
- 自动返回订单兑换页面
- 完善信息提示区域消失
- 显示完整的兑换界面：
  - 订单号输入框
  - "立即兑换"按钮
  - "查看兑换记录"按钮
- 用户可以正常使用兑换功能

### 测试用例3：权限验证测试
**步骤：**
1. 使用静默登录用户（昵称为"微信用户"）
2. 尝试访问订单兑换页面
3. 观察权限检查结果
4. 尝试直接输入订单号（如果可能）

**预期结果：**
- 页面显示完善信息提示区域
- 不显示订单号输入框和兑换按钮
- 不显示兑换记录按钮
- 页面状态：`needCompleteInfo: true`，`hasUserInfo: false`
- 如果尝试其他操作，会提示"请先完善个人信息"

### 测试用例4：成功兑换订单
**步骤：**
1. 输入测试订单号（如：`TB1234567890`）
2. 点击"立即兑换"按钮
3. 等待兑换结果

**预期结果：**
- 显示"兑换成功"弹窗
- 显示获得的积分数量
- 兑换记录中新增一条记录
- 用户积分增加

**测试订单号格式：**
- 淘宝：`TB` + 数字（如：TB1234567890）
- 天猫：`TM` + 数字（如：TM1234567890）
- 京东：`JD` + 数字（如：JD1234567890）
- 拼多多：`PDD` + 数字（如：PDD1234567890）

### 测试用例4：重复兑换检测
**步骤：**
1. 使用已兑换过的订单号再次兑换
2. 点击"立即兑换"按钮

**预期结果：**
- 显示"该订单号已经兑换过了"错误提示
- 不增加积分

### 测试用例5：每日限制检测
**步骤：**
1. 连续兑换4个不同的订单号
2. 第4次兑换时观察结果

**预期结果：**
- 前3次兑换成功
- 第4次显示"今日兑换次数已达上限"错误提示

### 测试用例6：无效订单号
**步骤：**
1. 输入包含"invalid"或"error"的订单号
2. 点击"立即兑换"按钮

**预期结果：**
- 显示"订单不存在或已失效"错误提示

### 测试用例7：兑换记录查看
**步骤：**
1. 点击页面右上角的"📋"图标
2. 查看兑换记录弹窗

**预期结果：**
- 显示用户的兑换历史记录
- 记录包含平台、订单号、日期等信息

## 积分计算规则

- **默认兑换比例：** 1元 = 0.5积分
- **最低订单金额：** 50元
- **每日兑换限制：** 3次
- **单次最高积分：** 1000积分

## 模拟订单金额

系统会根据订单号前缀模拟不同的订单金额：
- TB开头：99.00元 → 49积分
- TM开头：158.00元 → 79积分  
- JD开头：299.00元 → 149积分
- PDD开头：45.00元 → 不满足最低金额要求
- 其他：随机50-250元

## 常见问题排查

### 1. 登录后页面不刷新
- 检查 `checkUserAuth()` 方法是否正确调用
- 确认 `onShow()` 生命周期正常执行

### 2. API请求失败
- 检查后端服务是否启动
- 确认API路径配置正确
- 查看控制台错误日志

### 3. 积分未正确增加
- 检查 `points` 模型的 `addUserPoints` 方法
- 确认数据库表结构正确
- 查看后端日志

### 4. 兑换记录不显示
- 确认 `loadExchangeRecords()` 方法调用
- 检查API返回数据格式
- 验证用户openid是否正确

## 后台管理

可以通过后台管理系统查看和管理订单兑换：
- 访问路径：`/dashboard/marketing/order-gifts`
- 功能包括：兑换配置、数据统计、记录管理

## 注意事项

1. 测试时请使用开发环境，避免影响生产数据
2. 订单验证目前为模拟实现，实际使用需要对接真实的第三方API
3. 积分系统需要与现有的用户积分体系保持一致
4. 建议在正式上线前进行充分的压力测试
