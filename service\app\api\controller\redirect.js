function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

const Base = require('./base.js');
const rp = require('request-promise');

module.exports = class extends Base {

    /**
     * 生成URL Link
     * GET /api/redirect/generate-url-link
     */
    generateUrlLinkAction() {
        var _this = this;

        return _asyncToGenerator(function* () {
            try {
                const orderNumber = _this.get('orderNumber') || '';
                const source = _this.get('source') || 'web';
                const utm = _this.get('utm') || '';
                const userId = _this.get('userId') || '';

                // 构建小程序路径和参数
                const queryParams = [];
                if (orderNumber) queryParams.push(`orderNumber=${encodeURIComponent(orderNumber)}`);
                if (source) queryParams.push(`source=${encodeURIComponent(source)}`);
                if (utm) queryParams.push(`utm=${encodeURIComponent(utm)}`);
                if (userId) queryParams.push(`userId=${encodeURIComponent(userId)}`);

                const query = queryParams.join('&');
                const path = `pages/order-exchange/index${query ? '?' + query : ''}`;

                console.log('生成URL Link，路径:', path);

                // 调用微信API生成URL Link
                const urlLink = yield _this.generateWechatUrlLink(path);

                return _this.success({
                    urlLink: urlLink,
                    path: path,
                    query: query
                });
            } catch (error) {
                console.error('生成URL Link失败:', error);
                return _this.fail(500, '生成链接失败: ' + error.message);
            }
        })();
    }

    /**
     * 调用微信API生成URL Link
     */
    generateWechatUrlLink(path) {
        var _this2 = this;

        return _asyncToGenerator(function* () {
            try {
                // 获取access_token
                const accessToken = yield _this2.getAccessToken();

                // 调用微信API生成URL Link
                const url = `https://api.weixin.qq.com/wxa/generate_urllink?access_token=${accessToken}`;

                const requestData = {
                    path: path,
                    query: '',
                    env_version: 'develop', // develop-开发版, trial-体验版, release-正式版
                    is_expire: false
                };

                console.log('调用微信API生成URL Link:', requestData);

                const response = yield rp({
                    method: 'POST',
                    uri: url,
                    body: requestData,
                    json: true
                });

                console.log('微信API响应:', response);

                if (response.errcode === 0) {
                    return response.url_link;
                } else {
                    throw new Error(`微信API错误: ${response.errcode} - ${response.errmsg}`);
                }
            } catch (error) {
                console.error('调用微信API失败:', error);
                throw error;
            }
        })();
    }

    /**
     * 获取微信access_token
     */
    getAccessToken() {
        return _asyncToGenerator(function* () {
            try {
                const appid = think.config('weixin.appid');
                const secret = think.config('weixin.secret');

                if (!appid || !secret) {
                    throw new Error('微信配置不完整，请检查appid和secret');
                }

                // 检查缓存中是否有有效的access_token
                const cacheKey = 'wechat_access_token';
                let accessToken = yield think.cache(cacheKey);

                if (!accessToken) {
                    // 从微信API获取新的access_token
                    const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appid}&secret=${secret}`;

                    const response = yield rp({
                        method: 'GET',
                        uri: url,
                        json: true
                    });

                    if (response.access_token) {
                        accessToken = response.access_token;
                        // 缓存access_token，有效期7000秒（微信官方是7200秒，提前200秒刷新）
                        yield think.cache(cacheKey, accessToken, 7000);
                        console.log('获取新的access_token成功');
                    } else {
                        throw new Error(`获取access_token失败: ${response.errcode} - ${response.errmsg}`);
                    }
                } else {
                    console.log('使用缓存的access_token');
                }

                return accessToken;
            } catch (error) {
                console.error('获取access_token失败:', error);
                throw error;
            }
        })();
    }

    /**
     * 测试页面 - 用于微信开发者工具测试
     * GET /api/redirect/test
     */
    testAction() {
        var _this3 = this;

        return _asyncToGenerator(function* () {
            const html = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Link 测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; color: #555; }
        input, select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; }
        button { background: #007bff; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 10px 5px; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff; }
        .link { word-break: break-all; color: #007bff; text-decoration: none; }
        .link:hover { text-decoration: underline; }
        .error { background: #f8d7da; border-left-color: #dc3545; color: #721c24; }
        .success { background: #d4edda; border-left-color: #28a745; color: #155724; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 URL Link 测试工具</h1>

        <div class="form-group">
            <label for="orderNumber">订单号:</label>
            <input type="text" id="orderNumber" placeholder="例如: TB202412301234" value="TB202412301234">
        </div>

        <div class="form-group">
            <label for="source">来源:</label>
            <select id="source">
                <option value="test">测试</option>
                <option value="wechat_message">微信消息</option>
                <option value="qr_code">二维码</option>
                <option value="sms">短信</option>
            </select>
        </div>

        <div class="form-group">
            <label for="utm">UTM参数:</label>
            <input type="text" id="utm" placeholder="例如: campaign1" value="test_campaign">
        </div>

        <div class="form-group">
            <label for="autoJump">自动跳转:</label>
            <select id="autoJump">
                <option value="false">否</option>
                <option value="true">是</option>
            </select>
        </div>

        <button onclick="generateUrlLink()">生成 URL Link</button>
        <button onclick="generateRedirectPage()">生成重定向页面</button>
        <button onclick="testDirectJump()">直接测试跳转</button>

        <div id="result"></div>
    </div>

    <script>
        // 生成URL Link
        async function generateUrlLink() {
            const params = getParams();
            const url = '/api/redirect/generate-url-link?' + new URLSearchParams(params).toString();

            try {
                const response = await fetch(url);
                const data = await response.json();

                if (data.errno === 0) {
                    showResult('success', '✅ URL Link 生成成功',
                        '<strong>URL Link:</strong><br>' +
                        '<a href="' + data.data.urlLink + '" class="link" target="_blank">' + data.data.urlLink + '</a><br><br>' +
                        '<strong>小程序路径:</strong> ' + data.data.path + '<br>' +
                        '<strong>查询参数:</strong> ' + data.data.query
                    );
                } else {
                    showResult('error', '❌ 生成失败', data.errmsg);
                }
            } catch (error) {
                showResult('error', '❌ 请求失败', error.message);
            }
        }

        // 生成重定向页面
        function generateRedirectPage() {
            const params = getParams();
            const url = '/api/redirect/order-exchange?' + new URLSearchParams(params).toString();

            showResult('success', '🔗 重定向页面链接',
                '<a href="' + url + '" class="link" target="_blank">' + url + '</a><br><br>' +
                '<small>点击链接在新窗口中打开重定向页面</small>'
            );
        }

        // 直接测试跳转
        function testDirectJump() {
            const params = getParams();
            params.autoJump = 'true';
            const url = '/api/redirect/order-exchange?' + new URLSearchParams(params).toString();

            window.open(url, '_blank');
        }

        // 获取表单参数
        function getParams() {
            return {
                orderNumber: document.getElementById('orderNumber').value,
                source: document.getElementById('source').value,
                utm: document.getElementById('utm').value,
                autoJump: document.getElementById('autoJump').value
            };
        }

        // 显示结果
        function showResult(type, title, content) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result ' + type;
            resultDiv.innerHTML = '<strong>' + title + '</strong><br><br>' + content;
        }
    </script>
</body>
</html>`;

            _this3.header('Content-Type', 'text/html; charset=utf-8');
            _this3.body = html;
        })();
    }
    /**
     * 简单的订单兑换跳转测试页面
     */
    testAction() {
        var _this4 = this;

        return _asyncToGenerator(function* () {
            const html = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单兑换测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 400px;
            width: 100%;
        }
        .logo {
            font-size: 60px;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        p {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        .btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .tips {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🎁</div>
        <h1>订单兑换测试</h1>
        <p>点击下方按钮跳转到小程序订单兑换页面</p>

        <button class="btn" onclick="jumpToMiniProgram()">打开小程序</button>

        <div class="tips">
            <strong>测试说明：</strong><br>
            点击按钮会尝试打开小程序的订单兑换页面
        </div>
    </div>

    <script>
        function jumpToMiniProgram() {
            // 固定的小程序跳转链接
            const scheme = 'weixin://dl/business/?appid=wx919ca2ec612e6ecb&path=pages/order-exchange/index&env_version=release';

            // 直接跳转
            window.location.href = scheme;
        }
    </script>
</body>
</html>`;

            _this4.header('Content-Type', 'text/html; charset=utf-8');
            _this4.body = html;
        })();
    }

    /**
     * 直接跳转到小程序（无中间页面，无参数）
     * GET /api/redirect/direct
     */
    directAction() {
        var _this5 = this;

        return _asyncToGenerator(function* () {
            try {
                console.log('直接跳转到小程序订单有礼页面');

                // 生成URL Link（优先使用）
                let urlLink = null;
                try {
                    const path = 'pages/order-exchange/index';
                    urlLink = yield _this5.generateWechatUrlLink(path);
                    console.log('生成URL Link成功，直接重定向:', urlLink);

                    // 直接重定向到URL Link
                    _this5.redirect(urlLink);
                    return;
                } catch (error) {
                    console.error('生成URL Link失败，使用URL Scheme:', error);
                }

                // 如果URL Link失败，使用URL Scheme
                const appid = think.config('weixin.appid');
                const scheme = `weixin://dl/business/?appid=${appid}&path=pages/order-exchange/index&env_version=develop`;

                console.log('使用URL Scheme重定向:', scheme);
                _this5.redirect(scheme);
            } catch (error) {
                console.error('直接跳转失败:', error);
                return _this5.fail(500, '跳转失败: ' + error.message);
            }
        })();
    }

    /**
     * 订单兑换页面跳转（保留原有功能）
     */
    orderExchangeAction() {
        var _this6 = this;

        return _asyncToGenerator(function* () {
            try {
                // 获取参数
                const orderNumber = _this6.get('orderNumber') || '';
                const source = _this6.get('source') || 'web';
                const utm = _this6.get('utm') || '';
                const userId = _this6.get('userId') || '';
                const autoJump = _this6.get('autoJump') || 'false'; // 是否自动跳转

                // 获取小程序配置
                const appid = think.config('weixin.appid');
                const env = _this6.get('env') || 'release'; // 支持开发环境

                // 构建小程序跳转参数
                const queryParams = [];
                if (orderNumber) queryParams.push(`orderNumber=${encodeURIComponent(orderNumber)}`);
                if (source) queryParams.push(`source=${encodeURIComponent(source)}`);
                if (utm) queryParams.push(`utm=${encodeURIComponent(utm)}`);
                if (userId) queryParams.push(`userId=${encodeURIComponent(userId)}`);

                const query = queryParams.join('&');
                const encodedQuery = encodeURIComponent(query);

                // 生成URL Link（优先使用）
                let urlLink = null;
                try {
                    const path = `pages/order-exchange/index${query ? '?' + query : ''}`;
                    urlLink = yield _this6.generateWechatUrlLink(path);
                    console.log('生成URL Link成功:', urlLink);
                } catch (error) {
                    console.error('生成URL Link失败，使用URL Scheme作为备选:', error);
                }

                // 生成URL Scheme作为备选
                const scheme = `weixin://dl/business/?appid=${appid}&path=pages/order-exchange/index&query=${encodedQuery}&env_version=develop`;

                // 返回HTML页面
                const html = _this6.generateRedirectHTML({
                    title: '订单有礼 - 美汐缘',
                    orderNumber,
                    urlLink: urlLink || scheme, // 优先使用URL Link
                    scheme,
                    appid,
                    query: encodedQuery,
                    autoJump
                });

                _this6.header('Content-Type', 'text/html; charset=utf-8');
                _this6.body = html;
            } catch (error) {
                console.error('生成跳转页面错误:', error);
                _this6.body = _this6.generateErrorHTML();
            }
        })();
    }

    /**
     * 生成跳转HTML页面
     */
    generateRedirectHTML(params) {
        const { title, orderNumber, urlLink, scheme, appid, query, autoJump } = params;

        return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
        }
        .logo {
            font-size: 60px;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 700;
        }
        .subtitle {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        .order-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
            font-size: 14px;
            color: #333;
        }
        .btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            color: white;
            border: none;
            padding: 16px 40px;
            border-radius: 50px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(255, 107, 107, 0.3);
        }
        .tips {
            margin-top: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 16px;
            font-size: 14px;
            color: #666;
            line-height: 1.6;
            text-align: left;
        }
        .loading {
            display: none;
            margin: 20px 0;
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #ff6b6b;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🎁</div>
        <h1>订单有礼</h1>
        <p class="subtitle">点击下方按钮打开小程序<br>开始您的订单兑换之旅</p>
        
        ${orderNumber ? `<div class="order-info">订单号: ${orderNumber}</div>` : ''}
        
        <button class="btn" onclick="openMiniProgram()">立即打开小程序</button>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在跳转到小程序...</p>
        </div>
        
        <div class="tips">
            <strong>💡 使用说明</strong><br>
            • 点击按钮直接跳转到小程序<br>
            • 如无法跳转，请确保在微信中打开<br>
            • 支持输入订单号兑换积分
        </div>
    </div>

    <script>
        // 检测是否在微信中
        function isWechat() {
            return /micromessenger/i.test(navigator.userAgent);
        }

        // 打开小程序
        function openMiniProgram() {
            const loading = document.getElementById('loading');
            loading.style.display = 'block';

            const urlLink = '${urlLink}';
            const scheme = '${scheme}';

            if (isWechat()) {
                // 优先使用URL Link，如果不存在则使用URL Scheme
                const jumpUrl = urlLink && urlLink !== 'undefined' ? urlLink : scheme;
                console.log('跳转链接:', jumpUrl);
                window.location.href = jumpUrl;
            } else {
                // 不在微信中，提示用户
                loading.style.display = 'none';
                alert('请在微信中打开此链接');
            }
        }

        // 页面加载完成后自动尝试跳转
        window.onload = function() {
            console.log('页面加载完成');

            const autoJump = '${autoJump}';

            if (autoJump === 'true' && isWechat()) {
                // 自动跳转（延迟1秒让用户看到页面）
                setTimeout(openMiniProgram, 1000);
            }
        };
    </script>
</body>
</html>`;
    }

    /**
     * 生成错误页面
     */
    generateErrorHTML() {
        return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面错误</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background: #f5f5f5;
        }
        .error-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 400px;
            margin: 0 auto;
        }
        h1 { color: #e74c3c; }
        p { color: #666; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="error-container">
        <h1>😔 页面加载失败</h1>
        <p>抱歉，页面暂时无法访问</p>
        <p>请稍后重试或联系客服</p>
    </div>
</body>
</html>`;
    }
};