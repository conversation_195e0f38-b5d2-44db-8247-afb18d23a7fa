const Base = require('./base.js');

module.exports = class extends Base {
    
    /**
     * 获取用户佣金信息
     * GET /api/commission/info
     */
    async infoAction() {
        try {
            const userId = await this.getLoginUserId();
            
            console.log('=== 获取用户佣金信息 ===');
            console.log('用户ID:', userId);
            
            // 使用统一的佣金计算服务
            const commissionService = this.service('commission');
            const commissionInfo = await commissionService.calculateUserCommission(userId);
            
            // 使用佣金服务获取佣金明细
            const commissionDetails = await commissionService.getCommissionDetails(userId, 20);
            
            return this.success({
                commissionInfo: commissionInfo,
                commissionDetails: commissionDetails,
                message: '获取佣金信息成功'
            });
            
        } catch (error) {
            console.error('获取用户佣金信息失败:', error);
            return this.fail('获取佣金信息失败: ' + error.message);
        }
    }
    
    /**
     * 申请提现
     * POST /api/commission/withdraw
     */
    async withdrawAction() {
        try {
            const userId = await this.getLoginUserId();
            const withdrawAmount = parseFloat(this.post('amount') || 0);
            const withdrawMethod = this.post('method') || 'wechat'; // 提现方式
            
            console.log('=== 用户申请提现 ===');
            console.log('用户ID:', userId, '提现金额:', withdrawAmount, '提现方式:', withdrawMethod);
            
            if (withdrawAmount <= 0) {
                return this.fail('提现金额必须大于0');
            }
            
            // 使用统一的佣金计算服务检查可提现金额
            const commissionService = this.service('commission');
            const commissionInfo = await commissionService.calculateUserCommission(userId);
            
            if (withdrawAmount > commissionInfo.availableCommission) {
                return this.fail(`提现金额超过可提现余额，当前可提现：${commissionInfo.availableCommission}元`);
            }
            
            // 执行提现
            const withdrawResult = await this.processWithdraw(userId, withdrawAmount, withdrawMethod);
            
            if (withdrawResult.success) {
                return this.success({
                    message: '提现申请成功',
                    withdrawId: withdrawResult.withdrawId,
                    amount: withdrawAmount
                });
            } else {
                return this.fail(withdrawResult.message);
            }
            
        } catch (error) {
            console.error('用户提现申请失败:', error);
            return this.fail('提现申请失败: ' + error.message);
        }
    }
    
    /**
     * 处理提现
     */
    async processWithdraw(userId, amount, method) {
        try {
            const currentTime = parseInt(new Date().getTime() / 1000);

            // 1. 再次检查可提现金额（防止并发问题）
            const commissionService = this.service('commission');
            const commissionInfo = await commissionService.calculateUserCommission(userId);

            if (amount > commissionInfo.availableCommission) {
                throw new Error(`提现金额超过可用余额，当前可提现：${commissionInfo.availableCommission}元`);
            }

            // 2. 标记相关佣金为已提现状态
            await this.markCommissionsAsWithdrawn(userId, amount);

            // 3. 更新用户佣金账户（只更新withdrawn_commission，其他字段通过实时计算）
            await this.model('user_commission').where({
                user_id: userId
            }).update({
                withdrawn_commission: ['exp', `withdrawn_commission + ${amount}`],
                updated_at: new Date()
            });

            // 注意：available_commission 通过 calculateUserCommission 方法实时计算
            // 因为已经标记了相关佣金为已提现状态，实时计算会自动减少可用余额

            // 4. 创建提现记录
            const withdrawId = await this.model('withdraw_log').add({
                user_id: userId,
                amount: amount,
                method: method,
                status: 'pending',
                apply_time: currentTime,
                create_time: currentTime
            });

            console.log('✅ 提现处理成功，提现ID:', withdrawId);
            return { success: true, withdrawId: withdrawId };

        } catch (error) {
            console.error('处理提现失败:', error);
            return { success: false, message: error.message };
        }
    }
    
    /**
     * 标记佣金为已提现状态
     */
    async markCommissionsAsWithdrawn(userId, withdrawAmount) {
        const currentTime = parseInt(new Date().getTime() / 1000);
        const tenDaysAgo = currentTime - (10 * 24 * 3600);
        
        // 查询可提现的佣金记录（10天前发放的，按时间顺序）
        const availableCommissions = await this.model('commission_log').where({
            user_id: userId,
            commission_type: 'promotion',
            commission_status: 'settled',
            settle_time: ['<=', tenDaysAgo],
            commission_change: ['>', 0] // 只查询正数（获得的佣金）
        }).order('settle_time ASC').select();
        
        let remainingAmount = withdrawAmount;
        
        // 按时间顺序标记佣金为已提现
        for (const commission of availableCommissions) {
            if (remainingAmount <= 0) break;
            
            const commissionAmount = parseFloat(commission.commission_change);
            
            if (commissionAmount <= remainingAmount) {
                // 整条记录标记为已提现
                await this.model('commission_log').where({
                    id: commission.id
                }).update({
                    commission_status: 'withdrawn'
                });
                
                remainingAmount -= commissionAmount;
            } else {
                // 部分提现（这种情况比较复杂，暂时简化处理）
                await this.model('commission_log').where({
                    id: commission.id
                }).update({
                    commission_status: 'withdrawn'
                });
                
                remainingAmount = 0;
            }
        }
    }
    
    /**
     * 获取提现记录
     * GET /api/commission/withdraws
     */
    async withdrawsAction() {
        try {
            const userId = await this.getLoginUserId();
            const page = parseInt(this.get('page') || 1);
            const pageSize = parseInt(this.get('pageSize') || 20);
            
            console.log('=== 获取用户提现记录 ===');
            console.log('用户ID:', userId, '页码:', page, '每页:', pageSize);
            
            // 查询提现记录
            const withdraws = await this.model('withdraw_log').where({
                user_id: userId
            }).order('create_time DESC').page(page, pageSize).countSelect();
            
            return this.success({
                withdraws: withdraws,
                message: '获取提现记录成功'
            });
            
        } catch (error) {
            console.error('获取提现记录失败:', error);
            return this.fail('获取提现记录失败: ' + error.message);
        }
    }
};
