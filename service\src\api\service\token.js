const jwt = require('jsonwebtoken');
const secret = 'sdfsdfsdf123123!ASDasdasdasdasda';
module.exports = class extends think.Service {
    /**
     * 根据header中的x-hioshop-token值获取用户id
     */
    async getUserId(token) {
        console.log('=== Token验证开始 ===');
        console.log('Token:', token ? token.substring(0, 20) + '...' : 'null');

        if (!token) {
            console.log('Token为空');
            return 0;
        }

        const result = this.parse(token);
        console.log('JWT解析结果:', result);
        console.log('JWT中的所有字段:', Object.keys(result || {}));

        // 检查user_id在哪个字段中
        const userId = (result && result.user_id) || (result && result.id) || 0;
        console.log('提取的userId:', userId);

        if (think.isEmpty(result) || !userId || userId <= 0) {
            console.log('JWT解析失败或user_id无效');
            return 0;
        }

        // 验证数据库中用户是否存在
        try {
            const user = await this.model('user').where({
                id: userId
            }).find();

            if (think.isEmpty(user)) {
                console.log('数据库中用户不存在，user_id:', userId);
                return 0;
            }

            console.log('用户验证成功，user_id:', userId, '昵称:', user.nickname);
            return userId;
        } catch (error) {
            console.log('数据库查询失败:', error);
            return 0;
        }
    }
    parse(token) {
        if (token) {
            try {
                console.log('=== JWT解析开始 ===');
                console.log('Token长度:', token.length);
                console.log('Secret:', secret);
                const result = jwt.verify(token, secret);
                console.log('JWT解析成功:', result);
                return result;
            } catch (err) {
                console.log('=== JWT解析失败 ===');
                console.log('错误信息:', err.message);
                console.log('Token:', token.substring(0, 50) + '...');
                return null;
            }
        }
        console.log('Token为空，无法解析');
        return null;
    }
	async create(userInfo) {
	    const token = jwt.sign(userInfo, secret);
	    return token;
	}
	/**
	 * 根据值获取用户信息
	 */
	async getUserInfo() {
	    const userId = await this.getUserId();
	    if (userId <= 0) {
	        return null;
	    }
	    const userInfo = await this.model('user').field(['id', 'username', 'nickname', 'gender', 'avatar', 'birthday']).where({
	        id: userId
	    }).find();
	    return think.isEmpty(userInfo) ? null : userInfo;
	}
    async verify() {
        const result = await this.parse();
        if (think.isEmpty(result)) {
            return false;
        }
        return true;
    }
};