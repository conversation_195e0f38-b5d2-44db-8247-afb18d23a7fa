-- 轮次秒杀系统：每5分钟一轮，间隔2分钟，可选择不同商品
-- 删除旧表
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS `hiolabs_flash_sale_orders`;
DROP TABLE IF EXISTS `hiolabs_flash_sale_user_records`;
DROP TABLE IF EXISTS `hiolabs_flash_sale_rounds`;
DROP TABLE IF EXISTS `hiolabs_flash_sale_campaigns`;
SET FOREIGN_KEY_CHECKS = 1;

-- 1. 秒杀轮次表（主表）
CREATE TABLE IF NOT EXISTS `hiolabs_flash_sale_rounds` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '轮次ID',
  `round_number` int(11) NOT NULL COMMENT '轮次编号（全局递增）',
  `goods_id` int(11) NOT NULL COMMENT '参与秒杀的商品ID',
  `goods_name` varchar(200) NOT NULL COMMENT '商品名称（冗余字段）',
  `goods_image` varchar(500) DEFAULT NULL COMMENT '商品图片（冗余字段）',
  `original_price` decimal(10,2) NOT NULL COMMENT '商品原价',
  `flash_price` decimal(10,2) NOT NULL COMMENT '秒杀价格',
  `stock` int(11) NOT NULL COMMENT '本轮库存数量',
  `sold_count` int(11) DEFAULT 0 COMMENT '已售数量',
  `limit_quantity` int(11) DEFAULT 1 COMMENT '每人限购数量',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `status` enum('upcoming','active','ended','cancelled') DEFAULT 'upcoming' COMMENT '状态',
  `created_by` int(11) DEFAULT NULL COMMENT '创建人（管理员ID）',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_round_number` (`round_number`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_status_time` (`status`, `start_time`),
  KEY `idx_time_range` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀轮次表';

-- 2. 秒杀订单记录表
CREATE TABLE IF NOT EXISTS `hiolabs_flash_sale_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `round_id` int(11) NOT NULL COMMENT '轮次ID',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `quantity` int(11) NOT NULL COMMENT '购买数量',
  `flash_price` decimal(10,2) NOT NULL COMMENT '秒杀价格',
  `total_amount` decimal(10,2) NOT NULL COMMENT '总金额',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_round_id` (`round_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀订单记录表';

-- 3. 用户参与记录表（防止同一轮次重复参与）
CREATE TABLE IF NOT EXISTS `hiolabs_flash_sale_user_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `round_id` int(11) NOT NULL COMMENT '轮次ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `purchased_quantity` int(11) DEFAULT 0 COMMENT '已购买数量',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_round_user` (`round_id`, `user_id`),
  KEY `idx_round_id` (`round_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户参与记录表';

-- 4. 系统配置表（存储轮次规则）
CREATE TABLE IF NOT EXISTS `hiolabs_flash_sale_config` (
  `id` int(1) NOT NULL DEFAULT 1 COMMENT '配置ID（固定为1）',
  `round_duration` int(11) NOT NULL DEFAULT 300 COMMENT '每轮持续时间（秒，默认5分钟）',
  `break_duration` int(11) NOT NULL DEFAULT 120 COMMENT '轮次间隔时间（秒，默认2分钟）',
  `auto_start_next` tinyint(1) DEFAULT 1 COMMENT '是否自动开始下一轮',
  `daily_start_time` time NOT NULL DEFAULT '09:00:00' COMMENT '每日开始时间',
  `daily_end_time` time NOT NULL DEFAULT '22:00:00' COMMENT '每日结束时间',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用秒杀系统',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀系统配置表';

-- 插入默认配置
INSERT INTO `hiolabs_flash_sale_config` (
  `round_duration`, `break_duration`, `auto_start_next`, 
  `daily_start_time`, `daily_end_time`, `is_enabled`
) VALUES (300, 120, 1, '09:00:00', '22:00:00', 1);

-- 插入示例轮次数据
INSERT INTO `hiolabs_flash_sale_rounds` (
  `round_number`, `goods_id`, `goods_name`, `goods_image`, `original_price`, `flash_price`,
  `stock`, `limit_quantity`, `start_time`, `end_time`, `status`
) VALUES 
(1, 1, 'iPhone 15 Pro Max 256GB', '/images/iphone15pro.jpg', 8999.00, 6999.00, 10, 1, 
 DATE_ADD(NOW(), INTERVAL 1 MINUTE), DATE_ADD(NOW(), INTERVAL 6 MINUTE), 'upcoming'),
(2, 2, '小米14 Ultra 512GB', '/images/mi14ultra.jpg', 5999.00, 4999.00, 15, 2, 
 DATE_ADD(NOW(), INTERVAL 8 MINUTE), DATE_ADD(NOW(), INTERVAL 13 MINUTE), 'upcoming'),
(3, 3, 'MacBook Pro 14寸 M3', '/images/macbookpro.jpg', 15999.00, 13999.00, 5, 1, 
 DATE_ADD(NOW(), INTERVAL 15 MINUTE), DATE_ADD(NOW(), INTERVAL 20 MINUTE), 'upcoming');

SELECT '✅ 轮次秒杀系统表结构创建完成！' as result;
