/* 订单兑换页面样式 - 完全按照HTML代码实现 */
.page-container {
  background: #f9fafb; /* bg-gray-50 */
  width: 750rpx; /* w-[375px] * 2 */
  height: 1524rpx; /* h-[762px] * 2 */
  overflow-y: auto;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}



/* 主要内容区 - 完全按照main样式 */
.main-content {
  padding-top: 40rpx; /* pt-5 */
  padding-bottom: 160rpx; /* pb-20 为底部导航留空间 */
  padding-left: 40rpx; /* px-5 */
  padding-right: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 200rpx); /* 调整最小高度 */
}

.content-wrapper {
  width: 100%;
  max-width: 600rpx; /* max-w-[300px] = 300px = 600rpx */
  margin-bottom: 48rpx; /* mb-6 */
}

.text-center {
  text-align: center;
  margin-bottom: 80rpx; /* mb-10 */
}

/* 礼品图标 - 完全按照div样式 */
.gift-icon-container {
  width: 256rpx; /* w-32 */
  height: 256rpx; /* h-32 */
  margin: 0 auto 32rpx; /* mx-auto mb-4 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.gift-image {
  width: 100%;
  height: 100%;
  object-fit: contain; /* object-contain */
}

/* 标题 - 完全按照h2样式 */
.main-title {
  font-size: 40rpx; /* text-xl = 20px = 40rpx */
  font-weight: 500; /* font-medium */
  color: #1f2937; /* text-gray-800 */
  margin-bottom: 16rpx; /* mb-2 = 8px = 16rpx */
}

/* 副标题 - 完全按照p样式 */
.subtitle {
  color: #6b7280; /* text-gray-500 */
  font-size: 28rpx; /* text-sm = 14px = 28rpx */
}









/* 兑换区域 */
.exchange-section {
  width: 100%;
}

/* 输入框容器 - 完全按照div样式 */
.input-wrapper {
  position: relative;
  margin-bottom: 48rpx; /* mb-6 */
}

/* 输入框 - 完全按照HTML代码的input样式 */
.order-input {
  width: 100%; /* w-full */
  padding: 28rpx 32rpx; /* py-3.5 px-4 = 14px 16px = 28rpx 32rpx */
  border: 2rpx solid #e5e7eb; /* border border-gray-200 */
  border-radius: 24rpx; /* rounded-lg = 12px = 24rpx */
  font-size: 32rpx;
  color: #1f2937; /* text-gray-800 */
  background: white; /* bg-white */
  box-sizing: border-box;
  transition: all 0.3s ease; /* transition-all */
  height: 92rpx; /* 精确高度：py-3.5(28px) + border(2px) + line-height(16px) = 46px = 92rpx */
  line-height: 32rpx; /* 与字体大小一致 */
}

.order-input:focus {
  outline: none; /* focus:outline-none */
  box-shadow: 0 0 0 4rpx rgba(79, 70, 229, 0.2); /* focus:ring-2 focus:ring-primary/20 */
  border-color: #4F46E5; /* focus:border-primary */
  transform: translateY(-4rpx); /* input-field:focus transform translateY(-2px) */
}

/* 清除按钮 - 完全按照button样式 */
.clear-btn {
  position: absolute;
  right: 24rpx; /* right-3 */
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af; /* text-gray-400 */
  opacity: 0;
  transition: opacity 0.3s ease;
}

.clear-btn.show {
  opacity: 1;
}

.clear-icon-wrapper {
  width: 40rpx; /* w-5 */
  height: 40rpx; /* h-5 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-icon {
  font-size: 32rpx; /* ri-lg */
}

/* 兑换按钮 - 完全按照HTML代码的button样式 */
.exchange-btn {
  width: 100%; /* w-full */
  padding: 28rpx 0; /* py-3.5 = 14px = 28rpx */
  border-radius: 16rpx; /* rounded-button = 8px = 16rpx */
  color: white; /* text-white */
  font-size: 32rpx; /* 保持合适的字体大小 */
  font-weight: 500; /* font-medium */
  transition: all 0.3s ease; /* transition-all */
  background: linear-gradient(to right, #4F46E5, #60A5FA) !important; /* bg-gradient-to-r from-primary to-secondary */
  border: none;
  box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.1); /* hover:shadow-lg */
  height: auto; /* 让高度由padding决定 */
  line-height: 1; /* 确保文字垂直居中 */
}

.exchange-btn[disabled] {
  background: #d1d5db !important;
  color: #9ca3af !important;
  box-shadow: none !important;
}

.exchange-btn:active {
  transform: scale(0.98); /* exchange-btn:active */
}

/* 兑换记录区域 */
.records-section {
  margin-top: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24rpx;
  padding-left: 4rpx;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}

.record-item:last-child {
  border-bottom: none;
}

.record-left {
  flex: 1;
}

.order-number {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.record-date {
  font-size: 24rpx;
  color: #9ca3af;
}

.record-right {
  text-align: right;
}

.points-earned {
  font-size: 28rpx;
  color: #059669;
  font-weight: 600;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #9ca3af;
}

/* 授权弹窗样式 */
.auth-modal {
  width: 640rpx;
  max-height: 80vh;
  background: white;
  border-radius: 24rpx;
  position: relative;
  transform: scale(0.8);
  opacity: 0;
  transition: all 0.3s ease;
}

.auth-modal.show {
  transform: scale(1);
  opacity: 1;
}

.auth-step {
  padding: 60rpx 40rpx 40rpx;
}

.step-content {
  text-align: center;
}

.step-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.step-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.step-desc {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.auth-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}

.auth-btn::after {
  border: none;
}

.phone-btn {
  background: #07c160;
  color: white;
}

.phone-btn:active {
  background: #06ad56;
}

.complete-btn {
  background: #4f46e5;
  color: white;
}

.complete-btn:active {
  background: #4338ca;
}

.complete-btn:disabled {
  background: #e5e7eb;
  color: #9ca3af;
}

.step-tip {
  font-size: 24rpx;
  color: #9ca3af;
  margin-top: 16rpx;
}

/* 弹窗中的头像区域 */
.avatar-section {
  margin-bottom: 32rpx;
}

.avatar-btn {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  margin: 0 auto;
  background: none;
  border: none;
  padding: 0;
}

.avatar-btn::after {
  border: none;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8rpx;
  text-align: center;
  font-size: 20rpx;
}

/* 弹窗中的昵称区域 */
.nickname-section {
  margin-bottom: 32rpx;
}

.nickname-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  text-align: center;
}

.nickname-input:focus {
  border-color: #4f46e5;
}

/* 模态窗遮罩 - 完全按照HTML代码 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5); /* bg-black/50 */
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, transform 0.3s ease; /* modal transition */
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* 兑换记录模态窗 - 完全按照HTML代码 */
.history-modal {
  background: white; /* bg-white */
  width: 640rpx; /* w-[320px] * 2 */
  border-radius: 24rpx; /* rounded-xl */
  overflow: hidden;
  opacity: 0;
  transform: translateY(40rpx); /* modal-enter */
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.history-modal.show {
  opacity: 1;
  transform: translateY(0); /* modal-enter-active */
}

/* 模态窗头部 - 完全按照HTML代码 */
.modal-header {
  padding: 32rpx 40rpx; /* px-5 py-4 */
  border-bottom: 2rpx solid #f3f4f6; /* border-b border-gray-100 */
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-weight: 500; /* font-medium */
  color: #1f2937; /* text-gray-800 */
  font-size: 32rpx;
}

.modal-close {
  width: 48rpx; /* w-6 */
  height: 48rpx; /* h-6 */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.close-icon {
  font-size: 32rpx; /* ri-lg */
  color: #6b7280; /* text-gray-500 */
}

/* 记录容器 - 完全按照HTML代码 */
.records-container {
  max-height: 800rpx; /* max-h-[400px] * 2 */
  overflow-y: auto;
  padding: 16rpx 0; /* py-2 */
}

.record-item {
  padding: 24rpx 40rpx; /* px-5 py-3 */
  border-bottom: 2rpx solid #f3f4f6; /* border-b border-gray-100 */
}

.record-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx; /* mb-2 */
}

.record-title {
  font-size: 28rpx; /* text-sm */
  font-weight: 500; /* font-medium */
  color: #1f2937; /* text-gray-800 */
}

.record-date {
  font-size: 24rpx; /* text-xs */
  color: #6b7280; /* text-gray-500 */
}

.record-order {
  font-size: 24rpx; /* text-xs */
  color: #6b7280; /* text-gray-500 */
}

.record-status {
  margin-top: 12rpx; /* mt-1.5 */
  display: flex;
  align-items: center;
}

.status-tag {
  padding: 4rpx 16rpx; /* px-2 py-0.5 */
  background: #dcfce7; /* bg-green-50 */
  color: #16a34a; /* text-green-600 */
  font-size: 24rpx; /* text-xs */
  border-radius: 50rpx; /* rounded-full */
}

.no-records {
  padding: 80rpx 40rpx;
  text-align: center;
  color: #6b7280;
  font-size: 28rpx;
}

/* 模态窗底部 - 完全按照HTML代码 */
.modal-footer {
  padding: 24rpx 40rpx; /* px-5 py-3 */
  display: flex;
  justify-content: flex-end; /* justify-end */
  border-top: 2rpx solid #f3f4f6; /* border-t border-gray-100 */
}

.close-btn {
  padding: 16rpx 32rpx; /* px-4 py-2 */
  font-size: 28rpx; /* text-sm */
  color: #4F46E5; /* text-primary */
  background: rgba(79, 70, 229, 0.1); /* bg-primary/10 */
  border-radius: 16rpx; /* rounded-button = 8px * 2 = 16rpx */
  cursor: pointer;
  border: none;
}

/* 兑换成功模态窗 - 完全按照HTML代码 */
.success-modal {
  background: white; /* bg-white */
  width: 600rpx; /* w-[300px] * 2 */
  border-radius: 24rpx; /* rounded-xl */
  overflow: hidden;
  text-align: center;
  padding: 48rpx 40rpx; /* py-6 px-5 */
  opacity: 0;
  transform: translateY(40rpx); /* modal-enter */
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.success-modal.show {
  opacity: 1;
  transform: translateY(0); /* modal-enter-active */
}

/* 成功图标容器 - 完全按照HTML代码 */
.success-icon-container {
  width: 128rpx; /* w-16 */
  height: 128rpx; /* h-16 */
  margin: 0 auto 32rpx; /* mx-auto mb-4 */
  display: flex;
  align-items: center;
  justify-content: center;
  background: #dcfce7; /* bg-green-50 */
  border-radius: 50%; /* rounded-full */
}

.success-check {
  font-size: 64rpx; /* ri-2x */
  color: #16a34a; /* text-green-500 */
}

.success-title {
  font-size: 36rpx; /* text-lg */
  font-weight: 500; /* font-medium */
  color: #1f2937; /* text-gray-800 */
  margin-bottom: 16rpx; /* mb-2 */
}

.success-desc {
  font-size: 28rpx; /* text-sm */
  color: #6b7280; /* text-gray-500 */
  margin-bottom: 24rpx; /* mb-3 */
}

.success-points {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  padding: 16rpx 32rpx;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 16rpx;
  border: 2rpx solid rgba(16, 185, 129, 0.2);
}

.points-label {
  font-size: 28rpx;
  color: #6b7280;
  margin-right: 8rpx;
}

.points-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #10b981;
}

.success-btn {
  width: 100%; /* w-full */
  padding: 24rpx 0; /* py-3 */
  border-radius: 16rpx; /* rounded-button = 8px * 2 = 16rpx */
  color: white; /* text-white */
  font-weight: 500; /* font-medium */
  transition: all 0.3s ease; /* transition-all */
  background: linear-gradient(to right, #4F46E5, #60A5FA); /* bg-gradient-to-r from-primary to-secondary */
  border: none;
  box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.1); /* hover:shadow-lg */
}







