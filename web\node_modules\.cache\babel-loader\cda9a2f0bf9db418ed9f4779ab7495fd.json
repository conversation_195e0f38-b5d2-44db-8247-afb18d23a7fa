{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js!D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSalePage.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSalePage.vue", "mtime": 1753723289742}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\babel.config.js", "mtime": 1717470108000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8kBA;EACAA;EACAC;IACA;MACA;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;QACAd;QACAe;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IAAA;IACA;IACA;IACA;MACA;IACA;EACA;EAEAC;IACA;MACAC;IACA;EACA;EAEAC;IACAC;MAAA;MACA;;MAEA;MACA;QACAC;UAAA;QAAA;MACA;MAEA;QACAA;UAAA,OACAC;QAAA,EACA;MACA;MAEA;QACAD;UAAA;QAAA;MACA;MAEA;QACAA;UAAA;QAAA;MACA;MAEA;IACA;EACA;AAAA,wEAEA;EACA;AACA,sDAEA;EACA;EACAE;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;YAAA,OACAC,aACA,yBACA,qBACA,uBACA;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EACA;EAEA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YACA;YAAA;YAAA;YAAA,OAEA;UAAA;YAAAC;YACA;cACA;YACA;cACAC;YACA;YAAA;YAAA;UAAA;YAAA;YAAA;YAEAA;YACA;UAAA;YAAA;YAEA;YAAA;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAEA;EAEA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;YAAA;YAAA,OAEA;UAAA;YAAAF;YACA;cACA;cACA;;cAEA;cACA;gBACA;gBACA;gBACAG;cACA;YACA;cACAF;YACA;YAAA;YAAA;UAAA;YAAA;YAAA;YAEAA;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAEA;EAEA;EACAG;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YACA;YAAA;YAEAC;cACAC;cACAC;cACAC;cACAC;YACA;YAAA;YAAA,OAEA;cAAAJ;YAAA;UAAA;YAAAL;YACA;cACA;YACA;cACAC;YACA;YAAA;YAAA;UAAA;YAAA;YAAA;YAEAA;YACA;UAAA;YAAA;YAEA;YAAA;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAEA;EAEA;EACAS;IACA;MACAC;MACAC;MACAC;MACAC;IACA;IACA;EACA;EAEA;EACAC;IAAA;IACA;IACA;EACA;EAEA;EACAC;IACA;IACA;IACA;EACA;EAEA;EACAC;IACA;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEA;EACAC;IACA;IAEA;IACA;IAEA;MACA;IACA;MACA;IACA;EACA;EAEA;EACAC;IACAtB;IACA;EACA;EAEA;EACAuB;IACAvB;IACA;EACA;EAEA;EACAwB;IACAxB;IACA;EACA;EAEA;EACAyB;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;YAAA;YAAA,OAEA;UAAA;YAAA1B;YACA;cACA;cACA;cACA;cACA;cACA;gBACA2B;gBACAC;gBACAxD;gBACAyD;gBACArD;gBACAsD;gBACAC;cACA;YACA;cACA;YACA;YAAA;YAAA;UAAA;YAAA;YAAA;YAEA9B;YACA;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAEA;EAEA;EACA+B;IACA;IACA;EACA;AACA", "names": ["name", "data", "viewMode", "searchQuery", "filterStatus", "filterCategory", "showAddModal", "showTimeSlotModal", "statistics", "totalCampaigns", "activeCampaigns", "todayRounds", "flashSaleOrders", "flashSaleSales", "newFlashSale", "goodsId", "flashPrice", "originalPrice", "totalStock", "stockPerRound", "limitQuantity", "startDate", "endDate", "dailyStartTime", "dailyEndTime", "roundDuration", "breakDuration", "autoStart", "currentRounds", "upcomingRounds", "campaigns", "loading", "statisticsLoading", "timeSlotsLoading", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "computed", "filteredProducts", "filtered", "product", "loadData", "Promise", "loadStatistics", "response", "console", "loadRounds", "round", "loadCampaigns", "params", "search", "status", "page", "limit", "getStatusLabel", "active", "upcoming", "ended", "disabled", "selectTimeSlot", "resetFilters", "formatTime", "month", "day", "hour", "minute", "formatCountdown", "viewProduct", "editProduct", "toggleProductStatus", "createFlashSale", "productId", "timeSlotId", "stock", "startTime", "endTime", "refreshData"], "sourceRoot": "src/components/Marketing", "sources": ["FlashSalePage.vue"], "sourcesContent": ["<template>\n  <div class=\"flash-sale-page\">\n    <!-- 页面头部 -->\n    <div class=\"page-header bg-white shadow-sm border-b\">\n      <div class=\"px-6 py-4\">\n        <div class=\"flex items-center justify-between\">\n          <div>\n            <h1 class=\"text-2xl font-semibold text-gray-900\">轮次秒杀</h1>\n            <p class=\"text-sm text-gray-500 mt-1\">每5分钟一轮，间隔2分钟，可选择不同商品参与秒杀</p>\n          </div>\n          <div class=\"flex items-center space-x-3\">\n            <button @click=\"showAddModal = true\" class=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n              <i class=\"ri-add-line mr-2\"></i>\n              创建轮次\n            </button>\n            <button @click=\"showConfigModal = true\" class=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\">\n              <i class=\"ri-settings-line mr-2\"></i>\n              系统配置\n            </button>\n            <button @click=\"refreshData\" class=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n              <i class=\"ri-refresh-line mr-2\"></i>\n              刷新数据\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"p-6 max-w-7xl mx-auto\">\n      <!-- 统计卡片 -->\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center\">\n              <i class=\"ri-flashlight-line text-xl text-red-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm text-gray-500\">秒杀活动总数</p>\n              <p class=\"text-2xl font-bold text-gray-900\">{{ statistics.totalFlashSales }}</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <i class=\"ri-play-circle-line text-xl text-green-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm text-gray-500\">进行中</p>\n              <p class=\"text-2xl font-bold text-gray-900\">{{ statistics.activeFlashSales }}</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\n              <i class=\"ri-shopping-cart-line text-xl text-yellow-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm text-gray-500\">秒杀订单</p>\n              <p class=\"text-2xl font-bold text-gray-900\">{{ statistics.flashSaleOrders }}</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <i class=\"ri-money-dollar-circle-line text-xl text-purple-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm text-gray-500\">秒杀销售额</p>\n              <p class=\"text-2xl font-bold text-gray-900\">¥{{ statistics.flashSaleSales }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 实时轮次状态 -->\n      <div class=\"bg-white rounded-lg shadow-sm border mb-6\">\n        <div class=\"p-6\">\n          <div class=\"flex items-center justify-between mb-4\">\n            <h3 class=\"text-lg font-semibold text-gray-900\">实时秒杀轮次</h3>\n            <div class=\"flex items-center space-x-2\">\n              <span class=\"text-sm text-gray-500\">自动刷新</span>\n              <div class=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n            </div>\n          </div>\n\n          <!-- 当前进行中的轮次 -->\n          <div v-if=\"currentRounds.length > 0\" class=\"mb-6\">\n            <h4 class=\"text-md font-medium text-gray-800 mb-3 flex items-center\">\n              <span class=\"w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse\"></span>\n              正在进行 ({{ currentRounds.length }}场)\n            </h4>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              <div\n                v-for=\"round in currentRounds\"\n                :key=\"round.id\"\n                class=\"p-4 border-2 border-red-200 bg-red-50 rounded-lg\"\n              >\n                <div class=\"flex items-center justify-between mb-2\">\n                  <span class=\"text-sm font-medium text-red-700\">第{{ round.round_number }}轮</span>\n                  <span class=\"px-2 py-1 bg-red-500 text-white text-xs rounded-full\">进行中</span>\n                </div>\n                <div class=\"text-lg font-semibold text-gray-900 mb-1\">{{ round.campaign.name }}</div>\n                <div class=\"text-sm text-gray-600 mb-2\">\n                  ¥{{ round.campaign.flash_price }}\n                  <span class=\"line-through text-gray-400 ml-1\">¥{{ round.campaign.original_price }}</span>\n                </div>\n                <div class=\"flex justify-between text-xs text-gray-500\">\n                  <span>库存: {{ round.stock - round.sold_count }}/{{ round.stock }}</span>\n                  <span>{{ formatTime(round.end_time) }} 结束</span>\n                </div>\n                <div class=\"mt-2 bg-gray-200 rounded-full h-2\">\n                  <div\n                    class=\"bg-red-500 h-2 rounded-full transition-all duration-300\"\n                    :style=\"{ width: (round.sold_count / round.stock * 100) + '%' }\"\n                  ></div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 即将开始的轮次 -->\n          <div v-if=\"upcomingRounds.length > 0\">\n            <h4 class=\"text-md font-medium text-gray-800 mb-3 flex items-center\">\n              <span class=\"w-2 h-2 bg-orange-500 rounded-full mr-2\"></span>\n              即将开始 ({{ upcomingRounds.length }}场)\n            </h4>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              <div\n                v-for=\"round in upcomingRounds\"\n                :key=\"round.id\"\n                class=\"p-4 border border-orange-200 bg-orange-50 rounded-lg\"\n              >\n                <div class=\"flex items-center justify-between mb-2\">\n                  <span class=\"text-sm font-medium text-orange-700\">第{{ round.round_number }}轮</span>\n                  <span class=\"px-2 py-1 bg-orange-500 text-white text-xs rounded-full\">\n                    {{ formatCountdown(round.countdown) }}\n                  </span>\n                </div>\n                <div class=\"text-md font-semibold text-gray-900 mb-1\">{{ round.campaign.name }}</div>\n                <div class=\"text-sm text-gray-600 mb-2\">\n                  ¥{{ round.campaign.flash_price }}\n                </div>\n                <div class=\"text-xs text-gray-500\">\n                  {{ formatTime(round.start_time) }} 开始\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 无轮次时的提示 -->\n          <div v-if=\"currentRounds.length === 0 && upcomingRounds.length === 0\" class=\"text-center py-8\">\n            <i class=\"ri-time-line text-4xl text-gray-300 mb-2\"></i>\n            <p class=\"text-gray-500\">暂无进行中或即将开始的秒杀轮次</p>\n            <p class=\"text-sm text-gray-400 mt-1\">请创建秒杀活动或等待下一轮开始</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- 筛选和搜索 -->\n      <div class=\"bg-white rounded-lg shadow-sm border mb-6\">\n        <div class=\"p-6\">\n          <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">搜索商品</label>\n              <input\n                v-model=\"searchQuery\"\n                type=\"text\"\n                placeholder=\"输入商品名称\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">活动状态</label>\n              <select\n                v-model=\"filterStatus\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"\">全部状态</option>\n                <option value=\"upcoming\">即将开始</option>\n                <option value=\"active\">进行中</option>\n                <option value=\"ended\">已结束</option>\n                <option value=\"disabled\">已停用</option>\n              </select>\n            </div>\n\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">商品分类</label>\n              <select\n                v-model=\"filterCategory\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"\">全部分类</option>\n                <option value=\"electronics\">数码电器</option>\n                <option value=\"clothing\">服装鞋帽</option>\n                <option value=\"home\">家居用品</option>\n                <option value=\"beauty\">美妆护肤</option>\n              </select>\n            </div>\n\n            <div class=\"flex items-end\">\n              <button @click=\"resetFilters\" class=\"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors\">\n                <i class=\"ri-refresh-line mr-2\"></i>\n                重置\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 秒杀商品列表 -->\n      <div class=\"bg-white rounded-lg shadow-sm border\">\n        <div class=\"px-6 py-4 border-b\">\n          <div class=\"flex items-center justify-between\">\n            <h2 class=\"text-lg font-semibold text-gray-900\">\n              秒杀商品列表\n              <span v-if=\"selectedTimeSlot\" class=\"text-sm font-normal text-gray-500 ml-2\">\n                ({{ selectedTimeSlot.name }} {{ selectedTimeSlot.time }})\n              </span>\n            </h2>\n            <div class=\"flex items-center space-x-2\">\n              <button\n                @click=\"viewMode = 'grid'\"\n                :class=\"[\n                  'px-3 py-1 text-sm rounded-md transition-colors',\n                  viewMode === 'grid' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n                ]\"\n              >\n                <i class=\"ri-grid-line mr-1\"></i>\n                网格视图\n              </button>\n              <button\n                @click=\"viewMode = 'list'\"\n                :class=\"[\n                  'px-3 py-1 text-sm rounded-md transition-colors',\n                  viewMode === 'list' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n                ]\"\n              >\n                <i class=\"ri-list-check mr-1\"></i>\n                列表视图\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <!-- 网格视图 -->\n        <div v-if=\"viewMode === 'grid'\" class=\"p-6\">\n          <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            <div v-for=\"product in filteredProducts\" :key=\"product.id\" class=\"bg-white border rounded-lg overflow-hidden hover:shadow-lg transition-shadow\">\n              <!-- 商品图片 -->\n              <div class=\"relative\">\n                <img :src=\"product.image\" :alt=\"product.name\" class=\"w-full h-48 object-cover\">\n                <div class=\"absolute top-2 left-2\">\n                  <span :class=\"[\n                    'px-2 py-1 text-xs rounded-full',\n                    product.status === 'active' ? 'bg-red-100 text-red-800' :\n                    product.status === 'upcoming' ? 'bg-yellow-100 text-yellow-800' :\n                    product.status === 'ended' ? 'bg-gray-100 text-gray-800' :\n                    'bg-red-100 text-red-800'\n                  ]\">\n                    {{ getStatusLabel(product.status) }}\n                  </span>\n                </div>\n                <div class=\"absolute top-2 right-2\">\n                  <span class=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">\n                    {{ product.category }}\n                  </span>\n                </div>\n                <div v-if=\"product.status === 'active'\" class=\"absolute bottom-2 left-2\">\n                  <div class=\"bg-red-600 text-white px-2 py-1 rounded text-xs\">\n                    <i class=\"ri-time-line mr-1\"></i>\n                    {{ product.remainingTime }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- 商品信息 -->\n              <div class=\"p-4\">\n                <h3 class=\"font-medium text-gray-900 mb-2 line-clamp-2\">{{ product.name }}</h3>\n                <div class=\"space-y-2 text-sm\">\n                  <div class=\"flex justify-between items-center\">\n                    <span class=\"text-gray-500\">原价:</span>\n                    <span class=\"text-gray-400 line-through\">¥{{ product.originalPrice }}</span>\n                  </div>\n                  <div class=\"flex justify-between items-center\">\n                    <span class=\"text-gray-500\">秒杀价:</span>\n                    <span class=\"font-medium text-red-600 text-lg\">¥{{ product.flashPrice }}</span>\n                  </div>\n                  <div class=\"flex justify-between items-center\">\n                    <span class=\"text-gray-500\">库存:</span>\n                    <span class=\"font-medium\">{{ product.stock }}</span>\n                  </div>\n                  <div class=\"flex justify-between items-center\">\n                    <span class=\"text-gray-500\">已售:</span>\n                    <span class=\"font-medium text-green-600\">{{ product.soldCount }}</span>\n                  </div>\n                </div>\n\n                <!-- 进度条 -->\n                <div class=\"mt-3\">\n                  <div class=\"flex justify-between text-xs text-gray-500 mb-1\">\n                    <span>销售进度</span>\n                    <span>{{ Math.round((product.soldCount / (product.soldCount + product.stock)) * 100) }}%</span>\n                  </div>\n                  <div class=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div\n                      class=\"bg-red-600 h-2 rounded-full transition-all duration-300\"\n                      :style=\"{ width: Math.round((product.soldCount / (product.soldCount + product.stock)) * 100) + '%' }\"\n                    ></div>\n                  </div>\n                </div>\n\n                <!-- 操作按钮 -->\n                <div class=\"mt-4 space-y-2\">\n                  <div class=\"flex space-x-2\">\n                    <button @click=\"viewProduct(product)\" class=\"flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700\">\n                      详情\n                    </button>\n                    <button @click=\"editProduct(product)\" class=\"flex-1 px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700\">\n                      编辑\n                    </button>\n                  </div>\n                  <button @click=\"toggleProductStatus(product)\" :class=\"[\n                    'w-full px-3 py-2 text-sm rounded transition-colors',\n                    product.status === 'active'\n                      ? 'bg-red-600 text-white hover:bg-red-700'\n                      : 'bg-green-600 text-white hover:bg-green-700'\n                  ]\">\n                    {{ product.status === 'active' ? '停止秒杀' : '开始秒杀' }}\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 列表视图 -->\n        <div v-else class=\"overflow-x-auto\">\n          <table class=\"w-full\">\n            <thead class=\"bg-gray-50\">\n              <tr>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">商品信息</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">分类</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">原价</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">秒杀价</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">库存</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">已售</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">状态</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">操作</th>\n              </tr>\n            </thead>\n            <tbody class=\"bg-white divide-y divide-gray-200\">\n              <tr v-for=\"product in filteredProducts\" :key=\"product.id\" class=\"hover:bg-gray-50\">\n                <!-- 商品信息 -->\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"flex items-center\">\n                    <img :src=\"product.image\" :alt=\"product.name\" class=\"w-12 h-12 rounded-lg object-cover\">\n                    <div class=\"ml-3\">\n                      <div class=\"text-sm font-medium text-gray-900 max-w-xs truncate\">{{ product.name }}</div>\n                      <div class=\"text-sm text-gray-500\">ID: {{ product.id }}</div>\n                    </div>\n                  </div>\n                </td>\n\n                <!-- 分类 -->\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <span class=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">\n                    {{ product.category }}\n                  </span>\n                </td>\n\n                <!-- 原价 -->\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-400 line-through\">\n                  ¥{{ product.originalPrice }}\n                </td>\n\n                <!-- 秒杀价 -->\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600\">\n                  ¥{{ product.flashPrice }}\n                </td>\n\n                <!-- 库存 -->\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                  {{ product.stock }}\n                </td>\n\n                <!-- 已售 -->\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600\">\n                  {{ product.soldCount }}\n                </td>\n\n                <!-- 状态 -->\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <span :class=\"[\n                    'px-2 py-1 text-xs rounded-full',\n                    product.status === 'active' ? 'bg-red-100 text-red-800' :\n                    product.status === 'upcoming' ? 'bg-yellow-100 text-yellow-800' :\n                    product.status === 'ended' ? 'bg-gray-100 text-gray-800' :\n                    'bg-red-100 text-red-800'\n                  ]\">\n                    {{ getStatusLabel(product.status) }}\n                  </span>\n                </td>\n\n                <!-- 操作 -->\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                  <button @click=\"viewProduct(product)\" class=\"text-blue-600 hover:text-blue-900\">详情</button>\n                  <button @click=\"editProduct(product)\" class=\"text-green-600 hover:text-green-900\">编辑</button>\n                  <button @click=\"toggleProductStatus(product)\" :class=\"[\n                    'transition-colors',\n                    product.status === 'active' ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'\n                  ]\">\n                    {{ product.status === 'active' ? '停止' : '开始' }}\n                  </button>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n\n        <!-- 分页 -->\n        <div class=\"px-6 py-4 border-t bg-gray-50\">\n          <div class=\"flex items-center justify-between\">\n            <div class=\"text-sm text-gray-500\">\n              显示 {{ Math.min(filteredProducts.length, 20) }} 条，共 {{ filteredProducts.length }} 条记录\n            </div>\n            <div class=\"flex items-center space-x-2\">\n              <button class=\"px-3 py-1 border rounded-md text-sm hover:bg-gray-100\">\n                上一页\n              </button>\n              <span class=\"px-3 py-1 text-sm\">1 / 1</span>\n              <button class=\"px-3 py-1 border rounded-md text-sm hover:bg-gray-100\">\n                下一页\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 创建秒杀弹窗 -->\n    <div v-if=\"showAddModal\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div class=\"bg-white rounded-lg p-6 w-full max-w-3xl mx-4 max-h-[90vh] overflow-y-auto\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h3 class=\"text-lg font-semibold text-gray-900\">创建秒杀活动</h3>\n          <button @click=\"showAddModal = false\" class=\"text-gray-400 hover:text-gray-600\">\n            <i class=\"ri-close-line text-xl\"></i>\n          </button>\n        </div>\n\n        <div class=\"space-y-4\">\n          <div class=\"grid grid-cols-2 gap-4\">\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">选择商品</label>\n              <select\n                v-model=\"newFlashSale.productId\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"\">请选择商品</option>\n                <option value=\"1\">轻奢纯棉刺绣水洗四件套</option>\n                <option value=\"2\">秋冬保暖加厚澳洲羊毛被</option>\n              </select>\n            </div>\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">选择时段</label>\n              <select\n                v-model=\"newFlashSale.timeSlotId\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"\">请选择时段</option>\n                <option v-for=\"slot in timeSlots\" :key=\"slot.id\" :value=\"slot.id\">\n                  {{ slot.name }} {{ slot.time }}\n                </option>\n              </select>\n            </div>\n          </div>\n\n          <div class=\"grid grid-cols-3 gap-4\">\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">秒杀价格</label>\n              <input\n                v-model=\"newFlashSale.flashPrice\"\n                type=\"number\"\n                step=\"0.01\"\n                placeholder=\"请输入秒杀价格\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">秒杀库存</label>\n              <input\n                v-model=\"newFlashSale.stock\"\n                type=\"number\"\n                placeholder=\"请输入秒杀库存\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">限购数量</label>\n              <input\n                v-model=\"newFlashSale.limitQuantity\"\n                type=\"number\"\n                placeholder=\"每人限购数量\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n          </div>\n\n          <div class=\"grid grid-cols-2 gap-4\">\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">开始时间</label>\n              <input\n                v-model=\"newFlashSale.startTime\"\n                type=\"datetime-local\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">结束时间</label>\n              <input\n                v-model=\"newFlashSale.endTime\"\n                type=\"datetime-local\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n          </div>\n\n          <div class=\"flex justify-end space-x-3 pt-4 border-t\">\n            <button @click=\"showAddModal = false\" class=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\">\n              取消\n            </button>\n            <button @click=\"createFlashSale\" class=\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700\">\n              创建秒杀\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 时段管理弹窗 -->\n    <div v-if=\"showTimeSlotModal\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div class=\"bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h3 class=\"text-lg font-semibold text-gray-900\">秒杀时段管理</h3>\n          <button @click=\"showTimeSlotModal = false\" class=\"text-gray-400 hover:text-gray-600\">\n            <i class=\"ri-close-line text-xl\"></i>\n          </button>\n        </div>\n\n        <div class=\"space-y-4\">\n          <div class=\"grid grid-cols-1 gap-4\">\n            <div v-for=\"slot in timeSlots\" :key=\"slot.id\" class=\"flex items-center justify-between p-4 border rounded-lg\">\n              <div>\n                <div class=\"font-medium text-gray-900\">{{ slot.name }}</div>\n                <div class=\"text-sm text-gray-500\">{{ slot.time }}</div>\n                <div class=\"text-xs text-gray-400\">{{ slot.productCount }} 个商品</div>\n              </div>\n              <div class=\"flex items-center space-x-2\">\n                <button class=\"px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700\">\n                  编辑\n                </button>\n                <button class=\"px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700\">\n                  删除\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"flex justify-end space-x-3 pt-4 border-t\">\n            <button @click=\"showTimeSlotModal = false\" class=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\">\n              关闭\n            </button>\n            <button class=\"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700\">\n              添加时段\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FlashSalePage',\n  data() {\n    return {\n      // 视图模式\n      viewMode: 'grid',\n\n      // 搜索和筛选\n      searchQuery: '',\n      filterStatus: '',\n      filterCategory: '',\n\n      // 弹窗状态\n      showAddModal: false,\n      showTimeSlotModal: false,\n\n      // 统计数据\n      statistics: {\n        totalCampaigns: 0,\n        activeCampaigns: 0,\n        todayRounds: 0,\n        flashSaleOrders: 0,\n        flashSaleSales: '0.00'\n      },\n\n      // 新秒杀活动数据\n      newFlashSale: {\n        name: '',\n        goodsId: '',\n        flashPrice: '',\n        originalPrice: '',\n        totalStock: '',\n        stockPerRound: '',\n        limitQuantity: 1,\n        startDate: '',\n        endDate: '',\n        dailyStartTime: '09:00:00',\n        dailyEndTime: '22:00:00',\n        roundDuration: 300,\n        breakDuration: 0,\n        autoStart: true\n      },\n\n      // 当前轮次\n      currentRounds: [],\n\n      // 即将开始的轮次\n      upcomingRounds: [],\n\n      // 活动列表\n      campaigns: [],\n\n      // 加载状态\n      loading: false,\n      statisticsLoading: false,\n      timeSlotsLoading: false\n    }\n  },\n\n  mounted() {\n    this.loadData();\n    // 设置定时刷新轮次数据\n    this.refreshTimer = setInterval(() => {\n      this.loadRounds();\n    }, 10000); // 每10秒刷新一次\n  },\n\n  beforeDestroy() {\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer);\n    }\n  },\n\n  computed: {\n    filteredProducts() {\n      let filtered = this.products;\n\n      // 根据选中的时段筛选\n      if (this.selectedTimeSlot) {\n        filtered = filtered.filter(product => product.timeSlotId === this.selectedTimeSlot.id);\n      }\n\n      if (this.searchQuery) {\n        filtered = filtered.filter(product =>\n          product.name.toLowerCase().includes(this.searchQuery.toLowerCase())\n        );\n      }\n\n      if (this.filterStatus) {\n        filtered = filtered.filter(product => product.status === this.filterStatus);\n      }\n\n      if (this.filterCategory) {\n        filtered = filtered.filter(product => product.category === this.filterCategory);\n      }\n\n      return filtered;\n    }\n  },\n\n  mounted() {\n    this.loadData();\n  },\n\n  methods: {\n    // 加载所有数据\n    async loadData() {\n      await Promise.all([\n        this.loadStatistics(),\n        this.loadRounds(),\n        this.loadCampaigns()\n      ]);\n    },\n\n    // 加载统计数据\n    async loadStatistics() {\n      this.statisticsLoading = true;\n      try {\n        const response = await this.axios.get('flashsale/statistics');\n        if (response.data.errno === 0) {\n          this.statistics = response.data.data;\n        } else {\n          console.error('获取统计数据失败:', response.data.errmsg);\n        }\n      } catch (error) {\n        console.error('获取统计数据失败:', error);\n        this.$message.error('获取统计数据失败');\n      } finally {\n        this.statisticsLoading = false;\n      }\n    },\n\n    // 加载轮次数据\n    async loadRounds() {\n      try {\n        const response = await this.axios.get('flashsale/rounds');\n        if (response.data.errno === 0) {\n          this.currentRounds = response.data.data.current || [];\n          this.upcomingRounds = response.data.data.upcoming || [];\n\n          // 为即将开始的轮次计算倒计时\n          this.upcomingRounds.forEach(round => {\n            const startTime = new Date(round.start_time);\n            const now = new Date();\n            round.countdown = Math.max(0, Math.floor((startTime - now) / 1000));\n          });\n        } else {\n          console.error('获取轮次数据失败:', response.data.errmsg);\n        }\n      } catch (error) {\n        console.error('获取轮次数据失败:', error);\n      }\n    },\n\n    // 加载活动列表\n    async loadCampaigns() {\n      this.loading = true;\n      try {\n        const params = {\n          search: this.searchQuery,\n          status: this.filterStatus,\n          page: 1,\n          limit: 50\n        };\n\n        const response = await this.axios.get('flashsale/campaigns', { params });\n        if (response.data.errno === 0) {\n          this.campaigns = response.data.data;\n        } else {\n          console.error('获取活动列表失败:', response.data.errmsg);\n        }\n      } catch (error) {\n        console.error('获取活动列表失败:', error);\n        this.$message.error('获取活动列表失败');\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 获取状态标签\n    getStatusLabel(status) {\n      const labels = {\n        active: '进行中',\n        upcoming: '即将开始',\n        ended: '已结束',\n        disabled: '已停用'\n      };\n      return labels[status] || status;\n    },\n\n    // 选择时段\n    selectTimeSlot(timeSlot) {\n      this.selectedTimeSlot = this.selectedTimeSlot?.id === timeSlot.id ? null : timeSlot;\n      this.loadProducts(); // 重新加载商品列表\n    },\n\n    // 重置筛选\n    resetFilters() {\n      this.searchQuery = '';\n      this.filterStatus = '';\n      this.filterCategory = '';\n    },\n\n    // 格式化时间显示\n    formatTime(timeStr) {\n      const date = new Date(timeStr);\n      return date.toLocaleString('zh-CN', {\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    // 格式化倒计时\n    formatCountdown(seconds) {\n      if (seconds <= 0) return '即将开始';\n\n      const minutes = Math.floor(seconds / 60);\n      const remainingSeconds = seconds % 60;\n\n      if (minutes > 0) {\n        return `${minutes}分${remainingSeconds}秒`;\n      } else {\n        return `${remainingSeconds}秒`;\n      }\n    },\n\n    // 查看商品详情\n    viewProduct(product) {\n      console.log('查看商品详情:', product);\n      this.$message.info('查看功能开发中');\n    },\n\n    // 编辑商品\n    editProduct(product) {\n      console.log('编辑商品:', product);\n      this.$message.info('编辑功能开发中');\n    },\n\n    // 切换商品状态\n    toggleProductStatus(product) {\n      console.log('切换商品状态:', product);\n      this.$message.info('状态切换功能开发中');\n    },\n\n    // 创建秒杀活动\n    async createFlashSale() {\n      try {\n        const response = await this.axios.post('flashsale/create', this.newFlashSale);\n        if (response.data.errno === 0) {\n          this.showAddModal = false;\n          this.$message.success('秒杀活动创建成功');\n          this.loadData(); // 重新加载数据\n          // 重置表单\n          this.newFlashSale = {\n            productId: '',\n            timeSlotId: '',\n            flashPrice: '',\n            stock: '',\n            limitQuantity: '',\n            startTime: '',\n            endTime: ''\n          };\n        } else {\n          this.$message.error(response.data.errmsg || '创建失败');\n        }\n      } catch (error) {\n        console.error('创建秒杀活动失败:', error);\n        this.$message.error('创建失败');\n      }\n    },\n\n    // 刷新数据\n    refreshData() {\n      this.loadData();\n      this.$message.success('数据刷新成功');\n    }\n  }\n}\n</script>\n\n<style scoped>\n.flash-sale-page {\n  min-height: 100vh;\n  background-color: #f9fafb;\n}\n\n.page-header {\n  position: sticky;\n  top: 0;\n  z-index: 10;\n}\n\n/* 表格样式 */\n.overflow-x-auto {\n  overflow-x: auto;\n}\n\ntable {\n  min-width: 100%;\n}\n\n/* 文本截断 */\n.line-clamp-2 {\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .grid-cols-1.md\\\\:grid-cols-4 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .grid-cols-2.md\\\\:grid-cols-4.lg\\\\:grid-cols-6 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n}\n</style>"]}]}