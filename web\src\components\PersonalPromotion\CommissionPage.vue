<template>
  <div class="commission-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>推广佣金</h2>
      <p>个人推广佣金管理和结算</p>
    </div>

    <!-- 佣金统计 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-number">¥{{ stats.total_commission }}</div>
        <div class="stat-label">总佣金</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">¥{{ stats.pending_commission }}</div>
        <div class="stat-label">待结算佣金</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">¥{{ stats.settled_commission }}</div>
        <div class="stat-label">已结算佣金</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">¥{{ stats.month_commission }}</div>
        <div class="stat-label">本月佣金</div>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-filters">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="推广员">
          <el-input
            v-model="searchForm.keyword"
            placeholder="推广员昵称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="待结算" value="pending" />
            <el-option label="已结算" value="settled" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchCommissions">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 佣金记录列表 -->
    <div class="commissions-table">
      <el-table
        v-loading="loading"
        :data="commissions"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="推广员" width="200">
          <template #default="scope">
            <div class="promoter-info">
              <div>
                <div>{{ scope.row.promoter_nickname }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="订单信息" width="150">
          <template #default="scope">
            <div>
              <div>{{ scope.row.order_sn }}</div>
              <div class="amount">¥{{ scope.row.order_amount }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="商品信息" width="200">
          <template #default="scope">
            <div>{{ scope.row.goods_name }}</div>
          </template>
        </el-table-column>
        <el-table-column label="佣金比例" width="100">
          <template #default="scope">
            <span>{{ scope.row.commission_rate }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="佣金金额" width="120">
          <template #default="scope">
            <span class="commission">¥{{ scope.row.commission_amount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="160">
          <template #default="scope">
            <span>{{ formatTime(scope.row.create_time) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button 
              v-if="scope.row.status === 'pending'"
              type="text" 
              size="small" 
              @click="settleCommission(scope.row)"
            >
              结算
            </el-button>
            <el-button type="text" size="small" @click="viewDetail(scope.row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'CommissionPage',
  data() {
    return {
      loading: false,
      commissions: [],
      stats: {
        total_commission: '0.00',
        pending_commission: '0.00',
        settled_commission: '0.00',
        month_commission: '0.00'
      },
      searchForm: {
        keyword: '',
        status: '',
        dateRange: []
      },
      currentPage: 1,
      pageSize: 20,
      total: 0
    }
  },
  mounted() {
    this.loadStats();
    this.loadCommissions();
  },
  methods: {
    async loadStats() {
      // 这里调用佣金统计API
      console.log('加载佣金统计数据');
    },
    async loadCommissions() {
      this.loading = true;
      try {
        // 这里调用佣金记录API
        console.log('加载佣金记录');
        // 模拟数据
        this.commissions = [];
        this.total = 0;
      } catch (error) {
        console.error('加载佣金记录失败:', error);
        this.$message.error('加载数据失败');
      } finally {
        this.loading = false;
      }
    },
    searchCommissions() {
      this.currentPage = 1;
      this.loadCommissions();
    },
    resetSearch() {
      this.searchForm = {
        keyword: '',
        status: '',
        dateRange: []
      };
      this.currentPage = 1;
      this.loadCommissions();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.loadCommissions();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.loadCommissions();
    },
    settleCommission(commission) {
      console.log('结算佣金:', commission);
    },
    viewDetail(commission) {
      console.log('查看佣金详情:', commission);
    },
    getStatusText(status) {
      const statusMap = {
        'pending': '待结算',
        'settled': '已结算',
        'cancelled': '已取消'
      };
      return statusMap[status] || status;
    },
    getStatusType(status) {
      const typeMap = {
        'pending': 'warning',
        'settled': 'success',
        'cancelled': 'danger'
      };
      return typeMap[status] || '';
    },
    formatTime(timestamp) {
      return new Date(timestamp * 1000).toLocaleString('zh-CN');
    }
  }
}
</script>

<style scoped>
.commission-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.search-filters {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.commissions-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.promoter-info {
  display: flex;
  align-items: center;
  gap: 12px;
}



.amount {
  font-size: 12px;
  color: #909399;
}

.commission {
  color: #67C23A;
  font-weight: bold;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
