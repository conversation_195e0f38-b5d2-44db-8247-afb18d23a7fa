{"version": 3, "sources": ["..\\..\\..\\src\\common\\model\\flash_sale_round_goods.js"], "names": ["module", "exports", "think", "Model", "addRoundGoods", "roundId", "goodsList", "goodsData", "map", "goods", "index", "round_id", "goods_id", "goods_name", "goods_image", "original_price", "flash_price", "discount_rate", "calculateDiscountRate", "stock", "sold_count", "limit_quantity", "sort_order", "addMany", "originalPrice", "flashPrice", "Math", "round", "getRoundGoodsList", "where", "order", "select", "checkGoodsInActiveRounds", "goodsIds", "roundModel", "model", "activeGoods", "alias", "join", "table", "as", "on", "field", "checkGoodsInOverlappingRounds", "startTime", "endTime", "overlappingGoods", "updateSoldCount", "roundGoodsId", "quantity", "id", "increment", "getAvailableStock", "roundGoods", "find", "isEmpty", "max", "getUserPurchaseCount", "goodsId", "userId", "orderModel", "result", "user_id", "sum"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;;AAEzC;;;AAGMC,eAAN,CAAoBC,OAApB,EAA6BC,SAA7B,EAAwC;AAAA;;AAAA;AACtC,YAAMC,YAAYD,UAAUE,GAAV,CAAc,UAACC,KAAD,EAAQC,KAAR;AAAA,eAAmB;AACjDC,oBAAUN,OADuC;AAEjDO,oBAAUH,MAAMG,QAFiC;AAGjDC,sBAAYJ,MAAMI,UAH+B;AAIjDC,uBAAaL,MAAMK,WAAN,IAAqB,EAJe;AAKjDC,0BAAgBN,MAAMM,cAL2B;AAMjDC,uBAAaP,MAAMO,WAN8B;AAOjDC,yBAAeR,MAAMQ,aAAN,IAAuB,MAAKC,qBAAL,CAA2BT,MAAMM,cAAjC,EAAiDN,MAAMO,WAAvD,CAPW;AAQjDG,iBAAOV,MAAMU,KARoC;AASjDC,sBAAY,CATqC;AAUjDC,0BAAgBZ,MAAMY,cAAN,IAAwB,CAVS;AAWjDC,sBAAYZ;AAXqC,SAAnB;AAAA,OAAd,CAAlB;;AAcA,aAAO,MAAM,MAAKa,OAAL,CAAahB,SAAb,CAAb;AAfsC;AAgBvC;;AAED;;;AAGAW,wBAAsBM,aAAtB,EAAqCC,UAArC,EAAiD;AAC/C,QAAI,CAACD,aAAD,IAAkBA,iBAAiB,CAAvC,EAA0C,OAAO,CAAP;AAC1C,WAAOE,KAAKC,KAAL,CAAW,CAAC,IAAIF,aAAaD,aAAlB,IAAmC,GAAnC,GAAyC,GAApD,IAA2D,GAAlE,CAF+C,CAEwB;AACxE;;AAED;;;AAGMI,mBAAN,CAAwBvB,OAAxB,EAAiC;AAAA;;AAAA;AAC/B,aAAO,MAAM,OAAKwB,KAAL,CAAW,EAAElB,UAAUN,OAAZ,EAAX,EACVyB,KADU,CACJ,gBADI,EAEVC,MAFU,EAAb;AAD+B;AAIhC;;AAED;;;AAGMC,0BAAN,CAA+BC,QAA/B,EAAyC;AAAA;;AAAA;AACvC,YAAMC,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;;AAEA;AACA,YAAMC,cAAc,MAAM,OAAKC,KAAL,CAAW,IAAX,EACvBC,IADuB,CAClB;AACJC,eAAO,mBADH;AAEJD,cAAM,OAFF;AAGJE,YAAI,GAHA;AAIJC,YAAI,CAAC,aAAD,EAAgB,MAAhB;AAJA,OADkB,EAOvBZ,KAPuB,CAOjB;AACL,uBAAe,CAAC,IAAD,EAAOI,QAAP,CADV;AAEL,oBAAY,CAAC,IAAD,EAAO,CAAC,UAAD,EAAa,QAAb,CAAP;AAFP,OAPiB,EAWvBS,KAXuB,CAWjB,iEAXiB,EAYvBX,MAZuB,EAA1B;;AAcA,aAAOK,WAAP;AAlBuC;AAmBxC;;AAED;;;AAGMO,+BAAN,CAAoCV,QAApC,EAA8CW,SAA9C,EAAyDC,OAAzD,EAAkE;AAAA;;AAAA;AAChE,YAAMX,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;;AAEA;AACA,YAAMW,mBAAmB,MAAM,OAAKT,KAAL,CAAW,IAAX,EAC5BC,IAD4B,CACvB;AACJC,eAAO,mBADH;AAEJD,cAAM,OAFF;AAGJE,YAAI,GAHA;AAIJC,YAAI,CAAC,aAAD,EAAgB,MAAhB;AAJA,OADuB,EAO5BZ,KAP4B,CAOtB;AACL,uBAAe,CAAC,IAAD,EAAOI,QAAP,CADV;AAEL,oBAAY,CAAC,IAAD,EAAO,CAAC,UAAD,EAAa,QAAb,CAAP,CAFP;AAGL,oBAAY;AACV,oBAAU,IADA;AAEV;AACA,2BAAiB;AACf,4BAAgB,CAAC,IAAD,EAAOW,SAAP,CADD;AAEf,0BAAc,CAAC,GAAD,EAAMA,SAAN;AAFC,WAHP;AAOV;AACA,yBAAe;AACb,4BAAgB,CAAC,GAAD,EAAMC,OAAN,CADH;AAEb,0BAAc,CAAC,IAAD,EAAOA,OAAP;AAFD,WARL;AAYV;AACA,sBAAY;AACV,4BAAgB,CAAC,IAAD,EAAOD,SAAP,CADN;AAEV,0BAAc,CAAC,IAAD,EAAOC,OAAP;AAFJ;AAbF;AAHP,OAPsB,EA6B5BH,KA7B4B,CA6BtB,iEA7BsB,EA8B5BX,MA9B4B,EAA/B;;AAgCA,aAAOe,gBAAP;AApCgE;AAqCjE;;AAED;;;AAGMC,iBAAN,CAAsBC,YAAtB,EAAoCC,QAApC,EAA8C;AAAA;;AAAA;AAC5C,aAAO,MAAM,OAAKpB,KAAL,CAAW,EAAEqB,IAAIF,YAAN,EAAX,EACVG,SADU,CACA,YADA,EACcF,QADd,CAAb;AAD4C;AAG7C;;AAED;;;AAGMG,mBAAN,CAAwBJ,YAAxB,EAAsC;AAAA;;AAAA;AACpC,YAAMK,aAAa,MAAM,OAAKxB,KAAL,CAAW,EAAEqB,IAAIF,YAAN,EAAX,EAAiCM,IAAjC,EAAzB;AACA,UAAIpD,MAAMqD,OAAN,CAAcF,UAAd,CAAJ,EAA+B;AAC7B,eAAO,CAAP;AACD;AACD,aAAO3B,KAAK8B,GAAL,CAAS,CAAT,EAAYH,WAAWlC,KAAX,GAAmBkC,WAAWjC,UAA1C,CAAP;AALoC;AAMrC;;AAED;;;AAGMqC,sBAAN,CAA2BpD,OAA3B,EAAoCqD,OAApC,EAA6CC,MAA7C,EAAqD;AAAA;;AAAA;AACnD,YAAMC,aAAa,OAAKzB,KAAL,CAAW,mBAAX,CAAnB;AACA,YAAM0B,SAAS,MAAMD,WAAW/B,KAAX,CAAiB;AACpClB,kBAAUN,OAD0B;AAEpCO,kBAAU8C,OAF0B;AAGpCI,iBAASH;AAH2B,OAAjB,EAIlBI,GAJkB,CAId,UAJc,CAArB;;AAMA,aAAOF,UAAU,CAAjB;AARmD;AASpD;AAzIwC,CAA3C", "file": "..\\..\\..\\src\\common\\model\\flash_sale_round_goods.js", "sourcesContent": ["module.exports = class extends think.Model {\n  \n  /**\n   * 批量添加轮次商品\n   */\n  async addRoundGoods(roundId, goodsList) {\n    const goodsData = goodsList.map((goods, index) => ({\n      round_id: roundId,\n      goods_id: goods.goods_id,\n      goods_name: goods.goods_name,\n      goods_image: goods.goods_image || '',\n      original_price: goods.original_price,\n      flash_price: goods.flash_price,\n      discount_rate: goods.discount_rate || this.calculateDiscountRate(goods.original_price, goods.flash_price),\n      stock: goods.stock,\n      sold_count: 0,\n      limit_quantity: goods.limit_quantity || 1,\n      sort_order: index\n    }));\n    \n    return await this.addMany(goodsData);\n  }\n  \n  /**\n   * 计算折扣率\n   */\n  calculateDiscountRate(originalPrice, flashPrice) {\n    if (!originalPrice || originalPrice <= 0) return 0;\n    return Math.round((1 - flashPrice / originalPrice) * 100 * 100) / 100; // 保留2位小数\n  }\n  \n  /**\n   * 获取轮次商品列表\n   */\n  async getRoundGoodsList(roundId) {\n    return await this.where({ round_id: roundId })\n      .order('sort_order ASC')\n      .select();\n  }\n  \n  /**\n   * 检查商品是否已参与其他活跃轮次\n   */\n  async checkGoodsInActiveRounds(goodsIds) {\n    const roundModel = this.model('flash_sale_rounds');\n\n    // 查询活跃轮次中的商品\n    const activeGoods = await this.alias('rg')\n      .join({\n        table: 'flash_sale_rounds',\n        join: 'inner',\n        as: 'r',\n        on: ['rg.round_id', 'r.id']\n      })\n      .where({\n        'rg.goods_id': ['IN', goodsIds],\n        'r.status': ['IN', ['upcoming', 'active']]\n      })\n      .field('rg.goods_id, r.round_number, r.start_time, r.end_time, r.status')\n      .select();\n\n    return activeGoods;\n  }\n\n  /**\n   * 检查商品在指定时间段是否有重叠的轮次\n   */\n  async checkGoodsInOverlappingRounds(goodsIds, startTime, endTime) {\n    const roundModel = this.model('flash_sale_rounds');\n\n    // 查询时间重叠的轮次中的商品\n    const overlappingGoods = await this.alias('rg')\n      .join({\n        table: 'flash_sale_rounds',\n        join: 'inner',\n        as: 'r',\n        on: ['rg.round_id', 'r.id']\n      })\n      .where({\n        'rg.goods_id': ['IN', goodsIds],\n        'r.status': ['IN', ['upcoming', 'active']],\n        '_complex': {\n          '_logic': 'OR',\n          // 新轮次开始时间在现有轮次时间范围内\n          'start_overlap': {\n            'r.start_time': ['<=', startTime],\n            'r.end_time': ['>', startTime]\n          },\n          // 新轮次结束时间在现有轮次时间范围内\n          'end_overlap': {\n            'r.start_time': ['<', endTime],\n            'r.end_time': ['>=', endTime]\n          },\n          // 新轮次完全包含现有轮次\n          'contains': {\n            'r.start_time': ['>=', startTime],\n            'r.end_time': ['<=', endTime]\n          }\n        }\n      })\n      .field('rg.goods_id, r.round_number, r.start_time, r.end_time, r.status')\n      .select();\n\n    return overlappingGoods;\n  }\n  \n  /**\n   * 更新商品销售数量\n   */\n  async updateSoldCount(roundGoodsId, quantity) {\n    return await this.where({ id: roundGoodsId })\n      .increment('sold_count', quantity);\n  }\n  \n  /**\n   * 获取商品在轮次中的剩余库存\n   */\n  async getAvailableStock(roundGoodsId) {\n    const roundGoods = await this.where({ id: roundGoodsId }).find();\n    if (think.isEmpty(roundGoods)) {\n      return 0;\n    }\n    return Math.max(0, roundGoods.stock - roundGoods.sold_count);\n  }\n  \n  /**\n   * 获取用户在某轮次某商品的购买数量\n   */\n  async getUserPurchaseCount(roundId, goodsId, userId) {\n    const orderModel = this.model('flash_sale_orders');\n    const result = await orderModel.where({\n      round_id: roundId,\n      goods_id: goodsId,\n      user_id: userId\n    }).sum('quantity');\n    \n    return result || 0;\n  }\n};\n"]}