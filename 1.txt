<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>商城首页</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#FF4D4F", secondary: "#FFC53D" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      width: 375px;
      margin: 0 auto;
      min-height: 100vh;
      background-color: #f8f9fa;
      }
      .swiper-pagination-bullet {
      width: 6px;
      height: 6px;
      background: rgba(255, 255, 255, 0.6);
      opacity: 1;
      }
      .swiper-pagination-bullet-active {
      background: #fff;
      }
      .category-scroll::-webkit-scrollbar {
      display: none;
      }
      .coupon-scroll::-webkit-scrollbar {
      display: none;
      }
      .product-card {
      break-inside: avoid;
      }
    </style>
  </head>
  <body class="pb-16">
    <!-- 顶部导航栏 -->
    <div
      class="fixed top-0 w-full bg-primary text-white z-50 px-4 py-3 flex items-center justify-between"
    >
      <div class="text-xl font-['Pacifico']">logo</div>
      <div class="flex-1 mx-3">
        <div
          class="bg-white bg-opacity-20 rounded-full flex items-center px-3 py-1.5"
        >
          <div class="w-4 h-4 flex items-center justify-center mr-2">
            <i class="ri-search-line text-white"></i>
          </div>
          <span class="text-sm text-white opacity-80">搜索商品</span>
        </div>
      </div>
      <div class="w-6 h-6 flex items-center justify-center">
        <i class="ri-message-3-line text-white ri-lg"></i>
      </div>
    </div>
    <!-- 内容区域 -->
    <div class="pt-14 pb-2">
      <!-- 轮播图 -->
      <div
        class="relative w-full h-[150px] overflow-hidden"
        id="swiper-container"
      >
        <div class="swiper-wrapper flex transition-transform duration-300">
          <div class="swiper-slide w-full h-full flex-shrink-0">
            <img
              src="https://readdy.ai/api/search-image?query=promotional%20banner%20for%20online%20shopping%20mall%2C%20featuring%20summer%20fashion%20items%2C%20vibrant%20colors%2C%20clean%20modern%20design%2C%20high-quality%20product%20photography%2C%20eye-catching%20sale%20text%2C%20elegant%20layout%2C%20promotional%20discounts%20prominently%20displayed%2C%20professional%20e-commerce%20aesthetic&width=375&height=150&seq=1&orientation=landscape"
              alt="促销活动"
              class="w-full h-full object-cover"
            />
          </div>
          <div class="swiper-slide w-full h-full flex-shrink-0">
            <img
              src="https://readdy.ai/api/search-image?query=promotional%20banner%20for%20online%20shopping%20mall%2C%20featuring%20electronic%20gadgets%20and%20tech%20products%2C%20modern%20sleek%20design%2C%20blue%20color%20scheme%2C%20high-quality%20product%20photography%2C%20special%20offers%20text%2C%20elegant%20layout%2C%20promotional%20discounts%20prominently%20displayed%2C%20professional%20e-commerce%20aesthetic&width=375&height=150&seq=2&orientation=landscape"
              alt="电子产品"
              class="w-full h-full object-cover"
            />
          </div>
          <div class="swiper-slide w-full h-full flex-shrink-0">
            <img
              src="https://readdy.ai/api/search-image?query=promotional%20banner%20for%20online%20shopping%20mall%2C%20featuring%20beauty%20and%20skincare%20products%2C%20soft%20pastel%20colors%2C%20clean%20modern%20design%2C%20high-quality%20product%20photography%2C%20special%20offers%20text%2C%20elegant%20layout%2C%20promotional%20discounts%20prominently%20displayed%2C%20professional%20e-commerce%20aesthetic&width=375&height=150&seq=3&orientation=landscape"
              alt="美妆护肤"
              class="w-full h-full object-cover"
            />
          </div>
        </div>
        <div
          class="swiper-pagination absolute bottom-2 right-2 flex space-x-1"
        ></div>
      </div>
      <!-- 类目导航 -->
      <div class="px-3 py-4 bg-white rounded-lg mt-3 mx-2">
        <div class="overflow-x-auto category-scroll">
          <div class="grid grid-cols-5 gap-3 w-full">
            <div class="flex flex-col items-center cursor-pointer">
              <div class="w-12 h-12 rounded-full overflow-hidden mb-1">
                <img
                  src="https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20clothing%20and%20fashion%20items%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=4&orientation=squarish"
                  alt="服饰"
                  class="w-full h-full object-cover"
                />
              </div>
              <span
                class="text-xs whitespace-nowrap overflow-hidden text-overflow-ellipsis"
                >服饰</span
              >
            </div>
            <div class="flex flex-col items-center cursor-pointer">
              <div class="w-12 h-12 rounded-full overflow-hidden mb-1">
                <img
                  src="https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20electronic%20devices%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=5&orientation=squarish"
                  alt="电子"
                  class="w-full h-full object-cover"
                />
              </div>
              <span
                class="text-xs whitespace-nowrap overflow-hidden text-overflow-ellipsis"
                >电子</span
              >
            </div>
            <div class="flex flex-col items-center cursor-pointer">
              <div class="w-12 h-12 rounded-full overflow-hidden mb-1">
                <img
                  src="https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20beauty%20and%20skincare%20products%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=6&orientation=squarish"
                  alt="美妆"
                  class="w-full h-full object-cover"
                />
              </div>
              <span
                class="text-xs whitespace-nowrap overflow-hidden text-overflow-ellipsis"
                >美妆</span
              >
            </div>
            <div class="flex flex-col items-center cursor-pointer">
              <div class="w-12 h-12 rounded-full overflow-hidden mb-1">
                <img
                  src="https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20food%20and%20snacks%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=7&orientation=squarish"
                  alt="零食"
                  class="w-full h-full object-cover"
                />
              </div>
              <span
                class="text-xs whitespace-nowrap overflow-hidden text-overflow-ellipsis"
                >零食</span
              >
            </div>
            <div class="flex flex-col items-center cursor-pointer">
              <div class="w-12 h-12 rounded-full overflow-hidden mb-1">
                <img
                  src="https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20home%20appliances%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=8&orientation=squarish"
                  alt="家电"
                  class="w-full h-full object-cover"
                />
              </div>
              <span
                class="text-xs whitespace-nowrap overflow-hidden text-overflow-ellipsis"
                >家电</span
              >
            </div>
          </div>
          <div class="grid grid-cols-5 gap-3 w-full mt-4">
            <div class="flex flex-col items-center cursor-pointer">
              <div class="w-12 h-12 rounded-full overflow-hidden mb-1">
                <img
                  src="https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20furniture%20and%20home%20decor%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=9&orientation=squarish"
                  alt="家居"
                  class="w-full h-full object-cover"
                />
              </div>
              <span
                class="text-xs whitespace-nowrap overflow-hidden text-overflow-ellipsis"
                >家居</span
              >
            </div>
            <div class="flex flex-col items-center cursor-pointer">
              <div class="w-12 h-12 rounded-full overflow-hidden mb-1">
                <img
                  src="https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20mother%20and%20baby%20products%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=10&orientation=squarish"
                  alt="母婴"
                  class="w-full h-full object-cover"
                />
              </div>
              <span
                class="text-xs whitespace-nowrap overflow-hidden text-overflow-ellipsis"
                >母婴</span
              >
            </div>
            <div class="flex flex-col items-center cursor-pointer">
              <div class="w-12 h-12 rounded-full overflow-hidden mb-1">
                <img
                  src="https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20sports%20equipment%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=11&orientation=squarish"
                  alt="运动"
                  class="w-full h-full object-cover"
                />
              </div>
              <span
                class="text-xs whitespace-nowrap overflow-hidden text-overflow-ellipsis"
                >运动</span
              >
            </div>
            <div class="flex flex-col items-center cursor-pointer">
              <div class="w-12 h-12 rounded-full overflow-hidden mb-1">
                <img
                  src="https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20books%20and%20stationery%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=12&orientation=squarish"
                  alt="图书"
                  class="w-full h-full object-cover"
                />
              </div>
              <span
                class="text-xs whitespace-nowrap overflow-hidden text-overflow-ellipsis"
                >图书</span
              >
            </div>
            <div class="flex flex-col items-center cursor-pointer">
              <div class="w-12 h-12 rounded-full overflow-hidden mb-1">
                <img
                  src="https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%2C%20more%20categories%20icon%2C%20the%20icon%20should%20take%20up%2070%25%20of%20the%20frame%2C%20vibrant%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective%2C%20high%20detail%20quality%2C%20clean%20and%20modern%20look%2C%20single%20object%20focus&width=64&height=64&seq=13&orientation=squarish"
                  alt="更多"
                  class="w-full h-full object-cover"
                />
              </div>
              <span
                class="text-xs whitespace-nowrap overflow-hidden text-overflow-ellipsis"
                >更多</span
              >
            </div>
          </div>
        </div>
      </div>
      <!-- 优惠券专区 -->
      <div class="mt-3 mx-2">
        <!-- 新人专享券 -->
        <div class="bg-white rounded-lg p-3 mb-3">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-base font-medium flex items-center">
              <div class="w-5 h-5 flex items-center justify-center mr-1">
                <i class="ri-gift-2-line text-primary"></i>
              </div>
              新人专享券
            </h3>
            <div class="text-xs text-gray-500 flex items-center cursor-pointer">
              查看全部
              <div class="w-4 h-4 flex items-center justify-center ml-0.5">
                <i class="ri-arrow-right-s-line"></i>
              </div>
            </div>
          </div>
          <div class="overflow-x-auto coupon-scroll">
            <div class="flex space-x-3">
              <div
                class="flex-shrink-0 w-[140px] h-[90px] bg-gradient-to-br from-primary/5 to-primary/10 rounded-lg relative overflow-hidden cursor-pointer shadow-lg backdrop-blur-sm"
              >
                <img
                  src="https://readdy.ai/api/search-image?query=abstract%20geometric%20pattern%2C%20minimalist%20design%2C%20soft%20pink%20and%20red%20colors%2C%20subtle%20texture%2C%20elegant%20and%20modern%20style%2C%20perfect%20for%20coupon%20background%2C%20very%20light%20and%20delicate%20pattern&width=140&height=90&seq=20&orientation=landscape"
                  alt="coupon background"
                  class="absolute inset-0 w-full h-full object-cover opacity-10"
                />
                <div class="absolute top-0 left-0 w-full h-full">
                  <div
                    class="absolute -left-6 top-1/2 w-12 h-12 bg-white/80 rounded-full transform -translate-y-1/2 blur-sm"
                  ></div>
                  <div
                    class="absolute -right-6 top-1/2 w-12 h-12 bg-white/80 rounded-full transform -translate-y-1/2 blur-sm"
                  ></div>
                  <div class="h-full flex items-center">
                    <div
                      class="w-2/5 flex items-center justify-center relative border-r border-dashed border-primary/30"
                    >
                      <div class="text-primary drop-shadow-sm">
                        <span class="text-sm font-medium">¥</span>
                        <span class="text-3xl font-bold">50</span>
                      </div>
                    </div>
                    <div
                      class="w-3/5 p-2.5 flex flex-col justify-between h-full"
                    >
                      <div>
                        <div class="text-gray-800 text-xs font-medium">
                          新人专享券
                        </div>
                        <div class="text-primary/60 text-[10px] mt-0.5">
                          无门槛使用
                        </div>
                      </div>
                      <button
                        class="bg-primary text-white text-xs py-1.5 px-3 rounded-full !rounded-button shadow-sm hover:shadow-md transition-shadow"
                      >
                        立即领取
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="flex-shrink-0 w-[140px] h-[90px] bg-gradient-to-br from-secondary/5 to-secondary/10 rounded-lg relative overflow-hidden cursor-pointer shadow-lg backdrop-blur-sm"
              >
                <img
                  src="https://readdy.ai/api/search-image?query=abstract%20geometric%20pattern%2C%20minimalist%20design%2C%20soft%20yellow%20and%20orange%20colors%2C%20subtle%20texture%2C%20elegant%20and%20modern%20style%2C%20perfect%20for%20coupon%20background%2C%20very%20light%20and%20delicate%20pattern&width=140&height=90&seq=21&orientation=landscape"
                  alt="coupon background"
                  class="absolute inset-0 w-full h-full object-cover opacity-10"
                />
                <div class="absolute top-0 left-0 w-full h-full">
                  <div
                    class="absolute -left-6 top-1/2 w-12 h-12 bg-white/80 rounded-full transform -translate-y-1/2 blur-sm"
                  ></div>
                  <div
                    class="absolute -right-6 top-1/2 w-12 h-12 bg-white/80 rounded-full transform -translate-y-1/2 blur-sm"
                  ></div>
                  <div class="h-full flex items-center">
                    <div
                      class="w-2/5 flex items-center justify-center relative border-r border-dashed border-secondary/30"
                    >
                      <div class="text-secondary drop-shadow-sm">
                        <span class="text-sm font-medium">¥</span>
                        <span class="text-3xl font-bold">20</span>
                      </div>
                    </div>
                    <div
                      class="w-3/5 p-2.5 flex flex-col justify-between h-full"
                    >
                      <div>
                        <div class="text-gray-800 text-xs font-medium">
                          新人专享券
                        </div>
                        <div class="text-secondary/60 text-[10px] mt-0.5">
                          满99元可用
                        </div>
                      </div>
                      <button
                        class="bg-secondary text-white text-xs py-1.5 px-3 rounded-full !rounded-button shadow-sm hover:shadow-md transition-shadow"
                      >
                        立即领取
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="flex-shrink-0 w-[140px] h-[90px] bg-gradient-to-br from-primary/5 to-primary/10 rounded-lg relative overflow-hidden cursor-pointer shadow-lg backdrop-blur-sm"
              >
                <img
                  src="https://readdy.ai/api/search-image?query=abstract%20geometric%20pattern%2C%20minimalist%20design%2C%20soft%20pink%20and%20red%20colors%2C%20subtle%20texture%2C%20elegant%20and%20modern%20style%2C%20perfect%20for%20coupon%20background%2C%20very%20light%20and%20delicate%20pattern&width=140&height=90&seq=22&orientation=landscape"
                  alt="coupon background"
                  class="absolute inset-0 w-full h-full object-cover opacity-10"
                />
                <div class="absolute top-0 left-0 w-full h-full">
                  <div
                    class="absolute -left-6 top-1/2 w-12 h-12 bg-white/80 rounded-full transform -translate-y-1/2 blur-sm"
                  ></div>
                  <div
                    class="absolute -right-6 top-1/2 w-12 h-12 bg-white/80 rounded-full transform -translate-y-1/2 blur-sm"
                  ></div>
                  <div class="h-full flex items-center">
                    <div
                      class="w-2/5 flex items-center justify-center relative border-r border-dashed border-primary/30"
                    >
                      <div class="text-primary drop-shadow-sm">
                        <span class="text-sm font-medium">¥</span>
                        <span class="text-3xl font-bold">30</span>
                      </div>
                    </div>
                    <div
                      class="w-3/5 p-2.5 flex flex-col justify-between h-full"
                    >
                      <div>
                        <div class="text-gray-800 text-xs font-medium">
                          新人专享券
                        </div>
                        <div class="text-primary/60 text-[10px] mt-0.5">
                          满199元可用
                        </div>
                      </div>
                      <button
                        class="bg-primary text-white text-xs py-1.5 px-3 rounded-full !rounded-button shadow-sm hover:shadow-md transition-shadow"
                      >
                        立即领取
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 满减优惠券 -->
        <div class="bg-white rounded-lg p-3">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-base font-medium flex items-center">
              <div class="w-5 h-5 flex items-center justify-center mr-1">
                <i class="ri-coupon-3-line text-primary"></i>
              </div>
              满减优惠券
            </h3>
            <div class="text-xs text-gray-500 flex items-center cursor-pointer">
              查看全部
              <div class="w-4 h-4 flex items-center justify-center ml-0.5">
                <i class="ri-arrow-right-s-line"></i>
              </div>
            </div>
          </div>
          <div class="overflow-x-auto coupon-scroll">
            <div class="flex space-x-3">
              <div
                class="flex-shrink-0 w-[140px] h-[90px] rounded-lg relative overflow-hidden cursor-pointer shadow-lg"
              >
                <img
                  src="https://readdy.ai/api/search-image?query=abstract%20luxury%20pattern%2C%20modern%20geometric%20design%2C%20blue%20and%20navy%20colors%2C%20premium%20texture%2C%20elegant%20and%20sophisticated%20style%2C%20perfect%20for%20VIP%20coupon%20background&width=140&height=90&seq=23&orientation=landscape"
                  alt="coupon background"
                  class="absolute inset-0 w-full h-full object-cover"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-r from-[#2193b0]/90 to-[#6dd5ed]/90 backdrop-blur-sm"
                ></div>
                <div class="absolute top-0 left-0 w-full h-full">
                  <div class="h-full flex items-center">
                    <div
                      class="w-2/5 flex items-center justify-center relative border-r border-dashed border-white/30"
                    >
                      <div class="text-white drop-shadow-lg">
                        <span class="text-sm font-medium">¥</span>
                        <span class="text-3xl font-bold">10</span>
                      </div>
                    </div>
                    <div
                      class="w-3/5 p-2.5 flex flex-col justify-between h-full"
                    >
                      <div>
                        <div class="text-white text-xs font-medium">
                          全场通用券
                        </div>
                        <div class="text-white/70 text-[10px] mt-0.5">
                          满99元可用
                        </div>
                      </div>
                      <button
                        class="bg-white/90 hover:bg-white text-[#2193b0] text-xs py-1.5 px-3 rounded-full !rounded-button shadow-sm hover:shadow-md transition-all"
                      >
                        立即领取
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="flex-shrink-0 w-[140px] h-[90px] rounded-lg relative overflow-hidden cursor-pointer shadow-lg"
              >
                <img
                  src="https://readdy.ai/api/search-image?query=abstract%20luxury%20pattern%2C%20modern%20geometric%20design%2C%20purple%20and%20violet%20colors%2C%20premium%20texture%2C%20elegant%20and%20sophisticated%20style%2C%20perfect%20for%20VIP%20coupon%20background&width=140&height=90&seq=24&orientation=landscape"
                  alt="coupon background"
                  class="absolute inset-0 w-full h-full object-cover"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-r from-[#8E2DE2]/90 to-[#4A00E0]/90 backdrop-blur-sm"
                ></div>
                <div class="absolute top-0 left-0 w-full h-full">
                  <div class="h-full flex items-center">
                    <div
                      class="w-2/5 flex items-center justify-center relative border-r border-dashed border-white/30"
                    >
                      <div class="text-white drop-shadow-lg">
                        <span class="text-sm font-medium">¥</span>
                        <span class="text-3xl font-bold">20</span>
                      </div>
                    </div>
                    <div
                      class="w-3/5 p-2.5 flex flex-col justify-between h-full"
                    >
                      <div>
                        <div class="text-white text-xs font-medium">
                          全场通用券
                        </div>
                        <div class="text-white/70 text-[10px] mt-0.5">
                          满199元可用
                        </div>
                      </div>
                      <button
                        class="bg-white/90 hover:bg-white text-[#8E2DE2] text-xs py-1.5 px-3 rounded-full !rounded-button shadow-sm hover:shadow-md transition-all"
                      >
                        立即领取
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="flex-shrink-0 w-[140px] h-[90px] rounded-lg relative overflow-hidden cursor-pointer shadow-lg"
              >
                <img
                  src="https://readdy.ai/api/search-image?query=abstract%20luxury%20pattern%2C%20modern%20geometric%20design%2C%20emerald%20and%20green%20colors%2C%20premium%20texture%2C%20elegant%20and%20sophisticated%20style%2C%20perfect%20for%20VIP%20coupon%20background&width=140&height=90&seq=25&orientation=landscape"
                  alt="coupon background"
                  class="absolute inset-0 w-full h-full object-cover"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-r from-[#11998e]/90 to-[#38ef7d]/90 backdrop-blur-sm"
                ></div>
                <div class="absolute top-0 left-0 w-full h-full">
                  <div class="h-full flex items-center">
                    <div
                      class="w-2/5 flex items-center justify-center relative border-r border-dashed border-white/30"
                    >
                      <div class="text-white drop-shadow-lg">
                        <span class="text-sm font-medium">¥</span>
                        <span class="text-3xl font-bold">50</span>
                      </div>
                    </div>
                    <div
                      class="w-3/5 p-2.5 flex flex-col justify-between h-full"
                    >
                      <div>
                        <div class="text-white text-xs font-medium">
                          全场通用券
                        </div>
                        <div class="text-white/70 text-[10px] mt-0.5">
                          满299元可用
                        </div>
                      </div>
                      <button
                        class="bg-white/90 hover:bg-white text-[#11998e] text-xs py-1.5 px-3 rounded-full !rounded-button shadow-sm hover:shadow-md transition-all"
                      >
                        立即领取
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 精选推荐 -->
      <div class="mt-3 mx-2">
        <div class="bg-white rounded-lg p-3">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-base font-medium flex items-center">
              <div class="w-5 h-5 flex items-center justify-center mr-1">
                <i class="ri-thumb-up-line text-primary"></i>
              </div>
              精选推荐
            </h3>
            <div class="text-xs text-gray-500 flex items-center cursor-pointer">
              更多商品
              <div class="w-4 h-4 flex items-center justify-center ml-0.5">
                <i class="ri-arrow-right-s-line"></i>
              </div>
            </div>
          </div>
          <div class="grid grid-cols-2 gap-3">
            <!-- 商品卡片 1 -->
            <div
              class="product-card rounded-lg overflow-hidden shadow-sm cursor-pointer"
            >
              <div class="w-full h-36 overflow-hidden">
                <img
                  src="https://readdy.ai/api/search-image?query=professional%20product%20photography%20of%20a%20stylish%20womens%20summer%20dress%2C%20light%20floral%20pattern%2C%20hanging%20on%20a%20minimalist%20white%20background%2C%20high-quality%20fabric%20details%20visible%2C%20soft%20natural%20lighting%2C%20commercial%20fashion%20photography%20style%2C%20clean%20composition%2C%20isolated%20product%20with%20subtle%20shadow%2C%20e-commerce%20ready%20image&width=160&height=144&seq=14&orientation=portrait"
                  alt="夏季连衣裙"
                  class="w-full h-full object-cover object-top"
                />
              </div>
              <div class="p-2">
                <div
                  class="text-xs bg-primary bg-opacity-10 text-primary inline-block px-1.5 py-0.5 rounded-sm mb-1"
                >
                  新品上市
                </div>
                <h4 class="text-sm font-medium line-clamp-2 h-10">
                  2025 夏季新款碎花连衣裙女士轻薄透气时尚百搭
                </h4>
                <div class="mt-1 flex items-baseline">
                  <span class="text-primary text-sm">¥</span>
                  <span class="text-primary text-lg font-bold">129</span>
                  <span class="text-gray-400 text-xs line-through ml-1"
                    >¥199</span
                  >
                </div>
                <div class="mt-1 flex items-center justify-between">
                  <span class="text-xs text-gray-500">已售 2856</span>
                  <div
                    class="w-6 h-6 flex items-center justify-center bg-primary bg-opacity-10 rounded-full"
                  >
                    <i class="ri-shopping-cart-line text-primary ri-sm"></i>
                  </div>
                </div>
              </div>
            </div>
            <!-- 商品卡片 2 -->
            <div
              class="product-card rounded-lg overflow-hidden shadow-sm cursor-pointer"
            >
              <div class="w-full h-36 overflow-hidden">
                <img
                  src="https://readdy.ai/api/search-image?query=professional%20product%20photography%20of%20wireless%20bluetooth%20earbuds%20with%20charging%20case%2C%20sleek%20modern%20design%2C%20white%20color%2C%20on%20minimalist%20background%2C%20high-quality%20details%20visible%2C%20soft%20lighting%20highlighting%20product%20features%2C%20commercial%20technology%20photography%20style%2C%20clean%20composition%2C%20isolated%20product%20with%20subtle%20shadow%2C%20e-commerce%20ready%20image&width=160&height=144&seq=15&orientation=portrait"
                  alt="无线耳机"
                  class="w-full h-full object-cover object-top"
                />
              </div>
              <div class="p-2">
                <div
                  class="text-xs bg-secondary bg-opacity-10 text-secondary inline-block px-1.5 py-0.5 rounded-sm mb-1"
                >
                  限时优惠
                </div>
                <h4 class="text-sm font-medium line-clamp-2 h-10">
                  真无线蓝牙耳机 主动降噪 通话降噪 40小时续航
                </h4>
                <div class="mt-1 flex items-baseline">
                  <span class="text-primary text-sm">¥</span>
                  <span class="text-primary text-lg font-bold">299</span>
                  <span class="text-gray-400 text-xs line-through ml-1"
                    >¥399</span
                  >
                </div>
                <div class="mt-1 flex items-center justify-between">
                  <span class="text-xs text-gray-500">已售 5621</span>
                  <div
                    class="w-6 h-6 flex items-center justify-center bg-primary bg-opacity-10 rounded-full"
                  >
                    <i class="ri-shopping-cart-line text-primary ri-sm"></i>
                  </div>
                </div>
              </div>
            </div>
            <!-- 商品卡片 3 -->
            <div
              class="product-card rounded-lg overflow-hidden shadow-sm cursor-pointer"
            >
              <div class="w-full h-36 overflow-hidden">
                <img
                  src="https://readdy.ai/api/search-image?query=professional%20product%20photography%20of%20premium%20skincare%20set%20with%20serum%2C%20cream%20and%20toner%20bottles%2C%20elegant%20packaging%2C%20on%20minimalist%20light%20background%2C%20high-quality%20details%20visible%2C%20soft%20lighting%20highlighting%20product%20features%2C%20commercial%20beauty%20photography%20style%2C%20clean%20composition%2C%20isolated%20products%20with%20subtle%20shadow%2C%20e-commerce%20ready%20image&width=160&height=144&seq=16&orientation=portrait"
                  alt="护肤套装"
                  class="w-full h-full object-cover object-top"
                />
              </div>
              <div class="p-2">
                <div
                  class="text-xs bg-primary bg-opacity-10 text-primary inline-block px-1.5 py-0.5 rounded-sm mb-1"
                >
                  热销爆款
                </div>
                <h4 class="text-sm font-medium line-clamp-2 h-10">
                  高端护肤套装 精华水乳面霜三件套 补水保湿修护
                </h4>
                <div class="mt-1 flex items-baseline">
                  <span class="text-primary text-sm">¥</span>
                  <span class="text-primary text-lg font-bold">368</span>
                  <span class="text-gray-400 text-xs line-through ml-1"
                    >¥498</span
                  >
                </div>
                <div class="mt-1 flex items-center justify-between">
                  <span class="text-xs text-gray-500">已售 3452</span>
                  <div
                    class="w-6 h-6 flex items-center justify-center bg-primary bg-opacity-10 rounded-full"
                  >
                    <i class="ri-shopping-cart-line text-primary ri-sm"></i>
                  </div>
                </div>
              </div>
            </div>
            <!-- 商品卡片 4 -->
            <div
              class="product-card rounded-lg overflow-hidden shadow-sm cursor-pointer"
            >
              <div class="w-full h-36 overflow-hidden">
                <img
                  src="https://readdy.ai/api/search-image?query=professional%20product%20photography%20of%20smart%20watch%20with%20fitness%20tracking%20features%2C%20modern%20sleek%20design%2C%20black%20color%20with%20colorful%20display%2C%20on%20minimalist%20background%2C%20high-quality%20details%20visible%2C%20soft%20lighting%20highlighting%20product%20features%2C%20commercial%20technology%20photography%20style%2C%20clean%20composition%2C%20isolated%20product%20with%20subtle%20shadow%2C%20e-commerce%20ready%20image&width=160&height=144&seq=17&orientation=portrait"
                  alt="智能手表"
                  class="w-full h-full object-cover object-top"
                />
              </div>
              <div class="p-2">
                <div
                  class="text-xs bg-secondary bg-opacity-10 text-secondary inline-block px-1.5 py-0.5 rounded-sm mb-1"
                >
                  限量特惠
                </div>
                <h4 class="text-sm font-medium line-clamp-2 h-10">
                  智能手表 多功能健康监测 运动计步 14天超长续航
                </h4>
                <div class="mt-1 flex items-baseline">
                  <span class="text-primary text-sm">¥</span>
                  <span class="text-primary text-lg font-bold">399</span>
                  <span class="text-gray-400 text-xs line-through ml-1"
                    >¥599</span
                  >
                </div>
                <div class="mt-1 flex items-center justify-between">
                  <span class="text-xs text-gray-500">已售 4289</span>
                  <div
                    class="w-6 h-6 flex items-center justify-center bg-primary bg-opacity-10 rounded-full"
                  >
                    <i class="ri-shopping-cart-line text-primary ri-sm"></i>
                  </div>
                </div>
              </div>
            </div>
            <!-- 商品卡片 5 -->
            <div
              class="product-card rounded-lg overflow-hidden shadow-sm cursor-pointer"
            >
              <div class="w-full h-36 overflow-hidden">
                <img
                  src="https://readdy.ai/api/search-image?query=professional%20product%20photography%20of%20premium%20coffee%20maker%20machine%2C%20modern%20sleek%20design%2C%20stainless%20steel%20finish%2C%20on%20minimalist%20kitchen%20background%2C%20high-quality%20details%20visible%2C%20soft%20lighting%20highlighting%20product%20features%2C%20commercial%20home%20appliance%20photography%20style%2C%20clean%20composition%2C%20isolated%20product%20with%20subtle%20shadow%2C%20e-commerce%20ready%20image&width=160&height=144&seq=18&orientation=portrait"
                  alt="咖啡机"
                  class="w-full h-full object-cover object-top"
                />
              </div>
              <div class="p-2">
                <div
                  class="text-xs bg-primary bg-opacity-10 text-primary inline-block px-1.5 py-0.5 rounded-sm mb-1"
                >
                  新品上市
                </div>
                <h4 class="text-sm font-medium line-clamp-2 h-10">
                  全自动咖啡机 一键制作多种口味 家用办公室两用
                </h4>
                <div class="mt-1 flex items-baseline">
                  <span class="text-primary text-sm">¥</span>
                  <span class="text-primary text-lg font-bold">799</span>
                  <span class="text-gray-400 text-xs line-through ml-1"
                    >¥999</span
                  >
                </div>
                <div class="mt-1 flex items-center justify-between">
                  <span class="text-xs text-gray-500">已售 1256</span>
                  <div
                    class="w-6 h-6 flex items-center justify-center bg-primary bg-opacity-10 rounded-full"
                  >
                    <i class="ri-shopping-cart-line text-primary ri-sm"></i>
                  </div>
                </div>
              </div>
            </div>
            <!-- 商品卡片 6 -->
            <div
              class="product-card rounded-lg overflow-hidden shadow-sm cursor-pointer"
            >
              <div class="w-full h-36 overflow-hidden">
                <img
                  src="https://readdy.ai/api/search-image?query=professional%20product%20photography%20of%20premium%20backpack%2C%20modern%20stylish%20design%2C%20gray%20color%20with%20multiple%20compartments%2C%20on%20minimalist%20background%2C%20high-quality%20fabric%20details%20visible%2C%20soft%20lighting%20highlighting%20product%20features%2C%20commercial%20fashion%20photography%20style%2C%20clean%20composition%2C%20isolated%20product%20with%20subtle%20shadow%2C%20e-commerce%20ready%20image&width=160&height=144&seq=19&orientation=portrait"
                  alt="双肩包"
                  class="w-full h-full object-cover object-top"
                />
              </div>
              <div class="p-2">
                <div
                  class="text-xs bg-secondary bg-opacity-10 text-secondary inline-block px-1.5 py-0.5 rounded-sm mb-1"
                >
                  爆款推荐
                </div>
                <h4 class="text-sm font-medium line-clamp-2 h-10">
                  时尚双肩包 大容量电脑包 商务旅行背包 学生书包
                </h4>
                <div class="mt-1 flex items-baseline">
                  <span class="text-primary text-sm">¥</span>
                  <span class="text-primary text-lg font-bold">159</span>
                  <span class="text-gray-400 text-xs line-through ml-1"
                    >¥259</span
                  >
                </div>
                <div class="mt-1 flex items-center justify-between">
                  <span class="text-xs text-gray-500">已售 3785</span>
                  <div
                    class="w-6 h-6 flex items-center justify-center bg-primary bg-opacity-10 rounded-full"
                  >
                    <i class="ri-shopping-cart-line text-primary ri-sm"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 底部导航 -->
    <div
      class="fixed bottom-0 w-full bg-white border-t border-gray-200 grid grid-cols-5 py-1.5 z-50"
    >
      <div class="flex flex-col items-center cursor-pointer">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-home-5-fill text-primary ri-lg"></i>
        </div>
        <span class="text-xs text-primary mt-0.5">首页</span>
      </div>
      <div class="flex flex-col items-center cursor-pointer">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-apps-line text-gray-500 ri-lg"></i>
        </div>
        <span class="text-xs text-gray-500 mt-0.5">分类</span>
      </div>
      <div class="flex flex-col items-center cursor-pointer">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-shopping-cart-line text-gray-500 ri-lg"></i>
        </div>
        <span class="text-xs text-gray-500 mt-0.5">购物车</span>
      </div>
      <div class="flex flex-col items-center cursor-pointer">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-heart-line text-gray-500 ri-lg"></i>
        </div>
        <span class="text-xs text-gray-500 mt-0.5">收藏</span>
      </div>
      <div class="flex flex-col items-center cursor-pointer">
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-user-line text-gray-500 ri-lg"></i>
        </div>
        <span class="text-xs text-gray-500 mt-0.5">我的</span>
      </div>
    </div>
    <script id="swiperScript">
      document.addEventListener("DOMContentLoaded", function () {
        // 轮播图逻辑
        const swiperContainer = document.getElementById("swiper-container");
        const swiperWrapper = swiperContainer.querySelector(".swiper-wrapper");
        const slides = swiperContainer.querySelectorAll(".swiper-slide");
        const pagination = swiperContainer.querySelector(".swiper-pagination");
        let currentIndex = 0;
        const slideCount = slides.length;
        // 创建分页指示器
        for (let i = 0; i < slideCount; i++) {
          const bullet = document.createElement("span");
          bullet.classList.add("swiper-pagination-bullet");
          if (i === 0) {
            bullet.classList.add("swiper-pagination-bullet-active");
          }
          pagination.appendChild(bullet);
          // 点击分页指示器切换轮播图
          bullet.addEventListener("click", function () {
            goToSlide(i);
          });
        }
        // 切换到指定轮播图
        function goToSlide(index) {
          currentIndex = index;
          swiperWrapper.style.transform = `translateX(-${currentIndex * 100}%)`;
          // 更新分页指示器状态
          const bullets = pagination.querySelectorAll(".swiper-pagination-bullet");
          bullets.forEach((bullet, i) => {
            if (i === currentIndex) {
              bullet.classList.add("swiper-pagination-bullet-active");
            } else {
              bullet.classList.remove("swiper-pagination-bullet-active");
            }
          });
        }
        // 自动轮播
        let autoplayInterval = setInterval(function () {
          currentIndex = (currentIndex + 1) % slideCount;
          goToSlide(currentIndex);
        }, 3000);
        // 触摸事件处理
        let startX = 0;
        let isDragging = false;
        swiperContainer.addEventListener("touchstart", function (e) {
          startX = e.touches[0].clientX;
          isDragging = true;
          clearInterval(autoplayInterval);
        });
        swiperContainer.addEventListener("touchmove", function (e) {
          if (!isDragging) return;
          const currentX = e.touches[0].clientX;
          const diff = currentX - startX;
          const containerWidth = swiperContainer.offsetWidth;
          // 计算拖动的百分比
          const percentage = (diff / containerWidth) * 100;
          // 应用拖动效果，但限制在合理范围内
          const translateX = -currentIndex * 100 + percentage;
          if (translateX <= 0 && translateX >= -((slideCount - 1) * 100)) {
            swiperWrapper.style.transform = `translateX(${translateX}%)`;
          }
        });
        swiperContainer.addEventListener("touchend", function (e) {
          if (!isDragging) return;
          isDragging = false;
          const endX = e.changedTouches[0].clientX;
          const diff = endX - startX;
          // 判断滑动方向和距离
          if (Math.abs(diff) > 50) {
            if (diff > 0 && currentIndex > 0) {
              // 向右滑动，显示上一张
              currentIndex--;
            } else if (diff < 0 && currentIndex < slideCount - 1) {
              // 向左滑动，显示下一张
              currentIndex++;
            }
          }
          // 切换到目标轮播图
          goToSlide(currentIndex);
          // 恢复自动轮播
          autoplayInterval = setInterval(function () {
            currentIndex = (currentIndex + 1) % slideCount;
            goToSlide(currentIndex);
          }, 3000);
        });
      });
    </script>
    <script id="couponScript">
      document.addEventListener("DOMContentLoaded", function () {
        // 优惠券领取功能
        const couponButtons = document.querySelectorAll(".coupon-scroll button");
        couponButtons.forEach((button) => {
          button.addEventListener("click", function (e) {
            e.stopPropagation(); // 阻止事件冒泡
            // 获取优惠券信息
            const couponCard = this.closest(".flex-shrink-0");
            const amountElement = couponCard.querySelector(".text-2xl");
            const amount = amountElement ? amountElement.textContent : "";
            const typeElement = couponCard.querySelector(".w-3\\/5 .text-xs");
            const type = typeElement ? typeElement.textContent : "";
            // 修改按钮状态
            this.textContent = "已领取";
            this.disabled = true;
            this.classList.add("opacity-70");
            // 显示领取成功提示
            showToast(`成功领取${amount}元${type}`);
          });
        });
        // 显示提示信息
        function showToast(message) {
          // 创建提示元素
          const toast = document.createElement("div");
          toast.className =
            "fixed top-16 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 text-white px-4 py-2 rounded-lg z-50";
          toast.textContent = message;
          // 添加到页面
          document.body.appendChild(toast);
          // 2秒后自动消失
          setTimeout(() => {
            toast.classList.add("opacity-0", "transition-opacity", "duration-300");
            setTimeout(() => {
              document.body.removeChild(toast);
            }, 300);
          }, 2000);
        }
      });
    </script>
    <script id="productScript">
      document.addEventListener("DOMContentLoaded", function () {
        // 商品卡片点击事件
        const productCards = document.querySelectorAll(".product-card");
        productCards.forEach((card) => {
          card.addEventListener("click", function () {
            // 获取商品信息
            const title = this.querySelector("h4").textContent;
            showToast(`查看商品: ${title}`);
            // 实际应用中这里应该跳转到商品详情页
          });
          // 购物车按钮点击事件
          const cartBtn = card.querySelector(".w-6.h-6");
          if (cartBtn) {
            cartBtn.addEventListener("click", function (e) {
              e.stopPropagation(); // 阻止事件冒泡，避免触发卡片点击
              // 获取商品信息
              const card = this.closest(".product-card");
              const title = card.querySelector("h4").textContent;
              const price = card.querySelector(".text-lg").textContent;
              // 显示添加成功提示
              showToast(`已添加到购物车: ${title}`);
              // 添加动画效果
              this.classList.add("scale-125");
              setTimeout(() => {
                this.classList.remove("scale-125");
              }, 200);
            });
          }
        });
        // 显示提示信息
        function showToast(message) {
          // 创建提示元素
          const toast = document.createElement("div");
          toast.className =
            "fixed top-16 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 text-white px-4 py-2 rounded-lg z-50";
          toast.textContent = message;
          // 添加到页面
          document.body.appendChild(toast);
          // 2秒后自动消失
          setTimeout(() => {
            toast.classList.add("opacity-0", "transition-opacity", "duration-300");
            setTimeout(() => {
              document.body.removeChild(toast);
            }, 300);
          }, 2000);
        }
      });
    </script>
    <script id="navScript">
      document.addEventListener("DOMContentLoaded", function () {
        // 底部导航点击事件
        const navItems = document.querySelectorAll(".fixed.bottom-0 .flex.flex-col");
        navItems.forEach((item, index) => {
          item.addEventListener("click", function () {
            // 更新导航项的激活状态
            navItems.forEach((navItem) => {
              const icon = navItem.querySelector("i");
              const text = navItem.querySelector("span");
              icon.classList.remove("text-primary");
              icon.classList.add("text-gray-500");
              text.classList.remove("text-primary");
              text.classList.add("text-gray-500");
              // 将fill图标改为line图标
              if (icon.className.includes("-fill")) {
                icon.className = icon.className.replace("-fill", "-line");
              }
            });
            // 设置当前项为激活状态
            const currentIcon = this.querySelector("i");
            const currentText = this.querySelector("span");
            currentIcon.classList.remove("text-gray-500");
            currentIcon.classList.add("text-primary");
            currentText.classList.remove("text-gray-500");
            currentText.classList.add("text-primary");
            // 将line图标改为fill图标
            if (currentIcon.className.includes("-line")) {
              currentIcon.className = currentIcon.className.replace("-line", "-fill");
            }
            // 显示页面切换提示
            const pageName = currentText.textContent;
            if (pageName !== "首页") {
              showToast(`切换到${pageName}页面`);
            }
          });
        });
        // 显示提示信息
        function showToast(message) {
          // 创建提示元素
          const toast = document.createElement("div");
          toast.className =
            "fixed top-16 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 text-white px-4 py-2 rounded-lg z-50";
          toast.textContent = message;
          // 添加到页面
          document.body.appendChild(toast);
          // 2秒后自动消失
          setTimeout(() => {
            toast.classList.add("opacity-0", "transition-opacity", "duration-300");
            setTimeout(() => {
              document.body.removeChild(toast);
            }, 300);
          }, 2000);
        }
      });
    </script>
  </body>
</html>
