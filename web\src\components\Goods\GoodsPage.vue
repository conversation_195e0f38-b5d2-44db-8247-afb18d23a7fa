<template>
    <div class="goods-management-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div class="title-section">
                    <h1 class="page-title">
                        <i class="el-icon-goods"></i>
                        商品管理
                    </h1>
                    <p class="page-subtitle">管理商品信息，控制商品上架状态和库存</p>
                </div>
                <div class="header-actions">
                    <router-link to="/dashboard/goods/add">
                        <el-button type="primary" icon="el-icon-plus" size="medium" class="add-btn">
                            添加商品
                        </el-button>
                    </router-link>
                </div>
            </div>
        </div>

        <!-- 统计卡片区域 -->
        <div class="stats-section">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon total">
                        <i class="el-icon-goods"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ total }}</div>
                        <div class="stat-label">商品总数</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon success">
                        <i class="el-icon-check"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ onSaleCount }}</div>
                        <div class="stat-label">在售商品</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon warning">
                        <i class="el-icon-warning"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ lowStockCount }}</div>
                        <div class="stat-label">库存不足</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon danger">
                        <i class="el-icon-close"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ offSaleCount }}</div>
                        <div class="stat-label">已下架</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选和排序区域 -->
        <div class="filter-section">
            <el-card class="filter-card" shadow="never">
                <div class="filter-header">
                    <div class="sort-buttons">
                        <span class="sort-label">排序方式：</span>
                        <el-button-group class="sort-group">
                            <el-button
                                :type="activeClass == 1 ? 'primary' : 'default'"
                                :plain="activeClass != 1"
                                size="small"
                                @click="sortOrder(1)">
                                <i class="el-icon-s-data"></i>
                                销量
                            </el-button>
                            <el-button
                                :type="activeClass == 2 ? 'primary' : 'default'"
                                :plain="activeClass != 2"
                                size="small"
                                @click="sortOrder(2)">
                                <i class="el-icon-money"></i>
                                价格
                            </el-button>
                            <el-button
                                :type="activeClass == 3 ? 'primary' : 'default'"
                                :plain="activeClass != 3"
                                size="small"
                                @click="sortOrder(3)">
                                <i class="el-icon-box"></i>
                                库存
                            </el-button>
                        </el-button-group>
                    </div>
                </div>

                <div class="filter-content">
                    <el-tabs v-model="activeName" @tab-click="handleClick" class="status-tabs">
                        <el-tab-pane name="first">
                            <span slot="label">
                                <i class="el-icon-goods"></i>
                                全部商品
                            </span>
                        </el-tab-pane>
                        <el-tab-pane name="second">
                            <span slot="label">
                                <i class="el-icon-check"></i>
                                出售中
                            </span>
                        </el-tab-pane>
                        <el-tab-pane name="third">
                            <span slot="label">
                                <i class="el-icon-sold-out"></i>
                                已售完
                            </span>
                        </el-tab-pane>
                        <el-tab-pane name="fourth">
                            <span slot="label">
                                <i class="el-icon-close"></i>
                                已下架
                            </span>
                        </el-tab-pane>
                    </el-tabs>

                    <div class="search-form">
                        <el-form :inline="true" :model="filterForm" class="filter-form">
                            <el-form-item label="商品名称">
                                <el-input
                                    v-model="filterForm.name"
                                    placeholder="请输入商品名称"
                                    prefix-icon="el-icon-search"
                                    clearable
                                    style="width: 250px;">
                                </el-input>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" icon="el-icon-search" @click="onSubmitFilter">
                                    搜索
                                </el-button>
                                <el-button icon="el-icon-refresh" @click="clear">
                                    重置
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                </div>
            </el-card>
        </div>

        <!-- 商品列表区域 -->
        <div class="table-section">
            <el-card class="table-card" shadow="never">
                <div class="table-header">
                    <h3 class="table-title">商品列表</h3>
                    <div class="table-actions">
                        <el-button size="small" icon="el-icon-refresh" @click="getList">刷新</el-button>
                    </div>
                </div>
                <el-table
                    :data="tableData"
                    class="modern-table"
                    style="width: 100%"
                    :header-cell-style="{ background: '#fafafa', color: '#606266', fontWeight: '500' }"
                    :row-style="{ height: '70px' }"
                    empty-text="暂无商品数据"
                    v-loading="loading">
                    <el-table-column type="expand">
                        <template slot-scope="props">
                            <el-table :data="props.row.product" style="width: 100%" stripe>
                                <el-table-column prop="id" label="id" width="60"></el-table-column>
                                <el-table-column prop="goods_sn" label="商品SKU" width="140">
                                    <template slot-scope="scope">
                                        <el-input @blur="checkSkuOnly(scope.$index, scope.row)" size="mini"
                                                  v-model="scope.row.goods_sn" placeholder="商品SKU"></el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="goods_aka" label="快递单上的简称" width="160">
                                    <template slot-scope="scope">
                                        <el-input size="mini" v-model="scope.row.goods_name"
                                                  placeholder="简称"></el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="value" label="型号/规格" width="280">
                                    <template slot-scope="scope">
                                        <el-input size="mini" v-model="scope.row.value" placeholder="如1斤/条"></el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="cost" label="成本(元)" width="90">
                                    <template slot-scope="scope">
                                        <el-input size="mini" v-model="scope.row.cost" placeholder="成本"></el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="retail_price" label="零售(元)" width="90">
                                    <template slot-scope="scope">
                                        <el-input size="mini" v-model="scope.row.retail_price"
                                                  placeholder="零售"></el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="goods_weight" label="重量(KG)" width="90">
                                    <template slot-scope="scope">
                                        <el-input size="mini" v-model="scope.row.goods_weight"
                                                  placeholder="重量"></el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="goods_number" label="库存" width="90">
                                    <template slot-scope="scope">
                                        <el-input size="mini" v-model="scope.row.goods_number"
                                                  placeholder="库存"></el-input>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="140">
                                    <template slot-scope="scope">
                                        <el-button
                                                size="mini"
                                                type="danger"
                                                icon="el-icon-check" circle
                                                @click="specSave(scope.$index, scope.row)">
                                        </el-button>
                                        <el-switch
                                                size="mini"
                                                v-model="scope.row.is_on_sale"
                                                active-text=""
                                                inactive-text=""
                                                active-value="1"
                                                inactive-value="0"
                                                @change='changeProductStatus($event,scope.row.id)'>
                                        </el-switch>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </template>
                    </el-table-column>
                    <el-table-column label="商品信息" min-width="300">
                        <template slot-scope="scope">
                            <div class="goods-info">
                                <div class="goods-image-container">
                                    <img :src="scope.row.list_pic_url"
                                         alt="商品图片"
                                         class="goods-image"
                                         @error="handleImageError">
                                    <div class="image-overlay" v-if="!scope.row.is_on_sale">
                                        <span class="offline-tag">已下架</span>
                                    </div>
                                </div>
                                <div class="goods-details">
                                    <div class="goods-name">{{ scope.row.name }}</div>
                                    <div class="goods-meta">
                                        <span class="goods-id">ID: {{ scope.row.id }}</span>
                                        <span class="goods-category">{{ scope.row.category_name || '未分类' }}</span>
                                    </div>
                                    <div class="goods-price">
                                        <span class="price-label">售价：</span>
                                        <span class="price-value">¥{{ scope.row.retail_price }}</span>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="销售数据" width="120" align="center">
                        <template slot-scope="scope">
                            <div class="sales-data">
                                <div class="data-item">
                                    <span class="data-label">销量</span>
                                    <span class="data-value sales">{{ scope.row.sell_volume || 0 }}</span>
                                </div>
                                <div class="data-item">
                                    <span class="data-label">库存</span>
                                    <span class="data-value" :class="getStockClass(scope.row.goods_number)">
                                        {{ scope.row.goods_number || 0 }}
                                    </span>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column label="排序" width="120" align="center">
                        <template slot-scope="scope">
                            <el-input-number
                                v-model="scope.row.sort_order"
                                size="small"
                                :min="1"
                                :max="999"
                                controls-position="right"
                                class="sort-input"
                                @blur="submitSort(scope.$index, scope.row)"
                                @change="submitSort(scope.$index, scope.row)">
                            </el-input-number>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态设置" width="140" align="center">
                        <template slot-scope="scope">
                            <div class="status-controls">
                                <div class="control-item">
                                    <span class="control-label">首页</span>
                                    <el-switch
                                        v-model="scope.row.is_index"
                                        size="small"
                                        active-color="#13ce66"
                                        inactive-color="#dcdfe6"
                                        @change='changeShowStatus($event,scope.row.id)'>
                                    </el-switch>
                                </div>
                                <div class="control-item">
                                    <span class="control-label">上架</span>
                                    <el-switch
                                        v-model="scope.row.is_on_sale"
                                        size="small"
                                        active-color="#409eff"
                                        inactive-color="#dcdfe6"
                                        @change='changeStatus($event,scope.row.id)'>
                                    </el-switch>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column label="操作" width="140" align="center">
                        <template slot-scope="scope">
                            <div class="action-buttons">
                                <el-button
                                    size="small"
                                    type="primary"
                                    icon="el-icon-edit"
                                    class="action-btn"
                                    @click="handleRowEdit(scope.$index, scope.row)">
                                    编辑
                                </el-button>
                                <el-button
                                    size="small"
                                    type="danger"
                                    icon="el-icon-delete"
                                    class="action-btn"
                                    @click="handleRowDelete(scope.$index, scope.row)">
                                    删除
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页区域 -->
                <div class="pagination-section">
                    <el-pagination
                        background
                        @current-change="handlePageChange"
                        :current-page="page"
                        :page-size="size"
                        layout="total, prev, pager, next, jumper"
                        :total="total"
                        class="modern-pagination">
                    </el-pagination>
                </div>
            </el-card>
        </div>
    </div>
</template>

<script>

    export default {
        data() {
            return {
                page: 1,
				size:10,
                total: 0,
                filterForm: {
                    name: ''
                },
                tableData: [],
                activeName: 'second',
                pIndex: 0,
                num: 0,
                activeClass: 0,
                expand:true,
                loading: false,
                onSaleCount: 0,
                lowStockCount: 0,
                offSaleCount: 0,
                tableDa:[{
                    date: '2016-05-02',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1518 弄'
                  }, {
                    date: '2016-05-04',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1517 弄'
                  }, {
                    date: '2016-05-01',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1519 弄'
                  }, {
                    date: '2016-05-03',
                    name: '王小虎',
                    address: '上海市普陀区金沙江路 1516 弄'
                }],
            }
        },
        methods: {
            stockSyc(){
                this.$confirm('确定要同步库存?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.axios.post('crontab/updateStockByHand').then((response) => {
                        if (response.data.errno === 0) {
                            this.$message({
                                type: 'success',
                                message: '同步成功'
                            });
                            this.getOnSaleList();
                        }
                    })
                }).catch(() => {

                });
            },
            updateGoodsNumber(){
                this.axios.post('goods/updateGoodsNumber').then((response) => {
                    if (response.data.errno === 0) {
                        this.$message({
                            type: 'success',
                            message: '同步成功2/2，完成'
                        });
                    }
                })
            },
            specSave(index,row){
                if(row.goods_name == '' || row.value == '' || row.cost == '' || row.retail_price == '' || row.goods_weight == ''){
                    this.$message({
                        type: 'error',
                        message: '值不能为空!'
                    });
                    return false;
                }
                this.axios.post('goods/updatePrice', row).then((response) => {
                    if (response.data.errno === 0) {
                        this.$message({
                            type: 'success',
                            message: '修改成功!'
                        });
                    }
					else if (response.data.errno === 100) {
						this.$message({
						    type: 'error',
						    message: response.data.errmsg
						});
					}
                })

            },
            checkSkuOnly(index,row){
                console.log(index);
                console.log(row);
                if(row.goods_sn == ''){
                    this.$message({
                        type: 'error',
                        message: 'SKU不能为空'
                    })
                    return false;
                }
                this.axios.post('goods/checkSku', {info: row}).then((response) => {
                    if (response.data.errno === 100) {
                        this.$message({
                            type: 'error',
                            message: '该SKU已存在！'
                        })
                    }
                    else{
                        this.$message({
                            type: 'success',
                            message: '该SKU可以用！'
                        })
                    }
                })
            },
            expandToggle(){
                this.expand = !this.expand;
            },
            test(){
                console.log(this.tableData);
            },
            submitName(index, row){
                this.axios.post('goods/updateShortName', { id: row.id,short_name:row.short_name }).then((response) => {
                    if (response.data.errno === 0) {
                        this.$message({
                            type: 'success',
                            message: '修改成功!'
                        });
                    }
                })
            },
            submitSort(index, row){
                this.axios.post('goods/updateSort', { id: row.id,sort:row.sort_order }).then((response) => {})
            },
            handleClick(tab, event) {
                let pindex = tab._data.index;
				this.page = 1;
                this.activeClass = 0;
                if (pindex == 0) {
                    this.getList();
                    this.pIndex = 0;
                }
                else if (pindex == 1) {
                    this.getOnSaleList();
                    this.pIndex = 1;
                }
                else if (pindex == 2) {
                    this.getOutList();
                    this.pIndex = 2;
                }
                else if (pindex == 3) {
                    this.getDropList();
                    this.pIndex = 3;
                }
            },
            handlePageChange(val) {
                this.page = val;
                let nIndex = this.pIndex;
                if (nIndex == 0) {
                    this.getList();
                }
                else if (nIndex == 1) {
                    this.getOnSaleList();
                }
                else if (nIndex == 2) {
                    this.getOutList();
                }
                else if (nIndex == 3) {
                    this.getDropList();
                }
                else if (nIndex == 4) {
                    this.sortOrder(this.num);
                }

            },
            handleRowEdit(index, row) {
                this.$router.push({name: 'goods_add', query: {id: row.id}})
            },
            handleRowDelete(index, row) {

                this.$confirm('确定要删除?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                let that = this;
                that.axios.post('goods/destory', {id: row.id}).then((response) => {
                    if (response.data.errno === 0) {
                        that.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        let pIndex = localStorage.getItem('pIndex');
                        console.log(pIndex);
                        if (pIndex == 0) {
                            that.getList();
                        }
                        else if (pIndex == 1) {
                            that.getOnSaleList();
                        }
                        else if (pIndex == 2) {
                            that.getOutList();
                        }
                        else if (pIndex == 3) {
                            that.getDropList();
                        }
                    }
                })
                }).catch(() => {
//                    this.$message({
//                        type: 'info',
//                        message: '已取消删除'
//                    });
                });
            },
            onSubmitFilter() {
                this.page = 1;
                this.getList();
            },
            clear(){
                this.filterForm.name = '';
                this.page = 1;
                this.getList();
            },

            // 获取库存状态样式类
            getStockClass(stock) {
                if (stock <= 0) return 'stock-empty';
                if (stock <= 10) return 'stock-low';
                return 'stock-normal';
            },

            // 图片加载失败处理
            handleImageError(event) {
                event.target.src = '/static/images/default-goods.png';
            },
            getList() {
                this.axios.get('goods', {
                    params: {
                        page: this.page,
						size: this.size,
                        name: this.filterForm.name
                    }
                }).then((response) => {
                    this.tableData = response.data.data.data
                    this.page = response.data.data.currentPage
                    this.total = response.data.data.count
                })
            },
            getOnSaleList() {
                this.axios.get('goods/onsale', {
                    params: {
                        page: this.page,
						size: this.size
                    }
                }).then((response) => {
                    this.tableData = response.data.data.data
                    this.page = response.data.data.currentPage
                    this.total = response.data.data.count
                })
            },
            getOutList() {
                this.axios.get('goods/out', {
                    params: {
                        page: this.page,
						size: this.size
                    }
                }).then((response) => {
                    this.tableData = response.data.data.data;
                    this.page = response.data.data.currentPage;
                    this.total = response.data.data.count;
                })
            },
            getDropList() {
                this.axios.get('goods/drop', {
                    params: {
                        page: this.page,
						size: this.size
                    }
                }).then((response) => {
                    this.tableData = response.data.data.data;
                    this.page = response.data.data.currentPage;
                    this.total = response.data.data.count;
                })
            },
            sortOrder(num) {
                this.num = num;
                this.pIndex = 4;
                this.activeClass = num;
                this.axios.get('goods/sort', {
                    params: {
                        page: this.page,
						size: this.size,
                        index: num
                    }
                }).then((response) => {
                    this.tableData = response.data.data.data;
                    this.page = response.data.data.currentPage;
                    this.total = response.data.data.count;
                })
            },
            changeStatus($event, para) {
                this.axios.get('goods/saleStatus', {
                    params: {
                        status: $event,
                        id: para
                    }
                }).then((response) => {

                })
            },
            changeProductStatus($event, para) {
                this.axios.get('goods/productStatus', {
                    params: {
                        status: $event,
                        id: para
                    }
                }).then((response) => {

                })
            },
            changeShowStatus($event, para) {
                this.axios.get('goods/indexShowStatus', {
                    params: {
                        status: $event,
                        id: para
                    }
                }).then((response) => {

                })
            }
        },
        components: {},
        mounted() {
            this.getOnSaleList();
        }
    }

</script>

<style scoped>
/* 页面整体布局 */
.goods-management-page {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px;
  border-radius: 12px;
  color: white;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title i {
  font-size: 32px;
}

.page-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 15px;
}

.add-btn {
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 8px;
  font-weight: 500;
}

/* 统计卡片区域 */
.stats-section {
  margin-bottom: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-icon.success {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #fa709a, #fee140);
}

.stat-icon.danger {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20px;
}

.filter-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.filter-header {
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 20px;
}

.sort-buttons {
  display: flex;
  align-items: center;
  gap: 15px;
}

.sort-label {
  font-weight: 500;
  color: #606266;
}

.sort-group {
  display: flex;
  gap: 5px;
}

.filter-content {
  padding: 0 20px 20px 20px;
}

.status-tabs {
  margin-bottom: 20px;
}

.status-tabs ::v-deep .el-tabs__header {
  margin: 0;
}

.status-tabs ::v-deep .el-tabs__item {
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
  font-weight: 500;
}

.search-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-form {
  margin: 0;
}

/* 表格区域 */
.table-section {
  margin-bottom: 20px;
}

.table-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0 20px;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 10px;
}

/* 现代化表格样式 */
.modern-table {
  margin-top: 20px;
}

.modern-table ::v-deep .el-table__header-wrapper {
  border-radius: 8px 8px 0 0;
}

.modern-table ::v-deep .el-table__row {
  transition: all 0.3s ease;
}

.modern-table ::v-deep .el-table__row:hover {
  background-color: #f8f9ff !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 商品信息样式 */
.goods-info {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px 0;
}

.goods-image-container {
  position: relative;
  flex-shrink: 0;
}

.goods-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
}

.goods-image:hover {
  border-color: #409eff;
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.offline-tag {
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.goods-details {
  flex: 1;
  min-width: 0;
}

.goods-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.goods-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 6px;
}

.goods-id {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  color: #909399;
  background: #f4f4f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.goods-category {
  font-size: 12px;
  color: #606266;
  background: #e1f3ff;
  padding: 2px 6px;
  border-radius: 4px;
}

.goods-price {
  display: flex;
  align-items: center;
  gap: 4px;
}

.price-label {
  font-size: 12px;
  color: #909399;
}

.price-value {
  font-size: 16px;
  font-weight: 600;
  color: #e6a23c;
}

/* 销售数据样式 */
.sales-data {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.data-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.data-label {
  font-size: 12px;
  color: #909399;
}

.data-value {
  font-size: 16px;
  font-weight: 600;
}

.data-value.sales {
  color: #67c23a;
}

.data-value.stock-normal {
  color: #67c23a;
}

.data-value.stock-low {
  color: #e6a23c;
}

.data-value.stock-empty {
  color: #f56c6c;
}

/* 排序输入框 */
.sort-input {
  width: 80px;
}

.sort-input ::v-deep .el-input__inner {
  text-align: center;
}

/* 状态控制样式 */
.status-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.control-label {
  font-size: 12px;
  color: #606266;
  min-width: 30px;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.action-btn {
  border-radius: 6px;
  font-weight: 500;
  min-width: 60px;
}

/* 分页区域 */
.pagination-section {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
  margin-top: 20px;
}

.modern-pagination {
  display: flex;
  justify-content: center;
}

.modern-pagination ::v-deep .el-pager li {
  border-radius: 6px;
  margin: 0 2px;
}

.modern-pagination ::v-deep .btn-prev,
.modern-pagination ::v-deep .btn-next {
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .goods-management-page {
    padding: 10px;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .goods-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .filter-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .search-form {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .action-buttons {
    flex-direction: row;
  }
}

/* 动画效果 */
.filter-card,
.table-card,
.stat-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.modern-table ::v-deep .el-loading-mask {
  border-radius: 8px;
}
</style>
