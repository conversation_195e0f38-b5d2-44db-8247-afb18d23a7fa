const mysql = require('mysql2/promise');

async function createTable() {
  try {
    const connection = await mysql.createConnection({
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: '19841020',
      database: 'hiolabsDB'
    });

    console.log('✅ 数据库连接成功');

    // 创建活动配置表
    const createCampaignSQL = `
CREATE TABLE IF NOT EXISTS hiolabs_flash_sale_campaigns (
  id int(11) NOT NULL AUTO_INCREMENT COMMENT '活动配置ID',
  name varchar(100) NOT NULL COMMENT '活动名称',
  goods_id int(11) NOT NULL COMMENT '商品ID',
  flash_price decimal(10,2) NOT NULL COMMENT '秒杀价格',
  original_price decimal(10,2) NOT NULL COMMENT '原价',
  total_stock int(11) NOT NULL COMMENT '总库存',
  stock_per_round int(11) NOT NULL COMMENT '每轮库存',
  limit_quantity int(11) DEFAULT 1 COMMENT '限购数量',
  start_date date NOT NULL COMMENT '开始日期',
  end_date date NOT NULL COMMENT '结束日期',
  daily_start_time time NOT NULL DEFAULT '09:00:00' COMMENT '每日开始时间',
  daily_end_time time NOT NULL DEFAULT '22:00:00' COMMENT '每日结束时间',
  round_duration int(11) NOT NULL DEFAULT 300 COMMENT '每轮时长（秒）',
  break_duration int(11) NOT NULL DEFAULT 0 COMMENT '轮次间隔（秒）',
  status enum('draft','active','paused','ended') DEFAULT 'draft' COMMENT '状态',
  auto_start tinyint(1) DEFAULT 1 COMMENT '是否自动开始',
  created_at datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀活动配置表'
`;

    await connection.execute(createCampaignSQL);
    console.log('✅ 活动配置表创建成功');

    // 创建轮次表
    const createRoundsSQL = `
CREATE TABLE IF NOT EXISTS hiolabs_flash_sale_rounds (
  id int(11) NOT NULL AUTO_INCREMENT COMMENT '轮次ID',
  campaign_id int(11) NOT NULL COMMENT '活动配置ID',
  round_number int(11) NOT NULL COMMENT '轮次编号（从1开始）',
  start_time datetime NOT NULL COMMENT '开始时间',
  end_time datetime NOT NULL COMMENT '结束时间',
  stock int(11) NOT NULL COMMENT '本轮库存',
  sold_count int(11) DEFAULT 0 COMMENT '已售数量',
  status enum('upcoming','active','ended','cancelled') DEFAULT 'upcoming' COMMENT '状态',
  created_at datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  UNIQUE KEY uk_campaign_round (campaign_id, round_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀轮次表'
`;

    await connection.execute(createRoundsSQL);
    console.log('✅ 轮次表创建成功');

    // 插入示例数据
    const insertCampaignSQL = `
INSERT INTO hiolabs_flash_sale_campaigns (
  name, goods_id, flash_price, original_price, total_stock, stock_per_round,
  limit_quantity, start_date, end_date, daily_start_time, daily_end_time,
  round_duration, break_duration, status
) VALUES 
('iPhone 15 Pro 限时秒杀', 1, 6999.00, 8999.00, 100, 5, 1, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 7 DAY), '09:00:00', '22:00:00', 300, 0, 'active'),
('小米14 Ultra 疯狂秒杀', 2, 4999.00, 5999.00, 200, 10, 2, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 3 DAY), '10:00:00', '21:00:00', 300, 60, 'active')
`;

    await connection.execute(insertCampaignSQL);
    console.log('✅ 示例数据插入成功');

    await connection.end();
    console.log('✅ 数据库连接已关闭');
    
  } catch (error) {
    console.error('❌ 创建失败:', error.message);
  }
}

createTable();
