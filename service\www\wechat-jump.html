<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单兑换 - 美汐缘小程序</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .card {
            background: white;
            border-radius: 24px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #ffa726, #ff6b6b);
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 40px;
        }
        
        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 8px;
            font-weight: 700;
        }
        
        .subtitle {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        
        .jump-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            color: white;
            border: none;
            padding: 16px 40px;
            border-radius: 50px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
        }
        
        .jump-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(255, 107, 107, 0.3);
        }
        
        .jump-btn:active {
            transform: translateY(0);
        }
        
        .secondary-btn {
            background: #f8f9fa;
            color: #666;
            border: 2px solid #e9ecef;
            padding: 12px 30px;
            border-radius: 50px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .secondary-btn:hover {
            background: #e9ecef;
            color: #333;
        }
        
        .tips {
            margin-top: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 16px;
            font-size: 14px;
            color: #666;
            line-height: 1.6;
            text-align: left;
        }
        
        .tips-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .loading {
            display: none;
            margin: 20px 0;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #ff6b6b;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .wechat-tag {
            width: 100%;
            height: 50px;
            border-radius: 25px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            color: white;
            border: none;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
        }
        
        @media (max-width: 480px) {
            .card {
                margin: 10px;
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            .jump-btn {
                font-size: 16px;
                padding: 14px 30px;
            }
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="logo">🎁</div>
        <h1>订单有礼</h1>
        <p class="subtitle">输入订单号兑换积分<br>更多惊喜等你发现</p>
        
        <!-- 微信开放标签（在微信中显示） -->
        <div id="wechat-container" style="display: none;">
            <wx-open-launch-weapp 
                id="launch-btn" 
                username="gh_your_username" 
                path="pages/order-exchange/index">
                <template>
                    <button class="wechat-tag">立即打开小程序</button>
                </template>
            </wx-open-launch-weapp>
        </div>
        
        <!-- 普通按钮（在非微信环境显示） -->
        <div id="normal-container">
            <button class="jump-btn" onclick="openMiniProgram()">
                <span id="btn-text">打开小程序</span>
            </button>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在跳转到小程序...</p>
            </div>
            
            <button class="secondary-btn" onclick="copyLink()">复制链接</button>
            <button class="secondary-btn" onclick="showQRCode()">显示二维码</button>
        </div>
        
        <div class="tips">
            <div class="tips-title">💡 使用提示</div>
            • 在微信中打开可直接跳转小程序<br>
            • 其他浏览器请复制链接到微信打开<br>
            • 支持通过二维码扫码进入
        </div>
    </div>

    <!-- 微信JS-SDK -->
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    
    <script>
        // 获取URL参数
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                orderNumber: params.get('orderNumber') || '',
                source: params.get('source') || 'web',
                utm: params.get('utm') || ''
            };
        }

        // 检测环境
        function isWechat() {
            return /micromessenger/i.test(navigator.userAgent);
        }

        function isMobile() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }

        // 生成URL Scheme
        function generateScheme() {
            const params = getUrlParams();
            const appid = 'wx919ca2ec612e6ecb';
            const path = 'pages/order-exchange/index';
            
            let query = '';
            Object.keys(params).forEach(key => {
                if (params[key]) {
                    query += query ? '&' : '';
                    query += `${key}=${encodeURIComponent(params[key])}`;
                }
            });
            
            return `weixin://dl/business/?appid=${appid}&path=${path}&query=${encodeURIComponent(query)}&env_version=release`;
        }

        // 打开小程序
        function openMiniProgram() {
            const loading = document.getElementById('loading');
            const btnText = document.getElementById('btn-text');
            
            loading.style.display = 'block';
            btnText.textContent = '跳转中...';
            
            const scheme = generateScheme();
            
            if (isWechat()) {
                // 在微信中，尝试使用URL Scheme
                window.location.href = scheme;
            } else {
                // 在外部浏览器
                if (isMobile()) {
                    window.location.href = scheme;
                    
                    setTimeout(() => {
                        loading.style.display = 'none';
                        btnText.textContent = '打开小程序';
                        alert('请在微信中打开此链接');
                    }, 3000);
                } else {
                    loading.style.display = 'none';
                    btnText.textContent = '打开小程序';
                    showQRCode();
                }
            }
        }

        // 复制链接
        function copyLink() {
            const scheme = generateScheme();
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(scheme).then(() => {
                    alert('链接已复制！请在微信中粘贴打开');
                });
            } else {
                prompt('请复制以下链接到微信打开:', scheme);
            }
        }

        // 显示二维码
        function showQRCode() {
            const currentUrl = window.location.href;
            const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(currentUrl)}`;
            
            const qrWindow = window.open('', '_blank', 'width=300,height=350');
            qrWindow.document.write(`
                <html>
                    <head><title>扫码打开小程序</title></head>
                    <body style="text-align:center;padding:20px;font-family:Arial;">
                        <h3>扫码打开小程序</h3>
                        <img src="${qrUrl}" alt="二维码" style="border:1px solid #ddd;"/>
                        <p style="color:#666;font-size:14px;">请使用微信扫描二维码</p>
                    </body>
                </html>
            `);
        }

        // 初始化
        window.onload = function() {
            const params = getUrlParams();
            
            if (isWechat()) {
                // 在微信中，显示开放标签容器
                document.getElementById('wechat-container').style.display = 'block';
                document.getElementById('normal-container').style.display = 'none';
                
                // 配置微信JS-SDK（需要后端签名）
                // wx.config({...});
                
                // 设置开放标签参数
                const launchBtn = document.getElementById('launch-btn');
                if (launchBtn && params.orderNumber) {
                    launchBtn.setAttribute('path', `pages/order-exchange/index?orderNumber=${params.orderNumber}`);
                }
            } else {
                // 在非微信环境，显示普通按钮
                document.getElementById('wechat-container').style.display = 'none';
                document.getElementById('normal-container').style.display = 'block';
            }
            
            // 显示参数信息（调试用）
            console.log('页面参数:', params);
        };
    </script>
</body>
</html>
