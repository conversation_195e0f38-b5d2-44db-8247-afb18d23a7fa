{"version": 3, "sources": ["..\\..\\..\\src\\api\\model\\order_express.js"], "names": ["module", "exports", "think", "Model", "tableName", "tablePrefix", "getLatestOrderExpress", "orderId", "returnExpressInfo", "shipper_code", "shipper_name", "logistic_code", "is_finish", "request_time", "traces", "orderExpress", "where", "order_id", "find", "isEmpty", "datetime", "JSON", "parse", "ExpressSerivce", "service", "latestExpressInfo", "queryExpress", "nowTime", "Number", "parseInt", "Date", "now", "updateData", "update_time", "request_count", "success", "is<PERSON><PERSON><PERSON>", "stringify", "id", "update"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;AACvC,QAAIC,SAAJ,GAAgB;AACZ,eAAO,KAAKC,WAAL,GAAmB,eAA1B;AACH;AACD;;;;;AAKMC,yBAAN,CAA4BC,OAA5B,EAAqC;AAAA;;AAAA;AACjC,kBAAMC,oBAAoB;AACtBC,8BAAc,EADQ;AAEtBC,8BAAc,EAFQ;AAGtBC,+BAAe,EAHO;AAItBC,2BAAW,CAJW;AAKtBC,8BAAc,CALQ;AAMtBC,wBAAQ;AANc,aAA1B;AAQA,kBAAMC,eAAe,MAAM,MAAKC,KAAL,CAAW;AAClCC,0BAAUV;AADwB,aAAX,EAExBW,IAFwB,EAA3B,CATiC,CAWtB;AACX,gBAAIhB,MAAMiB,OAAN,CAAcJ,YAAd,CAAJ,EAAiC;AAAE;AAC/B,uBAAOP,iBAAP;AACH;AACD,gBAAIN,MAAMiB,OAAN,CAAcJ,aAAaN,YAA3B,KAA4CP,MAAMiB,OAAN,CAAcJ,aAAaJ,aAA3B,CAAhD,EAA2F;AACvF,uBAAOH,iBAAP,CADuF,CAC7D;AAC7B;AACD;AACAA,8BAAkBC,YAAlB,GAAiCM,aAAaN,YAA9C;AACAD,8BAAkBE,YAAlB,GAAiCK,aAAaL,YAA9C;AACAF,8BAAkBG,aAAlB,GAAkCI,aAAaJ,aAA/C;AACAH,8BAAkBI,SAAlB,GAA8BG,aAAaH,SAA3C;AACAJ,8BAAkBK,YAAlB,GAAiCX,MAAMkB,QAAN,CAAeL,aAAaF,YAAb,GAA4B,IAA3C,CAAjC;AACAL,8BAAkBM,MAAlB,GAA2BZ,MAAMiB,OAAN,CAAcJ,aAAaD,MAA3B,IAAqC,EAArC,GAA0CO,KAAKC,KAAL,CAAWP,aAAaD,MAAxB,CAArE;AACA;AACA,gBAAIC,aAAaH,SAAjB,EAA4B;AACxB,uBAAOJ,iBAAP;AACH;AACD;AACA,kBAAMe,iBAAiBrB,MAAMsB,OAAN,CAAc,SAAd,EAAyB,KAAzB,CAAvB,CA9BiC,CA8BuB;AACxD;AACA,kBAAMC,oBAAoB,MAAMF,eAAeG,YAAf,CAA4BX,aAAaN,YAAzC,EAAuDM,aAAaJ,aAApE,CAAhC;AACA,kBAAMgB,UAAUC,OAAOC,QAAP,CAAgBC,KAAKC,GAAL,KAAa,IAA7B,CAAhB;AACA,kBAAMC,aAAa;AACfnB,8BAAcc,OADC;AAEfM,6BAAaN,OAFE;AAGfO,+BAAe,CAAC,KAAD,EAAQ,iBAAR;AAHA,aAAnB;AAKA,gBAAIT,kBAAkBU,OAAtB,EAA+B;AAC3B3B,kCAAkBM,MAAlB,GAA2BW,kBAAkBX,MAA7C;AACAN,kCAAkBI,SAAlB,GAA8Ba,kBAAkBW,QAAhD;AACA;AACAJ,2BAAWlB,MAAX,GAAoBO,KAAKgB,SAAL,CAAeZ,kBAAkBX,MAAjC,CAApB;AACAN,kCAAkBK,YAAlB,GAAiCX,MAAMkB,QAAN,CAAeO,UAAU,IAAzB,CAAjC;AACAK,2BAAWpB,SAAX,GAAuBa,kBAAkBW,QAAzC;AACH;AACD,kBAAM,MAAKpB,KAAL,CAAW;AACbsB,oBAAIvB,aAAauB;AADJ,aAAX,EAEHC,MAFG,CAEIP,UAFJ,CAAN;AAGA,mBAAOxB,iBAAP;AAlDiC;AAmDpC;AA5DsC,CAA3C", "file": "..\\..\\..\\src\\api\\model\\order_express.js", "sourcesContent": ["module.exports = class extends think.Model {\n    get tableName() {\n        return this.tablePrefix + 'order_express';\n    }\n    /**\n     * 获取最新的订单物流信息\n     * @param orderId\n     * @returns {Promise.<*>}\n     */\n    async getLatestOrderExpress(orderId) {\n        const returnExpressInfo = {\n            shipper_code: '',\n            shipper_name: '',\n            logistic_code: '',\n            is_finish: 0,\n            request_time: 0,\n            traces: []\n        };\n        const orderExpress = await this.where({\n            order_id: orderId\n        }).find(); // 根据orderid得到order_express的info\n        if (think.isEmpty(orderExpress)) { // 如果是空的，说明还没发货\n            return returnExpressInfo;\n        }\n        if (think.isEmpty(orderExpress.shipper_code) || think.isEmpty(orderExpress.logistic_code)) {\n            return returnExpressInfo; // 如果是空的，说明还没发货\n        }\n        // 如果不空，则将里面的登录的快递号等信息复制给returnExpressInfo\n        returnExpressInfo.shipper_code = orderExpress.shipper_code;\n        returnExpressInfo.shipper_name = orderExpress.shipper_name;\n        returnExpressInfo.logistic_code = orderExpress.logistic_code;\n        returnExpressInfo.is_finish = orderExpress.is_finish;\n        returnExpressInfo.request_time = think.datetime(orderExpress.request_time * 1000);\n        returnExpressInfo.traces = think.isEmpty(orderExpress.traces) ? [] : JSON.parse(orderExpress.traces);\n        // 如果物流配送已完成，直接返回\n        if (orderExpress.is_finish) {\n            return returnExpressInfo;\n        }\n        // 查询最新物流信息\n        const ExpressSerivce = think.service('express', 'api'); // 引入api\n        // 调用api里的queryExpress方法 最重要\n        const latestExpressInfo = await ExpressSerivce.queryExpress(orderExpress.shipper_code, orderExpress.logistic_code);\n        const nowTime = Number.parseInt(Date.now() / 1000);\n        const updateData = {\n            request_time: nowTime,\n            update_time: nowTime,\n            request_count: ['EXP', 'request_count+1']\n        };\n        if (latestExpressInfo.success) {\n            returnExpressInfo.traces = latestExpressInfo.traces;\n            returnExpressInfo.is_finish = latestExpressInfo.isFinish;\n            // 查询成功则更新订单物流信息\n            updateData.traces = JSON.stringify(latestExpressInfo.traces);\n            returnExpressInfo.request_time = think.datetime(nowTime * 1000);\n            updateData.is_finish = latestExpressInfo.isFinish;\n        }\n        await this.where({\n            id: orderExpress.id\n        }).update(updateData);\n        return returnExpressInfo;\n    }\n};"]}