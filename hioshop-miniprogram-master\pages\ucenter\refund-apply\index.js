var util = require('../../../utils/util.js');
var api = require('../../../config/api.js');

Page({
    data: {
        orderId: 0,
        orderInfo: {},
        orderGoods: [],
        refundType: 'refund_only', // refund_only: 仅退款, return_refund: 退货退款
        refundAmount: 0,
        maxRefundAmount: 0,
        refundReason: '',
        refundReasons: [
            '不想要了',
            '商品有质量问题',
            '商品与描述不符',
            '发错货了',
            '商品破损',
            '其他原因'
        ],
        reasonIndex: -1,
        refundDesc: '',
        uploadedImages: [],
        maxImages: 3
    },

    onLoad: function (options) {
        if (options.orderId) {
            this.setData({
                orderId: options.orderId
            });
            this.getOrderDetail();
        }
    },

    // 获取订单详情
    getOrderDetail: function () {
        let that = this;
        util.request(api.OrderDetail, {
            orderId: that.data.orderId
        }).then(function (res) {
            if (res.errno === 0) {
                that.setData({
                    orderInfo: res.data.orderInfo,
                    orderGoods: res.data.orderGoods,
                    maxRefundAmount: parseFloat(res.data.orderInfo.actual_price),
                    refundAmount: parseFloat(res.data.orderInfo.actual_price)
                });
            }
        });
    },

    // 选择退款类型
    onRefundTypeChange: function (e) {
        const refundType = e.currentTarget.dataset.value;
        this.setData({
            refundType: refundType
        });
    },

    // 显示退款原因选择器
    showReasonPicker: function() {
        const that = this;
        wx.showActionSheet({
            itemList: this.data.refundReasons,
            success: function(res) {
                that.setData({
                    reasonIndex: res.tapIndex,
                    refundReason: that.data.refundReasons[res.tapIndex]
                });
            }
        });
    },

    // 输入退款金额
    onRefundAmountInput: function (e) {
        let amount = parseFloat(e.detail.value) || 0;
        if (amount > this.data.maxRefundAmount) {
            amount = this.data.maxRefundAmount;
            wx.showToast({
                title: '退款金额不能超过订单金额',
                icon: 'none'
            });
        }
        this.setData({
            refundAmount: amount
        });
    },

    // 输入退款说明
    onRefundDescInput: function (e) {
        this.setData({
            refundDesc: e.detail.value
        });
    },

    // 选择图片
    chooseImage: function () {
        let that = this;
        let remainCount = this.data.maxImages - this.data.uploadedImages.length;
        
        if (remainCount <= 0) {
            wx.showToast({
                title: '最多只能上传' + this.data.maxImages + '张图片',
                icon: 'none'
            });
            return;
        }

        wx.chooseImage({
            count: remainCount,
            sizeType: ['compressed'],
            sourceType: ['album', 'camera'],
            success: function (res) {
                let tempFilePaths = res.tempFilePaths;
                let uploadedImages = that.data.uploadedImages.concat(tempFilePaths);
                that.setData({
                    uploadedImages: uploadedImages
                });
            }
        });
    },

    // 删除图片
    deleteImage: function (e) {
        let index = e.currentTarget.dataset.index;
        let uploadedImages = this.data.uploadedImages;
        uploadedImages.splice(index, 1);
        this.setData({
            uploadedImages: uploadedImages
        });
    },

    // 预览图片
    previewImage: function (e) {
        let current = e.currentTarget.dataset.src;
        wx.previewImage({
            current: current,
            urls: this.data.uploadedImages
        });
    },

    // 提交售后申请
    submitRefund: function () {
        // 验证表单
        if (!this.data.refundReason) {
            wx.showToast({
                title: '请选择退款原因',
                icon: 'none'
            });
            return;
        }

        if (this.data.refundAmount <= 0) {
            wx.showToast({
                title: '请输入正确的退款金额',
                icon: 'none'
            });
            return;
        }

        // 构建提交数据
        let submitData = {
            orderId: this.data.orderId,
            refundType: this.data.refundType,
            refundAmount: this.data.refundAmount,
            refundReason: this.data.refundReason,
            refundDesc: this.data.refundDesc,
            images: this.data.uploadedImages
        };

        wx.showLoading({
            title: '提交中...'
        });

        // 提交售后申请
        util.request(api.RefundApply, submitData, 'POST').then(res => {
            wx.hideLoading();
            if (res.errno === 0) {
                wx.showToast({
                    title: '申请提交成功',
                    icon: 'success'
                });
                setTimeout(() => {
                    wx.navigateBack();
                }, 1500);
            } else {
                wx.showToast({
                    title: res.errmsg || '提交失败',
                    icon: 'none'
                });
            }
        }).catch(err => {
            wx.hideLoading();
            wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
            });
        });
    }
});
