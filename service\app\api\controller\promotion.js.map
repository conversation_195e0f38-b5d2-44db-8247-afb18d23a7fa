{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\promotion.js"], "names": ["Base", "require", "module", "exports", "recordVisitAction", "goodsId", "post", "promoterUserId", "shareSource", "console", "log", "fail", "visitorUserId", "getLoginUserId", "error", "currentTime", "parseInt", "Date", "getTime", "promoter", "model", "where", "user_id", "find", "think", "isEmpty", "promoterData", "level", "status", "first_share_time", "create_time", "update_time", "promoterId", "add", "id", "yesterday", "existingRecord", "promoter_user_id", "visitor_user_id", "goods_id", "visit_time", "ip<PERSON><PERSON><PERSON>", "ctx", "ip", "ip_address", "success", "message", "isNewVisitor", "visitRecord", "promoter_id", "share_source", "user_agent", "header", "is_new_visitor", "recordId", "updatePromoterStats", "type", "updateData", "total_views", "month_views", "total_orders", "month_orders", "update", "recordsAction", "userId", "page", "get", "limit", "records", "alias", "join", "table", "as", "on", "field", "order", "countSelect", "data", "total", "count", "statsAction", "visits", "orders", "commission", "today", "todayStart", "setHours", "todayVisits", "todayOrders", "todayCommission", "sum", "parseFloat", "total_commission", "recordOrderAction", "orderId", "orderAmount", "commissionService", "service", "result", "addShareCommission", "updatePromoterOrderStats", "checkPromoterLevelUp", "commissionDetails", "currentMonth", "getMonth", "currentYear", "getFullYear", "newTotalOrders", "promoterUpdateTime", "isCurrentMonth", "newMonthOrders", "shareRecordsAction", "pageSize", "startDate", "endDate", "actionType", "whereCondition", "startTime", "endTime", "sql", "offset", "limitSql", "query", "countSql", "count<PERSON><PERSON><PERSON>", "formattedRecords", "map", "record", "visitorNickname", "visitor_nickname", "goodsName", "goods_name", "goodsImage", "goods_image", "visitTime", "visitTimeText", "formatTime", "userAction", "user_action", "userActionText", "orderInfo", "order_id", "order_amount", "commissionAmount", "commission_amount", "orderStatus", "order_status", "statsResult", "getShareRecordsStats", "pagination", "totalPages", "Math", "ceil", "stats", "statsSql", "totalVisits", "total_visits", "uniqueVisitors", "unique_visitors", "totalOrders", "browsedOnly", "totalOrderAmount", "total_order_amount", "totalCommission", "conversionRate", "conversion_rate", "dataCenterAction", "timeFilter", "timeRange", "getTimeRange", "promotionData", "currentData", "teamData", "rankingList", "recentOrders", "Promise", "all", "getPromotionOverview", "getCurrentPeriodData", "getTeamData", "getRankingData", "getRecentOrders", "timePeriodText", "getTimePeriodText", "commissionInfo", "orderStats", "teamCount", "getTeamMemberCount", "totalAmount", "total_amount", "toFixed", "start", "end", "visitStats", "amount", "visitors", "directCount", "parent_promoter_user_id", "totalCount", "teamOrderStats", "getTeamOrderStats", "teamOrders", "teamCommission", "getCurrentMonthRange", "group", "select", "item", "nickname", "decodeNickname", "avatar", "orderCount", "order_count", "createTime", "timestamp", "date", "year", "month", "String", "padStart", "day", "getDate", "hour", "getHours", "minute", "getMinutes", "now", "weekStart", "setDate", "getDay", "textMap", "directMembers", "length", "member", "subCount", "teamMemberIds", "getAllTeamMemberIds", "visited", "Set", "has", "allMemberIds", "m", "subMemberIds", "concat", "<PERSON><PERSON><PERSON>", "from", "toString"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;;AAEAC,OAAOC,OAAP,GAAiB,cAAcH,IAAd,CAAmB;;AAEhC;;;;AAIMI,qBAAN,GAA0B;AAAA;;AAAA;AACtB,gBAAI;AACA,sBAAMC,UAAU,MAAKC,IAAL,CAAU,SAAV,CAAhB;AACA,sBAAMC,iBAAiB,MAAKD,IAAL,CAAU,gBAAV,CAAvB;AACA,sBAAME,cAAc,MAAKF,IAAL,CAAU,aAAV,KAA4B,aAAhD;;AAEAG,wBAAQC,GAAR,CAAY,gBAAZ;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBL,OAArB;AACAI,wBAAQC,GAAR,CAAY,UAAZ,EAAwBH,cAAxB;AACAE,wBAAQC,GAAR,CAAY,OAAZ,EAAqBF,WAArB;;AAEA,oBAAI,CAACH,OAAD,IAAY,CAACE,cAAjB,EAAiC;AAC7B,2BAAO,MAAKI,IAAL,CAAU,GAAV,EAAe,OAAf,CAAP;AACH;;AAED;AACA,oBAAIC,gBAAgB,CAApB;AACA,oBAAI;AACAA,oCAAgB,MAAM,MAAKC,cAAL,EAAtB;AACH,iBAFD,CAEE,OAAOC,KAAP,EAAc;AACZ;AACAL,4BAAQC,GAAR,CAAY,cAAZ;AACH;;AAED;AACAD,wBAAQC,GAAR,CAAY,eAAZ,EAA6BE,aAA7B,EAA4C,QAA5C,EAAsDL,cAAtD;;AAEA,sBAAMQ,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;;AAEA;AACA,oBAAIC,WAAW,MAAM,MAAKC,KAAL,CAAW,4BAAX,EAAyCC,KAAzC,CAA+C;AAChEC,6BAASf;AADuD,iBAA/C,EAElBgB,IAFkB,EAArB;;AAIA,oBAAIC,MAAMC,OAAN,CAAcN,QAAd,CAAJ,EAA6B;AACzB;AACA,0BAAMO,eAAe;AACjBJ,iCAASf,cADQ;AAEjBoB,+BAAO,CAFU;AAGjBC,gCAAQ,CAHS;AAIjBC,0CAAkBd,WAJD;AAKjBe,qCAAaf,WALI;AAMjBgB,qCAAahB;AANI,qBAArB;AAQA,0BAAMiB,aAAa,MAAM,MAAKZ,KAAL,CAAW,4BAAX,EAAyCa,GAAzC,CAA6CP,YAA7C,CAAzB;AACAP,+CAAae,IAAIF,UAAjB,IAAgCN,YAAhC;AACAjB,4BAAQC,GAAR,CAAY,WAAZ,EAAyBsB,UAAzB;AACH;;AAED;AACA,sBAAMG,YAAYpB,cAAc,KAAhC;AACA,oBAAIqB,iBAAiB,IAArB;;AAEA,oBAAIxB,gBAAgB,CAApB,EAAuB;AACnBwB,qCAAiB,MAAM,MAAKhB,KAAL,CAAW,0BAAX,EAAuCC,KAAvC,CAA6C;AAChEgB,0CAAkB9B,cAD8C;AAEhE+B,yCAAiB1B,aAF+C;AAGhE2B,kCAAUlC,OAHsD;AAIhEmC,oCAAY,CAAC,GAAD,EAAML,SAAN;AAJoD,qBAA7C,EAKpBZ,IALoB,EAAvB;AAMH,iBAPD,MAOO;AACH;AACA,0BAAMkB,YAAY,MAAKC,GAAL,CAASC,EAAT,IAAe,EAAjC;AACAP,qCAAiB,MAAM,MAAKhB,KAAL,CAAW,0BAAX,EAAuCC,KAAvC,CAA6C;AAChEgB,0CAAkB9B,cAD8C;AAEhEgC,kCAAUlC,OAFsD;AAGhEuC,oCAAYH,SAHoD;AAIhEH,yCAAiB,CAJ+C;AAKhEE,oCAAY,CAAC,GAAD,EAAML,SAAN;AALoD,qBAA7C,EAMpBZ,IANoB,EAAvB;AAOH;;AAED,oBAAI,CAACC,MAAMC,OAAN,CAAcW,cAAd,CAAL,EAAoC;AAChC3B,4BAAQC,GAAR,CAAY,mBAAZ;AACA,2BAAO,MAAKmC,OAAL,CAAa,EAAEC,SAAS,QAAX,EAAb,CAAP;AACH;;AAED;AACA,sBAAMC,eAAenC,gBAAgB,CAAhB,GACjBY,MAAMC,OAAN,EAAc,MAAM,MAAKL,KAAL,CAAW,0BAAX,EAAuCC,KAAvC,CAA6C;AAC7DgB,sCAAkB9B,cAD2C;AAE7D+B,qCAAiB1B,aAF4C;AAG7D4B,gCAAY,CAAC,GAAD,EAAML,SAAN;AAHiD,iBAA7C,EAIjBZ,IAJiB,EAApB,EADiB,GAKJ,CALjB;;AAOA;AACA,sBAAMyB,cAAc;AAChBC,iCAAa9B,SAASe,EADN;AAEhBG,sCAAkB9B,cAFF;AAGhB+B,qCAAiB1B,aAHD;AAIhB2B,8BAAUlC,OAJM;AAKhB6C,kCAAc1C,WALE;AAMhBgC,gCAAYzB,WANI;AAOhB6B,gCAAY,MAAKF,GAAL,CAASC,EAAT,IAAe,EAPX;AAQhBQ,gCAAY,MAAKT,GAAL,CAASU,MAAT,CAAgB,YAAhB,KAAiC,EAR7B;AAShBC,oCAAgBN,eAAe,CAAf,GAAmB;AATnB,iBAApB;;AAYA,sBAAMO,WAAW,MAAM,MAAKlC,KAAL,CAAW,0BAAX,EAAuCa,GAAvC,CAA2Ce,WAA3C,CAAvB;;AAEA,oBAAIM,QAAJ,EAAc;AACV7C,4BAAQC,GAAR,CAAY,gBAAZ,EAA8B4C,QAA9B;;AAEA;AACA,0BAAM,MAAKC,mBAAL,CAAyBpC,SAASe,EAAlC,EAAsC,OAAtC,CAAN;;AAEA,2BAAO,MAAKW,OAAL,CAAa;AAChBC,iCAAS,QADO;AAEhBQ,kCAAUA;AAFM,qBAAb,CAAP;AAIH,iBAVD,MAUO;AACH,2BAAO,MAAK3C,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACH;AAEJ,aAjHD,CAiHE,OAAOG,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,MAAKH,IAAL,CAAU,GAAV,EAAe,QAAf,CAAP;AACH;AArHqB;AAsHzB;;AAED;;;;;AAKM4C,uBAAN,CAA0BvB,UAA1B,EAAsCwB,OAAO,OAA7C,EAAsD;AAAA;;AAAA;AAClD,gBAAI;AACA,sBAAMzC,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;;AAEA;AACA,oBAAIuC,aAAa,EAAE1B,aAAahB,WAAf,EAAjB;;AAEA,oBAAIyC,SAAS,OAAb,EAAsB;AAClBC,+BAAWC,WAAX,GAAyB,CAAC,KAAD,EAAQ,iBAAR,CAAzB;AACAD,+BAAWE,WAAX,GAAyB,CAAC,KAAD,EAAQ,iBAAR,CAAzB;AACH,iBAHD,MAGO,IAAIH,SAAS,OAAb,EAAsB;AACzBC,+BAAWG,YAAX,GAA0B,CAAC,KAAD,EAAQ,kBAAR,CAA1B;AACAH,+BAAWI,YAAX,GAA0B,CAAC,KAAD,EAAQ,kBAAR,CAA1B;AACH;;AAED,sBAAM,OAAKzC,KAAL,CAAW,4BAAX,EAAyCC,KAAzC,CAA+C;AACjDa,wBAAIF;AAD6C,iBAA/C,EAEH8B,MAFG,CAEIL,UAFJ,CAAN;;AAIAhD,wBAAQC,GAAR,CAAY,WAAZ;AAEH,aApBD,CAoBE,OAAOI,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,YAAd,EAA4BA,KAA5B;AACH;AAvBiD;AAwBrD;;AAED;;;;AAIMiD,iBAAN,GAAsB;AAAA;;AAAA;AAClB,gBAAI;AACA,sBAAMC,SAAS,MAAM,OAAKnD,cAAL,EAArB;AACA,sBAAMoD,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,sBAAMC,QAAQ,OAAKD,GAAL,CAAS,OAAT,KAAqB,EAAnC;;AAEA;AACA,sBAAME,UAAU,MAAM,OAAKhD,KAAL,CAAW,0BAAX,EACjBiD,KADiB,CACX,IADW,EAEjBC,IAFiB,CAEZ;AACFC,2BAAO,eADL;AAEFD,0BAAM,MAFJ;AAGFE,wBAAI,GAHF;AAIFC,wBAAI,CAAC,aAAD,EAAgB,MAAhB;AAJF,iBAFY,EAQjBH,IARiB,CAQZ;AACFC,2BAAO,MADL;AAEFD,0BAAM,MAFJ;AAGFE,wBAAI,GAHF;AAIFC,wBAAI,CAAC,oBAAD,EAAuB,MAAvB;AAJF,iBARY,EAcjBpD,KAdiB,CAcX;AACH,2CAAuB2C;AADpB,iBAdW,EAiBjBU,KAjBiB,CAiBX,4EAjBW,EAkBjBC,KAlBiB,CAkBX,oBAlBW,EAmBjBV,IAnBiB,CAmBZA,IAnBY,EAmBNE,KAnBM,EAoBjBS,WApBiB,EAAtB;;AAsBA,uBAAO,OAAK/B,OAAL,CAAa;AAChBgC,0BAAMT,QAAQS,IADE;AAEhBC,2BAAOV,QAAQW,KAFC;AAGhBd,0BAAMjD,SAASiD,IAAT,CAHU;AAIhBE,2BAAOnD,SAASmD,KAAT;AAJS,iBAAb,CAAP;AAOH,aAnCD,CAmCE,OAAOrD,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,OAAKH,IAAL,CAAU,GAAV,EAAe,UAAf,CAAP;AACH;AAvCiB;AAwCrB;;AAED;;;;AAIMqE,eAAN,GAAoB;AAAA;;AAAA;AAChB,gBAAI;AACA,sBAAMhB,SAAS,MAAM,OAAKnD,cAAL,EAArB;;AAEA;AACA,sBAAMM,WAAW,MAAM,OAAKC,KAAL,CAAW,4BAAX,EAAyCC,KAAzC,CAA+C;AAClEC,6BAAS0C;AADyD,iBAA/C,EAEpBzC,IAFoB,EAAvB;;AAIA,oBAAIC,MAAMC,OAAN,CAAcN,QAAd,CAAJ,EAA6B;AACzB,2BAAO,OAAK0B,OAAL,CAAa;AAChBiC,+BAAO;AACHG,oCAAQ,CADL;AAEHC,oCAAQ,CAFL;AAGHC,wCAAY;AAHT,yBADS;AAMhBC,+BAAO;AACHH,oCAAQ,CADL;AAEHC,oCAAQ,CAFL;AAGHC,wCAAY;AAHT;AANS,qBAAb,CAAP;AAYH;;AAED;AACA,sBAAME,aAAarE,SAAS,IAAIC,IAAJ,GAAWqE,QAAX,CAAoB,CAApB,EAAuB,CAAvB,EAA0B,CAA1B,EAA6B,CAA7B,IAAkC,IAA3C,CAAnB;AACA,sBAAMC,cAAc,MAAM,OAAKnE,KAAL,CAAW,0BAAX,EAAuCC,KAAvC,CAA6C;AACnEgB,sCAAkB2B,MADiD;AAEnExB,gCAAY,CAAC,IAAD,EAAO6C,UAAP;AAFuD,iBAA7C,EAGvBN,KAHuB,EAA1B;;AAKA,sBAAMS,cAAc,MAAM,OAAKpE,KAAL,CAAW,0BAAX,EAAuCC,KAAvC,CAA6C;AACnEgB,sCAAkB2B,MADiD;AAEnElC,iCAAa,CAAC,IAAD,EAAOuD,UAAP;AAFsD,iBAA7C,EAGvBN,KAHuB,EAA1B;;AAKA,sBAAMU,kBAAkB,MAAM,OAAKrE,KAAL,CAAW,0BAAX,EAAuCC,KAAvC,CAA6C;AACvEgB,sCAAkB2B,MADqD;AAEvElC,iCAAa,CAAC,IAAD,EAAOuD,UAAP;AAF0D,iBAA7C,EAG3BK,GAH2B,CAGvB,mBAHuB,CAA9B;;AAKA,uBAAO,OAAK7C,OAAL,CAAa;AAChBiC,2BAAO;AACHG,gCAAQ9D,SAASuC,WAAT,IAAwB,CAD7B;AAEHwB,gCAAQ/D,SAASyC,YAAT,IAAyB,CAF9B;AAGHuB,oCAAYQ,WAAWxE,SAASyE,gBAApB,KAAyC;AAHlD,qBADS;AAMhBR,2BAAO;AACHH,gCAAQM,WADL;AAEHL,gCAAQM,WAFL;AAGHL,oCAAYQ,WAAWF,eAAX,KAA+B;AAHxC;AANS,iBAAb,CAAP;AAaH,aArDD,CAqDE,OAAO3E,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,OAAKH,IAAL,CAAU,GAAV,EAAe,UAAf,CAAP;AACH;AAzDe;AA0DnB;;AAED;;;;AAIMkF,qBAAN,GAA0B;AAAA;;AAAA;AACtB,gBAAI;AACA,sBAAMC,UAAU,OAAKxF,IAAL,CAAU,SAAV,CAAhB;AACA,sBAAMC,iBAAiB,OAAKD,IAAL,CAAU,gBAAV,CAAvB;AACA,sBAAMD,UAAU,OAAKC,IAAL,CAAU,SAAV,CAAhB;AACA,sBAAMyF,cAAc,OAAKzF,IAAL,CAAU,aAAV,KAA4B,CAAhD;AACA,sBAAME,cAAc,OAAKF,IAAL,CAAU,aAAV,KAA4B,aAAhD;;AAEAG,wBAAQC,GAAR,CAAY,wBAAZ;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBoF,OAArB;AACArF,wBAAQC,GAAR,CAAY,UAAZ,EAAwBH,cAAxB;AACAE,wBAAQC,GAAR,CAAY,OAAZ,EAAqBL,OAArB;AACAI,wBAAQC,GAAR,CAAY,OAAZ,EAAqBqF,WAArB;AACAtF,wBAAQC,GAAR,CAAY,OAAZ,EAAqBF,WAArB;;AAEA,oBAAI,CAACsF,OAAD,IAAY,CAACvF,cAAb,IAA+B,CAACF,OAApC,EAA6C;AACzC,2BAAO,OAAKM,IAAL,CAAU,GAAV,EAAe,OAAf,CAAP;AACH;;AAED;AACA,sBAAMQ,WAAW,MAAM,OAAKC,KAAL,CAAW,4BAAX,EAClBC,KADkB,CACZ,EAAEC,SAASf,cAAX,EAA2BqB,QAAQ,CAAnC,EADY,EAElBL,IAFkB,EAAvB;;AAIA,oBAAIC,MAAMC,OAAN,CAAcN,QAAd,CAAJ,EAA6B;AACzBV,4BAAQC,GAAR,CAAY,YAAZ;AACA,2BAAO,OAAKC,IAAL,CAAU,GAAV,EAAe,YAAf,CAAP;AACH;;AAED;AACA,sBAAMqF,oBAAoB,OAAKC,OAAL,CAAa,QAAb,CAA1B;AACA,sBAAMC,SAAS,MAAMF,kBAAkBG,kBAAlB,CACjB5F,cADiB,EAEjBuF,OAFiB,EAGjBzF,OAHiB,EAIjB0F,WAJiB,EAKjBvF,WALiB,CAArB;;AAQA,oBAAI0F,OAAOrD,OAAX,EAAoB;AAChB;AACA,0BAAM,OAAKuD,wBAAL,CAA8B7F,cAA9B,CAAN;;AAEA;AACA,0BAAMyF,kBAAkBK,oBAAlB,CAAuC9F,cAAvC,CAAN;;AAEAE,4BAAQC,GAAR,CAAY,cAAZ;AACA,2BAAO,OAAKmC,OAAL,CAAa;AAChBC,iCAAS,YADO;AAEhBqC,oCAAYe,OAAOf,UAFH;AAGhBmB,2CAAmBJ,OAAOA;AAHV,qBAAb,CAAP;AAKH,iBAbD,MAaO;AACHzF,4BAAQC,GAAR,CAAY,eAAZ,EAA6BwF,OAAOpD,OAApC;AACA,2BAAO,OAAKnC,IAAL,CAAU,GAAV,EAAeuF,OAAOpD,OAAP,IAAkB,QAAjC,CAAP;AACH;AAEJ,aAxDD,CAwDE,OAAOhC,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,OAAKH,IAAL,CAAU,GAAV,EAAe,UAAf,CAAP;AACH;AA5DqB;AA6DzB;;AAED;;;AAGMyF,4BAAN,CAA+B7F,cAA/B,EAA+C;AAAA;;AAAA;AAC3C,gBAAI;AACA,sBAAMQ,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACA,sBAAMqF,eAAe,IAAItF,IAAJ,GAAWuF,QAAX,KAAwB,CAA7C;AACA,sBAAMC,cAAc,IAAIxF,IAAJ,GAAWyF,WAAX,EAApB;;AAEA,sBAAMvF,WAAW,MAAM,OAAKC,KAAL,CAAW,4BAAX,EAClBC,KADkB,CACZ,EAAEC,SAASf,cAAX,EADY,EAElBgB,IAFkB,EAAvB;;AAIA,oBAAIC,MAAMC,OAAN,CAAcN,QAAd,CAAJ,EAA6B;AACzB;AACH;;AAED;AACA,sBAAMwF,iBAAiB3F,SAASG,SAASyC,YAAT,IAAyB,CAAlC,IAAuC,CAA9D;;AAEA;AACA,sBAAMgD,qBAAqB,IAAI3F,IAAJ,CAASE,SAASY,WAAT,GAAuB,IAAhC,CAA3B;AACA,sBAAM8E,iBAAiBD,mBAAmBJ,QAAnB,KAAgC,CAAhC,KAAsCD,YAAtC,IACDK,mBAAmBF,WAAnB,OAAqCD,WAD3D;;AAGA,oBAAIK,cAAJ;AACA,oBAAID,cAAJ,EAAoB;AAChBC,qCAAiB9F,SAASG,SAAS0C,YAAT,IAAyB,CAAlC,IAAuC,CAAxD;AACH,iBAFD,MAEO;AACH;AACAiD,qCAAiB,CAAjB;AACH;;AAED;AACA,sBAAM,OAAK1F,KAAL,CAAW,4BAAX,EACDC,KADC,CACK,EAAEC,SAASf,cAAX,EADL,EAEDuD,MAFC,CAEM;AACJF,kCAAc+C,cADV;AAEJ9C,kCAAciD,cAFV;AAGJ/E,iCAAahB;AAHT,iBAFN,CAAN;;AAQAN,wBAAQC,GAAR,CAAa,oBAAmBiG,cAAe,SAAQG,cAAe,EAAtE;AAEH,aAxCD,CAwCE,OAAOhG,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,cAAd,EAA8BA,KAA9B;AACA,sBAAMA,KAAN;AACH;AA5C0C;AA6C9C;;AAED;;;;AAIMiG,sBAAN,GAA2B;AAAA;;AAAA;AACvB,gBAAI;AACA,sBAAM/C,SAAS,MAAM,OAAKnD,cAAL,EAArB;AACA,sBAAMoD,OAAOjD,SAAS,OAAKkD,GAAL,CAAS,MAAT,KAAoB,CAA7B,CAAb;AACA,sBAAM8C,WAAWhG,SAAS,OAAKkD,GAAL,CAAS,UAAT,KAAwB,EAAjC,CAAjB;AACA,sBAAM+C,YAAY,OAAK/C,GAAL,CAAS,WAAT,KAAyB,EAA3C;AACA,sBAAMgD,UAAU,OAAKhD,GAAL,CAAS,SAAT,KAAuB,EAAvC;AACA,sBAAMiD,aAAa,OAAKjD,GAAL,CAAS,YAAT,KAA0B,EAA7C,CANA,CAMiD;;AAEjDzD,wBAAQC,GAAR,CAAY,gBAAZ;AACAD,wBAAQC,GAAR,CAAY,QAAZ,EAAsBsD,MAAtB,EAA8B,KAA9B,EAAqCC,IAArC,EAA2C,OAA3C,EAAoDkD,UAApD;;AAEA;AACA,oBAAIC,iBAAkB,wBAAuBpD,MAAO,EAApD;;AAEA;AACA,oBAAIiD,aAAaC,OAAjB,EAA0B;AACtB,0BAAMG,YAAYrG,SAAS,IAAIC,IAAJ,CAASgG,SAAT,EAAoB/F,OAApB,KAAgC,IAAzC,CAAlB;AACA,0BAAMoG,UAAUtG,SAAS,IAAIC,IAAJ,CAASiG,OAAT,EAAkBhG,OAAlB,KAA8B,IAAvC,CAAhB;AACAkG,sCAAmB,6BAA4BC,SAAU,QAAOC,OAAQ,EAAxE;AACH;;AAED;AACA,oBAAIC,MAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBA6BCH,cAAe;aA7B3B;;AAgCA;AACA,oBAAID,eAAe,SAAnB,EAA8B;AAC1BI,2BAAQ,iCAAR;AACH,iBAFD,MAEO,IAAIJ,eAAe,SAAnB,EAA8B;AACjCI,2BAAQ,iCAAR;AACH;;AAEDA,uBAAQ,6BAAR;;AAEA;AACA,sBAAMC,SAAS,CAACvD,OAAO,CAAR,IAAa+C,QAA5B;AACA,sBAAMS,WAAWF,MAAO,UAASC,MAAO,KAAIR,QAAS,EAArD;;AAEA;AACA,sBAAM5C,UAAU,MAAM,OAAKhD,KAAL,GAAasG,KAAb,CAAmBD,QAAnB,CAAtB;;AAEA;AACA,sBAAME,WAAY,kCAAiCJ,GAAI,WAAvD;AACA,sBAAMK,cAAc,MAAM,OAAKxG,KAAL,GAAasG,KAAb,CAAmBC,QAAnB,CAA1B;AACA,sBAAM7C,QAAQ8C,YAAY,CAAZ,EAAe9C,KAA7B;;AAEA;AACA,sBAAM+C,mBAAmBzD,QAAQ0D,GAAR,CAAY,kBAAU;AAC3C,2BAAO;AACH5F,4BAAI6F,OAAO7F,EADR;AAEHtB,uCAAemH,OAAOzF,eAFnB;AAGH0F,yCAAiBD,OAAOE,gBAAP,IAA2B,MAHzC;AAIH5H,iCAAS0H,OAAOxF,QAJb;AAKH2F,mCAAWH,OAAOI,UALf;AAMHC,oCAAYL,OAAOM,WANhB;AAOH7H,qCAAauH,OAAO7E,YAPjB;AAQHoF,mCAAWP,OAAOvF,UARf;AASH+F,uCAAe,OAAKC,UAAL,CAAgBT,OAAOvF,UAAvB,CATZ;AAUHC,mCAAWsF,OAAOnF,UAVf;AAWHG,sCAAcgF,OAAO1E,cAAP,KAA0B,CAXrC;AAYHoF,oCAAYV,OAAOW,WAZhB;AAaHC,wCAAgBZ,OAAOW,WAAP,KAAuB,SAAvB,GAAmC,KAAnC,GAA2C,KAbxD;AAcHE,mCAAWb,OAAOW,WAAP,KAAuB,SAAvB,GAAmC;AAC1C5C,qCAASiC,OAAOc,QAD0B;AAE1C9C,yCAAaJ,WAAWoC,OAAOe,YAAP,IAAuB,CAAlC,CAF6B;AAG1CC,8CAAkBpD,WAAWoC,OAAOiB,iBAAP,IAA4B,CAAvC,CAHwB;AAI1CC,yCAAalB,OAAOmB;AAJsB,yBAAnC,GAKP;AAnBD,qBAAP;AAqBH,iBAtBwB,CAAzB;;AAwBA;AACA,sBAAMC,cAAc,MAAM,OAAKC,oBAAL,CAA0BpF,MAA1B,EAAkCiD,SAAlC,EAA6CC,OAA7C,CAA1B;;AAEAzG,wBAAQC,GAAR,CAAa,YAAWoE,KAAM,KAA9B;;AAEA,uBAAO,OAAKjC,OAAL,CAAa;AAChBuB,6BAASyD,gBADO;AAEhBwB,gCAAY;AACRpF,8BAAMA,IADE;AAER+C,kCAAUA,QAFF;AAGRlC,+BAAOA,KAHC;AAIRwE,oCAAYC,KAAKC,IAAL,CAAU1E,QAAQkC,QAAlB;AAJJ,qBAFI;AAQhByC,2BAAON;AARS,iBAAb,CAAP;AAWH,aApHD,CAoHE,OAAOrI,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,OAAKH,IAAL,CAAU,eAAeG,MAAMgC,OAA/B,CAAP;AACH;AAxHsB;AAyH1B;;AAED;;;AAGMsG,wBAAN,CAA2BpF,MAA3B,EAAmCiD,YAAY,EAA/C,EAAmDC,UAAU,EAA7D,EAAiE;AAAA;;AAAA;AAC7D,gBAAI;AACA,oBAAIE,iBAAkB,wBAAuBpD,MAAO,EAApD;;AAEA;AACA,oBAAIiD,aAAaC,OAAjB,EAA0B;AACtB,0BAAMG,YAAYrG,SAAS,IAAIC,IAAJ,CAASgG,SAAT,EAAoB/F,OAApB,KAAgC,IAAzC,CAAlB;AACA,0BAAMoG,UAAUtG,SAAS,IAAIC,IAAJ,CAASiG,OAAT,EAAkBhG,OAAlB,KAA8B,IAAvC,CAAhB;AACAkG,sCAAmB,6BAA4BC,SAAU,QAAOC,OAAQ,EAAxE;AACH;;AAED,sBAAMoC,WAAY;;;;;;;;;;;;;wBAaNtC,cAAe;aAb3B;;AAgBA,sBAAM+B,cAAc,MAAM,OAAK/H,KAAL,GAAasG,KAAb,CAAmBgC,QAAnB,CAA1B;AACA,sBAAMD,QAAQN,YAAY,CAAZ,CAAd;;AAEA,uBAAO;AACHQ,iCAAa3I,SAASyI,MAAMG,YAAf,CADV,EACkD;AACrDC,oCAAgB7I,SAASyI,MAAMK,eAAf,CAFb,EAEkD;AACrDC,iCAAa/I,SAASyI,MAAM7F,YAAf,CAHV,EAGkD;AACrDoG,iCAAahJ,SAASyI,MAAMG,YAAf,IAA+B5I,SAASyI,MAAM7F,YAAf,CAJzC,EAIuE;AAC1EqG,sCAAkBtE,WAAW8D,MAAMS,kBAAjB,CALf,EAKqD;AACxDC,qCAAiBxE,WAAW8D,MAAM7D,gBAAjB,CANd,EAMqD;AACxDwE,oCAAgBzE,WAAW8D,MAAMY,eAAjB,CAPb,CAOqD;AAPrD,iBAAP;AAUH,aAvCD,CAuCE,OAAOvJ,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO;AACH6I,iCAAa,CADV;AAEHE,oCAAgB,CAFb;AAGHE,iCAAa,CAHV;AAIHC,iCAAa,CAJV;AAKHC,sCAAkB,CALf;AAMHE,qCAAiB,CANd;AAOHC,oCAAgB;AAPb,iBAAP;AASH;AAnD4D;AAoDhE;;AAED;;;;AAIME,oBAAN,GAAyB;AAAA;;AAAA;AACrB,gBAAI;AACA,sBAAMtG,SAAS,MAAM,OAAKnD,cAAL,EAArB;AACA,sBAAM0J,aAAa,OAAKrG,GAAL,CAAS,YAAT,KAA0B,OAA7C,CAFA,CAEsD;;AAEtDzD,wBAAQC,GAAR,CAAY,oBAAZ;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBsD,MAArB,EAA6B,OAA7B,EAAsCuG,UAAtC;;AAEA;AACA,sBAAMC,YAAY,OAAKC,YAAL,CAAkBF,UAAlB,CAAlB;;AAEA;AACA,sBAAM,CACFG,aADE,EAEFC,WAFE,EAGFC,QAHE,EAIFC,WAJE,EAKFC,YALE,IAMF,MAAMC,QAAQC,GAAR,CAAY,CAClB,OAAKC,oBAAL,CAA0BjH,MAA1B,CADkB,EAElB,OAAKkH,oBAAL,CAA0BlH,MAA1B,EAAkCwG,SAAlC,CAFkB,EAGlB,OAAKW,WAAL,CAAiBnH,MAAjB,CAHkB,EAIlB,OAAKoH,cAAL,CAAoBpH,MAApB,CAJkB,EAKlB,OAAKqH,eAAL,CAAqBrH,MAArB,CALkB,CAAZ,CANV;;AAcAvD,wBAAQC,GAAR,CAAY,cAAZ;;AAEA,uBAAO,OAAKmC,OAAL,CAAa;AAChB6H,iCADgB;AAEhBC,+BAFgB;AAGhBC,4BAHgB;AAIhBC,+BAJgB;AAKhBC,gCALgB;AAMhBP,8BANgB;AAOhBe,oCAAgB,OAAKC,iBAAL,CAAuBhB,UAAvB;AAPA,iBAAb,CAAP;AAUH,aArCD,CAqCE,OAAOzJ,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,eAAd,EAA+BA,KAA/B;AACA,uBAAO,OAAKH,IAAL,CAAU,aAAaG,MAAMgC,OAA7B,CAAP;AACH;AAzCoB;AA0CxB;;AAED;;;AAGMmI,wBAAN,CAA2BjH,MAA3B,EAAmC;AAAA;;AAAA;AAC/B,gBAAI;AACA;AACA,sBAAMwH,iBAAiB,MAAM,QAAKpK,KAAL,CAAW,yBAAX,EAAsCC,KAAtC,CAA4C;AACrEC,6BAAS0C;AAD4D,iBAA5C,EAE1BzC,IAF0B,EAA7B;;AAIA;AACA,sBAAMkK,aAAa,MAAM,QAAKrK,KAAL,CAAW,0BAAX,EAAuCC,KAAvC,CAA6C;AAClEgB,sCAAkB2B,MADgD;AAElEpC,4BAAQ,CAAC,IAAD,EAAO,WAAP;AAF0D,iBAA7C,EAGtB8C,KAHsB,CAGhB,yGAHgB,EAG2FnD,IAH3F,EAAzB;;AAKA;AACA,sBAAMmK,YAAY,MAAM,QAAKC,kBAAL,CAAwB3H,MAAxB,CAAxB;;AAEA,uBAAO;AACH+F,iCAAa/I,SAASyK,WAAW7H,YAAX,IAA2B,CAApC,CADV;AAEHgI,iCAAajG,WAAW8F,WAAWI,YAAX,IAA2B,CAAtC,EAAyCC,OAAzC,CAAiD,CAAjD,CAFV;AAGH3B,qCAAiBxE,WAAW6F,eAAe5F,gBAAf,IAAmC,CAA9C,EAAiDkG,OAAjD,CAAyD,CAAzD,CAHd;AAIHJ,+BAAWA;AAJR,iBAAP;AAOH,aAtBD,CAsBE,OAAO5K,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO;AACHiJ,iCAAa,CADV;AAEH6B,iCAAa,MAFV;AAGHzB,qCAAiB,MAHd;AAIHuB,+BAAW;AAJR,iBAAP;AAMH;AA/B8B;AAgClC;;AAED;;;AAGMR,wBAAN,CAA2BlH,MAA3B,EAAmCwG,SAAnC,EAA8C;AAAA;;AAAA;AAC1C,gBAAI;AACA,sBAAMpD,iBAAiB;AACnB/E,sCAAkB2B,MADC;AAEnBpC,4BAAQ,CAAC,IAAD,EAAO,WAAP;AAFW,iBAAvB;;AAKA,oBAAI4I,UAAUuB,KAAV,IAAmBvB,UAAUwB,GAAjC,EAAsC;AAClC5E,mCAAetF,WAAf,GAA6B,CAAC,SAAD,EAAY,CAAC0I,UAAUuB,KAAX,EAAkBvB,UAAUwB,GAA5B,CAAZ,CAA7B;AACH;;AAED;AACA,sBAAMP,aAAa,MAAM,QAAKrK,KAAL,CAAW,0BAAX,EAAuCC,KAAvC,CAA6C+F,cAA7C,EACpB1C,KADoB,CACd,uFADc,EAEpBnD,IAFoB,EAAzB;;AAIA;AACA,sBAAM0K,aAAa,MAAM,QAAK7K,KAAL,CAAW,0BAAX,EAAuCC,KAAvC,CAA6C;AAClEgB,sCAAkB2B,MADgD;AAElExB,gCAAYgI,UAAUuB,KAAV,IAAmBvB,UAAUwB,GAA7B,GAAmC,CAAC,SAAD,EAAY,CAACxB,UAAUuB,KAAX,EAAkBvB,UAAUwB,GAA5B,CAAZ,CAAnC,GAAmF,CAAC,GAAD,EAAM,CAAN;AAF7B,iBAA7C,EAGtBtH,KAHsB,CAGhB,6CAHgB,EAG+BnD,IAH/B,EAAzB;;AAKA,uBAAO;AACH2D,4BAAQlE,SAASyK,WAAWvG,MAAX,IAAqB,CAA9B,CADL;AAEHgH,4BAAQvG,WAAW8F,WAAWS,MAAX,IAAqB,CAAhC,EAAmCJ,OAAnC,CAA2C,CAA3C,CAFL;AAGH3G,gCAAYQ,WAAW8F,WAAWtG,UAAX,IAAyB,CAApC,EAAuC2G,OAAvC,CAA+C,CAA/C,CAHT;AAIHK,8BAAUnL,SAASiL,WAAWE,QAAX,IAAuB,CAAhC;AAJP,iBAAP;AAOH,aA5BD,CA4BE,OAAOrL,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,cAAd,EAA8BA,KAA9B;AACA,uBAAO;AACHoE,4BAAQ,CADL;AAEHgH,4BAAQ,MAFL;AAGH/G,gCAAY,MAHT;AAIHgH,8BAAU;AAJP,iBAAP;AAMH;AArCyC;AAsC7C;;AAED;;;AAGMhB,eAAN,CAAkBnH,MAAlB,EAA0B;AAAA;;AAAA;AACtB,gBAAI;AACA;AACA,sBAAMoI,cAAc,MAAM,QAAKhL,KAAL,CAAW,0BAAX,EAAuCC,KAAvC,CAA6C;AACnEgL,6CAAyBrI;AAD0C,iBAA7C,EAEvBU,KAFuB,CAEjB,2CAFiB,EAE4BnD,IAF5B,EAA1B;;AAIA;AACA,sBAAM+K,aAAa,MAAM,QAAKX,kBAAL,CAAwB3H,MAAxB,CAAzB;;AAEA;AACA,sBAAMuI,iBAAiB,MAAM,QAAKC,iBAAL,CAAuBxI,MAAvB,CAA7B;;AAEA,uBAAO;AACHoI,iCAAapL,SAASoL,YAAYrH,KAAZ,IAAqB,CAA9B,CADV;AAEHuH,gCAAYA,UAFT;AAGHG,gCAAYF,eAAerH,MAHxB;AAIHwH,oCAAgBH,eAAepH;AAJ5B,iBAAP;AAOH,aAnBD,CAmBE,OAAOrE,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO;AACHsL,iCAAa,CADV;AAEHE,gCAAY,CAFT;AAGHG,gCAAY,CAHT;AAIHC,oCAAgB;AAJb,iBAAP;AAMH;AA5BqB;AA6BzB;;AAED;;;AAGMtB,kBAAN,CAAqBpH,MAArB,EAA6B;AAAA;;AAAA;AACzB,gBAAI;AACA;AACA,sBAAMuC,eAAe,QAAKoG,oBAAL,EAArB;;AAEA,sBAAM9B,cAAc,MAAM,QAAKzJ,KAAL,CAAW,0BAAX,EACrBiD,KADqB,CACf,IADe,EAErBC,IAFqB,CAEhB;AACFC,2BAAO,cADL;AAEFD,0BAAM,MAFJ;AAGFE,wBAAI,GAHF;AAIFC,wBAAI,CAAC,qBAAD,EAAwB,MAAxB;AAJF,iBAFgB,EAQrBpD,KARqB,CAQf;AACH,iCAAa,CAAC,IAAD,EAAO,WAAP,CADV;AAEH,sCAAkB,CAAC,SAAD,EAAY,CAACkF,aAAawF,KAAd,EAAqBxF,aAAayF,GAAlC,CAAZ;AAFf,iBARe,EAYrBtH,KAZqB,CAYf,wHAZe,EAarBkI,KAbqB,CAaf,qBAbe,EAcrBjI,KAdqB,CAcf,iBAde,EAerBR,KAfqB,CAef,CAfe,EAgBrB0I,MAhBqB,EAA1B;;AAkBA,uBAAOhC,YAAY/C,GAAZ,CAAgB;AAAA,2BAAS;AAC5B5F,4BAAI4K,KAAKxL,OADmB;AAE5ByL,kCAAU,QAAKC,cAAL,CAAoBF,KAAKC,QAAzB,KAAuC,KAAID,KAAKxL,OAAQ,EAFtC;AAG5B2L,gCAAQH,KAAKG,MAAL,IAAe,EAHK;AAI5BC,oCAAYlM,SAAS8L,KAAKK,WAAd,CAJgB;AAK5BhI,oCAAYQ,WAAWmH,KAAK3H,UAAL,IAAmB,CAA9B,EAAiC2G,OAAjC,CAAyC,CAAzC;AALgB,qBAAT;AAAA,iBAAhB,CAAP;AAQH,aA9BD,CA8BE,OAAOhL,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,YAAd,EAA4BA,KAA5B;AACA,uBAAO,EAAP;AACH;AAlCwB;AAmC5B;;AAED;;;AAGMuK,mBAAN,CAAsBrH,MAAtB,EAA8B;AAAA;;AAAA;AAC1B,gBAAI;AACA,sBAAM8G,eAAe,MAAM,QAAK1J,KAAL,CAAW,0BAAX,EAAuCC,KAAvC,CAA6C;AACpEgB,sCAAkB2B,MADkD;AAEpEpC,4BAAQ,CAAC,IAAD,EAAO,WAAP;AAF4D,iBAA7C,EAI1B8C,KAJ0B,CAIpB,8DAJoB,EAK1BC,KAL0B,CAKpB,kBALoB,EAM1BR,KAN0B,CAMpB,CANoB,EAO1B0I,MAP0B,EAA3B;;AASA,uBAAO/B,aAAahD,GAAb,CAAiB;AAAA,2BAAU;AAC9B5F,4BAAIyC,MAAMzC,EADoB;AAE9BgG,mCAAWvD,MAAMwD,UAFa;AAG9BpC,qCAAaJ,WAAWhB,MAAMmE,YAAjB,EAA+BgD,OAA/B,CAAuC,CAAvC,CAHiB;AAI9B3G,oCAAYQ,WAAWhB,MAAMqE,iBAAjB,EAAoC8C,OAApC,CAA4C,CAA5C,CAJkB;AAK9BsB,oCAAY,QAAK5E,UAAL,CAAgB7D,MAAM7C,WAAtB;AALkB,qBAAV;AAAA,iBAAjB,CAAP;AAQH,aAlBD,CAkBE,OAAOhB,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,EAAP;AACH;AAtByB;AAuB7B;;AAED;;;AAGA0H,eAAW6E,SAAX,EAAsB;AAClB,cAAMC,OAAO,IAAIrM,IAAJ,CAASoM,YAAY,IAArB,CAAb;AACA,cAAME,OAAOD,KAAK5G,WAAL,EAAb;AACA,cAAM8G,QAAQC,OAAOH,KAAK9G,QAAL,KAAkB,CAAzB,EAA4BkH,QAA5B,CAAqC,CAArC,EAAwC,GAAxC,CAAd;AACA,cAAMC,MAAMF,OAAOH,KAAKM,OAAL,EAAP,EAAuBF,QAAvB,CAAgC,CAAhC,EAAmC,GAAnC,CAAZ;AACA,cAAMG,OAAOJ,OAAOH,KAAKQ,QAAL,EAAP,EAAwBJ,QAAxB,CAAiC,CAAjC,EAAoC,GAApC,CAAb;AACA,cAAMK,SAASN,OAAOH,KAAKU,UAAL,EAAP,EAA0BN,QAA1B,CAAmC,CAAnC,EAAsC,GAAtC,CAAf;;AAEA,eAAQ,GAAEH,IAAK,IAAGC,KAAM,IAAGG,GAAI,IAAGE,IAAK,IAAGE,MAAO,EAAjD;AACH;;AAED;;;AAGAtD,iBAAaF,UAAb,EAAyB;AACrB,cAAM0D,MAAM,IAAIhN,IAAJ,EAAZ;AACA,YAAI8K,KAAJ,EAAWC,GAAX;;AAEA,gBAAOzB,UAAP;AACI,iBAAK,OAAL;AACIwB,wBAAQ/K,SAAS,IAAIC,IAAJ,CAASgN,IAAIvH,WAAJ,EAAT,EAA4BuH,IAAIzH,QAAJ,EAA5B,EAA4CyH,IAAIL,OAAJ,EAA5C,EAA2D1M,OAA3D,KAAuE,IAAhF,CAAR;AACA8K,sBAAMhL,SAAS,IAAIC,IAAJ,CAASgN,IAAIvH,WAAJ,EAAT,EAA4BuH,IAAIzH,QAAJ,EAA5B,EAA4CyH,IAAIL,OAAJ,EAA5C,EAA2D,EAA3D,EAA+D,EAA/D,EAAmE,EAAnE,EAAuE1M,OAAvE,KAAmF,IAA5F,CAAN;AACA;AACJ,iBAAK,MAAL;AACI,sBAAMgN,YAAY,IAAIjN,IAAJ,CAASgN,GAAT,CAAlB;AACAC,0BAAUC,OAAV,CAAkBF,IAAIL,OAAJ,KAAgBK,IAAIG,MAAJ,EAAlC;AACAF,0BAAU5I,QAAV,CAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B;AACAyG,wBAAQ/K,SAASkN,UAAUhN,OAAV,KAAsB,IAA/B,CAAR;AACA8K,sBAAMhL,SAASiN,IAAI/M,OAAJ,KAAgB,IAAzB,CAAN;AACA;AACJ,iBAAK,OAAL;AACI6K,wBAAQ/K,SAAS,IAAIC,IAAJ,CAASgN,IAAIvH,WAAJ,EAAT,EAA4BuH,IAAIzH,QAAJ,EAA5B,EAA4C,CAA5C,EAA+CtF,OAA/C,KAA2D,IAApE,CAAR;AACA8K,sBAAMhL,SAASiN,IAAI/M,OAAJ,KAAgB,IAAzB,CAAN;AACA;AACJ,iBAAK,KAAL;AACA;AACI6K,wBAAQ,IAAR;AACAC,sBAAM,IAAN;AACA;AApBR;;AAuBA,eAAO,EAAED,KAAF,EAASC,GAAT,EAAP;AACH;;AAED;;;AAGAW,2BAAuB;AACnB,cAAMsB,MAAM,IAAIhN,IAAJ,EAAZ;AACA,cAAM8K,QAAQ/K,SAAS,IAAIC,IAAJ,CAASgN,IAAIvH,WAAJ,EAAT,EAA4BuH,IAAIzH,QAAJ,EAA5B,EAA4C,CAA5C,EAA+CtF,OAA/C,KAA2D,IAApE,CAAd;AACA,cAAM8K,MAAMhL,SAAS,IAAIC,IAAJ,CAASgN,IAAIvH,WAAJ,EAAT,EAA4BuH,IAAIzH,QAAJ,KAAiB,CAA7C,EAAgD,CAAhD,EAAmD,EAAnD,EAAuD,EAAvD,EAA2D,EAA3D,EAA+DtF,OAA/D,KAA2E,IAApF,CAAZ;AACA,eAAO,EAAE6K,KAAF,EAASC,GAAT,EAAP;AACH;;AAED;;;AAGAT,sBAAkBhB,UAAlB,EAA8B;AAC1B,cAAM8D,UAAU;AACZ,qBAAS,IADG;AAEZ,oBAAQ,IAFI;AAGZ,qBAAS,IAHG;AAIZ,mBAAO;AAJK,SAAhB;AAMA,eAAOA,QAAQ9D,UAAR,KAAuB,IAA9B;AACH;;AAED;;;AAGMoB,sBAAN,CAAyB3H,MAAzB,EAAiC;AAAA;;AAAA;AAC7B,gBAAI;AACA;AACA,sBAAMsK,gBAAgB,MAAM,QAAKlN,KAAL,CAAW,0BAAX,EAAuCC,KAAvC,CAA6C;AACrEgL,6CAAyBrI;AAD4C,iBAA7C,EAEzBU,KAFyB,CAEnB,sCAFmB,EAEqBmI,MAFrB,EAA5B;;AAIA,oBAAIP,aAAagC,cAAcC,MAA/B;;AAEA;AACA,qBAAK,IAAIC,MAAT,IAAmBF,aAAnB,EAAkC;AAC9B,0BAAMG,WAAW,MAAM,QAAK9C,kBAAL,CAAwB6C,OAAOlN,OAA/B,CAAvB;AACAgL,kCAAcmC,QAAd;AACH;;AAED,uBAAOnC,UAAP;AAEH,aAhBD,CAgBE,OAAOxL,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO,CAAP;AACH;AApB4B;AAqBhC;;AAED;;;AAGM0L,qBAAN,CAAwBxI,MAAxB,EAAgC;AAAA;;AAAA;AAC5B,gBAAI;AACA;AACA,sBAAM0K,gBAAgB,MAAM,QAAKC,mBAAL,CAAyB3K,MAAzB,CAA5B;;AAEA,oBAAI0K,cAAcH,MAAd,KAAyB,CAA7B,EAAgC;AAC5B,2BAAO,EAAErJ,QAAQ,CAAV,EAAaC,YAAY,MAAzB,EAAP;AACH;;AAED;AACA,sBAAMsE,QAAQ,MAAM,QAAKrI,KAAL,CAAW,0BAAX,EAAuCC,KAAvC,CAA6C;AAC7DgB,sCAAkB,CAAC,IAAD,EAAOqM,aAAP,CAD2C;AAE7D9M,4BAAQ,CAAC,IAAD,EAAO,WAAP;AAFqD,iBAA7C,EAGjB8C,KAHiB,CAGX,0DAHW,EAGiDnD,IAHjD,EAApB;;AAKA,uBAAO;AACH2D,4BAAQlE,SAASyI,MAAMvE,MAAN,IAAgB,CAAzB,CADL;AAEHC,gCAAYQ,WAAW8D,MAAMtE,UAAN,IAAoB,CAA/B,EAAkC2G,OAAlC,CAA0C,CAA1C;AAFT,iBAAP;AAKH,aAnBD,CAmBE,OAAOhL,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO,EAAEoE,QAAQ,CAAV,EAAaC,YAAY,MAAzB,EAAP;AACH;AAvB2B;AAwB/B;;AAED;;;AAGMwJ,uBAAN,CAA0B3K,MAA1B,EAAkC4K,UAAU,IAAIC,GAAJ,EAA5C,EAAuD;AAAA;;AAAA;AACnD,gBAAID,QAAQE,GAAR,CAAY9K,MAAZ,CAAJ,EAAyB;AACrB,uBAAO,EAAP;AACH;AACD4K,oBAAQ3M,GAAR,CAAY+B,MAAZ;;AAEA,gBAAI;AACA;AACA,sBAAMsK,gBAAgB,MAAM,QAAKlN,KAAL,CAAW,0BAAX,EAAuCC,KAAvC,CAA6C;AACrEgL,6CAAyBrI;AAD4C,iBAA7C,EAEzBU,KAFyB,CAEnB,sCAFmB,EAEqBmI,MAFrB,EAA5B;;AAIA,oBAAIkC,eAAeT,cAAcxG,GAAd,CAAkB;AAAA,2BAAKkH,EAAE1N,OAAP;AAAA,iBAAlB,CAAnB;;AAEA;AACA,qBAAK,IAAIkN,MAAT,IAAmBF,aAAnB,EAAkC;AAC9B,0BAAMW,eAAe,MAAM,QAAKN,mBAAL,CAAyBH,OAAOlN,OAAhC,EAAyCsN,OAAzC,CAA3B;AACAG,mCAAeA,aAAaG,MAAb,CAAoBD,YAApB,CAAf;AACH;;AAED,uBAAOF,YAAP;AAEH,aAhBD,CAgBE,OAAOjO,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO,EAAP;AACH;AAzBkD;AA0BtD;;AAED;;;AAGAkM,mBAAeD,QAAf,EAAyB;AACrB,YAAI,CAACA,QAAL,EAAe,OAAO,IAAP;AACf,YAAI;AACA,mBAAOoC,OAAOC,IAAP,CAAYrC,QAAZ,EAAsB,QAAtB,EAAgCsC,QAAhC,CAAyC,MAAzC,CAAP;AACH,SAFD,CAEE,OAAOvO,KAAP,EAAc;AACZ,mBAAOiM,QAAP;AACH;AACJ;AAp8B+B,CAApC", "file": "..\\..\\..\\src\\api\\controller\\promotion.js", "sourcesContent": ["const Base = require('./base.js');\n\nmodule.exports = class extends Base {\n    \n    /**\n     * 记录推广访问\n     * POST /api/promotion/recordVisit\n     */\n    async recordVisitAction() {\n        try {\n            const goodsId = this.post('goodsId');\n            const promoterUserId = this.post('promoterUserId');\n            const shareSource = this.post('shareSource') || 'miniprogram';\n            \n            console.log('=== 记录推广访问 ===');\n            console.log('商品ID:', goodsId);\n            console.log('推广员用户ID:', promoterUserId);\n            console.log('分享来源:', shareSource);\n            \n            if (!goodsId || !promoterUserId) {\n                return this.fail(400, '参数不完整');\n            }\n            \n            // 获取当前用户ID（如果已登录）\n            let visitorUserId = 0;\n            try {\n                visitorUserId = await this.getLoginUserId();\n            } catch (error) {\n                // 用户未登录，visitorUserId 为 0\n                console.log('用户未登录，记录匿名访问');\n            }\n            \n            // 移除自购限制，允许推广员访问自己的分享\n            console.log('记录推广访问，访问者ID:', visitorUserId, '推广员ID:', promoterUserId);\n            \n            const currentTime = parseInt(new Date().getTime() / 1000);\n            \n            // 首先获取或创建推广员记录\n            let promoter = await this.model('hiolabs_personal_promoters').where({\n                user_id: promoterUserId\n            }).find();\n            \n            if (think.isEmpty(promoter)) {\n                // 如果推广员不存在，创建推广员记录\n                const promoterData = {\n                    user_id: promoterUserId,\n                    level: 1,\n                    status: 1,\n                    first_share_time: currentTime,\n                    create_time: currentTime,\n                    update_time: currentTime\n                };\n                const promoterId = await this.model('hiolabs_personal_promoters').add(promoterData);\n                promoter = { id: promoterId, ...promoterData };\n                console.log('创建新推广员记录:', promoterId);\n            }\n            \n            // 检查是否已经记录过（24小时内同一用户访问同一推广员的同一商品）\n            const yesterday = currentTime - 86400;\n            let existingRecord = null;\n            \n            if (visitorUserId > 0) {\n                existingRecord = await this.model('hiolabs_promotion_visits').where({\n                    promoter_user_id: promoterUserId,\n                    visitor_user_id: visitorUserId,\n                    goods_id: goodsId,\n                    visit_time: ['>', yesterday]\n                }).find();\n            } else {\n                // 对于未登录用户，通过IP地址判断\n                const ipAddress = this.ctx.ip || '';\n                existingRecord = await this.model('hiolabs_promotion_visits').where({\n                    promoter_user_id: promoterUserId,\n                    goods_id: goodsId,\n                    ip_address: ipAddress,\n                    visitor_user_id: 0,\n                    visit_time: ['>', yesterday]\n                }).find();\n            }\n            \n            if (!think.isEmpty(existingRecord)) {\n                console.log('24小时内已记录过访问，不重复记录');\n                return this.success({ message: '访问记录成功' });\n            }\n            \n            // 检查是否是新访客（24小时内未访问过该推广员的任何商品）\n            const isNewVisitor = visitorUserId > 0 ?\n                think.isEmpty(await this.model('hiolabs_promotion_visits').where({\n                    promoter_user_id: promoterUserId,\n                    visitor_user_id: visitorUserId,\n                    visit_time: ['>', yesterday]\n                }).find()) : 1;\n            \n            // 创建推广访问记录\n            const visitRecord = {\n                promoter_id: promoter.id,\n                promoter_user_id: promoterUserId,\n                visitor_user_id: visitorUserId,\n                goods_id: goodsId,\n                share_source: shareSource,\n                visit_time: currentTime,\n                ip_address: this.ctx.ip || '',\n                user_agent: this.ctx.header['user-agent'] || '',\n                is_new_visitor: isNewVisitor ? 1 : 0\n            };\n            \n            const recordId = await this.model('hiolabs_promotion_visits').add(visitRecord);\n            \n            if (recordId) {\n                console.log('推广访问记录成功，记录ID:', recordId);\n                \n                // 更新推广员统计数据\n                await this.updatePromoterStats(promoter.id, 'visit');\n                \n                return this.success({ \n                    message: '访问记录成功',\n                    recordId: recordId\n                });\n            } else {\n                return this.fail(500, '记录访问失败');\n            }\n            \n        } catch (error) {\n            console.error('记录推广访问失败:', error);\n            return this.fail(500, '记录访问失败');\n        }\n    }\n    \n    /**\n     * 更新推广员统计数据\n     * @param {number} promoterId 推广员ID\n     * @param {string} type 统计类型：visit, order\n     */\n    async updatePromoterStats(promoterId, type = 'visit') {\n        try {\n            const currentTime = parseInt(new Date().getTime() / 1000);\n            \n            // 更新推广员统计数据\n            let updateData = { update_time: currentTime };\n            \n            if (type === 'visit') {\n                updateData.total_views = ['exp', 'total_views + 1'];\n                updateData.month_views = ['exp', 'month_views + 1'];\n            } else if (type === 'order') {\n                updateData.total_orders = ['exp', 'total_orders + 1'];\n                updateData.month_orders = ['exp', 'month_orders + 1'];\n            }\n            \n            await this.model('hiolabs_personal_promoters').where({\n                id: promoterId\n            }).update(updateData);\n            \n            console.log('推广员统计更新成功');\n            \n        } catch (error) {\n            console.error('更新推广员统计失败:', error);\n        }\n    }\n    \n    /**\n     * 获取推广记录列表\n     * GET /api/promotion/records\n     */\n    async recordsAction() {\n        try {\n            const userId = await this.getLoginUserId();\n            const page = this.get('page') || 1;\n            const limit = this.get('limit') || 20;\n            \n            // 获取用户的推广记录\n            const records = await this.model('hiolabs_promotion_visits')\n                .alias('pv')\n                .join({\n                    table: 'hiolabs_goods',\n                    join: 'left',\n                    as: 'g',\n                    on: ['pv.goods_id', 'g.id']\n                })\n                .join({\n                    table: 'user',\n                    join: 'left',\n                    as: 'u',\n                    on: ['pv.visitor_user_id', 'u.id']\n                })\n                .where({\n                    'pv.promoter_user_id': userId\n                })\n                .field('pv.*, g.name as goods_name, g.list_pic_url, u.nickname as visitor_nickname')\n                .order('pv.visit_time DESC')\n                .page(page, limit)\n                .countSelect();\n            \n            return this.success({\n                data: records.data,\n                total: records.count,\n                page: parseInt(page),\n                limit: parseInt(limit)\n            });\n            \n        } catch (error) {\n            console.error('获取推广记录失败:', error);\n            return this.fail(500, '获取推广记录失败');\n        }\n    }\n    \n    /**\n     * 获取推广统计数据\n     * GET /api/promotion/stats\n     */\n    async statsAction() {\n        try {\n            const userId = await this.getLoginUserId();\n            \n            // 获取推广员信息\n            const promoter = await this.model('hiolabs_personal_promoters').where({\n                user_id: userId\n            }).find();\n            \n            if (think.isEmpty(promoter)) {\n                return this.success({\n                    total: {\n                        visits: 0,\n                        orders: 0,\n                        commission: 0\n                    },\n                    today: {\n                        visits: 0,\n                        orders: 0,\n                        commission: 0\n                    }\n                });\n            }\n            \n            // 获取今日统计数据\n            const todayStart = parseInt(new Date().setHours(0, 0, 0, 0) / 1000);\n            const todayVisits = await this.model('hiolabs_promotion_visits').where({\n                promoter_user_id: userId,\n                visit_time: ['>=', todayStart]\n            }).count();\n            \n            const todayOrders = await this.model('hiolabs_promotion_orders').where({\n                promoter_user_id: userId,\n                create_time: ['>=', todayStart]\n            }).count();\n\n            const todayCommission = await this.model('hiolabs_promotion_orders').where({\n                promoter_user_id: userId,\n                create_time: ['>=', todayStart]\n            }).sum('commission_amount');\n            \n            return this.success({\n                total: {\n                    visits: promoter.total_views || 0,\n                    orders: promoter.total_orders || 0,\n                    commission: parseFloat(promoter.total_commission) || 0\n                },\n                today: {\n                    visits: todayVisits,\n                    orders: todayOrders,\n                    commission: parseFloat(todayCommission) || 0\n                }\n            });\n            \n        } catch (error) {\n            console.error('获取推广统计失败:', error);\n            return this.fail(500, '获取统计数据失败');\n        }\n    }\n\n    /**\n     * 处理推广成交，发放佣金奖励（基于分销池配置）\n     * POST /api/promotion/recordOrder\n     */\n    async recordOrderAction() {\n        try {\n            const orderId = this.post('orderId');\n            const promoterUserId = this.post('promoterUserId');\n            const goodsId = this.post('goodsId');\n            const orderAmount = this.post('orderAmount') || 0;\n            const shareSource = this.post('shareSource') || 'miniprogram';\n\n            console.log('=== 处理推广成交（分享有礼佣金） ===');\n            console.log('订单ID:', orderId);\n            console.log('推广员用户ID:', promoterUserId);\n            console.log('商品ID:', goodsId);\n            console.log('订单金额:', orderAmount);\n            console.log('分享来源:', shareSource);\n\n            if (!orderId || !promoterUserId || !goodsId) {\n                return this.fail(400, '参数不完整');\n            }\n\n            // 检查推广员是否存在\n            const promoter = await this.model('hiolabs_personal_promoters')\n                .where({ user_id: promoterUserId, status: 1 })\n                .find();\n\n            if (think.isEmpty(promoter)) {\n                console.log('推广员不存在或已禁用');\n                return this.fail(400, '推广员不存在或已禁用');\n            }\n\n            // 使用佣金服务发放分享有礼佣金\n            const commissionService = this.service('points');\n            const result = await commissionService.addShareCommission(\n                promoterUserId,\n                orderId,\n                goodsId,\n                orderAmount,\n                shareSource\n            );\n\n            if (result.success) {\n                // 更新推广员成交统计\n                await this.updatePromoterOrderStats(promoterUserId);\n\n                // 检查等级升级\n                await commissionService.checkPromoterLevelUp(promoterUserId);\n\n                console.log('✅ 分享有礼佣金发放完成');\n                return this.success({\n                    message: '分享有礼佣金发放成功',\n                    commission: result.commission,\n                    commissionDetails: result.result\n                });\n            } else {\n                console.log('❌ 分享有礼佣金发放失败:', result.message);\n                return this.fail(400, result.message || '佣金发放失败');\n            }\n\n        } catch (error) {\n            console.error('处理推广成交失败:', error);\n            return this.fail(500, '处理推广成交失败');\n        }\n    }\n\n    /**\n     * 更新推广员成交统计\n     */\n    async updatePromoterOrderStats(promoterUserId) {\n        try {\n            const currentTime = parseInt(new Date().getTime() / 1000);\n            const currentMonth = new Date().getMonth() + 1;\n            const currentYear = new Date().getFullYear();\n\n            const promoter = await this.model('hiolabs_personal_promoters')\n                .where({ user_id: promoterUserId })\n                .find();\n\n            if (think.isEmpty(promoter)) {\n                return;\n            }\n\n            // 更新总成交次数\n            const newTotalOrders = parseInt(promoter.total_orders || 0) + 1;\n\n            // 检查是否是当月\n            const promoterUpdateTime = new Date(promoter.update_time * 1000);\n            const isCurrentMonth = promoterUpdateTime.getMonth() + 1 === currentMonth &&\n                                  promoterUpdateTime.getFullYear() === currentYear;\n\n            let newMonthOrders;\n            if (isCurrentMonth) {\n                newMonthOrders = parseInt(promoter.month_orders || 0) + 1;\n            } else {\n                // 新月份，重置月度统计\n                newMonthOrders = 1;\n            }\n\n            // 更新推广员统计\n            await this.model('hiolabs_personal_promoters')\n                .where({ user_id: promoterUserId })\n                .update({\n                    total_orders: newTotalOrders,\n                    month_orders: newMonthOrders,\n                    update_time: currentTime\n                });\n\n            console.log(`✅ 推广员成交统计更新: 总成交=${newTotalOrders}, 月成交=${newMonthOrders}`);\n\n        } catch (error) {\n            console.error('更新推广员成交统计失败:', error);\n            throw error;\n        }\n    }\n\n    /**\n     * 获取分享记录列表（区分浏览和下单）\n     * GET /api/promotion/shareRecords\n     */\n    async shareRecordsAction() {\n        try {\n            const userId = await this.getLoginUserId();\n            const page = parseInt(this.get('page') || 1);\n            const pageSize = parseInt(this.get('pageSize') || 20);\n            const startDate = this.get('startDate') || '';\n            const endDate = this.get('endDate') || '';\n            const actionType = this.get('actionType') || ''; // all, browsed, ordered\n\n            console.log('=== 获取分享记录 ===');\n            console.log('推广员ID:', userId, '页码:', page, '筛选类型:', actionType);\n\n            // 构建查询条件\n            let whereCondition = `v.promoter_user_id = ${userId}`;\n\n            // 时间筛选\n            if (startDate && endDate) {\n                const startTime = parseInt(new Date(startDate).getTime() / 1000);\n                const endTime = parseInt(new Date(endDate).getTime() / 1000);\n                whereCondition += ` AND v.visit_time BETWEEN ${startTime} AND ${endTime}`;\n            }\n\n            // 构建主查询SQL\n            let sql = `\n                SELECT\n                    v.id,\n                    v.promoter_user_id,\n                    v.visitor_user_id,\n                    v.goods_id,\n                    v.visit_time,\n                    v.share_source,\n                    v.ip_address,\n                    v.is_new_visitor,\n                    CASE\n                        WHEN o.id IS NOT NULL THEN 'ordered'\n                        ELSE 'browsed'\n                    END as user_action,\n                    o.id as order_record_id,\n                    o.order_id,\n                    o.order_amount,\n                    o.commission_amount,\n                    o.status as order_status,\n                    g.name as goods_name,\n                    g.list_pic_url as goods_image,\n                    u.nickname as visitor_nickname\n                FROM hiolabs_promotion_visits v\n                LEFT JOIN hiolabs_promotion_orders o\n                    ON v.visitor_user_id = o.buyer_user_id\n                    AND v.goods_id = o.goods_id\n                    AND v.promoter_user_id = o.promoter_user_id\n                LEFT JOIN hiolabs_goods g ON v.goods_id = g.id\n                LEFT JOIN hiolabs_user u ON v.visitor_user_id = u.id\n                WHERE ${whereCondition}\n            `;\n\n            // 根据行为类型筛选\n            if (actionType === 'browsed') {\n                sql += ` HAVING user_action = 'browsed'`;\n            } else if (actionType === 'ordered') {\n                sql += ` HAVING user_action = 'ordered'`;\n            }\n\n            sql += ` ORDER BY v.visit_time DESC`;\n\n            // 分页查询\n            const offset = (page - 1) * pageSize;\n            const limitSql = sql + ` LIMIT ${offset}, ${pageSize}`;\n\n            // 执行查询\n            const records = await this.model().query(limitSql);\n\n            // 查询总数\n            const countSql = `SELECT COUNT(*) as total FROM (${sql}) as temp`;\n            const countResult = await this.model().query(countSql);\n            const total = countResult[0].total;\n\n            // 格式化数据\n            const formattedRecords = records.map(record => {\n                return {\n                    id: record.id,\n                    visitorUserId: record.visitor_user_id,\n                    visitorNickname: record.visitor_nickname || '匿名用户',\n                    goodsId: record.goods_id,\n                    goodsName: record.goods_name,\n                    goodsImage: record.goods_image,\n                    shareSource: record.share_source,\n                    visitTime: record.visit_time,\n                    visitTimeText: this.formatTime(record.visit_time),\n                    ipAddress: record.ip_address,\n                    isNewVisitor: record.is_new_visitor === 1,\n                    userAction: record.user_action,\n                    userActionText: record.user_action === 'ordered' ? '已下单' : '仅浏览',\n                    orderInfo: record.user_action === 'ordered' ? {\n                        orderId: record.order_id,\n                        orderAmount: parseFloat(record.order_amount || 0),\n                        commissionAmount: parseFloat(record.commission_amount || 0),\n                        orderStatus: record.order_status\n                    } : null\n                };\n            });\n\n            // 统计数据\n            const statsResult = await this.getShareRecordsStats(userId, startDate, endDate);\n\n            console.log(`✅ 查询完成: 共${total}条记录`);\n\n            return this.success({\n                records: formattedRecords,\n                pagination: {\n                    page: page,\n                    pageSize: pageSize,\n                    total: total,\n                    totalPages: Math.ceil(total / pageSize)\n                },\n                stats: statsResult\n            });\n\n        } catch (error) {\n            console.error('获取分享记录失败:', error);\n            return this.fail('获取分享记录失败: ' + error.message);\n        }\n    }\n\n    /**\n     * 获取分享记录统计数据\n     */\n    async getShareRecordsStats(userId, startDate = '', endDate = '') {\n        try {\n            let whereCondition = `v.promoter_user_id = ${userId}`;\n\n            // 时间筛选\n            if (startDate && endDate) {\n                const startTime = parseInt(new Date(startDate).getTime() / 1000);\n                const endTime = parseInt(new Date(endDate).getTime() / 1000);\n                whereCondition += ` AND v.visit_time BETWEEN ${startTime} AND ${endTime}`;\n            }\n\n            const statsSql = `\n                SELECT\n                    COUNT(v.id) as total_visits,\n                    COUNT(DISTINCT v.visitor_user_id) as unique_visitors,\n                    COUNT(o.id) as total_orders,\n                    COALESCE(SUM(o.order_amount), 0) as total_order_amount,\n                    COALESCE(SUM(o.commission_amount), 0) as total_commission,\n                    ROUND(COUNT(o.id) * 100.0 / COUNT(v.id), 2) as conversion_rate\n                FROM hiolabs_promotion_visits v\n                LEFT JOIN hiolabs_promotion_orders o\n                    ON v.visitor_user_id = o.buyer_user_id\n                    AND v.goods_id = o.goods_id\n                    AND v.promoter_user_id = o.promoter_user_id\n                WHERE ${whereCondition}\n            `;\n\n            const statsResult = await this.model().query(statsSql);\n            const stats = statsResult[0];\n\n            return {\n                totalVisits: parseInt(stats.total_visits),           // 总访问次数\n                uniqueVisitors: parseInt(stats.unique_visitors),     // 独立访客数\n                totalOrders: parseInt(stats.total_orders),           // 总下单数\n                browsedOnly: parseInt(stats.total_visits) - parseInt(stats.total_orders), // 仅浏览数\n                totalOrderAmount: parseFloat(stats.total_order_amount), // 总订单金额\n                totalCommission: parseFloat(stats.total_commission),    // 总佣金\n                conversionRate: parseFloat(stats.conversion_rate)       // 转化率\n            };\n\n        } catch (error) {\n            console.error('获取统计数据失败:', error);\n            return {\n                totalVisits: 0,\n                uniqueVisitors: 0,\n                totalOrders: 0,\n                browsedOnly: 0,\n                totalOrderAmount: 0,\n                totalCommission: 0,\n                conversionRate: 0\n            };\n        }\n    }\n\n    /**\n     * 获取推广数据中心信息\n     * GET /api/promotion/dataCenter\n     */\n    async dataCenterAction() {\n        try {\n            const userId = await this.getLoginUserId();\n            const timeFilter = this.get('timeFilter') || 'month'; // today, week, month, all\n\n            console.log('=== 获取推广数据中心信息 ===');\n            console.log('用户ID:', userId, '时间筛选:', timeFilter);\n\n            // 获取时间范围\n            const timeRange = this.getTimeRange(timeFilter);\n\n            // 并行获取各种数据\n            const [\n                promotionData,\n                currentData,\n                teamData,\n                rankingList,\n                recentOrders\n            ] = await Promise.all([\n                this.getPromotionOverview(userId),\n                this.getCurrentPeriodData(userId, timeRange),\n                this.getTeamData(userId),\n                this.getRankingData(userId),\n                this.getRecentOrders(userId)\n            ]);\n\n            console.log('✅ 数据中心信息获取完成');\n\n            return this.success({\n                promotionData,\n                currentData,\n                teamData,\n                rankingList,\n                recentOrders,\n                timeFilter,\n                timePeriodText: this.getTimePeriodText(timeFilter)\n            });\n\n        } catch (error) {\n            console.error('获取推广数据中心信息失败:', error);\n            return this.fail('获取数据失败: ' + error.message);\n        }\n    }\n\n    /**\n     * 获取推广总览数据\n     */\n    async getPromotionOverview(userId) {\n        try {\n            // 获取用户佣金信息\n            const commissionInfo = await this.model('hiolabs_user_commission').where({\n                user_id: userId\n            }).find();\n\n            // 获取推广订单统计\n            const orderStats = await this.model('hiolabs_promotion_orders').where({\n                promoter_user_id: userId,\n                status: ['!=', 'cancelled']\n            }).field('COUNT(*) as total_orders, SUM(order_amount) as total_amount, SUM(commission_amount) as total_commission').find();\n\n            // 获取团队人数（直接下级 + 间接下级）\n            const teamCount = await this.getTeamMemberCount(userId);\n\n            return {\n                totalOrders: parseInt(orderStats.total_orders || 0),\n                totalAmount: parseFloat(orderStats.total_amount || 0).toFixed(2),\n                totalCommission: parseFloat(commissionInfo.total_commission || 0).toFixed(2),\n                teamCount: teamCount\n            };\n\n        } catch (error) {\n            console.error('获取推广总览数据失败:', error);\n            return {\n                totalOrders: 0,\n                totalAmount: '0.00',\n                totalCommission: '0.00',\n                teamCount: 0\n            };\n        }\n    }\n\n    /**\n     * 获取当前时间段数据\n     */\n    async getCurrentPeriodData(userId, timeRange) {\n        try {\n            const whereCondition = {\n                promoter_user_id: userId,\n                status: ['!=', 'cancelled']\n            };\n\n            if (timeRange.start && timeRange.end) {\n                whereCondition.create_time = ['between', [timeRange.start, timeRange.end]];\n            }\n\n            // 获取订单数据\n            const orderStats = await this.model('hiolabs_promotion_orders').where(whereCondition)\n                .field('COUNT(*) as orders, SUM(order_amount) as amount, SUM(commission_amount) as commission')\n                .find();\n\n            // 获取访问数据\n            const visitStats = await this.model('hiolabs_promotion_visits').where({\n                promoter_user_id: userId,\n                visit_time: timeRange.start && timeRange.end ? ['between', [timeRange.start, timeRange.end]] : ['>', 0]\n            }).field('COUNT(DISTINCT visitor_user_id) as visitors').find();\n\n            return {\n                orders: parseInt(orderStats.orders || 0),\n                amount: parseFloat(orderStats.amount || 0).toFixed(2),\n                commission: parseFloat(orderStats.commission || 0).toFixed(2),\n                visitors: parseInt(visitStats.visitors || 0)\n            };\n\n        } catch (error) {\n            console.error('获取当前时间段数据失败:', error);\n            return {\n                orders: 0,\n                amount: '0.00',\n                commission: '0.00',\n                visitors: 0\n            };\n        }\n    }\n\n    /**\n     * 获取团队数据\n     */\n    async getTeamData(userId) {\n        try {\n            // 获取直接下级数量\n            const directCount = await this.model('hiolabs_promotion_orders').where({\n                parent_promoter_user_id: userId\n            }).field('COUNT(DISTINCT promoter_user_id) as count').find();\n\n            // 获取团队总人数（包括间接下级）\n            const totalCount = await this.getTeamMemberCount(userId);\n\n            // 获取团队订单数据\n            const teamOrderStats = await this.getTeamOrderStats(userId);\n\n            return {\n                directCount: parseInt(directCount.count || 0),\n                totalCount: totalCount,\n                teamOrders: teamOrderStats.orders,\n                teamCommission: teamOrderStats.commission\n            };\n\n        } catch (error) {\n            console.error('获取团队数据失败:', error);\n            return {\n                directCount: 0,\n                totalCount: 0,\n                teamOrders: 0,\n                teamCommission: '0.00'\n            };\n        }\n    }\n\n    /**\n     * 获取排行榜数据\n     */\n    async getRankingData(userId) {\n        try {\n            // 获取本月推广排行榜（前5名）\n            const currentMonth = this.getCurrentMonthRange();\n\n            const rankingList = await this.model('hiolabs_promotion_orders')\n                .alias('po')\n                .join({\n                    table: 'hiolabs_user',\n                    join: 'left',\n                    as: 'u',\n                    on: ['po.promoter_user_id', 'u.id']\n                })\n                .where({\n                    'po.status': ['!=', 'cancelled'],\n                    'po.create_time': ['between', [currentMonth.start, currentMonth.end]]\n                })\n                .field('po.promoter_user_id as user_id, u.nickname, u.avatar, COUNT(*) as order_count, SUM(po.commission_amount) as commission')\n                .group('po.promoter_user_id')\n                .order('commission DESC')\n                .limit(5)\n                .select();\n\n            return rankingList.map(item => ({\n                id: item.user_id,\n                nickname: this.decodeNickname(item.nickname) || `用户${item.user_id}`,\n                avatar: item.avatar || '',\n                orderCount: parseInt(item.order_count),\n                commission: parseFloat(item.commission || 0).toFixed(2)\n            }));\n\n        } catch (error) {\n            console.error('获取排行榜数据失败:', error);\n            return [];\n        }\n    }\n\n    /**\n     * 获取最近订单\n     */\n    async getRecentOrders(userId) {\n        try {\n            const recentOrders = await this.model('hiolabs_promotion_orders').where({\n                promoter_user_id: userId,\n                status: ['!=', 'cancelled']\n            })\n            .field('id, goods_name, order_amount, commission_amount, create_time')\n            .order('create_time DESC')\n            .limit(5)\n            .select();\n\n            return recentOrders.map(order => ({\n                id: order.id,\n                goodsName: order.goods_name,\n                orderAmount: parseFloat(order.order_amount).toFixed(2),\n                commission: parseFloat(order.commission_amount).toFixed(2),\n                createTime: this.formatTime(order.create_time)\n            }));\n\n        } catch (error) {\n            console.error('获取最近订单失败:', error);\n            return [];\n        }\n    }\n\n    /**\n     * 格式化时间戳\n     */\n    formatTime(timestamp) {\n        const date = new Date(timestamp * 1000);\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        const hour = String(date.getHours()).padStart(2, '0');\n        const minute = String(date.getMinutes()).padStart(2, '0');\n\n        return `${year}-${month}-${day} ${hour}:${minute}`;\n    }\n\n    /**\n     * 获取时间范围\n     */\n    getTimeRange(timeFilter) {\n        const now = new Date();\n        let start, end;\n\n        switch(timeFilter) {\n            case 'today':\n                start = parseInt(new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime() / 1000);\n                end = parseInt(new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59).getTime() / 1000);\n                break;\n            case 'week':\n                const weekStart = new Date(now);\n                weekStart.setDate(now.getDate() - now.getDay());\n                weekStart.setHours(0, 0, 0, 0);\n                start = parseInt(weekStart.getTime() / 1000);\n                end = parseInt(now.getTime() / 1000);\n                break;\n            case 'month':\n                start = parseInt(new Date(now.getFullYear(), now.getMonth(), 1).getTime() / 1000);\n                end = parseInt(now.getTime() / 1000);\n                break;\n            case 'all':\n            default:\n                start = null;\n                end = null;\n                break;\n        }\n\n        return { start, end };\n    }\n\n    /**\n     * 获取当前月份范围\n     */\n    getCurrentMonthRange() {\n        const now = new Date();\n        const start = parseInt(new Date(now.getFullYear(), now.getMonth(), 1).getTime() / 1000);\n        const end = parseInt(new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59).getTime() / 1000);\n        return { start, end };\n    }\n\n    /**\n     * 获取时间段文本\n     */\n    getTimePeriodText(timeFilter) {\n        const textMap = {\n            'today': '今日',\n            'week': '本周',\n            'month': '本月',\n            'all': '全部'\n        };\n        return textMap[timeFilter] || '本月';\n    }\n\n    /**\n     * 获取团队成员数量（递归计算）\n     */\n    async getTeamMemberCount(userId) {\n        try {\n            // 获取直接下级\n            const directMembers = await this.model('hiolabs_promotion_orders').where({\n                parent_promoter_user_id: userId\n            }).field('DISTINCT promoter_user_id as user_id').select();\n\n            let totalCount = directMembers.length;\n\n            // 递归获取间接下级\n            for (let member of directMembers) {\n                const subCount = await this.getTeamMemberCount(member.user_id);\n                totalCount += subCount;\n            }\n\n            return totalCount;\n\n        } catch (error) {\n            console.error('获取团队成员数量失败:', error);\n            return 0;\n        }\n    }\n\n    /**\n     * 获取团队订单统计\n     */\n    async getTeamOrderStats(userId) {\n        try {\n            // 获取所有团队成员ID\n            const teamMemberIds = await this.getAllTeamMemberIds(userId);\n\n            if (teamMemberIds.length === 0) {\n                return { orders: 0, commission: '0.00' };\n            }\n\n            // 获取团队订单统计\n            const stats = await this.model('hiolabs_promotion_orders').where({\n                promoter_user_id: ['IN', teamMemberIds],\n                status: ['!=', 'cancelled']\n            }).field('COUNT(*) as orders, SUM(commission_amount) as commission').find();\n\n            return {\n                orders: parseInt(stats.orders || 0),\n                commission: parseFloat(stats.commission || 0).toFixed(2)\n            };\n\n        } catch (error) {\n            console.error('获取团队订单统计失败:', error);\n            return { orders: 0, commission: '0.00' };\n        }\n    }\n\n    /**\n     * 获取所有团队成员ID（递归）\n     */\n    async getAllTeamMemberIds(userId, visited = new Set()) {\n        if (visited.has(userId)) {\n            return [];\n        }\n        visited.add(userId);\n\n        try {\n            // 获取直接下级\n            const directMembers = await this.model('hiolabs_promotion_orders').where({\n                parent_promoter_user_id: userId\n            }).field('DISTINCT promoter_user_id as user_id').select();\n\n            let allMemberIds = directMembers.map(m => m.user_id);\n\n            // 递归获取间接下级\n            for (let member of directMembers) {\n                const subMemberIds = await this.getAllTeamMemberIds(member.user_id, visited);\n                allMemberIds = allMemberIds.concat(subMemberIds);\n            }\n\n            return allMemberIds;\n\n        } catch (error) {\n            console.error('获取团队成员ID失败:', error);\n            return [];\n        }\n    }\n\n    /**\n     * 解码昵称（base64）\n     */\n    decodeNickname(nickname) {\n        if (!nickname) return null;\n        try {\n            return Buffer.from(nickname, 'base64').toString('utf8');\n        } catch (error) {\n            return nickname;\n        }\n    }\n};\n"]}