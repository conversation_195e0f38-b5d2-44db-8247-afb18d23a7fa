{"version": 3, "sources": ["..\\..\\..\\src\\admin\\service\\token.js"], "names": ["jwt", "require", "secret", "moment", "rp", "fs", "http", "module", "exports", "think", "Service", "getUserId", "token", "result", "parse", "isEmpty", "user_id", "getUserInfo", "userId", "userInfo", "model", "where", "id", "find", "create", "sign", "verify", "err", "getAccessToken", "options", "method", "url", "qs", "grant_type", "config", "appid", "sessionData", "JSON", "access_token"], "mappings": ";;AAAA,MAAMA,MAAMC,QAAQ,cAAR,CAAZ;AACA,MAAMC,SAAS,uBAAf;;AAEA,MAAMC,SAASF,QAAQ,QAAR,CAAf;AACA,MAAMG,KAAKH,QAAQ,iBAAR,CAAX;AACA,MAAMI,KAAKJ,QAAQ,IAAR,CAAX;AACA,MAAMK,OAAOL,QAAQ,MAAR,CAAb;;AAEAM,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,OAApB,CAA4B;AACzC;;;AAGMC,aAAN,GAAkB;AAAA;;AAAA;AACd,kBAAMC,QAAQH,MAAMG,KAApB;AACA,gBAAI,CAACA,KAAL,EAAY;AACR,uBAAO,CAAP;AACH;;AAED,kBAAMC,SAAS,MAAM,MAAKC,KAAL,EAArB;AACA,gBAAIL,MAAMM,OAAN,CAAcF,MAAd,KAAyBA,OAAOG,OAAP,IAAkB,CAA/C,EAAkD;AAC9C,uBAAO,CAAP;AACH;;AAED,mBAAOH,OAAOG,OAAd;AAXc;AAYjB;;AAED;;;AAGMC,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAMC,SAAS,MAAM,OAAKP,SAAL,EAArB;AACA,gBAAIO,UAAU,CAAd,EAAiB;AACb,uBAAO,IAAP;AACH;;AAED,kBAAMC,WAAW,MAAM,OAAKC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B,EAACC,IAAIJ,MAAL,EAA1B,EAAwCK,IAAxC,EAAvB;;AAEA,mBAAOd,MAAMM,OAAN,CAAcI,QAAd,IAA0B,IAA1B,GAAiCA,QAAxC;AARgB;AASnB;;AAEKK,UAAN,CAAaL,QAAb,EAAuB;AAAA;AACnB,kBAAMP,QAAQZ,IAAIyB,IAAJ,CAASN,QAAT,EAAmBjB,MAAnB,CAAd;AACA,mBAAOU,KAAP;AAFmB;AAGtB;;AAEKE,SAAN,GAAc;AAAA;AACV,gBAAIL,MAAMG,KAAV,EAAiB;AACb,oBAAI;AACA,2BAAOZ,IAAI0B,MAAJ,CAAWjB,MAAMG,KAAjB,EAAwBV,MAAxB,CAAP;AACH,iBAFD,CAEE,OAAOyB,GAAP,EAAY;AACV,2BAAO,IAAP;AACH;AACJ;AACD,mBAAO,IAAP;AARU;AASb;;AAEKD,UAAN,GAAe;AAAA;;AAAA;AACX,kBAAMb,SAAS,MAAM,OAAKC,KAAL,EAArB;AACA,gBAAIL,MAAMM,OAAN,CAAcF,MAAd,CAAJ,EAA2B;AACvB,uBAAO,KAAP;AACH;;AAED,mBAAO,IAAP;AANW;AAOd;;AAEKe,kBAAN,GAAuB;AAAA;AACnB,kBAAMC,UAAU;AACZC,wBAAQ,MADI;AAEZC,qBAAK,yCAFO;AAGZC,oBAAI;AACAC,gCAAY,mBADZ;AAEA/B,4BAAQO,MAAMyB,MAAN,CAAa,eAAb,CAFR;AAGAC,2BAAO1B,MAAMyB,MAAN,CAAa,cAAb;AAHP;AAHQ,aAAhB;AASA,gBAAIE,cAAc,MAAMhC,GAAGyB,OAAH,CAAxB;AACAO,0BAAcC,KAAKvB,KAAL,CAAWsB,WAAX,CAAd;AACA,gBAAIxB,QAAQwB,YAAYE,YAAxB;AACA,mBAAO1B,KAAP;AAbmB;AActB;AAvEwC,CAA7C", "file": "..\\..\\..\\src\\admin\\service\\token.js", "sourcesContent": ["const jwt = require('jsonwebtoken');\nconst secret = 'SLDLKKDS323ssdd@#@@gf';\n\nconst moment = require('moment');\nconst rp = require('request-promise');\nconst fs = require('fs');\nconst http = require(\"http\");\n\nmodule.exports = class extends think.Service {\n    /**\n     * 根据header中的x-hioshop-token值获取用户id\n     */\n    async getUserId() {\n        const token = think.token;\n        if (!token) {\n            return 0;\n        }\n\n        const result = await this.parse();\n        if (think.isEmpty(result) || result.user_id <= 0) {\n            return 0;\n        }\n\n        return result.user_id;\n    }\n\n    /**\n     * 根据值获取用户信息\n     */\n    async getUserInfo() {\n        const userId = await this.getUserId();\n        if (userId <= 0) {\n            return null;\n        }\n\n        const userInfo = await this.model('admin').where({id: userId}).find();\n\n        return think.isEmpty(userInfo) ? null : userInfo;\n    }\n\n    async create(userInfo) {\n        const token = jwt.sign(userInfo, secret);\n        return token;\n    }\n\n    async parse() {\n        if (think.token) {\n            try {\n                return jwt.verify(think.token, secret);\n            } catch (err) {\n                return null;\n            }\n        }\n        return null;\n    }\n\n    async verify() {\n        const result = await this.parse();\n        if (think.isEmpty(result)) {\n            return false;\n        }\n\n        return true;\n    }\n\n    async getAccessToken() {\n        const options = {\n            method: 'POST',\n            url: 'https://api.weixin.qq.com/cgi-bin/token',\n            qs: {\n                grant_type: 'client_credential',\n                secret: think.config('weixin.secret'),\n                appid: think.config('weixin.appid')\n            }\n        };\n        let sessionData = await rp(options);\n        sessionData = JSON.parse(sessionData);\n        let token = sessionData.access_token;\n        return token;\n    }\n};\n"]}