{"version": 3, "sources": ["..\\..\\..\\src\\api\\model\\signin.js"], "names": ["module", "exports", "think", "Model", "tableName", "getUserSignStats", "userId", "totalSignDays", "where", "user_id", "count", "currentMonth", "getCurrentMonth", "monthSignDays", "sign_date", "consecutiveDays", "getConsecutiveDays", "maxConsecutiveDays", "getMaxConsecutiveDays", "error", "console", "today", "getCurrentDate", "yesterday", "getDateBefore", "yesterdayRecord", "find", "isEmpty", "checkDate", "i", "record", "records", "field", "order", "select", "length", "maxConsecutive", "currentConsecutive", "prevDate", "Date", "currDate", "diffTime", "diffDays", "Math", "max", "getMonthSignRecords", "month", "startDate", "endDate", "checkTodaySigned", "getSignRanking", "limit", "sql", "rankings", "query", "getSystemStats", "todaySignCount", "monthSignCount", "group", "totalSignCount", "totalUserCount", "model", "signRate", "toFixed", "now", "year", "getFullYear", "String", "getMonth", "padStart", "day", "getDate", "dateStr", "days", "date", "setDate"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;;AAEzC,MAAIC,SAAJ,GAAgB;AACd,WAAO,iBAAP;AACD;;AAED;;;AAGMC,kBAAN,CAAuBC,MAAvB,EAA+B;AAAA;;AAAA;AAC7B,UAAI;AACF;AACA,cAAMC,gBAAgB,MAAM,MAAKC,KAAL,CAAW;AACrCC,mBAASH;AAD4B,SAAX,EAEzBI,KAFyB,EAA5B;;AAIA;AACA,cAAMC,eAAe,MAAKC,eAAL,EAArB;AACA,cAAMC,gBAAgB,MAAM,MAAKL,KAAL,CAAW;AACrCC,mBAASH,MAD4B;AAErCQ,qBAAW,CAAC,MAAD,EAAU,GAAEH,YAAa,GAAzB;AAF0B,SAAX,EAGzBD,KAHyB,EAA5B;;AAKA;AACA,cAAMK,kBAAkB,MAAM,MAAKC,kBAAL,CAAwBV,MAAxB,CAA9B;;AAEA;AACA,cAAMW,qBAAqB,MAAM,MAAKC,qBAAL,CAA2BZ,MAA3B,CAAjC;;AAEA,eAAO;AACLC,yBAAeA,aADV;AAELM,yBAAeA,aAFV;AAGLE,2BAAiBA,eAHZ;AAILE,8BAAoBA;AAJf,SAAP;AAMD,OAzBD,CAyBE,OAAOE,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,eAAO,IAAP;AACD;AA7B4B;AA8B9B;;AAED;;;AAGMH,oBAAN,CAAyBV,MAAzB,EAAiC;AAAA;;AAAA;AAC/B,YAAMe,QAAQ,OAAKC,cAAL,EAAd;AACA,YAAMC,YAAY,OAAKC,aAAL,CAAmBH,KAAnB,EAA0B,CAA1B,CAAlB;;AAEA;AACA,YAAMI,kBAAkB,MAAM,OAAKjB,KAAL,CAAW;AACvCC,iBAASH,MAD8B;AAEvCQ,mBAAWS;AAF4B,OAAX,EAG3BG,IAH2B,EAA9B;;AAKA,UAAIxB,MAAMyB,OAAN,CAAcF,eAAd,CAAJ,EAAoC;AAClC,eAAO,CAAP;AACD;;AAED;AACA,UAAIV,kBAAkB,CAAtB;AACA,UAAIa,YAAYL,SAAhB;;AAEA,WAAK,IAAIM,IAAI,CAAb,EAAgBA,IAAI,GAApB,EAAyBA,GAAzB,EAA8B;AAC5B,cAAMC,SAAS,MAAM,OAAKtB,KAAL,CAAW;AAC9BC,mBAASH,MADqB;AAE9BQ,qBAAWc;AAFmB,SAAX,EAGlBF,IAHkB,EAArB;;AAKA,YAAI,CAACxB,MAAMyB,OAAN,CAAcG,MAAd,CAAL,EAA4B;AAC1Bf;AACAa,sBAAY,OAAKJ,aAAL,CAAmBI,SAAnB,EAA8B,CAA9B,CAAZ;AACD,SAHD,MAGO;AACL;AACD;AACF;;AAED,aAAOb,eAAP;AAhC+B;AAiChC;;AAED;;;AAGMG,uBAAN,CAA4BZ,MAA5B,EAAoC;AAAA;;AAAA;AAClC,UAAI;AACF;AACA,cAAMyB,UAAU,MAAM,OAAKvB,KAAL,CAAW;AAC/BC,mBAASH;AADsB,SAAX,EAEnB0B,KAFmB,CAEb,WAFa,EAEAC,KAFA,CAEM,eAFN,EAEuBC,MAFvB,EAAtB;;AAIA,YAAIH,QAAQI,MAAR,KAAmB,CAAvB,EAA0B;AACxB,iBAAO,CAAP;AACD;;AAED,YAAIC,iBAAiB,CAArB;AACA,YAAIC,qBAAqB,CAAzB;;AAEA,aAAK,IAAIR,IAAI,CAAb,EAAgBA,IAAIE,QAAQI,MAA5B,EAAoCN,GAApC,EAAyC;AACvC,gBAAMS,WAAW,IAAIC,IAAJ,CAASR,QAAQF,IAAI,CAAZ,EAAef,SAAxB,CAAjB;AACA,gBAAM0B,WAAW,IAAID,IAAJ,CAASR,QAAQF,CAAR,EAAWf,SAApB,CAAjB;;AAEA;AACA,gBAAM2B,WAAWD,WAAWF,QAA5B;AACA,gBAAMI,WAAWD,YAAY,OAAO,EAAP,GAAY,EAAZ,GAAiB,EAA7B,CAAjB;;AAEA,cAAIC,aAAa,CAAjB,EAAoB;AAClB;AACAL;AACAD,6BAAiBO,KAAKC,GAAL,CAASR,cAAT,EAAyBC,kBAAzB,CAAjB;AACD,WAJD,MAIO;AACL;AACAA,iCAAqB,CAArB;AACD;AACF;;AAED,eAAOD,cAAP;AACD,OAhCD,CAgCE,OAAOjB,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,eAAd,EAA+BA,KAA/B;AACA,eAAO,CAAP;AACD;AApCiC;AAqCnC;;AAED;;;AAGM0B,qBAAN,CAA0BvC,MAA1B,EAAkCwC,KAAlC,EAAyC;AAAA;;AAAA;AACvC,UAAI;AACF,cAAMC,YAAa,GAAED,KAAM,KAA3B;AACA,cAAME,UAAW,GAAEF,KAAM,KAAzB;;AAEA,cAAMf,UAAU,MAAM,OAAKvB,KAAL,CAAW;AAC/BC,mBAASH,MADsB;AAE/BQ,qBAAW,CAAC,SAAD,EAAYiC,SAAZ,EAAuBC,OAAvB;AAFoB,SAAX,EAGnBhB,KAHmB,CAGb,4CAHa,EAGiCC,KAHjC,CAGuC,eAHvC,EAGwDC,MAHxD,EAAtB;;AAKA,eAAOH,OAAP;AACD,OAVD,CAUE,OAAOZ,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,eAAO,EAAP;AACD;AAdsC;AAexC;;AAED;;;AAGM8B,kBAAN,CAAuB3C,MAAvB,EAA+B;AAAA;;AAAA;AAC7B,YAAMe,QAAQ,OAAKC,cAAL,EAAd;;AAEA,YAAMQ,SAAS,MAAM,OAAKtB,KAAL,CAAW;AAC9BC,iBAASH,MADqB;AAE9BQ,mBAAWO;AAFmB,OAAX,EAGlBK,IAHkB,EAArB;;AAKA,aAAO,CAACxB,MAAMyB,OAAN,CAAcG,MAAd,CAAR;AAR6B;AAS9B;;AAED;;;AAGMoB,gBAAN,CAAqBC,QAAQ,EAA7B,EAAiC;AAAA;;AAAA;AAC/B,UAAI;AACF;AACA,cAAMC,MAAO;;;;;;;;;;;gBAWHD,KAAM;OAXhB;;AAcA,cAAME,WAAW,MAAM,OAAKC,KAAL,CAAWF,GAAX,CAAvB;AACA,eAAOC,QAAP;AACD,OAlBD,CAkBE,OAAOlC,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,YAAd,EAA4BA,KAA5B;AACA,eAAO,EAAP;AACD;AAtB8B;AAuBhC;;AAED;;;AAGMoC,gBAAN,GAAuB;AAAA;;AAAA;AACrB,UAAI;AACF,cAAMlC,QAAQ,OAAKC,cAAL,EAAd;AACA,cAAMX,eAAe,OAAKC,eAAL,EAArB;;AAEA;AACA,cAAM4C,iBAAiB,MAAM,OAAKhD,KAAL,CAAW;AACtCM,qBAAWO;AAD2B,SAAX,EAE1BX,KAF0B,EAA7B;;AAIA;AACA,cAAM+C,iBAAiB,MAAM,OAAKjD,KAAL,CAAW;AACtCM,qBAAW,CAAC,MAAD,EAAU,GAAEH,YAAa,GAAzB;AAD2B,SAAX,EAE1B+C,KAF0B,CAEpB,SAFoB,EAEThD,KAFS,EAA7B;;AAIA;AACA,cAAMiD,iBAAiB,MAAM,OAAKjD,KAAL,EAA7B;;AAEA;AACA,cAAMkD,iBAAiB,MAAM,OAAKC,KAAL,CAAW,MAAX,EAAmBnD,KAAnB,EAA7B;;AAEA,eAAO;AACL8C,0BAAgBA,cADX;AAELC,0BAAgBA,cAFX;AAGLE,0BAAgBA,cAHX;AAILC,0BAAgBA,cAJX;AAKLE,oBAAUF,iBAAiB,CAAjB,GAAqB,CAACH,iBAAiBG,cAAjB,GAAkC,GAAnC,EAAwCG,OAAxC,CAAgD,CAAhD,CAArB,GAA0E;AAL/E,SAAP;AAOD,OA3BD,CA2BE,OAAO5C,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,eAAO,IAAP;AACD;AA/BoB;AAgCtB;;AAED;;;AAGAP,oBAAkB;AAChB,UAAMoD,MAAM,IAAIzB,IAAJ,EAAZ;AACA,UAAM0B,OAAOD,IAAIE,WAAJ,EAAb;AACA,UAAMpB,QAAQqB,OAAOH,IAAII,QAAJ,KAAiB,CAAxB,EAA2BC,QAA3B,CAAoC,CAApC,EAAuC,GAAvC,CAAd;AACA,WAAQ,GAAEJ,IAAK,IAAGnB,KAAM,EAAxB;AACD;;AAED;;;AAGAxB,mBAAiB;AACf,UAAM0C,MAAM,IAAIzB,IAAJ,EAAZ;AACA,UAAM0B,OAAOD,IAAIE,WAAJ,EAAb;AACA,UAAMpB,QAAQqB,OAAOH,IAAII,QAAJ,KAAiB,CAAxB,EAA2BC,QAA3B,CAAoC,CAApC,EAAuC,GAAvC,CAAd;AACA,UAAMC,MAAMH,OAAOH,IAAIO,OAAJ,EAAP,EAAsBF,QAAtB,CAA+B,CAA/B,EAAkC,GAAlC,CAAZ;AACA,WAAQ,GAAEJ,IAAK,IAAGnB,KAAM,IAAGwB,GAAI,EAA/B;AACD;;AAED;;;AAGA9C,gBAAcgD,OAAd,EAAuBC,IAAvB,EAA6B;AAC3B,UAAMC,OAAO,IAAInC,IAAJ,CAASiC,OAAT,CAAb;AACAE,SAAKC,OAAL,CAAaD,KAAKH,OAAL,KAAiBE,IAA9B;;AAEA,UAAMR,OAAOS,KAAKR,WAAL,EAAb;AACA,UAAMpB,QAAQqB,OAAOO,KAAKN,QAAL,KAAkB,CAAzB,EAA4BC,QAA5B,CAAqC,CAArC,EAAwC,GAAxC,CAAd;AACA,UAAMC,MAAMH,OAAOO,KAAKH,OAAL,EAAP,EAAuBF,QAAvB,CAAgC,CAAhC,EAAmC,GAAnC,CAAZ;AACA,WAAQ,GAAEJ,IAAK,IAAGnB,KAAM,IAAGwB,GAAI,EAA/B;AACD;AA5PwC,CAA3C", "file": "..\\..\\..\\src\\api\\model\\signin.js", "sourcesContent": ["module.exports = class extends think.Model {\n\n  get tableName() {\n    return 'sign_in_records';\n  }\n  \n  /**\n   * 获取用户的签到统计信息\n   */\n  async getUserSignStats(userId) {\n    try {\n      // 获取总签到天数\n      const totalSignDays = await this.where({\n        user_id: userId\n      }).count();\n\n      // 获取本月签到天数\n      const currentMonth = this.getCurrentMonth();\n      const monthSignDays = await this.where({\n        user_id: userId,\n        sign_date: ['LIKE', `${currentMonth}%`]\n      }).count();\n\n      // 获取连续签到天数\n      const consecutiveDays = await this.getConsecutiveDays(userId);\n\n      // 获取历史最长连续签到\n      const maxConsecutiveDays = await this.getMaxConsecutiveDays(userId);\n\n      return {\n        totalSignDays: totalSignDays,\n        monthSignDays: monthSignDays,\n        consecutiveDays: consecutiveDays,\n        maxConsecutiveDays: maxConsecutiveDays\n      };\n    } catch (error) {\n      console.error('获取用户签到统计失败:', error);\n      return null;\n    }\n  }\n\n  /**\n   * 获取用户连续签到天数\n   */\n  async getConsecutiveDays(userId) {\n    const today = this.getCurrentDate();\n    const yesterday = this.getDateBefore(today, 1);\n    \n    // 检查昨天是否签到\n    const yesterdayRecord = await this.where({\n      user_id: userId,\n      sign_date: yesterday\n    }).find();\n\n    if (think.isEmpty(yesterdayRecord)) {\n      return 0;\n    }\n\n    // 从昨天开始往前计算\n    let consecutiveDays = 0;\n    let checkDate = yesterday;\n\n    for (let i = 0; i < 365; i++) {\n      const record = await this.where({\n        user_id: userId,\n        sign_date: checkDate\n      }).find();\n\n      if (!think.isEmpty(record)) {\n        consecutiveDays++;\n        checkDate = this.getDateBefore(checkDate, 1);\n      } else {\n        break;\n      }\n    }\n\n    return consecutiveDays;\n  }\n\n  /**\n   * 获取历史最长连续签到天数\n   */\n  async getMaxConsecutiveDays(userId) {\n    try {\n      // 获取用户所有签到记录，按日期排序\n      const records = await this.where({\n        user_id: userId\n      }).field('sign_date').order('sign_date ASC').select();\n\n      if (records.length === 0) {\n        return 0;\n      }\n\n      let maxConsecutive = 1;\n      let currentConsecutive = 1;\n\n      for (let i = 1; i < records.length; i++) {\n        const prevDate = new Date(records[i - 1].sign_date);\n        const currDate = new Date(records[i].sign_date);\n        \n        // 计算日期差\n        const diffTime = currDate - prevDate;\n        const diffDays = diffTime / (1000 * 60 * 60 * 24);\n\n        if (diffDays === 1) {\n          // 连续的日期\n          currentConsecutive++;\n          maxConsecutive = Math.max(maxConsecutive, currentConsecutive);\n        } else {\n          // 不连续，重置计数\n          currentConsecutive = 1;\n        }\n      }\n\n      return maxConsecutive;\n    } catch (error) {\n      console.error('获取最长连续签到天数失败:', error);\n      return 0;\n    }\n  }\n\n  /**\n   * 获取用户指定月份的签到记录\n   */\n  async getMonthSignRecords(userId, month) {\n    try {\n      const startDate = `${month}-01`;\n      const endDate = `${month}-31`;\n      \n      const records = await this.where({\n        user_id: userId,\n        sign_date: ['BETWEEN', startDate, endDate]\n      }).field('sign_date, points_earned, consecutive_days').order('sign_date ASC').select();\n\n      return records;\n    } catch (error) {\n      console.error('获取月份签到记录失败:', error);\n      return [];\n    }\n  }\n\n  /**\n   * 检查用户今日是否已签到\n   */\n  async checkTodaySigned(userId) {\n    const today = this.getCurrentDate();\n    \n    const record = await this.where({\n      user_id: userId,\n      sign_date: today\n    }).find();\n\n    return !think.isEmpty(record);\n  }\n\n  /**\n   * 获取签到排行榜\n   */\n  async getSignRanking(limit = 10) {\n    try {\n      // 获取连续签到天数排行\n      const sql = `\n        SELECT \n          sr.user_id,\n          u.nickname,\n          u.avatar,\n          COUNT(*) as total_signs,\n          MAX(sr.consecutive_days) as max_consecutive\n        FROM sign_in_records sr\n        LEFT JOIN user u ON sr.user_id = u.id\n        GROUP BY sr.user_id\n        ORDER BY max_consecutive DESC, total_signs DESC\n        LIMIT ${limit}\n      `;\n\n      const rankings = await this.query(sql);\n      return rankings;\n    } catch (error) {\n      console.error('获取签到排行榜失败:', error);\n      return [];\n    }\n  }\n\n  /**\n   * 获取系统签到统计\n   */\n  async getSystemStats() {\n    try {\n      const today = this.getCurrentDate();\n      const currentMonth = this.getCurrentMonth();\n\n      // 今日签到人数\n      const todaySignCount = await this.where({\n        sign_date: today\n      }).count();\n\n      // 本月签到人数\n      const monthSignCount = await this.where({\n        sign_date: ['LIKE', `${currentMonth}%`]\n      }).group('user_id').count();\n\n      // 总签到次数\n      const totalSignCount = await this.count();\n\n      // 总用户数\n      const totalUserCount = await this.model('user').count();\n\n      return {\n        todaySignCount: todaySignCount,\n        monthSignCount: monthSignCount,\n        totalSignCount: totalSignCount,\n        totalUserCount: totalUserCount,\n        signRate: totalUserCount > 0 ? (monthSignCount / totalUserCount * 100).toFixed(2) : 0\n      };\n    } catch (error) {\n      console.error('获取系统签到统计失败:', error);\n      return null;\n    }\n  }\n\n  /**\n   * 获取当前月份 YYYY-MM\n   */\n  getCurrentMonth() {\n    const now = new Date();\n    const year = now.getFullYear();\n    const month = String(now.getMonth() + 1).padStart(2, '0');\n    return `${year}-${month}`;\n  }\n\n  /**\n   * 获取当前日期 YYYY-MM-DD\n   */\n  getCurrentDate() {\n    const now = new Date();\n    const year = now.getFullYear();\n    const month = String(now.getMonth() + 1).padStart(2, '0');\n    const day = String(now.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n\n  /**\n   * 获取指定日期之前的日期\n   */\n  getDateBefore(dateStr, days) {\n    const date = new Date(dateStr);\n    date.setDate(date.getDate() - days);\n    \n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n};\n"]}