const Base = require('./base.js');
const rp = require('request-promise');
const fs = require('fs');
const http = require("https");
const path = require('path');
// const mineType = require('mime-types');
module.exports = class extends Base {
    async getBase64Action() {
        let goodsId = this.post('goodsId');
        let userId = this.post('userId') || 0;
        let page = "pages/goods/goods";

        // 场景值限制：最多32个字符，建议使用简短格式
        let sceneData = userId > 0 ? `${goodsId},${userId}` : goodsId.toString();

        console.log('=== 生成二维码 ===');
        console.log('商品ID:', goodsId);
        console.log('分享人ID:', userId);
        console.log('场景值:', sceneData);
        console.log('场景值长度:', sceneData.length);

        // 检查场景值长度
        if (sceneData.length > 32) {
            console.log('❌ 场景值超过32字符限制');
            return this.fail(400, '场景值过长');
        }
        const options = {
            method: 'POST',
            url: 'https://api.weixin.qq.com/cgi-bin/token',
            qs: {
                grant_type: 'client_credential',
                secret: think.config('weixin.secret'),
                appid: think.config('weixin.appid')
            }
        };
        let sessionData = await rp(options);
        sessionData = JSON.parse(sessionData);
        let token = sessionData.access_token;
        let data = {
            "scene": sceneData,
            "page": page,
            "width": 516,  // 增加二维码尺寸20%（430 * 1.2 = 516），提高清晰度
            "auto_color": false,
            "line_color": {"r": 0, "g": 0, "b": 0},
            "is_hyaline": false
        };

        console.log('二维码生成参数:', data);
        data = JSON.stringify(data);
        var options2 = {
            method: "POST",
            host: "api.weixin.qq.com",
            path: "/wxa/getwxacodeunlimit?access_token=" + token,
            headers: {
                "Content-Type": "application/json",
                "Content-Length": data.length
            }
        };
        const uploadFunc = async () => {
            return new Promise((resolve, reject) => {
                try {
                    var req = http.request(options2, function(res) {
                        console.log('微信API响应状态码:', res.statusCode);
                        console.log('微信API响应头:', res.headers);

                        if (res.statusCode !== 200) {
                            console.log('❌ 微信API返回错误状态码:', res.statusCode);
                            return resolve(null);
                        }

                        res.setEncoding("base64");
                        var imgData = "";
                        res.on('data', function(chunk) {
                            imgData += chunk;
                        });
                        res.on("end", function() {
                            console.log('✅ 二维码生成成功，数据长度:', imgData.length);
                            return resolve(imgData);
                        });
                        res.on("error", function(error) {
                            console.log('❌ 接收数据时出错:', error);
                            return resolve(null);
                        });
                    });

                    req.on('error', function(error) {
                        console.log('❌ 请求微信API出错:', error);
                        return resolve(null);
                    });

                    req.write(data);
                    req.end();
                } catch (e) {
                    console.log('❌ 二维码生成异常:', e);
                    return resolve(null);
                }
            })
        };

        console.log('开始调用微信API生成二维码...');
        const url = await uploadFunc();

        if (url) {
            console.log('✅ 二维码生成完成');
            return this.success(url);
        } else {
            console.log('❌ 二维码生成失败');
            return this.fail(500, '二维码生成失败');
        }
    }

    /**
     * 生成订单兑换页面的URL Scheme
     */
    async generateOrderExchangeSchemeAction() {
        try {
            // 获取参数
            const query = this.post('query') || '';
            const envVersion = this.post('env_version') || 'release';

            // 获取微信access_token
            const tokenOptions = {
                method: 'POST',
                url: 'https://api.weixin.qq.com/cgi-bin/token',
                qs: {
                    grant_type: 'client_credential',
                    secret: think.config('weixin.secret'),
                    appid: think.config('weixin.appid')
                }
            };

            let sessionData = await rp(tokenOptions);
            sessionData = JSON.parse(sessionData);

            if (!sessionData.access_token) {
                return this.fail('获取微信access_token失败');
            }

            const token = sessionData.access_token;

            // 生成URL Scheme的参数
            const schemeData = {
                jump_wxa: {
                    path: 'pages/order-exchange/index',
                    query: query
                },
                is_expire: false,
                expire_type: 1,
                expire_interval: 30 // 30天有效期
            };

            // 调用微信API生成URL Scheme
            const schemeOptions = {
                method: 'POST',
                url: `https://api.weixin.qq.com/wxa/generatescheme?access_token=${token}`,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(schemeData)
            };

            const schemeResult = await rp(schemeOptions);
            const schemeResponse = JSON.parse(schemeResult);

            if (schemeResponse.errcode === 0) {
                // 同时生成明文URL Scheme作为备选
                const appid = think.config('weixin.appid');
                const path = 'pages/order-exchange/index';
                const encodedQuery = encodeURIComponent(query);

                const plainScheme = `weixin://dl/business/?appid=${appid}&path=${path}&query=${encodedQuery}&env_version=${envVersion}`;

                return this.success({
                    encrypted_scheme: schemeResponse.openlink, // 加密URL Scheme
                    plain_scheme: plainScheme, // 明文URL Scheme
                    expire_time: schemeResponse.expire_time || null
                });
            } else {
                return this.fail(`生成URL Scheme失败: ${schemeResponse.errmsg}`);
            }

        } catch (error) {
            console.error('生成URL Scheme错误:', error);
            return this.fail('生成URL Scheme失败，请稍后重试');
        }
    }

    /**
     * 生成HTTPS跳转链接
     */
    async generateOrderExchangeLinkAction() {
        try {
            // 获取参数
            const orderNumber = this.post('orderNumber') || '';
            const source = this.post('source') || '';
            const utm = this.post('utm') || '';

            // 构建HTTPS跳转链接
            const baseUrl = 'https://your-domain.com/order-exchange-redirect.html'; // 替换为您的域名
            let redirectUrl = baseUrl;

            const params = [];
            if (orderNumber) params.push(`orderNumber=${encodeURIComponent(orderNumber)}`);
            if (source) params.push(`source=${encodeURIComponent(source)}`);
            if (utm) params.push(`utm=${encodeURIComponent(utm)}`);

            if (params.length > 0) {
                redirectUrl += '?' + params.join('&');
            }

            // 同时生成直接的URL Scheme
            const appid = think.config('weixin.appid');
            const path = 'pages/order-exchange/index';

            let query = '';
            if (orderNumber) query += `orderNumber=${encodeURIComponent(orderNumber)}`;
            if (source) {
                query += query ? '&' : '';
                query += `source=${encodeURIComponent(source)}`;
            }
            if (utm) {
                query += query ? '&' : '';
                query += `utm=${encodeURIComponent(utm)}`;
            }

            const directScheme = `weixin://dl/business/?appid=${appid}&path=${path}&query=${encodeURIComponent(query)}&env_version=release`;

            return this.success({
                https_link: redirectUrl,        // HTTPS跳转页面
                direct_scheme: directScheme,    // 直接URL Scheme
                qr_code_data: redirectUrl       // 可用于生成二维码的数据
            });

        } catch (error) {
            console.error('生成跳转链接错误:', error);
            return this.fail('生成跳转链接失败，请稍后重试');
        }
    }

    /**
     * 生成短链接（可选）
     */
    async generateShortLinkAction() {
        try {
            const longUrl = this.post('url');
            if (!longUrl) {
                return this.fail('缺少URL参数');
            }

            // 这里可以集成第三方短链接服务
            const shortCode = this.generateShortCode();
            const shortUrl = `https://your-domain.com/s/${shortCode}`;

            return this.success({
                short_url: shortUrl,
                long_url: longUrl,
                short_code: shortCode
            });

        } catch (error) {
            console.error('生成短链接错误:', error);
            return this.fail('生成短链接失败，请稍后重试');
        }
    }

    /**
     * 生成微信消息中使用的订单兑换链接
     */
    async generateWechatMessageLinkAction() {
        try {
            // 获取参数
            const orderNumber = this.post('orderNumber') || '';
            const source = this.post('source') || 'wechat_message';
            const utm = this.post('utm') || '';
            const userId = this.post('userId') || '';

            const appid = think.config('weixin.appid');
            const path = 'pages/order-exchange/index';

            // 构建query参数
            const queryParams = [];
            if (orderNumber) queryParams.push(`orderNumber=${encodeURIComponent(orderNumber)}`);
            if (source) queryParams.push(`source=${encodeURIComponent(source)}`);
            if (utm) queryParams.push(`utm=${encodeURIComponent(utm)}`);
            if (userId) queryParams.push(`userId=${encodeURIComponent(userId)}`);

            const query = queryParams.join('&');
            const encodedQuery = encodeURIComponent(query);

            // 生成明文URL Scheme（适合微信消息）
            const wechatScheme = `weixin://dl/business/?appid=${appid}&path=${path}&query=${encodedQuery}&env_version=release`;

            // 生成短链接（可选，用于短信等场景）
            const shortCode = this.generateShortCode();
            const shortUrl = `https://your-domain.com/go/${shortCode}`;

            return this.success({
                wechat_scheme: wechatScheme,     // 微信消息中使用的链接
                short_url: shortUrl,            // 短链接（可选）
                original_params: {              // 原始参数（调试用）
                    orderNumber,
                    source,
                    utm,
                    userId
                },
                usage_tips: [
                    '在微信消息中直接使用 wechat_scheme',
                    '在短信中可以使用 short_url',
                    '用户点击后会直接打开小程序订单兑换页面'
                ]
            });

        } catch (error) {
            console.error('生成微信消息链接错误:', error);
            return this.fail('生成链接失败，请稍后重试');
        }
    }

    /**
     * 生成短代码
     */
    generateShortCode() {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 6; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
}