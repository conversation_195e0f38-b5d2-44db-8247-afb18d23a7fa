{"version": 3, "sources": ["..\\..\\..\\src\\api\\model\\goods.js"], "names": ["module", "exports", "think", "Model", "getProductList", "goodsId", "goods", "model", "where", "goods_id", "is_delete", "select", "getSpecificationList", "info", "item", "product", "goods_specification_ids", "id", "find", "goods_number", "spec_id", "specification_id", "specification", "name", "data", "valueList"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;AACvC;;;;;AAKMC,kBAAN,CAAqBC,OAArB,EAA8B;AAAA;;AAAA;AAC1B,kBAAMC,QAAQ,MAAM,MAAKC,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B,EAACC,UAAUJ,OAAX,EAAmBK,WAAU,CAA7B,EAA5B,EAA6DC,MAA7D,EAApB;AACA,mBAAOL,KAAP;AAF0B;AAG7B;;AAED;;;;;AAKMM,wBAAN,CAA2BP,OAA3B,EAAoC;AAAA;;AAAA;AAChC;AACA,gBAAIQ,OAAO,MAAM,OAAKN,KAAL,CAAW,qBAAX,EAAkCC,KAAlC,CAAwC,EAACC,UAASJ,OAAV,EAAkBK,WAAU,CAA5B,EAAxC,EAAwEC,MAAxE,EAAjB;AACA,iBAAI,MAAMG,IAAV,IAAkBD,IAAlB,EAAuB;AACnB,oBAAIE,UAAU,MAAM,OAAKR,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC5CQ,6CAAwBF,KAAKG,EADe;AAE5CP,+BAAU;AAFkC,iBAA5B,EAGjBQ,IAHiB,EAApB;AAIAJ,qBAAKK,YAAL,GAAoBJ,QAAQI,YAA5B;AACH;AACD,gBAAIC,UAAUP,KAAK,CAAL,EAAQQ,gBAAtB;AACA,gBAAIC,gBAAgB,MAAM,OAAKf,KAAL,CAAW,eAAX,EAA4BC,KAA5B,CAAkC,EAACS,IAAGG,OAAJ,EAAlC,EAAgDF,IAAhD,EAA1B;AACA,gBAAIK,OAAOD,cAAcC,IAAzB;AACA,gBAAIC,OAAO;AACPH,kCAAiBD,OADV;AAEPG,sBAAKA,IAFE;AAGPE,2BAAUZ;AAHH,aAAX;AAKA,mBAAOW,IAAP;AAlBgC;AAmBnC;AAnCsC,CAA3C", "file": "..\\..\\..\\src\\api\\model\\goods.js", "sourcesContent": ["module.exports = class extends think.Model {\n    /**\n     * 获取商品的product\n     * @param goodsId\n     * @returns {Promise.<*>}\n     */\n    async getProductList(goodsId) {\n        const goods = await this.model('product').where({goods_id: goodsId,is_delete:0}).select();\n        return goods;\n    }\n\n    /**\n     * 获取商品的规格信息\n     * @param goodsId\n     * @returns {Promise.<Array>}\n     */\n    async getSpecificationList(goodsId) {\n        // 根据sku商品信息，查找规格值列表\n        let info = await this.model('goods_specification').where({goods_id:goodsId,is_delete:0}).select();\n        for(const item of info){\n            let product = await this.model('product').where({\n                goods_specification_ids:item.id,\n                is_delete:0\n            }).find();\n            item.goods_number = product.goods_number;\n        }\n        let spec_id = info[0].specification_id;\n        let specification = await this.model('specification').where({id:spec_id}).find();\n        let name = specification.name;\n        let data = {\n            specification_id:spec_id,\n            name:name,\n            valueList:info\n        }\n        return data;\n    }\n};\n"]}