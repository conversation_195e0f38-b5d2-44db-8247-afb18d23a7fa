/**
 * 权限管理工具类
 * 用于检查用户授权状态和权限控制
 */

/**
 * 检查用户是否已完整授权
 * @returns {boolean} true-已授权，false-未授权
 */
function isFullyAuthorized() {
  const userInfo = wx.getStorageSync('userInfo');
  const token = wx.getStorageSync('token');

  // 检查是否存在用户信息和token
  if (!userInfo || !token) {
    return false;
  }

  // 检查是否已授权（不是默认的占位信息）
  if (userInfo.nickname === '点我登录' || userInfo.nickname === '点击登录') {
    return false;
  }

  // 检查是否为"微信用户"（静默登录的默认昵称）
  if (userInfo.nickname === '微信用户') {
    return false;
  }

  // 检查是否有授权标识
  if (userInfo.is_authorized === false || userInfo.is_authorized === 0) {
    return false;
  }

  return true;
}

/**
 * 检查微信登录会话是否有效
 * @returns {Promise} 
 */
function checkWechatSession() {
  return new Promise((resolve, reject) => {
    wx.checkSession({
      success: () => resolve(true),
      fail: () => reject(false)
    });
  });
}

/**
 * 跳转到授权页面
 * @param {string} from 来源页面标识
 */
function navigateToAuth(from = '') {
  const url = from ? `/pages/app-auth/index?from=${from}` : '/pages/app-auth/index';
  wx.navigateTo({
    url: url
  });
}

/**
 * 检查页面访问权限
 * @param {string} from 来源页面标识，用于授权后跳转
 * @returns {boolean} true-有权限，false-无权限
 */
function checkPageAuth(from = '') {
  if (!isFullyAuthorized()) {
    navigateToAuth(from);
    return false;
  }
  return true;
}

/**
 * 获取当前用户信息
 * @returns {object|null} 用户信息对象或null
 */
function getCurrentUser() {
  const userInfo = wx.getStorageSync('userInfo');
  return userInfo || null;
}

/**
 * 获取当前token
 * @returns {string} token字符串
 */
function getCurrentToken() {
  return wx.getStorageSync('token') || '';
}

/**
 * 清除用户登录信息
 */
function clearUserInfo() {
  wx.removeStorageSync('userInfo');
  wx.removeStorageSync('token');
}

module.exports = {
  isFullyAuthorized,
  checkWechatSession,
  navigateToAuth,
  checkPageAuth,
  getCurrentUser,
  getCurrentToken,
  clearUserInfo
};
