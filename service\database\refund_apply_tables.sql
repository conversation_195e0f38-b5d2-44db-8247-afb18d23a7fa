-- 售后申请表
CREATE TABLE IF NOT EXISTS `hiolabs_refund_apply` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `order_sn` varchar(50) NOT NULL COMMENT '订单号',
  `refund_type` varchar(20) NOT NULL DEFAULT 'refund_only' COMMENT '退款类型：refund_only仅退款，return_refund退货退款',
  `refund_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '退款金额',
  `refund_reason` varchar(100) NOT NULL COMMENT '退款原因',
  `refund_desc` text COMMENT '退款说明',
  `images` text COMMENT '凭证图片，JSON格式',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态：pending待处理，processing处理中，approved已同意，rejected已拒绝，wait_return等待退货，returned已退货，completed已完成',
  `admin_memo` text COMMENT '管理员备注',
  `reject_reason` varchar(200) COMMENT '拒绝原因',
  `return_address` text COMMENT '退货地址（退货退款时使用）',
  `return_contact` varchar(100) COMMENT '退货联系人',
  `return_phone` varchar(20) COMMENT '退货联系电话',
  `user_logistics_company` varchar(50) COMMENT '用户退货物流公司',
  `user_logistics_no` varchar(50) COMMENT '用户退货物流单号',
  `user_return_time` int(11) DEFAULT NULL COMMENT '用户退货时间',
  `apply_time` int(11) NOT NULL COMMENT '申请时间',
  `process_time` int(11) DEFAULT NULL COMMENT '处理时间',
  `complete_time` int(11) DEFAULT NULL COMMENT '完成时间',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_sn` (`order_sn`),
  KEY `idx_status` (`status`),
  KEY `idx_apply_time` (`apply_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='售后申请表';

-- 插入测试数据（可选）
-- INSERT INTO `hiolabs_refund_apply` (`order_id`, `user_id`, `order_sn`, `refund_type`, `refund_amount`, `refund_reason`, `refund_desc`, `status`, `apply_time`, `created_at`, `updated_at`) 
-- VALUES (1, 1, 'TEST001', 'refund_only', 99.00, '不想要了', '商品不符合预期', 'pending', UNIX_TIMESTAMP(), NOW(), NOW());
