{"version": 3, "sources": ["..\\..\\..\\src\\api\\model\\cart.js"], "names": ["module", "exports", "think", "Model", "getGoodsList", "goodsList", "model", "where", "user_id", "userId", "is_delete", "select", "getCheckedGoodsList", "checked", "clearBuyGoods", "$res", "update"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;AACvC;;;;AAIMC,gBAAN,GAAqB;AAAA;;AAAA;AACjB,kBAAMC,YAAY,MAAM,MAAKC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC7CC,yBAASN,MAAMO,MAD8B;AAE7CC,2BAAW;AAFkC,aAAzB,EAGrBC,MAHqB,EAAxB;AAIA,mBAAON,SAAP;AALiB;AAMpB;AACD;;;;AAIMO,uBAAN,GAA4B;AAAA;;AAAA;AACxB,kBAAMP,YAAY,MAAM,OAAKC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC7CC,yBAASN,MAAMO,MAD8B;AAE7CI,yBAAS,CAFoC;AAG7CH,2BAAW;AAHkC,aAAzB,EAIrBC,MAJqB,EAAxB;AAKA,mBAAON,SAAP;AANwB;AAO3B;AACD;;;;AAIMS,iBAAN,GAAsB;AAAA;;AAAA;AAClB,kBAAMC,OAAO,MAAM,OAAKT,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACxCC,yBAASN,MAAMO,MADyB;AAExCI,yBAAS,CAF+B;AAGxCH,2BAAW;AAH6B,aAAzB,EAIhBM,MAJgB,CAIT;AACNN,2BAAW;AADL,aAJS,CAAnB;AAOA,mBAAOK,IAAP;AARkB;AASrB;AArCsC,CAA3C", "file": "..\\..\\..\\src\\api\\model\\cart.js", "sourcesContent": ["module.exports = class extends think.Model {\n    /**\n     * 获取购物车的商品\n     * @returns {Promise.<*>}\n     */\n    async getGoodsList() {\n        const goodsList = await this.model('cart').where({\n            user_id: think.userId,\n            is_delete: 0\n        }).select();\n        return goodsList;\n    }\n    /**\n     * 获取购物车的选中的商品\n     * @returns {Promise.<*>}\n     */\n    async getCheckedGoodsList() {\n        const goodsList = await this.model('cart').where({\n            user_id: think.userId,\n            checked: 1,\n            is_delete: 0\n        }).select();\n        return goodsList;\n    }\n    /**\n     * 清空已购买的商品\n     * @returns {Promise.<*>}\n     */\n    async clearBuyGoods() {\n        const $res = await this.model('cart').where({\n            user_id: think.userId,\n            checked: 1,\n            is_delete: 0\n        }).update({\n            is_delete: 1\n        });\n        return $res;\n    }\n};"]}