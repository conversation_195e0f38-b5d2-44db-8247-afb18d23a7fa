{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\admin.js"], "names": ["Base", "require", "moment", "md5", "module", "exports", "indexAction", "data", "model", "where", "is_delete", "select", "item", "last_login_time", "unix", "format", "password", "success", "adminDetailAction", "id", "post", "info", "find", "adminAddAction", "user", "upData", "username", "password_salt", "replace", "length", "add", "adminSaveAction", "change", "newPassword", "newpassword", "ex", "think", "isEmpty", "fail", "update", "infoAction", "get", "storeAction", "isPost", "values", "is_show", "is_new", "deleAdminAction", "limit", "delete", "showsetAction", "showsetStoreAction", "changeAutoStatusAction", "status", "autoDelivery", "storeShipperSettingsAction", "senderInfoAction"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;AACA,MAAME,MAAMF,QAAQ,KAAR,CAAZ;AACAG,OAAOC,OAAP,GAAiB,cAAcL,IAAd,CAAmB;AAChC;;;;AAIMM,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAMC,OAAO,MAAM,MAAKC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AACzC;AACAC,2BAAW;AAF8B,aAA1B,EAGhBC,MAHgB,EAAnB;AAIA,iBAAK,MAAMC,IAAX,IAAmBL,IAAnB,EAAyB;AACrB,oBAAIK,KAAKC,eAAL,IAAwB,CAA5B,EAA+B;AAC3BD,yBAAKC,eAAL,GAAuBX,OAAOY,IAAP,CAAYF,KAAKC,eAAjB,EAAkCE,MAAlC,CAAyC,qBAAzC,CAAvB;AACH,iBAFD,MAEO;AACHH,yBAAKC,eAAL,GAAuB,OAAvB;AACH;AACDD,qBAAKI,QAAL,GAAgB,EAAhB;AACH;AACD,mBAAO,MAAKC,OAAL,CAAaV,IAAb,CAAP;AAbgB;AAcnB;AACKW,qBAAN,GAA0B;AAAA;;AAAA;AACtB,gBAAIC,KAAK,OAAKC,IAAL,CAAU,IAAV,CAAT;AACA,gBAAIC,OAAO,MAAM,OAAKb,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AACvCU,oBAAIA;AADmC,aAA1B,EAEdG,IAFc,EAAjB;AAGA,mBAAO,OAAKL,OAAL,CAAaI,IAAb,CAAP;AALsB;AAMzB;AACKE,kBAAN,GAAuB;AAAA;;AAAA;AACnB,gBAAIC,OAAO,OAAKJ,IAAL,CAAU,MAAV,CAAX;AACA,gBAAIJ,WAAWQ,KAAKR,QAApB;AACA,gBAAIS,SAAS;AACTC,0BAAUL,KAAKK,QADN;AAETC,+BAAe;AAFN,aAAb;AAIA,gBAAIX,SAASY,OAAT,CAAiB,gBAAjB,EAAmC,EAAnC,EAAuCC,MAAvC,IAAiD,CAArD,EAAwD;AACpDb,2BAAWb,IAAIkB,KAAKL,QAAL,GAAgB,EAAhB,GAAqBS,OAAOE,aAAhC,CAAX;AACAF,uBAAOT,QAAP,GAAkBA,QAAlB;AACH;AACD,kBAAM,OAAKR,KAAL,CAAW,OAAX,EAAoBsB,GAApB,CAAwBL,MAAxB,CAAN;AACA,mBAAO,OAAKR,OAAL,EAAP;AAZmB;AAatB;AACKc,mBAAN,GAAwB;AAAA;;AAAA;AACpB,gBAAIP,OAAO,OAAKJ,IAAL,CAAU,MAAV,CAAX;AACA,gBAAIY,SAAS,OAAKZ,IAAL,CAAU,QAAV,CAAb;AACA,gBAAIK,SAAS;AACTC,0BAAUF,KAAKE;AADN,aAAb;AAGA,gBAAIM,UAAU,IAAd,EAAoB;AAChB,oBAAIC,cAAcT,KAAKU,WAAvB;AACA,oBAAID,YAAYL,OAAZ,CAAoB,gBAApB,EAAsC,EAAtC,EAA0CC,MAA1C,IAAoD,CAAxD,EAA2D;AACvDI,kCAAc9B,IAAIqB,KAAKU,WAAL,GAAmB,EAAnB,GAAwBV,KAAKG,aAAjC,CAAd;AACAF,2BAAOT,QAAP,GAAkBiB,WAAlB;AACH;AACJ;AACD,gBAAIE,KAAK,MAAM,OAAK3B,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AACrCiB,0BAAUF,KAAKE,QADsB;AAErCP,oBAAI,CAAC,IAAD,EAAOK,KAAKL,EAAZ;AAFiC,aAA1B,EAGZG,IAHY,EAAf;AAIA,gBAAI,CAACc,MAAMC,OAAN,CAAcF,EAAd,CAAL,EAAwB;AACpB,uBAAO,OAAKG,IAAL,CAAU,GAAV,EAAe,KAAf,CAAP;AACH;AACD;AACA;AACA;AACA,kBAAM,OAAK9B,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5BU,oBAAIK,KAAKL;AADmB,aAA1B,EAEHoB,MAFG,CAEId,MAFJ,CAAN;AAGA,mBAAO,OAAKR,OAAL,EAAP;AA1BoB;AA2BvB;AACKuB,cAAN,GAAmB;AAAA;;AAAA;AACf,kBAAMrB,KAAK,OAAKsB,GAAL,CAAS,IAAT,CAAX;AACA,kBAAMjC,QAAQ,OAAKA,KAAL,CAAW,MAAX,CAAd;AACA,kBAAMD,OAAO,MAAMC,MAAMC,KAAN,CAAY;AAC3BU,oBAAIA;AADuB,aAAZ,EAEhBG,IAFgB,EAAnB;AAGA,mBAAO,OAAKL,OAAL,CAAaV,IAAb,CAAP;AANe;AAOlB;AACKmC,eAAN,GAAoB;AAAA;;AAAA;AAChB,gBAAI,CAAC,OAAKC,MAAV,EAAkB;AACd,uBAAO,KAAP;AACH;AACD,kBAAMC,SAAS,OAAKxB,IAAL,EAAf;AACA,kBAAMD,KAAK,OAAKC,IAAL,CAAU,IAAV,CAAX;AACA,kBAAMZ,QAAQ,OAAKA,KAAL,CAAW,MAAX,CAAd;AACAoC,mBAAOC,OAAP,GAAiBD,OAAOC,OAAP,GAAiB,CAAjB,GAAqB,CAAtC;AACAD,mBAAOE,MAAP,GAAgBF,OAAOE,MAAP,GAAgB,CAAhB,GAAoB,CAApC;AACA,gBAAI3B,KAAK,CAAT,EAAY;AACR,sBAAMX,MAAMC,KAAN,CAAY;AACdU,wBAAIA;AADU,iBAAZ,EAEHoB,MAFG,CAEIK,MAFJ,CAAN;AAGH,aAJD,MAIO;AACH,uBAAOA,OAAOzB,EAAd;AACA,sBAAMX,MAAMsB,GAAN,CAAUc,MAAV,CAAN;AACH;AACD,mBAAO,OAAK3B,OAAL,CAAa2B,MAAb,CAAP;AAjBgB;AAkBnB;AACKG,mBAAN,GAAwB;AAAA;;AAAA;AACpB,kBAAM5B,KAAK,OAAKC,IAAL,CAAU,IAAV,CAAX;AACA,kBAAM,OAAKZ,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5BU,oBAAIA;AADwB,aAA1B,EAEH6B,KAFG,CAEG,CAFH,EAEMC,MAFN,EAAN;AAGA,mBAAO,OAAKhC,OAAL,EAAP;AALoB;AAMvB;AACKiC,iBAAN,GAAsB;AAAA;;AAAA;AAClB,kBAAM1C,QAAQ,OAAKA,KAAL,CAAW,eAAX,CAAd;AACA,gBAAID,OAAO,MAAMC,MAAMc,IAAN,EAAjB;AACA,mBAAO,OAAKL,OAAL,CAAaV,IAAb,CAAP;AAHkB;AAIrB;AACK4C,sBAAN,GAA2B;AAAA;;AAAA;AACvB,gBAAIhC,KAAK,CAAT;AACA,kBAAMyB,SAAS,OAAKxB,IAAL,EAAf;AACA,kBAAMZ,QAAQ,OAAKA,KAAL,CAAW,eAAX,CAAd;AACA,kBAAMA,MAAMC,KAAN,CAAY;AACdU,oBAAIA;AADU,aAAZ,EAEHoB,MAFG,CAEIK,MAFJ,CAAN;AAGA,mBAAO,OAAK3B,OAAL,CAAa2B,MAAb,CAAP;AAPuB;AAQ1B;AACKQ,0BAAN,GAA+B;AAAA;;AAAA;AAC3B,kBAAMC,SAAS,QAAKjC,IAAL,CAAU,QAAV,CAAf;AACA,kBAAM,QAAKZ,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAC/BU,oBAAI;AAD2B,aAA7B,EAEHoB,MAFG,CAEI;AACNe,8BAAcD;AADR,aAFJ,CAAN;AAKA,mBAAO,QAAKpC,OAAL,EAAP;AAP2B;AAQ9B;AACKsC,8BAAN,GAAmC;AAAA;;AAAA;AAC/B,kBAAMX,SAAS,QAAKxB,IAAL,EAAf;AACA,kBAAM,QAAKZ,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAC/BU,oBAAIyB,OAAOzB;AADoB,aAA7B,EAEHoB,MAFG,CAEIK,MAFJ,CAAN;AAGA,mBAAO,QAAK3B,OAAL,EAAP;AAL+B;AAMlC;AACKuC,oBAAN,GAAyB;AAAA;;AAAA;AACrB,gBAAInC,OAAO,MAAM,QAAKb,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAC1CU,oBAAI;AADsC,aAA7B,EAEdG,IAFc,EAAjB;AAGA,mBAAO,QAAKL,OAAL,CAAaI,IAAb,CAAP;AAJqB;AAKxB;AA1I+B,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\admin.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\nconst md5 = require('md5');\nmodule.exports = class extends Base {\n    /**\n     * index action\n     * @return {Promise} []\n     */\n    async indexAction() {\n        const data = await this.model('admin').where({\n            // is_show: 1,\n            is_delete: 0\n        }).select();\n        for (const item of data) {\n            if (item.last_login_time != 0) {\n                item.last_login_time = moment.unix(item.last_login_time).format('YYYY-MM-DD HH:mm:ss');\n            } else {\n                item.last_login_time = '还没登录过'\n            }\n            item.password = '';\n        }\n        return this.success(data);\n    }\n    async adminDetailAction() {\n        let id = this.post('id')\n        let info = await this.model('admin').where({\n            id: id\n        }).find();\n        return this.success(info);\n    }\n    async adminAddAction() {\n        let user = this.post('user');\n        let password = user.password;\n        let upData = {\n            username: info.username,\n            password_salt: 'HIOLABS'\n        };\n        if (password.replace(/(^\\s*)|(\\s*$)/g, \"\").length != 0) {\n            password = md5(info.password + '' + upData.password_salt);\n            upData.password = password;\n        }\n        await this.model('admin').add(upData);\n        return this.success();\n    }\n    async adminSaveAction() {\n        let user = this.post('user');\n        let change = this.post('change');\n        let upData = {\n            username: user.username,\n        };\n        if (change == true) {\n            let newPassword = user.newpassword;\n            if (newPassword.replace(/(^\\s*)|(\\s*$)/g, \"\").length != 0) {\n                newPassword = md5(user.newpassword + '' + user.password_salt);\n                upData.password = newPassword;\n            }\n        }\n        let ex = await this.model('admin').where({\n            username: user.username,\n            id: ['<>', user.id]\n        }).find();\n        if (!think.isEmpty(ex)) {\n            return this.fail(400, '重名了')\n        }\n        // if (user.id == 14) {\n        //     return this.fail(400, '演示版后台的管理员密码不能修改!本地开发，删除这个判断')\n        // }\n        await this.model('admin').where({\n            id: user.id\n        }).update(upData);\n        return this.success();\n    }\n    async infoAction() {\n        const id = this.get('id');\n        const model = this.model('user');\n        const data = await model.where({\n            id: id\n        }).find();\n        return this.success(data);\n    }\n    async storeAction() {\n        if (!this.isPost) {\n            return false;\n        }\n        const values = this.post();\n        const id = this.post('id');\n        const model = this.model('user');\n        values.is_show = values.is_show ? 1 : 0;\n        values.is_new = values.is_new ? 1 : 0;\n        if (id > 0) {\n            await model.where({\n                id: id\n            }).update(values);\n        } else {\n            delete values.id;\n            await model.add(values);\n        }\n        return this.success(values);\n    }\n    async deleAdminAction() {\n        const id = this.post('id');\n        await this.model('admin').where({\n            id: id\n        }).limit(1).delete();\n        return this.success();\n    }\n    async showsetAction() {\n        const model = this.model('show_settings');\n        let data = await model.find();\n        return this.success(data);\n    }\n    async showsetStoreAction() {\n        let id = 1;\n        const values = this.post();\n        const model = this.model('show_settings');\n        await model.where({\n            id: id\n        }).update(values);\n        return this.success(values);\n    }\n    async changeAutoStatusAction() {\n        const status = this.post('status');\n        await this.model('settings').where({\n            id: 1\n        }).update({\n            autoDelivery: status\n        });\n        return this.success();\n    }\n    async storeShipperSettingsAction() {\n        const values = this.post();\n        await this.model('settings').where({\n            id: values.id\n        }).update(values);\n        return this.success();\n    }\n    async senderInfoAction() {\n        let info = await this.model('settings').where({\n            id: 1\n        }).find();\n        return this.success(info);\n    }\n};"]}