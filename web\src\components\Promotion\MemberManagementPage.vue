<template>
  <div class="member-management-page">
    <!-- 页面头部 -->
    <div class="page-header bg-white shadow-sm border-b">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">分销员管理</h1>
            <p class="text-sm text-gray-500 mt-1">管理分销员信息、等级和权限</p>
          </div>
          <div class="flex items-center space-x-3">
            <button @click="showAddModal = true" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <i class="ri-add-line mr-2"></i>
              添加分销员
            </button>
            <button @click="exportData" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <i class="ri-download-line mr-2"></i>
              导出数据
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="p-6 max-w-7xl mx-auto">
      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="ri-team-line text-xl text-blue-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">总分销员</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.totalMembers }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="ri-user-star-line text-xl text-green-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">活跃分销员</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.activeMembers }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <i class="ri-money-dollar-circle-line text-xl text-yellow-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">累计佣金</p>
              <p class="text-2xl font-bold text-gray-900">¥{{ statistics.totalCommission }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <i class="ri-shopping-cart-line text-xl text-purple-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">推广订单</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.totalOrders }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <div class="bg-white rounded-lg shadow-sm border mb-6">
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">搜索分销员</label>
              <input
                v-model="searchQuery"
                type="text"
                placeholder="输入姓名、手机号或微信昵称"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">分销员等级</label>
              <select
                v-model="filterLevel"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">全部等级</option>
                <option value="团长">团长</option>
                <option value="一级分销">一级分销</option>
                <option value="二级分销">二级分销</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
              <select
                v-model="filterStatus"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">全部状态</option>
                <option value="active">启用</option>
                <option value="inactive">禁用</option>
              </select>
            </div>

            <div class="flex items-end">
              <button @click="resetFilters" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                <i class="ri-refresh-line mr-2"></i>
                重置
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分销员列表 -->
      <div class="bg-white rounded-lg shadow-sm border">
        <div class="px-6 py-4 border-b">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900">分销员列表</h2>
            <div class="flex items-center space-x-2">
              <button
                @click="viewMode = 'tree'"
                :class="[
                  'px-3 py-1 text-sm rounded-md transition-colors',
                  viewMode === 'tree' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                ]"
              >
                <i class="ri-node-tree mr-1"></i>
                层级视图
              </button>
              <button
                @click="viewMode = 'list'"
                :class="[
                  'px-3 py-1 text-sm rounded-md transition-colors',
                  viewMode === 'list' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                ]"
              >
                <i class="ri-list-check mr-1"></i>
                列表视图
              </button>
            </div>
          </div>
        </div>

        <!-- 层级视图 -->
        <div v-if="viewMode === 'tree'" class="p-6">
          <div class="space-y-6">
            <!-- 团长列表 -->
            <div v-for="leader in filteredLeaders" :key="leader.id" class="border rounded-lg">
              <!-- 团长信息 -->
              <div class="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <img :src="leader.avatar" :alt="leader.name" class="w-12 h-12 rounded-full object-cover">
                    <div>
                      <div class="flex items-center space-x-2">
                        <h3 class="font-semibold text-gray-900">{{ leader.name }}</h3>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">团长</span>
                        <span :class="[
                          'px-2 py-1 text-xs rounded-full',
                          leader.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        ]">
                          {{ leader.status === 'active' ? '启用' : '禁用' }}
                        </span>
                      </div>
                      <p class="text-sm text-gray-500">{{ leader.phone }} | 加入时间: {{ leader.joinTime }}</p>
                      <div class="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                        <span>佣金比例: {{ leader.commissionRate }}%</span>
                        <span>累计销售: ¥{{ leader.totalSales }}</span>
                        <span>累计佣金: ¥{{ leader.totalCommission }}</span>
                        <span>客户数: {{ leader.customerCount }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <button @click="toggleLeaderExpand(leader.id)" class="p-2 text-gray-400 hover:text-gray-600">
                      <i :class="['ri-arrow-down-s-line transition-transform', leader.expanded ? 'rotate-180' : '']"></i>
                    </button>
                    <button @click="viewMemberDetail(leader)" class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                      详情
                    </button>
                    <button @click="toggleMemberStatus(leader)" :class="[
                      'px-3 py-1 text-sm rounded transition-colors',
                      leader.status === 'active'
                        ? 'bg-red-600 text-white hover:bg-red-700'
                        : 'bg-green-600 text-white hover:bg-green-700'
                    ]">
                      {{ leader.status === 'active' ? '禁用' : '启用' }}
                    </button>
                  </div>
                </div>
              </div>

              <!-- 团长下属 -->
              <div v-show="leader.expanded" class="p-4">
                <!-- 一级分销员 -->
                <div v-if="leader.level1Members && leader.level1Members.length > 0" class="mb-4">
                  <h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                    <i class="ri-user-line mr-2"></i>
                    一级分销员 ({{ leader.level1Members.length }}人)
                  </h4>
                  <div class="space-y-3 ml-6">
                    <div v-for="member1 in leader.level1Members" :key="member1.id" class="border rounded-lg p-3 bg-green-50">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                          <img :src="member1.avatar" :alt="member1.name" class="w-10 h-10 rounded-full object-cover">
                          <div>
                            <div class="flex items-center space-x-2">
                              <span class="font-medium text-gray-900">{{ member1.name }}</span>
                              <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">一级分销</span>
                              <span :class="[
                                'px-2 py-1 text-xs rounded-full',
                                member1.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                              ]">
                                {{ member1.status === 'active' ? '启用' : '禁用' }}
                              </span>
                            </div>
                            <p class="text-xs text-gray-500">{{ member1.phone }} | {{ member1.joinTime }}</p>
                            <div class="flex items-center space-x-3 mt-1 text-xs text-gray-600">
                              <span>佣金: {{ member1.commissionRate }}%</span>
                              <span>销售: ¥{{ member1.totalSales }}</span>
                              <span>佣金: ¥{{ member1.totalCommission }}</span>
                              <span>客户: {{ member1.customerCount }}</span>
                            </div>
                          </div>
                        </div>
                        <div class="flex items-center space-x-2">
                          <button @click="toggleLevel1Expand(member1.id)" class="p-1 text-gray-400 hover:text-gray-600">
                            <i :class="['ri-arrow-down-s-line transition-transform text-sm', member1.expanded ? 'rotate-180' : '']"></i>
                          </button>
                          <button @click="viewMemberDetail(member1)" class="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                            详情
                          </button>
                          <button @click="toggleMemberStatus(member1)" :class="[
                            'px-2 py-1 text-xs rounded transition-colors',
                            member1.status === 'active'
                              ? 'bg-red-600 text-white hover:bg-red-700'
                              : 'bg-green-600 text-white hover:bg-green-700'
                          ]">
                            {{ member1.status === 'active' ? '禁用' : '启用' }}
                          </button>
                        </div>
                      </div>

                      <!-- 二级分销员 -->
                      <div v-show="member1.expanded && member1.level2Members && member1.level2Members.length > 0" class="mt-3 ml-6">
                        <h5 class="text-xs font-medium text-gray-600 mb-2 flex items-center">
                          <i class="ri-user-2-line mr-1"></i>
                          二级分销员 ({{ member1.level2Members.length }}人)
                        </h5>
                        <div class="space-y-2">
                          <div v-for="member2 in member1.level2Members" :key="member2.id" class="border rounded p-2 bg-yellow-50">
                            <div class="flex items-center justify-between">
                              <div class="flex items-center space-x-2">
                                <img :src="member2.avatar" :alt="member2.name" class="w-8 h-8 rounded-full object-cover">
                                <div>
                                  <div class="flex items-center space-x-2">
                                    <span class="text-sm font-medium text-gray-900">{{ member2.name }}</span>
                                    <span class="px-1 py-0.5 bg-yellow-100 text-yellow-800 text-xs rounded">二级分销</span>
                                    <span :class="[
                                      'px-1 py-0.5 text-xs rounded',
                                      member2.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                    ]">
                                      {{ member2.status === 'active' ? '启用' : '禁用' }}
                                    </span>
                                  </div>
                                  <p class="text-xs text-gray-500">{{ member2.phone }} | {{ member2.joinTime }}</p>
                                  <div class="flex items-center space-x-2 mt-1 text-xs text-gray-600">
                                    <span>佣金: {{ member2.commissionRate }}%</span>
                                    <span>销售: ¥{{ member2.totalSales }}</span>
                                    <span>佣金: ¥{{ member2.totalCommission }}</span>
                                    <span>客户: {{ member2.customerCount }}</span>
                                  </div>
                                </div>
                              </div>
                              <div class="flex items-center space-x-1">
                                <button @click="viewMemberDetail(member2)" class="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                                  详情
                                </button>
                                <button @click="toggleMemberStatus(member2)" :class="[
                                  'px-2 py-1 text-xs rounded transition-colors',
                                  member2.status === 'active'
                                    ? 'bg-red-600 text-white hover:bg-red-700'
                                    : 'bg-green-600 text-white hover:bg-green-700'
                                ]">
                                  {{ member2.status === 'active' ? '禁用' : '启用' }}
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 列表视图 -->
        <div v-else class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分销员信息</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">等级</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">佣金比例</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">累计销售额</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">累计佣金</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户数</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">加入时间</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="member in filteredAllMembers" :key="member.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <img :src="member.avatar" :alt="member.name" class="w-10 h-10 rounded-full object-cover">
                    <div class="ml-3">
                      <div class="text-sm font-medium text-gray-900">{{ member.name }}</div>
                      <div class="text-sm text-gray-500">{{ member.phone }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="[
                    'px-2 py-1 text-xs rounded-full',
                    member.level === '团长' ? 'bg-blue-100 text-blue-800' :
                    member.level === '一级分销' ? 'bg-green-100 text-green-800' :
                    'bg-yellow-100 text-yellow-800'
                  ]">
                    {{ member.level }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ member.commissionRate }}%</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥{{ member.totalSales }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥{{ member.totalCommission }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ member.customerCount }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ member.joinTime }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="[
                    'px-2 py-1 text-xs rounded-full',
                    member.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  ]">
                    {{ member.status === 'active' ? '启用' : '禁用' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button @click="viewMemberDetail(member)" class="text-blue-600 hover:text-blue-900">详情</button>
                  <button @click="toggleMemberStatus(member)" :class="[
                    'transition-colors',
                    member.status === 'active' ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'
                  ]">
                    {{ member.status === 'active' ? '禁用' : '启用' }}
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 分销员详情弹窗 -->
    <div v-if="showDetailModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">分销员详情</h3>
          <button @click="showDetailModal = false" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>

        <div v-if="selectedMember" class="space-y-4">
          <div class="flex items-center space-x-4">
            <img :src="selectedMember.avatar" :alt="selectedMember.name" class="w-16 h-16 rounded-full object-cover">
            <div>
              <h4 class="text-xl font-semibold text-gray-900">{{ selectedMember.name }}</h4>
              <p class="text-gray-500">{{ selectedMember.phone }}</p>
              <span :class="[
                'px-2 py-1 text-xs rounded-full',
                selectedMember.level === '团长' ? 'bg-blue-100 text-blue-800' :
                selectedMember.level === '一级分销' ? 'bg-green-100 text-green-800' :
                'bg-yellow-100 text-yellow-800'
              ]">
                {{ selectedMember.level }}
              </span>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div class="bg-gray-50 p-4 rounded-lg">
              <p class="text-sm text-gray-500">佣金比例</p>
              <p class="text-lg font-semibold text-gray-900">{{ selectedMember.commissionRate }}%</p>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
              <p class="text-sm text-gray-500">累计销售额</p>
              <p class="text-lg font-semibold text-gray-900">¥{{ selectedMember.totalSales }}</p>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
              <p class="text-sm text-gray-500">累计佣金</p>
              <p class="text-lg font-semibold text-gray-900">¥{{ selectedMember.totalCommission }}</p>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
              <p class="text-sm text-gray-500">客户数量</p>
              <p class="text-lg font-semibold text-gray-900">{{ selectedMember.customerCount }}</p>
            </div>
          </div>

          <div class="bg-gray-50 p-4 rounded-lg">
            <p class="text-sm text-gray-500 mb-2">加入时间</p>
            <p class="text-gray-900">{{ selectedMember.joinTime }}</p>
          </div>

          <div class="bg-gray-50 p-4 rounded-lg">
            <p class="text-sm text-gray-500 mb-2">推广链接</p>
            <div class="flex items-center space-x-2">
              <input
                :value="selectedMember.promotionLink"
                readonly
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-white text-sm"
              />
              <button @click="copyLink(selectedMember.promotionLink)" class="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                复制
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MemberManagementPage',
  data() {
    return {
      // 视图模式
      viewMode: 'tree', // tree: 层级视图, list: 列表视图

      // 搜索和筛选
      searchQuery: '',
      filterLevel: '',
      filterStatus: '',

      // 弹窗状态
      showDetailModal: false,
      showAddModal: false,
      selectedMember: null,

      // 统计数据
      statistics: {
        totalMembers: 156,
        activeMembers: 142,
        totalCommission: '28,650.00',
        totalOrders: 1248
      },

      // 分销员数据
      distributors: [
        {
          id: 1,
          name: '张团长',
          phone: '138****8888',
          avatar: '/images/avatar1.jpg',
          level: '团长',
          commissionRate: 15.0,
          totalSales: '125,680.00',
          totalCommission: '18,852.00',
          customerCount: 89,
          joinTime: '2023-08-15',
          status: 'active',
          expanded: false,
          promotionLink: 'https://shop.example.com?ref=zhang001',
          level1Members: [
            {
              id: 11,
              name: '李分销',
              phone: '139****6666',
              avatar: '/images/avatar2.jpg',
              level: '一级分销',
              commissionRate: 8.0,
              totalSales: '45,200.00',
              totalCommission: '3,616.00',
              customerCount: 32,
              joinTime: '2023-09-20',
              status: 'active',
              expanded: false,
              promotionLink: 'https://shop.example.com?ref=li001',
              level2Members: [
                {
                  id: 111,
                  name: '王小销',
                  phone: '136****5555',
                  avatar: '/images/avatar3.jpg',
                  level: '二级分销',
                  commissionRate: 3.0,
                  totalSales: '12,800.00',
                  totalCommission: '384.00',
                  customerCount: 15,
                  joinTime: '2023-10-05',
                  status: 'active',
                  promotionLink: 'https://shop.example.com?ref=wang001'
                },
                {
                  id: 112,
                  name: '赵小推',
                  phone: '137****4444',
                  avatar: '/images/avatar4.jpg',
                  level: '二级分销',
                  commissionRate: 3.0,
                  totalSales: '8,500.00',
                  totalCommission: '255.00',
                  customerCount: 8,
                  joinTime: '2023-10-12',
                  status: 'active',
                  promotionLink: 'https://shop.example.com?ref=zhao001'
                }
              ]
            },
            {
              id: 12,
              name: '陈推广',
              phone: '135****7777',
              avatar: '/images/avatar5.jpg',
              level: '一级分销',
              commissionRate: 8.0,
              totalSales: '32,100.00',
              totalCommission: '2,568.00',
              customerCount: 24,
              joinTime: '2023-09-25',
              status: 'active',
              expanded: false,
              promotionLink: 'https://shop.example.com?ref=chen001',
              level2Members: []
            }
          ]
        },
        {
          id: 2,
          name: '刘团长',
          phone: '138****9999',
          avatar: '/images/avatar6.jpg',
          level: '团长',
          commissionRate: 15.0,
          totalSales: '98,450.00',
          totalCommission: '14,767.50',
          customerCount: 67,
          joinTime: '2023-08-20',
          status: 'active',
          expanded: false,
          promotionLink: 'https://shop.example.com?ref=liu001',
          level1Members: [
            {
              id: 21,
              name: '孙代理',
              phone: '139****3333',
              avatar: '/images/avatar7.jpg',
              level: '一级分销',
              commissionRate: 8.0,
              totalSales: '28,900.00',
              totalCommission: '2,312.00',
              customerCount: 19,
              joinTime: '2023-09-30',
              status: 'inactive',
              expanded: false,
              promotionLink: 'https://shop.example.com?ref=sun001',
              level2Members: []
            }
          ]
        }
      ]
    }
  },

  computed: {
    // 过滤后的团长列表
    filteredLeaders() {
      return this.distributors.filter(leader => {
        const matchesSearch = !this.searchQuery ||
          leader.name.includes(this.searchQuery) ||
          leader.phone.includes(this.searchQuery);
        const matchesLevel = !this.filterLevel || leader.level === this.filterLevel;
        const matchesStatus = !this.filterStatus || leader.status === this.filterStatus;

        return matchesSearch && matchesLevel && matchesStatus;
      });
    },

    // 所有分销员的扁平列表
    filteredAllMembers() {
      const allMembers = [];

      this.distributors.forEach(leader => {
        // 添加团长
        allMembers.push(leader);

        // 添加一级分销员
        if (leader.level1Members) {
          leader.level1Members.forEach(member1 => {
            allMembers.push(member1);

            // 添加二级分销员
            if (member1.level2Members) {
              member1.level2Members.forEach(member2 => {
                allMembers.push(member2);
              });
            }
          });
        }
      });

      return allMembers.filter(member => {
        const matchesSearch = !this.searchQuery ||
          member.name.includes(this.searchQuery) ||
          member.phone.includes(this.searchQuery);
        const matchesLevel = !this.filterLevel || member.level === this.filterLevel;
        const matchesStatus = !this.filterStatus || member.status === this.filterStatus;

        return matchesSearch && matchesLevel && matchesStatus;
      });
    }
  },

  methods: {
    // 切换团长展开状态
    toggleLeaderExpand(leaderId) {
      const leader = this.distributors.find(l => l.id === leaderId);
      if (leader) {
        leader.expanded = !leader.expanded;
      }
    },

    // 切换一级分销员展开状态
    toggleLevel1Expand(memberId) {
      this.distributors.forEach(leader => {
        if (leader.level1Members) {
          const member = leader.level1Members.find(m => m.id === memberId);
          if (member) {
            member.expanded = !member.expanded;
          }
        }
      });
    },

    // 查看分销员详情
    viewMemberDetail(member) {
      this.selectedMember = member;
      this.showDetailModal = true;
    },

    // 切换分销员状态
    toggleMemberStatus(member) {
      member.status = member.status === 'active' ? 'inactive' : 'active';
      console.log(`${member.name} 状态已切换为: ${member.status}`);
    },

    // 重置筛选条件
    resetFilters() {
      this.searchQuery = '';
      this.filterLevel = '';
      this.filterStatus = '';
    },

    // 导出数据
    exportData() {
      console.log('导出分销员数据');
      alert('数据导出功能开发中');
    },

    // 复制推广链接
    copyLink(link) {
      navigator.clipboard.writeText(link).then(() => {
        alert('推广链接已复制到剪贴板');
      }).catch(() => {
        alert('复制失败，请手动复制');
      });
    }
  },

  mounted() {
    console.log('分销员管理页面已加载');

    // 模拟头像图片
    this.distributors.forEach((leader, index) => {
      leader.avatar = `https://ui-avatars.com/api/?name=${leader.name}&background=random&size=128`;

      if (leader.level1Members) {
        leader.level1Members.forEach((member1, i1) => {
          member1.avatar = `https://ui-avatars.com/api/?name=${member1.name}&background=random&size=128`;

          if (member1.level2Members) {
            member1.level2Members.forEach((member2, i2) => {
              member2.avatar = `https://ui-avatars.com/api/?name=${member2.name}&background=random&size=128`;
            });
          }
        });
      }
    });
  }
}
</script>

<style scoped>
.member-management-page {
  min-height: 100vh;
  background-color: #f9fafb;
}

.page-header {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 表格样式 */
.overflow-x-auto {
  overflow-x: auto;
}

table {
  min-width: 100%;
}

th, td {
  white-space: nowrap;
}

/* 头像样式 */
img {
  object-fit: cover;
}

/* 按钮悬停效果 */
.hover\:bg-blue-700:hover {
  background-color: #1d4ed8;
}

.hover\:bg-green-700:hover {
  background-color: #15803d;
}

.hover\:bg-red-700:hover {
  background-color: #b91c1c;
}

.hover\:bg-gray-700:hover {
  background-color: #374151;
}

.hover\:bg-gray-200:hover {
  background-color: #e5e7eb;
}

/* 文本颜色悬停 */
.hover\:text-blue-900:hover {
  color: #1e3a8a;
}

.hover\:text-green-900:hover {
  color: #14532d;
}

.hover\:text-red-900:hover {
  color: #7f1d1d;
}

.hover\:text-gray-600:hover {
  color: #4b5563;
}

/* 行悬停效果 */
.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}

/* 过渡动画 */
.transition-colors {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

.transition-transform {
  transition: transform 0.2s ease;
}

.rotate-180 {
  transform: rotate(180deg);
}

/* 输入框聚焦效果 */
input:focus, select:focus {
  outline: none;
  ring: 2px;
  ring-color: #3b82f6;
  border-color: transparent;
}

/* 弹窗样式 */
.fixed {
  position: fixed;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.z-50 {
  z-index: 50;
}

.max-h-\[90vh\] {
  max-height: 90vh;
}

.overflow-y-auto {
  overflow-y: auto;
}

/* 网格布局 */
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

/* 响应式设计 */
@media (min-width: 768px) {
  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* 间距 */
.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-x-1 > * + * {
  margin-left: 0.25rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

/* 分割线 */
.divide-y > * + * {
  border-top-width: 1px;
}

.divide-gray-200 > * + * {
  border-color: #e5e7eb;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 层级缩进 */
.ml-6 {
  margin-left: 1.5rem;
}

/* 图标样式 */
.ri-team-line,
.ri-user-star-line,
.ri-money-dollar-circle-line,
.ri-shopping-cart-line,
.ri-add-line,
.ri-download-line,
.ri-refresh-line,
.ri-node-tree,
.ri-list-check,
.ri-arrow-down-s-line,
.ri-user-line,
.ri-user-2-line,
.ri-close-line {
  font-size: inherit;
}

/* 状态标签 */
.bg-blue-100 {
  background-color: #dbeafe;
}

.text-blue-800 {
  color: #1e40af;
}

.bg-green-100 {
  background-color: #dcfce7;
}

.text-green-800 {
  color: #166534;
}

.bg-yellow-100 {
  background-color: #fef3c7;
}

.text-yellow-800 {
  color: #92400e;
}

.bg-red-100 {
  background-color: #fee2e2;
}

.text-red-800 {
  color: #991b1b;
}

/* 渐变背景 */
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-blue-50 {
  --tw-gradient-from: #eff6ff;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(239, 246, 255, 0));
}

.to-indigo-50 {
  --tw-gradient-to: #eef2ff;
}
</style>
