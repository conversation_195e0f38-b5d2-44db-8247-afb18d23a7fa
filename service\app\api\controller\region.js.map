{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\region.js"], "names": ["Base", "require", "module", "exports", "infoAction", "region", "model", "getRegionInfo", "get", "success", "listAction", "regionList", "getRegionList", "dataAction", "parentId", "post", "info", "where", "parent_id", "getField", "codeAction", "province", "city", "country", "provinceInfo", "name", "field", "find", "province_id", "id", "cityInfo", "city_id", "countryInfo", "country_id", "data"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACAC,OAAOC,OAAP,GAAiB,cAAcH,IAAd,CAAmB;AAC1BI,cAAN,GAAmB;AAAA;;AAAA;AACf,kBAAMC,SAAS,MAAM,MAAKC,KAAL,CAAW,QAAX,EAAqBC,aAArB,CAAmC,MAAKC,GAAL,CAAS,UAAT,CAAnC,CAArB;AACA,mBAAO,MAAKC,OAAL,CAAaJ,MAAb,CAAP;AAFe;AAGlB;AACKK,cAAN,GAAmB;AAAA;;AAAA;AACf,kBAAMC,aAAa,MAAM,OAAKL,KAAL,CAAW,QAAX,EAAqBM,aAArB,CAAmC,OAAKJ,GAAL,CAAS,UAAT,CAAnC,CAAzB;AACA,mBAAO,OAAKC,OAAL,CAAaE,UAAb,CAAP;AAFe;AAGlB;AACKE,cAAN,GAAmB;AAAA;;AAAA;AACf,gBAAIC,WAAW,OAAKC,IAAL,CAAU,WAAV,CAAf;AACA,gBAAIC,OAAO,MAAM,OAAKV,KAAL,CAAW,QAAX,EAAqBW,KAArB,CAA2B;AACxCC,2BAAWJ;AAD6B,aAA3B,EAEdK,QAFc,CAEL,SAFK,CAAjB;AAGA,mBAAO,OAAKV,OAAL,CAAaO,IAAb,CAAP;AALe;AAMlB;AACKI,cAAN,GAAmB;AAAA;;AAAA;AACf,gBAAIC,WAAW,OAAKN,IAAL,CAAU,UAAV,CAAf;AACA,gBAAIO,OAAO,OAAKP,IAAL,CAAU,MAAV,CAAX;AACA,gBAAIQ,UAAU,OAAKR,IAAL,CAAU,SAAV,CAAd;AACA,gBAAIS,eAAe,MAAM,OAAKlB,KAAL,CAAW,QAAX,EAAqBW,KAArB,CAA2B;AAChDQ,sBAAMJ;AAD0C,aAA3B,EAEtBK,KAFsB,CAEhB,IAFgB,EAEVC,IAFU,EAAzB;AAGA,gBAAIC,cAAcJ,aAAaK,EAA/B;AACA,gBAAIC,WAAW,MAAM,OAAKxB,KAAL,CAAW,QAAX,EAAqBW,KAArB,CAA2B;AAC5CQ,sBAAMH;AADsC,aAA3B,EAElBI,KAFkB,CAEZ,IAFY,EAENC,IAFM,EAArB;AAGA,gBAAII,UAAUD,SAASD,EAAvB;AACA,gBAAIG,cAAc,MAAM,OAAK1B,KAAL,CAAW,QAAX,EAAqBW,KAArB,CAA2B;AAC/CQ,sBAAMF;AADyC,aAA3B,EAErBG,KAFqB,CAEf,IAFe,EAETC,IAFS,EAAxB;AAGA,gBAAIM,aAAaD,YAAYH,EAA7B;AACA,gBAAIK,OAAO;AACPN,6BAAaA,WADN;AAEPG,yBAASA,OAFF;AAGPE,4BAAYA;AAHL,aAAX;AAKA,mBAAO,OAAKxB,OAAL,CAAayB,IAAb,CAAP;AArBe;AAsBlB;AAtC+B,CAApC", "file": "..\\..\\..\\src\\api\\controller\\region.js", "sourcesContent": ["const Base = require('./base.js');\nmodule.exports = class extends Base {\n    async infoAction() {\n        const region = await this.model('region').getRegionInfo(this.get('regionId'));\n        return this.success(region);\n    }\n    async listAction() {\n        const regionList = await this.model('region').getRegionList(this.get('parentId'));\n        return this.success(regionList);\n    }\n    async dataAction() {\n        let parentId = this.post('parent_id');\n        let info = await this.model('region').where({\n            parent_id: parentId\n        }).getField('id,name');\n        return this.success(info);\n    }\n    async codeAction() {\n        let province = this.post('Province');\n        let city = this.post('City');\n        let country = this.post('Country');\n        let provinceInfo = await this.model('region').where({\n            name: province\n        }).field('id').find();\n        let province_id = provinceInfo.id;\n        let cityInfo = await this.model('region').where({\n            name: city\n        }).field('id').find();\n        let city_id = cityInfo.id;\n        let countryInfo = await this.model('region').where({\n            name: country\n        }).field('id').find();\n        let country_id = countryInfo.id;\n        let data = {\n            province_id: province_id,\n            city_id: city_id,\n            country_id: country_id\n        }\n        return this.success(data);\n    }\n};"]}