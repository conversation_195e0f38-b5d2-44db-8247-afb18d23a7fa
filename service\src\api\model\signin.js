module.exports = class extends think.Model {

  get tableName() {
    return 'sign_in_records';
  }
  
  /**
   * 获取用户的签到统计信息
   */
  async getUserSignStats(userId) {
    try {
      // 获取总签到天数
      const totalSignDays = await this.where({
        user_id: userId
      }).count();

      // 获取本月签到天数
      const currentMonth = this.getCurrentMonth();
      const monthSignDays = await this.where({
        user_id: userId,
        sign_date: ['LIKE', `${currentMonth}%`]
      }).count();

      // 获取连续签到天数
      const consecutiveDays = await this.getConsecutiveDays(userId);

      // 获取历史最长连续签到
      const maxConsecutiveDays = await this.getMaxConsecutiveDays(userId);

      return {
        totalSignDays: totalSignDays,
        monthSignDays: monthSignDays,
        consecutiveDays: consecutiveDays,
        maxConsecutiveDays: maxConsecutiveDays
      };
    } catch (error) {
      console.error('获取用户签到统计失败:', error);
      return null;
    }
  }

  /**
   * 获取用户连续签到天数
   */
  async getConsecutiveDays(userId) {
    const today = this.getCurrentDate();
    const yesterday = this.getDateBefore(today, 1);
    
    // 检查昨天是否签到
    const yesterdayRecord = await this.where({
      user_id: userId,
      sign_date: yesterday
    }).find();

    if (think.isEmpty(yesterdayRecord)) {
      return 0;
    }

    // 从昨天开始往前计算
    let consecutiveDays = 0;
    let checkDate = yesterday;

    for (let i = 0; i < 365; i++) {
      const record = await this.where({
        user_id: userId,
        sign_date: checkDate
      }).find();

      if (!think.isEmpty(record)) {
        consecutiveDays++;
        checkDate = this.getDateBefore(checkDate, 1);
      } else {
        break;
      }
    }

    return consecutiveDays;
  }

  /**
   * 获取历史最长连续签到天数
   */
  async getMaxConsecutiveDays(userId) {
    try {
      // 获取用户所有签到记录，按日期排序
      const records = await this.where({
        user_id: userId
      }).field('sign_date').order('sign_date ASC').select();

      if (records.length === 0) {
        return 0;
      }

      let maxConsecutive = 1;
      let currentConsecutive = 1;

      for (let i = 1; i < records.length; i++) {
        const prevDate = new Date(records[i - 1].sign_date);
        const currDate = new Date(records[i].sign_date);
        
        // 计算日期差
        const diffTime = currDate - prevDate;
        const diffDays = diffTime / (1000 * 60 * 60 * 24);

        if (diffDays === 1) {
          // 连续的日期
          currentConsecutive++;
          maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
        } else {
          // 不连续，重置计数
          currentConsecutive = 1;
        }
      }

      return maxConsecutive;
    } catch (error) {
      console.error('获取最长连续签到天数失败:', error);
      return 0;
    }
  }

  /**
   * 获取用户指定月份的签到记录
   */
  async getMonthSignRecords(userId, month) {
    try {
      const startDate = `${month}-01`;
      const endDate = `${month}-31`;
      
      const records = await this.where({
        user_id: userId,
        sign_date: ['BETWEEN', startDate, endDate]
      }).field('sign_date, points_earned, consecutive_days').order('sign_date ASC').select();

      return records;
    } catch (error) {
      console.error('获取月份签到记录失败:', error);
      return [];
    }
  }

  /**
   * 检查用户今日是否已签到
   */
  async checkTodaySigned(userId) {
    const today = this.getCurrentDate();
    
    const record = await this.where({
      user_id: userId,
      sign_date: today
    }).find();

    return !think.isEmpty(record);
  }

  /**
   * 获取签到排行榜
   */
  async getSignRanking(limit = 10) {
    try {
      // 获取连续签到天数排行
      const sql = `
        SELECT 
          sr.user_id,
          u.nickname,
          u.avatar,
          COUNT(*) as total_signs,
          MAX(sr.consecutive_days) as max_consecutive
        FROM sign_in_records sr
        LEFT JOIN user u ON sr.user_id = u.id
        GROUP BY sr.user_id
        ORDER BY max_consecutive DESC, total_signs DESC
        LIMIT ${limit}
      `;

      const rankings = await this.query(sql);
      return rankings;
    } catch (error) {
      console.error('获取签到排行榜失败:', error);
      return [];
    }
  }

  /**
   * 获取系统签到统计
   */
  async getSystemStats() {
    try {
      const today = this.getCurrentDate();
      const currentMonth = this.getCurrentMonth();

      // 今日签到人数
      const todaySignCount = await this.where({
        sign_date: today
      }).count();

      // 本月签到人数
      const monthSignCount = await this.where({
        sign_date: ['LIKE', `${currentMonth}%`]
      }).group('user_id').count();

      // 总签到次数
      const totalSignCount = await this.count();

      // 总用户数
      const totalUserCount = await this.model('user').count();

      return {
        todaySignCount: todaySignCount,
        monthSignCount: monthSignCount,
        totalSignCount: totalSignCount,
        totalUserCount: totalUserCount,
        signRate: totalUserCount > 0 ? (monthSignCount / totalUserCount * 100).toFixed(2) : 0
      };
    } catch (error) {
      console.error('获取系统签到统计失败:', error);
      return null;
    }
  }

  /**
   * 获取当前月份 YYYY-MM
   */
  getCurrentMonth() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  }

  /**
   * 获取当前日期 YYYY-MM-DD
   */
  getCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * 获取指定日期之前的日期
   */
  getDateBefore(dateStr, days) {
    const date = new Date(dateStr);
    date.setDate(date.getDate() - days);
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
};
