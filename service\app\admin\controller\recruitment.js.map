{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\recruitment.js"], "names": ["Base", "require", "moment", "module", "exports", "rulesAction", "model", "rules", "find", "success", "defaultRules", "joinCondition", "conditions", "requirePurchase", "requireAmount", "minAmount", "requireOrders", "minOrders", "applicationMethod", "requireApplicationInfo", "auditMethod", "error", "console", "fail", "saveRulesAction", "rulesData", "post", "hasCondition", "currentTime", "parseInt", "Date", "getTime", "existingRules", "saveData", "join_condition", "JSON", "stringify", "application_method", "require_application_info", "audit_method", "update_time", "id", "where", "update", "add_time", "add", "updateRulesAction", "statsAction", "totalApplications", "audit_status", "count", "pendingApplications", "approvedApplications", "rejectedApplications", "todayStart", "startOf", "unix", "todayEnd", "endOf", "todayApplications", "stats", "approvalRate", "toFixed", "checkConditionsAction", "userId", "get", "rulesModel", "canJoin", "message", "parse", "checkResults", "failedConditions", "userOrders", "user_id", "order_status", "sum", "totalAmount", "parseFloat", "requiredAmount", "push", "type", "required", "current", "passed", "orderCount", "requiredOrders"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;;AAEAE,OAAOC,OAAP,GAAiB,cAAcJ,IAAd,CAAmB;AAChC;;;;AAIMK,eAAN,GAAoB;AAAA;;AAAA;AAChB,gBAAI;AACA,sBAAMC,QAAQ,MAAKA,KAAL,CAAW,mBAAX,CAAd;AACA,sBAAMC,QAAQ,MAAMD,MAAME,IAAN,EAApB;;AAEA,oBAAID,KAAJ,EAAW;AACP,2BAAO,MAAKE,OAAL,CAAaF,KAAb,CAAP;AACH,iBAFD,MAEO;AACH;AACA,0BAAMG,eAAe;AACjBC,uCAAe,iBADE;AAEjBC,oCAAY;AACRC,6CAAiB,KADT;AAERC,2CAAe,IAFP;AAGRC,uCAAW,KAHH;AAIRC,2CAAe,KAJP;AAKRC,uCAAW;AALH,yBAFK;AASjBC,2CAAmB,cATF;AAUjBC,gDAAwB,KAVP;AAWjBC,qCAAa;AAXI,qBAArB;AAaA,2BAAO,MAAKX,OAAL,CAAaC,YAAb,CAAP;AACH;AACJ,aAvBD,CAuBE,OAAOW,KAAP,EAAc;AACZC,wBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,MAAKE,IAAL,CAAU,UAAV,CAAP;AACH;AA3Be;AA4BnB;;AAED;;;;AAIMC,mBAAN,GAAwB;AAAA;;AAAA;AACpB,gBAAI;AACA,sBAAMC,YAAY,OAAKC,IAAL,EAAlB;;AAEA;AACA,oBAAI,CAACD,UAAUd,aAAf,EAA8B;AAC1B,2BAAO,OAAKY,IAAL,CAAU,WAAV,CAAP;AACH;;AAED,oBAAI,CAACE,UAAUP,iBAAf,EAAkC;AAC9B,2BAAO,OAAKK,IAAL,CAAU,SAAV,CAAP;AACH;;AAED,oBAAI,CAACE,UAAUL,WAAf,EAA4B;AACxB,2BAAO,OAAKG,IAAL,CAAU,SAAV,CAAP;AACH;;AAED;AACA,oBAAIE,UAAUd,aAAV,KAA4B,iBAAhC,EAAmD;AAC/C,0BAAMC,aAAaa,UAAUb,UAAV,IAAwB,EAA3C;AACA,0BAAMe,eAAef,WAAWC,eAAX,IACFD,WAAWE,aADT,IAEFF,WAAWI,aAF9B;;AAIA,wBAAI,CAACW,YAAL,EAAmB;AACf,+BAAO,OAAKJ,IAAL,CAAU,aAAV,CAAP;AACH;;AAED,wBAAIX,WAAWE,aAAf,EAA8B;AAC1B,4BAAI,CAACF,WAAWG,SAAZ,IAAyBH,WAAWG,SAAX,GAAuB,IAApD,EAA0D;AACtD,mCAAO,OAAKQ,IAAL,CAAU,gBAAV,CAAP;AACH;AACJ;;AAED,wBAAIX,WAAWI,aAAf,EAA8B;AAC1B,4BAAI,CAACJ,WAAWK,SAAZ,IAAyBL,WAAWK,SAAX,GAAuB,CAApD,EAAuD;AACnD,mCAAO,OAAKM,IAAL,CAAU,aAAV,CAAP;AACH;AACJ;AACJ;;AAED,sBAAMjB,QAAQ,OAAKA,KAAL,CAAW,mBAAX,CAAd;AACA,sBAAMsB,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;;AAEA;AACA,sBAAMC,gBAAgB,MAAM1B,MAAME,IAAN,EAA5B;;AAEA,sBAAMyB,WAAW;AACbC,oCAAgBT,UAAUd,aADb;AAEbC,gCAAYuB,KAAKC,SAAL,CAAeX,UAAUb,UAAV,IAAwB,EAAvC,CAFC;AAGbyB,wCAAoBZ,UAAUP,iBAHjB;AAIboB,8CAA0Bb,UAAUN,sBAAV,GAAmC,CAAnC,GAAuC,CAJpD;AAKboB,kCAAcd,UAAUL,WALX;AAMboB,iCAAaZ;AANA,iBAAjB;;AASA,oBAAII,iBAAiBA,cAAcS,EAAnC,EAAuC;AACnC;AACA,0BAAMnC,MAAMoC,KAAN,CAAY,EAAED,IAAIT,cAAcS,EAApB,EAAZ,EAAsCE,MAAtC,CAA6CV,QAA7C,CAAN;AACH,iBAHD,MAGO;AACH;AACAA,6BAASW,QAAT,GAAoBhB,WAApB;AACA,0BAAMtB,MAAMuC,GAAN,CAAUZ,QAAV,CAAN;AACH;;AAED,uBAAO,OAAKxB,OAAL,CAAa,UAAb,CAAP;AACH,aAjED,CAiEE,OAAOY,KAAP,EAAc;AACZC,wBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,OAAKE,IAAL,CAAU,UAAV,CAAP;AACH;AArEmB;AAsEvB;;AAED;;;;AAIMuB,qBAAN,GAA0B;AAAA;;AAAA;AACtB,mBAAO,OAAKtB,eAAL,EAAP;AADsB;AAEzB;;AAED;;;;AAIMuB,eAAN,GAAoB;AAAA;;AAAA;AAChB,gBAAI;AACA;AACA,sBAAMC,oBAAoB,MAAM,OAAK1C,KAAL,CAAW,cAAX,EAA2BoC,KAA3B,CAAiC;AAC7DO,kCAAc,CAAC,IAAD,EAAO,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAP,CAD+C,CAC7B;AAD6B,iBAAjC,EAE7BC,KAF6B,EAAhC;;AAIA;AACA,sBAAMC,sBAAsB,MAAM,OAAK7C,KAAL,CAAW,cAAX,EAA2BoC,KAA3B,CAAiC;AAC/DO,kCAAc;AADiD,iBAAjC,EAE/BC,KAF+B,EAAlC;;AAIA;AACA,sBAAME,uBAAuB,MAAM,OAAK9C,KAAL,CAAW,cAAX,EAA2BoC,KAA3B,CAAiC;AAChEO,kCAAc;AADkD,iBAAjC,EAEhCC,KAFgC,EAAnC;;AAIA;AACA,sBAAMG,uBAAuB,MAAM,OAAK/C,KAAL,CAAW,cAAX,EAA2BoC,KAA3B,CAAiC;AAChEO,kCAAc;AADkD,iBAAjC,EAEhCC,KAFgC,EAAnC;;AAIA;AACA,sBAAMI,aAAapD,SAASqD,OAAT,CAAiB,KAAjB,EAAwBC,IAAxB,EAAnB;AACA,sBAAMC,WAAWvD,SAASwD,KAAT,CAAe,KAAf,EAAsBF,IAAtB,EAAjB;AACA,sBAAMG,oBAAoB,MAAM,OAAKrD,KAAL,CAAW,cAAX,EAA2BoC,KAA3B,CAAiC;AAC7DE,8BAAU,CAAC,SAAD,EAAYU,UAAZ,EAAwBG,QAAxB;AADmD,iBAAjC,EAE7BP,KAF6B,EAAhC;;AAIA,sBAAMU,QAAQ;AACVZ,uCAAmBA,iBADT;AAEVG,yCAAqBA,mBAFX;AAGVC,0CAAsBA,oBAHZ;AAIVC,0CAAsBA,oBAJZ;AAKVM,uCAAmBA,iBALT;AAMVE,kCAAcb,oBAAoB,CAApB,GAAwB,CAAEI,uBAAuBJ,iBAAxB,GAA6C,GAA9C,EAAmDc,OAAnD,CAA2D,CAA3D,CAAxB,GAAwF;AAN5F,iBAAd;;AASA,uBAAO,OAAKrD,OAAL,CAAamD,KAAb,CAAP;AACH,aAtCD,CAsCE,OAAOvC,KAAP,EAAc;AACZC,wBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,OAAKE,IAAL,CAAU,UAAV,CAAP;AACH;AA1Ce;AA2CnB;;AAED;;;;AAIMwC,yBAAN,GAA8B;AAAA;;AAAA;AAC1B,gBAAI;AACA,sBAAMC,SAAS,OAAKC,GAAL,CAAS,SAAT,CAAf;;AAEA,oBAAI,CAACD,MAAL,EAAa;AACT,2BAAO,OAAKzC,IAAL,CAAU,UAAV,CAAP;AACH;;AAED;AACA,sBAAM2C,aAAa,OAAK5D,KAAL,CAAW,mBAAX,CAAnB;AACA,sBAAMC,QAAQ,MAAM2D,WAAW1D,IAAX,EAApB;;AAEA,oBAAI,CAACD,KAAD,IAAUA,MAAM2B,cAAN,KAAyB,eAAvC,EAAwD;AACpD,2BAAO,OAAKzB,OAAL,CAAa;AAChB0D,iCAAS,IADO;AAEhBC,iCAAS;AAFO,qBAAb,CAAP;AAIH;;AAED,sBAAMxD,aAAauB,KAAKkC,KAAL,CAAW9D,MAAMK,UAAN,IAAoB,IAA/B,CAAnB;AACA,sBAAM0D,eAAe;AACjBH,6BAAS,IADQ;AAEjBvD,gCAAY,EAFK;AAGjB2D,sCAAkB;AAHD,iBAArB;;AAMA;AACA,oBAAI3D,WAAWE,aAAf,EAA8B;AAC1B,0BAAM0D,aAAa,MAAM,OAAKlE,KAAL,CAAW,OAAX,EAAoBoC,KAApB,CAA0B;AAC/C+B,iCAAST,MADsC;AAE/CU,sCAAc,CAAC,IAAD,EAAO,GAAP,CAFiC,CAErB;AAFqB,qBAA1B,EAGtBC,GAHsB,CAGlB,cAHkB,CAAzB;;AAKA,0BAAMC,cAAcC,WAAWL,cAAc,CAAzB,CAApB;AACA,0BAAMM,iBAAiBD,WAAWjE,WAAWG,SAAX,IAAwB,CAAnC,CAAvB;;AAEA,wBAAI6D,eAAeE,cAAnB,EAAmC;AAC/BR,qCAAa1D,UAAb,CAAwBmE,IAAxB,CAA6B;AACzBC,kCAAM,QADmB;AAEzBC,sCAAUH,cAFe;AAGzBI,qCAASN,WAHgB;AAIzBO,oCAAQ;AAJiB,yBAA7B;AAMH,qBAPD,MAOO;AACHb,qCAAa1D,UAAb,CAAwBmE,IAAxB,CAA6B;AACzBC,kCAAM,QADmB;AAEzBC,sCAAUH,cAFe;AAGzBI,qCAASN,WAHgB;AAIzBO,oCAAQ;AAJiB,yBAA7B;AAMAb,qCAAaC,gBAAb,CAA8BQ,IAA9B,CAAmC,QAAnC;AACAT,qCAAaH,OAAb,GAAuB,KAAvB;AACH;AACJ;;AAED;AACA,oBAAIvD,WAAWI,aAAf,EAA8B;AAC1B,0BAAMoE,aAAa,MAAM,OAAK9E,KAAL,CAAW,OAAX,EAAoBoC,KAApB,CAA0B;AAC/C+B,iCAAST,MADsC;AAE/CU,sCAAc,CAAC,IAAD,EAAO,GAAP,CAFiC,CAErB;AAFqB,qBAA1B,EAGtBxB,KAHsB,EAAzB;;AAKA,0BAAMmC,iBAAiBxD,SAASjB,WAAWK,SAAX,IAAwB,CAAjC,CAAvB;;AAEA,wBAAImE,cAAcC,cAAlB,EAAkC;AAC9Bf,qCAAa1D,UAAb,CAAwBmE,IAAxB,CAA6B;AACzBC,kCAAM,QADmB;AAEzBC,sCAAUI,cAFe;AAGzBH,qCAASE,UAHgB;AAIzBD,oCAAQ;AAJiB,yBAA7B;AAMH,qBAPD,MAOO;AACHb,qCAAa1D,UAAb,CAAwBmE,IAAxB,CAA6B;AACzBC,kCAAM,QADmB;AAEzBC,sCAAUI,cAFe;AAGzBH,qCAASE,UAHgB;AAIzBD,oCAAQ;AAJiB,yBAA7B;AAMAb,qCAAaC,gBAAb,CAA8BQ,IAA9B,CAAmC,QAAnC;AACAT,qCAAaH,OAAb,GAAuB,KAAvB;AACH;AACJ;;AAED;AACA,oBAAIvD,WAAWC,eAAf,EAAgC;AAC5B;AACA;AACAyD,iCAAa1D,UAAb,CAAwBmE,IAAxB,CAA6B;AACzBC,8BAAM,UADmB;AAEzBC,kCAAU,MAFe;AAGzBC,iCAAS,KAHgB;AAIzBC,gCAAQ;AAJiB,qBAA7B;AAMH;;AAED,uBAAO,OAAK1E,OAAL,CAAa6D,YAAb,CAAP;AACH,aA/FD,CA+FE,OAAOjD,KAAP,EAAc;AACZC,wBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,OAAKE,IAAL,CAAU,UAAV,CAAP;AACH;AAnGyB;AAoG7B;AAhR+B,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\recruitment.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\n\nmodule.exports = class extends Base {\n    /**\n     * 获取招募规则\n     * @return {Promise} []\n     */\n    async rulesAction() {\n        try {\n            const model = this.model('recruitment_rules');\n            const rules = await model.find();\n            \n            if (rules) {\n                return this.success(rules);\n            } else {\n                // 返回默认规则\n                const defaultRules = {\n                    joinCondition: 'with_conditions',\n                    conditions: {\n                        requirePurchase: false,\n                        requireAmount: true,\n                        minAmount: 99.00,\n                        requireOrders: false,\n                        minOrders: 1\n                    },\n                    applicationMethod: 'manual_apply',\n                    requireApplicationInfo: false,\n                    auditMethod: 'auto_audit'\n                };\n                return this.success(defaultRules);\n            }\n        } catch (error) {\n            console.error('获取招募规则失败:', error);\n            return this.fail('获取招募规则失败');\n        }\n    }\n    \n    /**\n     * 保存招募规则\n     * @return {Promise} []\n     */\n    async saveRulesAction() {\n        try {\n            const rulesData = this.post();\n            \n            // 验证数据\n            if (!rulesData.joinCondition) {\n                return this.fail('请选择加入条件类型');\n            }\n            \n            if (!rulesData.applicationMethod) {\n                return this.fail('请选择申请方式');\n            }\n            \n            if (!rulesData.auditMethod) {\n                return this.fail('请选择审核方式');\n            }\n            \n            // 如果是有条件加入，验证条件设置\n            if (rulesData.joinCondition === 'with_conditions') {\n                const conditions = rulesData.conditions || {};\n                const hasCondition = conditions.requirePurchase || \n                                   conditions.requireAmount || \n                                   conditions.requireOrders;\n                \n                if (!hasCondition) {\n                    return this.fail('请至少设置一个加入条件');\n                }\n                \n                if (conditions.requireAmount) {\n                    if (!conditions.minAmount || conditions.minAmount < 0.01) {\n                        return this.fail('自购金额最低设置为0.01元');\n                    }\n                }\n                \n                if (conditions.requireOrders) {\n                    if (!conditions.minOrders || conditions.minOrders < 1) {\n                        return this.fail('消费笔数最低设置为1笔');\n                    }\n                }\n            }\n            \n            const model = this.model('recruitment_rules');\n            const currentTime = parseInt(new Date().getTime() / 1000);\n            \n            // 检查是否已存在规则\n            const existingRules = await model.find();\n            \n            const saveData = {\n                join_condition: rulesData.joinCondition,\n                conditions: JSON.stringify(rulesData.conditions || {}),\n                application_method: rulesData.applicationMethod,\n                require_application_info: rulesData.requireApplicationInfo ? 1 : 0,\n                audit_method: rulesData.auditMethod,\n                update_time: currentTime\n            };\n            \n            if (existingRules && existingRules.id) {\n                // 更新现有规则\n                await model.where({ id: existingRules.id }).update(saveData);\n            } else {\n                // 创建新规则\n                saveData.add_time = currentTime;\n                await model.add(saveData);\n            }\n            \n            return this.success('招募规则保存成功');\n        } catch (error) {\n            console.error('保存招募规则失败:', error);\n            return this.fail('保存招募规则失败');\n        }\n    }\n    \n    /**\n     * 更新招募规则\n     * @return {Promise} []\n     */\n    async updateRulesAction() {\n        return this.saveRulesAction();\n    }\n    \n    /**\n     * 获取招募统计\n     * @return {Promise} []\n     */\n    async statsAction() {\n        try {\n            // 总申请人数\n            const totalApplications = await this.model('distributors').where({\n                audit_status: ['IN', [0, 1, 2]] // 0:待审核 1:已通过 2:已拒绝\n            }).count();\n            \n            // 待审核人数\n            const pendingApplications = await this.model('distributors').where({\n                audit_status: 0\n            }).count();\n            \n            // 已通过人数\n            const approvedApplications = await this.model('distributors').where({\n                audit_status: 1\n            }).count();\n            \n            // 已拒绝人数\n            const rejectedApplications = await this.model('distributors').where({\n                audit_status: 2\n            }).count();\n            \n            // 今日新增申请\n            const todayStart = moment().startOf('day').unix();\n            const todayEnd = moment().endOf('day').unix();\n            const todayApplications = await this.model('distributors').where({\n                add_time: ['BETWEEN', todayStart, todayEnd]\n            }).count();\n            \n            const stats = {\n                totalApplications: totalApplications,\n                pendingApplications: pendingApplications,\n                approvedApplications: approvedApplications,\n                rejectedApplications: rejectedApplications,\n                todayApplications: todayApplications,\n                approvalRate: totalApplications > 0 ? ((approvedApplications / totalApplications) * 100).toFixed(2) : '0.00'\n            };\n            \n            return this.success(stats);\n        } catch (error) {\n            console.error('获取招募统计失败:', error);\n            return this.fail('获取招募统计失败');\n        }\n    }\n    \n    /**\n     * 检查用户是否满足招募条件\n     * @return {Promise} []\n     */\n    async checkConditionsAction() {\n        try {\n            const userId = this.get('user_id');\n            \n            if (!userId) {\n                return this.fail('用户ID不能为空');\n            }\n            \n            // 获取招募规则\n            const rulesModel = this.model('recruitment_rules');\n            const rules = await rulesModel.find();\n            \n            if (!rules || rules.join_condition === 'no_conditions') {\n                return this.success({\n                    canJoin: true,\n                    message: '无条件加入'\n                });\n            }\n            \n            const conditions = JSON.parse(rules.conditions || '{}');\n            const checkResults = {\n                canJoin: true,\n                conditions: [],\n                failedConditions: []\n            };\n            \n            // 检查自购金额条件\n            if (conditions.requireAmount) {\n                const userOrders = await this.model('order').where({\n                    user_id: userId,\n                    order_status: ['>=', 300] // 已支付订单\n                }).sum('actual_price');\n                \n                const totalAmount = parseFloat(userOrders || 0);\n                const requiredAmount = parseFloat(conditions.minAmount || 0);\n                \n                if (totalAmount >= requiredAmount) {\n                    checkResults.conditions.push({\n                        type: 'amount',\n                        required: requiredAmount,\n                        current: totalAmount,\n                        passed: true\n                    });\n                } else {\n                    checkResults.conditions.push({\n                        type: 'amount',\n                        required: requiredAmount,\n                        current: totalAmount,\n                        passed: false\n                    });\n                    checkResults.failedConditions.push('自购金额不足');\n                    checkResults.canJoin = false;\n                }\n            }\n            \n            // 检查消费笔数条件\n            if (conditions.requireOrders) {\n                const orderCount = await this.model('order').where({\n                    user_id: userId,\n                    order_status: ['>=', 300] // 已支付订单\n                }).count();\n                \n                const requiredOrders = parseInt(conditions.minOrders || 0);\n                \n                if (orderCount >= requiredOrders) {\n                    checkResults.conditions.push({\n                        type: 'orders',\n                        required: requiredOrders,\n                        current: orderCount,\n                        passed: true\n                    });\n                } else {\n                    checkResults.conditions.push({\n                        type: 'orders',\n                        required: requiredOrders,\n                        current: orderCount,\n                        passed: false\n                    });\n                    checkResults.failedConditions.push('消费笔数不足');\n                    checkResults.canJoin = false;\n                }\n            }\n            \n            // 检查指定商品购买条件\n            if (conditions.requirePurchase) {\n                // 这里可以添加指定商品购买检查逻辑\n                // 暂时设为通过\n                checkResults.conditions.push({\n                    type: 'purchase',\n                    required: '指定商品',\n                    current: '已购买',\n                    passed: true\n                });\n            }\n            \n            return this.success(checkResults);\n        } catch (error) {\n            console.error('检查招募条件失败:', error);\n            return this.fail('检查招募条件失败');\n        }\n    }\n};\n"]}