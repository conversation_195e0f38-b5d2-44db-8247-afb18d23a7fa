function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

const Base = require('./base.js');

module.exports = class extends Base {

  /**
   * 获取签到数据
   * GET /api/signin/data
   */
  dataAction() {
    var _this = this;

    return _asyncToGenerator(function* () {
      console.log('=== 签到数据请求开始 ===');

      // 获取openid参数
      const openid = _this.get('openid');
      console.log('获取到的openid:', openid);

      if (!openid) {
        console.log('openid参数缺失');
        return _this.fail(400, 'openid参数必须提供');
      }

      // 通过openid查询用户
      const user = yield _this.model('user').where({
        weixin_openid: openid
      }).find();

      if (think.isEmpty(user)) {
        console.log('用户不存在，openid:', openid);
        return _this.fail(404, '用户不存在，请先完成授权');
      }

      const userId = user.id;
      console.log('查询到用户ID:', userId, '昵称:', user.nickname);

      try {
        const month = _this.get('month') || _this.getCurrentMonth();
        console.log('=== 获取签到数据 ===');
        console.log('用户ID:', userId);
        console.log('查询月份:', month);

        // 获取用户积分信息
        const userPoints = yield _this.getUserPoints(userId);

        // 获取连续签到天数
        const consecutiveDays = yield _this.getConsecutiveDays(userId);

        // 获取指定月份的签到记录
        const signedRecords = yield _this.getMonthSignRecords(userId, month);
        const signedDates = signedRecords.map(function (record) {
          return record.sign_date;
        });

        // 检查今日是否已签到
        const today = _this.getCurrentDate();
        const todaySigned = signedDates.includes(today);

        // 获取月度统计
        const monthlyStats = yield _this.getMonthlyStats(userId, month);

        const result = {
          consecutiveDays: consecutiveDays,
          totalPoints: userPoints && userPoints.total_points || 0,
          availablePoints: userPoints && userPoints.available_points || 0,
          todaySigned: todaySigned,
          signedDates: signedDates,
          monthlyStats: monthlyStats
        };

        console.log('返回签到数据:', result);
        return _this.success(result);
      } catch (error) {
        console.error('获取签到数据失败:', error);
        return _this.fail(500, '获取签到数据失败');
      }
    })();
  }

  /**
   * 执行签到
   * POST /api/signin/checkin
   */
  checkinAction() {
    var _this2 = this;

    return _asyncToGenerator(function* () {
      console.log('=== 签到执行请求开始 ===');

      // 获取openid参数
      const openid = _this2.post('openid');
      console.log('获取到的openid:', openid);

      if (!openid) {
        console.log('openid参数缺失');
        return _this2.fail(400, 'openid参数必须提供');
      }

      // 通过openid查询用户
      const user = yield _this2.model('user').where({
        weixin_openid: openid
      }).find();

      if (think.isEmpty(user)) {
        console.log('用户不存在，openid:', openid);
        return _this2.fail(404, '用户不存在，请先完成授权');
      }

      const userId = user.id;
      console.log('查询到用户ID:', userId, '昵称:', user.nickname);

      try {
        const signDate = _this2.post('date') || _this2.getCurrentDate();
        const today = _this2.getCurrentDate();

        console.log('=== 执行签到 ===');
        console.log('用户ID:', userId);
        console.log('签到日期:', signDate);
        console.log('当前日期:', today);

        // 验证签到日期
        if (signDate !== today) {
          return _this2.fail(400, '只能签到当天');
        }

        // 检查是否已签到
        const existingRecord = yield _this2.model('signin').where({
          user_id: userId,
          sign_date: signDate
        }).find();

        if (!think.isEmpty(existingRecord)) {
          return _this2.fail(400, '今日已签到');
        }

        // 计算连续签到天数
        const consecutiveDays = yield _this2.calculateConsecutiveDays(userId, signDate);

        // 计算积分奖励
        const pointsInfo = _this2.calculatePoints(consecutiveDays);

        console.log('连续签到天数:', consecutiveDays);
        console.log('积分奖励:', pointsInfo);

        // 简化执行，先不用事务
        console.log('开始执行签到...');

        try {
          // 1. 插入签到记录
          console.log('插入签到记录...');
          const signRecord = yield _this2.model('signin').add({
            user_id: userId,
            sign_date: signDate,
            points_earned: pointsInfo.totalPoints,
            consecutive_days: consecutiveDays,
            is_bonus: pointsInfo.bonusPoints > 0 ? 1 : 0,
            created_at: new Date()
          });
          console.log('签到记录插入成功，ID:', signRecord);

          // 积分系统已移除，签到功能保留但不发放积分
          console.log('签到完成（积分系统已移除）');
          console.log('原本会获得积分:', pointsInfo.totalPoints);

          console.log('签到执行完成');
        } catch (insertError) {
          console.error('签到执行失败:', insertError);
          throw insertError;
        }

        // 获取更新后的用户积分
        const updatedPoints = yield _this2.getUserPoints(userId);

        const result = {
          success: true,
          earnedPoints: pointsInfo.totalPoints,
          consecutiveDays: consecutiveDays,
          totalPoints: updatedPoints.total_points,
          bonusInfo: {
            isBonus: pointsInfo.bonusPoints > 0,
            bonusType: _this2.getBonusType(consecutiveDays),
            bonusPoints: pointsInfo.bonusPoints
          }
        };

        console.log('签到成功，返回结果:', result);
        return _this2.success(result);
      } catch (error) {
        console.error('签到失败:', error);
        return _this2.fail(500, '签到失败，请重试');
      }
    })();
  }

  /**
   * 获取用户积分信息（积分系统已移除，返回默认值）
   */
  getUserPoints(userId) {
    return _asyncToGenerator(function* () {
      return {
        total_points: 0,
        available_points: 0,
        used_points: 0
      };
    })();
  }

  /**
   * 获取连续签到天数
   */
  getConsecutiveDays(userId) {
    var _this3 = this;

    return _asyncToGenerator(function* () {
      return yield _this3.model('signin').getConsecutiveDays(userId);
    })();
  }

  /**
   * 计算连续签到天数（包含今天）
   */
  calculateConsecutiveDays(userId, signDate) {
    var _this4 = this;

    return _asyncToGenerator(function* () {
      const consecutiveDays = yield _this4.getConsecutiveDays(userId);
      return consecutiveDays + 1; // 加上今天
    })();
  }

  /**
   * 获取指定月份的签到记录
   */
  getMonthSignRecords(userId, month) {
    var _this5 = this;

    return _asyncToGenerator(function* () {
      return yield _this5.model('signin').getMonthSignRecords(userId, month);
    })();
  }

  /**
   * 获取月度统计
   */
  getMonthlyStats(userId, month) {
    var _this6 = this;

    return _asyncToGenerator(function* () {
      const [year, monthNum] = month.split('-');
      const daysInMonth = new Date(year, monthNum, 0).getDate();

      const signedRecords = yield _this6.getMonthSignRecords(userId, month);
      const signedDays = signedRecords.length;

      return {
        totalDays: daysInMonth,
        signedDays: signedDays,
        isFullAttendance: signedDays === daysInMonth
      };
    })();
  }

  /**
   * 计算积分奖励
   */
  calculatePoints(consecutiveDays) {
    const basePoints = 5; // 基础积分
    let bonusPoints = 0; // 奖励积分

    // 连续签到奖励
    if (consecutiveDays >= 30) {
      bonusPoints = 45; // 连续30天额外奖励45积分
    } else if (consecutiveDays >= 15) {
      bonusPoints = 15; // 连续15天额外奖励15积分
    } else if (consecutiveDays >= 7) {
      bonusPoints = 10; // 连续7天额外奖励10积分
    } else if (consecutiveDays >= 3) {
      bonusPoints = 5; // 连续3天额外奖励5积分
    }

    return {
      basePoints: basePoints,
      bonusPoints: bonusPoints,
      totalPoints: basePoints + bonusPoints
    };
  }

  /**
   * 获取奖励类型
   */
  getBonusType(consecutiveDays) {
    if (consecutiveDays >= 30) return 'consecutive_30';
    if (consecutiveDays >= 15) return 'consecutive_15';
    if (consecutiveDays >= 7) return 'consecutive_7';
    if (consecutiveDays >= 3) return 'consecutive_3';
    return 'none';
  }

  /**
   * 更新用户积分
   */
  updateUserPoints(userId, earnedPoints) {
    var _this7 = this;

    return _asyncToGenerator(function* () {
      return yield _this7.model('points').addUserPoints(userId, earnedPoints, 'signin', null, '每日签到');
    })();
  }

  /**
   * 添加积分日志
   */
  addPointsLog(userId, pointsChange, pointsType, sourceId, description) {
    var _this8 = this;

    return _asyncToGenerator(function* () {
      return yield _this8.model('points').addUserPoints(userId, pointsChange, pointsType, sourceId, description);
    })();
  }

  /**
   * 获取当前月份 YYYY-MM
   */
  getCurrentMonth() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  }

  /**
   * 获取当前日期 YYYY-MM-DD
   */
  getCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * 获取指定日期之前的日期
   */
  getDateBefore(dateStr, days) {
    const date = new Date(dateStr);
    date.setDate(date.getDate() - days);

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

};