<template>
  <div class="product-pool-page">
    <!-- 页面头部 -->
    <div class="page-header bg-white shadow-sm border-b">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">商品分销池</h1>
            <p class="text-sm text-gray-500 mt-1">管理系统中所有可分销的商品</p>
          </div>
          <!-- 操作按钮已移除 -->
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="p-6 max-w-7xl mx-auto">
      <!-- 分销池统计卡片 -->
      <div class="grid grid-cols-6 gap-4 mb-6">
        <div class="bg-white rounded-lg shadow-sm border p-4">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="ri-database-line text-lg text-blue-600"></i>
            </div>
            <div class="ml-3">
              <p class="text-xs text-gray-500">池子总容量</p>
              <p class="text-xl font-bold text-gray-900">{{ statistics.totalProducts }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-4">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="ri-check-line text-lg text-green-600"></i>
            </div>
            <div class="ml-3">
              <p class="text-xs text-gray-500">已激活分销</p>
              <p class="text-xl font-bold text-gray-900">{{ distributedProducts.length }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-4">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
              <i class="ri-archive-line text-lg text-gray-600"></i>
            </div>
            <div class="ml-3">
              <p class="text-xs text-gray-500">未入池商品</p>
              <p class="text-xl font-bold text-gray-900">{{ undistributedProducts.length }}</p>
            </div>
          </div>
        </div>



        <div class="bg-white rounded-lg shadow-sm border p-4">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
              <i class="ri-money-dollar-circle-line text-lg text-yellow-600"></i>
            </div>
            <div class="ml-3">
              <p class="text-xs text-gray-500">总佣金池</p>
              <p class="text-xl font-bold text-gray-900">¥{{ (statistics.commissionStats && statistics.commissionStats.totalCommissionPool) || statistics.totalCommission }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-4">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <i class="ri-pie-chart-line text-lg text-purple-600"></i>
            </div>
            <div class="ml-3">
              <p class="text-xs text-gray-500">池子利用率</p>
              <p class="text-xl font-bold text-gray-900">{{ statistics.totalProducts > 0 ? Math.round((distributedProducts.length / statistics.totalProducts) * 100) : 0 }}%</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 分销池状态Tab -->
      <div class="bg-white rounded-lg shadow-sm border mb-6">
        <div class="border-b">
          <nav class="flex space-x-8 px-6" aria-label="Tabs">
            <button
              @click="activeTab = 'all'"
              :class="[
                'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
                activeTab === 'all'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              <i class="ri-database-line mr-1"></i>
              池子总览 ({{ allProducts.length }})
            </button>
            <button
              @click="activeTab = 'distributed'"
              :class="[
                'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
                activeTab === 'distributed'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              <i class="ri-check-line mr-1"></i>
              已激活分销 ({{ distributedProducts.length }})
            </button>
            <button
              @click="activeTab = 'undistributed'"
              :class="[
                'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
                activeTab === 'undistributed'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              <i class="ri-inbox-line mr-1"></i>
              未入池商品 ({{ undistributedProducts.length }})
            </button>
          </nav>
        </div>

        <!-- 搜索和筛选 -->
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">搜索商品</label>
              <input
                v-model="searchQuery"
                type="text"
                placeholder="输入商品名称或编号"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">商品分类</label>
              <select
                v-model="filterCategory"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">全部分类</option>
                <option v-for="category in categories" :key="category.id" :value="category.name">
                  {{ category.name }}
                </option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">商品状态</label>
              <select
                v-model="filterStatus"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">全部状态</option>
                <option value="1">上架中</option>
                <option value="0">已下架</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">佣金范围</label>
              <select
                v-model="filterCommission"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">全部佣金</option>
                <option value="low">0-5%</option>
                <option value="medium">5-15%</option>
                <option value="high">15%以上</option>
              </select>
            </div>

            <div class="flex items-end">
              <button @click="resetFilters" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                <i class="ri-refresh-line mr-2"></i>
                重置
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 商品列表 -->
      <div class="bg-white rounded-lg shadow-sm border">
        <div class="px-6 py-4 border-b">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900">分销商品列表</h2>
            <div class="flex items-center space-x-2">
              <button 
                @click="viewMode = 'grid'"
                :class="[
                  'px-3 py-1 text-sm rounded-md transition-colors',
                  viewMode === 'grid' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                ]"
              >
                <i class="ri-grid-line mr-1"></i>
                网格视图
              </button>
              <button 
                @click="viewMode = 'list'"
                :class="[
                  'px-3 py-1 text-sm rounded-md transition-colors',
                  viewMode === 'list' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                ]"
              >
                <i class="ri-list-check mr-1"></i>
                列表视图
              </button>
            </div>
          </div>
        </div>

        <!-- 批量操作工具栏 -->
        <div v-if="selectedProducts.length > 0" class="mx-6 mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <span class="text-sm text-gray-600">已选择 {{ selectedProducts.length }} 个商品</span>
              <button @click="clearSelection" class="text-sm text-blue-600 hover:text-blue-800">清除选择</button>
            </div>
            <div class="flex space-x-2">
              <button @click="batchAddToPool" class="px-4 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700">
                批量投入分销池
              </button>
              <button @click="batchRemoveFromPool" class="px-4 py-2 bg-red-600 text-white text-sm rounded hover:bg-red-700">
                批量移出分销池
              </button>
            </div>
          </div>
        </div>

        <!-- 网格视图 -->
        <div v-if="viewMode === 'grid'" class="p-6">
          <!-- 全选控制 -->
          <div class="mb-4 flex items-center space-x-4">
            <label class="flex items-center">
              <input type="checkbox" :checked="isAllSelected" @change="toggleSelectAll" class="w-4 h-4 text-blue-600 rounded">
              <span class="ml-2 text-sm text-gray-600">全选</span>
            </label>
            <button @click="selectNone" class="text-sm text-gray-600 hover:text-gray-800">全不选</button>
            <button @click="selectInverse" class="text-sm text-gray-600 hover:text-gray-800">反选</button>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <div v-for="product in paginatedProducts" :key="product.id" class="bg-white border rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
              <!-- 商品图片 -->
              <div class="relative">
                <img :src="product.https_pic_url || product.list_pic_url" :alt="product.name" class="w-full h-48 object-cover">
                <div class="absolute top-2 left-2">
                  <input type="checkbox" :value="product.id" v-model="selectedProducts" class="w-4 h-4 text-blue-600 rounded">
                </div>
                <div class="absolute top-2 right-2 flex flex-col space-y-1">
                  <span :class="[
                    'px-2 py-1 text-xs rounded-full',
                    product.is_on_sale === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  ]">
                    {{ product.is_on_sale === 1 ? '上架中' : '已下架' }}
                  </span>
                  <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                    {{ getCategoryName(product.category_id) }}
                  </span>
                </div>
                <div v-if="product.isDistributed" class="absolute bottom-2 left-2">
                  <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                    已分销
                  </span>
                </div>
              </div>

              <!-- 商品信息 -->
              <div class="p-4">
                <h3 class="font-medium text-gray-900 mb-2 line-clamp-2">{{ product.name }}</h3>
                <p class="text-xs text-gray-500 mb-2">{{ product.goods_brief }}</p>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-gray-500">商品价格:</span>
                    <span class="font-medium text-red-600">¥{{ product.retail_price }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">佣金比例:</span>
                    <span class="font-medium text-green-600">{{ product.commission_rate || 0 }}%</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">预计佣金:</span>
                    <span class="font-medium text-yellow-600">¥{{ product.estimated_commission || '0.00' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-500">销量:</span>
                    <span class="font-medium">{{ product.sell_volume }}</span>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="mt-4 space-y-2">
                  <div class="flex space-x-2">
                    <button @click="viewProductDetail(product)" class="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                      详情
                    </button>
                    <button v-if="product.isDistributed" @click="setProductCommission(product)" class="flex-1 px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700">
                      设置佣金
                    </button>
                  </div>
                  <button @click="handleDistributionAction(product)" :class="[
                    'w-full px-3 py-2 text-sm rounded transition-colors',
                    getPoolActionButtonClass(product)
                  ]">
                    {{ getPoolActionText(product) }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 列表视图 -->
        <div v-else>
          <table class="w-full table-fixed">
            <thead class="bg-gray-50">
              <tr>
                <th class="w-12 px-2 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input type="checkbox" :checked="isAllSelected" @change="toggleSelectAll" class="w-4 h-4 text-blue-600 rounded">
                </th>
                <th class="w-1/4 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">商品信息</th>
                <th class="w-16 px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
                <th class="w-20 px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">价格</th>
                <th class="w-16 px-2 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">佣金率</th>
                <th class="w-16 px-2 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">等级</th>
                <th class="w-16 px-2 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">销量</th>
                <th class="w-20 px-2 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th class="w-24 px-2 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="product in paginatedProducts" :key="product.id" class="hover:bg-gray-50">
                <!-- 复选框 -->
                <td class="px-2 py-3 text-center">
                  <input type="checkbox" :value="product.id" v-model="selectedProducts" class="w-4 h-4 text-blue-600 rounded">
                </td>
                <!-- 商品信息 -->
                <td class="px-4 py-3">
                  <div class="flex items-center">
                    <img :src="product.https_pic_url || product.list_pic_url" :alt="product.name" class="w-10 h-10 rounded-lg object-cover flex-shrink-0">
                    <div class="ml-3 min-w-0 flex-1">
                      <div class="text-sm font-medium text-gray-900 truncate" :title="product.name">{{ product.name }}</div>
                      <div class="text-xs text-gray-500">ID: {{ product.id }}</div>
                    </div>
                  </div>
                </td>

                <!-- 分类 -->
                <td class="px-2 py-3 text-center">
                  <span class="text-xs text-gray-600 truncate" :title="getCategoryName(product.category_id)">
                    {{ getCategoryName(product.category_id) }}
                  </span>
                </td>

                <!-- 价格 -->
                <td class="px-2 py-3 text-center text-sm font-medium text-red-600">
                  ¥{{ product.retail_price }}
                </td>

                <!-- 佣金率 -->
                <td class="px-2 py-3 text-center">
                  <div class="text-xs">
                    <div class="text-green-600 font-medium">{{ product.personal_rate || product.commission_rate || 0 }}%</div>
                    <div class="text-gray-400">个人</div>
                  </div>
                </td>

                <!-- 等级 -->
                <td class="px-2 py-3 text-center">
                  <span :class="[
                    'px-1 py-1 text-xs rounded',
                    getLevelBadgeClass(product.min_level_required || 1)
                  ]">
                    {{ getLevelName(product.min_level_required || 1) }}
                  </span>
                </td>

                <!-- 销量 -->
                <td class="px-2 py-3 text-center text-sm text-gray-900">
                  {{ product.sell_volume || 0 }}
                </td>

                <!-- 状态 -->
                <td class="px-2 py-3 text-center">
                  <div class="space-y-1">
                    <!-- 分销状态 -->
                    <div>
                      <span :class="[
                        'px-2 py-1 text-xs rounded-full',
                        getPoolStatus(product).class
                      ]">
                        {{ getPoolStatus(product).text }}
                      </span>
                    </div>
                    <!-- 上架状态 -->
                    <div>
                      <span :class="[
                        'px-2 py-1 text-xs rounded-full',
                        product.is_on_sale === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      ]">
                        {{ product.is_on_sale === 1 ? '上架' : '下架' }}
                      </span>
                    </div>
                  </div>
                </td>

                <!-- 操作 -->
                <td class="px-2 py-3 text-center">
                  <div class="space-y-1">
                    <div>
                      <button @click="viewProductDetail(product)" class="text-blue-600 hover:text-blue-900 text-xs">
                        详情
                      </button>
                    </div>
                    <div>
                      <button @click="handleDistributionAction(product)" :class="[
                        'text-xs transition-colors',
                        product.is_distributed === 1 ? 'text-red-600 hover:text-red-900' :
                        'text-blue-600 hover:text-blue-900'
                      ]">
                        {{ getPoolActionText(product) }}
                      </button>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div class="px-6 py-4 border-t bg-gray-50">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500">
              显示 {{ Math.min(paginatedProducts.length, pageSize) }} 条，共 {{ filteredProducts.length }} 条记录
            </div>
            <div class="flex items-center space-x-2">
              <button
                @click="goToPage(currentPage - 1)"
                :disabled="currentPage <= 1"
                :class="[
                  'px-3 py-1 border rounded-md text-sm',
                  currentPage <= 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'hover:bg-gray-100 text-gray-700'
                ]"
              >
                上一页
              </button>

              <!-- 页码显示 -->
              <div class="flex items-center space-x-1">
                <template v-if="totalPages <= 7">
                  <button
                    v-for="page in totalPages"
                    :key="page"
                    @click="goToPage(page)"
                    :class="[
                      'px-3 py-1 text-sm rounded-md',
                      page === currentPage
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    ]"
                  >
                    {{ page }}
                  </button>
                </template>
                <template v-else>
                  <button
                    @click="goToPage(1)"
                    :class="[
                      'px-3 py-1 text-sm rounded-md',
                      1 === currentPage
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    ]"
                  >
                    1
                  </button>
                  <span v-if="currentPage > 4" class="px-2 text-gray-500">...</span>
                  <button
                    v-for="page in getVisiblePages()"
                    :key="page"
                    @click="goToPage(page)"
                    :class="[
                      'px-3 py-1 text-sm rounded-md',
                      page === currentPage
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    ]"
                  >
                    {{ page }}
                  </button>
                  <span v-if="currentPage < totalPages - 3" class="px-2 text-gray-500">...</span>
                  <button
                    v-if="totalPages > 1"
                    @click="goToPage(totalPages)"
                    :class="[
                      'px-3 py-1 text-sm rounded-md',
                      totalPages === currentPage
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    ]"
                  >
                    {{ totalPages }}
                  </button>
                </template>
              </div>

              <button
                @click="goToPage(currentPage + 1)"
                :disabled="currentPage >= totalPages"
                :class="[
                  'px-3 py-1 border rounded-md text-sm',
                  currentPage >= totalPages
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'hover:bg-gray-100 text-gray-700'
                ]"
              >
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 商品详情弹窗 -->
    <div v-if="showDetailModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">商品详情</h3>
          <button @click="showDetailModal = false" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>

        <div v-if="selectedProduct" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 商品图片 -->
          <div>
            <img :src="selectedProduct.https_pic_url || selectedProduct.list_pic_url" :alt="selectedProduct.name" class="w-full h-80 object-cover rounded-lg">
          </div>

          <!-- 商品信息 -->
          <div class="space-y-4">
            <div>
              <h4 class="text-xl font-semibold text-gray-900 mb-2">{{ selectedProduct.name }}</h4>
              <p class="text-gray-600">{{ selectedProduct.goods_brief }}</p>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-sm text-gray-500">商品价格</p>
                <p class="text-lg font-semibold text-red-600">¥{{ selectedProduct.retail_price }}</p>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-sm text-gray-500">佣金比例</p>
                <p class="text-lg font-semibold text-green-600">{{ selectedProduct.commission_rate || 0 }}%</p>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-sm text-gray-500">预计佣金</p>
                <p class="text-lg font-semibold text-yellow-600">¥{{ selectedProduct.estimated_commission || '0.00' }}</p>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-sm text-gray-500">商品分类</p>
                <p class="text-lg font-semibold text-blue-600">{{ getCategoryName(selectedProduct.category_id) }}</p>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-sm text-gray-500">销量</p>
                <p class="text-lg font-semibold text-gray-900">{{ selectedProduct.sell_volume }}</p>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-sm text-gray-500">库存</p>
                <p class="text-lg font-semibold text-gray-900">{{ selectedProduct.goods_number }}</p>
              </div>
            </div>

            <div class="bg-gray-50 p-4 rounded-lg">
              <p class="text-sm text-gray-500 mb-2">商品状态</p>
              <div class="flex items-center space-x-3">
                <span :class="[
                  'px-3 py-1 text-sm rounded-full',
                  selectedProduct.is_on_sale === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                ]">
                  {{ selectedProduct.is_on_sale === 1 ? '上架中' : '已下架' }}
                </span>
                <span :class="[
                  'px-3 py-1 text-sm rounded-full',
                  selectedProduct.isDistributed ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'
                ]">
                  {{ selectedProduct.isDistributed ? '已分销' : '未分销' }}
                </span>
              </div>
            </div>

            <div v-if="selectedProduct.isDistributed" class="bg-gray-50 p-4 rounded-lg">
              <p class="text-sm text-gray-500 mb-2">推广链接</p>
              <div class="flex items-center space-x-2">
                <input
                  :value="`https://shop.example.com/product/${selectedProduct.id}?ref=distributor`"
                  readonly
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-white text-sm"
                />
                <button @click="copyPromotionLink(selectedProduct)" class="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                  复制
                </button>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex space-x-3 pt-4 border-t">
              <button v-if="selectedProduct.isDistributed" @click="setProductCommission(selectedProduct)" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                设置佣金
              </button>
              <button @click="handleDistributionAction(selectedProduct)" :class="[
                'px-4 py-2 rounded-md transition-colors',
                selectedProduct.isDistributed
                  ? 'bg-red-600 text-white hover:bg-red-700'
                  : 'bg-purple-600 text-white hover:bg-purple-700'
              ]">
                {{ selectedProduct.isDistributed ? '移除分销' : '加入分销' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 佣金设置弹窗 -->
    <div v-if="showCommissionModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">投入分销池 - 设置分销佣金</h3>
          <button @click="showCommissionModal = false" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>

        <div v-if="selectedProduct" class="space-y-6">
          <!-- 商品信息 -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <div class="flex items-center space-x-4">
              <img :src="selectedProduct.https_pic_url || selectedProduct.list_pic_url" :alt="selectedProduct.name" class="w-16 h-16 rounded-lg object-cover">
              <div>
                <h4 class="font-medium text-gray-900">{{ selectedProduct.name }}</h4>
                <p class="text-sm text-gray-500">商品价格: ¥{{ selectedProduct.retail_price }}</p>
                <p class="text-sm text-gray-500">商品ID: {{ selectedProduct.id }}</p>
                <div class="mt-1">
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                    准备投入分销池
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 分销佣金设置 -->
          <div class="space-y-4">
            <div>
              <h4 class="font-medium text-gray-900">分销佣金比例设置</h4>
              <p class="text-sm text-gray-600">设置该商品的各级分销佣金比例，投入分销池后分销员可获得相应佣金</p>

              <!-- 快捷设置按钮 -->
              <div class="mt-3 flex flex-wrap gap-2">
                <button @click="setQuickCommission('low')" class="px-3 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200">
                  低佣金 (5%+2%+1%+1%)
                </button>
                <button @click="setQuickCommission('medium')" class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                  中等佣金 (8%+3%+1%+2%)
                </button>
                <button @click="setQuickCommission('high')" class="px-3 py-1 text-xs bg-orange-100 text-orange-700 rounded hover:bg-orange-200">
                  高佣金 (12%+5%+2%+3%)
                </button>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">个人佣金比例 (%)</label>
                <input
                  type="number"
                  v-model.number="batchCommissionSettings.personal_rate"
                  min="0"
                  max="100"
                  step="0.1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0.0"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">一级分销佣金 (%)</label>
                <input
                  type="number"
                  v-model.number="batchCommissionSettings.level1_rate"
                  min="0"
                  max="100"
                  step="0.1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0.0"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">二级分销佣金 (%)</label>
                <input
                  type="number"
                  v-model.number="batchCommissionSettings.level2_rate"
                  min="0"
                  max="100"
                  step="0.1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0.0"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">团长佣金 (%)</label>
                <input
                  type="number"
                  v-model.number="batchCommissionSettings.team_leader_rate"
                  min="0"
                  max="100"
                  step="0.1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0.0"
                />
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">最低等级要求</label>
              <select
                v-model="batchCommissionSettings.min_level_required"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="1">普通会员</option>
                <option value="2">银牌分销员</option>
                <option value="3">金牌分销员</option>
                <option value="4">钻石分销员</option>
              </select>
            </div>

            <!-- 预计佣金显示 -->
            <div class="bg-yellow-50 p-4 rounded border">
              <h5 class="font-medium text-gray-900 mb-2">预计佣金收益</h5>
              <div class="grid grid-cols-2 gap-3 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">个人佣金:</span>
                  <span class="font-medium text-green-600">
                    ¥{{ calculateEstimatedCommission(selectedProduct.retail_price, batchCommissionSettings.personal_rate) }}
                  </span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">一级佣金:</span>
                  <span class="font-medium text-blue-600">
                    ¥{{ calculateEstimatedCommission(selectedProduct.retail_price, batchCommissionSettings.level1_rate) }}
                  </span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">二级佣金:</span>
                  <span class="font-medium text-purple-600">
                    ¥{{ calculateEstimatedCommission(selectedProduct.retail_price, batchCommissionSettings.level2_rate) }}
                  </span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">团长佣金:</span>
                  <span class="font-medium text-orange-600">
                    ¥{{ calculateEstimatedCommission(selectedProduct.retail_price, batchCommissionSettings.team_leader_rate) }}
                  </span>
                </div>
              </div>
              <div class="mt-2 pt-2 border-t border-yellow-200">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">总佣金成本:</span>
                  <span class="font-medium text-red-600">
                    ¥{{ calculateEstimatedCommission(selectedProduct.retail_price,
                        batchCommissionSettings.personal_rate +
                        batchCommissionSettings.level1_rate +
                        batchCommissionSettings.level2_rate +
                        batchCommissionSettings.team_leader_rate) }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-end space-x-3 pt-4 border-t">
            <button @click="showCommissionModal = false" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
              取消
            </button>
            <button @click="saveCommissionSettings" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              投入分销池
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量佣金设置弹窗 -->
    <div v-if="showBatchCommissionModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">批量设置分销佣金</h3>
          <button @click="showBatchCommissionModal = false" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>

        <div class="space-y-6">
          <!-- 选中商品信息 -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <p class="text-sm text-gray-600 mb-2">已选择 {{ selectedProducts.length }} 个商品</p>
            <div class="max-h-32 overflow-y-auto">
              <div v-for="productId in selectedProducts.slice(0, 5)" :key="productId" class="text-xs text-gray-500">
                {{ getProductById(productId) && getProductById(productId).name }}
              </div>
              <div v-if="selectedProducts.length > 5" class="text-xs text-gray-400">
                还有 {{ selectedProducts.length - 5 }} 个商品...
              </div>
            </div>
          </div>

          <!-- 分销佣金设置 -->
          <div class="space-y-4">
            <div>
              <h4 class="font-medium text-gray-900">分销佣金比例设置</h4>

              <!-- 快捷设置按钮 -->
              <div class="mt-3 flex flex-wrap gap-2">
                <button @click="setQuickCommission('low')" class="px-3 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200">
                  低佣金 (5%+2%+1%+1%)
                </button>
                <button @click="setQuickCommission('medium')" class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                  中等佣金 (8%+3%+1%+2%)
                </button>
                <button @click="setQuickCommission('high')" class="px-3 py-1 text-xs bg-orange-100 text-orange-700 rounded hover:bg-orange-200">
                  高佣金 (12%+5%+2%+3%)
                </button>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">个人佣金比例 (%)</label>
                <input
                  type="number"
                  v-model.number="batchCommissionSettings.personal_rate"
                  min="0"
                  max="100"
                  step="0.1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0.0"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">一级分销佣金 (%)</label>
                <input
                  type="number"
                  v-model.number="batchCommissionSettings.level1_rate"
                  min="0"
                  max="100"
                  step="0.1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0.0"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">二级分销佣金 (%)</label>
                <input
                  type="number"
                  v-model.number="batchCommissionSettings.level2_rate"
                  min="0"
                  max="100"
                  step="0.1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0.0"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">团长佣金 (%)</label>
                <input
                  type="number"
                  v-model.number="batchCommissionSettings.team_leader_rate"
                  min="0"
                  max="100"
                  step="0.1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0.0"
                />
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">最低等级要求</label>
              <select
                v-model="batchCommissionSettings.min_level_required"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="1">普通会员</option>
                <option value="2">银牌分销员</option>
                <option value="3">金牌分销员</option>
                <option value="4">钻石分销员</option>
              </select>
            </div>

            <!-- 批量设置预计佣金说明 -->
            <div class="bg-blue-50 p-4 rounded border">
              <h5 class="font-medium text-gray-900 mb-2">批量设置说明</h5>
              <div class="text-sm text-gray-600 space-y-1">
                <p>• 将为选中的 {{ selectedProducts.length }} 个商品统一设置相同的佣金比例</p>
                <p>• 个人佣金: {{ batchCommissionSettings.personal_rate }}%</p>
                <p>• 一级分销: {{ batchCommissionSettings.level1_rate }}%</p>
                <p>• 二级分销: {{ batchCommissionSettings.level2_rate }}%</p>
                <p>• 团长佣金: {{ batchCommissionSettings.team_leader_rate }}%</p>
                <p class="text-orange-600 font-medium">
                  • 总佣金比例: {{ (batchCommissionSettings.personal_rate +
                                  batchCommissionSettings.level1_rate +
                                  batchCommissionSettings.level2_rate +
                                  batchCommissionSettings.team_leader_rate).toFixed(1) }}%
                </p>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-end space-x-3 pt-4 border-t">
            <button @click="showBatchCommissionModal = false" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
              取消
            </button>
            <button @click="saveBatchCommissionSettings" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              批量设置
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作结果弹窗 -->
    <div v-if="showResultModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4 transform transition-all duration-300 scale-100">
        <div class="text-center">
          <!-- 成功图标 -->
          <div v-if="resultModal.type === 'success'" class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
            <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>

          <!-- 失败图标 -->
          <div v-if="resultModal.type === 'error'" class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </div>

          <!-- 标题 -->
          <h3 :class="[
            'text-lg font-medium mb-2',
            resultModal.type === 'success' ? 'text-green-900' : 'text-red-900'
          ]">
            {{ resultModal.title }}
          </h3>

          <!-- 消息内容 -->
          <p class="text-sm text-gray-600 mb-6">
            {{ resultModal.message }}
          </p>

          <!-- 商品信息（如果有） -->
          <div v-if="resultModal.product" class="bg-gray-50 p-3 rounded-lg mb-6">
            <div class="flex items-center space-x-3">
              <img :src="resultModal.product.https_pic_url || resultModal.product.list_pic_url"
                   :alt="resultModal.product.name"
                   class="w-12 h-12 rounded-lg object-cover">
              <div class="text-left">
                <p class="font-medium text-gray-900 text-sm">{{ resultModal.product.name }}</p>
                <p class="text-xs text-gray-500">商品ID: {{ resultModal.product.id }}</p>
                <p class="text-xs text-gray-500">价格: ¥{{ resultModal.product.retail_price }}</p>
              </div>
            </div>
          </div>

          <!-- 佣金信息（投入分销池成功时显示） -->
          <div v-if="resultModal.type === 'success' && resultModal.commissionInfo" class="bg-blue-50 p-3 rounded-lg mb-6">
            <h4 class="font-medium text-blue-900 mb-2 text-sm">佣金设置</h4>
            <div class="grid grid-cols-2 gap-2 text-xs">
              <div class="flex justify-between">
                <span class="text-blue-700">个人佣金:</span>
                <span class="font-medium text-blue-900">{{ resultModal.commissionInfo.personal_rate }}%</span>
              </div>
              <div class="flex justify-between">
                <span class="text-blue-700">一级佣金:</span>
                <span class="font-medium text-blue-900">{{ resultModal.commissionInfo.level1_rate }}%</span>
              </div>
              <div class="flex justify-between">
                <span class="text-blue-700">二级佣金:</span>
                <span class="font-medium text-blue-900">{{ resultModal.commissionInfo.level2_rate }}%</span>
              </div>
              <div class="flex justify-between">
                <span class="text-blue-700">团长佣金:</span>
                <span class="font-medium text-blue-900">{{ resultModal.commissionInfo.team_leader_rate }}%</span>
              </div>
            </div>
          </div>

          <!-- 批量操作信息 -->
          <div v-if="resultModal.batchInfo" class="bg-yellow-50 p-3 rounded-lg mb-6">
            <p class="text-sm text-yellow-800">
              <span class="font-medium">{{ resultModal.batchInfo.successCount }}</span> 个商品操作成功
              <span v-if="resultModal.batchInfo.failCount > 0" class="text-red-600">
                ，<span class="font-medium">{{ resultModal.batchInfo.failCount }}</span> 个商品操作失败
              </span>
            </p>
          </div>

          <!-- 按钮 -->
          <button @click="closeResultModal" :class="[
            'w-full px-4 py-2 rounded-md text-white font-medium transition-colors',
            resultModal.type === 'success' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'
          ]">
            确定
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getGoodsList,
  getCategoryList,
  getDistributionConfig,
  setDistributionStatus,
  setCommission,
  getDistributionStats,
  getCommissionRules,
  getPoolOverview,
  getRecommendConfig,
  batchAddToPool,
  batchRemoveFromPool,
  applyCommissionTemplate
} from '@/api/distribution'

export default {
  name: 'ProductPoolPage',
  data() {
    return {
      // 视图模式
      viewMode: 'list', // 默认显示列表视图

      // Tab状态
      activeTab: 'all', // all: 全部商品, distributed: 已分销, undistributed: 未分销

      // 分页相关
      currentPage: 1,
      pageSize: 20,
      totalCount: 0,

      // 搜索和筛选
      searchQuery: '',
      filterCategory: '',
      filterStatus: '',
      filterCommission: '',

      // 批量选择
      selectedProducts: [],

      // 弹窗状态
      showDetailModal: false,
      showCommissionModal: false,
      showBatchCommissionModal: false,
      showResultModal: false,
      selectedProduct: null,

      // 操作结果弹窗数据
      resultModal: {
        type: 'success', // 'success' | 'error'
        title: '',
        message: '',
        product: null,
        commissionInfo: null,
        batchInfo: null
      },

      // 佣金设置（从数据库动态加载）
      commissionSettings: {
        type: 'default', // default: 默认佣金, custom: 自定义佣金
        defaultRate: 0,
        customRate: 0,
        rules: []
      },

      // 批量佣金设置
      batchCommissionSettings: {
        personal_rate: 8.0,
        level1_rate: 3.0,
        level2_rate: 1.0,
        team_leader_rate: 2.0,
        min_level_required: 1
      },

      // 统计数据（从数据库动态加载）
      statistics: {
        totalProducts: 0,
        activeProducts: 0,
        totalCommission: '0.00',
        hotProducts: 0
      },

      // 商品分类（从数据库动态加载）
      categories: [],

      // 所有商品数据（从数据库动态加载）
      allProducts: [],

      // 分销配置数据（从数据库动态加载）
      distributionConfig: []
    }
  },

  watch: {
    // 监听tab切换，重置分页
    activeTab() {
      this.currentPage = 1;
    },

    // 监听搜索条件变化，重置分页
    searchQuery() {
      this.currentPage = 1;
    },

    filterCategory() {
      this.currentPage = 1;
    },

    filterStatus() {
      this.currentPage = 1;
    },

    filterCommission() {
      this.currentPage = 1;
    }
  },

  computed: {
    // 全选状态
    isAllSelected() {
      return this.paginatedProducts.length > 0 &&
             this.paginatedProducts.every(product => this.selectedProducts.includes(product.id));
    },

    // 已激活分销的商品（在池子里且激活）
    distributedProducts() {
      return this.allProducts.filter(product => {
        return product.is_distributed === 1;
      });
    },

    // 未入池商品（未投入分销池的商品）
    undistributedProducts() {
      return this.allProducts.filter(product => {
        return product.is_distributed !== 1;
      });
    },

    // 当前显示的商品列表
    currentProducts() {
      let products = [];
      switch (this.activeTab) {
        case 'distributed':
          products = this.distributedProducts;
          break;
        case 'undistributed':
          products = this.undistributedProducts;
          break;
        default:
          products = [...this.distributedProducts, ...this.undistributedProducts];
      }
      return products;
    },

    // 总页数
    totalPages() {
      return Math.ceil(this.filteredProducts.length / this.pageSize);
    },

    // 分页后的商品列表
    paginatedProducts() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.filteredProducts.slice(start, end);
    },

    // 过滤后的商品列表
    filteredProducts() {
      return this.currentProducts.filter(product => {
        const matchesSearch = !this.searchQuery ||
          product.name.includes(this.searchQuery) ||
          product.id.toString().includes(this.searchQuery);

        const categoryName = this.getCategoryName(product.category_id);
        const matchesCategory = !this.filterCategory || categoryName === this.filterCategory;

        const matchesStatus = !this.filterStatus || product.is_on_sale.toString() === this.filterStatus;
        const matchesCommission = !this.filterCommission || this.checkCommissionRange(product.commission_rate || 0);

        return matchesSearch && matchesCategory && matchesStatus && matchesCommission;
      });
    }
  },
  
  methods: {
    // 批量选择相关方法
    toggleSelectAll() {
      if (this.isAllSelected) {
        // 取消选择当前页所有商品
        this.paginatedProducts.forEach(product => {
          const index = this.selectedProducts.indexOf(product.id);
          if (index > -1) {
            this.selectedProducts.splice(index, 1);
          }
        });
      } else {
        // 选择当前页所有商品
        this.paginatedProducts.forEach(product => {
          if (!this.selectedProducts.includes(product.id)) {
            this.selectedProducts.push(product.id);
          }
        });
      }
    },

    selectNone() {
      this.selectedProducts = [];
    },

    selectInverse() {
      const currentPageIds = this.paginatedProducts.map(p => p.id);
      currentPageIds.forEach(id => {
        const index = this.selectedProducts.indexOf(id);
        if (index > -1) {
          this.selectedProducts.splice(index, 1);
        } else {
          this.selectedProducts.push(id);
        }
      });
    },

    clearSelection() {
      this.selectedProducts = [];
    },

    getProductById(id) {
      return this.allProducts.find(p => p.id === id);
    },

    // 显示操作结果弹窗
    showResultDialog(type, title, message, options = {}) {
      this.resultModal = {
        type: type, // 'success' | 'error'
        title: title,
        message: message,
        product: options.product || null,
        commissionInfo: options.commissionInfo || null,
        batchInfo: options.batchInfo || null
      };
      this.showResultModal = true;
    },

    // 关闭操作结果弹窗
    closeResultModal() {
      this.showResultModal = false;
      this.resultModal = {
        type: 'success',
        title: '',
        message: '',
        product: null,
        commissionInfo: null,
        batchInfo: null
      };
    },

    // 获取分类名称
    getCategoryName(categoryId) {
      const category = this.categories.find(c => c.id === categoryId);
      return category ? category.name : '未分类';
    },

    // 检查佣金范围
    checkCommissionRange(rate) {
      switch (this.filterCommission) {
        case 'low': return rate <= 5;
        case 'medium': return rate > 5 && rate <= 15;
        case 'high': return rate > 15;
        default: return true;
      }
    },

    // 获取分销池操作按钮样式
    getPoolActionButtonClass(product) {
      if (product.is_distributed === 1) {
        return 'bg-red-600 text-white hover:bg-red-700';
      } else {
        // 统一使用蓝色样式，表示"投入分销池"操作
        return 'bg-blue-600 text-white hover:bg-blue-700';
      }
    },

    // 获取分销池操作按钮文本
    getPoolActionText(product) {
      if (product.is_distributed === 1) {
        return '移出分销池';
      } else {
        // 统一显示"投入分销池"，无论是首次投入还是重新激活
        return '投入分销池';
      }
    },

    // 获取商品在分销池中的状态
    getPoolStatus(product) {
      if (product.is_distributed === 1) {
        return { status: 'active', text: '已激活', class: 'bg-green-100 text-green-800' };
      } else {
        return { status: 'not_in_pool', text: '未入池', class: 'bg-gray-100 text-gray-800' };
      }
    },

    // 获取等级徽章样式
    getLevelBadgeClass(level) {
      switch (level) {
        case 1:
          return 'bg-gray-100 text-gray-800';
        case 2:
          return 'bg-blue-100 text-blue-800';
        case 3:
          return 'bg-yellow-100 text-yellow-800';
        case 4:
          return 'bg-purple-100 text-purple-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    },

    // 获取等级名称
    getLevelName(level) {
      switch (level) {
        case 1:
          return '新手';
        case 2:
          return '优秀';
        case 3:
          return '金牌';
        case 4:
          return '钻石';
        default:
          return '新手';
      }
    },

    // 分页相关方法
    goToPage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page;
      }
    },

    // 获取可见的页码
    getVisiblePages() {
      const pages = [];
      const start = Math.max(2, this.currentPage - 2);
      const end = Math.min(this.totalPages - 1, this.currentPage + 2);

      for (let i = start; i <= end; i++) {
        if (i !== 1 && i !== this.totalPages) {
          pages.push(i);
        }
      }
      return pages;
    },

    // 查看商品详情
    viewProductDetail(product) {
      this.selectedProduct = product;
      this.showDetailModal = true;
    },

    // 设置商品佣金
    setProductCommission(product) {
      this.selectedProduct = product;

      // 获取当前商品的佣金配置
      const config = this.distributionConfig.find(c => c.goodsId === product.id);
      if (config) {
        this.commissionSettings.type = config.commissionType === 'custom' ? 'custom' : 'default';
        this.commissionSettings.customRate = config.commissionRate;
      } else {
        this.commissionSettings.type = 'default';
        this.commissionSettings.customRate = 0;
      }

      this.showCommissionModal = true;
    },

    // 保存佣金设置（投入分销池）
    async saveCommissionSettings() {
      if (!this.selectedProduct) return;

      // 验证佣金设置
      const totalRate = this.batchCommissionSettings.personal_rate +
                       this.batchCommissionSettings.level1_rate +
                       this.batchCommissionSettings.level2_rate +
                       this.batchCommissionSettings.team_leader_rate;

      if (this.batchCommissionSettings.personal_rate <= 0) {
        this.$message.error('个人佣金比例必须大于0');
        return;
      }

      if (totalRate > 50) {
        this.$message.error('总佣金比例不能超过50%，当前为' + totalRate.toFixed(1) + '%');
        return;
      }

      try {
        // 使用批量佣金设置的数据投入分销池
        const commissionData = {
          goods_id: this.selectedProduct.id,
          is_distributed: 1,
          personal_rate: this.batchCommissionSettings.personal_rate,
          level1_rate: this.batchCommissionSettings.level1_rate,
          level2_rate: this.batchCommissionSettings.level2_rate,
          team_leader_rate: this.batchCommissionSettings.team_leader_rate,
          min_level_required: this.batchCommissionSettings.min_level_required,
          commission_rate: this.batchCommissionSettings.personal_rate,
          commission_type: 'custom'
        };

        console.log('投入分销池请求数据:', commissionData);
        const response = await setDistributionStatus(commissionData);

        if (response.data && response.data.errno === 0) {
          // 更新本地数据
          const personalRate = this.batchCommissionSettings.personal_rate;
          const commissionType = 'custom';

          const existingConfigIndex = this.distributionConfig.findIndex(c => c.goodsId === this.selectedProduct.id);
          if (existingConfigIndex >= 0) {
            this.distributionConfig[existingConfigIndex] = {
              ...this.distributionConfig[existingConfigIndex],
              commissionRate: personalRate,
              commissionType: commissionType,
              isDistributed: true
            };
          } else {
            this.distributionConfig.push({
              goodsId: this.selectedProduct.id,
              commissionRate: personalRate,
              commissionType: commissionType,
              isDistributed: true
            });
          }

          // 更新商品列表中的数据
          const productIndex = this.allProducts.findIndex(p => p.id === this.selectedProduct.id);
          if (productIndex >= 0) {
            this.allProducts[productIndex].commission_rate = personalRate;
            this.allProducts[productIndex].commission_type = commissionType;
            this.allProducts[productIndex].is_distributed = 1;
            this.allProducts[productIndex].personal_rate = this.batchCommissionSettings.personal_rate;
            this.allProducts[productIndex].level1_rate = this.batchCommissionSettings.level1_rate;
            this.allProducts[productIndex].level2_rate = this.batchCommissionSettings.level2_rate;
            this.allProducts[productIndex].team_leader_rate = this.batchCommissionSettings.team_leader_rate;
            this.allProducts[productIndex].estimated_commission = this.calculateEstimatedCommission(this.selectedProduct.retail_price, personalRate);
          }

          // 更新统计数据
          this.updateStatistics();

          console.log(`商品 ${this.selectedProduct.name} 投入分销池成功，个人佣金设置为 ${personalRate}%`);

          // 显示成功弹窗
          this.showResultDialog('success', '投入分销池成功', '商品已成功投入分销池，分销员现在可以推广此商品了！', {
            product: this.selectedProduct,
            commissionInfo: {
              personal_rate: this.batchCommissionSettings.personal_rate,
              level1_rate: this.batchCommissionSettings.level1_rate,
              level2_rate: this.batchCommissionSettings.level2_rate,
              team_leader_rate: this.batchCommissionSettings.team_leader_rate
            }
          });

          this.showCommissionModal = false;
          this.showDetailModal = false;

          // 重新加载数据
          this.loadProductsFromDatabase();
        } else {
          console.error('投入分销池失败:', response.data);
          this.showResultDialog('error', '投入分销池失败', response.data.errmsg || '未知错误，请稍后重试', {
            product: this.selectedProduct
          });
        }
      } catch (error) {
        console.error('投入分销池API调用失败:', error);
        this.showResultDialog('error', '网络请求失败', '无法连接到服务器，请检查网络连接后重试', {
          product: this.selectedProduct
        });
      }
    },

    // 计算预计佣金
    calculateEstimatedCommission(price, rate) {
      return (parseFloat(price) * rate / 100).toFixed(2);
    },

    // 处理分销操作（新方法，支持用户设置佣金）
    handleDistributionAction(product) {
      const currentStatus = product.is_distributed;

      if (currentStatus === 1) {
        // 当前已激活 -> 移出分销池
        this.removeFromDistributionPool(product);
      } else {
        // 投入分销池 -> 直接显示佣金设置弹窗
        this.selectedProduct = product;
        this.resetBatchCommissionSettings();
        this.showCommissionModal = true;
      }
    },

    // 移出分销池
    async removeFromDistributionPool(product) {
      try {
        const commissionData = {
          goods_id: product.id,
          is_distributed: 0
        };

        const response = await setDistributionStatus(commissionData);

        if (response.data && response.data.errno === 0) {
          // 更新本地数据
          const productIndex = this.allProducts.findIndex(p => p.id === product.id);
          if (productIndex >= 0) {
            this.allProducts[productIndex].is_distributed = null;
            this.allProducts[productIndex].commission_rate = null;
            this.allProducts[productIndex].personal_rate = null;
            this.allProducts[productIndex].level1_rate = null;
            this.allProducts[productIndex].level2_rate = null;
            this.allProducts[productIndex].team_leader_rate = null;
            this.allProducts[productIndex].estimated_commission = null;
          }

          this.updateStatistics();

          // 显示成功弹窗
          this.showResultDialog('success', '移出分销池成功', '商品已成功移出分销池，分销员将无法继续推广此商品', {
            product: product
          });

          this.retryLoadData(product.id, null, null);
        } else {
          this.showResultDialog('error', '移出分销池失败', response.data.errmsg || '未知错误，请稍后重试', {
            product: product
          });
        }
      } catch (error) {
        console.error('移出分销池API调用失败:', error);
        this.showResultDialog('error', '网络请求失败', '无法连接到服务器，请检查网络连接后重试', {
          product: product
        });
      }
    },

    // 批量投入分销池
    batchAddToPool() {
      if (this.selectedProducts.length === 0) {
        this.$message.warning('请先选择要投入分销池的商品');
        return;
      }

      // 直接显示批量佣金设置弹窗
      this.resetBatchCommissionSettings();
      this.showBatchCommissionModal = true;
    },

    // 批量移出分销池
    async batchRemoveFromPool() {
      if (this.selectedProducts.length === 0) {
        this.$message.warning('请先选择要移出分销池的商品');
        return;
      }

      try {
        const response = await batchRemoveFromPool({
          goods_ids: this.selectedProducts
        });

        if (response.data && response.data.errno === 0) {
          // 显示批量移出成功弹窗
          this.showResultDialog('success', '批量移出分销池成功', '所选商品已成功移出分销池，分销员将无法继续推广这些商品', {
            batchInfo: {
              successCount: this.selectedProducts.length,
              failCount: 0
            }
          });

          this.clearSelection();
          this.loadProductsFromDatabase();
        } else {
          this.showResultDialog('error', '批量移出分销池失败', response.data.errmsg || '未知错误，请稍后重试', {
            batchInfo: {
              successCount: 0,
              failCount: this.selectedProducts.length
            }
          });
        }
      } catch (error) {
        console.error('批量移出API调用失败:', error);
        this.showResultDialog('error', '网络请求失败', '无法连接到服务器，请检查网络连接后重试', {
          batchInfo: {
            successCount: 0,
            failCount: this.selectedProducts.length
          }
        });
      }
    },

    // 保存批量佣金设置
    async saveBatchCommissionSettings() {
      // 验证佣金设置
      const totalRate = this.batchCommissionSettings.personal_rate +
                       this.batchCommissionSettings.level1_rate +
                       this.batchCommissionSettings.level2_rate +
                       this.batchCommissionSettings.team_leader_rate;

      if (this.batchCommissionSettings.personal_rate <= 0) {
        this.$message.error('个人佣金比例必须大于0');
        return;
      }

      if (totalRate > 50) {
        this.$message.error('总佣金比例不能超过50%，当前为' + totalRate.toFixed(1) + '%');
        return;
      }

      try {
        const commissionData = {
          goods_ids: this.selectedProducts,
          is_distributed: 1,
          personal_rate: this.batchCommissionSettings.personal_rate,
          level1_rate: this.batchCommissionSettings.level1_rate,
          level2_rate: this.batchCommissionSettings.level2_rate,
          team_leader_rate: this.batchCommissionSettings.team_leader_rate,
          min_level_required: this.batchCommissionSettings.min_level_required,
          commission_rate: this.batchCommissionSettings.personal_rate,
          commission_type: 'custom'
        };

        const response = await batchAddToPool(commissionData);

        if (response.data && response.data.errno === 0) {
          // 显示批量成功弹窗
          this.showResultDialog('success', '批量投入分销池成功', '所选商品已成功投入分销池，分销员现在可以推广这些商品了！', {
            batchInfo: {
              successCount: this.selectedProducts.length,
              failCount: 0
            },
            commissionInfo: {
              personal_rate: this.batchCommissionSettings.personal_rate,
              level1_rate: this.batchCommissionSettings.level1_rate,
              level2_rate: this.batchCommissionSettings.level2_rate,
              team_leader_rate: this.batchCommissionSettings.team_leader_rate
            }
          });

          this.showBatchCommissionModal = false;
          this.clearSelection();
          this.loadProductsFromDatabase();
        } else {
          this.showResultDialog('error', '批量投入分销池失败', response.data.errmsg || '未知错误，请稍后重试', {
            batchInfo: {
              successCount: 0,
              failCount: this.selectedProducts.length
            }
          });
        }
      } catch (error) {
        console.error('批量投入API调用失败:', error);
        this.showResultDialog('error', '网络请求失败', '无法连接到服务器，请检查网络连接后重试', {
          batchInfo: {
            successCount: 0,
            failCount: this.selectedProducts.length
          }
        });
      }
    },

    // 重置批量佣金设置
    resetBatchCommissionSettings() {
      this.batchCommissionSettings = {
        personal_rate: 8.0,
        level1_rate: 3.0,
        level2_rate: 1.0,
        team_leader_rate: 2.0,
        min_level_required: 1
      };
    },

    // 快捷设置佣金比例
    setQuickCommission(type) {
      switch (type) {
        case 'low':
          this.batchCommissionSettings.personal_rate = 5.0;
          this.batchCommissionSettings.level1_rate = 2.0;
          this.batchCommissionSettings.level2_rate = 1.0;
          this.batchCommissionSettings.team_leader_rate = 1.0;
          break;
        case 'medium':
          this.batchCommissionSettings.personal_rate = 8.0;
          this.batchCommissionSettings.level1_rate = 3.0;
          this.batchCommissionSettings.level2_rate = 1.0;
          this.batchCommissionSettings.team_leader_rate = 2.0;
          break;
        case 'high':
          this.batchCommissionSettings.personal_rate = 12.0;
          this.batchCommissionSettings.level1_rate = 5.0;
          this.batchCommissionSettings.level2_rate = 2.0;
          this.batchCommissionSettings.team_leader_rate = 3.0;
          break;
      }
    },


    
    // 更新统计数据（基于真实数据库数据）
    updateStatistics() {
      if (!this.allProducts || this.allProducts.length === 0) {
        this.statistics = {
          totalProducts: 0,
          activeProducts: 0,
          totalCommission: '0.00',
          hotProducts: 0
        };
        return;
      }

      this.statistics.totalProducts = this.allProducts.length;
      this.statistics.activeProducts = this.allProducts.filter(p => p.is_on_sale === 1).length;

      const distributedProducts = this.distributedProducts;
      const totalCommission = distributedProducts.reduce((sum, product) => {
        const commission = parseFloat(product.estimated_commission || 0);
        return sum + commission;
      }, 0);
      this.statistics.totalCommission = totalCommission.toFixed(2);

      this.statistics.hotProducts = distributedProducts.filter(p => (p.sell_volume || 0) > 100).length;
    },

    // 重置筛选条件
    resetFilters() {
      this.searchQuery = '';
      this.filterCategory = '';
      this.filterStatus = '';
      this.filterCommission = '';
    },

    // 刷新数据、导出数据、添加商品方法已移除

    // 编辑商品
    editProduct(product) {
      console.log('编辑商品:', product);
      alert('商品编辑功能开发中');
      this.showDetailModal = false;
    },

    // 复制推广链接
    copyPromotionLink(product) {
      const link = `https://shop.example.com/product/${product.id}?ref=distributor`;
      navigator.clipboard.writeText(link).then(() => {
        alert('推广链接已复制到剪贴板');
      }).catch(() => {
        alert('复制失败，请手动复制');
      });
    },

    // 从数据库加载商品数据（使用真实API调用）
    async loadProductsFromDatabase() {
      try {
        const params = {
          page: 1,
          size: 100, // 加载更多商品
          name: this.searchQuery,
          category_id: this.filterCategory,
          is_on_sale: this.filterStatus
        };

        const response = await getGoodsList(params);

        if (response.data && response.data.errno === 0) {
          this.allProducts = response.data.data.data || [];
          console.log('成功加载商品数据:', this.allProducts.length, '个商品');

          // 调试：检查前几个商品的分销状态
          if (this.allProducts.length > 0) {
            console.log('前3个商品的分销状态:');
            this.allProducts.slice(0, 3).forEach(product => {
              console.log(`商品${product.id}: is_distributed=${product.is_distributed}, commission_rate=${product.commission_rate}`);
            });
          }
        } else {
          console.error('加载商品数据失败:', response.data);
          this.$message.error('加载商品数据失败: ' + (response.data.errmsg || '未知错误'));
          this.allProducts = [];
        }
      } catch (error) {
        console.error('API调用失败:', error);
        this.$message.error('网络请求失败，请检查网络连接');
        this.allProducts = [];
      }
    },

    // 重试加载数据（用于验证数据库更新）
    retryLoadData(productId, expectedStatus, retryCount) {
      setTimeout(async () => {
        await this.loadProductsFromDatabase();

        // 验证更新是否成功
        const updatedProduct = this.allProducts.find(p => p.id === productId);
        if (updatedProduct && updatedProduct.is_distributed !== expectedStatus && retryCount < 3) {
          console.warn(`数据库更新可能未生效，第${retryCount + 1}次重试...`);
          // 再次尝试加载
          this.retryLoadData(productId, expectedStatus, retryCount + 1);
        }
      }, 500 + retryCount * 500); // 递增延迟
    },

    // 加载分类数据
    async loadCategories() {
      try {
        const response = await getCategoryList();

        if (response.data && response.data.errno === 0) {
          this.categories = response.data.data || [];
          console.log('成功加载分类数据:', this.categories.length, '个分类');
        } else {
          console.error('加载分类数据失败:', response.data);
        }
      } catch (error) {
        console.error('加载分类数据失败:', error);
        this.$message.error('加载分类数据失败');
        this.categories = [];
      }
    },

    // 加载统计数据
    async loadStatistics() {
      try {
        const response = await getDistributionStats();

        if (response.data && response.data.errno === 0) {
          this.statistics = response.data.data;
          console.log('成功加载统计数据:', this.statistics);
        } else {
          console.error('加载统计数据失败:', response.data);
        }
      } catch (error) {
        console.error('加载统计数据失败:', error);
        this.$message.error('加载统计数据失败');
        // 重置统计数据
        this.statistics = {
          totalProducts: 0,
          activeProducts: 0,
          totalCommission: '0.00',
          hotProducts: 0
        };
      }
    },

    // 加载佣金规则
    async loadCommissionRules() {
      try {
        const response = await getCommissionRules();

        if (response.data && response.data.errno === 0) {
          const rulesData = response.data.data;
          this.commissionSettings.defaultRate = rulesData.defaultRate || 0;
          this.commissionSettings.rules = rulesData.rules || [];
          console.log('成功加载佣金规则:', this.commissionSettings.rules);
        } else {
          console.error('加载佣金规则失败:', response.data);
          this.$message.error('加载佣金规则失败');
        }
      } catch (error) {
        console.error('加载佣金规则失败:', error);
        this.$message.error('加载佣金规则失败');
        this.commissionSettings.rules = [];
        this.commissionSettings.defaultRate = 0;
      }
    }
  },
  
  mounted() {
    console.log('商品分销池页面已加载');

    // 从数据库加载商品数据
    this.loadProductsFromDatabase();

    // 加载分类数据
    this.loadCategories();

    // 加载统计数据
    this.loadStatistics();

    // 加载佣金规则
    this.loadCommissionRules();
  }
}
</script>

<style scoped>
.product-pool-page {
  min-height: 100vh;
  background-color: #f9fafb;
}

.page-header {
  position: sticky;
  top: 0;
  z-index: 10;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 表格样式 */
.overflow-x-auto {
  overflow-x: auto;
}

table {
  min-width: 100%;
}

th, td {
  white-space: nowrap;
}

/* 图片样式 */
img {
  object-fit: cover;
}

/* 按钮悬停效果 */
.hover\:bg-blue-700:hover {
  background-color: #1d4ed8;
}

.hover\:bg-green-700:hover {
  background-color: #15803d;
}

.hover\:bg-purple-700:hover {
  background-color: #7c3aed;
}

.hover\:bg-red-700:hover {
  background-color: #b91c1c;
}

.hover\:bg-gray-700:hover {
  background-color: #374151;
}

.hover\:bg-gray-200:hover {
  background-color: #e5e7eb;
}

.hover\:bg-gray-100:hover {
  background-color: #f3f4f6;
}

/* 文本颜色悬停 */
.hover\:text-blue-900:hover {
  color: #1e3a8a;
}

.hover\:text-green-900:hover {
  color: #14532d;
}

.hover\:text-red-900:hover {
  color: #7f1d1d;
}

.hover\:text-gray-600:hover {
  color: #4b5563;
}

/* 行悬停效果 */
.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}

/* 卡片悬停效果 */
.hover\:shadow-lg:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 过渡动画 */
.transition-colors {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

.transition-shadow {
  transition: box-shadow 0.2s ease;
}

/* 输入框和选择框聚焦效果 */
input:focus, select:focus {
  outline: none;
  ring: 2px;
  ring-color: #3b82f6;
  border-color: transparent;
}

/* 弹窗样式 */
.fixed {
  position: fixed;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.z-50 {
  z-index: 50;
}

.max-h-\[90vh\] {
  max-height: 90vh;
}

.overflow-y-auto {
  overflow-y: auto;
}

/* 网格布局 */
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

/* 响应式设计 */
@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .md\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1280px) {
  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* 间距 */
.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

/* 分割线 */
.divide-y > * + * {
  border-top-width: 1px;
}

.divide-gray-200 > * + * {
  border-color: #e5e7eb;
}

/* 状态标签颜色 */
.bg-blue-100 {
  background-color: #dbeafe;
}

.text-blue-800 {
  color: #1e40af;
}

.bg-green-100 {
  background-color: #dcfce7;
}

.text-green-800 {
  color: #166534;
}

.bg-red-100 {
  background-color: #fee2e2;
}

.text-red-800 {
  color: #991b1b;
}

.bg-yellow-100 {
  background-color: #fef3c7;
}

.text-yellow-800 {
  color: #92400e;
}

/* 文本颜色 */
.text-red-600 {
  color: #dc2626;
}

.text-green-600 {
  color: #16a34a;
}

.text-yellow-600 {
  color: #ca8a04;
}

.text-blue-600 {
  color: #2563eb;
}

.text-purple-600 {
  color: #9333ea;
}

/* 文本截断 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.max-w-xs {
  max-width: 20rem;
}

/* 边框样式 */
.border-t {
  border-top-width: 1px;
}

.border-gray-300 {
  border-color: #d1d5db;
}

/* 背景色 */
.bg-gray-50 {
  background-color: #f9fafb;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 图标样式 */
.ri-shopping-bag-line,
.ri-eye-line,
.ri-money-dollar-circle-line,
.ri-fire-line,
.ri-refresh-line,
.ri-download-line,
.ri-add-line,
.ri-grid-line,
.ri-list-check,
.ri-close-line {
  font-size: inherit;
}

/* 表格头部样式 */
.uppercase {
  text-transform: uppercase;
}

.tracking-wider {
  letter-spacing: 0.05em;
}

/* 文本截断 */
.whitespace-nowrap {
  white-space: nowrap;
}
</style>
