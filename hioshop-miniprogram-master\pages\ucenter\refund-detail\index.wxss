/* 售后详情页面样式 */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 状态信息 */
.status-section {
  background: #fff;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.status-icon .icon {
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.status-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

/* 信息区域 */
.info-section {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 160rpx;
  font-size: 30rpx;
  color: #666;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  word-break: break-all;
}

.info-value.price {
  color: #e64340;
  font-weight: bold;
}

/* 图片列表 */
.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.evidence-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  border: 1rpx solid #eee;
}

/* 订单信息 */
.order-info {
  
}

.order-sn {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.goods-list {
  
}

.goods-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.goods-item:last-child {
  border-bottom: none;
}

.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.goods-info {
  flex: 1;
}

.goods-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.goods-spec {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.goods-price {
  font-size: 28rpx;
  color: #e64340;
  font-weight: bold;
}

/* 备注内容 */
.memo-content {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  background: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #007aff;
}

/* 操作按钮 */
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  gap: 20rpx;
}

.contact-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background: #f0f0f0;
  color: #333;
  border: none;
  border-radius: 8rpx;
  font-size: 30rpx;
}

.contact-btn:after {
  border: none;
}

.cancel-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background: #ff3b30;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 30rpx;
}

.cancel-btn:after {
  border: none;
}

/* 退货地址样式 */
.return-address {
    background: #f8f9fa;
    border-radius: 16rpx;
    padding: 32rpx;
}

.address-item {
    display: flex;
    margin-bottom: 16rpx;
}

.address-item:last-child {
    margin-bottom: 0;
}

.address-label {
    width: 160rpx;
    color: #666;
    font-size: 28rpx;
}

.address-value {
    flex: 1;
    color: #333;
    font-size: 28rpx;
    line-height: 1.4;
}

/* 物流信息表单 */
.logistics-form {
    background: #f8f9fa;
    border-radius: 16rpx;
    padding: 32rpx;
}

.form-item {
    margin-bottom: 32rpx;
}

.form-item:last-of-type {
    margin-bottom: 40rpx;
}

.form-label {
    color: #333;
    font-size: 28rpx;
    margin-bottom: 16rpx;
    font-weight: 500;
}

.form-input {
    background: white;
    border: 2rpx solid #e1e5e9;
    border-radius: 12rpx;
    padding: 24rpx 32rpx;
    font-size: 28rpx;
    color: #333;
}

.form-input:focus {
    border-color: #667eea;
}

.submit-logistics-btn {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 500;
    text-align: center;
}
