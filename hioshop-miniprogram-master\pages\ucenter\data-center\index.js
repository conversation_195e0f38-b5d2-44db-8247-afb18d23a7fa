var util = require('../../../utils/util.js');
var api = require('../../../config/api.js');
var auth = require('../../../utils/auth.js');

Page({
  data: {
    timeFilter: 'month', // today, week, month, all
    timePeriodText: '本月',
    promotionData: {
      totalOrders: 0,
      totalAmount: '0.00',
      totalCommission: '0.00',
      teamCount: 0
    },
    currentData: {
      orders: 0,
      amount: '0.00',
      commission: '0.00',
      visitors: 0
    },
    teamData: {
      directCount: 0,
      totalCount: 0,
      teamOrders: 0,
      teamCommission: '0.00'
    },
    rankingList: [],
    recentOrders: []
  },

  onLoad: function (options) {
    // 检查授权
    if (!auth.isFullyAuthorized()) {
      wx.showModal({
        title: '需要登录',
        content: '查看数据需要先完善个人信息',
        confirmText: '立即设置',
        showCancel: false,
        success: (res) => {
          wx.navigateTo({
            url: '/pages/app-auth/index?from=data-center'
          });
        }
      });
      return;
    }

    this.loadDataCenterInfo();
  },

  onShow: function () {
    // 每次显示页面时刷新数据
    if (auth.isFullyAuthorized()) {
      this.loadDataCenterInfo();
    }
  },

  // 加载数据中心信息
  loadDataCenterInfo: function() {
    this.getDataCenterInfo();
  },

  // 切换时间筛选
  changeTimeFilter: function(e) {
    const filter = e.currentTarget.dataset.filter;
    let periodText = '';

    switch(filter) {
      case 'today': periodText = '今日'; break;
      case 'week': periodText = '本周'; break;
      case 'month': periodText = '本月'; break;
      case 'all': periodText = '全部'; break;
    }

    this.setData({
      timeFilter: filter,
      timePeriodText: periodText
    });

    // 重新加载数据
    this.getDataCenterInfo();
  },

  // 获取数据中心信息
  getDataCenterInfo: function() {
    const that = this;

    util.request(api.PromotionDataCenter, {
      timeFilter: this.data.timeFilter
    }).then(function (res) {
      if (res.errno === 0) {
        console.log('数据中心信息:', res.data);
        that.setData({
          promotionData: res.data.promotionData,
          currentData: res.data.currentData,
          teamData: res.data.teamData,
          rankingList: res.data.rankingList,
          recentOrders: res.data.recentOrders,
          timePeriodText: res.data.timePeriodText
        });
      } else {
        wx.showToast({
          title: res.errmsg || '获取数据失败',
          icon: 'none'
        });
      }
    }).catch(function(err) {
      console.error('获取数据中心信息失败:', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },





  // 跳转到排行榜页面
  toRankingList: function() {
    wx.showToast({
      title: '排行榜功能开发中',
      icon: 'none'
    });
  },

  // 跳转到推广订单页面
  toPromotionOrders: function() {
    wx.showToast({
      title: '推广订单功能开发中',
      icon: 'none'
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadDataCenterInfo();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  }
});
