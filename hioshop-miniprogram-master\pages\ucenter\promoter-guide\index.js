var util = require('../../../utils/util.js');
var api = require('../../../config/api.js');
var auth = require('../../../utils/auth.js');

Page({
  data: {
    isPromoter: false,
    isLoading: false,
    animatedAmounts: [0, 0, 0], // 动态显示的数字
    platformData: {
      promoters: '1,234',
      todayOrders: '89',
      totalCommission: '¥12.8万'
    },
    faqList: [
      {
        id: 1,
        question: '推广员有什么要求吗？',
        answer: '无任何要求！只要您愿意分享商品，任何人都可以成为推广员。无需投资，无需囤货，零门槛加入。',
        expanded: false
      },
      {
        id: 2,
        question: '佣金什么时候到账？',
        answer: '买家确认收货后，佣金会立即到账。有10天保护期，10天后即可申请提现，1-3个工作日到账。',
        expanded: false
      },
      {
        id: 3,
        question: '如何查看我的推广数据？',
        answer: '在"我的"页面点击"数据"可以查看详细的推广数据，包括推广订单、佣金收入、团队数据等。',
        expanded: false
      },
      {
        id: 4,
        question: '如何提现佣金？',
        answer: '在"我的"页面点击"佣金"进入佣金页面，点击"立即提现"即可申请提现到微信或支付宝。',
        expanded: false
      },
      {
        id: 5,
        question: '推广链接如何获得？',
        answer: '成为推广员后，在任意商品页面都会有"分享赚佣金"按钮，点击即可获得专属推广链接。',
        expanded: false
      }
    ]
  },

  onLoad: function (options) {
    // 检查用户是否已经是推广员
    this.checkPromoterStatus();
    // 加载平台数据
    this.loadPlatformData();
    // 启动数字动画
    this.startNumberAnimation();
  },

  onShow: function () {
    // 每次显示页面时检查推广员状态
    this.checkPromoterStatus();
  },

  // 检查用户是否已经是推广员
  checkPromoterStatus: function() {
    let that = this;
    
    // 检查是否已登录
    if (!auth.isFullyAuthorized()) {
      console.log('用户未登录');
      return;
    }

    // 这里应该调用API检查用户是否已经是推广员
    // util.request(api.CheckPromoterStatus).then(function (res) {
    //   if (res.errno === 0) {
    //     that.setData({
    //       isPromoter: res.data.isPromoter
    //     });
    //   }
    // });

    // 暂时模拟检查逻辑
    setTimeout(() => {
      that.setData({
        isPromoter: false // 假设用户还不是推广员
      });
    }, 500);
  },

  // 加载平台数据
  loadPlatformData: function() {
    let that = this;
    
    // 这里应该调用API获取平台实时数据
    // util.request(api.PlatformStats).then(function (res) {
    //   if (res.errno === 0) {
    //     that.setData({
    //       platformData: res.data
    //     });
    //   }
    // });

    // 暂时使用模拟数据
    setTimeout(() => {
      that.setData({
        platformData: {
          promoters: '1,234',
          todayOrders: '89',
          totalCommission: '¥12.8万'
        }
      });
    }, 1000);
  },

  // 启动数字动画
  startNumberAnimation: function() {
    const targets = [1280, 156, 368]; // 目标数字
    const duration = 2000; // 动画持续时间2秒
    const frameRate = 60; // 帧率
    const totalFrames = duration / (1000 / frameRate);

    let currentFrame = 0;
    const timer = setInterval(() => {
      currentFrame++;
      const progress = currentFrame / totalFrames;

      // 使用缓动函数让动画更自然
      const easeProgress = this.easeOutQuart(progress);

      const animatedAmounts = targets.map(target => {
        return Math.floor(target * easeProgress);
      });

      this.setData({
        animatedAmounts: animatedAmounts
      });

      if (currentFrame >= totalFrames) {
        clearInterval(timer);
        // 确保最终显示准确数字
        this.setData({
          animatedAmounts: targets
        });
      }
    }, 1000 / frameRate);
  },

  // 缓动函数 - 四次方缓出
  easeOutQuart: function(t) {
    return 1 - Math.pow(1 - t, 4);
  },

  // 切换FAQ展开状态
  toggleFaq: function(e) {
    const index = e.currentTarget.dataset.index;
    const faqList = this.data.faqList;

    faqList[index].expanded = !faqList[index].expanded;

    this.setData({
      faqList: faqList
    });
  },

  // 开始推广
  startPromotion: function() {
    let that = this;

    // 检查是否已登录
    if (!auth.isFullyAuthorized()) {
      wx.showModal({
        title: '需要登录',
        content: '成为推广员需要先完善个人信息，是否立即设置？',
        confirmText: '立即设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/app-auth/index?from=promoter-guide'
            });
          }
        }
      });
      return;
    }

    // 如果已经是推广员，跳转到数据中心
    if (this.data.isPromoter) {
      wx.navigateTo({
        url: '/pages/ucenter/data-center/index'
      });
      return;
    }

    // 成为推广员
    this.setData({ isLoading: true });

    // 这里应该调用API创建推广员
    // util.request(api.BecomePromoter, {}, 'POST').then(function (res) {
    //   that.setData({ isLoading: false });
    //   if (res.errno === 0) {
    //     wx.showToast({
    //       title: '恭喜成为推广员！',
    //       icon: 'success'
    //     });
    //     that.setData({ isPromoter: true });
    //     // 跳转到推广中心或商品页面
    //     setTimeout(() => {
    //       wx.navigateTo({
    //         url: '/pages/ucenter/data-center/index'
    //       });
    //     }, 1500);
    //   } else {
    //     wx.showToast({
    //       title: res.errmsg || '操作失败',
    //       icon: 'none'
    //     });
    //   }
    // }).catch(function(err) {
    //   that.setData({ isLoading: false });
    //   wx.showToast({
    //     title: '网络错误，请重试',
    //     icon: 'none'
    //   });
    // });

    // 暂时模拟成为推广员的过程
    setTimeout(() => {
      that.setData({ 
        isLoading: false,
        isPromoter: true
      });
      
      wx.showToast({
        title: '恭喜成为推广员！',
        icon: 'success'
      });

      // 延迟跳转到数据中心
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/ucenter/data-center/index'
        });
      }, 1500);
    }, 2000);
  },

  // 分享页面
  onShareAppMessage: function () {
    return {
      title: '成为推广员，轻松赚佣金！',
      path: '/pages/ucenter/promoter-guide/index',
      imageUrl: '/images/share/promoter-guide.jpg' // 需要准备分享图片
    };
  },

  // 分享到朋友圈
  onShareTimeline: function () {
    return {
      title: '成为推广员，轻松赚佣金！零门槛加入，立即开始赚钱',
      imageUrl: '/images/share/promoter-guide.jpg'
    };
  }
});
