const mysql = require('mysql');
const crypto = require('crypto');
const readline = require('readline');

// 数据库配置
const dbConfig = {
    host: '127.0.0.1',
    port: 3306,
    user: 'root',
    password: '19841020',
    database: 'hiolabsDB',
    charset: 'utf8mb4'
};

// 创建readline接口用于用户输入
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// 创建数据库连接
const connection = mysql.createConnection(dbConfig);

console.log('正在连接数据库...');

connection.connect((err) => {
    if (err) {
        console.error('数据库连接失败:', err);
        rl.close();
        return;
    }

    console.log('数据库连接成功！');

    // 提示用户输入用户名和新密码
    rl.question('请输入要重置密码的用户名: ', (username) => {
        if (!username) {
            console.log('用户名不能为空');
            connection.end();
            rl.close();
            return;
        }

        rl.question('请输入新密码（至少6位字符）: ', (newPassword) => {
            if (!newPassword || newPassword.length < 6) {
                console.log('密码长度不能少于6位字符');
                connection.end();
                rl.close();
                return;
            }

            // 生成新的密码盐
            const passwordSalt = Math.random().toString(36).substring(2, 15);
            const encryptedPassword = crypto.createHash('md5').update(newPassword + passwordSalt).digest('hex');

            console.log('正在更新密码...');

            // 更新数据库中的密码和密码盐
            const updateSql = 'UPDATE hiolabs_admin SET password = ?, password_salt = ? WHERE username = ?';

            connection.query(updateSql, [encryptedPassword, passwordSalt, username], (err, result) => {
                if (err) {
                    console.error('更新密码失败:', err);
                    connection.end();
                    rl.close();
                    return;
                }

                if (result.affectedRows === 0) {
                    console.log('用户不存在，密码更新失败');
                } else {
                    console.log('密码更新成功！');
                    console.log('影响行数:', result.affectedRows);
                    console.log('');
                    console.log('现在可以使用以下账号登录：');
                    console.log('用户名:', username);
                    console.log('密码:', newPassword);
                    console.log('请妥善保管您的登录信息');
                }

                connection.end();
                rl.close();
            });
        });
    });
});
