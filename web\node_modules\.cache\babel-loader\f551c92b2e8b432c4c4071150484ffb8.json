{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js!D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleRoundsPage.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleRoundsPage.vue", "mtime": 1753735541601}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\babel.config.js", "mtime": 1717470108000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovcHktaWRlL2hpb3Nob3AtbWluaXByb2dyYW0tbWFzdGVyL3dlYi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vcmVnZW5lcmF0b3JSdW50aW1lLmpzIjsKaW1wb3J0IF9hc3luY1RvR2VuZXJhdG9yIGZyb20gIkQ6L3B5LWlkZS9oaW9zaG9wLW1pbmlwcm9ncmFtLW1hc3Rlci93ZWIvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2FzeW5jVG9HZW5lcmF0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLml0ZXJhdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5pdGVyYXRvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZpbmQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmNvbmNhdC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5wYWQtc3RhcnQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAudG8tc3RyaW5nLmpzIjsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRmxhc2hTYWxlUm91bmRzUGFnZScsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOe7n+iuoeaVsOaNrgogICAgICBzdGF0aXN0aWNzOiB7CiAgICAgICAgdG90YWxSb3VuZHM6IDAsCiAgICAgICAgYWN0aXZlUm91bmRzOiAwLAogICAgICAgIHVwY29taW5nUm91bmRzOiAwLAogICAgICAgIGVuZGVkUm91bmRzOiAwLAogICAgICAgIHRvdGFsT3JkZXJzOiAwLAogICAgICAgIHRvZGF5T3JkZXJzOiAwLAogICAgICAgIHRvdGFsU2FsZXM6IDAKICAgICAgfSwKICAgICAgLy8g5b2T5YmN6L2u5qyhCiAgICAgIGN1cnJlbnRSb3VuZHM6IHsKICAgICAgICBjdXJyZW50OiBbXSwKICAgICAgICB1cGNvbWluZzogW10sCiAgICAgICAgdG90YWw6IDAKICAgICAgfSwKICAgICAgLy8g6L2u5qyh5YiX6KGoCiAgICAgIHJvdW5kc0xpc3Q6IHsKICAgICAgICBkYXRhOiBbXSwKICAgICAgICB0b3RhbDogMCwKICAgICAgICBjdXJyZW50X3BhZ2U6IDEsCiAgICAgICAgbGFzdF9wYWdlOiAxCiAgICAgIH0sCiAgICAgIC8vIOetm+mAieeKtuaAgQogICAgICBmaWx0ZXJTdGF0dXM6ICcnLAogICAgICBjdXJyZW50UGFnZTogMSwKICAgICAgLy8g5qih5oCB5qGG54q25oCBCiAgICAgIHNob3dBZGRNb2RhbDogZmFsc2UsCiAgICAgIHNob3dDb25maWdNb2RhbDogZmFsc2UsCiAgICAgIC8vIOaWsOi9ruasoeihqOWNlQogICAgICBuZXdSb3VuZDogewogICAgICAgIGdvb2RzX2lkOiAnJywKICAgICAgICBnb29kc19uYW1lOiAnJywKICAgICAgICBnb29kc19pbWFnZTogJycsCiAgICAgICAgb3JpZ2luYWxfcHJpY2U6ICcnLAogICAgICAgIGZsYXNoX3ByaWNlOiAnJywKICAgICAgICBzdG9jazogJycsCiAgICAgICAgbGltaXRfcXVhbnRpdHk6IDEKICAgICAgfSwKICAgICAgLy8g5ZWG5ZOB5YiX6KGoCiAgICAgIGdvb2RzTGlzdDogW10sCiAgICAgIC8vIOezu+e7n+mFjee9rgogICAgICBzeXN0ZW1Db25maWc6IHsKICAgICAgICByb3VuZF9kdXJhdGlvbjogMzAwLAogICAgICAgIGJyZWFrX2R1cmF0aW9uOiAxMjAsCiAgICAgICAgYXV0b19zdGFydF9uZXh0OiB0cnVlLAogICAgICAgIGRhaWx5X3N0YXJ0X3RpbWU6ICcwOTowMDowMCcsCiAgICAgICAgZGFpbHlfZW5kX3RpbWU6ICcyMjowMDowMCcsCiAgICAgICAgaXNfZW5hYmxlZDogdHJ1ZQogICAgICB9LAogICAgICAvLyDlrprml7blmagKICAgICAgcmVmcmVzaFRpbWVyOiBudWxsLAogICAgICBjb3VudGRvd25UaW1lcjogbnVsbAogICAgfTsKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICB0aGlzLmxvYWREYXRhKCk7CiAgICB0aGlzLnN0YXJ0QXV0b1JlZnJlc2goKTsKICB9LAogIGJlZm9yZURlc3Ryb3k6IGZ1bmN0aW9uIGJlZm9yZURlc3Ryb3koKSB7CiAgICB0aGlzLnN0b3BBdXRvUmVmcmVzaCgpOwogIH0sCiAgbWV0aG9kczogewogICAgLy8g5Yqg6L295omA5pyJ5pWw5o2uCiAgICBsb2FkRGF0YTogZnVuY3Rpb24gbG9hZERhdGEoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDI7CiAgICAgICAgICAgICAgcmV0dXJuIFByb21pc2UuYWxsKFtfdGhpcy5sb2FkU3RhdGlzdGljcygpLCBfdGhpcy5sb2FkQ3VycmVudFJvdW5kcygpLCBfdGhpcy5sb2FkUm91bmRzTGlzdCgpLCBfdGhpcy5sb2FkR29vZHNMaXN0KCksIF90aGlzLmxvYWRTeXN0ZW1Db25maWcoKV0pOwogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy8g5Yqg6L2957uf6K6h5pWw5o2uCiAgICBsb2FkU3RhdGlzdGljczogZnVuY3Rpb24gbG9hZFN0YXRpc3RpY3MoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMigpIHsKICAgICAgICB2YXIgcmVzcG9uc2U7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUyJChfY29udGV4dDIpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0Mi5wcmV2ID0gX2NvbnRleHQyLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0Mi5wcmV2ID0gMDsKICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDM7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMi5heGlvcy5nZXQoJ2ZsYXNoc2FsZXJvdW5kcy9zdGF0aXN0aWNzJyk7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICByZXNwb25zZSA9IF9jb250ZXh0Mi5zZW50OwogICAgICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmVycm5vID09PSAwKSB7CiAgICAgICAgICAgICAgICBfdGhpczIuc3RhdGlzdGljcyA9IHJlc3BvbnNlLmRhdGEuZGF0YTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX2NvbnRleHQyLm5leHQgPSAxMDsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgIF9jb250ZXh0Mi5wcmV2ID0gNzsKICAgICAgICAgICAgICBfY29udGV4dDIudDAgPSBfY29udGV4dDJbImNhdGNoIl0oMCk7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L2957uf6K6h5pWw5o2u5aSx6LSlOicsIF9jb250ZXh0Mi50MCk7CiAgICAgICAgICAgIGNhc2UgMTA6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTIsIG51bGwsIFtbMCwgN11dKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy8g5Yqg6L295b2T5YmN6L2u5qyhCiAgICBsb2FkQ3VycmVudFJvdW5kczogZnVuY3Rpb24gbG9hZEN1cnJlbnRSb3VuZHMoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlMygpIHsKICAgICAgICB2YXIgcmVzcG9uc2U7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUzJChfY29udGV4dDMpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0My5wcmV2ID0gX2NvbnRleHQzLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0My5wcmV2ID0gMDsKICAgICAgICAgICAgICBfY29udGV4dDMubmV4dCA9IDM7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMy5heGlvcy5nZXQoJ2ZsYXNoc2FsZXJvdW5kcy9jdXJyZW50Jyk7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICByZXNwb25zZSA9IF9jb250ZXh0My5zZW50OwogICAgICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmVycm5vID09PSAwKSB7CiAgICAgICAgICAgICAgICBfdGhpczMuY3VycmVudFJvdW5kcyA9IHJlc3BvbnNlLmRhdGEuZGF0YTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSAxMDsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgIF9jb250ZXh0My5wcmV2ID0gNzsKICAgICAgICAgICAgICBfY29udGV4dDMudDAgPSBfY29udGV4dDNbImNhdGNoIl0oMCk7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295b2T5YmN6L2u5qyh5aSx6LSlOicsIF9jb250ZXh0My50MCk7CiAgICAgICAgICAgIGNhc2UgMTA6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0My5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTMsIG51bGwsIFtbMCwgN11dKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy8g5Yqg6L296L2u5qyh5YiX6KGoCiAgICBsb2FkUm91bmRzTGlzdDogZnVuY3Rpb24gbG9hZFJvdW5kc0xpc3QoKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlNCgpIHsKICAgICAgICB2YXIgcGFyYW1zLCByZXNwb25zZTsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTQkKF9jb250ZXh0NCkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ0LnByZXYgPSBfY29udGV4dDQubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQ0LnByZXYgPSAwOwogICAgICAgICAgICAgIHBhcmFtcyA9IHsKICAgICAgICAgICAgICAgIHBhZ2U6IF90aGlzNC5jdXJyZW50UGFnZSwKICAgICAgICAgICAgICAgIGxpbWl0OiAyMAogICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgaWYgKF90aGlzNC5maWx0ZXJTdGF0dXMpIHsKICAgICAgICAgICAgICAgIHBhcmFtcy5zdGF0dXMgPSBfdGhpczQuZmlsdGVyU3RhdHVzOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dDQubmV4dCA9IDU7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzNC5heGlvcy5nZXQoJ2ZsYXNoc2FsZXJvdW5kcy9saXN0JywgewogICAgICAgICAgICAgICAgcGFyYW1zOiBwYXJhbXMKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICAgIHJlc3BvbnNlID0gX2NvbnRleHQ0LnNlbnQ7CiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuZXJybm8gPT09IDApIHsKICAgICAgICAgICAgICAgIF90aGlzNC5yb3VuZHNMaXN0ID0gcmVzcG9uc2UuZGF0YS5kYXRhOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dDQubmV4dCA9IDEyOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDk6CiAgICAgICAgICAgICAgX2NvbnRleHQ0LnByZXYgPSA5OwogICAgICAgICAgICAgIF9jb250ZXh0NC50MCA9IF9jb250ZXh0NFsiY2F0Y2giXSgwKTsKICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3ova7mrKHliJfooajlpLHotKU6JywgX2NvbnRleHQ0LnQwKTsKICAgICAgICAgICAgY2FzZSAxMjoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ0LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNCwgbnVsbCwgW1swLCA5XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDliqDovb3llYblk4HliJfooagKICAgIGxvYWRHb29kc0xpc3Q6IGZ1bmN0aW9uIGxvYWRHb29kc0xpc3QoKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlNSgpIHsKICAgICAgICB2YXIgcmVzcG9uc2U7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU1JChfY29udGV4dDUpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0NS5wcmV2ID0gX2NvbnRleHQ1Lm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF9jb250ZXh0NS5wcmV2ID0gMDsKICAgICAgICAgICAgICBfY29udGV4dDUubmV4dCA9IDM7CiAgICAgICAgICAgICAgcmV0dXJuIF90aGlzNS5heGlvcy5nZXQoJ2ZsYXNoc2FsZXJvdW5kcy9nb29kcycpOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgcmVzcG9uc2UgPSBfY29udGV4dDUuc2VudDsKICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5lcnJubyA9PT0gMCkgewogICAgICAgICAgICAgICAgX3RoaXM1Lmdvb2RzTGlzdCA9IHJlc3BvbnNlLmRhdGEuZGF0YTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX2NvbnRleHQ1Lm5leHQgPSAxMDsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgIF9jb250ZXh0NS5wcmV2ID0gNzsKICAgICAgICAgICAgICBfY29udGV4dDUudDAgPSBfY29udGV4dDVbImNhdGNoIl0oMCk7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295ZWG5ZOB5YiX6KGo5aSx6LSlOicsIF9jb250ZXh0NS50MCk7CiAgICAgICAgICAgIGNhc2UgMTA6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0NS5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTUsIG51bGwsIFtbMCwgN11dKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy8g5Yqg6L2957O757uf6YWN572uCiAgICBsb2FkU3lzdGVtQ29uZmlnOiBmdW5jdGlvbiBsb2FkU3lzdGVtQ29uZmlnKCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgcmV0dXJuIF9hc3luY1RvR2VuZXJhdG9yKCAvKiNfX1BVUkVfXyovX3JlZ2VuZXJhdG9yUnVudGltZSgpLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTYoKSB7CiAgICAgICAgdmFyIHJlc3BvbnNlOwogICAgICAgIHJldHVybiBfcmVnZW5lcmF0b3JSdW50aW1lKCkud3JhcChmdW5jdGlvbiBfY2FsbGVlNiQoX2NvbnRleHQ2KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dDYucHJldiA9IF9jb250ZXh0Ni5uZXh0KSB7CiAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICBfY29udGV4dDYucHJldiA9IDA7CiAgICAgICAgICAgICAgX2NvbnRleHQ2Lm5leHQgPSAzOwogICAgICAgICAgICAgIHJldHVybiBfdGhpczYuYXhpb3MuZ2V0KCdmbGFzaHNhbGVyb3VuZHMvY29uZmlnJyk7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICByZXNwb25zZSA9IF9jb250ZXh0Ni5zZW50OwogICAgICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmVycm5vID09PSAwKSB7CiAgICAgICAgICAgICAgICBfdGhpczYuc3lzdGVtQ29uZmlnID0gcmVzcG9uc2UuZGF0YS5kYXRhOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dDYubmV4dCA9IDEwOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDc6CiAgICAgICAgICAgICAgX2NvbnRleHQ2LnByZXYgPSA3OwogICAgICAgICAgICAgIF9jb250ZXh0Ni50MCA9IF9jb250ZXh0NlsiY2F0Y2giXSgwKTsKICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3ns7vnu5/phY3nva7lpLHotKU6JywgX2NvbnRleHQ2LnQwKTsKICAgICAgICAgICAgY2FzZSAxMDoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ2LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNiwgbnVsbCwgW1swLCA3XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDliJvlu7rmlrDova7mrKEKICAgIGNyZWF0ZVJvdW5kOiBmdW5jdGlvbiBjcmVhdGVSb3VuZCgpIHsKICAgICAgdmFyIF90aGlzNyA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU3KCkgewogICAgICAgIHZhciByZXNwb25zZTsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTckKF9jb250ZXh0NykgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ3LnByZXYgPSBfY29udGV4dDcubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQ3LnByZXYgPSAwOwogICAgICAgICAgICAgIF9jb250ZXh0Ny5uZXh0ID0gMzsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXM3LmF4aW9zLnBvc3QoJ2ZsYXNoc2FsZXJvdW5kcy9jcmVhdGUnLCBfdGhpczcubmV3Um91bmQpOwogICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgcmVzcG9uc2UgPSBfY29udGV4dDcuc2VudDsKICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5lcnJubyA9PT0gMCkgewogICAgICAgICAgICAgICAgX3RoaXM3LnNob3dBZGRNb2RhbCA9IGZhbHNlOwogICAgICAgICAgICAgICAgX3RoaXM3LiRtZXNzYWdlLnN1Y2Nlc3MoJ+i9ruasoeWIm+W7uuaIkOWKnycpOwogICAgICAgICAgICAgICAgX3RoaXM3LmxvYWREYXRhKCk7CiAgICAgICAgICAgICAgICBfdGhpczcucmVzZXROZXdSb3VuZCgpOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBfdGhpczcuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UuZGF0YS5lcnJtc2cgfHwgJ+WIm+W7uuWksei0pScpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfY29udGV4dDcubmV4dCA9IDExOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDc6CiAgICAgICAgICAgICAgX2NvbnRleHQ3LnByZXYgPSA3OwogICAgICAgICAgICAgIF9jb250ZXh0Ny50MCA9IF9jb250ZXh0N1siY2F0Y2giXSgwKTsKICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfliJvlu7rova7mrKHlpLHotKU6JywgX2NvbnRleHQ3LnQwKTsKICAgICAgICAgICAgICBfdGhpczcuJG1lc3NhZ2UuZXJyb3IoJ+WIm+W7uuWksei0pScpOwogICAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDcuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU3LCBudWxsLCBbWzAsIDddXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8vIOmHjee9ruaWsOi9ruasoeihqOWNlQogICAgcmVzZXROZXdSb3VuZDogZnVuY3Rpb24gcmVzZXROZXdSb3VuZCgpIHsKICAgICAgdGhpcy5uZXdSb3VuZCA9IHsKICAgICAgICBnb29kc19pZDogJycsCiAgICAgICAgZ29vZHNfbmFtZTogJycsCiAgICAgICAgZ29vZHNfaW1hZ2U6ICcnLAogICAgICAgIG9yaWdpbmFsX3ByaWNlOiAnJywKICAgICAgICBmbGFzaF9wcmljZTogJycsCiAgICAgICAgc3RvY2s6ICcnLAogICAgICAgIGxpbWl0X3F1YW50aXR5OiAxCiAgICAgIH07CiAgICB9LAogICAgLy8g6YCJ5oup5ZWG5ZOBCiAgICBvbkdvb2RzU2VsZWN0OiBmdW5jdGlvbiBvbkdvb2RzU2VsZWN0KCkgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKICAgICAgdmFyIHNlbGVjdGVkR29vZHMgPSB0aGlzLmdvb2RzTGlzdC5maW5kKGZ1bmN0aW9uIChnb29kcykgewogICAgICAgIHJldHVybiBnb29kcy5pZCA9PSBfdGhpczgubmV3Um91bmQuZ29vZHNfaWQ7CiAgICAgIH0pOwogICAgICBpZiAoc2VsZWN0ZWRHb29kcykgewogICAgICAgIHRoaXMubmV3Um91bmQuZ29vZHNfbmFtZSA9IHNlbGVjdGVkR29vZHMubmFtZTsKICAgICAgICB0aGlzLm5ld1JvdW5kLmdvb2RzX2ltYWdlID0gc2VsZWN0ZWRHb29kcy5saXN0X3BpY191cmw7CiAgICAgICAgdGhpcy5uZXdSb3VuZC5vcmlnaW5hbF9wcmljZSA9IHNlbGVjdGVkR29vZHMucmV0YWlsX3ByaWNlOwogICAgICB9CiAgICB9LAogICAgLy8g5YiG6aG1CiAgICBjaGFuZ2VQYWdlOiBmdW5jdGlvbiBjaGFuZ2VQYWdlKHBhZ2UpIHsKICAgICAgaWYgKHBhZ2UgPj0gMSAmJiBwYWdlIDw9IHRoaXMucm91bmRzTGlzdC5sYXN0X3BhZ2UpIHsKICAgICAgICB0aGlzLmN1cnJlbnRQYWdlID0gcGFnZTsKICAgICAgICB0aGlzLmxvYWRSb3VuZHNMaXN0KCk7CiAgICAgIH0KICAgIH0sCiAgICAvLyDliLfmlrDmlbDmja4KICAgIHJlZnJlc2hEYXRhOiBmdW5jdGlvbiByZWZyZXNoRGF0YSgpIHsKICAgICAgdGhpcy5sb2FkRGF0YSgpOwogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aVsOaNruWIt+aWsOaIkOWKnycpOwogICAgfSwKICAgIC8vIOS/neWtmOezu+e7n+mFjee9rgogICAgc2F2ZUNvbmZpZzogZnVuY3Rpb24gc2F2ZUNvbmZpZygpIHsKICAgICAgdmFyIF90aGlzOSA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU4KCkgewogICAgICAgIHZhciByZXNwb25zZTsKICAgICAgICByZXR1cm4gX3JlZ2VuZXJhdG9yUnVudGltZSgpLndyYXAoZnVuY3Rpb24gX2NhbGxlZTgkKF9jb250ZXh0OCkgewogICAgICAgICAgd2hpbGUgKDEpIHN3aXRjaCAoX2NvbnRleHQ4LnByZXYgPSBfY29udGV4dDgubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQ4LnByZXYgPSAwOwogICAgICAgICAgICAgIF9jb250ZXh0OC5uZXh0ID0gMzsKICAgICAgICAgICAgICByZXR1cm4gX3RoaXM5LmF4aW9zLnBvc3QoJ2ZsYXNoc2FsZXJvdW5kcy9jb25maWcnLCBfdGhpczkuc3lzdGVtQ29uZmlnKTsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIHJlc3BvbnNlID0gX2NvbnRleHQ4LnNlbnQ7CiAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuZXJybm8gPT09IDApIHsKICAgICAgICAgICAgICAgIF90aGlzOS5zaG93Q29uZmlnTW9kYWwgPSBmYWxzZTsKICAgICAgICAgICAgICAgIF90aGlzOS4kbWVzc2FnZS5zdWNjZXNzKCfphY3nva7kv53lrZjmiJDlip8nKTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgX3RoaXM5LiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLmRhdGEuZXJybXNnIHx8ICfkv53lrZjlpLHotKUnKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgX2NvbnRleHQ4Lm5leHQgPSAxMTsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgIF9jb250ZXh0OC5wcmV2ID0gNzsKICAgICAgICAgICAgICBfY29udGV4dDgudDAgPSBfY29udGV4dDhbImNhdGNoIl0oMCk7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5L+d5a2Y6YWN572u5aSx6LSlOicsIF9jb250ZXh0OC50MCk7CiAgICAgICAgICAgICAgX3RoaXM5LiRtZXNzYWdlLmVycm9yKCfkv53lrZjlpLHotKUnKTsKICAgICAgICAgICAgY2FzZSAxMToKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ4LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlOCwgbnVsbCwgW1swLCA3XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDlvIDlp4voh6rliqjliLfmlrAKICAgIHN0YXJ0QXV0b1JlZnJlc2g6IGZ1bmN0aW9uIHN0YXJ0QXV0b1JlZnJlc2goKSB7CiAgICAgIHZhciBfdGhpczAgPSB0aGlzOwogICAgICAvLyDmr48xMOenkuWIt+aWsOS4gOasoeW9k+WJjei9ruasoQogICAgICB0aGlzLnJlZnJlc2hUaW1lciA9IHNldEludGVydmFsKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczAubG9hZEN1cnJlbnRSb3VuZHMoKTsKICAgICAgICBfdGhpczAubG9hZFN0YXRpc3RpY3MoKTsKICAgICAgfSwgMTAwMDApOwoKICAgICAgLy8g5q+P56eS5pu05paw5YCS6K6h5pe2CiAgICAgIHRoaXMuY291bnRkb3duVGltZXIgPSBzZXRJbnRlcnZhbChmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMwLnVwZGF0ZUNvdW50ZG93bnMoKTsKICAgICAgfSwgMTAwMCk7CiAgICB9LAogICAgLy8g5YGc5q2i6Ieq5Yqo5Yi35pawCiAgICBzdG9wQXV0b1JlZnJlc2g6IGZ1bmN0aW9uIHN0b3BBdXRvUmVmcmVzaCgpIHsKICAgICAgaWYgKHRoaXMucmVmcmVzaFRpbWVyKSB7CiAgICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLnJlZnJlc2hUaW1lcik7CiAgICAgICAgdGhpcy5yZWZyZXNoVGltZXIgPSBudWxsOwogICAgICB9CiAgICAgIGlmICh0aGlzLmNvdW50ZG93blRpbWVyKSB7CiAgICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLmNvdW50ZG93blRpbWVyKTsKICAgICAgICB0aGlzLmNvdW50ZG93blRpbWVyID0gbnVsbDsKICAgICAgfQogICAgfSwKICAgIC8vIOabtOaWsOWAkuiuoeaXtgogICAgdXBkYXRlQ291bnRkb3duczogZnVuY3Rpb24gdXBkYXRlQ291bnRkb3ducygpIHsKICAgICAgLy8g5pu05paw5b2T5YmN6L2u5qyh5YCS6K6h5pe2CiAgICAgIHRoaXMuY3VycmVudFJvdW5kcy5jdXJyZW50LmZvckVhY2goZnVuY3Rpb24gKHJvdW5kKSB7CiAgICAgICAgaWYgKHJvdW5kLmNvdW50ZG93biA+IDApIHsKICAgICAgICAgIHJvdW5kLmNvdW50ZG93bi0tOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHRoaXMuY3VycmVudFJvdW5kcy51cGNvbWluZy5mb3JFYWNoKGZ1bmN0aW9uIChyb3VuZCkgewogICAgICAgIGlmIChyb3VuZC5jb3VudGRvd24gPiAwKSB7CiAgICAgICAgICByb3VuZC5jb3VudGRvd24tLTsKICAgICAgICB9CiAgICAgIH0pOwoKICAgICAgLy8g5pu05paw5YiX6KGo5Lit55qE5YCS6K6h5pe2CiAgICAgIHRoaXMucm91bmRzTGlzdC5kYXRhLmZvckVhY2goZnVuY3Rpb24gKHJvdW5kKSB7CiAgICAgICAgaWYgKHJvdW5kLmNvdW50ZG93biA+IDApIHsKICAgICAgICAgIHJvdW5kLmNvdW50ZG93bi0tOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy8g5qC85byP5YyW5YCS6K6h5pe2CiAgICBmb3JtYXRDb3VudGRvd246IGZ1bmN0aW9uIGZvcm1hdENvdW50ZG93bihzZWNvbmRzKSB7CiAgICAgIGlmIChzZWNvbmRzIDw9IDApIHJldHVybiAnMDA6MDAnOwogICAgICB2YXIgbWludXRlcyA9IE1hdGguZmxvb3Ioc2Vjb25kcyAvIDYwKTsKICAgICAgdmFyIHJlbWFpbmluZ1NlY29uZHMgPSBzZWNvbmRzICUgNjA7CiAgICAgIHJldHVybiAiIi5jb25jYXQobWludXRlcy50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyksICI6IikuY29uY2F0KHJlbWFpbmluZ1NlY29uZHMudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpKTsKICAgIH0sCiAgICAvLyDmoLzlvI/ljJbml6XmnJ/ml7bpl7QKICAgIGZvcm1hdERhdGVUaW1lOiBmdW5jdGlvbiBmb3JtYXREYXRlVGltZShkYXRlVGltZSkgewogICAgICBpZiAoIWRhdGVUaW1lKSByZXR1cm4gJy0nOwogICAgICB2YXIgZGF0ZSA9IG5ldyBEYXRlKGRhdGVUaW1lKTsKICAgICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVTdHJpbmcoJ3poLUNOJywgewogICAgICAgIG1vbnRoOiAnMi1kaWdpdCcsCiAgICAgICAgZGF5OiAnMi1kaWdpdCcsCiAgICAgICAgaG91cjogJzItZGlnaXQnLAogICAgICAgIG1pbnV0ZTogJzItZGlnaXQnCiAgICAgIH0pOwogICAgfSwKICAgIC8vIOiOt+WPlueKtuaAgeagt+W8jwogICAgZ2V0U3RhdHVzQ2xhc3M6IGZ1bmN0aW9uIGdldFN0YXR1c0NsYXNzKHN0YXR1cykgewogICAgICB2YXIgY2xhc3NlcyA9IHsKICAgICAgICAndXBjb21pbmcnOiAnYmctb3JhbmdlLTEwMCB0ZXh0LW9yYW5nZS04MDAnLAogICAgICAgICdhY3RpdmUnOiAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAnLAogICAgICAgICdlbmRlZCc6ICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktODAwJywKICAgICAgICAnY2FuY2VsbGVkJzogJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnCiAgICAgIH07CiAgICAgIHJldHVybiBjbGFzc2VzW3N0YXR1c10gfHwgJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnOwogICAgfSwKICAgIC8vIOiOt+WPlueKtuaAgeaWh+acrAogICAgZ2V0U3RhdHVzVGV4dDogZnVuY3Rpb24gZ2V0U3RhdHVzVGV4dChzdGF0dXMpIHsKICAgICAgdmFyIHRleHRzID0gewogICAgICAgICd1cGNvbWluZyc6ICfljbPlsIblvIDlp4snLAogICAgICAgICdhY3RpdmUnOiAn6L+b6KGM5LitJywKICAgICAgICAnZW5kZWQnOiAn5bey57uT5p2fJywKICAgICAgICAnY2FuY2VsbGVkJzogJ+W3suWPlua2iCcKICAgICAgfTsKICAgICAgcmV0dXJuIHRleHRzW3N0YXR1c10gfHwgJ+acquefpSc7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqZA;EACAA;EACAC;IACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;QACAb;QACAY;QACAE;QACAC;MACA;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACAC,aACA,wBACA,2BACA,wBACA,uBACA,yBACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cAAAC;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAC;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cAAAF;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAC;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAEAC;gBACAC;gBACAC;cACA;cAEA;gBACAF;cACA;cAAA;cAAA,OAEA;gBAAAA;cAAA;YAAA;cAAAJ;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAC;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAM;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cAAAP;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAC;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAO;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cAAAR;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAC;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAQ;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cAAAT;cACA;gBACA;gBACA;gBACA;gBACA;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAS;MACA;QACAjC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACA4B;MAAA;MACA;QAAA;MAAA;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cAAAd;cACA;gBACA;gBACA;cACA;gBACA;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAc;MAAA;MACA;MACA;QACA;QACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACAC;QACA;MACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;UACAC;QACA;MACA;MAEA;QACA;UACAA;QACA;MACA;;MAEA;MACA;QACA;UACAA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;EACA;AACA", "names": ["name", "data", "statistics", "totalRounds", "activeRounds", "upcomingRounds", "endedRounds", "totalOrders", "todayOrders", "totalSales", "currentRounds", "current", "upcoming", "total", "roundsList", "current_page", "last_page", "filterStatus", "currentPage", "showAddModal", "showConfigModal", "newRound", "goods_id", "goods_name", "goods_image", "original_price", "flash_price", "stock", "limit_quantity", "goodsList", "systemConfig", "round_duration", "break_duration", "auto_start_next", "daily_start_time", "daily_end_time", "is_enabled", "refreshTimer", "countdownTimer", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "loadData", "Promise", "loadStatistics", "response", "console", "loadCurrentRounds", "loadRoundsList", "params", "page", "limit", "loadGoodsList", "loadSystemConfig", "createRound", "resetNewRound", "onGoodsSelect", "changePage", "refreshData", "saveConfig", "startAutoRefresh", "stopAutoRefresh", "clearInterval", "updateCountdowns", "round", "formatCountdown", "formatDateTime", "month", "day", "hour", "minute", "getStatusClass", "getStatusText"], "sourceRoot": "src/components/Marketing", "sources": ["FlashSaleRoundsPage.vue"], "sourcesContent": ["<template>\n  <div class=\"flash-sale-rounds-page\">\n    <!-- 页面头部 -->\n    <div class=\"page-header bg-white shadow-sm border-b\">\n      <div class=\"px-6 py-4\">\n        <div class=\"flex items-center justify-between\">\n          <div>\n            <h1 class=\"text-2xl font-semibold text-gray-900\">轮次秒杀</h1>\n            <p class=\"text-sm text-gray-500 mt-1\">每5分钟一轮，间隔2分钟，可选择不同商品参与秒杀</p>\n          </div>\n          <div class=\"flex items-center space-x-3\">\n            <button @click=\"showAddModal = true\" class=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n              <i class=\"ri-add-line mr-2\"></i>\n              创建轮次\n            </button>\n            <button @click=\"showConfigModal = true\" class=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\">\n              <i class=\"ri-settings-line mr-2\"></i>\n              系统配置\n            </button>\n            <button @click=\"refreshData\" class=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n              <i class=\"ri-refresh-line mr-2\"></i>\n              刷新数据\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"p-6 max-w-7xl mx-auto\">\n      <!-- 统计卡片 -->\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center\">\n              <i class=\"ri-flashlight-line text-xl text-red-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm text-gray-500\">总轮次数</p>\n              <p class=\"text-2xl font-bold text-gray-900\">{{ statistics.totalRounds }}</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <i class=\"ri-play-circle-line text-xl text-green-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm text-gray-500\">进行中轮次</p>\n              <p class=\"text-2xl font-bold text-gray-900\">{{ statistics.activeRounds }}</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <i class=\"ri-shopping-cart-line text-xl text-blue-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm text-gray-500\">秒杀订单数</p>\n              <p class=\"text-2xl font-bold text-gray-900\">{{ statistics.totalOrders }}</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\n              <i class=\"ri-money-dollar-circle-line text-xl text-yellow-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm text-gray-500\">秒杀销售额</p>\n              <p class=\"text-2xl font-bold text-gray-900\">¥{{ statistics.totalSales }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 当前轮次状态 -->\n      <div class=\"bg-white rounded-lg shadow-sm border mb-6\">\n        <div class=\"px-6 py-4 border-b\">\n          <h2 class=\"text-lg font-semibold text-gray-900\">当前轮次状态</h2>\n        </div>\n        <div class=\"p-6\">\n          <div v-if=\"currentRounds.current.length > 0\" class=\"mb-6\">\n            <h3 class=\"text-md font-medium text-gray-900 mb-4\">🔥 进行中轮次</h3>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              <div v-for=\"round in currentRounds.current\" :key=\"round.id\" \n                   class=\"bg-red-50 border-2 border-red-200 rounded-lg p-4\">\n                <div class=\"flex items-center justify-between mb-3\">\n                  <span class=\"text-sm font-medium text-red-600\">第{{ round.round_number }}轮</span>\n                  <span class=\"px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full\">进行中</span>\n                </div>\n                <div class=\"mb-3\">\n                  <h4 class=\"font-medium text-gray-900\">{{ round.goods_name }}</h4>\n                  <div class=\"flex items-center space-x-2 mt-1\">\n                    <span class=\"text-lg font-bold text-red-600\">¥{{ round.flash_price }}</span>\n                    <span class=\"text-sm text-gray-500 line-through\">¥{{ round.original_price }}</span>\n                  </div>\n                </div>\n                <div class=\"mb-3\">\n                  <div class=\"flex justify-between text-sm text-gray-600 mb-1\">\n                    <span>库存进度</span>\n                    <span>{{ round.sold_count }}/{{ round.stock }}</span>\n                  </div>\n                  <div class=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div class=\"bg-red-600 h-2 rounded-full\" :style=\"{width: round.stockProgress + '%'}\"></div>\n                  </div>\n                </div>\n                <div class=\"text-center\">\n                  <div class=\"text-lg font-bold text-red-600\">{{ formatCountdown(round.countdown) }}</div>\n                  <div class=\"text-xs text-gray-500\">剩余时间</div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div v-if=\"currentRounds.upcoming.length > 0\">\n            <h3 class=\"text-md font-medium text-gray-900 mb-4\">⏰ 即将开始</h3>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              <div v-for=\"round in currentRounds.upcoming\" :key=\"round.id\" \n                   class=\"bg-orange-50 border-2 border-orange-200 rounded-lg p-4\">\n                <div class=\"flex items-center justify-between mb-3\">\n                  <span class=\"text-sm font-medium text-orange-600\">第{{ round.round_number }}轮</span>\n                  <span class=\"px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full\">即将开始</span>\n                </div>\n                <div class=\"mb-3\">\n                  <h4 class=\"font-medium text-gray-900\">{{ round.goods_name }}</h4>\n                  <div class=\"flex items-center space-x-2 mt-1\">\n                    <span class=\"text-lg font-bold text-orange-600\">¥{{ round.flash_price }}</span>\n                    <span class=\"text-sm text-gray-500 line-through\">¥{{ round.original_price }}</span>\n                  </div>\n                </div>\n                <div class=\"mb-3\">\n                  <div class=\"text-sm text-gray-600\">\n                    库存：{{ round.stock }}件 | 限购：{{ round.limit_quantity }}件\n                  </div>\n                </div>\n                <div class=\"text-center\">\n                  <div class=\"text-lg font-bold text-orange-600\">{{ formatCountdown(round.countdown) }}</div>\n                  <div class=\"text-xs text-gray-500\">开始倒计时</div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div v-if=\"currentRounds.current.length === 0 && currentRounds.upcoming.length === 0\" \n               class=\"text-center py-8 text-gray-500\">\n            <i class=\"ri-time-line text-4xl mb-2\"></i>\n            <p>暂无进行中或即将开始的轮次</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- 轮次列表 -->\n      <div class=\"bg-white rounded-lg shadow-sm border\">\n        <div class=\"px-6 py-4 border-b\">\n          <div class=\"flex items-center justify-between\">\n            <h2 class=\"text-lg font-semibold text-gray-900\">轮次列表</h2>\n            <div class=\"flex items-center space-x-2\">\n              <select v-model=\"filterStatus\" @change=\"loadRoundsList\" \n                      class=\"px-3 py-1 border border-gray-300 rounded-md text-sm\">\n                <option value=\"\">全部状态</option>\n                <option value=\"upcoming\">即将开始</option>\n                <option value=\"active\">进行中</option>\n                <option value=\"ended\">已结束</option>\n              </select>\n            </div>\n          </div>\n        </div>\n        <div class=\"overflow-x-auto\">\n          <table class=\"min-w-full divide-y divide-gray-200\">\n            <thead class=\"bg-gray-50\">\n              <tr>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">轮次</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">商品信息</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">价格</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">库存</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">时间</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">状态</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">倒计时</th>\n              </tr>\n            </thead>\n            <tbody class=\"bg-white divide-y divide-gray-200\">\n              <tr v-for=\"round in roundsList.data\" :key=\"round.id\">\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"text-sm font-medium text-gray-900\">第{{ round.round_number }}轮</div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"flex items-center\">\n                    <img v-if=\"round.goods_image\" :src=\"round.goods_image\" \n                         class=\"h-10 w-10 rounded-lg object-cover mr-3\" :alt=\"round.goods_name\">\n                    <div class=\"w-10 h-10 bg-gray-200 rounded-lg mr-3 flex items-center justify-center\" v-else>\n                      <i class=\"ri-image-line text-gray-400\"></i>\n                    </div>\n                    <div>\n                      <div class=\"text-sm font-medium text-gray-900\">{{ round.goods_name }}</div>\n                      <div class=\"text-sm text-gray-500\">ID: {{ round.goods_id }}</div>\n                    </div>\n                  </div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"text-sm font-medium text-red-600\">¥{{ round.flash_price }}</div>\n                  <div class=\"text-sm text-gray-500 line-through\">¥{{ round.original_price }}</div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"text-sm text-gray-900\">{{ round.sold_count }}/{{ round.stock }}</div>\n                  <div class=\"w-16 bg-gray-200 rounded-full h-1 mt-1\">\n                    <div class=\"bg-blue-600 h-1 rounded-full\" :style=\"{width: round.stockProgress + '%'}\"></div>\n                  </div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                  <div>{{ formatDateTime(round.start_time) }}</div>\n                  <div>{{ formatDateTime(round.end_time) }}</div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <span :class=\"getStatusClass(round.status)\" \n                        class=\"px-2 inline-flex text-xs leading-5 font-semibold rounded-full\">\n                    {{ getStatusText(round.status) }}\n                  </span>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                  <div v-if=\"round.countdown > 0\" class=\"font-medium\">\n                    {{ formatCountdown(round.countdown) }}\n                  </div>\n                  <div v-else class=\"text-gray-400\">-</div>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n        \n        <!-- 分页 -->\n        <div v-if=\"roundsList.total > 0\" class=\"px-6 py-4 border-t\">\n          <div class=\"flex items-center justify-between\">\n            <div class=\"text-sm text-gray-500\">\n              共 {{ roundsList.total }} 条记录，第 {{ roundsList.current_page }} / {{ roundsList.last_page }} 页\n            </div>\n            <div class=\"flex space-x-2\">\n              <button @click=\"changePage(roundsList.current_page - 1)\" \n                      :disabled=\"roundsList.current_page <= 1\"\n                      class=\"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50\">\n                上一页\n              </button>\n              <button @click=\"changePage(roundsList.current_page + 1)\" \n                      :disabled=\"roundsList.current_page >= roundsList.last_page\"\n                      class=\"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50\">\n                下一页\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 创建轮次模态框 -->\n    <div v-if=\"showAddModal\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div class=\"bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h3 class=\"text-lg font-semibold text-gray-900\">创建秒杀轮次</h3>\n          <button @click=\"showAddModal = false\" class=\"text-gray-400 hover:text-gray-600\">\n            <i class=\"ri-close-line text-xl\"></i>\n          </button>\n        </div>\n\n        <form @submit.prevent=\"createRound\" class=\"space-y-4\">\n          <!-- 商品选择 -->\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 mb-2\">选择商品</label>\n            <select v-model=\"newRound.goods_id\" @change=\"onGoodsSelect\"\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    required>\n              <option value=\"\">请选择商品</option>\n              <option v-for=\"goods in goodsList\" :key=\"goods.id\" :value=\"goods.id\">\n                {{ goods.name }} - ¥{{ goods.retail_price }}\n              </option>\n            </select>\n          </div>\n\n          <!-- 商品预览 -->\n          <div v-if=\"newRound.goods_id\" class=\"p-4 bg-gray-50 rounded-lg\">\n            <div class=\"flex items-center space-x-4\">\n              <img v-if=\"newRound.goods_image\" :src=\"newRound.goods_image\"\n                   class=\"w-16 h-16 object-cover rounded-lg\" alt=\"商品图片\">\n              <div>\n                <div class=\"font-medium text-gray-900\">{{ newRound.goods_name }}</div>\n                <div class=\"text-sm text-gray-500\">原价: ¥{{ newRound.original_price }}</div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 价格设置 -->\n          <div class=\"grid grid-cols-2 gap-4\">\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">秒杀价格</label>\n              <input v-model=\"newRound.flash_price\" type=\"number\" step=\"0.01\" min=\"0\"\n                     class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                     placeholder=\"请输入秒杀价格\" required>\n            </div>\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">库存数量</label>\n              <input v-model=\"newRound.stock\" type=\"number\" min=\"1\"\n                     class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                     placeholder=\"请输入库存数量\" required>\n            </div>\n          </div>\n\n          <!-- 限购数量 -->\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 mb-2\">限购数量</label>\n            <input v-model=\"newRound.limit_quantity\" type=\"number\" min=\"1\"\n                   class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                   placeholder=\"每人限购数量\">\n          </div>\n\n          <!-- 提示信息 -->\n          <div class=\"p-4 bg-blue-50 rounded-lg\">\n            <div class=\"flex items-start\">\n              <i class=\"ri-information-line text-blue-500 mt-0.5 mr-2\"></i>\n              <div class=\"text-sm text-blue-700\">\n                <div class=\"font-medium mb-1\">轮次规则说明：</div>\n                <ul class=\"list-disc list-inside space-y-1\">\n                  <li>每轮秒杀持续5分钟</li>\n                  <li>轮次间隔2分钟</li>\n                  <li>系统将自动安排开始时间</li>\n                  <li>创建后立即进入排队状态</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          <!-- 操作按钮 -->\n          <div class=\"flex justify-end space-x-3 pt-4\">\n            <button type=\"button\" @click=\"showAddModal = false\"\n                    class=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50\">\n              取消\n            </button>\n            <button type=\"submit\"\n                    class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\">\n              创建轮次\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n\n    <!-- 系统配置模态框 -->\n    <div v-if=\"showConfigModal\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div class=\"bg-white rounded-lg p-6 w-full max-w-lg mx-4\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h3 class=\"text-lg font-semibold text-gray-900\">系统配置</h3>\n          <button @click=\"showConfigModal = false\" class=\"text-gray-400 hover:text-gray-600\">\n            <i class=\"ri-close-line text-xl\"></i>\n          </button>\n        </div>\n\n        <div class=\"space-y-4\">\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 mb-2\">轮次时长（秒）</label>\n            <input v-model=\"systemConfig.round_duration\" type=\"number\" min=\"60\"\n                   class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n          </div>\n\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 mb-2\">间隔时长（秒）</label>\n            <input v-model=\"systemConfig.break_duration\" type=\"number\" min=\"0\"\n                   class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n          </div>\n\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 mb-2\">运营时间</label>\n            <div class=\"grid grid-cols-2 gap-4\">\n              <input v-model=\"systemConfig.daily_start_time\" type=\"time\"\n                     class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n              <input v-model=\"systemConfig.daily_end_time\" type=\"time\"\n                     class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n            </div>\n          </div>\n\n          <div class=\"flex items-center\">\n            <input v-model=\"systemConfig.is_enabled\" type=\"checkbox\" id=\"enabled\"\n                   class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\">\n            <label for=\"enabled\" class=\"ml-2 block text-sm text-gray-900\">启用秒杀系统</label>\n          </div>\n        </div>\n\n        <div class=\"flex justify-end space-x-3 pt-6\">\n          <button @click=\"showConfigModal = false\"\n                  class=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50\">\n            取消\n          </button>\n          <button @click=\"saveConfig\"\n                  class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\">\n            保存配置\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FlashSaleRoundsPage',\n  data() {\n    return {\n      // 统计数据\n      statistics: {\n        totalRounds: 0,\n        activeRounds: 0,\n        upcomingRounds: 0,\n        endedRounds: 0,\n        totalOrders: 0,\n        todayOrders: 0,\n        totalSales: 0\n      },\n\n      // 当前轮次\n      currentRounds: {\n        current: [],\n        upcoming: [],\n        total: 0\n      },\n\n      // 轮次列表\n      roundsList: {\n        data: [],\n        total: 0,\n        current_page: 1,\n        last_page: 1\n      },\n\n      // 筛选状态\n      filterStatus: '',\n      currentPage: 1,\n\n      // 模态框状态\n      showAddModal: false,\n      showConfigModal: false,\n\n      // 新轮次表单\n      newRound: {\n        goods_id: '',\n        goods_name: '',\n        goods_image: '',\n        original_price: '',\n        flash_price: '',\n        stock: '',\n        limit_quantity: 1\n      },\n\n      // 商品列表\n      goodsList: [],\n\n      // 系统配置\n      systemConfig: {\n        round_duration: 300,\n        break_duration: 120,\n        auto_start_next: true,\n        daily_start_time: '09:00:00',\n        daily_end_time: '22:00:00',\n        is_enabled: true\n      },\n\n      // 定时器\n      refreshTimer: null,\n      countdownTimer: null\n    }\n  },\n\n  mounted() {\n    this.loadData();\n    this.startAutoRefresh();\n  },\n\n  beforeDestroy() {\n    this.stopAutoRefresh();\n  },\n\n  methods: {\n    // 加载所有数据\n    async loadData() {\n      await Promise.all([\n        this.loadStatistics(),\n        this.loadCurrentRounds(),\n        this.loadRoundsList(),\n        this.loadGoodsList(),\n        this.loadSystemConfig()\n      ]);\n    },\n\n    // 加载统计数据\n    async loadStatistics() {\n      try {\n        const response = await this.axios.get('flashsalerounds/statistics');\n        if (response.data.errno === 0) {\n          this.statistics = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载统计数据失败:', error);\n      }\n    },\n\n    // 加载当前轮次\n    async loadCurrentRounds() {\n      try {\n        const response = await this.axios.get('flashsalerounds/current');\n        if (response.data.errno === 0) {\n          this.currentRounds = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载当前轮次失败:', error);\n      }\n    },\n\n    // 加载轮次列表\n    async loadRoundsList() {\n      try {\n        const params = {\n          page: this.currentPage,\n          limit: 20\n        };\n\n        if (this.filterStatus) {\n          params.status = this.filterStatus;\n        }\n\n        const response = await this.axios.get('flashsalerounds/list', { params });\n        if (response.data.errno === 0) {\n          this.roundsList = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载轮次列表失败:', error);\n      }\n    },\n\n    // 加载商品列表\n    async loadGoodsList() {\n      try {\n        const response = await this.axios.get('flashsalerounds/goods');\n        if (response.data.errno === 0) {\n          this.goodsList = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载商品列表失败:', error);\n      }\n    },\n\n    // 加载系统配置\n    async loadSystemConfig() {\n      try {\n        const response = await this.axios.get('flashsalerounds/config');\n        if (response.data.errno === 0) {\n          this.systemConfig = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载系统配置失败:', error);\n      }\n    },\n\n    // 创建新轮次\n    async createRound() {\n      try {\n        const response = await this.axios.post('flashsalerounds/create', this.newRound);\n        if (response.data.errno === 0) {\n          this.showAddModal = false;\n          this.$message.success('轮次创建成功');\n          this.loadData();\n          this.resetNewRound();\n        } else {\n          this.$message.error(response.data.errmsg || '创建失败');\n        }\n      } catch (error) {\n        console.error('创建轮次失败:', error);\n        this.$message.error('创建失败');\n      }\n    },\n\n    // 重置新轮次表单\n    resetNewRound() {\n      this.newRound = {\n        goods_id: '',\n        goods_name: '',\n        goods_image: '',\n        original_price: '',\n        flash_price: '',\n        stock: '',\n        limit_quantity: 1\n      };\n    },\n\n    // 选择商品\n    onGoodsSelect() {\n      const selectedGoods = this.goodsList.find(goods => goods.id == this.newRound.goods_id);\n      if (selectedGoods) {\n        this.newRound.goods_name = selectedGoods.name;\n        this.newRound.goods_image = selectedGoods.list_pic_url;\n        this.newRound.original_price = selectedGoods.retail_price;\n      }\n    },\n\n    // 分页\n    changePage(page) {\n      if (page >= 1 && page <= this.roundsList.last_page) {\n        this.currentPage = page;\n        this.loadRoundsList();\n      }\n    },\n\n    // 刷新数据\n    refreshData() {\n      this.loadData();\n      this.$message.success('数据刷新成功');\n    },\n\n    // 保存系统配置\n    async saveConfig() {\n      try {\n        const response = await this.axios.post('flashsalerounds/config', this.systemConfig);\n        if (response.data.errno === 0) {\n          this.showConfigModal = false;\n          this.$message.success('配置保存成功');\n        } else {\n          this.$message.error(response.data.errmsg || '保存失败');\n        }\n      } catch (error) {\n        console.error('保存配置失败:', error);\n        this.$message.error('保存失败');\n      }\n    },\n\n    // 开始自动刷新\n    startAutoRefresh() {\n      // 每10秒刷新一次当前轮次\n      this.refreshTimer = setInterval(() => {\n        this.loadCurrentRounds();\n        this.loadStatistics();\n      }, 10000);\n\n      // 每秒更新倒计时\n      this.countdownTimer = setInterval(() => {\n        this.updateCountdowns();\n      }, 1000);\n    },\n\n    // 停止自动刷新\n    stopAutoRefresh() {\n      if (this.refreshTimer) {\n        clearInterval(this.refreshTimer);\n        this.refreshTimer = null;\n      }\n      if (this.countdownTimer) {\n        clearInterval(this.countdownTimer);\n        this.countdownTimer = null;\n      }\n    },\n\n    // 更新倒计时\n    updateCountdowns() {\n      // 更新当前轮次倒计时\n      this.currentRounds.current.forEach(round => {\n        if (round.countdown > 0) {\n          round.countdown--;\n        }\n      });\n\n      this.currentRounds.upcoming.forEach(round => {\n        if (round.countdown > 0) {\n          round.countdown--;\n        }\n      });\n\n      // 更新列表中的倒计时\n      this.roundsList.data.forEach(round => {\n        if (round.countdown > 0) {\n          round.countdown--;\n        }\n      });\n    },\n\n    // 格式化倒计时\n    formatCountdown(seconds) {\n      if (seconds <= 0) return '00:00';\n\n      const minutes = Math.floor(seconds / 60);\n      const remainingSeconds = seconds % 60;\n\n      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n    },\n\n    // 格式化日期时间\n    formatDateTime(dateTime) {\n      if (!dateTime) return '-';\n      const date = new Date(dateTime);\n      return date.toLocaleString('zh-CN', {\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    // 获取状态样式\n    getStatusClass(status) {\n      const classes = {\n        'upcoming': 'bg-orange-100 text-orange-800',\n        'active': 'bg-red-100 text-red-800',\n        'ended': 'bg-gray-100 text-gray-800',\n        'cancelled': 'bg-gray-100 text-gray-800'\n      };\n      return classes[status] || 'bg-gray-100 text-gray-800';\n    },\n\n    // 获取状态文本\n    getStatusText(status) {\n      const texts = {\n        'upcoming': '即将开始',\n        'active': '进行中',\n        'ended': '已结束',\n        'cancelled': '已取消'\n      };\n      return texts[status] || '未知';\n    }\n  }\n}\n</script>\n\n<style scoped>\n.flash-sale-rounds-page {\n  min-height: 100vh;\n  background-color: #f9fafb;\n}\n\n.page-header {\n  position: sticky;\n  top: 0;\n  z-index: 10;\n}\n\n/* 状态指示灯动画 */\n.status-indicator {\n  position: relative;\n}\n\n.status-indicator.active::before {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 8px;\n  height: 8px;\n  background-color: #ef4444;\n  border-radius: 50%;\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    transform: translate(-50%, -50%) scale(0.95);\n    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);\n  }\n\n  70% {\n    transform: translate(-50%, -50%) scale(1);\n    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);\n  }\n\n  100% {\n    transform: translate(-50%, -50%) scale(0.95);\n    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);\n  }\n}\n\n/* 倒计时数字动画 */\n.countdown-number {\n  transition: all 0.3s ease;\n}\n\n.countdown-number.urgent {\n  color: #ef4444;\n  font-weight: bold;\n  animation: shake 0.5s ease-in-out;\n}\n\n@keyframes shake {\n  0%, 100% { transform: translateX(0); }\n  25% { transform: translateX(-2px); }\n  75% { transform: translateX(2px); }\n}\n\n/* 库存进度条动画 */\n.stock-progress {\n  transition: width 0.5s ease;\n}\n\n/* 卡片悬停效果 */\n.round-card {\n  transition: all 0.3s ease;\n}\n\n.round-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\n}\n</style>\n"]}]}