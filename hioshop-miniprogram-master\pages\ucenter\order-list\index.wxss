page {
    background: #f8f8f8;
    height: 100%;
}

.container {
    width: 100%;
    background: #f8f9fa;
    min-height: 100vh;
    overflow-x: hidden;
    padding-top: 96rpx;
}

/* 现代化标签栏 - 参考main.html设计 */
.tab-nav {
    position: fixed;
    top: 0rpx;
    width: 100%;
    height: 96rpx;
    display: flex;
    background-color: #fff;
    border-bottom: 1rpx solid #f1f1f1;
    z-index: 10;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.tab {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    color: #666;
    height: 96rpx;
    position: relative;
    transition: all 0.3s ease;
}

.tab.active {
    color: #ff3456;
    font-weight: 500;
}

.tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40rpx;
    height: 4rpx;
    background: #ff3456;
    border-radius: 2rpx;
}

.list-num {
    position: absolute;
    top: -2rpx;
    right: -12rpx;
    width: 30rpx;
    height: 30rpx;
    font-size: 20rpx;
    background: #ff3456;
    color: #fff;
    /* border: 1rpx solid #ff3456; */
    border-radius: 30rpx;
    text-align: center;
    line-height: 30rpx;
}



.no-order {
    width: 100%;
    display: none;
    position: absolute;
    bottom: 0;
    top: 88rpx;
    left: 0;
    right: 0;
    text-align: center;
    padding-top: 203rpx;
}

.show {
    display: block;
}

.no-order-img {
    width: 100rpx;
    height: 100rpx;
    margin-bottom: 30rpx;
}

.no-order .text {
    font-size: 30rpx;
    color: #999;
    text-align: center;
    margin-bottom: 30rpx;
}

.to-index-btn {
    color: #fff;
    background: linear-gradient(to right, #ff3456, #ff347d);
    border-radius: 10rpx;
    width: 300rpx;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    font-size: 28rpx;
    margin: 0 auto;
}

/* 现代化订单容器 - 参考main.html设计 */
.orders-container {
    padding: 24rpx;
    background: #f8f9fa;
}

/* 订单卡片样式 */
.order-card {
    background: white;
    border-radius: 16rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.order-card:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
    background: #f8f9fa;
}

/* 订单头部 */
.order-header {
    padding: 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid #f1f1f1;
}

.order-time {
    font-size: 26rpx;
    color: #666;
}

.order-status-wrap {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.offline-pay-tag {
    background: #f0f0f0;
    color: #666;
    font-size: 20rpx;
    padding: 4rpx 12rpx;
    border-radius: 8rpx;
}

.list-top-wrap .status .pay-status {
    color: #fff;
    border-radius: 8rpx;
    background: #594d72;
    height: 30rpx;
    line-height: 30rpx;
    padding: 8rpx 20rpx;
    font-size: 24rpx;
    margin-right: 20rpx;
}

/* 订单状态样式 - 参考main.html颜色设计 */
.order-status {
    font-size: 26rpx;
    font-weight: 500;
    padding: 6rpx 16rpx;
    border-radius: 12rpx;
    background: #f0f0f0;
}

.status-pending {
    color: #ef4444;
    background: #fef2f2;
}

.status-paid {
    color: #3b82f6;
    background: #eff6ff;
}

.status-shipped {
    color: #f59e0b;
    background: #fffbeb;
}

.status-success {
    color: #ff3456;
    background: #fef2f2;
}

.status-closed {
    color: #6b7280;
    background: #f9fafb;
}

/* 售后状态样式 */
.status-refund {
    color: #fa541c;
    background: #fff2e8;
    border: 1rpx solid #ffbb96;
}



/* 商品信息区域 */
.order-goods {
    position: relative;
    padding: 24rpx;
    display: flex;
    align-items: center;
    gap: 24rpx;
}

.goods-images {
    display: flex;
    gap: 8rpx;
}

.goods-thumb {
    width: 120rpx;
    height: 120rpx;
    border-radius: 16rpx;
    background: #f5f5f5;
}

.goods-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 120rpx;
    padding-left: 16rpx;
}

.goods-names {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1;
}

.goods-name {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 8rpx;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
}

.more-goods {
    font-size: 24rpx;
    color: #999;
    margin-top: 4rpx;
}

.goods-count {
    position: absolute;
    right: 24rpx;
    bottom: 24rpx;
    font-size: 24rpx;
    color: #999;
}

.order-list-wrap .goods-list {
    width: 100%;
    background-color: #fff;
}

.goods-list .list-title {
    font-size: 26rpx;
    color: #666;
    padding: 24rpx;
    border-bottom: 1rpx solid #eee;
}

.goods-list .a-goods {
    display: flex;
    padding: 24rpx;
    background-color: #fcfcfc;
    justify-content: space-between;
    align-items: flex-start;
}

.a-goods .img-box {
    height: 120rpx;
    display: flex;
    justify-content: flex-start;
}

.goods-image {
    width: 120rpx;
    height: 120rpx;
    margin-right: 18rpx;
    background-color: #fff;
    /* border:1rpx solid #eee;
    border-radius: 8rpx; */
}

/* 商品信息区域 */
.goods-info {
    flex: 1;
    margin-left: 20rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 120rpx;
}

/* 商品名称区域 */
.goods-names {
    flex: 1;
    margin-bottom: 10rpx;
}

.goods-name-item {
    font-size: 26rpx;
    color: #333;
    line-height: 1.4;
    margin-bottom: 8rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.more-goods {
    font-size: 24rpx;
    color: #999;
    margin-top: 5rpx;
}

.goods-sum {
    font-size: 26rpx;
    color: #666;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 10rpx;
}

.goods-sum .text{
    margin-right: 20rpx;
}

.goods-sum .arrow{
    width: 10rpx;
    height: 10rpx;
    border-top: 4rpx solid #ccc;
    border-right: 4rpx solid #ccc;
    transform: rotate(45deg);
}

.order-price-wrap {
    border-top: 1rpx solid #f4f4f4;
    height: 90rpx;
    line-height: 90rpx;
    padding: 0 24rpx;
    display: flex;
    border-bottom: 1rpx solid #f4f4f4;
    justify-content: flex-end;
    align-content: center;
    background: #fff;
}

.order-price {
    height: 90rpx;
    display: flex;
    justify-content: flex-end;
}

.price-label {
    font-size: 26rpx;
    color: #666;
}

.price-sum {
    font-size: 30rpx;
    color: #233445;
}

.trans {
    font-size: 26rpx;
    color: #233445;
}

.order-edit {
    height: 100rpx;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.edit-btn {
    padding: 8rpx 20rpx;
    border-radius: 6rpx;
    font-size: 24rpx;
    color: #fff;
    background: linear-gradient(to right, #ff3456, #ff347d);
    height: 40rpx;
    line-height: 40rpx;
    border: 1rpx solid #ff3456;
    margin-right: 24rpx;
}

.dele-btn {
    padding: 8rpx 20rpx;
    border-radius: 6rpx;
    font-size: 24rpx;
    height: 40rpx;
    line-height: 40rpx;
    margin-right: 24rpx;
    color: #666;
    background: #fff;
    border: 1rpx solid #ccc;
}

/* 订单底部样式 */
.order-footer {
    padding: 24rpx;
    border-top: 1rpx solid #f1f1f1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 箭头提示 */
.order-arrow {
    display: flex;
    align-items: center;
}

.arrow-icon {
    font-size: 32rpx;
    color: #ccc;
    font-weight: bold;
}

.order-total {
    display: flex;
    align-items: baseline;
    gap: 8rpx;
}

.total-label {
    font-size: 26rpx;
    color: #333;
}

.total-price {
    font-size: 32rpx;
    color: #ff3456;
}

.freight-info {
    font-size: 22rpx;
    color: #999;
}

.order-actions {
    display: flex;
    gap: 16rpx;
}

/* 现代化按钮样式 - 参考main.html */
.btn-primary {
    background: #ff3456;
    color: white;
    font-size: 26rpx;
    padding: 16rpx 32rpx;
    border-radius: 20rpx;
    text-align: center;
    transition: all 0.3s ease;
}

.btn-primary:active {
    transform: scale(0.95);
    background: #e02d4a;
}

.btn-secondary {
    background: transparent;
    color: #ff3456;
    font-size: 26rpx;
    padding: 16rpx 32rpx;
    border: 1rpx solid #ff3456;
    border-radius: 20rpx;
    text-align: center;
    transition: all 0.3s ease;
}

.btn-secondary:active {
    transform: scale(0.95);
    background: #fef2f2;
}

/* 底部提示 */
.no-more-tip {
    text-align: center;
    font-size: 26rpx;
    color: #999;
    padding: 40rpx;
    background: #f8f9fa;
}