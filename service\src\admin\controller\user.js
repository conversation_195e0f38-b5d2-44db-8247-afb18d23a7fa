const Base = require('./base.js');
const moment = require('moment');
module.exports = class extends Base {
    /**
     * index action
     * @return {Promise} []
     */
    async indexAction() {
        const page = this.get('page') || 1;
        const size = this.get('size') || 10;
        let nickname = this.get('nickname') || '';
        const buffer = Buffer.from(nickname);
        nickname = buffer.toString('base64');
        const model = this.model('user');
        const data = await model.where({
            nickname: ['like', `%${nickname}%`],
        }).field('id,nickname,gender,mobile,avatar,register_time,last_login_time').order(['id DESC']).page(page, size).countSelect();

        for (const item of data.data) {
            item.register_time = moment.unix(item.register_time).format('YYYY-MM-DD HH:mm:ss');
            item.last_login_time = moment.unix(item.last_login_time).format('YYYY-MM-DD HH:mm:ss');
            item.nickname = Buffer.from(item.nickname, 'base64').toString();

            // 处理头像URL
            if (!item.avatar || item.avatar === '' || item.avatar.startsWith('wxfile://')) {
                // 如果没有头像或者是微信临时文件，使用默认头像
                item.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';
            } else if (!item.avatar.startsWith('http://') && !item.avatar.startsWith('https://')) {
                // 如果是相对路径，补充完整的域名
                item.avatar = `https://ht.rxkjsdj.com${item.avatar}`;
            }

            // 查询是否为推广员（使用正确的表名）
            let isPromoter = false;
            let isDistributor = false;

            try {
                // 查询个人推广员表（使用完整表名）
                const promoter = await this.model().query(`
                    SELECT id FROM hiolabs_personal_promoters
                    WHERE user_id = ${item.id} AND status = 1
                    LIMIT 1
                `);
                isPromoter = promoter && promoter.length > 0;
            } catch (error) {
                console.log('查询推广员表失败:', error.message);
            }

            try {
                // 查询分销商表（使用完整表名）
                const distributor = await this.model().query(`
                    SELECT id FROM hiolabs_distributors
                    WHERE user_id = ${item.id} AND is_active = 1 AND audit_status = 1
                    LIMIT 1
                `);
                isDistributor = distributor && distributor.length > 0;
            } catch (error) {
                console.log('查询分销商表失败:', error.message);
            }

            // 设置推广员状态（推广员或分销商都算作推广员）
            item.is_distributor = isPromoter || isDistributor;
        }
        let info = {
            userData: data,
        }
        return this.success(info);
    }
    async infoAction() {
        const id = this.get('id');
        const model = this.model('user');
        let info = await model.where({
            id: id
        }).find();
        info.register_time = moment.unix(info.register_time).format('YYYY-MM-DD HH:mm:ss');
        info.last_login_time = moment.unix(info.last_login_time).format('YYYY-MM-DD HH:mm:ss');
        info.nickname = Buffer.from(info.nickname, 'base64').toString();

        // 处理头像URL
        if (!info.avatar || info.avatar === '' || info.avatar.startsWith('wxfile://')) {
            // 如果没有头像或者是微信临时文件，使用默认头像
            info.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';
        } else if (!info.avatar.startsWith('http://') && !info.avatar.startsWith('https://')) {
            // 如果是相对路径，补充完整的域名
            info.avatar = `https://ht.rxkjsdj.com${info.avatar}`;
        }

        // 查询是否为推广员（使用正确的表名）
        let isPromoter = false;
        let isDistributor = false;

        try {
            // 查询个人推广员表（使用完整表名）
            const promoter = await this.model().query(`
                SELECT id FROM hiolabs_personal_promoters
                WHERE user_id = ${info.id} AND status = 1
                LIMIT 1
            `);
            isPromoter = promoter && promoter.length > 0;
        } catch (error) {
            console.log('查询推广员表失败:', error.message);
        }

        try {
            // 查询分销商表（使用完整表名）
            const distributor = await this.model().query(`
                SELECT id FROM hiolabs_distributors
                WHERE user_id = ${info.id} AND is_active = 1 AND audit_status = 1
                LIMIT 1
            `);
            isDistributor = distributor && distributor.length > 0;
        } catch (error) {
            console.log('查询分销商表失败:', error.message);
        }

        // 设置推广员状态
        info.is_distributor = isPromoter || isDistributor;

        return this.success(info);
    }
    async datainfoAction() {
        const id = this.get('id');
        let info = {};
        info.orderSum = await this.model('order').where({
            user_id: id,
            order_type: ['<', 8],
            is_delete: 0
        }).count();
        info.orderDone = await this.model('order').where({
            user_id: id,
            order_status: ['IN', '302,303,401'],
            order_type: ['<', 8],
            is_delete: 0
        }).count();
        info.orderMoney = await this.model('order').where({
            user_id: id,
            order_status: ['IN', '302,303,401'],
            order_type: ['<', 8],
            is_delete: 0
        }).sum('actual_price');
        info.cartSum = await this.model('cart').where({
            user_id: id,
            is_delete: 0
        }).sum('number');
        return this.success(info);
    }
    async addressAction() {
        const id = this.get('id');
        const page = this.get('page') || 1;
        const size = this.get('size') || 10;
        let addr = await this.model('address').where({
            user_id: id
        }).page(page, size).countSelect();
        for (const item of addr.data) {
            let province_name = await this.model('region').where({
                id: item.province_id
            }).getField('name', true);
            let city_name = await this.model('region').where({
                id: item.city_id
            }).getField('name', true);
            let district_name = await this.model('region').where({
                id: item.district_id
            }).getField('name', true);
            item.full_region = province_name + city_name + district_name + item.address;
        }
        return this.success(addr);
    }
    async saveaddressAction() {
        const id = this.post('id');
        const user_id = this.post('user_id');
        const name = this.post('name');
        const mobile = this.post('mobile');
        const address = this.post('address');
        const addOptions = this.post('addOptions');
        const province = addOptions[0];
        const city = addOptions[1];
        const district = addOptions[2];
        let info = {
            name: name,
            mobile: mobile,
            address: address,
            province_id: province,
            district_id: district,
            city_id: city
        }
        await this.model('address').where({
            user_id: user_id,
            id: id
        }).update(info);
        return this.success();
    }
    async cartdataAction() {
        const id = this.get('id');
        const page = this.get('page') || 1;
        const size = this.get('size') || 10;
        const model = this.model('cart');
        const data = await model.where({
            user_id: id
        }).order(['add_time DESC']).page(page, size).countSelect();
        for (const item of data.data) {
            item.add_time = moment.unix(item.add_time).format('YYYY-MM-DD HH:mm:ss');
        }
        return this.success(data);
    }
    async footAction() {
        const id = this.get('id');
        const page = this.get('page') || 1;
        const size = this.get('size') || 10;
        const model = this.model('footprint');
        const data = await model.alias('f').join({
            table: 'goods',
            join: 'left',
            as: 'g',
            on: ['f.goods_id', 'g.id']
        }).where({
            user_id: id
        }).page(page, size).countSelect();
        console.log(data);
        return this.success(data);
    }
    async orderAction() {
        const page = this.get('page') || 1;
        const size = this.get('size') || 10;
        const user_id = this.get('id');
        const model = this.model('order');
        const data = await model.where({
            user_id: user_id,
            order_type: ['<', 8],
        }).order(['id DESC']).page(page, size).countSelect();
        console.log(data.count);
        for (const item of data.data) {
            item.goodsList = await this.model('order_goods').field('goods_name,list_pic_url,number,goods_specifition_name_value,retail_price').where({
                order_id: item.id,
                is_delete: 0
            }).select();
            item.goodsCount = 0;
            item.goodsList.forEach(v => {
                item.goodsCount += v.number;
            });
            let province_name = await this.model('region').where({
                id: item.province
            }).getField('name', true);
            let city_name = await this.model('region').where({
                id: item.city
            }).getField('name', true);
            let district_name = await this.model('region').where({
                id: item.district
            }).getField('name', true);
            item.full_region = province_name + city_name + district_name;
            item.postscript = Buffer.from(item.postscript, 'base64').toString();
            item.add_time = moment.unix(item.add_time).format('YYYY-MM-DD HH:mm:ss');
            item.order_status_text = await this.model('order').getOrderStatusText(item.id);
            item.button_text = await this.model('order').getOrderBtnText(item.id);
        }
        return this.success(data);
    }
    async getOrderStatusText(orderInfo) {
        let statusText = '待付款';
        switch (orderInfo.order_status) {
            case 101:
                statusText = '待付款';
                break;
            case 102:
                statusText = '交易关闭';
                break;
            case 103:
                statusText = '交易关闭'; //到时间系统自动取消
                break;
            case 201:
                statusText = '待发货';
                break;
            case 202:
                statusText = '退款中';
                break;
            case 203:
                statusText = '已退款';
                break;
            case 300:
                statusText = '已备货';
                break;
            case 301:
                statusText = '已发货';
                break;
            case 302:
                statusText = '待评价';
                break;
            case 303:
                statusText = '待评价'; //到时间，未收货的系统自动收货、
                break;
            case 401:
                statusText = '交易成功'; //到时间，未收货的系统自动收货、
                break;
            case 801:
                statusText = '拼团待付款';
                break;
            case 802:
                statusText = '拼团中'; // 如果sum变为0了。则，变成201待发货
                break;
        }
        return statusText;
    }
    async updateInfoAction() {
        const id = this.post('id');
        let nickname = this.post('nickname');
        const buffer = Buffer.from(nickname);
        nickname = buffer.toString('base64');
        const model = this.model('user');
        const data = await model.where({
            id: id
        }).update({
            nickname: nickname
        });
        return this.success(data);
    }
    async updateNameAction() {
        const id = this.post('id');
        const name = this.post('name');
        const model = this.model('user');
        const data = await model.where({
            id: id
        }).update({
            name: name
        });
        return this.success(data);
    }
    async updateMobileAction() {
        const id = this.post('id');
        const mobile = this.post('mobile');
        const model = this.model('user');
        const data = await model.where({
            id: id
        }).update({
            mobile: mobile
        });
        return this.success(data);
    }
    async storeAction() {
        if (!this.isPost) {
            return false;
        }
        const values = this.post();
        const id = this.post('id');
        const model = this.model('user');
        values.is_show = values.is_show ? 1 : 0;
        values.is_new = values.is_new ? 1 : 0;
        if (id > 0) {
            await model.where({
                id: id
            }).update(values);
        } else {
            delete values.id;
            await model.add(values);
        }
        return this.success(values);
    }
    async destoryAction() {
        const id = this.post('id');
        await this.model('user').where({
            id: id
        }).limit(1).delete();
        return this.success();
    }

    // 获取用户地址列表（用于购物车详情弹窗）
    async addressesAction() {
        const userId = this.get('id');
        const addresses = await this.model('address').where({
            user_id: userId
        }).order(['is_default DESC', 'id DESC']).select();

        // 处理地址信息，获取完整的省市区名称
        for (const item of addresses) {
            try {
                const province = await this.model('region').where({ id: item.province_id }).getField('name', true);
                const city = await this.model('region').where({ id: item.city_id }).getField('name', true);
                const district = await this.model('region').where({ id: item.district_id }).getField('name', true);

                item.province = province || '';
                item.city = city || '';
                item.county = district || '';
                item.address_detail = item.address || '';
            } catch (error) {
                console.log('获取地址区域信息失败:', error.message);
                item.province = '';
                item.city = '';
                item.county = '';
                item.address_detail = item.address || '';
            }
        }

        return this.success(addresses);
    }

    // 获取用户购物车统计信息
    async cartStatsAction() {
        const userId = this.get('id');

        try {
            // 获取购物车商品总数
            const totalItems = await this.model('cart').where({
                user_id: userId,
                is_delete: 0
            }).sum('number') || 0;

            // 获取购物车总金额
            const cartItems = await this.model('cart').where({
                user_id: userId,
                is_delete: 0
            }).select();

            let totalAmount = 0;
            for (const item of cartItems) {
                // 获取商品当前价格
                const goods = await this.model('goods').where({ id: item.goods_id }).find();
                if (goods) {
                    totalAmount += parseFloat(goods.retail_price || 0) * parseInt(item.number || 0);
                }
            }

            const stats = {
                totalItems: parseInt(totalItems),
                totalAmount: totalAmount.toFixed(2)
            };

            return this.success(stats);
        } catch (error) {
            console.log('获取购物车统计失败:', error.message);
            return this.success({
                totalItems: 0,
                totalAmount: '0.00'
            });
        }
    }
};