module.exports = class extends think.Model {
    /**
     * 获取售后申请表名
     */
    get tableName() {
        return 'hiolabs_refund_apply';
    }

    /**
     * 获取售后申请详情
     * @param {number} applyId 申请ID
     * @returns {Promise<Object>}
     */
    async getApplyDetail(applyId) {
        const apply = await this.where({ id: applyId }).find();
        if (think.isEmpty(apply)) {
            return {};
        }

        // 解析图片JSON
        if (apply.images) {
            try {
                apply.images = JSON.parse(apply.images);
            } catch (e) {
                apply.images = [];
            }
        } else {
            apply.images = [];
        }

        return apply;
    }

    /**
     * 获取用户的售后申请列表
     * @param {number} userId 用户ID
     * @param {number} page 页码
     * @param {number} size 每页数量
     * @returns {Promise<Object>}
     */
    async getUserApplyList(userId, page = 1, size = 10) {
        const list = await this.where({ user_id: userId })
            .order('apply_time DESC')
            .page(page, size)
            .countSelect();

        // 处理图片字段
        if (list.data && list.data.length > 0) {
            list.data.forEach(item => {
                if (item.images) {
                    try {
                        item.images = JSON.parse(item.images);
                    } catch (e) {
                        item.images = [];
                    }
                } else {
                    item.images = [];
                }
            });
        }

        return list;
    }

    /**
     * 更新申请状态
     * @param {number} applyId 申请ID
     * @param {string} status 新状态
     * @param {string} memo 备注
     * @returns {Promise<number>}
     */
    async updateApplyStatus(applyId, status, memo = '') {
        const updateData = {
            status: status,
            updated_at: new Date()
        };

        if (status === 'processing') {
            updateData.process_time = parseInt(new Date().getTime() / 1000);
        } else if (status === 'completed') {
            updateData.complete_time = parseInt(new Date().getTime() / 1000);
        }

        if (memo) {
            updateData.admin_memo = memo;
        }

        return await this.where({ id: applyId }).update(updateData);
    }

    /**
     * 拒绝申请
     * @param {number} applyId 申请ID
     * @param {string} rejectReason 拒绝原因
     * @returns {Promise<number>}
     */
    async rejectApply(applyId, rejectReason) {
        return await this.where({ id: applyId }).update({
            status: 'rejected',
            reject_reason: rejectReason,
            process_time: parseInt(new Date().getTime() / 1000),
            updated_at: new Date()
        });
    }

    /**
     * 获取申请统计
     * @returns {Promise<Object>}
     */
    async getApplyStats() {
        const stats = await this.field('status, COUNT(*) as count')
            .group('status')
            .select();

        const result = {
            pending: 0,
            processing: 0,
            approved: 0,
            rejected: 0,
            completed: 0,
            total: 0
        };

        stats.forEach(item => {
            result[item.status] = parseInt(item.count);
            result.total += parseInt(item.count);
        });

        return result;
    }
};
