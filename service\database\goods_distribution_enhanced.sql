-- 商品分销池增强版数据库结构
-- 支持多级分销和复杂佣金计算

-- 1. 扩展商品分销配置表
ALTER TABLE `hiolabs_goods_distribution` 
ADD COLUMN `level1_rate` decimal(5,2) DEFAULT '0.00' COMMENT '一级分销佣金比例(%)',
ADD COLUMN `level2_rate` decimal(5,2) DEFAULT '0.00' COMMENT '二级分销佣金比例(%)',
ADD COLUMN `personal_rate` decimal(5,2) DEFAULT '0.00' COMMENT '个人推广佣金比例(%)',
ADD COLUMN `team_leader_rate` decimal(5,2) DEFAULT '0.00' COMMENT '团长额外佣金比例(%)',
ADD COLUMN `min_level_required` tinyint(1) DEFAULT '1' COMMENT '最低分销等级要求',
ADD COLUMN `max_level_allowed` tinyint(1) DEFAULT '4' COMMENT '最高分销等级限制',
ADD COLUMN `category_id` int(10) unsigned DEFAULT '0' COMMENT '商品分类ID',
ADD COLUMN `priority` int(10) DEFAULT '0' COMMENT '分销优先级',
ADD COLUMN `start_time` int(10) unsigned DEFAULT '0' COMMENT '分销开始时间',
ADD COLUMN `end_time` int(10) unsigned DEFAULT '0' COMMENT '分销结束时间',
ADD COLUMN `daily_limit` int(10) DEFAULT '0' COMMENT '每日分销限额(0=无限制)',
ADD COLUMN `total_limit` int(10) DEFAULT '0' COMMENT '总分销限额(0=无限制)',
ADD COLUMN `current_count` int(10) DEFAULT '0' COMMENT '当前分销数量',
ADD COLUMN `tags` varchar(255) DEFAULT '' COMMENT '商品标签(JSON格式)',
ADD COLUMN `description` text COMMENT '分销说明',
ADD INDEX `idx_category_id` (`category_id`),
ADD INDEX `idx_level_required` (`min_level_required`),
ADD INDEX `idx_priority` (`priority`),
ADD INDEX `idx_start_end_time` (`start_time`, `end_time`);

-- 2. 创建分销佣金规则模板表
CREATE TABLE IF NOT EXISTS `hiolabs_commission_templates` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_code` varchar(50) NOT NULL COMMENT '模板代码',
  `category_id` int(10) unsigned DEFAULT '0' COMMENT '适用分类ID(0=全部)',
  `price_min` decimal(10,2) DEFAULT '0.00' COMMENT '价格区间最小值',
  `price_max` decimal(10,2) DEFAULT '999999.99' COMMENT '价格区间最大值',
  `personal_rate` decimal(5,2) DEFAULT '0.00' COMMENT '个人推广佣金比例',
  `level1_rate` decimal(5,2) DEFAULT '0.00' COMMENT '一级分销佣金比例',
  `level2_rate` decimal(5,2) DEFAULT '0.00' COMMENT '二级分销佣金比例',
  `team_leader_rate` decimal(5,2) DEFAULT '0.00' COMMENT '团长额外佣金比例',
  `min_level_required` tinyint(1) DEFAULT '1' COMMENT '最低等级要求',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认模板',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `sort_order` int(10) DEFAULT '0' COMMENT '排序',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_code` (`template_code`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_price_range` (`price_min`, `price_max`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销佣金规则模板表';

-- 3. 创建分销员关系表（支持多级关系）
CREATE TABLE IF NOT EXISTS `hiolabs_distributor_relations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '分销员用户ID',
  `parent_id` int(10) unsigned DEFAULT '0' COMMENT '上级分销员用户ID',
  `level` tinyint(1) DEFAULT '1' COMMENT '在关系链中的层级(1=直接下级)',
  `relation_path` varchar(500) DEFAULT '' COMMENT '关系路径(如: 1,2,3)',
  `team_leader_id` int(10) unsigned DEFAULT '0' COMMENT '所属团长ID',
  `join_time` int(10) unsigned NOT NULL COMMENT '建立关系时间',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '关系是否有效',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_parent` (`user_id`, `parent_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`),
  KEY `idx_team_leader_id` (`team_leader_id`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员关系表';

-- 4. 创建分销佣金记录表
CREATE TABLE IF NOT EXISTS `hiolabs_distribution_commissions` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` int(10) unsigned NOT NULL COMMENT '订单ID',
  `goods_id` int(10) unsigned NOT NULL COMMENT '商品ID',
  `buyer_user_id` int(10) unsigned NOT NULL COMMENT '购买者用户ID',
  `promoter_user_id` int(10) unsigned NOT NULL COMMENT '推广员用户ID',
  `commission_type` enum('personal','level1','level2','team_leader') NOT NULL COMMENT '佣金类型',
  `commission_rate` decimal(5,2) NOT NULL COMMENT '佣金比例',
  `order_amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `commission_amount` decimal(10,2) NOT NULL COMMENT '佣金金额',
  `promoter_level` tinyint(1) NOT NULL COMMENT '推广员等级',
  `status` enum('pending','confirmed','paid','cancelled') DEFAULT 'pending' COMMENT '佣金状态',
  `settle_time` int(10) unsigned DEFAULT '0' COMMENT '结算时间',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_buyer_user_id` (`buyer_user_id`),
  KEY `idx_promoter_user_id` (`promoter_user_id`),
  KEY `idx_commission_type` (`commission_type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销佣金记录表';

-- 5. 创建分销统计表
CREATE TABLE IF NOT EXISTS `hiolabs_distribution_stats` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '分销员用户ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `personal_orders` int(10) DEFAULT '0' COMMENT '个人推广订单数',
  `personal_amount` decimal(10,2) DEFAULT '0.00' COMMENT '个人推广金额',
  `personal_commission` decimal(10,2) DEFAULT '0.00' COMMENT '个人推广佣金',
  `level1_orders` int(10) DEFAULT '0' COMMENT '一级分销订单数',
  `level1_amount` decimal(10,2) DEFAULT '0.00' COMMENT '一级分销金额',
  `level1_commission` decimal(10,2) DEFAULT '0.00' COMMENT '一级分销佣金',
  `level2_orders` int(10) DEFAULT '0' COMMENT '二级分销订单数',
  `level2_amount` decimal(10,2) DEFAULT '0.00' COMMENT '二级分销金额',
  `level2_commission` decimal(10,2) DEFAULT '0.00' COMMENT '二级分销佣金',
  `team_leader_commission` decimal(10,2) DEFAULT '0.00' COMMENT '团长额外佣金',
  `total_commission` decimal(10,2) DEFAULT '0.00' COMMENT '总佣金',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_date` (`user_id`, `stat_date`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销统计表';

-- 6. 插入默认佣金模板
INSERT INTO `hiolabs_commission_templates` 
(`template_name`, `template_code`, `category_id`, `price_min`, `price_max`, `personal_rate`, `level1_rate`, `level2_rate`, `team_leader_rate`, `min_level_required`, `is_default`, `is_active`, `sort_order`, `create_time`, `update_time`) 
VALUES
('基础商品模板', 'basic', 0, 0.00, 100.00, 8.00, 3.00, 1.00, 2.00, 1, 1, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('中档商品模板', 'medium', 0, 100.01, 500.00, 10.00, 4.00, 2.00, 3.00, 2, 0, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('高档商品模板', 'premium', 0, 500.01, 999999.99, 15.00, 6.00, 3.00, 5.00, 3, 0, 1, 3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
