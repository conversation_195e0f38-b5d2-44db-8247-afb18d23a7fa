{"_from": "seq-queue@^0.0.5", "_id": "seq-queue@0.0.5", "_inBundle": false, "_integrity": "sha512-hr3Wtp/GZIc/6DAGPDcV4/9WoZhjrkXsi5B/07QgX8tsdc6ilr7BFM6PM6rbdAX1kFSDYeZGLipIZZKyQP0O5Q==", "_location": "/seq-queue", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "seq-queue@^0.0.5", "name": "seq-queue", "escapedName": "seq-queue", "rawSpec": "^0.0.5", "saveSpec": null, "fetchSpec": "^0.0.5"}, "_requiredBy": ["/mysql2"], "_resolved": "https://registry.npmjs.org/seq-queue/-/seq-queue-0.0.5.tgz", "_shasum": "d56812e1c017a6e4e7c3e3a37a1da6d78dd3c93e", "_spec": "seq-queue@^0.0.5", "_where": "D:\\py-ide\\hioshop-miniprogram-master\\node_modules\\mysql2", "author": {"name": "changchang", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/changchang/seq-queue/issues"}, "bundleDependencies": false, "contributors": [{"name": "* <PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "deprecated": false, "description": "A simple tool to keep requests to be executed in order.", "devDependencies": {"mocha": ">=0.0.1", "should": ">=0.0.1"}, "homepage": "https://github.com/changchang/seq-queue", "name": "seq-queue", "repository": {"type": "git", "url": "git+ssh://**************/changchang/seq-queue.git"}, "version": "0.0.5"}