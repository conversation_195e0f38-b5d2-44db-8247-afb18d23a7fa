{"version": 3, "sources": ["..\\..\\src\\config\\wangdian.js"], "names": ["isDev", "process", "env", "NODE_ENV", "module", "exports", "current", "test", "sid", "appkey", "appsecret", "baseUrl", "shopNo", "warehouseNo", "platformId", "enabled", "production"], "mappings": "AAAA;;;;AAIA,MAAMA,QAAQC,QAAQC,GAAR,CAAYC,QAAZ,KAAyB,aAAvC;;AAEAC,OAAOC,OAAP,GAAiB;AACb;AACAC,aAAS,MAFI;;AAIb;AACAC,UAAM;AACFC,aAAK,SADH,EAC2B;AAC7BC,gBAAQ,YAFN,EAE2B;AAC7BC,mBAAW,kCAHT,EAG8C;AAChDC,iBAAS,kCAJP,EAI+C;AACjDC,gBAAQ,SALN,EAK2B;AAC7BC,qBAAa,MANX,EAM2B;AAC7BC,oBAAY,GAPV,EAO2B;AAC7BC,iBAAS,IARP,CAQ2B;AAR3B,KALO;;AAgBb;AACAC,gBAAY;AACRR,aAAK,qBADG,EAC8B;AACtCC,gBAAQ,wBAFA,EAE8B;AACtCC,mBAAW,wBAHH,EAG8B;AACtCC,iBAAS,kCAJD,EAIsC;AAC9CC,gBAAQ,sBALA,EAK8B;AACtCC,qBAAa,2BANL,EAMkC;AAC1CC,oBAAY,GAPJ,EAO8B;AACtCC,iBAAS,KARD,CAQ8B;AAR9B;AAjBC,CAAjB", "file": "..\\..\\src\\config\\wangdian.js", "sourcesContent": ["/**\n * 旺店通ERP配置\n */\n\nconst isDev = process.env.NODE_ENV === 'development';\n\nmodule.exports = {\n    // 当前使用的配置 - 默认使用测试环境\n    current: 'test',\n    \n    // 测试环境配置\n    test: {\n        sid: 'rx00002',              // 卖家账号\n        appkey: 'rx00002-gw',        // 接口账号\n        appsecret: '42af07635c3b606a075ff68146c93405',  // 接口密钥\n        baseUrl: 'https://api.wangdian.cn/openapi2',     // 生产环境\n        shopNo: 'XCX0001',           // 店铺编号\n        warehouseNo: '0021',         // 仓库编号\n        platformId: 127,             // 平台ID\n        enabled: true                // 是否启用\n    },\n    \n    // 生产环境配置\n    production: {\n        sid: 'your_production_sid',           // 生产环境卖家账号\n        appkey: 'your_production_appkey',     // 生产环境接口账号\n        appsecret: 'your_production_secret',  // 生产环境接口密钥\n        baseUrl: 'https://api.wangdian.cn/openapi2',  // 生产环境\n        shopNo: 'your_production_shop',       // 生产环境店铺编号\n        warehouseNo: 'your_production_warehouse', // 生产环境仓库编号\n        platformId: 127,                      // 平台ID\n        enabled: false                        // 生产环境暂时禁用，需要配置后启用\n    }\n};\n"]}