const rp = require("request-promise");
module.exports = class extends think.Controller {

  // 小程序扫码确认登录
  async confirmQrLoginAction() {
    try {
      const qrToken = this.post('qrToken');
      const openid = this.post('openid') || this.get('openid');

      if (!qrToken) {
        return this.fail('缺少二维码token');
      }

      if (!openid) {
        return this.fail('用户未登录');
      }

      const currentTime = parseInt(Date.now() / 1000);

      // 查询二维码token
      const qrData = await this.model().query(`
        SELECT * FROM hiolabs_qr_login_tokens WHERE token = '${qrToken}' LIMIT 1
      `);
      const qrRecord = qrData && qrData.length > 0 ? qrData[0] : null;

      if (!qrRecord) {
        return this.fail('无效的二维码');
      }

      // 检查是否过期
      if (currentTime > qrRecord.expire_time) {
        await this.model().query(`
          UPDATE hiolabs_qr_login_tokens SET status = 'expired' WHERE token = '${qrToken}'
        `);
        return this.fail('二维码已过期，请刷新后重试');
      }

      // 检查是否已经被使用
      if (qrRecord.status === 'success') {
        return this.fail('二维码已被使用');
      }

      // 通过openid查找用户
      const user = await this.model('user').where({
        weixin_openid: openid,
        is_delete: 0
      }).find();

      if (think.isEmpty(user)) {
        return this.fail('用户不存在');
      }

      // 检查用户是否为推广员或分销商
      const userType = await this.checkUserRole(user.id);
      if (!userType) {
        return this.fail('您还不是推广员或分销商，无法登录管理后台');
      }

      // 更新二维码状态
      await this.model().query(`
        UPDATE hiolabs_qr_login_tokens
        SET status = 'success', user_id = ${user.id}, user_type = '${userType}',
            scan_time = ${currentTime}, login_time = ${currentTime}
        WHERE token = '${qrToken}'
      `);

      return this.success({
        message: '登录确认成功，请在网页中查看',
        userType: userType,
        userName: user.nickname
      });

    } catch (error) {
      console.error('扫码登录确认失败:', error);
      return this.fail('登录确认失败，请重试');
    }
  }

  // 检查用户角色
  async checkUserRole(userId) {
    try {
      // 检查是否为个人推广员
      const promoter = await this.model('personal_promoters').where({
        user_id: userId,
        status: 1
      }).find();

      if (!think.isEmpty(promoter)) {
        return 'promoter';
      }

      // 检查是否为分销商
      const distributor = await this.model('distributors').where({
        user_id: userId,
        is_active: 1,
        audit_status: 1
      }).find();

      if (!think.isEmpty(distributor)) {
        return 'distributor';
      }

      return null;
    } catch (error) {
      console.error('检查用户角色失败:', error);
      return null;
    }
  }
  async loginByWeixinAction() {
    // 获取code和用户信息
    const code = this.post("code");
    const userInfo = this.post("userInfo"); // 获取前端传来的用户信息

    // 添加调试日志
    console.log('=== 授权登录调试信息 ===');
    console.log('接收到的code:', code);
    console.log('接收到的userInfo:', userInfo);
    console.log('userInfo类型:', typeof userInfo);
    console.log('userInfo是否为空:', think.isEmpty(userInfo));

    let currentTime = parseInt(new Date().getTime() / 1000);
    const clientIp = ""; // 暂时不记录 ip test git

    // 获取openid
    const options = {
      method: "GET",
      url: "https://api.weixin.qq.com/sns/jscode2session",
      qs: {
        grant_type: "authorization_code",
        js_code: code,
        secret: think.config("weixin.secret"),
        appid: think.config("weixin.appid"),
      },
    };

    let sessionData = await rp(options);
    sessionData = JSON.parse(sessionData);
    if (!sessionData.openid) {
      return this.fail("登录失败，openid无效");
    }

    // 根据openid查找用户是否已经注册
    let userId = await this.model("user")
      .where({
        weixin_openid: sessionData.openid,
      })
      .getField("id", true);

    let is_new = 0;

    // 准备用户数据
    // 默认昵称和头像
    const buffer = Buffer.from('微信用户');
    let nickname = buffer.toString("base64");
    let avatarUrl = '/images/icon/default_avatar_big.jpg';

    // 如果前端传来了用户信息，则使用前端传来的昵称和头像
    if (userInfo) {
      if (userInfo.nickName) {
        // 对昵称进行Base64编码存储
        const nickBuffer = Buffer.from(userInfo.nickName);
        nickname = nickBuffer.toString("base64");
      }

      if (userInfo.avatarUrl) {
        // 检查是否是wxfile://路径（小程序本地临时文件）
        if (userInfo.avatarUrl.startsWith('wxfile://')) {
          console.log('检测到wxfile://路径，保持原样存储:', userInfo.avatarUrl);
          // 对于wxfile://路径，直接存储，前端可以正常显示
          avatarUrl = userInfo.avatarUrl;
        } else {
          // 其他情况（如https://路径）直接存储
          avatarUrl = userInfo.avatarUrl;
        }
        console.log('最终头像URL:', avatarUrl);
      }
    }

    if (think.isEmpty(userId)) {
      // 注册新用户
      userId = await this.model("user").add({
        username: "微信用户" + think.uuid(6), // 保持原有username生成逻辑
        password: sessionData.openid,
        register_time: currentTime,
        register_ip: clientIp,
        last_login_time: currentTime,
        last_login_ip: clientIp,
        mobile: "",
        weixin_openid: sessionData.openid,
        nickname: nickname,
        avatar: avatarUrl,
        is_new_user: 1 // 标记为新用户
      });
      is_new = 1;

      // 自动发放新人券
      try {
        await this.autoDistributeNewUserCoupons(userId);
      } catch (error) {
        console.log('自动发放新人券失败:', error);
        // 不影响注册流程，只记录错误
      }
    } else if (userInfo) {
      // 如果用户已存在且前端传来了用户信息，则更新用户的昵称和头像
      console.log('=== 执行数据库更新 ===');
      console.log('用户ID:', userId);
      console.log('更新昵称:', nickname);
      console.log('更新头像:', avatarUrl);

      const updateResult = await this.model("user")
        .where({
          id: userId,
        })
        .update({
          nickname: nickname,
          avatar: avatarUrl,
          last_login_time: currentTime,
          last_login_ip: clientIp,
        });

      console.log('数据库更新结果:', updateResult);
    } else {
      // 仅更新登录信息
      console.log('=== 仅更新登录信息（未传入userInfo）===');
      console.log('用户ID:', userId);

      await this.model("user")
        .where({
          id: userId,
        })
        .update({
          last_login_time: currentTime,
          last_login_ip: clientIp,
        });
    }

    sessionData.user_id = userId;

    const newUserInfo = await this.model("user")
      .field("id,username,nickname, avatar")
      .where({
        id: userId,
      })
      .find();

    newUserInfo.nickname = Buffer.from(
      newUserInfo.nickname,
      "base64"
    ).toString();

    // 添加授权标识字段
    // 如果前端传来了userInfo，说明用户已授权；否则为静默登录（未授权）
    newUserInfo.is_authorized = userInfo ? true : false;

    const TokenSerivce = this.service("token", "api");
    const sessionKey = await TokenSerivce.create(sessionData);

    if (think.isEmpty(newUserInfo) || think.isEmpty(sessionKey)) {
      return this.fail("登录失败");
    }

    return this.success({
      token: sessionKey,
      userInfo: newUserInfo,
      is_new: is_new,
      openid: sessionData.openid,
    });
  }
  async logoutAction() {
    return this.success();
  }

  /**
   * 获取用户手机号
   */
  async getPhoneNumberAction() {
    try {
      // 获取前端传来的code和用户ID
      const code = this.post("code");
      const userId = this.post("userId"); // 前端需要传递用户ID

      console.log('=== 获取手机号接口调用 ===');
      console.log('接收到的code:', code);
      console.log('用户ID:', userId);

      if (!code) {
        return this.fail(400, 'code参数必须提供');
      }

      // 如果没有传递userId，尝试从token获取
      let currentUserId = userId;
      if (!currentUserId) {
        const token = this.ctx.header['x-hioshop-token'] || this.ctx.header['X-Hioshop-Token'] || '';
        if (token) {
          const tokenService = think.service('token', 'api');
          currentUserId = await tokenService.getUserId(token);
          console.log('从token获取到的用户ID:', currentUserId);
        }
      }

      // 获取access_token
      const tokenOptions = {
        method: "GET",
        url: "https://api.weixin.qq.com/cgi-bin/token",
        qs: {
          grant_type: "client_credential",
          appid: think.config("weixin.appid"),
          secret: think.config("weixin.secret"),
        },
      };

      let tokenResponse = await rp(tokenOptions);
      tokenResponse = JSON.parse(tokenResponse);

      console.log('获取access_token响应:', tokenResponse);

      if (!tokenResponse.access_token) {
        console.log('获取access_token失败:', tokenResponse);
        return this.fail(500, '获取access_token失败');
      }

      // 调用微信接口获取手机号
      const phoneOptions = {
        method: "POST",
        url: "https://api.weixin.qq.com/wxa/business/getuserphonenumber",
        qs: {
          access_token: tokenResponse.access_token
        },
        body: {
          code: code
        },
        json: true
      };

      let phoneResponse = await rp(phoneOptions);

      console.log('获取手机号响应:', phoneResponse);

      if (phoneResponse.errcode === 0) {
        // 成功获取手机号
        const phoneInfo = phoneResponse.phone_info;
        console.log('手机号信息:', phoneInfo);

        // 如果有用户ID，更新用户表中的手机号
        if (currentUserId) {
          try {
            const userModel = this.model('user');
            await userModel.where({
              id: currentUserId
            }).update({
              mobile: phoneInfo.phoneNumber,
              updated_at: new Date()
            });
            console.log('用户手机号更新成功:', currentUserId, phoneInfo.phoneNumber);
          } catch (updateError) {
            console.error('更新用户手机号失败:', updateError);
            // 不影响手机号返回，只记录错误
          }
        } else {
          console.log('未找到用户ID，跳过手机号更新');
        }

        return this.success({
          phoneNumber: phoneInfo.phoneNumber,
          purePhoneNumber: phoneInfo.purePhoneNumber,
          countryCode: phoneInfo.countryCode,
          userId: currentUserId // 返回用户ID供前端使用
        });
      } else {
        // 获取手机号失败
        console.log('微信接口返回错误:', phoneResponse);
        return this.fail(phoneResponse.errcode, phoneResponse.errmsg || '获取手机号失败');
      }

    } catch (error) {
      console.log('获取手机号异常:', error);
      return this.fail(500, '服务器内部错误');
    }
  }

  /**
   * 自动发放新人券
   */
  async autoDistributeNewUserCoupons(userId) {
    try {
      // 首先检查用户是否已经有新人券
      const hasNewUserCoupon = await this.model('user_coupons').alias('uc')
        .join('hiolabs_coupons c ON uc.coupon_id = c.id')
        .where({
          'uc.user_id': userId,
          'c.type': 'newuser'
        }).count();

      if (hasNewUserCoupon > 0) {
        console.log('用户已有新人券，跳过自动发放');
        return;
      }

      // 获取所有自动发放的新人券
      const newUserCoupons = await this.model('coupons').where({
        type: 'newuser',
        status: 'active',
        auto_distribute: 1,
        is_delete: 0,
        start_time: ['<=', new Date()],
        end_time: ['>=', new Date()]
      }).select();

      console.log('找到自动发放新人券:', newUserCoupons.length, '张');

      // 只发放第一张可用的新人券（每人限一张）
      if (newUserCoupons.length > 0) {
        const coupon = newUserCoupons[0]; // 选择第一张
        try {
          // 生成优惠券码
          const couponCode = this.generateCouponCode();
          const expireAt = coupon.valid_days ?
            new Date(Date.now() + coupon.valid_days * 24 * 60 * 60 * 1000) :
            new Date(coupon.end_time);

          await this.model('user_coupons').add({
            user_id: userId,
            coupon_id: coupon.id,
            coupon_code: couponCode,
            expire_at: expireAt,
            source: 'auto'
          });

          console.log('成功发放新人券:', coupon.name, '给用户:', userId);
        } catch (error) {
          console.log('发放新人券失败:', coupon.name, error);
        }
      }
    } catch (error) {
      console.log('自动发放新人券异常:', error);
      throw error;
    }
  }

  /**
   * 生成优惠券码
   */
  generateCouponCode() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `CPN${timestamp}${random}`.toUpperCase();
  }
};
