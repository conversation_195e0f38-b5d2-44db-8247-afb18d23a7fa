/* 数据中心页面样式 */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 20rpx;
}

/* 数据概览 */
.data-overview {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  padding: 40rpx 30rpx;
  color: white;
}

.overview-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.header-subtitle {
  font-size: 26rpx;
  opacity: 0.8;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stats-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 15rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  backdrop-filter: blur(10rpx);
}

.stats-value {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 时间筛选 */
.time-filter {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 15rpx;
  padding: 10rpx;
}

.filter-tabs {
  display: flex;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 10rpx;
  transition: all 0.3s;
}

.filter-tab.active {
  background: #4facfe;
  color: white;
}

/* 详细数据 */
.data-details {
  margin: 0 30rpx;
}

.detail-section {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-period {
  font-size: 24rpx;
  color: #4facfe;
  background: rgba(79, 172, 254, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.section-more {
  color: #666;
  font-size: 26rpx;
}

/* 详细数据网格 */
.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.detail-item {
  text-align: center;
}

.detail-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #4facfe;
  margin-bottom: 10rpx;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
}

/* 团队数据 */
.team-stats {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.team-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.team-item:last-child {
  border-bottom: none;
}

.team-label {
  font-size: 28rpx;
  color: #333;
}

.team-value {
  font-size: 30rpx;
  font-weight: bold;
  color: #4facfe;
}

/* 排行榜 */
.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.ranking-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.ranking-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.ranking-number {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #ddd;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.ranking-number.top {
  background: linear-gradient(135deg, #ffd700, #ffb347);
}

.ranking-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.ranking-info {
  flex: 1;
}

.ranking-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.ranking-desc {
  font-size: 24rpx;
  color: #666;
}

.ranking-right {
  text-align: right;
}

.ranking-commission {
  font-size: 30rpx;
  font-weight: bold;
  color: #ff6b6b;
}

/* 订单列表 */
.order-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.order-left {
  flex: 1;
}

.order-goods {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-right {
  text-align: right;
}

.order-amount {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.order-commission {
  font-size: 24rpx;
  color: #ff6b6b;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 0;
}

.empty-text {
  font-size: 26rpx;
  color: #999;
}
