{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\flashsalemulti.js"], "names": ["Base", "require", "module", "exports", "statisticsAction", "roundModel", "model", "orderModel", "totalRounds", "count", "activeRounds", "where", "status", "upcomingRounds", "endedRounds", "totalOrders", "today", "Date", "toISOString", "slice", "todayOrders", "created_at", "totalSales", "sum", "success", "error", "console", "fail", "testAction", "log", "rounds", "select", "length", "message", "data", "listAction", "page", "get", "limit", "roundGoodsModel", "order", "countSelect", "round", "id", "round_name", "goods", "getRoundGoodsList", "goods_list", "goods_count", "total_stock", "reduce", "g", "stock", "total_sold", "sold_count", "roundError", "currentAction", "currentRounds", "now", "startTime", "start_time", "endTime", "end_time", "countdown", "Math", "max", "floor", "current", "upcoming", "total", "createAction", "post", "JSON", "stringify", "Array", "isArray", "isNaN", "getTime", "goodsModel", "i", "goods_id", "flash_price", "is_hourly_flash", "goodsIds", "map", "conflictGoods", "checkGoodsInActiveRounds", "conflictNames", "round_number", "join", "checkGoodsInOverlappingRounds", "lastRound", "find", "nextRoundNumber", "roundData", "replace", "created_by", "think", "userId", "slot_index", "total_slots", "roundId", "add", "Error", "goodsList", "goodsItem", "isEmpty", "originalPrice", "parseFloat", "retail_price", "flashPrice", "discountRate", "push", "goods_name", "name", "goods_image", "list_pic_url", "original_price", "discount_rate", "limit_quantity", "addRoundGoods", "result", "goodsAction", "productModel", "allGoods", "is_on_sale", "is_delete", "field", "activeGoodsIds", "alias", "table", "as", "on", "activeIds", "item", "Promise", "all", "products", "priceRange", "totalStock", "prices", "p", "filter", "goods_number", "minPrice", "min", "maxPrice", "toFixed", "actualStock", "isInFlashSale", "includes", "price_range", "actual_stock", "is_in_flash_sale", "can_select", "warning", "configAction", "configModel", "isGet", "config", "update", "closeAction", "updated_at"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;;AAEAC,OAAOC,OAAP,GAAiB,cAAcH,IAAd,CAAmB;;AAElC;;;;AAIMI,kBAAN,GAAyB;AAAA;;AAAA;AACvB,UAAI;AACF,cAAMC,aAAa,MAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAMC,aAAa,MAAKD,KAAL,CAAW,mBAAX,CAAnB;;AAEA;AACA,cAAME,cAAc,MAAMH,WAAWI,KAAX,EAA1B;AACA,cAAMC,eAAe,MAAML,WAAWM,KAAX,CAAiB,EAAEC,QAAQ,QAAV,EAAjB,EAAuCH,KAAvC,EAA3B;AACA,cAAMI,iBAAiB,MAAMR,WAAWM,KAAX,CAAiB,EAAEC,QAAQ,UAAV,EAAjB,EAAyCH,KAAzC,EAA7B;AACA,cAAMK,cAAc,MAAMT,WAAWM,KAAX,CAAiB,EAAEC,QAAQ,OAAV,EAAjB,EAAsCH,KAAtC,EAA1B;;AAEA;AACA,cAAMM,cAAc,MAAMR,WAAWE,KAAX,EAA1B;AACA,cAAMO,QAAQ,IAAIC,IAAJ,GAAWC,WAAX,GAAyBC,KAAzB,CAA+B,CAA/B,EAAkC,EAAlC,CAAd;AACA,cAAMC,cAAc,MAAMb,WAAWI,KAAX,CAAiB;AACzCU,sBAAY,CAAC,IAAD,EAAOL,QAAQ,WAAf;AAD6B,SAAjB,EAEvBP,KAFuB,EAA1B;;AAIA;AACA,cAAMa,aAAa,OAAMf,WAAWgB,GAAX,CAAe,cAAf,CAAN,KAAwC,CAA3D;;AAEA,eAAO,MAAKC,OAAL,CAAa;AAClBhB,qBADkB;AAElBE,sBAFkB;AAGlBG,wBAHkB;AAIlBC,qBAJkB;AAKlBC,qBALkB;AAMlBK,qBANkB;AAOlBE;AAPkB,SAAb,CAAP;AAUD,OA9BD,CA8BE,OAAOG,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,MAAKE,IAAL,CAAU,UAAV,CAAP;AACD;AAlCsB;AAmCxB;;AAED;;;;AAIMC,YAAN,GAAmB;AAAA;;AAAA;AACjB,UAAI;AACFF,gBAAQG,GAAR,CAAY,iBAAZ;AACA,cAAMxB,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;;AAEA;AACA,cAAMwB,SAAS,MAAMzB,WAAW0B,MAAX,EAArB;AACAL,gBAAQG,GAAR,CAAY,SAAZ,EAAuBC,OAAOE,MAA9B;;AAEA,eAAO,OAAKR,OAAL,CAAa;AAClBS,mBAAS,MADS;AAElBxB,iBAAOqB,OAAOE,MAFI;AAGlBE,gBAAMJ;AAHY,SAAb,CAAP;AAKD,OAbD,CAaE,OAAOL,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,UAAd,EAA0BA,KAA1B;AACA,eAAO,OAAKE,IAAL,CAAU,WAAWF,MAAMQ,OAA3B,CAAP;AACD;AAjBgB;AAkBlB;;AAED;;;;AAIME,YAAN,GAAmB;AAAA;;AAAA;AACjB,UAAI;AACFT,gBAAQG,GAAR,CAAY,gBAAZ;AACA,cAAMO,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,cAAMC,QAAQ,OAAKD,GAAL,CAAS,OAAT,KAAqB,EAAnC;AACA,cAAMzB,SAAS,OAAKyB,GAAL,CAAS,QAAT,CAAf;;AAEAX,gBAAQG,GAAR,CAAY,OAAZ,EAAqB,EAAEO,IAAF,EAAQE,KAAR,EAAe1B,MAAf,EAArB;;AAEA,cAAMP,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAMiC,kBAAkB,OAAKjC,KAAL,CAAW,wBAAX,CAAxB;;AAEA,YAAIK,QAAQ,EAAZ;AACA,YAAIC,MAAJ,EAAY;AACVD,gBAAMC,MAAN,GAAeA,MAAf;AACD;;AAED;AACAc,gBAAQG,GAAR,CAAY,OAAZ,EAAqBlB,KAArB;AACA,cAAMmB,SAAS,MAAMzB,WAAWM,KAAX,CAAiBA,KAAjB,EAClB6B,KADkB,CACZ,mBADY,EAElBJ,IAFkB,CAEbA,IAFa,EAEPE,KAFO,EAGlBG,WAHkB,EAArB;;AAKAf,gBAAQG,GAAR,CAAY,WAAZ,EAAyBC,OAAOI,IAAP,GAAcJ,OAAOI,IAAP,CAAYF,MAA1B,GAAmC,CAA5D;AACAN,gBAAQG,GAAR,CAAY,SAAZ,EAAuBC,MAAvB;;AAEA;AACA,YAAIA,OAAOI,IAAP,IAAeJ,OAAOI,IAAP,CAAYF,MAAZ,GAAqB,CAAxC,EAA2C;AACzC,eAAK,IAAIU,KAAT,IAAkBZ,OAAOI,IAAzB,EAA+B;AAC7B,gBAAI;AACFR,sBAAQG,GAAR,CAAY,OAAZ,EAAqBa,MAAMC,EAA3B,EAA+BD,MAAME,UAArC;AACA,oBAAMC,QAAQ,MAAMN,gBAAgBO,iBAAhB,CAAkCJ,MAAMC,EAAxC,CAApB;AACAjB,sBAAQG,GAAR,CAAY,SAAZ,EAAuBgB,MAAMb,MAA7B;;AAEAU,oBAAMK,UAAN,GAAmBF,SAAS,EAA5B;AACAH,oBAAMM,WAAN,GAAoBH,QAAQA,MAAMb,MAAd,GAAuB,CAA3C;;AAEA;AACAU,oBAAMO,WAAN,GAAoBJ,QAAQA,MAAMK,MAAN,CAAa,UAAC3B,GAAD,EAAM4B,CAAN;AAAA,uBAAY5B,OAAO4B,EAAEC,KAAF,IAAW,CAAlB,CAAZ;AAAA,eAAb,EAA+C,CAA/C,CAAR,GAA4D,CAAhF;AACAV,oBAAMW,UAAN,GAAmBR,QAAQA,MAAMK,MAAN,CAAa,UAAC3B,GAAD,EAAM4B,CAAN;AAAA,uBAAY5B,OAAO4B,EAAEG,UAAF,IAAgB,CAAvB,CAAZ;AAAA,eAAb,EAAoD,CAApD,CAAR,GAAiE,CAApF;AACD,aAXD,CAWE,OAAOC,UAAP,EAAmB;AACnB7B,sBAAQD,KAAR,CAAc,SAAd,EAAyBiB,MAAMC,EAA/B,EAAmCY,UAAnC;AACA;AACAb,oBAAMK,UAAN,GAAmB,EAAnB;AACAL,oBAAMM,WAAN,GAAoB,CAApB;AACAN,oBAAMO,WAAN,GAAoB,CAApB;AACAP,oBAAMW,UAAN,GAAmB,CAAnB;AACD;AACF;AACF;;AAED3B,gBAAQG,GAAR,CAAY,YAAZ,EAA0BC,OAAOI,IAAP,CAAYF,MAAtC;AACA,eAAO,OAAKR,OAAL,CAAaM,MAAb,CAAP;AAED,OAtDD,CAsDE,OAAOL,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKE,IAAL,CAAU,UAAV,CAAP;AACD;AA1DgB;AA2DlB;;AAED;;;;AAIM6B,eAAN,GAAsB;AAAA;;AAAA;AACpB,UAAI;AACF,cAAMnD,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAMiC,kBAAkB,OAAKjC,KAAL,CAAW,wBAAX,CAAxB;;AAEA;AACA,cAAMmD,gBAAgB,MAAMpD,WAAWM,KAAX,CAAiB,EAAEC,QAAQ,QAAV,EAAjB,EAAuCmB,MAAvC,EAA5B;;AAEA;AACA,cAAMlB,iBAAiB,MAAMR,WAAWM,KAAX,CAAiB,EAAEC,QAAQ,UAAV,EAAjB,EAC1B4B,KAD0B,CACpB,gBADoB,EAE1BF,KAF0B,CAEpB,CAFoB,EAG1BP,MAH0B,EAA7B;;AAKA;AACA,aAAK,IAAIW,KAAT,IAAkB,CAAC,GAAGe,aAAJ,EAAmB,GAAG5C,cAAtB,CAAlB,EAAyD;AACvD,gBAAMgC,QAAQ,MAAMN,gBAAgBO,iBAAhB,CAAkCJ,MAAMC,EAAxC,CAApB;AACAD,gBAAMK,UAAN,GAAmBF,KAAnB;AACAH,gBAAMM,WAAN,GAAoBH,MAAMb,MAA1B;;AAEA;AACA,gBAAM0B,MAAM,IAAIzC,IAAJ,EAAZ;AACA,gBAAM0C,YAAY,IAAI1C,IAAJ,CAASyB,MAAMkB,UAAf,CAAlB;AACA,gBAAMC,UAAU,IAAI5C,IAAJ,CAASyB,MAAMoB,QAAf,CAAhB;;AAEA,cAAIpB,MAAM9B,MAAN,KAAiB,QAArB,EAA+B;AAC7B8B,kBAAMqB,SAAN,GAAkBC,KAAKC,GAAL,CAAS,CAAT,EAAYD,KAAKE,KAAL,CAAW,CAACL,UAAUH,GAAX,IAAkB,IAA7B,CAAZ,CAAlB;AACD,WAFD,MAEO,IAAIhB,MAAM9B,MAAN,KAAiB,UAArB,EAAiC;AACtC8B,kBAAMqB,SAAN,GAAkBC,KAAKC,GAAL,CAAS,CAAT,EAAYD,KAAKE,KAAL,CAAW,CAACP,YAAYD,GAAb,IAAoB,IAA/B,CAAZ,CAAlB;AACD;AACF;;AAED,eAAO,OAAKlC,OAAL,CAAa;AAClB2C,mBAASV,aADS;AAElBW,oBAAUvD,cAFQ;AAGlBwD,iBAAOZ,cAAczB,MAAd,GAAuBnB,eAAemB;AAH3B,SAAb,CAAP;AAMD,OArCD,CAqCE,OAAOP,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKE,IAAL,CAAU,UAAV,CAAP;AACD;AAzCmB;AA0CrB;;AAED;;;;AAIM2C,cAAN,GAAqB;AAAA;;AAAA;AACnB,UAAI;AACF5C,gBAAQG,GAAR,CAAY,oBAAZ;;AAEA,cAAMK,OAAO,OAAKqC,IAAL,EAAb;AACA7C,gBAAQG,GAAR,CAAY,SAAZ,EAAuB2C,KAAKC,SAAL,CAAevC,IAAf,EAAqB,IAArB,EAA2B,CAA3B,CAAvB;;AAEA;AACA,YAAI,CAACA,KAAKU,UAAV,EAAsB;AACpB,iBAAO,OAAKjB,IAAL,CAAU,SAAV,CAAP;AACD;;AAED,YAAI,CAACO,KAAK0B,UAAV,EAAsB;AACpB,iBAAO,OAAKjC,IAAL,CAAU,SAAV,CAAP;AACD;;AAED,YAAI,CAACO,KAAK4B,QAAV,EAAoB;AAClB,iBAAO,OAAKnC,IAAL,CAAU,SAAV,CAAP;AACD;;AAED,YAAI,CAACO,KAAKa,UAAN,IAAoB,CAAC2B,MAAMC,OAAN,CAAczC,KAAKa,UAAnB,CAArB,IAAuDb,KAAKa,UAAL,CAAgBf,MAAhB,KAA2B,CAAtF,EAAyF;AACvF,iBAAO,OAAKL,IAAL,CAAU,YAAV,CAAP;AACD;;AAED;AACA,cAAMgC,YAAY,IAAI1C,IAAJ,CAASiB,KAAK0B,UAAd,CAAlB;AACA,cAAMC,UAAU,IAAI5C,IAAJ,CAASiB,KAAK4B,QAAd,CAAhB;AACA,cAAMJ,MAAM,IAAIzC,IAAJ,EAAZ;;AAEA,YAAI2D,MAAMjB,UAAUkB,OAAV,EAAN,KAA8BD,MAAMf,QAAQgB,OAAR,EAAN,CAAlC,EAA4D;AAC1D,iBAAO,OAAKlD,IAAL,CAAU,SAAV,CAAP;AACD;;AAED,YAAIgC,aAAaD,GAAjB,EAAsB;AACpB,iBAAO,OAAK/B,IAAL,CAAU,cAAV,CAAP;AACD;;AAED,YAAIkC,WAAWF,SAAf,EAA0B;AACxB,iBAAO,OAAKhC,IAAL,CAAU,cAAV,CAAP;AACD;;AAED,cAAMtB,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;AACA,cAAMiC,kBAAkB,OAAKjC,KAAL,CAAW,wBAAX,CAAxB;AACA,cAAMwE,aAAa,OAAKxE,KAAL,CAAW,OAAX,CAAnB;;AAEA;AACA,aAAK,IAAIyE,IAAI,CAAb,EAAgBA,IAAI7C,KAAKa,UAAL,CAAgBf,MAApC,EAA4C+C,GAA5C,EAAiD;AAC/C,gBAAMlC,QAAQX,KAAKa,UAAL,CAAgBgC,CAAhB,CAAd;AACA,cAAI,CAAClC,MAAMmC,QAAP,IAAmB,CAACnC,MAAMoC,WAA1B,IAAyC,CAACpC,MAAMO,KAApD,EAA2D;AACzD,mBAAO,OAAKzB,IAAL,CAAW,IAAGoD,IAAE,CAAE,UAAlB,CAAP;AACD;AACF;;AAED;AACA;AACA,YAAI,CAAC7C,KAAKgD,eAAV,EAA2B;AACzB,gBAAMC,WAAWjD,KAAKa,UAAL,CAAgBqC,GAAhB,CAAoB;AAAA,mBAAKjC,EAAE6B,QAAP;AAAA,WAApB,CAAjB;AACA,gBAAMK,gBAAgB,MAAM9C,gBAAgB+C,wBAAhB,CAAyCH,QAAzC,CAA5B;AACA,cAAIE,cAAcrD,MAAd,GAAuB,CAA3B,EAA8B;AAC5B,kBAAMuD,gBAAgBF,cAAcD,GAAd,CAAkB;AAAA,qBAAM,QAAOjC,EAAE6B,QAAS,MAAK7B,EAAEqC,YAAa,GAA5C;AAAA,aAAlB,EAAkEC,IAAlE,CAAuE,IAAvE,CAAtB;AACA,mBAAO,OAAK9D,IAAL,CAAW,kBAAiB4D,aAAc,EAA1C,CAAP;AACD;AACF,SAPD,MAOO;AACL;AACA,gBAAMJ,WAAWjD,KAAKa,UAAL,CAAgBqC,GAAhB,CAAoB;AAAA,mBAAKjC,EAAE6B,QAAP;AAAA,WAApB,CAAjB;AACA,gBAAMK,gBAAgB,MAAM9C,gBAAgBmD,6BAAhB,CAA8CP,QAA9C,EAAwDjD,KAAK0B,UAA7D,EAAyE1B,KAAK4B,QAA9E,CAA5B;AACA,cAAIuB,cAAcrD,MAAd,GAAuB,CAA3B,EAA8B;AAC5B,kBAAMuD,gBAAgBF,cAAcD,GAAd,CAAkB;AAAA,qBAAM,QAAOjC,EAAE6B,QAAS,IAAG7B,EAAES,UAAW,IAAGT,EAAEW,QAAS,GAAtD;AAAA,aAAlB,EAA4E2B,IAA5E,CAAiF,IAAjF,CAAtB;AACA,mBAAO,OAAK9D,IAAL,CAAW,iBAAgB4D,aAAc,EAAzC,CAAP;AACD;AACF;;AAED;;AAEA;AACA,cAAMI,YAAY,MAAMtF,WAAWmC,KAAX,CAAiB,mBAAjB,EAAsCoD,IAAtC,EAAxB;AACA,cAAMC,kBAAmBF,aAAaA,UAAUH,YAAxB,GAAwCG,UAAUH,YAAV,GAAyB,CAAjE,GAAqE,CAA7F;;AAEA;AACA,cAAMM,YAAY;AAChBN,wBAAcK,eADE;AAEhBjD,sBAAYV,KAAKU,UAFD;AAGhBgB,sBAAYD,UAAUzC,WAAV,GAAwBC,KAAxB,CAA8B,CAA9B,EAAiC,EAAjC,EAAqC4E,OAArC,CAA6C,GAA7C,EAAkD,GAAlD,CAHI;AAIhBjC,oBAAUD,QAAQ3C,WAAR,GAAsBC,KAAtB,CAA4B,CAA5B,EAA+B,EAA/B,EAAmC4E,OAAnC,CAA2C,GAA3C,EAAgD,GAAhD,CAJM;AAKhBnF,kBAAQ,UALQ;AAMhBoF,sBAAYC,MAAMC,MAAN,IAAgB,CANZ;AAOhBhB,2BAAiBhD,KAAKgD,eAAL,IAAwB,KAPzB;AAQhBiB,sBAAYjE,KAAKiE,UAAL,IAAmB,IARf;AAShBC,uBAAalE,KAAKkE,WAAL,IAAoB;AATjB,SAAlB;;AAYA1E,gBAAQG,GAAR,CAAY,SAAZ,EAAuBiE,SAAvB;AACA,cAAMO,UAAU,MAAMhG,WAAWiG,GAAX,CAAeR,SAAf,CAAtB;AACA,YAAI,CAACO,OAAL,EAAc;AACZ,gBAAM,IAAIE,KAAJ,CAAU,QAAV,CAAN;AACD;AACD7E,gBAAQG,GAAR,CAAY,YAAZ,EAA0BwE,OAA1B;;AAEA;AACA,cAAMG,YAAY,EAAlB;AACA,aAAK,MAAMC,SAAX,IAAwBvE,KAAKa,UAA7B,EAAyC;AACvCrB,kBAAQG,GAAR,CAAY,OAAZ,EAAqB4E,UAAUzB,QAA/B;AACA,gBAAMnC,QAAQ,MAAMiC,WAAWnE,KAAX,CAAiB,EAAEgC,IAAI8D,UAAUzB,QAAhB,EAAjB,EAA6CY,IAA7C,EAApB;AACA,cAAIK,MAAMS,OAAN,CAAc7D,KAAd,CAAJ,EAA0B;AACxB,kBAAM,IAAI0D,KAAJ,CAAW,QAAOE,UAAUzB,QAAS,MAArC,CAAN;AACD;;AAED;AACA,gBAAM2B,gBAAgBC,WAAW/D,MAAMgE,YAAjB,KAAkC,CAAxD;AACA,gBAAMC,aAAaF,WAAWH,UAAUxB,WAArB,KAAqC,CAAxD;AACA,gBAAM8B,eAAeJ,gBAAgB,CAAhB,GAAoB3C,KAAKtB,KAAL,CAAW,CAAC,IAAIoE,aAAaH,aAAlB,IAAmC,GAA9C,CAApB,GAAyE,CAA9F;;AAEAH,oBAAUQ,IAAV,CAAe;AACbhC,sBAAUyB,UAAUzB,QADP;AAEbiC,wBAAYpE,MAAMqE,IAFL;AAGbC,yBAAatE,MAAMuE,YAAN,IAAsB,EAHtB;AAIbC,4BAAgBV,aAJH;AAKb1B,yBAAa6B,UALA;AAMbQ,2BAAeP,YANF;AAOb3D,mBAAOqD,UAAUrD,KAPJ;AAQbmE,4BAAgBd,UAAUc,cAAV,IAA4B;AAR/B,WAAf;AAUD;;AAED7F,gBAAQG,GAAR,CAAY,cAAZ,EAA4B2E,UAAUxE,MAAtC;AACA;AACA,cAAMO,gBAAgBiF,aAAhB,CAA8BnB,OAA9B,EAAuCG,SAAvC,CAAN;;AAEA,cAAMiB,SAAS;AACb9E,cAAI0D,OADS;AAEbb,wBAAcK,eAFD;AAGbjC,sBAAYkC,UAAUlC,UAHT;AAIbE,oBAAUgC,UAAUhC,QAJP;AAKbd,uBAAawD,UAAUxE;AALV,SAAf;;AAQAN,gBAAQG,GAAR,CAAY,WAAZ,EAAyB4F,MAAzB;AACA,eAAO,OAAKjG,OAAL,CAAa;AAClBS,mBAAS,QADS;AAElBC,gBAAMuF;AAFY,SAAb,CAAP;AAKD,OA7ID,CA6IE,OAAOhG,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,eAAO,OAAKE,IAAL,CAAU,WAAWF,MAAMQ,OAA3B,CAAP;AACD;AAjJkB;AAkJpB;;AAED;;;;AAIMyF,aAAN,GAAoB;AAAA;;AAAA;AAClB,UAAI;AACF,cAAM5C,aAAa,OAAKxE,KAAL,CAAW,OAAX,CAAnB;AACA,cAAMqH,eAAe,OAAKrH,KAAL,CAAW,SAAX,CAArB;AACA,cAAMiC,kBAAkB,OAAKjC,KAAL,CAAW,wBAAX,CAAxB;;AAEA;AACA,cAAMsH,WAAW,MAAM9C,WAAWnE,KAAX,CAAiB;AACtCkH,sBAAY,CAD0B;AAEtCC,qBAAW;AAF2B,SAAjB,EAGpBC,KAHoB,CAGd,oDAHc,EAGwChG,MAHxC,EAAvB;;AAKA;AACA,cAAMiG,iBAAiB,MAAMzF,gBAAgB0F,KAAhB,CAAsB,IAAtB,EAC1BxC,IAD0B,CACrB;AACJyC,iBAAO,mBADH;AAEJzC,gBAAM,OAFF;AAGJ0C,cAAI,GAHA;AAIJC,cAAI,CAAC,aAAD,EAAgB,MAAhB;AAJA,SADqB,EAO1BzH,KAP0B,CAOpB;AACL,sBAAY,CAAC,IAAD,EAAO,CAAC,UAAD,EAAa,QAAb,CAAP;AADP,SAPoB,EAU1BoH,KAV0B,CAUpB,aAVoB,EAW1BhG,MAX0B,EAA7B;;AAaA,cAAMsG,YAAYL,eAAe5C,GAAf,CAAmB;AAAA,iBAAQkD,KAAKtD,QAAb;AAAA,SAAnB,CAAlB;;AAEA;AACA,cAAMwB,YAAY,MAAM+B,QAAQC,GAAR,CAAYZ,SAASxC,GAAT;AAAA,uCAAa,WAAOvC,KAAP,EAAiB;AAChE;AACA,kBAAM4F,WAAW,MAAMd,aACpBhH,KADoB,CACd;AACLqE,wBAAUnC,MAAMF,EADX;AAELmF,yBAAW;AAFN,aADc,EAKpBC,KALoB,CAKd,4BALc,EAMpBhG,MANoB,EAAvB;;AAQA,gBAAI2G,aAAa,IAAjB;AACA,gBAAIC,aAAa,CAAjB;;AAEA,gBAAIF,YAAYA,SAASzG,MAAT,GAAkB,CAAlC,EAAqC;AACnC,oBAAM4G,SAASH,SAASrD,GAAT,CAAa;AAAA,uBAAKwB,WAAWiC,EAAEhC,YAAb,CAAL;AAAA,eAAb,EAA8CiC,MAA9C,CAAqD;AAAA,uBAAKD,IAAI,CAAT;AAAA,eAArD,CAAf;AACAF,2BAAaF,SAASvF,MAAT,CAAgB,UAAC3B,GAAD,EAAMsH,CAAN;AAAA,uBAAYtH,OAAOsH,EAAEE,YAAF,IAAkB,CAAzB,CAAZ;AAAA,eAAhB,EAAyD,CAAzD,CAAb;;AAEA,kBAAIH,OAAO5G,MAAP,GAAgB,CAApB,EAAuB;AACrB,sBAAMgH,WAAWhF,KAAKiF,GAAL,CAAS,GAAGL,MAAZ,CAAjB;AACA,sBAAMM,WAAWlF,KAAKC,GAAL,CAAS,GAAG2E,MAAZ,CAAjB;AACAF,6BAAc,IAAGM,SAASG,OAAT,CAAiB,CAAjB,CAAoB,OAAMD,SAASC,OAAT,CAAiB,CAAjB,CAAoB,EAA/D;AACD;AACF;;AAED;AACA,kBAAMC,cAAcT,aAAa,CAAb,GAAiBA,UAAjB,GAA+B9F,MAAMkG,YAAN,IAAsB,CAAzE;AACA,kBAAMM,gBAAgBhB,UAAUiB,QAAV,CAAmBzG,MAAMF,EAAzB,CAAtB;;AAEA,qCACKE,KADL;AAEE0G,2BAAab,UAFf;AAGEc,4BAAcJ,WAHhB;AAIEK,gCAAkBJ,aAJpB;AAKEK,0BAAY,CAACL,aAAD,IAAkBD,cAAc,CAL9C;AAMEO,uBAASP,eAAe,CAAf,GAAmB,MAAnB,GAA6BC,gBAAgB,SAAhB,GAA4B;AANpE;AAQD,WApCmC;;AAAA;AAAA;AAAA;AAAA,aAAZ,CAAxB;;AAsCA,eAAO,OAAK7H,OAAL,CAAagF,SAAb,CAAP;AAED,OApED,CAoEE,OAAO/E,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKE,IAAL,CAAU,UAAV,CAAP;AACD;AAxEiB;AAyEnB;;AAED;;;AAGMiI,cAAN,GAAqB;AAAA;;AAAA;AACnB,UAAI;AACF,cAAMC,cAAc,OAAKvJ,KAAL,CAAW,mBAAX,CAApB;;AAEA,YAAI,OAAKwJ,KAAT,EAAgB;AACd;AACA,gBAAMC,SAAS,MAAMF,YAAYlJ,KAAZ,CAAkB,EAAEgC,IAAI,CAAN,EAAlB,EAA6BiD,IAA7B,EAArB;AACA,iBAAO,OAAKpE,OAAL,CAAauI,UAAU,EAAvB,CAAP;AACD,SAJD,MAIO;AACL;AACA,gBAAM7H,OAAO,OAAKqC,IAAL,EAAb;AACA,gBAAMsF,YAAYlJ,KAAZ,CAAkB,EAAEgC,IAAI,CAAN,EAAlB,EAA6BqH,MAA7B,CAAoC9H,IAApC,CAAN;AACA,iBAAO,OAAKV,OAAL,CAAa,QAAb,CAAP;AACD;AAEF,OAdD,CAcE,OAAOC,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,eAAO,OAAKE,IAAL,CAAU,QAAV,CAAP;AACD;AAlBkB;AAmBpB;;AAED;;;;AAIMsI,aAAN,GAAoB;AAAA;;AAAA;AAClB,UAAI;AACF,cAAM5D,UAAU,OAAK9B,IAAL,CAAU,UAAV,CAAhB;AACA,YAAI,CAAC8B,OAAL,EAAc;AACZ,iBAAO,OAAK1E,IAAL,CAAU,SAAV,CAAP;AACD;;AAEDD,gBAAQG,GAAR,CAAY,gBAAZ;AACAH,gBAAQG,GAAR,CAAY,OAAZ,EAAqBwE,OAArB;;AAEA,cAAMhG,aAAa,OAAKC,KAAL,CAAW,mBAAX,CAAnB;;AAEA;AACA,cAAMoC,QAAQ,MAAMrC,WAAWM,KAAX,CAAiB,EAAEgC,IAAI0D,OAAN,EAAjB,EAAkCT,IAAlC,EAApB;AACA,YAAIK,MAAMS,OAAN,CAAchE,KAAd,CAAJ,EAA0B;AACxB,iBAAO,OAAKf,IAAL,CAAU,OAAV,CAAP;AACD;;AAED;AACA,YAAIe,MAAM9B,MAAN,KAAiB,OAArB,EAA8B;AAC5B,iBAAO,OAAKe,IAAL,CAAU,QAAV,CAAP;AACD;;AAED;AACA,cAAMtB,WAAWM,KAAX,CAAiB,EAAEgC,IAAI0D,OAAN,EAAjB,EAAkC2D,MAAlC,CAAyC;AAC7CpJ,kBAAQ,OADqC;AAE7CsJ,sBAAY,IAAIjJ,IAAJ,GAAWC,WAAX,GAAyBC,KAAzB,CAA+B,CAA/B,EAAkC,EAAlC,EAAsC4E,OAAtC,CAA8C,GAA9C,EAAmD,GAAnD;AAFiC,SAAzC,CAAN;;AAKArE,gBAAQG,GAAR,CAAY,WAAZ,EAAyBwE,OAAzB;AACA,eAAO,OAAK7E,OAAL,CAAa;AAClBS,mBAAS,OADS;AAElBC,gBAAM,EAAES,IAAI0D,OAAN,EAAezF,QAAQ,OAAvB;AAFY,SAAb,CAAP;AAKD,OAlCD,CAkCE,OAAOa,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,eAAO,OAAKE,IAAL,CAAU,WAAWF,MAAMQ,OAA3B,CAAP;AACD;AAtCiB;AAuCnB;;AA9diC,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\flashsalemulti.js", "sourcesContent": ["const Base = require('./base.js');\n\nmodule.exports = class extends Base {\n\n  /**\n   * 获取统计数据\n   * GET /admin/flashsalemulti/statistics\n   */\n  async statisticsAction() {\n    try {\n      const roundModel = this.model('flash_sale_rounds');\n      const orderModel = this.model('flash_sale_orders');\n\n      // 统计轮次数据\n      const totalRounds = await roundModel.count();\n      const activeRounds = await roundModel.where({ status: 'active' }).count();\n      const upcomingRounds = await roundModel.where({ status: 'upcoming' }).count();\n      const endedRounds = await roundModel.where({ status: 'ended' }).count();\n\n      // 统计订单数据\n      const totalOrders = await orderModel.count();\n      const today = new Date().toISOString().slice(0, 10);\n      const todayOrders = await orderModel.where({\n        created_at: ['>=', today + ' 00:00:00']\n      }).count();\n\n      // 统计销售额\n      const totalSales = await orderModel.sum('total_amount') || 0;\n\n      return this.success({\n        totalRounds,\n        activeRounds,\n        upcomingRounds,\n        endedRounds,\n        totalOrders,\n        todayOrders,\n        totalSales\n      });\n\n    } catch (error) {\n      console.error('获取统计数据失败:', error);\n      return this.fail('获取统计数据失败');\n    }\n  }\n\n  /**\n   * 测试API - 简单查询轮次\n   * GET /admin/flashsalemulti/test\n   */\n  async testAction() {\n    try {\n      console.log('=== 测试API调用 ===');\n      const roundModel = this.model('flash_sale_rounds');\n\n      // 简单查询所有轮次\n      const rounds = await roundModel.select();\n      console.log('测试查询结果:', rounds.length);\n\n      return this.success({\n        message: '测试成功',\n        count: rounds.length,\n        data: rounds\n      });\n    } catch (error) {\n      console.error('测试API失败:', error);\n      return this.fail('测试失败: ' + error.message);\n    }\n  }\n\n  /**\n   * 获取轮次列表\n   * GET /admin/flashsalemulti/list\n   */\n  async listAction() {\n    try {\n      console.log('=== 获取轮次列表 ===');\n      const page = this.get('page') || 1;\n      const limit = this.get('limit') || 20;\n      const status = this.get('status');\n\n      console.log('查询参数:', { page, limit, status });\n\n      const roundModel = this.model('flash_sale_rounds');\n      const roundGoodsModel = this.model('flash_sale_round_goods');\n\n      let where = {};\n      if (status) {\n        where.status = status;\n      }\n\n      // 获取轮次列表\n      console.log('查询条件:', where);\n      const rounds = await roundModel.where(where)\n        .order('round_number DESC')\n        .page(page, limit)\n        .countSelect();\n\n      console.log('查询到的轮次数量:', rounds.data ? rounds.data.length : 0);\n      console.log('轮次数据结构:', rounds);\n\n      // 为每个轮次获取商品信息\n      if (rounds.data && rounds.data.length > 0) {\n        for (let round of rounds.data) {\n          try {\n            console.log('处理轮次:', round.id, round.round_name);\n            const goods = await roundGoodsModel.getRoundGoodsList(round.id);\n            console.log('轮次商品数量:', goods.length);\n\n            round.goods_list = goods || [];\n            round.goods_count = goods ? goods.length : 0;\n\n            // 计算总库存和总销量\n            round.total_stock = goods ? goods.reduce((sum, g) => sum + (g.stock || 0), 0) : 0;\n            round.total_sold = goods ? goods.reduce((sum, g) => sum + (g.sold_count || 0), 0) : 0;\n          } catch (roundError) {\n            console.error('处理轮次失败:', round.id, roundError);\n            // 即使单个轮次处理失败，也继续处理其他轮次\n            round.goods_list = [];\n            round.goods_count = 0;\n            round.total_stock = 0;\n            round.total_sold = 0;\n          }\n        }\n      }\n\n      console.log('返回轮次列表，总数:', rounds.data.length);\n      return this.success(rounds);\n\n    } catch (error) {\n      console.error('获取轮次列表失败:', error);\n      return this.fail('获取轮次列表失败');\n    }\n  }\n\n  /**\n   * 获取当前轮次\n   * GET /admin/flashsalemulti/current\n   */\n  async currentAction() {\n    try {\n      const roundModel = this.model('flash_sale_rounds');\n      const roundGoodsModel = this.model('flash_sale_round_goods');\n\n      // 获取当前活跃轮次\n      const currentRounds = await roundModel.where({ status: 'active' }).select();\n      \n      // 获取即将开始的轮次\n      const upcomingRounds = await roundModel.where({ status: 'upcoming' })\n        .order('start_time ASC')\n        .limit(5)\n        .select();\n\n      // 为每个轮次添加商品信息\n      for (let round of [...currentRounds, ...upcomingRounds]) {\n        const goods = await roundGoodsModel.getRoundGoodsList(round.id);\n        round.goods_list = goods;\n        round.goods_count = goods.length;\n        \n        // 计算倒计时\n        const now = new Date();\n        const startTime = new Date(round.start_time);\n        const endTime = new Date(round.end_time);\n        \n        if (round.status === 'active') {\n          round.countdown = Math.max(0, Math.floor((endTime - now) / 1000));\n        } else if (round.status === 'upcoming') {\n          round.countdown = Math.max(0, Math.floor((startTime - now) / 1000));\n        }\n      }\n\n      return this.success({\n        current: currentRounds,\n        upcoming: upcomingRounds,\n        total: currentRounds.length + upcomingRounds.length\n      });\n\n    } catch (error) {\n      console.error('获取当前轮次失败:', error);\n      return this.fail('获取当前轮次失败');\n    }\n  }\n\n  /**\n   * 创建新轮次（支持多商品）\n   * POST /admin/flashsalemulti/create\n   */\n  async createAction() {\n    try {\n      console.log('=== 创建新轮次（多商品） ===');\n\n      const data = this.post();\n      console.log('接收到的数据:', JSON.stringify(data, null, 2));\n\n      // 验证必填字段\n      if (!data.round_name) {\n        return this.fail('请填写轮次名称');\n      }\n\n      if (!data.start_time) {\n        return this.fail('请选择开始时间');\n      }\n\n      if (!data.end_time) {\n        return this.fail('请选择结束时间');\n      }\n\n      if (!data.goods_list || !Array.isArray(data.goods_list) || data.goods_list.length === 0) {\n        return this.fail('请选择参与秒杀的商品');\n      }\n\n      // 验证时间\n      const startTime = new Date(data.start_time);\n      const endTime = new Date(data.end_time);\n      const now = new Date();\n\n      if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {\n        return this.fail('时间格式不正确');\n      }\n\n      if (startTime <= now) {\n        return this.fail('开始时间必须晚于当前时间');\n      }\n\n      if (endTime <= startTime) {\n        return this.fail('结束时间必须晚于开始时间');\n      }\n\n      const roundModel = this.model('flash_sale_rounds');\n      const roundGoodsModel = this.model('flash_sale_round_goods');\n      const goodsModel = this.model('goods');\n      \n      // 验证商品数据\n      for (let i = 0; i < data.goods_list.length; i++) {\n        const goods = data.goods_list[i];\n        if (!goods.goods_id || !goods.flash_price || !goods.stock) {\n          return this.fail(`第${i+1}个商品信息不完整`);\n        }\n      }\n\n      // 检查商品是否已参与其他活跃轮次\n      // 对于整点秒杀，允许商品在不同时段重复使用\n      if (!data.is_hourly_flash) {\n        const goodsIds = data.goods_list.map(g => g.goods_id);\n        const conflictGoods = await roundGoodsModel.checkGoodsInActiveRounds(goodsIds);\n        if (conflictGoods.length > 0) {\n          const conflictNames = conflictGoods.map(g => `商品ID:${g.goods_id}(轮次${g.round_number})`).join(', ');\n          return this.fail(`以下商品已参与其他活跃轮次: ${conflictNames}`);\n        }\n      } else {\n        // 整点秒杀：只检查时间重叠的轮次\n        const goodsIds = data.goods_list.map(g => g.goods_id);\n        const conflictGoods = await roundGoodsModel.checkGoodsInOverlappingRounds(goodsIds, data.start_time, data.end_time);\n        if (conflictGoods.length > 0) {\n          const conflictNames = conflictGoods.map(g => `商品ID:${g.goods_id}(${g.start_time}-${g.end_time})`).join(', ');\n          return this.fail(`以下商品在该时段已有冲突: ${conflictNames}`);\n        }\n      }\n\n      // 移除系统配置依赖，直接使用用户提供的时间\n\n      // 获取下一个轮次编号\n      const lastRound = await roundModel.order('round_number DESC').find();\n      const nextRoundNumber = (lastRound && lastRound.round_number) ? lastRound.round_number + 1 : 1;\n\n      // 创建轮次（暂时去掉事务处理）\n      const roundData = {\n        round_number: nextRoundNumber,\n        round_name: data.round_name,\n        start_time: startTime.toISOString().slice(0, 19).replace('T', ' '),\n        end_time: endTime.toISOString().slice(0, 19).replace('T', ' '),\n        status: 'upcoming',\n        created_by: think.userId || 0,\n        is_hourly_flash: data.is_hourly_flash || false,\n        slot_index: data.slot_index || null,\n        total_slots: data.total_slots || null\n      };\n\n      console.log('准备创建轮次:', roundData);\n      const roundId = await roundModel.add(roundData);\n      if (!roundId) {\n        throw new Error('创建轮次失败');\n      }\n      console.log('轮次创建成功，ID:', roundId);\n\n      // 获取商品详细信息并创建轮次商品\n      const goodsList = [];\n      for (const goodsItem of data.goods_list) {\n        console.log('处理商品:', goodsItem.goods_id);\n        const goods = await goodsModel.where({ id: goodsItem.goods_id }).find();\n        if (think.isEmpty(goods)) {\n          throw new Error(`商品ID ${goodsItem.goods_id} 不存在`);\n        }\n\n        // 计算折扣率\n        const originalPrice = parseFloat(goods.retail_price) || 0;\n        const flashPrice = parseFloat(goodsItem.flash_price) || 0;\n        const discountRate = originalPrice > 0 ? Math.round((1 - flashPrice / originalPrice) * 100) : 0;\n\n        goodsList.push({\n          goods_id: goodsItem.goods_id,\n          goods_name: goods.name,\n          goods_image: goods.list_pic_url || '',\n          original_price: originalPrice,\n          flash_price: flashPrice,\n          discount_rate: discountRate,\n          stock: goodsItem.stock,\n          limit_quantity: goodsItem.limit_quantity || 1\n        });\n      }\n\n      console.log('准备添加轮次商品，数量:', goodsList.length);\n      // 批量添加轮次商品\n      await roundGoodsModel.addRoundGoods(roundId, goodsList);\n\n      const result = {\n        id: roundId,\n        round_number: nextRoundNumber,\n        start_time: roundData.start_time,\n        end_time: roundData.end_time,\n        goods_count: goodsList.length\n      };\n\n      console.log('准备返回成功响应:', result);\n      return this.success({\n        message: '轮次创建成功',\n        data: result\n      });\n\n    } catch (error) {\n      console.error('创建轮次失败:', error);\n      return this.fail('创建失败: ' + error.message);\n    }\n  }\n\n  /**\n   * 获取可选商品列表（排除已参与活跃轮次的商品）\n   * GET /admin/flashsalemulti/goods\n   */\n  async goodsAction() {\n    try {\n      const goodsModel = this.model('goods');\n      const productModel = this.model('product');\n      const roundGoodsModel = this.model('flash_sale_round_goods');\n\n      // 获取所有上架商品\n      const allGoods = await goodsModel.where({\n        is_on_sale: 1,\n        is_delete: 0\n      }).field('id, name, retail_price, list_pic_url, goods_number').select();\n\n      // 获取已参与活跃轮次的商品ID\n      const activeGoodsIds = await roundGoodsModel.alias('rg')\n        .join({\n          table: 'flash_sale_rounds',\n          join: 'inner',\n          as: 'r',\n          on: ['rg.round_id', 'r.id']\n        })\n        .where({\n          'r.status': ['IN', ['upcoming', 'active']]\n        })\n        .field('rg.goods_id')\n        .select();\n\n      const activeIds = activeGoodsIds.map(item => item.goods_id);\n\n      // 为每个商品获取规格信息和价格区间\n      const goodsList = await Promise.all(allGoods.map(async (goods) => {\n        // 获取商品的所有规格产品\n        const products = await productModel\n          .where({\n            goods_id: goods.id,\n            is_delete: 0\n          })\n          .field('retail_price, goods_number')\n          .select();\n\n        let priceRange = null;\n        let totalStock = 0;\n\n        if (products && products.length > 0) {\n          const prices = products.map(p => parseFloat(p.retail_price)).filter(p => p > 0);\n          totalStock = products.reduce((sum, p) => sum + (p.goods_number || 0), 0);\n\n          if (prices.length > 1) {\n            const minPrice = Math.min(...prices);\n            const maxPrice = Math.max(...prices);\n            priceRange = `¥${minPrice.toFixed(2)} - ¥${maxPrice.toFixed(2)}`;\n          }\n        }\n\n        // 使用实际库存或商品表中的库存\n        const actualStock = totalStock > 0 ? totalStock : (goods.goods_number || 0);\n        const isInFlashSale = activeIds.includes(goods.id);\n\n        return {\n          ...goods,\n          price_range: priceRange,\n          actual_stock: actualStock,\n          is_in_flash_sale: isInFlashSale,\n          can_select: !isInFlashSale && actualStock > 0,\n          warning: actualStock <= 0 ? '库存不足' : (isInFlashSale ? '已参与其他轮次' : null)\n        };\n      }));\n\n      return this.success(goodsList);\n\n    } catch (error) {\n      console.error('获取商品列表失败:', error);\n      return this.fail('获取商品列表失败');\n    }\n  }\n\n  /**\n   * 获取/保存系统配置\n   */\n  async configAction() {\n    try {\n      const configModel = this.model('flash_sale_config');\n\n      if (this.isGet) {\n        // 获取配置\n        const config = await configModel.where({ id: 1 }).find();\n        return this.success(config || {});\n      } else {\n        // 保存配置\n        const data = this.post();\n        await configModel.where({ id: 1 }).update(data);\n        return this.success('配置保存成功');\n      }\n\n    } catch (error) {\n      console.error('配置操作失败:', error);\n      return this.fail('配置操作失败');\n    }\n  }\n\n  /**\n   * 手动关闭轮次\n   * POST /admin/flashsalemulti/close\n   */\n  async closeAction() {\n    try {\n      const roundId = this.post('round_id');\n      if (!roundId) {\n        return this.fail('请提供轮次ID');\n      }\n\n      console.log('=== 手动关闭轮次 ===');\n      console.log('轮次ID:', roundId);\n\n      const roundModel = this.model('flash_sale_rounds');\n\n      // 检查轮次是否存在\n      const round = await roundModel.where({ id: roundId }).find();\n      if (think.isEmpty(round)) {\n        return this.fail('轮次不存在');\n      }\n\n      // 检查轮次状态\n      if (round.status === 'ended') {\n        return this.fail('轮次已经结束');\n      }\n\n      // 更新轮次状态为已结束\n      await roundModel.where({ id: roundId }).update({\n        status: 'ended',\n        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' ')\n      });\n\n      console.log('轮次手动关闭成功:', roundId);\n      return this.success({\n        message: '轮次已关闭',\n        data: { id: roundId, status: 'ended' }\n      });\n\n    } catch (error) {\n      console.error('关闭轮次失败:', error);\n      return this.fail('关闭失败: ' + error.message);\n    }\n  }\n\n\n};\n"]}