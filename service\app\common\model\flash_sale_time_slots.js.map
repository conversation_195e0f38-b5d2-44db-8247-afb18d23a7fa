{"version": 3, "sources": ["..\\..\\..\\src\\common\\model\\flash_sale_time_slots.js"], "names": ["module", "exports", "think", "Model", "tableName", "getActiveTimeSlots", "where", "is_active", "order", "select", "getCurrentTimeSlot", "now", "Date", "currentTime", "toTimeString", "substring", "start_time", "end_time", "find", "getNextTimeSlot"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;;AAEzC;;;AAGA,MAAIC,SAAJ,GAAgB;AACd,WAAO,+BAAP;AACD;;AAED;;;AAGMC,oBAAN,GAA2B;AAAA;;AAAA;AACzB,aAAO,MAAM,MAAKC,KAAL,CAAW;AACtBC,mBAAW;AADW,OAAX,EAEVC,KAFU,CAEJ,gBAFI,EAEcC,MAFd,EAAb;AADyB;AAI1B;;AAED;;;AAGMC,oBAAN,GAA2B;AAAA;;AAAA;AACzB,YAAMC,MAAM,IAAIC,IAAJ,EAAZ;AACA,YAAMC,cAAcF,IAAIG,YAAJ,GAAmBC,SAAnB,CAA6B,CAA7B,EAAgC,CAAhC,CAApB,CAFyB,CAE+B;;AAExD,aAAO,MAAM,OAAKT,KAAL,CAAW;AACtBC,mBAAW,CADW;AAEtBS,oBAAY,CAAC,IAAD,EAAOH,WAAP,CAFU;AAGtBI,kBAAU,CAAC,IAAD,EAAOJ,WAAP;AAHY,OAAX,EAIVK,IAJU,EAAb;AAJyB;AAS1B;;AAED;;;AAGMC,iBAAN,GAAwB;AAAA;;AAAA;AACtB,YAAMR,MAAM,IAAIC,IAAJ,EAAZ;AACA,YAAMC,cAAcF,IAAIG,YAAJ,GAAmBC,SAAnB,CAA6B,CAA7B,EAAgC,CAAhC,CAApB;;AAEA,aAAO,MAAM,OAAKT,KAAL,CAAW;AACtBC,mBAAW,CADW;AAEtBS,oBAAY,CAAC,GAAD,EAAMH,WAAN;AAFU,OAAX,EAGVL,KAHU,CAGJ,gBAHI,EAGcU,IAHd,EAAb;AAJsB;AAQvB;AA3CwC,CAA3C", "file": "..\\..\\..\\src\\common\\model\\flash_sale_time_slots.js", "sourcesContent": ["module.exports = class extends think.Model {\n  \n  /**\n   * 获取表名\n   */\n  get tableName() {\n    return 'hiolabs_flash_sale_time_slots';\n  }\n  \n  /**\n   * 获取所有启用的时段\n   */\n  async getActiveTimeSlots() {\n    return await this.where({\n      is_active: 1\n    }).order('sort_order ASC').select();\n  }\n  \n  /**\n   * 根据当前时间获取对应的时段\n   */\n  async getCurrentTimeSlot() {\n    const now = new Date();\n    const currentTime = now.toTimeString().substring(0, 8); // HH:MM:SS格式\n    \n    return await this.where({\n      is_active: 1,\n      start_time: ['<=', currentTime],\n      end_time: ['>=', currentTime]\n    }).find();\n  }\n  \n  /**\n   * 获取下一个时段\n   */\n  async getNextTimeSlot() {\n    const now = new Date();\n    const currentTime = now.toTimeString().substring(0, 8);\n    \n    return await this.where({\n      is_active: 1,\n      start_time: ['>', currentTime]\n    }).order('start_time ASC').find();\n  }\n};\n"]}