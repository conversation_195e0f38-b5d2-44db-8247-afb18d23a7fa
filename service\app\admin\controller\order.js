function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

const Base = require('./base.js');
const moment = require('moment');
const _ = require('lodash');
// const Jushuitan = require('jushuitan');
const WangDianSync = require('../../common/wangdian_sync.js');
module.exports = class extends Base {
    /**
     * index action
     * @return {Promise} []
     */
    indexAction() {
        var _this = this;

        return _asyncToGenerator(function* () {
            const page = _this.get('page') || 1;
            const size = _this.get('size') || 10;
            const orderSn = _this.get('orderSn') || '';
            const consignee = _this.get('consignee') || '';
            const logistic_code = _this.get('logistic_code') || '';
            const status = _this.get('status') || '';
            let data = {};
            const model = _this.model('order');
            if (logistic_code == '') {
                let whereCondition = {
                    order_sn: ['like', `%${orderSn}%`],
                    consignee: ['like', `%${consignee}%`],
                    order_status: ['IN', status],
                    order_type: ['<', 7]
                };

                // 特定状态下排除有售后申请的订单（保留全部订单和已关闭的显示）
                const excludeRefundStatuses = ['300', '301', '401']; // 待发货、待收货、已收货
                const shouldExcludeRefund = excludeRefundStatuses.some(function (s) {
                    return status.includes(s);
                });

                if (shouldExcludeRefund) {
                    // 获取有售后申请的订单ID列表
                    const refundOrderIds = yield _this.model('refund_apply').field('order_id').select();
                    const excludeOrderIds = refundOrderIds.map(function (item) {
                        return item.order_id;
                    });

                    if (excludeOrderIds.length > 0) {
                        whereCondition.id = ['NOT IN', excludeOrderIds];
                    }
                }

                data = yield model.where(whereCondition).order(['id DESC']).page(page, size).countSelect();
                console.log(data);
            } else {
                let orderData = yield _this.model('order_express').where({
                    logistic_code: logistic_code
                }).find();
                let order_id = orderData.order_id;
                data = yield model.where({
                    id: order_id
                }).order(['id DESC']).page(page, size).countSelect();
            }
            for (const item of data.data) {
                item.goodsList = yield _this.model('order_goods').field('goods_name,goods_aka,list_pic_url,number,goods_specifition_name_value,retail_price').where({
                    order_id: item.id,
                    is_delete: 0
                }).select();
                item.goodsCount = 0;
                item.goodsList.forEach(function (v) {
                    item.goodsCount += v.number;
                });
                let user = yield _this.model('user').where({
                    id: item.user_id
                }).field('nickname,name,mobile,avatar').find();
                if (!think.isEmpty(user)) {
                    // 确保nickname存在且不为空再进行base64解码
                    if (user.nickname) {
                        try {
                            user.nickname = Buffer.from(user.nickname, 'base64').toString();
                        } catch (e) {
                            console.log('用户昵称base64解码失败:', e.message);
                            user.nickname = '解码失败';
                        }
                    } else {
                        user.nickname = '未设置昵称';
                    }
                } else {
                    user = { nickname: '已删除', name: '', mobile: '', avatar: '' };
                }
                item.userInfo = user;
                let province_name = yield _this.model('region').where({
                    id: item.province
                }).getField('name', true);
                let city_name = yield _this.model('region').where({
                    id: item.city
                }).getField('name', true);
                let district_name = yield _this.model('region').where({
                    id: item.district
                }).getField('name', true);
                item.full_region = province_name + city_name + district_name;
                item.postscript = Buffer.from(item.postscript, 'base64').toString();
                item.add_time = moment.unix(item.add_time).format('YYYY-MM-DD HH:mm:ss');
                if (item.pay_time != 0) {
                    item.pay_time = moment.unix(item.pay_time).format('YYYY-MM-DD HH:mm:ss');
                } else {
                    item.pay_time = 0;
                }
                item.order_status_text = yield _this.model('order').getOrderStatusText(item.id);
                let express = yield _this.model('order_express').where({
                    order_id: item.id
                }).find();
                if (!think.isEmpty(express)) {
                    item.expressInfo = express.shipper_name + express.logistic_code;
                } else {
                    item.expressInfo = '';
                }

                // 获取售后申请信息
                let refundApply = yield _this.model('refund_apply').where({
                    order_id: item.id
                }).find();

                if (!think.isEmpty(refundApply)) {
                    item.refundInfo = {
                        id: refundApply.id,
                        status: refundApply.status,
                        refund_amount: refundApply.refund_amount,
                        refund_reason: refundApply.refund_reason,
                        apply_time: refundApply.apply_time,
                        status_text: _this.getRefundStatusText(refundApply.status)
                    };
                    item.hasRefund = true;
                } else {
                    item.refundInfo = null;
                    item.hasRefund = false;
                }

                // item.button_text = await this.model('order').getOrderBtnText(item.id);
            }
            return _this.success(data);
        })();
    }

    /**
     * 获取售后状态文本
     */
    getRefundStatusText(status) {
        const statusMap = {
            'pending': '待处理',
            'processing': '处理中',
            'approved': '已同意',
            'rejected': '已拒绝',
            'wait_return': '等待退货',
            'returned': '已退货',
            'completed': '已完成'
        };
        return statusMap[status] || '未知状态';
    }

    /**
     * 获取售后订单列表
     */
    refundListAction() {
        var _this2 = this;

        return _asyncToGenerator(function* () {
            const page = _this2.get('page') || 1;
            const size = _this2.get('size') || 10;
            const orderSn = _this2.get('orderSn') || '';
            const refundStatus = _this2.get('refundStatus') || '';
            const consignee = _this2.get('consignee') || '';
            const refundType = _this2.get('refundType') || ''; // 退款类型筛选
            const processStatus = _this2.get('processStatus') || ''; // 处理状态筛选：pending,processing为待处理，approved,completed为已处理

            try {
                console.log('=== 获取售后订单列表 ===');
                console.log('页码:', page, '大小:', size, '订单号:', orderSn, '售后状态:', refundStatus);

                // 构建查询条件
                let whereCondition = {};
                if (orderSn) {
                    whereCondition.order_sn = ['like', `%${orderSn}%`];
                }
                if (refundStatus) {
                    whereCondition.status = refundStatus;
                }
                if (refundType) {
                    whereCondition.refund_type = refundType;
                }

                // 处理状态筛选
                if (processStatus === 'pending') {
                    whereCondition.status = ['IN', ['pending', 'processing']];
                } else if (processStatus === 'processed') {
                    whereCondition.status = ['IN', ['rejected', 'completed', 'approved']];
                } else {
                    // 默认情况下，排除已完成、已拒绝和已同意的售后申请
                    whereCondition.status = ['NOT IN', ['completed', 'rejected', 'approved']];
                }

                // 查询售后申请，关联订单信息
                const refundModel = _this2.model('refund_apply');
                const data = yield refundModel.alias('ra').join({
                    table: 'order',
                    join: 'left',
                    as: 'o',
                    on: ['ra.order_id', 'o.id']
                }).where(whereCondition).where({
                    'o.consignee': ['like', `%${consignee}%`]
                }).field('ra.*, o.order_sn, o.consignee, o.mobile, o.actual_price, o.add_time as order_add_time, o.user_id').order(['ra.id DESC']).page(page, size).countSelect();

                // 处理每个售后申请的详细信息
                for (const item of data.data) {
                    // 获取用户信息
                    let user = yield _this2.model('user').where({
                        id: item.user_id
                    }).field('nickname,name,mobile,avatar').find();

                    if (!think.isEmpty(user)) {
                        // 确保nickname存在且不为空再进行base64解码
                        if (user.nickname) {
                            try {
                                user.nickname = Buffer.from(user.nickname, 'base64').toString();
                            } catch (e) {
                                console.log('用户昵称base64解码失败:', e.message);
                                user.nickname = '解码失败';
                            }
                        } else {
                            user.nickname = '未设置昵称';
                        }
                    } else {
                        user = { nickname: '已删除', name: '', mobile: '', avatar: '' };
                    }
                    item.userInfo = user;

                    // 获取订单商品信息
                    item.goodsList = yield _this2.model('order_goods').field('goods_name,goods_aka,list_pic_url,number,goods_specifition_name_value,retail_price').where({
                        order_id: item.order_id,
                        is_delete: 0
                    }).select();

                    // 处理图片字段
                    if (item.images) {
                        try {
                            item.images = JSON.parse(item.images);
                        } catch (e) {
                            item.images = [];
                        }
                    } else {
                        item.images = [];
                    }

                    // 格式化时间
                    item.apply_time_text = moment.unix(item.apply_time).format('YYYY-MM-DD HH:mm:ss');
                    item.order_add_time_text = moment.unix(item.order_add_time).format('YYYY-MM-DD HH:mm:ss');

                    // 添加状态文本
                    item.status_text = _this2.getRefundStatusText(item.status);
                }

                console.log('✅ 售后订单列表获取成功，共', data.count, '条记录');
                return _this2.success(data);
            } catch (error) {
                console.error('获取售后订单列表失败:', error);
                return _this2.fail('获取售后订单列表失败: ' + error.message);
            }
        })();
    }

    /**
     * 处理售后申请
     */
    handleRefundAction() {
        var _this3 = this;

        return _asyncToGenerator(function* () {
            const applyId = _this3.post('applyId');
            const action = _this3.post('action'); // approve, reject, complete
            const adminMemo = _this3.post('adminMemo') || '';
            const rejectReason = _this3.post('rejectReason') || '';

            try {
                console.log('=== 处理售后申请 ===');
                console.log('申请ID:', applyId, '操作:', action);

                // 获取售后申请信息
                const refundApply = yield _this3.model('refund_apply').where({
                    id: applyId
                }).find();

                if (think.isEmpty(refundApply)) {
                    return _this3.fail('售后申请不存在');
                }

                // 检查当前状态是否允许操作
                if (refundApply.status === 'completed') {
                    return _this3.fail('售后已完成，无法再次操作');
                }

                const currentTime = parseInt(new Date().getTime() / 1000);
                let updateData = {
                    process_time: currentTime,
                    updated_at: new Date()
                };

                switch (action) {
                    case 'approve':
                        if (refundApply.status !== 'pending') {
                            return _this3.fail('只有待处理状态的申请可以同意');
                        }
                        updateData.admin_memo = adminMemo;

                        // 根据退款类型采用不同处理流程
                        if (refundApply.refund_type === 'refund_only') {
                            // 仅退款：需要调用微信退款API
                            console.log('=== 开始处理仅退款申请 ===');

                            // 获取订单信息
                            const orderInfo = yield _this3.model('order').where({ id: refundApply.order_id }).find();
                            if (think.isEmpty(orderInfo)) {
                                return _this3.fail('订单信息不存在');
                            }

                            // 检查订单是否已支付
                            if (orderInfo.pay_status !== 2) {
                                return _this3.fail('订单未支付，无法退款');
                            }

                            // 调用微信退款API
                            try {
                                console.log('=== 调用微信退款API ===');
                                const WeixinService = _this3.service('weixin', 'api');

                                // 生成退款单号
                                const refundNo = 'RF' + Date.now() + Math.random().toString(36).substr(2, 4).toUpperCase();

                                const refundParams = {
                                    out_trade_no: orderInfo.order_sn, // 原订单号
                                    out_refund_no: refundNo, // 退款单号
                                    total_fee: parseInt(orderInfo.actual_price * 100), // 原订单金额（分）
                                    refund_fee: parseInt(refundApply.refund_amount * 100), // 退款金额（分）
                                    refund_desc: `售后退款：${refundApply.refund_reason}`
                                };

                                console.log('微信退款参数:', refundParams);
                                const refundResult = yield WeixinService.createRefund(refundParams);

                                if (refundResult.success) {
                                    console.log('✅ 微信退款成功:', refundResult);

                                    // 退款成功，更新状态
                                    updateData.status = 'completed';
                                    updateData.complete_time = currentTime;
                                    updateData.admin_memo = `${adminMemo} [微信退款成功，退款单号：${refundResult.out_refund_no}]`;

                                    // 将订单状态更新为售后完成状态
                                    yield _this3.model('order').where({
                                        id: refundApply.order_id
                                    }).update({
                                        order_status: 203, // 售后完成状态
                                        refund_time: currentTime,
                                        updated_at: new Date()
                                    });

                                    // 处理佣金扣除
                                    try {
                                        const commissionService = _this3.service('commission', 'common');
                                        yield commissionService.handleRefundCommission(refundApply.order_id);
                                        console.log('✅ 佣金扣除处理完成');
                                    } catch (commissionError) {
                                        console.error('❌ 佣金扣除处理失败:', commissionError);
                                        // 不影响主流程，继续执行
                                    }
                                } else {
                                    console.error('❌ 微信退款失败:', refundResult);
                                    return _this3.fail(`微信退款失败：${refundResult.error_msg || '未知错误'}`);
                                }
                            } catch (refundError) {
                                console.error('❌ 调用微信退款API异常:', refundError);
                                return _this3.fail(`退款处理失败：${refundError.error_msg || refundError.message || '系统异常'}`);
                            }

                            // 推送退款状态到旺店通ERP
                            try {
                                console.log(`[旺店通同步] 仅退款申请已同意，开始推送退款状态到旺店通: ${refundApply.order_sn}`);

                                // 获取商品信息
                                const orderGoods = yield _this3.model('order_goods').where({ order_id: refundApply.order_id }).select();

                                if (!think.isEmpty(orderGoods)) {
                                    const wangdianSync = new WangDianSync(_this3);
                                    const result = yield wangdianSync.pushOrderRefund(orderInfo, orderGoods);

                                    if (result.success) {
                                        console.log(`[旺店通同步] 退款状态推送成功: ${refundApply.order_sn}`);
                                    } else {
                                        console.error(`[旺店通同步] 退款状态推送失败: ${refundApply.order_sn}, 错误: ${result.message}`);
                                    }
                                } else {
                                    console.error(`[旺店通同步] 获取商品信息失败，无法推送退款状态: ${refundApply.order_sn}`);
                                }
                            } catch (error) {
                                console.error('[旺店通同步] 推送退款状态时发生异常:', error);
                                // 不抛出异常，避免影响主要的退款流程
                            }
                        } else if (refundApply.refund_type === 'return_refund') {
                            // 退货退款：等待用户退货
                            updateData.status = 'wait_return';
                            // 设置退货地址信息
                            updateData.return_address = adminMemo || '请联系客服获取退货地址';
                            updateData.return_contact = '客服中心';
                            updateData.return_phone = '************';
                        }
                        break;

                    case 'reject':
                        if (refundApply.status !== 'pending') {
                            return _this3.fail('只有待处理状态的申请可以拒绝');
                        }
                        updateData.status = 'rejected';
                        updateData.reject_reason = rejectReason;
                        break;

                    case 'confirm_return':
                        if (refundApply.status !== 'returned') {
                            return _this3.fail('只有已退货状态的申请可以确认收货');
                        }

                        // 确认收货后，需要调用微信退款API
                        console.log('=== 开始处理退货退款确认收货 ===');

                        // 获取订单信息
                        const orderInfo = yield _this3.model('order').where({ id: refundApply.order_id }).find();
                        if (think.isEmpty(orderInfo)) {
                            return _this3.fail('订单信息不存在');
                        }

                        // 检查订单是否已支付
                        if (orderInfo.pay_status !== 2) {
                            return _this3.fail('订单未支付，无法退款');
                        }

                        // 调用微信退款API
                        try {
                            console.log('=== 调用微信退款API（退货退款） ===');
                            const WeixinService = _this3.service('weixin', 'api');

                            // 生成退款单号
                            const refundNo = 'RF' + Date.now() + Math.random().toString(36).substr(2, 4).toUpperCase();

                            const refundParams = {
                                out_trade_no: orderInfo.order_sn, // 原订单号
                                out_refund_no: refundNo, // 退款单号
                                total_fee: parseInt(orderInfo.actual_price * 100), // 原订单金额（分）
                                refund_fee: parseInt(refundApply.refund_amount * 100), // 退款金额（分）
                                refund_desc: `退货退款：${refundApply.refund_reason}`
                            };

                            console.log('微信退款参数:', refundParams);
                            const refundResult = yield WeixinService.createRefund(refundParams);

                            if (refundResult.success) {
                                console.log('✅ 微信退款成功:', refundResult);

                                // 退款成功，更新状态
                                updateData.status = 'completed';
                                updateData.complete_time = currentTime;
                                updateData.admin_memo = `${adminMemo || '已确认收到退货商品'} [微信退款成功，退款单号：${refundResult.out_refund_no}]`;

                                // 确认收货后，将订单状态更新为售后完成状态
                                yield _this3.model('order').where({
                                    id: refundApply.order_id
                                }).update({
                                    order_status: 203, // 售后完成状态
                                    refund_time: currentTime,
                                    updated_at: new Date()
                                });

                                // 处理佣金扣除
                                try {
                                    const commissionService = _this3.service('commission', 'common');
                                    yield commissionService.handleRefundCommission(refundApply.order_id);
                                    console.log('✅ 佣金扣除处理完成');
                                } catch (commissionError) {
                                    console.error('❌ 佣金扣除处理失败:', commissionError);
                                    // 不影响主流程，继续执行
                                }
                            } else {
                                console.error('❌ 微信退款失败:', refundResult);
                                return _this3.fail(`微信退款失败：${refundResult.error_msg || '未知错误'}`);
                            }
                        } catch (refundError) {
                            console.error('❌ 调用微信退款API异常:', refundError);
                            return _this3.fail(`退款处理失败：${refundError.error_msg || refundError.message || '系统异常'}`);
                        }

                        // 推送退款状态到旺店通ERP
                        try {
                            console.log(`[旺店通同步] 退货退款已确认收货，开始推送退款状态到旺店通: ${refundApply.order_sn}`);

                            // 获取商品信息
                            const orderGoods = yield _this3.model('order_goods').where({ order_id: refundApply.order_id }).select();

                            if (!think.isEmpty(orderGoods)) {
                                const wangdianSync = new WangDianSync(_this3);
                                const result = yield wangdianSync.pushOrderRefund(orderInfo, orderGoods);

                                if (result.success) {
                                    console.log(`[旺店通同步] 退款状态推送成功: ${refundApply.order_sn}`);
                                } else {
                                    console.error(`[旺店通同步] 退款状态推送失败: ${refundApply.order_sn}, 错误: ${result.message}`);
                                }
                            } else {
                                console.error(`[旺店通同步] 获取商品信息失败，无法推送退款状态: ${refundApply.order_sn}`);
                            }
                        } catch (error) {
                            console.error('[旺店通同步] 推送退款状态时发生异常:', error);
                            // 不抛出异常，避免影响主要的退款流程
                        }
                        break;

                    case 'complete':
                        // 这个动作现在主要用于仅退款的直接完成，或者其他特殊情况
                        updateData.status = 'completed';
                        updateData.complete_time = currentTime;
                        break;

                    case 'processing':
                        if (refundApply.status !== 'pending') {
                            return _this3.fail('只有待处理状态的申请可以设为处理中');
                        }
                        updateData.status = 'processing';
                        updateData.admin_memo = adminMemo;
                        break;

                    default:
                        return _this3.fail('无效的操作类型');
                }

                // 更新售后申请状态
                yield _this3.model('refund_apply').where({
                    id: applyId
                }).update(updateData);

                console.log('✅ 售后申请处理成功');
                return _this3.success({
                    message: '售后申请处理成功',
                    newStatus: updateData.status
                });
            } catch (error) {
                console.error('处理售后申请失败:', error);
                return _this3.fail('处理售后申请失败: ' + error.message);
            }
        })();
    }

    // 获取最近一个月各状态订单数量统计
    getStatusCountAction() {
        var _this4 = this;

        return _asyncToGenerator(function* () {
            try {
                // 计算一个月前的时间戳
                const oneMonthAgo = Math.floor(Date.now() / 1000) - 30 * 24 * 60 * 60;

                const model = _this4.model('order');

                // 获取各状态订单数量
                const statusCounts = {};

                // 全部订单（最近一个月）
                statusCounts.all = yield model.where({
                    add_time: ['>', oneMonthAgo],
                    order_type: ['<', 7],
                    is_delete: 0
                }).count();

                // 待付款订单 (101, 801)
                statusCounts.toPay = yield model.where({
                    add_time: ['>', oneMonthAgo],
                    order_status: ['IN', '101,801'],
                    order_type: ['<', 7],
                    is_delete: 0
                }).count();

                // 待发货订单 (300)
                statusCounts.toDelivery = yield model.where({
                    add_time: ['>', oneMonthAgo],
                    order_status: 300,
                    order_type: ['<', 7],
                    is_delete: 0
                }).count();

                // 待收货订单 (301)
                statusCounts.toReceive = yield model.where({
                    add_time: ['>', oneMonthAgo],
                    order_status: 301,
                    order_type: ['<', 7],
                    is_delete: 0
                }).count();

                // 已收货订单 (401)
                statusCounts.received = yield model.where({
                    add_time: ['>', oneMonthAgo],
                    order_status: 401,
                    order_type: ['<', 7],
                    is_delete: 0
                }).count();

                // 已关闭订单 (102, 103, 203) - 包含售后完成的订单
                statusCounts.closed = yield model.where({
                    add_time: ['>', oneMonthAgo],
                    order_status: ['IN', '102,103,203'],
                    order_type: ['<', 7],
                    is_delete: 0
                }).count();

                // 售后完成订单单独统计
                statusCounts.refundCompleted = yield model.where({
                    add_time: ['>', oneMonthAgo],
                    order_status: 203,
                    order_type: ['<', 7],
                    is_delete: 0
                }).count();

                return _this4.success(statusCounts);
            } catch (error) {
                console.error('获取订单状态统计失败:', error);
                return _this4.fail('获取统计数据失败');
            }
        })();
    }

    // 更新客服备注和旗子标识
    updateServiceMemoAction() {
        var _this5 = this;

        return _asyncToGenerator(function* () {
            try {
                const orderId = _this5.post('order_id');
                const adminMemo = _this5.post('admin_memo') || '';
                const flagColor = _this5.post('service_flag_color') || null;
                const priority = _this5.post('service_priority') || 0;

                if (!orderId) {
                    return _this5.fail('订单ID不能为空');
                }

                // 检查订单是否存在
                const orderExists = yield _this5.model('order').where({ id: orderId }).find();
                if (think.isEmpty(orderExists)) {
                    return _this5.fail('订单不存在');
                }

                // 更新订单的客服备注信息
                yield _this5.model('order').where({ id: orderId }).update({
                    admin_memo: adminMemo,
                    service_flag_color: flagColor,
                    service_priority: parseInt(priority),
                    service_updated_at: Math.floor(Date.now() / 1000),
                    service_updated_by: 'admin' // 这里可以从session中获取当前管理员信息
                });

                return _this5.success('客服备注更新成功');
            } catch (error) {
                console.error('更新客服备注失败:', error);
                return _this5.fail('更新客服备注失败');
            }
        })();
    }

    // 获取客服备注信息
    getServiceMemoAction() {
        var _this6 = this;

        return _asyncToGenerator(function* () {
            try {
                const orderId = _this6.get('order_id');

                if (!orderId) {
                    return _this6.fail('订单ID不能为空');
                }

                const orderInfo = yield _this6.model('order').where({ id: orderId }).field('id,order_sn,admin_memo,service_flag_color,service_priority,service_updated_at,service_updated_by').find();

                if (think.isEmpty(orderInfo)) {
                    return _this6.fail('订单不存在');
                }

                // 格式化更新时间
                if (orderInfo.service_updated_at) {
                    orderInfo.service_updated_at = moment.unix(orderInfo.service_updated_at).format('YYYY-MM-DD HH:mm:ss');
                }

                return _this6.success(orderInfo);
            } catch (error) {
                console.error('获取客服备注失败:', error);
                return _this6.fail('获取客服备注失败');
            }
        })();
    }

    getAutoStatusAction() {
        var _this7 = this;

        return _asyncToGenerator(function* () {
            let status = yield _this7.model('settings').where({
                id: 1
            }).field('autoDelivery').find();
            let info = status.autoDelivery;
            return _this7.success(info);
        })();
    }
    toDeliveryAction() {
        var _this8 = this;

        return _asyncToGenerator(function* () {
            const page = _this8.get('page') || 1;
            const size = _this8.get('size') || 10;
            const status = _this8.get('status') || '';
            const model = _this8.model('order');
            const data = yield model.where({
                order_status: status
            }).order(['id DESC']).page(page, size).countSelect();
            for (const item of data.data) {
                item.goodsList = yield _this8.model('order_goods').field('goods_name,list_pic_url,number,goods_specifition_name_value,retail_price').where({
                    order_id: item.id
                }).select();
                item.goodsCount = 0;
                item.goodsList.forEach(function (v) {
                    item.goodsCount += v.number;
                });
                let province_name = yield _this8.model('region').where({
                    id: item.province
                }).getField('name', true);
                let city_name = yield _this8.model('region').where({
                    id: item.city
                }).getField('name', true);
                let district_name = yield _this8.model('region').where({
                    id: item.district
                }).getField('name', true);
                item.address = province_name + city_name + district_name + item.address;
                item.postscript = Buffer.from(item.postscript, 'base64').toString();
                item.add_time = moment.unix(item.add_time).format('YYYY-MM-DD HH:mm:ss');
                item.order_status_text = yield _this8.model('order').getOrderStatusText(item.id);
                item.button_text = yield _this8.model('order').getOrderBtnText(item.id);
            }
            return _this8.success(data);
        })();
    }
    saveGoodsListAction() {
        var _this9 = this;

        return _asyncToGenerator(function* () {
            // console.log(typeof(data));
            let id = _this9.post('id');
            let order_id = _this9.post('order_id');
            let number = _this9.post('number');
            let price = _this9.post('retail_price');
            let addOrMinus = _this9.post('addOrMinus');
            let changePrice = Number(number) * Number(price);
            console.log(order_id);
            console.log(changePrice);
            if (addOrMinus == 0) {
                yield _this9.model('order_goods').where({
                    id: id
                }).decrement('number', number);
                yield _this9.model('order').where({
                    id: order_id
                }).decrement({
                    actual_price: changePrice,
                    order_price: changePrice,
                    goods_price: changePrice
                });
                let order_sn = _this9.model('order').generateOrderNumber();
                yield _this9.model('order').where({
                    id: order_id
                }).update({
                    order_sn: order_sn
                });
                return _this9.success(order_sn);
            } else if (addOrMinus == 1) {
                yield _this9.model('order_goods').where({
                    id: id
                }).increment('number', number);
                yield _this9.model('order').where({
                    id: order_id
                }).increment({
                    actual_price: changePrice,
                    order_price: changePrice,
                    goods_price: changePrice
                });
                let order_sn = _this9.model('order').generateOrderNumber();
                yield _this9.model('order').where({
                    id: order_id
                }).update({
                    order_sn: order_sn
                });
                return _this9.success(order_sn);
            }
        })();
    }
    goodsListDeleteAction() {
        var _this10 = this;

        return _asyncToGenerator(function* () {
            console.log(_this10.post('id'));
            let id = _this10.post('id');
            let order_id = _this10.post('order_id');
            let number = _this10.post('number');
            let price = _this10.post('retail_price');
            let addOrMinus = _this10.post('addOrMinus');
            let changePrice = Number(number) * Number(price);
            console.log(order_id);
            console.log(changePrice);
            yield _this10.model('order_goods').where({
                id: id
            }).update({
                is_delete: 1
            });
            yield _this10.model('order').where({
                id: order_id
            }).decrement({
                actual_price: changePrice,
                order_price: changePrice,
                goods_price: changePrice
            });
            let order_sn = _this10.model('order').generateOrderNumber();
            yield _this10.model('order').where({
                id: order_id
            }).update({
                order_sn: order_sn
            });
            return _this10.success(order_sn);
        })();
    }
    saveAdminMemoAction() {
        var _this11 = this;

        return _asyncToGenerator(function* () {
            const id = _this11.post('id');
            const text = _this11.post('text');
            const model = _this11.model('order');
            let info = {
                admin_memo: text
            };
            let data = yield model.where({
                id: id
            }).update(info);
            return _this11.success(data);
        })();
    }
    savePrintInfoAction() {
        var _this12 = this;

        return _asyncToGenerator(function* () {
            const id = _this12.post('id');
            const print_info = _this12.post('print_info');
            const model = _this12.model('order');
            let info = {
                print_info: print_info
            };
            let data = yield model.where({
                id: id
            }).update(info);
            return _this12.success(data);
        })();
    }
    saveExpressValueInfoAction() {
        var _this13 = this;

        return _asyncToGenerator(function* () {
            const id = _this13.post('id');
            const express_value = _this13.post('express_value');
            const model = _this13.model('order');
            let info = {
                express_value: express_value
            };
            let data = yield model.where({
                id: id
            }).update(info);
            return _this13.success(data);
        })();
    }
    saveRemarkInfoAction() {
        var _this14 = this;

        return _asyncToGenerator(function* () {
            const id = _this14.post('id');
            const remark = _this14.post('remark');
            const model = _this14.model('order');
            let info = {
                remark: remark
            };
            let data = yield model.where({
                id: id
            }).update(info);
            return _this14.success(data);
        })();
    }
    detailAction() {
        var _this15 = this;

        return _asyncToGenerator(function* () {
            const id = _this15.get('orderId');
            const model = _this15.model('order');
            let data = yield model.where({
                id: id
            }).find();
            data.goodsList = yield _this15.model('order_goods').field('id,product_id,goods_name,goods_aka,list_pic_url,number,goods_specifition_name_value,retail_price,goods_id').where({
                order_id: data.id,
                is_delete: 0
            }).select();
            data.goodsCount = 0;
            data.goodsList.forEach(function (v) {
                data.goodsCount += v.number;
            });
            for (const item of data.goodsList) {
                let info = yield _this15.model('product').where({
                    id: item.product_id
                }).field('goods_sn').find();
                item.goods_sn = info.goods_sn;
            }
            console.log(data.goodsList);
            let userInfo = yield _this15.model('user').where({
                id: data.user_id
            }).find();
            let _nickname = Buffer.from(userInfo.nickname, 'base64').toString();
            data.user_name = _nickname;
            data.avatar = userInfo.avatar;
            let province_name = yield _this15.model('region').where({
                id: data.province
            }).getField('name', true);
            let city_name = yield _this15.model('region').where({
                id: data.city
            }).getField('name', true);
            let district_name = yield _this15.model('region').where({
                id: data.district
            }).getField('name', true);
            data.full_region = province_name + city_name + district_name;
            data.postscript = Buffer.from(data.postscript, 'base64').toString();
            data.order_status_text = yield _this15.model('order').getOrderStatusText(data.id);
            data.add_time = moment.unix(data.add_time).format('YYYY-MM-DD HH:mm:ss');
            data.allAddress = data.full_region + data.address;
            if (data.pay_time != 0) {
                data.pay_time = moment.unix(data.pay_time).format('YYYY-MM-DD HH:mm:ss');
            }
            if (data.shipping_time != 0) {
                data.shipping_time = moment.unix(data.shipping_time).format('YYYY-MM-DD HH:mm:ss');
            }
            if (data.confirm_time != 0) {
                data.confirm_time = moment.unix(data.confirm_time).format('YYYY-MM-DD HH:mm:ss');
            }
            if (data.dealdone_time != 0) {
                data.dealdone_time = moment.unix(data.dealdone_time).format('YYYY-MM-DD HH:mm:ss');
            }
            let def = yield _this15.model('settings').where({
                id: 1
            }).find();
            let senderInfo = {};
            let receiveInfo = {};
            receiveInfo = {
                name: data.consignee,
                mobile: data.mobile,
                province: province_name,
                province_id: data.province,
                city: city_name,
                city_id: data.city,
                district: district_name,
                district_id: data.district,
                address: data.address
            };
            senderInfo = {
                name: def.Name,
                mobile: def.Tel,
                province: def.ProvinceName,
                city: def.CityName,
                district: def.ExpAreaName,
                province_id: def.province_id,
                city_id: def.city_id,
                district_id: def.district_id,
                address: def.Address
            };
            return _this15.success({
                orderInfo: data,
                receiver: receiveInfo,
                sender: senderInfo
            });
        })();
    }
    getAllRegionAction() {
        var _this16 = this;

        return _asyncToGenerator(function* () {
            // 我写的算法
            const model = _this16.model('region');
            const aData = yield model.where({
                type: 1
            }).select();
            const bData = yield model.where({
                type: 2
            }).select();
            const cData = yield model.where({
                type: 3
            }).select();
            let newData = [];
            for (const item of aData) {
                let children = [];
                for (const bitem of bData) {
                    let innerChildren = [];
                    for (const citem of cData) {
                        if (citem.parent_id == bitem.id) {
                            innerChildren.push({
                                value: citem.id,
                                label: citem.name
                            });
                        }
                    }
                    if (bitem.parent_id == item.id) {
                        children.push({
                            value: bitem.id,
                            label: bitem.name,
                            children: innerChildren
                        });
                    }
                }
                newData.push({
                    value: item.id,
                    label: item.name,
                    children: children
                });
            }
            return _this16.success(newData);
        })();
    }
    orderpackAction() {
        var _this17 = this;

        return _asyncToGenerator(function* () {
            const id = _this17.get('orderId');
            const model = _this17.model('order');
            const data = yield model.where({
                id: id
            }).update({
                order_status: 300
            });
        })();
    }
    orderReceiveAction() {
        var _this18 = this;

        return _asyncToGenerator(function* () {
            const id = _this18.get('orderId');
            let currentTime = parseInt(new Date().getTime() / 1000);
            const model = _this18.model('order');
            const data = yield model.where({
                id: id
            }).update({
                order_status: 302,
                shipping_time: currentTime
            });
        })();
    }
    orderPriceAction() {
        var _this19 = this;

        return _asyncToGenerator(function* () {
            const id = _this19.get('orderId');
            const goodsPrice = _this19.get('goodsPrice');
            const freightPrice = _this19.get('freightPrice');
            const actualPrice = _this19.get('actualPrice');
            const model = _this19.model('order');
            const data = yield model.where({
                id: id
            }).find();
            let newData = {
                actual_price: actualPrice,
                freight_price: freightPrice,
                goods_price: goodsPrice,
                order_sn: model.generateOrderNumber()
            };
            yield model.where({
                id: id
            }).update(newData);
        })();
    }
    getOrderExpressAction() {
        var _this20 = this;

        return _asyncToGenerator(function* () {
            const orderId = _this20.post('orderId');
            const latestExpressInfo = yield _this20.model('order_express').getLatestOrderExpressByAli(orderId);
            return _this20.success(latestExpressInfo);
        })();
    }
    getPrintTestAction() {
        var _this21 = this;

        return _asyncToGenerator(function* () {
            const latestExpressInfo = yield _this21.model('order_express').printExpress();
            return _this21.success(latestExpressInfo);
        })();
    }
    getMianExpressAction() {
        var _this22 = this;

        return _asyncToGenerator(function* () {
            const orderId = _this22.post('orderId');
            const sender = _this22.post('sender');
            const receiver = _this22.post('receiver');
            console.log(orderId);
            console.log(sender);
            console.log(receiver);
            let senderOptions = sender.senderOptions;
            let receiveOptions = receiver.receiveOptions;
            let senderInfo = {
                Name: sender.name,
                Tel: sender.mobile,
                ProvinceName: yield _this22.model('region').where({
                    id: senderOptions[0]
                }).getField('name', true),
                CityName: yield _this22.model('region').where({
                    id: senderOptions[1]
                }).getField('name', true),
                ExpAreaName: yield _this22.model('region').where({
                    id: senderOptions[2]
                }).getField('name', true),
                Address: sender.address
            };
            let receiverInfo = {
                Name: receiver.name,
                Tel: receiver.mobile,
                ProvinceName: yield _this22.model('region').where({
                    id: receiveOptions[0]
                }).getField('name', true),
                CityName: yield _this22.model('region').where({
                    id: receiveOptions[1]
                }).getField('name', true),
                ExpAreaName: yield _this22.model('region').where({
                    id: receiveOptions[2]
                }).getField('name', true),
                Address: receiver.address
            };
            // 每次重新生成一次订单号，这样，不会出现已经下过单的情况了。
            const expressType = _this22.post('expressType');
            const latestExpressInfo = yield _this22.model('order_express').getMianExpress(orderId, senderInfo, receiverInfo, expressType);
            console.log('lastExpressInfo++++++++++++++++++++++');
            console.log(latestExpressInfo);
            if (latestExpressInfo.ResultCode == 100) {
                // 获取快递单号成功，然后存入order_express中
                _this22.orderExpressAdd(latestExpressInfo, orderId);
            }
            return _this22.success({
                latestExpressInfo: latestExpressInfo,
                sender: senderInfo,
                receiver: receiverInfo
            });
        })();
    }
    rePrintExpressAction() {
        var _this23 = this;

        return _asyncToGenerator(function* () {
            const date = new Date();
            let orderId = _this23.get('orderId');
            let order_sn = date.getFullYear() + _.padStart(date.getMonth(), 2, '0') + _.padStart(date.getDay(), 2, '0') + _.padStart(date.getHours(), 2, '0') + _.padStart(date.getMinutes(), 2, '0') + _.padStart(date.getSeconds(), 2, '0') + _.random(100000, 999999);
            let info = yield _this23.model('order').where({
                id: orderId
            }).update({
                order_sn: order_sn
            });
            return _this23.success(info);
        })();
    }
    directPrintExpressAction() {
        var _this24 = this;

        return _asyncToGenerator(function* () {
            let orderId = _this24.get('orderId');
            let express = yield _this24.model('order_express').where({
                order_id: orderId
            }).find();
            let info = {};
            if (express.express_type < 4) {
                info = yield _this24.model('shipper').where({
                    code: 'SF'
                }).find();
            } else {
                info = yield _this24.model('shipper').where({
                    code: 'YTO'
                }).find();
            }
            express.MonthCode = info.MonthCode;
            express.send_time = moment.unix(express.add_time).format('YYYY-MM-DD');
            return _this24.success(express);
        })();
    }
    orderExpressAdd(ele, orderId) {
        var _this25 = this;

        return _asyncToGenerator(function* () {
            let currentTime = parseInt(new Date().getTime() / 1000);
            let info = yield _this25.model('order_express').where({
                order_id: orderId
            }).find();
            if (think.isEmpty(info)) {
                let orderInfo = ele.Order;
                let ShipperCode = orderInfo.ShipperCode;
                let logistic_code = orderInfo.LogisticCode;
                let expressType = ele.expressType;
                let region_code = orderInfo.DestinatioCode;
                if (expressType == 4) {
                    region_code = orderInfo.MarkDestination;
                }
                const model = _this25.model('order');
                let kdInfo = yield _this25.model('shipper').where({
                    code: ShipperCode
                }).find();
                let kdData = {
                    order_id: orderId,
                    shipper_id: kdInfo.id,
                    shipper_name: kdInfo.name,
                    shipper_code: ShipperCode,
                    logistic_code: logistic_code,
                    region_code: region_code,
                    express_type: expressType,
                    add_time: currentTime
                };
                yield _this25.model('order_express').add(kdData);
            } else {
                let orderInfo = ele.Order;
                yield _this25.model('order_express').where({
                    order_id: orderId
                }).update({
                    logistic_code: orderInfo.LogisticCode
                });
            }
            // 如果生成快递单号了。然后又最后没有使用，又去生成快递单号，那么应该重新生成下订单号，用新订单号去生成快递单号，然后update掉旧的order_express
        })();
    }
    // 点击打印并发货按钮后，就将订单的状态改成已发货
    goDeliveryAction() {
        var _this26 = this;

        return _asyncToGenerator(function* () {
            let orderId = _this26.post('order_id');
            let currentTime = parseInt(new Date().getTime() / 1000);
            let updateData = {
                order_status: 301,
                print_status: 1,
                shipping_status: 1,
                shipping_time: currentTime
            };
            let data = yield _this26.model('order').where({
                id: orderId
            }).update(updateData);

            // 获取订单详细信息
            let orderInfo = yield _this26.model('order').where({
                id: orderId
            }).find();
            let user = yield _this26.model('user').where({
                id: orderInfo.user_id
            }).find();
            let openId = user.weixin_openid;

            // 获取商品信息
            let goodsInfo = yield _this26.model('order_goods').where({
                order_id: orderId
            }).field('goods_name,number').select();
            let express = yield _this26.model('order_express').where({
                order_id: orderId
            }).find();

            // 物品名称
            let goodsName = '';
            if (goodsInfo.length == 1) {
                goodsName = goodsInfo[0].goods_name;
            } else {
                goodsName = goodsInfo[0].goods_name + '等' + goodsInfo.length + '件商品';
            }

            // 支付时间
            let shippingTime = moment.unix(currentTime).format('YYYY-MM-DD HH:mm:ss');

            // 发送订阅消息
            let TEMPLATE_ID = think.config('templateId.deliveryId');
            let message = {
                "touser": openId,
                "template_id": TEMPLATE_ID,
                "page": '/pages/ucenter/index/index',
                "miniprogram_state": "formal",
                "lang": "zh_CN",
                "data": {
                    "thing7": {
                        "value": goodsName
                    },
                    "date2": {
                        "value": shippingTime
                    },
                    "name3": {
                        "value": express.shipper_name
                    },
                    "character_string4": {
                        "value": express.logistic_code
                    },
                    "thing9": {
                        "value": '签收前请检查包裹！'
                    }
                }
            };

            const tokenServer = think.service('weixin', 'api');
            const token = yield tokenServer.getAccessToken();
            const res = yield tokenServer.sendMessage(token, message);

            // 调用微信订单发货管理接口
            yield _this26.syncWeixinShippingInfo(orderInfo, express, goodsInfo, openId, 1);

            return _this26.success();
        })();
    }
    goPrintOnlyAction() {
        var _this27 = this;

        return _asyncToGenerator(function* () {
            let orderId = _this27.post('order_id');
            let updateData = {
                print_status: 1
            };
            let data = yield _this27.model('order').where({
                id: orderId
            }).update(updateData);
            return _this27.success(data);
        })();
    }
    orderDeliveryAction() {
        var _this28 = this;

        return _asyncToGenerator(function* () {
            // 发货api
            const orderId = _this28.get('orderId');
            const method = _this28.get('method');
            const deliveryId = _this28.get('shipper') || 0;
            const logistic_code = _this28.get('logistic_code') || 0;
            let currentTime = parseInt(new Date().getTime() / 1000);
            let expressName = '';
            let expressInfo = null;

            if (method == 2) {
                // 快递发货
                let ele = yield _this28.model('order_express').where({
                    order_id: orderId
                }).find();
                if (think.isEmpty(ele)) {
                    let kdInfo = yield _this28.model('shipper').where({
                        id: deliveryId
                    }).find();
                    expressName = kdInfo.name;
                    let kdData = {
                        order_id: orderId,
                        shipper_id: deliveryId,
                        shipper_name: kdInfo.name,
                        shipper_code: kdInfo.code,
                        logistic_code: logistic_code,
                        add_time: currentTime
                    };
                    yield _this28.model('order_express').add(kdData);
                    expressInfo = kdData;
                    let updateData = {
                        order_status: 301,
                        shipping_status: 1,
                        shipping_time: currentTime
                    };
                    yield _this28.model('order').where({
                        id: orderId
                    }).update(updateData);
                } else {
                    let kdInfo = yield _this28.model('shipper').where({
                        id: deliveryId
                    }).find();
                    expressName = kdInfo.name;
                    let kdData = {
                        order_id: orderId,
                        shipper_id: deliveryId,
                        shipper_name: kdInfo.name,
                        shipper_code: kdInfo.code,
                        logistic_code: logistic_code,
                        add_time: currentTime
                    };
                    yield _this28.model('order_express').where({
                        order_id: orderId
                    }).update(kdData);
                    expressInfo = kdData;
                }
            } else if (method == 3) {
                // 自提
                let updateData = {
                    order_status: 301,
                    shipping_time: currentTime
                };
                yield _this28.model('order').where({
                    id: orderId
                }).update(updateData);
                expressName = '自提件';
            }

            // 发送订阅消息
            yield _this28.deliveryMessage(method, orderId, expressName, logistic_code);

            // 获取订单信息并同步微信发货接口
            let orderInfo = yield _this28.model('order').where({
                id: orderId
            }).find();
            let user = yield _this28.model('user').where({
                id: orderInfo.user_id
            }).find();
            let goodsInfo = yield _this28.model('order_goods').where({
                order_id: orderId
            }).field('goods_name,number').select();

            // 确定物流类型：1-快递 2-同城 3-虚拟 4-自提
            let logisticsType = method == 2 ? 1 : method == 3 ? 4 : 1;

            // 调用微信订单发货管理接口
            yield _this28.syncWeixinShippingInfo(orderInfo, expressInfo, goodsInfo, user.weixin_openid, logisticsType);

            return _this28.success();
        })();
    }
    deliveryMessage(method, orderId, expressName, logistic_code) {
        var _this29 = this;

        return _asyncToGenerator(function* () {
            let orderInfo = yield _this29.model('order').where({
                id: orderId
            }).field('user_id').find();
            let user = yield _this29.model('user').where({
                id: orderInfo.user_id
            }).field('weixin_openid').find();
            let openId = user.weixin_openid;
            // 物品名称
            // 快递单号
            // 快递公司
            // 发货时间
            // 温馨提示
            let goodsInfo = yield _this29.model('order_goods').where({
                order_id: orderId
            }).field('goods_name').select();
            // 物品名称
            let goodsName = '';
            if (goodsInfo.length == 1) {
                goodsName = goodsInfo[0].goods_name;
            } else {
                goodsName = goodsInfo[0].goods_name + '等' + goodsInfo.length + '件商品';
            }
            // 支付时间
            let currentTime = parseInt(new Date().getTime() / 1000);
            let shippingTime = moment.unix(currentTime).format('YYYY-MM-DD HH:mm:ss');
            // 订单金额
            // 订阅消息 请先在微信小程序的官方后台设置好订阅消息模板，然后根据自己的data的字段信息，设置好data
            let TEMPLATE_ID = think.config('templateId.deliveryId');
            let message = {
                "touser": openId,
                "template_id": TEMPLATE_ID,
                "page": '/pages/ucenter/index/index',
                "miniprogram_state": "formal",
                "lang": "zh_CN",
                "data": {
                    "thing7": {
                        "value": goodsName
                    },
                    "date2": {
                        "value": shippingTime
                    },
                    "name3": {
                        "value": expressName
                    },
                    "character_string4": {
                        "value": logistic_code
                    },
                    "thing9": {
                        "value": '签收前请检查包裹！'
                    }
                }
            };
            const tokenServer = think.service('weixin', 'api');
            const token = yield tokenServer.getAccessToken();
            const res = yield tokenServer.sendMessage(token, message);
        })();
    }
    checkExpressAction() {
        var _this30 = this;

        return _asyncToGenerator(function* () {
            const id = _this30.get('orderId');
            let info = yield _this30.model('order_express').where({
                order_id: id
            }).find();
            if (!think.isEmpty(info)) {
                return _this30.success(info);
            } else {
                return _this30.fail(100, '没找到');
            }
        })();
    }
    saveAddressAction() {
        var _this31 = this;

        return _asyncToGenerator(function* () {
            const sn = _this31.post('order_sn');
            const name = _this31.post('name');
            const mobile = _this31.post('mobile');
            const cAddress = _this31.post('cAddress');
            const addOptions = _this31.post('addOptions');
            const province = addOptions[0];
            const city = addOptions[1];
            const district = addOptions[2];
            let info = {
                consignee: name,
                mobile: mobile,
                address: cAddress,
                province: province,
                city: city,
                district: district
            };
            const model = _this31.model('order');
            const data = yield model.where({
                order_sn: sn
            }).update(info);
            return _this31.success(data);
        })();
    }
    storeAction() {
        var _this32 = this;

        return _asyncToGenerator(function* () {
            if (!_this32.isPost) {
                return false;
            }
            const values = _this32.post();
            const id = _this32.post('id');
            const model = _this32.model('order');
            values.is_show = values.is_show ? 1 : 0;
            values.is_new = values.is_new ? 1 : 0;
            if (id > 0) {
                yield model.where({
                    id: id
                }).update(values);
            } else {
                delete values.id;
                yield model.add(values);
            }
            return _this32.success(values);
        })();
    }
    changeStatusAction() {
        var _this33 = this;

        return _asyncToGenerator(function* () {
            const orderSn = _this33.post('orderSn');
            const value = _this33.post('status');
            const info = yield _this33.model('order').where({
                order_sn: orderSn
            }).update({
                order_status: value
            });
            return _this33.success(info);
        })();
    }
    destoryAction() {
        var _this34 = this;

        return _asyncToGenerator(function* () {
            const id = _this34.post('id');
            yield _this34.model('order').where({
                id: id
            }).limit(1).delete();
            // 删除订单商品
            yield _this34.model('order_goods').where({
                order_id: id
            }).delete();
            // TODO 事务，验证订单是否可删除（只有失效的订单才可以删除）
            return _this34.success();
        })();
    }
    getGoodsSpecificationAction() {
        var _this35 = this;

        return _asyncToGenerator(function* () {
            const goods_id = _this35.post('goods_id');
            let data = yield _this35.model('goods_specification').where({
                goods_id: goods_id,
                is_delete: 0
            }).field('id,value').select();
            return _this35.success(data);
        })();
    }

    /**
     * 同步微信订单发货信息
     * @param {Object} orderInfo 订单信息
     * @param {Object} expressInfo 快递信息
     * @param {Array} goodsInfo 商品信息
     * @param {string} openid 用户openid
     * @param {number} logisticsType 物流类型
     */
    syncWeixinShippingInfo(orderInfo, expressInfo, goodsInfo, openid, logisticsType = 1) {
        var _this36 = this;

        return _asyncToGenerator(function* () {
            try {
                // 检查是否启用订单发货管理
                const shippingConfig = think.config('weixin.shipping_management');
                if (!shippingConfig || !shippingConfig.enabled) {
                    console.log('订单发货管理功能未启用，跳过同步');
                    return { success: false, reason: 'disabled' };
                }

                console.log('=== 开始同步微信订单发货信息 ===');
                console.log('订单ID:', orderInfo.id, '订单号:', orderInfo.order_sn);
                console.log('物流类型:', logisticsType, '快递信息:', expressInfo ? '有' : '无');

                // 数据验证
                if (!openid) {
                    console.error('用户openid为空，跳过微信发货同步');
                    return { success: false, reason: 'invalid_openid' };
                }

                if (!orderInfo || !orderInfo.order_sn) {
                    console.error('订单信息不完整，跳过微信发货同步');
                    return { success: false, reason: 'invalid_order' };
                }

                const weixinService = think.service('weixin', 'api');

                // 构建发货数据（包含数据验证）
                let shippingData;
                try {
                    shippingData = weixinService.buildShippingData({
                        order_sn: orderInfo.order_sn,
                        goods_list: goodsInfo
                    }, expressInfo, openid, logisticsType);
                } catch (validationError) {
                    console.error('发货数据验证失败:', validationError.message);

                    // 记录验证失败日志
                    try {
                        const errorInfo = JSON.stringify({
                            type: 'validation_error',
                            message: validationError.message
                        });

                        // 先查询当前状态，避免重复更新
                        const currentOrder = yield _this36.model('order').where({
                            id: orderInfo.id
                        }).field('weixin_shipping_sync').find();

                        // 只有状态不同时才更新
                        if (think.isEmpty(currentOrder) || currentOrder.weixin_shipping_sync !== 0) {
                            yield _this36.model('order').where({
                                id: orderInfo.id
                            }).update({
                                weixin_shipping_sync: 0,
                                weixin_shipping_sync_error: errorInfo
                            });
                            console.log('数据库验证失败状态已更新');
                        } else {
                            console.log('数据库验证失败状态无需更新');
                        }
                    } catch (dbError) {
                        console.error('更新数据库验证失败状态失败:', dbError.message);
                    }

                    return { success: false, reason: 'validation_failed', error: validationError.message };
                }

                console.log('发货数据构建完成:', JSON.stringify(shippingData, null, 2));

                // 调用微信发货接口
                const result = yield weixinService.uploadShippingInfo(shippingData);

                if (result.success) {
                    console.log('微信发货信息同步成功');

                    // 记录同步成功日志到数据库
                    try {
                        // 先查询当前状态，避免重复更新
                        const currentOrder = yield _this36.model('order').where({
                            id: orderInfo.id
                        }).field('weixin_shipping_sync,weixin_shipping_sync_time').find();

                        // 只有状态不同时才更新
                        if (think.isEmpty(currentOrder) || currentOrder.weixin_shipping_sync !== 1) {
                            yield _this36.model('order').where({
                                id: orderInfo.id
                            }).update({
                                weixin_shipping_sync: 1,
                                weixin_shipping_sync_time: parseInt(new Date().getTime() / 1000),
                                weixin_shipping_sync_error: null
                            });
                            console.log('数据库同步状态已更新');
                        } else {
                            console.log('数据库同步状态无需更新');
                        }
                    } catch (dbError) {
                        console.error('更新数据库同步状态失败:', dbError.message);
                        // 数据库更新失败不影响接口调用成功的结果
                    }

                    return { success: true };
                } else {
                    console.error('微信发货信息同步失败:', result.error);

                    // 记录失败日志
                    try {
                        yield _this36.model('order').where({
                            id: orderInfo.id
                        }).update({
                            weixin_shipping_sync: 0,
                            weixin_shipping_sync_error: JSON.stringify(result.error)
                        });
                    } catch (dbError) {
                        console.error('更新数据库失败状态失败:', dbError.message);
                    }

                    return { success: false, reason: 'api_error', error: result.error };
                }
            } catch (error) {
                console.error('微信发货信息同步异常:', error);

                // 记录异常日志
                try {
                    // 先查询当前状态，避免重复更新
                    const currentOrder = yield _this36.model('order').where({
                        id: orderInfo.id
                    }).field('weixin_shipping_sync').find();

                    const errorInfo = JSON.stringify({
                        type: 'exception',
                        message: error.message,
                        stack: error.stack
                    });

                    // 只有状态不同时才更新
                    if (think.isEmpty(currentOrder) || currentOrder.weixin_shipping_sync !== 0) {
                        yield _this36.model('order').where({
                            id: orderInfo.id
                        }).update({
                            weixin_shipping_sync: 0,
                            weixin_shipping_sync_error: errorInfo
                        });
                        console.log('数据库异常状态已更新');
                    } else {
                        console.log('数据库异常状态无需更新');
                    }
                } catch (dbError) {
                    console.error('记录同步异常日志失败:', dbError.message);
                }

                return { success: false, reason: 'exception', error: error.message };
            }
        })();
    }

    /**
     * 重新同步微信发货信息（手动重试接口）
     */
    resyncWeixinShippingAction() {
        var _this37 = this;

        return _asyncToGenerator(function* () {
            const orderId = _this37.post('orderId');

            try {
                // 获取订单信息
                let orderInfo = yield _this37.model('order').where({
                    id: orderId
                }).find();

                if (think.isEmpty(orderInfo)) {
                    return _this37.fail('订单不存在');
                }

                if (orderInfo.order_status !== 301) {
                    return _this37.fail('订单未发货，无法同步');
                }

                // 获取用户信息
                let user = yield _this37.model('user').where({
                    id: orderInfo.user_id
                }).find();

                // 获取快递信息
                let expressInfo = yield _this37.model('order_express').where({
                    order_id: orderId
                }).find();

                // 获取商品信息
                let goodsInfo = yield _this37.model('order_goods').where({
                    order_id: orderId
                }).field('goods_name,number').select();

                // 确定物流类型
                let logisticsType = think.isEmpty(expressInfo) ? 4 : 1; // 有快递信息为快递，否则为自提

                // 重新同步
                yield _this37.syncWeixinShippingInfo(orderInfo, expressInfo, goodsInfo, user.weixin_openid, logisticsType);

                return _this37.success('重新同步完成');
            } catch (error) {
                console.error('重新同步微信发货信息失败:', error);
                return _this37.fail('重新同步失败');
            }
        })();
    }
};