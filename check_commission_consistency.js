/**
 * 检查佣金数据一致性问题
 * 验证 hiolabs_user_commission 表与实际订单佣金的同步情况
 */

const mysql = require('mysql2/promise');

// 数据库配置（请根据实际情况修改）
const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'hiolabsdb',
    charset: 'utf8mb4'
};

async function checkCommissionConsistency() {
    let connection;
    
    try {
        console.log('=== 检查佣金数据一致性问题 ===\n');
        
        // 连接数据库
        connection = await mysql.createConnection(dbConfig);
        console.log('✓ 数据库连接成功');
        
        // 1. 检查相关表是否存在
        console.log('\n1. 检查佣金相关表...');
        
        const tables = [
            'hiolabs_user_commission',
            'hiolabs_commission_log', 
            'hiolabs_promotion_orders'
        ];
        
        for (const table of tables) {
            try {
                const [result] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
                console.log(`✓ ${table}: ${result[0].count} 条记录`);
            } catch (error) {
                console.log(`✗ ${table}: 表不存在或查询失败`);
                return;
            }
        }
        
        // 2. 检查数据一致性问题
        console.log('\n2. 检查数据一致性问题...');
        
        // 2.1 检查用户佣金账户表与佣金日志表的一致性
        console.log('\n2.1 检查账户表与日志表的一致性...');
        
        const [inconsistentUsers] = await connection.execute(`
            SELECT 
                uc.user_id,
                uc.total_commission as account_total,
                uc.available_commission as account_available,
                uc.frozen_commission as account_frozen,
                uc.withdrawn_commission as account_withdrawn,
                COALESCE(SUM(CASE WHEN cl.commission_change > 0 THEN cl.commission_change ELSE 0 END), 0) as log_total_earned,
                COALESCE(SUM(CASE WHEN cl.commission_change < 0 THEN ABS(cl.commission_change) ELSE 0 END), 0) as log_total_deducted,
                COALESCE(SUM(cl.commission_change), 0) as log_net_commission
            FROM hiolabs_user_commission uc
            LEFT JOIN hiolabs_commission_log cl ON uc.user_id = cl.user_id 
                AND cl.commission_type = 'promotion' 
                AND cl.status = 'completed'
            GROUP BY uc.user_id, uc.total_commission, uc.available_commission, uc.frozen_commission, uc.withdrawn_commission
            HAVING ABS(uc.total_commission - log_net_commission) > 0.01
            ORDER BY ABS(uc.total_commission - log_net_commission) DESC
            LIMIT 10
        `);
        
        if (inconsistentUsers.length > 0) {
            console.log(`⚠️  发现 ${inconsistentUsers.length} 个用户的账户表与日志表数据不一致:`);
            inconsistentUsers.forEach(user => {
                const diff = Math.abs(parseFloat(user.account_total) - parseFloat(user.log_net_commission));
                console.log(`  用户 ${user.user_id}:`);
                console.log(`    账户表总佣金: ${user.account_total}`);
                console.log(`    日志表净佣金: ${user.log_net_commission}`);
                console.log(`    差额: ${diff.toFixed(2)}`);
                console.log('');
            });
        } else {
            console.log('✓ 账户表与日志表数据一致');
        }
        
        // 2.2 检查推广订单与佣金日志的关联
        console.log('\n2.2 检查推广订单与佣金日志的关联...');
        
        // 查找有佣金但没有对应日志记录的推广订单
        const [orphanOrders] = await connection.execute(`
            SELECT po.id, po.promoter_user_id, po.commission_amount, po.commission_status
            FROM hiolabs_promotion_orders po
            LEFT JOIN hiolabs_commission_log cl ON po.id = cl.promotion_order_id
            WHERE po.commission_amount > 0 
            AND po.commission_status = 'settled'
            AND cl.id IS NULL
            LIMIT 10
        `);
        
        if (orphanOrders.length > 0) {
            console.log(`⚠️  发现 ${orphanOrders.length} 个已结算但没有佣金日志的推广订单:`);
            orphanOrders.forEach(order => {
                console.log(`  订单ID: ${order.id}, 推广员: ${order.promoter_user_id}, 佣金: ${order.commission_amount}`);
            });
        } else {
            console.log('✓ 所有已结算推广订单都有对应的佣金日志');
        }
        
        // 查找有佣金日志但没有对应推广订单的记录
        const [orphanLogs] = await connection.execute(`
            SELECT cl.id, cl.user_id, cl.commission_change, cl.promotion_order_id
            FROM hiolabs_commission_log cl
            LEFT JOIN hiolabs_promotion_orders po ON cl.promotion_order_id = po.id
            WHERE cl.commission_type = 'promotion' 
            AND cl.promotion_order_id IS NOT NULL
            AND po.id IS NULL
            LIMIT 10
        `);
        
        if (orphanLogs.length > 0) {
            console.log(`⚠️  发现 ${orphanLogs.length} 个佣金日志没有对应的推广订单:`);
            orphanLogs.forEach(log => {
                console.log(`  日志ID: ${log.id}, 用户: ${log.user_id}, 佣金: ${log.commission_change}, 推广订单ID: ${log.promotion_order_id}`);
            });
        } else {
            console.log('✓ 所有佣金日志都有对应的推广订单');
        }
        
        // 2.3 检查字段逻辑一致性
        console.log('\n2.3 检查字段逻辑一致性...');
        
        const [logicErrors] = await connection.execute(`
            SELECT user_id, total_commission, available_commission, frozen_commission, withdrawn_commission
            FROM hiolabs_user_commission
            WHERE total_commission < 0 
            OR available_commission < 0 
            OR frozen_commission < 0 
            OR withdrawn_commission < 0
            OR (available_commission + frozen_commission + withdrawn_commission) > (total_commission + 1)
            LIMIT 10
        `);
        
        if (logicErrors.length > 0) {
            console.log(`⚠️  发现 ${logicErrors.length} 个用户的佣金字段逻辑错误:`);
            logicErrors.forEach(user => {
                console.log(`  用户 ${user.user_id}: 总=${user.total_commission}, 可用=${user.available_commission}, 冻结=${user.frozen_commission}, 已提现=${user.withdrawn_commission}`);
            });
        } else {
            console.log('✓ 佣金字段逻辑正确');
        }
        
        // 2.4 检查冻结佣金计算
        console.log('\n2.4 检查冻结佣金计算...');
        
        const tenDaysAgo = Math.floor(Date.now() / 1000) - (10 * 24 * 3600);
        
        const [frozenErrors] = await connection.execute(`
            SELECT 
                uc.user_id,
                uc.frozen_commission as account_frozen,
                COALESCE(SUM(CASE WHEN cl.commission_change > 0 THEN cl.commission_change ELSE 0 END), 0) as calculated_frozen
            FROM hiolabs_user_commission uc
            LEFT JOIN hiolabs_commission_log cl ON uc.user_id = cl.user_id 
                AND cl.commission_type = 'promotion' 
                AND cl.status = 'completed'
                AND cl.commission_change > 0
                AND UNIX_TIMESTAMP(cl.created_at) > ?
            GROUP BY uc.user_id, uc.frozen_commission
            HAVING ABS(uc.frozen_commission - calculated_frozen) > 0.01
            LIMIT 5
        `, [tenDaysAgo]);
        
        if (frozenErrors.length > 0) {
            console.log(`⚠️  发现 ${frozenErrors.length} 个用户的冻结佣金计算错误:`);
            frozenErrors.forEach(user => {
                console.log(`  用户 ${user.user_id}: 账户冻结=${user.account_frozen}, 计算冻结=${user.calculated_frozen}`);
            });
        } else {
            console.log('✓ 冻结佣金计算正确');
        }
        
        // 3. 统计总体情况
        console.log('\n3. 统计总体情况...');
        
        const [summary] = await connection.execute(`
            SELECT 
                COUNT(DISTINCT uc.user_id) as total_users,
                SUM(uc.total_commission) as total_commission_sum,
                SUM(uc.available_commission) as available_commission_sum,
                SUM(uc.frozen_commission) as frozen_commission_sum,
                SUM(uc.withdrawn_commission) as withdrawn_commission_sum,
                COUNT(DISTINCT cl.user_id) as users_with_logs,
                SUM(CASE WHEN cl.commission_change > 0 THEN cl.commission_change ELSE 0 END) as log_total_earned,
                SUM(CASE WHEN cl.commission_change < 0 THEN ABS(cl.commission_change) ELSE 0 END) as log_total_deducted
            FROM hiolabs_user_commission uc
            LEFT JOIN hiolabs_commission_log cl ON uc.user_id = cl.user_id 
                AND cl.commission_type = 'promotion' 
                AND cl.status = 'completed'
        `);
        
        const stats = summary[0];
        console.log('佣金系统统计:');
        console.log(`  有佣金账户的用户: ${stats.total_users}`);
        console.log(`  有佣金日志的用户: ${stats.users_with_logs}`);
        console.log(`  账户表总佣金: ${parseFloat(stats.total_commission_sum || 0).toFixed(2)}`);
        console.log(`  日志表总收入: ${parseFloat(stats.log_total_earned || 0).toFixed(2)}`);
        console.log(`  日志表总扣除: ${parseFloat(stats.log_total_deducted || 0).toFixed(2)}`);
        console.log(`  可用佣金总额: ${parseFloat(stats.available_commission_sum || 0).toFixed(2)}`);
        console.log(`  冻结佣金总额: ${parseFloat(stats.frozen_commission_sum || 0).toFixed(2)}`);
        console.log(`  已提现佣金总额: ${parseFloat(stats.withdrawn_commission_sum || 0).toFixed(2)}`);
        
        console.log('\n=== 检查完成 ===');
        
        // 4. 问题总结
        const hasIssues = inconsistentUsers.length > 0 || orphanOrders.length > 0 || 
                         orphanLogs.length > 0 || logicErrors.length > 0 || frozenErrors.length > 0;
        
        if (hasIssues) {
            console.log('\n❌ 发现数据一致性问题:');
            if (inconsistentUsers.length > 0) console.log('- 账户表与日志表数据不一致');
            if (orphanOrders.length > 0) console.log('- 存在孤立的推广订单记录');
            if (orphanLogs.length > 0) console.log('- 存在孤立的佣金日志记录');
            if (logicErrors.length > 0) console.log('- 佣金字段逻辑错误');
            if (frozenErrors.length > 0) console.log('- 冻结佣金计算错误');
            console.log('\n建议运行数据修复脚本');
        } else {
            console.log('\n✅ 数据一致性检查通过，未发现问题');
        }
        
    } catch (error) {
        console.error('检查过程中发生错误:', error.message);
    } finally {
        if (connection) {
            await connection.end();
            console.log('\n数据库连接已关闭');
        }
    }
}

// 运行检查
if (require.main === module) {
    checkCommissionConsistency().catch(console.error);
}

module.exports = { checkCommissionConsistency };
