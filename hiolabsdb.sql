﻿# Host: localhost  (Version: 5.7.26)
# Date: 2025-07-16 03:34:00
# Generator: MySQL-Front 5.3  (Build 4.234)

/*!40101 SET NAMES utf8 */;

#
# Structure for table "hiolabs_ad"
#

DROP TABLE IF EXISTS `hiolabs_ad`;
CREATE TABLE `hiolabs_ad` (
  `id` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `link_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0商品，1链接',
  `link` varchar(255) DEFAULT '',
  `goods_id` int(11) NOT NULL DEFAULT '0',
  `image_url` text NOT NULL,
  `end_time` int(11) NOT NULL DEFAULT '0',
  `enabled` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `sort_order` tinyint(4) NOT NULL DEFAULT '0',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `enabled` (`enabled`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_address"
#

DROP TABLE IF EXISTS `hiolabs_address`;
CREATE TABLE `hiolabs_address` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL DEFAULT '',
  `user_id` mediumint(8) unsigned NOT NULL DEFAULT '0',
  `country_id` smallint(6) NOT NULL DEFAULT '0',
  `province_id` smallint(6) NOT NULL DEFAULT '0',
  `city_id` smallint(6) NOT NULL DEFAULT '0',
  `district_id` smallint(6) NOT NULL DEFAULT '0',
  `address` varchar(120) NOT NULL DEFAULT '',
  `mobile` varchar(60) NOT NULL DEFAULT '',
  `is_default` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `is_delete` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2190 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_admin"
#

DROP TABLE IF EXISTS `hiolabs_admin`;
CREATE TABLE `hiolabs_admin` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(25) NOT NULL DEFAULT '',
  `password` varchar(255) NOT NULL DEFAULT '',
  `password_salt` varchar(255) NOT NULL DEFAULT '',
  `last_login_ip` varchar(60) NOT NULL DEFAULT '',
  `last_login_time` int(11) NOT NULL DEFAULT '0',
  `is_delete` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_cart"
#

DROP TABLE IF EXISTS `hiolabs_cart`;
CREATE TABLE `hiolabs_cart` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` mediumint(8) unsigned NOT NULL DEFAULT '0',
  `goods_id` mediumint(8) unsigned NOT NULL DEFAULT '0',
  `goods_sn` varchar(60) NOT NULL DEFAULT '',
  `product_id` mediumint(8) unsigned NOT NULL DEFAULT '0',
  `goods_name` varchar(120) NOT NULL DEFAULT '',
  `goods_aka` varchar(120) NOT NULL DEFAULT '',
  `goods_weight` double(4,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '重量',
  `add_price` decimal(10,2) DEFAULT '0.00' COMMENT '加入购物车时的价格',
  `retail_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `number` smallint(5) unsigned NOT NULL DEFAULT '0',
  `goods_specifition_name_value` text NOT NULL COMMENT '规格属性组成的字符串，用来显示用',
  `goods_specifition_ids` varchar(60) NOT NULL DEFAULT '' COMMENT 'product表对应的goods_specifition_ids',
  `checked` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `list_pic_url` varchar(255) NOT NULL DEFAULT '',
  `freight_template_id` mediumint(8) unsigned NOT NULL COMMENT '运费模板',
  `is_on_sale` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0',
  `add_time` int(11) NOT NULL DEFAULT '0',
  `is_fast` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=22223 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_category"
#

DROP TABLE IF EXISTS `hiolabs_category`;
CREATE TABLE `hiolabs_category` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(90) NOT NULL DEFAULT '',
  `keywords` varchar(255) NOT NULL DEFAULT '',
  `front_desc` varchar(255) NOT NULL DEFAULT '',
  `parent_id` int(10) unsigned NOT NULL DEFAULT '0',
  `sort_order` tinyint(3) unsigned NOT NULL DEFAULT '50',
  `show_index` tinyint(1) NOT NULL DEFAULT '0',
  `is_show` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `icon_url` varchar(255) NOT NULL,
  `img_url` varchar(255) NOT NULL,
  `level` varchar(255) NOT NULL,
  `front_name` varchar(255) NOT NULL,
  `p_height` int(11) NOT NULL DEFAULT '0',
  `is_category` tinyint(1) NOT NULL DEFAULT '0',
  `is_channel` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `parent_id` (`parent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1036009 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_commission_config"
#

DROP TABLE IF EXISTS `hiolabs_commission_config`;
CREATE TABLE `hiolabs_commission_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_id` int(11) DEFAULT '0' COMMENT '商品分类ID，0表示全部',
  `goods_id` int(11) DEFAULT '0' COMMENT '商品ID，0表示按分类',
  `commission_rate` decimal(5,2) NOT NULL COMMENT '佣金比例',
  `level_multiplier` text COMMENT '等级倍数JSON {1:1.0,2:1.2,3:1.5,4:2.0}',
  `first_order_bonus` decimal(5,2) DEFAULT '0.00' COMMENT '首单额外奖励比例',
  `min_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低订单金额',
  `max_commission` decimal(10,2) DEFAULT '0.00' COMMENT '最高佣金限制，0表示无限制',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='佣金配置表';

#
# Structure for table "hiolabs_commission_log"
#

DROP TABLE IF EXISTS `hiolabs_commission_log`;
CREATE TABLE `hiolabs_commission_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `commission_change` decimal(10,2) NOT NULL COMMENT '佣金变动金额（正数为增加，负数为减少）',
  `commission_type` varchar(50) NOT NULL COMMENT '佣金类型：promotion推广佣金,bonus奖励佣金,withdraw提现,admin管理员操作',
  `source_id` int(11) DEFAULT NULL COMMENT '来源ID（订单ID、提现ID等）',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `balance_after` decimal(10,2) NOT NULL COMMENT '变动后余额',
  `status` enum('pending','completed','failed') DEFAULT 'completed' COMMENT '状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `commission_type` (`commission_type`),
  KEY `source_id` (`source_id`),
  KEY `created_at` (`created_at`),
  KEY `idx_commission_log_type_user` (`commission_type`,`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COMMENT='佣金变动日志表';

#
# Structure for table "hiolabs_commission_settlement_log"
#

DROP TABLE IF EXISTS `hiolabs_commission_settlement_log`;
CREATE TABLE `hiolabs_commission_settlement_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `promotion_order_id` int(11) NOT NULL COMMENT '推广订单ID',
  `promoter_user_id` int(11) NOT NULL COMMENT '推广员用户ID',
  `commission_amount` decimal(10,2) NOT NULL COMMENT '佣金金额',
  `settlement_type` varchar(20) NOT NULL COMMENT '结算类型 auto自动 manual手动 cancel取消',
  `admin_user_id` int(11) DEFAULT '0' COMMENT '操作管理员ID',
  `reason` varchar(255) DEFAULT '' COMMENT '操作原因',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_promotion_order_id` (`promotion_order_id`),
  KEY `idx_promoter_user_id` (`promoter_user_id`),
  KEY `idx_settlement_type` (`settlement_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='佣金结算日志表';

#
# Structure for table "hiolabs_coupon_usage_logs"
#

DROP TABLE IF EXISTS `hiolabs_coupon_usage_logs`;
CREATE TABLE `hiolabs_coupon_usage_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `coupon_id` int(11) NOT NULL,
  `user_coupon_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `original_amount` decimal(10,2) NOT NULL COMMENT '原订单金额',
  `discount_amount` decimal(10,2) NOT NULL COMMENT '优惠金额',
  `final_amount` decimal(10,2) NOT NULL COMMENT '最终金额',
  `used_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_order` (`user_id`,`order_id`),
  KEY `idx_coupon` (`coupon_id`),
  KEY `idx_user_coupon` (`user_coupon_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券使用日志表';

#
# Structure for table "hiolabs_coupons"
#

DROP TABLE IF EXISTS `hiolabs_coupons`;
CREATE TABLE `hiolabs_coupons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '优惠券名称',
  `code` varchar(50) DEFAULT NULL COMMENT '优惠券代码',
  `type` enum('newuser','full_reduction') NOT NULL COMMENT '类型：newuser新人券，full_reduction满减券',
  `discount_type` enum('fixed','percent') NOT NULL COMMENT '优惠类型：fixed固定金额，percent百分比',
  `discount_value` decimal(10,2) NOT NULL COMMENT '优惠值',
  `min_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低消费金额',
  `max_discount` decimal(10,2) DEFAULT NULL COMMENT '最大优惠金额(百分比券用)',
  `total_quantity` int(11) DEFAULT '-1' COMMENT '发放总数量，-1为无限制',
  `per_user_limit` int(11) DEFAULT '1' COMMENT '每用户限领数量',
  `start_time` datetime NOT NULL COMMENT '有效期开始时间',
  `end_time` datetime NOT NULL COMMENT '有效期结束时间',
  `valid_days` int(11) DEFAULT NULL COMMENT '领取后有效天数，NULL表示使用固定时间',
  `status` enum('active','disabled','expired') DEFAULT 'active' COMMENT '状态',
  `description` text COMMENT '使用说明',
  `auto_distribute` tinyint(1) DEFAULT '0' COMMENT '是否自动发放给新用户',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` tinyint(1) DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_type_status` (`type`,`status`),
  KEY `idx_time` (`start_time`,`end_time`),
  KEY `idx_auto_distribute` (`auto_distribute`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='优惠券模板表';

#
# Structure for table "hiolabs_except_area"
#

DROP TABLE IF EXISTS `hiolabs_except_area`;
CREATE TABLE `hiolabs_except_area` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `content` varchar(255) NOT NULL COMMENT '名称',
  `area` varchar(3000) NOT NULL DEFAULT '0' COMMENT '0位默认，',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_except_area_detail"
#

DROP TABLE IF EXISTS `hiolabs_except_area_detail`;
CREATE TABLE `hiolabs_except_area_detail` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `except_area_id` int(11) NOT NULL DEFAULT '0',
  `area` int(11) NOT NULL DEFAULT '0' COMMENT '0位默认，',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=62 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_footprint"
#

DROP TABLE IF EXISTS `hiolabs_footprint`;
CREATE TABLE `hiolabs_footprint` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT '0',
  `goods_id` int(11) NOT NULL DEFAULT '0',
  `add_time` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15738 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_formid"
#

DROP TABLE IF EXISTS `hiolabs_formid`;
CREATE TABLE `hiolabs_formid` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `form_id` varchar(255) NOT NULL,
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `use_times` tinyint(1) NOT NULL DEFAULT '0' COMMENT '使用次数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3985 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_freight_template"
#

DROP TABLE IF EXISTS `hiolabs_freight_template`;
CREATE TABLE `hiolabs_freight_template` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(120) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '运费模板名称',
  `package_price` decimal(5,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '包装费用',
  `freight_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0按件，1按重量',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_freight_template_detail"
#

DROP TABLE IF EXISTS `hiolabs_freight_template_detail`;
CREATE TABLE `hiolabs_freight_template_detail` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `template_id` int(11) NOT NULL DEFAULT '0',
  `group_id` int(11) NOT NULL DEFAULT '0',
  `area` int(11) NOT NULL DEFAULT '0' COMMENT '0位默认，',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=263 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_freight_template_group"
#

DROP TABLE IF EXISTS `hiolabs_freight_template_group`;
CREATE TABLE `hiolabs_freight_template_group` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `template_id` int(11) NOT NULL DEFAULT '0',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '默认，area:0',
  `area` varchar(3000) NOT NULL DEFAULT '0' COMMENT '0位默认，',
  `start` int(11) NOT NULL DEFAULT '1',
  `start_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
  `add` int(11) NOT NULL DEFAULT '1',
  `add_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
  `free_by_number` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0没有设置',
  `free_by_money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '0没设置',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=81 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_goods"
#

DROP TABLE IF EXISTS `hiolabs_goods`;
CREATE TABLE `hiolabs_goods` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `category_id` int(10) unsigned NOT NULL DEFAULT '0',
  `is_on_sale` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `name` varchar(120) NOT NULL DEFAULT '',
  `goods_number` mediumint(8) unsigned NOT NULL DEFAULT '0',
  `sell_volume` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '销售量',
  `keywords` varchar(255) NOT NULL DEFAULT '',
  `retail_price` varchar(100) NOT NULL DEFAULT '0.00' COMMENT '零售价格',
  `min_retail_price` decimal(10,2) DEFAULT '0.00',
  `cost_price` varchar(100) NOT NULL DEFAULT '0.00',
  `min_cost_price` decimal(10,2) DEFAULT '0.00',
  `goods_brief` varchar(255) NOT NULL DEFAULT '',
  `goods_desc` text,
  `sort_order` smallint(5) unsigned NOT NULL DEFAULT '100',
  `is_index` tinyint(1) DEFAULT '0',
  `is_new` tinyint(1) DEFAULT '0',
  `goods_unit` varchar(45) NOT NULL COMMENT '商品单位',
  `https_pic_url` varchar(255) NOT NULL DEFAULT '0' COMMENT '商品https图',
  `list_pic_url` varchar(255) NOT NULL COMMENT '商品列表图',
  `freight_template_id` int(11) DEFAULT '0',
  `freight_type` tinyint(1) DEFAULT '0',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `has_gallery` tinyint(1) DEFAULT '0',
  `has_done` tinyint(1) DEFAULT '0',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `cat_id` (`category_id`) USING BTREE,
  KEY `goods_number` (`goods_number`) USING BTREE,
  KEY `sort_order` (`sort_order`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1181010 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_goods_distribution"
#

DROP TABLE IF EXISTS `hiolabs_goods_distribution`;
CREATE TABLE `hiolabs_goods_distribution` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `goods_id` int(10) unsigned NOT NULL,
  `is_distributed` tinyint(1) NOT NULL DEFAULT '0',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '0.00',
  `commission_type` varchar(20) NOT NULL DEFAULT 'default',
  `personal_rate` decimal(5,2) DEFAULT '0.00',
  `level1_rate` decimal(5,2) DEFAULT '0.00',
  `level2_rate` decimal(5,2) DEFAULT '0.00',
  `team_leader_rate` decimal(5,2) DEFAULT '0.00',
  `min_level_required` tinyint(1) DEFAULT '1',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `goods_id` (`goods_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_goods_gallery"
#

DROP TABLE IF EXISTS `hiolabs_goods_gallery`;
CREATE TABLE `hiolabs_goods_gallery` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `goods_id` int(10) unsigned NOT NULL DEFAULT '0',
  `img_url` varchar(255) NOT NULL DEFAULT '',
  `img_desc` varchar(255) NOT NULL DEFAULT '',
  `sort_order` int(10) unsigned NOT NULL DEFAULT '5',
  `is_delete` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `goods_id` (`goods_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=763 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_goods_specification"
#

DROP TABLE IF EXISTS `hiolabs_goods_specification`;
CREATE TABLE `hiolabs_goods_specification` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `goods_id` int(10) unsigned NOT NULL DEFAULT '0',
  `specification_id` int(10) unsigned NOT NULL DEFAULT '0',
  `value` varchar(50) NOT NULL DEFAULT '',
  `pic_url` varchar(255) NOT NULL DEFAULT '',
  `is_delete` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `goods_id` (`goods_id`) USING BTREE,
  KEY `specification_id` (`specification_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=152 DEFAULT CHARSET=utf8mb4 COMMENT='商品对应规格表值表';

#
# Structure for table "hiolabs_keywords"
#

DROP TABLE IF EXISTS `hiolabs_keywords`;
CREATE TABLE `hiolabs_keywords` (
  `keyword` varchar(90) NOT NULL DEFAULT '',
  `is_hot` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `is_default` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `is_show` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `sort_order` int(10) unsigned NOT NULL DEFAULT '100',
  `scheme _url` varchar(255) NOT NULL DEFAULT '' COMMENT '关键词的跳转链接',
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='热闹关键词表';

#
# Structure for table "hiolabs_notice"
#

DROP TABLE IF EXISTS `hiolabs_notice`;
CREATE TABLE `hiolabs_notice` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content` varchar(255) NOT NULL DEFAULT '0',
  `end_time` int(11) NOT NULL DEFAULT '0',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=112 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_order"
#

DROP TABLE IF EXISTS `hiolabs_order`;
CREATE TABLE `hiolabs_order` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `order_sn` varchar(20) NOT NULL DEFAULT '',
  `user_id` mediumint(8) unsigned NOT NULL DEFAULT '0',
  `order_status` smallint(5) unsigned NOT NULL DEFAULT '0' COMMENT '101：未付款、102：已取消、103已取消(系统)、201：已付款、202：订单取消，退款中、203：已退款、301：已发货、302：已收货、303：已收货(系统)、401：已完成、801：拼团中,未付款、802：拼团中,已付款',
  `offline_pay` tinyint(3) unsigned DEFAULT '0' COMMENT '线下支付订单标志',
  `shipping_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '0未发货，1已发货',
  `print_status` tinyint(1) NOT NULL DEFAULT '0',
  `pay_status` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `consignee` varchar(60) NOT NULL DEFAULT '',
  `country` smallint(5) unsigned NOT NULL DEFAULT '0',
  `province` smallint(5) unsigned NOT NULL DEFAULT '0',
  `city` smallint(5) unsigned NOT NULL DEFAULT '0',
  `district` smallint(5) unsigned NOT NULL DEFAULT '0',
  `address` varchar(255) NOT NULL DEFAULT '',
  `print_info` varchar(255) NOT NULL DEFAULT '',
  `mobile` varchar(60) NOT NULL DEFAULT '',
  `postscript` varchar(255) NOT NULL DEFAULT '',
  `admin_memo` varchar(255) CHARACTER SET utf8 DEFAULT NULL,
  `shipping_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '免邮的商品的邮费，这个在退款时不能退给客户',
  `pay_name` varchar(120) NOT NULL DEFAULT '',
  `pay_id` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '0',
  `change_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '0没改价，不等于0改过价格，这里记录原始的价格',
  `actual_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '实际需要支付的金额',
  `order_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单总价',
  `goods_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品总价',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0',
  `pay_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '付款时间',
  `shipping_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '发货时间',
  `confirm_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '确认时间',
  `dealdone_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '成交时间，用户评论或自动好评时间',
  `freight_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '配送费用',
  `express_value` decimal(10,2) NOT NULL DEFAULT '480.00' COMMENT '顺丰保价金额',
  `remark` varchar(255) NOT NULL DEFAULT '需电联客户请优先派送勿放快递柜',
  `order_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '订单类型：0普通，1秒杀，2团购，3返现订单,7充值，8会员',
  `is_delete` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '订单删除标志',
  `service_flag_color` varchar(20) DEFAULT NULL COMMENT '客服旗子颜色标识',
  `service_priority` tinyint(1) DEFAULT '0' COMMENT '优先级：0普通，1重要，2紧急',
  `service_updated_at` int(10) DEFAULT NULL COMMENT '客服备注更新时间',
  `service_updated_by` varchar(50) DEFAULT NULL COMMENT '客服备注更新人',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `order_sn` (`order_sn`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE,
  KEY `order_status` (`order_status`) USING BTREE,
  KEY `shipping_status` (`shipping_status`) USING BTREE,
  KEY `pay_status` (`pay_status`) USING BTREE,
  KEY `pay_id` (`pay_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1446 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_order_express"
#

DROP TABLE IF EXISTS `hiolabs_order_express`;
CREATE TABLE `hiolabs_order_express` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` mediumint(8) unsigned NOT NULL DEFAULT '0',
  `shipper_id` mediumint(8) unsigned NOT NULL DEFAULT '0',
  `shipper_name` varchar(120) NOT NULL DEFAULT '' COMMENT '物流公司名称',
  `shipper_code` varchar(60) NOT NULL DEFAULT '' COMMENT '物流公司代码',
  `logistic_code` varchar(40) NOT NULL DEFAULT '' COMMENT '快递单号',
  `traces` varchar(2000) NOT NULL DEFAULT '' COMMENT '物流跟踪信息',
  `is_finish` tinyint(1) NOT NULL DEFAULT '0',
  `request_count` int(11) DEFAULT '0' COMMENT '总查询次数',
  `request_time` int(11) DEFAULT '0' COMMENT '最近一次向第三方查询物流信息时间',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `express_type` tinyint(1) NOT NULL DEFAULT '0',
  `region_code` varchar(10) NOT NULL DEFAULT '0' COMMENT '快递的地区编码，如杭州571',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `order_id` (`order_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=130 DEFAULT CHARSET=utf8mb4 COMMENT='订单物流信息表，发货时生成';

#
# Structure for table "hiolabs_order_goods"
#

DROP TABLE IF EXISTS `hiolabs_order_goods`;
CREATE TABLE `hiolabs_order_goods` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `order_id` mediumint(8) unsigned NOT NULL DEFAULT '0',
  `goods_id` mediumint(8) unsigned NOT NULL DEFAULT '0',
  `goods_name` varchar(120) NOT NULL DEFAULT '',
  `goods_aka` varchar(120) NOT NULL,
  `product_id` mediumint(8) unsigned NOT NULL DEFAULT '0',
  `number` smallint(5) unsigned NOT NULL DEFAULT '1',
  `retail_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `goods_specifition_name_value` text NOT NULL,
  `goods_specifition_ids` varchar(255) NOT NULL DEFAULT '',
  `list_pic_url` varchar(255) NOT NULL DEFAULT '',
  `user_id` mediumint(8) unsigned NOT NULL DEFAULT '0',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除标志',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `order_id` (`order_id`) USING BTREE,
  KEY `goods_id` (`goods_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1617 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_personal_promoters"
#

DROP TABLE IF EXISTS `hiolabs_personal_promoters`;
CREATE TABLE `hiolabs_personal_promoters` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `level` tinyint(1) DEFAULT '1' COMMENT '推广员等级 1新手 2优秀 3金牌 4钻石',
  `total_views` int(11) DEFAULT '0' COMMENT '总浏览次数',
  `total_orders` int(11) DEFAULT '0' COMMENT '总成交次数',
  `total_commission` decimal(10,2) DEFAULT '0.00' COMMENT '总佣金积分',
  `month_views` int(11) DEFAULT '0' COMMENT '本月浏览次数',
  `month_orders` int(11) DEFAULT '0' COMMENT '本月成交次数',
  `month_commission` decimal(10,2) DEFAULT '0.00' COMMENT '本月佣金积分',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态 1正常 0禁用',
  `first_share_time` int(11) NOT NULL COMMENT '首次分享时间',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  `parent_user_id` int(11) DEFAULT NULL COMMENT '上级推广员用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_level` (`level`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='个人推广员表';

#
# Structure for table "hiolabs_product"
#

DROP TABLE IF EXISTS `hiolabs_product`;
CREATE TABLE `hiolabs_product` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `goods_id` mediumint(8) unsigned NOT NULL DEFAULT '0',
  `goods_specification_ids` varchar(50) NOT NULL DEFAULT '',
  `goods_sn` varchar(60) NOT NULL DEFAULT '',
  `goods_number` mediumint(8) unsigned NOT NULL DEFAULT '0',
  `retail_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00',
  `cost` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '成本',
  `goods_weight` double(6,2) NOT NULL DEFAULT '0.00' COMMENT '重量',
  `has_change` tinyint(1) NOT NULL DEFAULT '0',
  `goods_name` varchar(120) NOT NULL,
  `is_on_sale` tinyint(1) NOT NULL DEFAULT '1',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=326 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_promoter_levels"
#

DROP TABLE IF EXISTS `hiolabs_promoter_levels`;
CREATE TABLE `hiolabs_promoter_levels` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `level` tinyint(1) NOT NULL COMMENT '等级',
  `name` varchar(50) NOT NULL COMMENT '等级名称',
  `min_orders` int(11) NOT NULL COMMENT '最低成交单数',
  `max_orders` int(11) DEFAULT '0' COMMENT '最高成交单数，0表示无上限',
  `commission_multiplier` decimal(10,2) DEFAULT '10.00' COMMENT '每单获得佣金（元）',
  `bonus_points` decimal(10,2) DEFAULT '0.00' COMMENT '升级奖励佣金（元）',
  `privileges` text COMMENT '特权描述JSON',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_level` (`level`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='推广员等级配置表';

#
# Structure for table "hiolabs_promotion_orders"
#

DROP TABLE IF EXISTS `hiolabs_promotion_orders`;
CREATE TABLE `hiolabs_promotion_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `promoter_id` int(11) NOT NULL COMMENT '推广员ID',
  `promoter_user_id` int(11) NOT NULL COMMENT '推广员用户ID',
  `buyer_user_id` int(11) NOT NULL COMMENT '购买者用户ID',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `order_sn` varchar(64) NOT NULL COMMENT '订单号',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `goods_name` varchar(255) NOT NULL COMMENT '商品名称',
  `goods_price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `order_amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `commission_rate` decimal(5,2) NOT NULL COMMENT '佣金比例',
  `commission_amount` decimal(10,2) NOT NULL COMMENT '佣金金额',
  `personal_commission` decimal(10,2) DEFAULT '0.00' COMMENT '个人佣金',
  `level1_commission` decimal(10,2) DEFAULT '0.00' COMMENT '一级佣金',
  `level2_commission` decimal(10,2) DEFAULT '0.00' COMMENT '二级佣金',
  `team_leader_commission` decimal(10,2) DEFAULT '0.00' COMMENT '团长佣金',
  `share_source` varchar(50) NOT NULL COMMENT '分享来源',
  `commission_source` varchar(20) DEFAULT 'share' COMMENT '佣金来源：share分享有礼，distribution分销池',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态 pending待结算 settled已结算 cancelled已取消',
  `is_first_order` tinyint(1) DEFAULT '0' COMMENT '是否首次购买',
  `settle_time` int(11) DEFAULT '0' COMMENT '结算时间',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) NOT NULL COMMENT '更新时间',
  `parent_promoter_user_id` int(11) DEFAULT NULL COMMENT '上级推广员用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_goods` (`order_id`,`goods_id`),
  KEY `idx_promoter_id` (`promoter_id`),
  KEY `idx_promoter_user_id` (`promoter_user_id`),
  KEY `idx_buyer_user_id` (`buyer_user_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_status` (`status`),
  KEY `idx_promotion_orders_commission_source` (`commission_source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推广订单表';

#
# Structure for table "hiolabs_promotion_visits"
#

DROP TABLE IF EXISTS `hiolabs_promotion_visits`;
CREATE TABLE `hiolabs_promotion_visits` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `promoter_id` int(11) NOT NULL COMMENT '推广员ID',
  `promoter_user_id` int(11) NOT NULL COMMENT '推广员用户ID',
  `visitor_user_id` int(11) DEFAULT '0' COMMENT '访问者用户ID',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `share_source` varchar(50) NOT NULL COMMENT '分享来源',
  `visit_time` int(11) NOT NULL COMMENT '访问时间',
  `ip_address` varchar(45) DEFAULT '' COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `is_new_visitor` tinyint(1) DEFAULT '0' COMMENT '是否新访客',
  PRIMARY KEY (`id`),
  KEY `idx_promoter_id` (`promoter_id`),
  KEY `idx_promoter_user_id` (`promoter_user_id`),
  KEY `idx_visitor_user_id` (`visitor_user_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_visit_time` (`visit_time`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='推广访问记录表';

#
# Structure for table "hiolabs_region"
#

DROP TABLE IF EXISTS `hiolabs_region`;
CREATE TABLE `hiolabs_region` (
  `id` smallint(5) unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` smallint(5) unsigned NOT NULL DEFAULT '0',
  `name` varchar(120) NOT NULL DEFAULT '',
  `type` tinyint(1) NOT NULL DEFAULT '2',
  `agency_id` smallint(5) unsigned NOT NULL DEFAULT '0',
  `area` smallint(5) unsigned NOT NULL DEFAULT '0' COMMENT '方位，根据这个定运费',
  `area_code` varchar(10) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '方位代码',
  `far_area` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '偏远地区',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `parent_id` (`parent_id`) USING BTREE,
  KEY `region_type` (`type`) USING BTREE,
  KEY `agency_id` (`agency_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4047 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_search_history"
#

DROP TABLE IF EXISTS `hiolabs_search_history`;
CREATE TABLE `hiolabs_search_history` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `keyword` char(50) NOT NULL,
  `from` varchar(45) NOT NULL DEFAULT '' COMMENT '搜索来源，如PC、小程序、APP等',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '搜索时间',
  `user_id` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6254 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_settings"
#

DROP TABLE IF EXISTS `hiolabs_settings`;
CREATE TABLE `hiolabs_settings` (
  `id` mediumint(9) NOT NULL AUTO_INCREMENT,
  `autoDelivery` tinyint(1) NOT NULL DEFAULT '0',
  `Name` varchar(100) NOT NULL,
  `Tel` varchar(20) NOT NULL,
  `ProvinceName` varchar(20) NOT NULL,
  `CityName` varchar(20) NOT NULL,
  `ExpAreaName` varchar(20) NOT NULL,
  `Address` varchar(20) NOT NULL,
  `discovery_img_height` int(11) NOT NULL DEFAULT '0',
  `discovery_img` varchar(255) NOT NULL DEFAULT '',
  `goods_id` int(11) NOT NULL DEFAULT '0',
  `city_id` int(11) NOT NULL DEFAULT '0',
  `province_id` int(11) NOT NULL DEFAULT '0',
  `district_id` int(11) NOT NULL DEFAULT '0',
  `countdown` int(11) NOT NULL DEFAULT '0' COMMENT '10分钟倒计时',
  `reset` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_shipper"
#

DROP TABLE IF EXISTS `hiolabs_shipper`;
CREATE TABLE `hiolabs_shipper` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(20) NOT NULL DEFAULT '' COMMENT '快递公司名称',
  `code` varchar(10) NOT NULL DEFAULT '' COMMENT '快递公司代码',
  `sort_order` int(11) NOT NULL DEFAULT '10' COMMENT '排序',
  `MonthCode` varchar(100) DEFAULT NULL,
  `CustomerName` varchar(100) DEFAULT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `hiolabs_shipper_id_uindex` (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COMMENT='快递公司';

#
# Structure for table "hiolabs_show_settings"
#

DROP TABLE IF EXISTS `hiolabs_show_settings`;
CREATE TABLE `hiolabs_show_settings` (
  `id` mediumint(9) NOT NULL AUTO_INCREMENT,
  `banner` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '滚动banner',
  `channel` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启menu,几个图标',
  `index_banner_img` tinyint(1) NOT NULL DEFAULT '0' COMMENT '首页的img图片是否显示',
  `notice` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_specification"
#

DROP TABLE IF EXISTS `hiolabs_specification`;
CREATE TABLE `hiolabs_specification` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(60) NOT NULL DEFAULT '',
  `sort_order` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `memo` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '说明',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COMMENT='规格表，存的是各种规格，如规格，重量，颜色，包装等';

#
# Structure for table "hiolabs_user"
#

DROP TABLE IF EXISTS `hiolabs_user`;
CREATE TABLE `hiolabs_user` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `nickname` varchar(1024) NOT NULL,
  `name` varchar(60) NOT NULL DEFAULT '',
  `username` varchar(60) NOT NULL DEFAULT '',
  `password` varchar(32) NOT NULL DEFAULT '',
  `gender` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `birthday` int(10) unsigned NOT NULL DEFAULT '0',
  `register_time` int(10) unsigned NOT NULL DEFAULT '0',
  `last_login_time` int(10) unsigned NOT NULL DEFAULT '0',
  `last_login_ip` varchar(15) NOT NULL DEFAULT '',
  `mobile` varchar(20) NOT NULL,
  `register_ip` varchar(45) NOT NULL DEFAULT '',
  `avatar` varchar(255) NOT NULL DEFAULT '',
  `weixin_openid` varchar(50) NOT NULL DEFAULT '',
  `name_mobile` tinyint(1) NOT NULL DEFAULT '0',
  `country` varchar(255) DEFAULT '0',
  `province` varchar(100) DEFAULT '0',
  `city` varchar(100) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5665 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "hiolabs_user_commission"
#

DROP TABLE IF EXISTS `hiolabs_user_commission`;
CREATE TABLE `hiolabs_user_commission` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `total_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总佣金',
  `available_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '可用佣金',
  `frozen_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '冻结佣金',
  `withdrawn_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '已提现佣金',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `available_commission` (`available_commission`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COMMENT='用户佣金账户表';

#
# Structure for table "hiolabs_user_coupons"
#

DROP TABLE IF EXISTS `hiolabs_user_coupons`;
CREATE TABLE `hiolabs_user_coupons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `coupon_id` int(11) NOT NULL COMMENT '优惠券模板ID',
  `coupon_code` varchar(50) NOT NULL COMMENT '优惠券唯一码',
  `status` enum('unused','used','expired') DEFAULT 'unused' COMMENT '状态',
  `received_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
  `used_at` timestamp NULL DEFAULT NULL COMMENT '使用时间',
  `expire_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `order_id` int(11) DEFAULT NULL COMMENT '使用的订单ID',
  `source` enum('manual','auto','batch') DEFAULT 'manual' COMMENT '获取来源：manual手动领取，auto自动发放，batch批量发放',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_coupon_code` (`coupon_code`),
  KEY `idx_user_status` (`user_id`,`status`),
  KEY `idx_coupon_id` (`coupon_id`),
  KEY `idx_expire` (`expire_at`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='用户优惠券表';

#
# Structure for table "points_log"
#

DROP TABLE IF EXISTS `points_log`;
CREATE TABLE `points_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `points_change` int(11) NOT NULL COMMENT '积分变动(正数增加,负数减少)',
  `points_type` enum('signin','bonus','consume','expire','admin','promotion','upgrade') DEFAULT 'signin' COMMENT '积分类型',
  `source_id` int(11) DEFAULT NULL COMMENT '来源ID(如签到记录ID)',
  `description` varchar(255) DEFAULT NULL COMMENT '变动描述',
  `balance_after` int(11) NOT NULL COMMENT '变动后余额',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`points_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=MyISAM AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "sign_in_records"
#

DROP TABLE IF EXISTS `sign_in_records`;
CREATE TABLE `sign_in_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `sign_date` date DEFAULT NULL COMMENT '签到日期',
  `points_earned` int(11) DEFAULT '0' COMMENT '获得积分',
  `consecutive_days` int(11) DEFAULT '1' COMMENT '连续签到天数',
  `is_bonus` tinyint(4) DEFAULT '0' COMMENT '是否为奖励签到(连续奖励)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_date` (`user_id`,`sign_date`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_sign_date` (`sign_date`)
) ENGINE=MyISAM AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4;

#
# Structure for table "user_points"
#

DROP TABLE IF EXISTS `user_points`;
CREATE TABLE `user_points` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `total_points` int(11) DEFAULT '0' COMMENT '总积分',
  `available_points` int(11) DEFAULT '0' COMMENT '可用积分',
  `used_points` int(11) DEFAULT '0' COMMENT '已使用积分',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=MyISAM AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4;
