# 佣金系统测试说明

## 🎯 系统改造概述

我们已经将原来的积分奖励系统改造为真实的佣金分销系统：

### ✅ 主要变更：

1. **积分 → 佣金**：推广成功后获得真实金钱而不是积分
2. **多级分销**：支持个人、一级、二级、团长四级佣金分配
3. **分销池控制**：只有投入分销池的商品才能获得佣金
4. **确认收货触发**：客户确认收货后才发放佣金

## 🗄️ 数据库设置

### 1. 运行数据库脚本
```bash
cd service
node setup_commission_system.js
```

### 2. 新增的表结构
- `hiolabs_user_commission` - 用户佣金账户表
- `hiolabs_commission_log` - 佣金变动日志表
- 更新现有表添加佣金相关字段

## 🔄 业务流程

### 1. 管理员操作
1. 在产品分销池页面投入商品
2. 设置各级佣金比例：
   - 个人佣金：8%（直接推广员）
   - 一级佣金：3%（推广员的上级）
   - 二级佣金：1%（推广员的上上级）
   - 团长佣金：2%（团队领导）

### 2. 用户推广流程
1. 用户在小程序看到"推广有佣金"
2. 生成推广链接/二维码
3. 分享给其他用户

### 3. 佣金发放流程
1. 客户通过推广链接购买商品
2. 系统记录推广关系到`promotion_orders`表
3. 客户确认收货后触发佣金计算
4. 根据商品的佣金比例计算各级佣金
5. 发放佣金到推广员账户

## 🧪 测试步骤

### 第一步：设置分销商品
1. 登录管理后台
2. 进入"产品分销池"页面
3. 选择商品点击"投入分销池"
4. 设置佣金比例（如：个人8%，一级3%，二级1%，团长2%）
5. 确认投入

### 第二步：生成推广链接
1. 在小程序中打开已投入分销池的商品
2. 看到"推广有佣金"按钮
3. 点击生成推广二维码或分享链接

### 第三步：模拟购买
1. 使用另一个用户扫码或点击推广链接
2. 购买商品并支付
3. 查看`promotion_orders`表确认推广记录

### 第四步：确认收货触发佣金
1. 在订单管理中将订单状态改为"确认收货"
2. 或者在小程序中点击"确认收货"
3. 系统自动计算并发放佣金

### 第五步：验证佣金发放
1. 查看`user_commission`表确认佣金余额
2. 查看`commission_log`表确认佣金记录
3. 查看推广员统计数据更新

## 📊 数据验证

### 检查佣金计算是否正确
```sql
-- 查看用户佣金账户
SELECT * FROM hiolabs_user_commission WHERE user_id = [推广员用户ID];

-- 查看佣金变动日志
SELECT * FROM hiolabs_commission_log WHERE user_id = [推广员用户ID] ORDER BY created_at DESC;

-- 查看推广订单记录
SELECT * FROM hiolabs_promotion_orders WHERE promoter_user_id = [推广员用户ID];
```

### 验证佣金计算公式
- 个人佣金 = 商品价格 × 个人佣金比例 ÷ 100
- 一级佣金 = 商品价格 × 一级佣金比例 ÷ 100
- 二级佣金 = 商品价格 × 二级佣金比例 ÷ 100
- 团长佣金 = 商品价格 × 团长佣金比例 ÷ 100

## ⚠️ 注意事项

1. **只有投入分销池的商品才能获得佣金**
2. **必须确认收货后才发放佣金**
3. **佣金金额保留2位小数**
4. **推广员必须是激活状态**
5. **避免自己推广自己的商品**

## 🔧 故障排查

### 如果佣金没有发放：
1. 检查商品是否在分销池中（`is_distributed = 1`）
2. 检查推广记录是否存在（`promotion_orders`表）
3. 检查订单是否确认收货（`order_status = 401`）
4. 检查推广员状态是否正常
5. 查看服务器日志确认错误信息

### 常见问题：
- **商品显示"该商品不支持推广"**：商品未投入分销池
- **点击推广无反应**：检查用户登录状态
- **佣金计算错误**：检查分销池中的佣金比例设置
- **佣金未到账**：检查订单是否确认收货
