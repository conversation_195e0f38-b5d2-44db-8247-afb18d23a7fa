{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleRoundsPage.vue?vue&type=style&index=0&id=52ebf3fc&scoped=true&lang=css&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSaleRoundsPage.vue", "mtime": 1753735541601}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZmxhc2gtc2FsZS1yb3VuZHMtcGFnZSB7CiAgbWluLWhlaWdodDogMTAwdmg7CiAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZmFmYjsKfQoKLnBhZ2UtaGVhZGVyIHsKICBwb3NpdGlvbjogc3RpY2t5OwogIHRvcDogMDsKICB6LWluZGV4OiAxMDsKfQoKLyog54q25oCB5oyH56S654Gv5Yqo55S7ICovCi5zdGF0dXMtaW5kaWNhdG9yIHsKICBwb3NpdGlvbjogcmVsYXRpdmU7Cn0KCi5zdGF0dXMtaW5kaWNhdG9yLmFjdGl2ZTo6YmVmb3JlIHsKICBjb250ZW50OiAnJzsKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgdG9wOiA1MCU7CiAgbGVmdDogNTAlOwogIHRyYW5zZm9ybTogdHJhbnNsYXRlKC01MCUsIC01MCUpOwogIHdpZHRoOiA4cHg7CiAgaGVpZ2h0OiA4cHg7CiAgYmFja2dyb3VuZC1jb2xvcjogI2VmNDQ0NDsKICBib3JkZXItcmFkaXVzOiA1MCU7CiAgYW5pbWF0aW9uOiBwdWxzZSAycyBpbmZpbml0ZTsKfQoKQGtleWZyYW1lcyBwdWxzZSB7CiAgMCUgewogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSkgc2NhbGUoMC45NSk7CiAgICBib3gtc2hhZG93OiAwIDAgMCAwIHJnYmEoMjM5LCA2OCwgNjgsIDAuNyk7CiAgfQoKICA3MCUgewogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSkgc2NhbGUoMSk7CiAgICBib3gtc2hhZG93OiAwIDAgMCAxMHB4IHJnYmEoMjM5LCA2OCwgNjgsIDApOwogIH0KCiAgMTAwJSB7CiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgtNTAlLCAtNTAlKSBzY2FsZSgwLjk1KTsKICAgIGJveC1zaGFkb3c6IDAgMCAwIDAgcmdiYSgyMzksIDY4LCA2OCwgMCk7CiAgfQp9CgovKiDlgJLorqHml7bmlbDlrZfliqjnlLsgKi8KLmNvdW50ZG93bi1udW1iZXIgewogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7Cn0KCi5jb3VudGRvd24tbnVtYmVyLnVyZ2VudCB7CiAgY29sb3I6ICNlZjQ0NDQ7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgYW5pbWF0aW9uOiBzaGFrZSAwLjVzIGVhc2UtaW4tb3V0Owp9CgpAa2V5ZnJhbWVzIHNoYWtlIHsKICAwJSwgMTAwJSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWCgwKTsgfQogIDI1JSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtMnB4KTsgfQogIDc1JSB7IHRyYW5zZm9ybTogdHJhbnNsYXRlWCgycHgpOyB9Cn0KCi8qIOW6k+WtmOi/m+W6puadoeWKqOeUuyAqLwouc3RvY2stcHJvZ3Jlc3MgewogIHRyYW5zaXRpb246IHdpZHRoIDAuNXMgZWFzZTsKfQoKLyog5Y2h54mH5oKs5YGc5pWI5p6cICovCi5yb3VuZC1jYXJkIHsKICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOwp9Cgoucm91bmQtY2FyZDpob3ZlciB7CiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpOwogIGJveC1zaGFkb3c6IDAgMTBweCAyNXB4IHJnYmEoMCwgMCwgMCwgMC4xKTsKfQo="}, {"version": 3, "sources": ["FlashSaleRoundsPage.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2tBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "FlashSaleRoundsPage.vue", "sourceRoot": "src/components/Marketing", "sourcesContent": ["<template>\n  <div class=\"flash-sale-rounds-page\">\n    <!-- 页面头部 -->\n    <div class=\"page-header bg-white shadow-sm border-b\">\n      <div class=\"px-6 py-4\">\n        <div class=\"flex items-center justify-between\">\n          <div>\n            <h1 class=\"text-2xl font-semibold text-gray-900\">轮次秒杀</h1>\n            <p class=\"text-sm text-gray-500 mt-1\">每5分钟一轮，间隔2分钟，可选择不同商品参与秒杀</p>\n          </div>\n          <div class=\"flex items-center space-x-3\">\n            <button @click=\"showAddModal = true\" class=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n              <i class=\"ri-add-line mr-2\"></i>\n              创建轮次\n            </button>\n            <button @click=\"showConfigModal = true\" class=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\">\n              <i class=\"ri-settings-line mr-2\"></i>\n              系统配置\n            </button>\n            <button @click=\"refreshData\" class=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n              <i class=\"ri-refresh-line mr-2\"></i>\n              刷新数据\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"p-6 max-w-7xl mx-auto\">\n      <!-- 统计卡片 -->\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center\">\n              <i class=\"ri-flashlight-line text-xl text-red-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm text-gray-500\">总轮次数</p>\n              <p class=\"text-2xl font-bold text-gray-900\">{{ statistics.totalRounds }}</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <i class=\"ri-play-circle-line text-xl text-green-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm text-gray-500\">进行中轮次</p>\n              <p class=\"text-2xl font-bold text-gray-900\">{{ statistics.activeRounds }}</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <i class=\"ri-shopping-cart-line text-xl text-blue-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm text-gray-500\">秒杀订单数</p>\n              <p class=\"text-2xl font-bold text-gray-900\">{{ statistics.totalOrders }}</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\n              <i class=\"ri-money-dollar-circle-line text-xl text-yellow-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm text-gray-500\">秒杀销售额</p>\n              <p class=\"text-2xl font-bold text-gray-900\">¥{{ statistics.totalSales }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 当前轮次状态 -->\n      <div class=\"bg-white rounded-lg shadow-sm border mb-6\">\n        <div class=\"px-6 py-4 border-b\">\n          <h2 class=\"text-lg font-semibold text-gray-900\">当前轮次状态</h2>\n        </div>\n        <div class=\"p-6\">\n          <div v-if=\"currentRounds.current.length > 0\" class=\"mb-6\">\n            <h3 class=\"text-md font-medium text-gray-900 mb-4\">🔥 进行中轮次</h3>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              <div v-for=\"round in currentRounds.current\" :key=\"round.id\" \n                   class=\"bg-red-50 border-2 border-red-200 rounded-lg p-4\">\n                <div class=\"flex items-center justify-between mb-3\">\n                  <span class=\"text-sm font-medium text-red-600\">第{{ round.round_number }}轮</span>\n                  <span class=\"px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full\">进行中</span>\n                </div>\n                <div class=\"mb-3\">\n                  <h4 class=\"font-medium text-gray-900\">{{ round.goods_name }}</h4>\n                  <div class=\"flex items-center space-x-2 mt-1\">\n                    <span class=\"text-lg font-bold text-red-600\">¥{{ round.flash_price }}</span>\n                    <span class=\"text-sm text-gray-500 line-through\">¥{{ round.original_price }}</span>\n                  </div>\n                </div>\n                <div class=\"mb-3\">\n                  <div class=\"flex justify-between text-sm text-gray-600 mb-1\">\n                    <span>库存进度</span>\n                    <span>{{ round.sold_count }}/{{ round.stock }}</span>\n                  </div>\n                  <div class=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div class=\"bg-red-600 h-2 rounded-full\" :style=\"{width: round.stockProgress + '%'}\"></div>\n                  </div>\n                </div>\n                <div class=\"text-center\">\n                  <div class=\"text-lg font-bold text-red-600\">{{ formatCountdown(round.countdown) }}</div>\n                  <div class=\"text-xs text-gray-500\">剩余时间</div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div v-if=\"currentRounds.upcoming.length > 0\">\n            <h3 class=\"text-md font-medium text-gray-900 mb-4\">⏰ 即将开始</h3>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              <div v-for=\"round in currentRounds.upcoming\" :key=\"round.id\" \n                   class=\"bg-orange-50 border-2 border-orange-200 rounded-lg p-4\">\n                <div class=\"flex items-center justify-between mb-3\">\n                  <span class=\"text-sm font-medium text-orange-600\">第{{ round.round_number }}轮</span>\n                  <span class=\"px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full\">即将开始</span>\n                </div>\n                <div class=\"mb-3\">\n                  <h4 class=\"font-medium text-gray-900\">{{ round.goods_name }}</h4>\n                  <div class=\"flex items-center space-x-2 mt-1\">\n                    <span class=\"text-lg font-bold text-orange-600\">¥{{ round.flash_price }}</span>\n                    <span class=\"text-sm text-gray-500 line-through\">¥{{ round.original_price }}</span>\n                  </div>\n                </div>\n                <div class=\"mb-3\">\n                  <div class=\"text-sm text-gray-600\">\n                    库存：{{ round.stock }}件 | 限购：{{ round.limit_quantity }}件\n                  </div>\n                </div>\n                <div class=\"text-center\">\n                  <div class=\"text-lg font-bold text-orange-600\">{{ formatCountdown(round.countdown) }}</div>\n                  <div class=\"text-xs text-gray-500\">开始倒计时</div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div v-if=\"currentRounds.current.length === 0 && currentRounds.upcoming.length === 0\" \n               class=\"text-center py-8 text-gray-500\">\n            <i class=\"ri-time-line text-4xl mb-2\"></i>\n            <p>暂无进行中或即将开始的轮次</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- 轮次列表 -->\n      <div class=\"bg-white rounded-lg shadow-sm border\">\n        <div class=\"px-6 py-4 border-b\">\n          <div class=\"flex items-center justify-between\">\n            <h2 class=\"text-lg font-semibold text-gray-900\">轮次列表</h2>\n            <div class=\"flex items-center space-x-2\">\n              <select v-model=\"filterStatus\" @change=\"loadRoundsList\" \n                      class=\"px-3 py-1 border border-gray-300 rounded-md text-sm\">\n                <option value=\"\">全部状态</option>\n                <option value=\"upcoming\">即将开始</option>\n                <option value=\"active\">进行中</option>\n                <option value=\"ended\">已结束</option>\n              </select>\n            </div>\n          </div>\n        </div>\n        <div class=\"overflow-x-auto\">\n          <table class=\"min-w-full divide-y divide-gray-200\">\n            <thead class=\"bg-gray-50\">\n              <tr>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">轮次</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">商品信息</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">价格</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">库存</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">时间</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">状态</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">倒计时</th>\n              </tr>\n            </thead>\n            <tbody class=\"bg-white divide-y divide-gray-200\">\n              <tr v-for=\"round in roundsList.data\" :key=\"round.id\">\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"text-sm font-medium text-gray-900\">第{{ round.round_number }}轮</div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"flex items-center\">\n                    <img v-if=\"round.goods_image\" :src=\"round.goods_image\" \n                         class=\"h-10 w-10 rounded-lg object-cover mr-3\" :alt=\"round.goods_name\">\n                    <div class=\"w-10 h-10 bg-gray-200 rounded-lg mr-3 flex items-center justify-center\" v-else>\n                      <i class=\"ri-image-line text-gray-400\"></i>\n                    </div>\n                    <div>\n                      <div class=\"text-sm font-medium text-gray-900\">{{ round.goods_name }}</div>\n                      <div class=\"text-sm text-gray-500\">ID: {{ round.goods_id }}</div>\n                    </div>\n                  </div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"text-sm font-medium text-red-600\">¥{{ round.flash_price }}</div>\n                  <div class=\"text-sm text-gray-500 line-through\">¥{{ round.original_price }}</div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"text-sm text-gray-900\">{{ round.sold_count }}/{{ round.stock }}</div>\n                  <div class=\"w-16 bg-gray-200 rounded-full h-1 mt-1\">\n                    <div class=\"bg-blue-600 h-1 rounded-full\" :style=\"{width: round.stockProgress + '%'}\"></div>\n                  </div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                  <div>{{ formatDateTime(round.start_time) }}</div>\n                  <div>{{ formatDateTime(round.end_time) }}</div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <span :class=\"getStatusClass(round.status)\" \n                        class=\"px-2 inline-flex text-xs leading-5 font-semibold rounded-full\">\n                    {{ getStatusText(round.status) }}\n                  </span>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                  <div v-if=\"round.countdown > 0\" class=\"font-medium\">\n                    {{ formatCountdown(round.countdown) }}\n                  </div>\n                  <div v-else class=\"text-gray-400\">-</div>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n        \n        <!-- 分页 -->\n        <div v-if=\"roundsList.total > 0\" class=\"px-6 py-4 border-t\">\n          <div class=\"flex items-center justify-between\">\n            <div class=\"text-sm text-gray-500\">\n              共 {{ roundsList.total }} 条记录，第 {{ roundsList.current_page }} / {{ roundsList.last_page }} 页\n            </div>\n            <div class=\"flex space-x-2\">\n              <button @click=\"changePage(roundsList.current_page - 1)\" \n                      :disabled=\"roundsList.current_page <= 1\"\n                      class=\"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50\">\n                上一页\n              </button>\n              <button @click=\"changePage(roundsList.current_page + 1)\" \n                      :disabled=\"roundsList.current_page >= roundsList.last_page\"\n                      class=\"px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50\">\n                下一页\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 创建轮次模态框 -->\n    <div v-if=\"showAddModal\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div class=\"bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h3 class=\"text-lg font-semibold text-gray-900\">创建秒杀轮次</h3>\n          <button @click=\"showAddModal = false\" class=\"text-gray-400 hover:text-gray-600\">\n            <i class=\"ri-close-line text-xl\"></i>\n          </button>\n        </div>\n\n        <form @submit.prevent=\"createRound\" class=\"space-y-4\">\n          <!-- 商品选择 -->\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 mb-2\">选择商品</label>\n            <select v-model=\"newRound.goods_id\" @change=\"onGoodsSelect\"\n                    class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    required>\n              <option value=\"\">请选择商品</option>\n              <option v-for=\"goods in goodsList\" :key=\"goods.id\" :value=\"goods.id\">\n                {{ goods.name }} - ¥{{ goods.retail_price }}\n              </option>\n            </select>\n          </div>\n\n          <!-- 商品预览 -->\n          <div v-if=\"newRound.goods_id\" class=\"p-4 bg-gray-50 rounded-lg\">\n            <div class=\"flex items-center space-x-4\">\n              <img v-if=\"newRound.goods_image\" :src=\"newRound.goods_image\"\n                   class=\"w-16 h-16 object-cover rounded-lg\" alt=\"商品图片\">\n              <div>\n                <div class=\"font-medium text-gray-900\">{{ newRound.goods_name }}</div>\n                <div class=\"text-sm text-gray-500\">原价: ¥{{ newRound.original_price }}</div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 价格设置 -->\n          <div class=\"grid grid-cols-2 gap-4\">\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">秒杀价格</label>\n              <input v-model=\"newRound.flash_price\" type=\"number\" step=\"0.01\" min=\"0\"\n                     class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                     placeholder=\"请输入秒杀价格\" required>\n            </div>\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">库存数量</label>\n              <input v-model=\"newRound.stock\" type=\"number\" min=\"1\"\n                     class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                     placeholder=\"请输入库存数量\" required>\n            </div>\n          </div>\n\n          <!-- 限购数量 -->\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 mb-2\">限购数量</label>\n            <input v-model=\"newRound.limit_quantity\" type=\"number\" min=\"1\"\n                   class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                   placeholder=\"每人限购数量\">\n          </div>\n\n          <!-- 提示信息 -->\n          <div class=\"p-4 bg-blue-50 rounded-lg\">\n            <div class=\"flex items-start\">\n              <i class=\"ri-information-line text-blue-500 mt-0.5 mr-2\"></i>\n              <div class=\"text-sm text-blue-700\">\n                <div class=\"font-medium mb-1\">轮次规则说明：</div>\n                <ul class=\"list-disc list-inside space-y-1\">\n                  <li>每轮秒杀持续5分钟</li>\n                  <li>轮次间隔2分钟</li>\n                  <li>系统将自动安排开始时间</li>\n                  <li>创建后立即进入排队状态</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          <!-- 操作按钮 -->\n          <div class=\"flex justify-end space-x-3 pt-4\">\n            <button type=\"button\" @click=\"showAddModal = false\"\n                    class=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50\">\n              取消\n            </button>\n            <button type=\"submit\"\n                    class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\">\n              创建轮次\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n\n    <!-- 系统配置模态框 -->\n    <div v-if=\"showConfigModal\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div class=\"bg-white rounded-lg p-6 w-full max-w-lg mx-4\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h3 class=\"text-lg font-semibold text-gray-900\">系统配置</h3>\n          <button @click=\"showConfigModal = false\" class=\"text-gray-400 hover:text-gray-600\">\n            <i class=\"ri-close-line text-xl\"></i>\n          </button>\n        </div>\n\n        <div class=\"space-y-4\">\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 mb-2\">轮次时长（秒）</label>\n            <input v-model=\"systemConfig.round_duration\" type=\"number\" min=\"60\"\n                   class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n          </div>\n\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 mb-2\">间隔时长（秒）</label>\n            <input v-model=\"systemConfig.break_duration\" type=\"number\" min=\"0\"\n                   class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n          </div>\n\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 mb-2\">运营时间</label>\n            <div class=\"grid grid-cols-2 gap-4\">\n              <input v-model=\"systemConfig.daily_start_time\" type=\"time\"\n                     class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n              <input v-model=\"systemConfig.daily_end_time\" type=\"time\"\n                     class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\">\n            </div>\n          </div>\n\n          <div class=\"flex items-center\">\n            <input v-model=\"systemConfig.is_enabled\" type=\"checkbox\" id=\"enabled\"\n                   class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\">\n            <label for=\"enabled\" class=\"ml-2 block text-sm text-gray-900\">启用秒杀系统</label>\n          </div>\n        </div>\n\n        <div class=\"flex justify-end space-x-3 pt-6\">\n          <button @click=\"showConfigModal = false\"\n                  class=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50\">\n            取消\n          </button>\n          <button @click=\"saveConfig\"\n                  class=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\">\n            保存配置\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FlashSaleRoundsPage',\n  data() {\n    return {\n      // 统计数据\n      statistics: {\n        totalRounds: 0,\n        activeRounds: 0,\n        upcomingRounds: 0,\n        endedRounds: 0,\n        totalOrders: 0,\n        todayOrders: 0,\n        totalSales: 0\n      },\n\n      // 当前轮次\n      currentRounds: {\n        current: [],\n        upcoming: [],\n        total: 0\n      },\n\n      // 轮次列表\n      roundsList: {\n        data: [],\n        total: 0,\n        current_page: 1,\n        last_page: 1\n      },\n\n      // 筛选状态\n      filterStatus: '',\n      currentPage: 1,\n\n      // 模态框状态\n      showAddModal: false,\n      showConfigModal: false,\n\n      // 新轮次表单\n      newRound: {\n        goods_id: '',\n        goods_name: '',\n        goods_image: '',\n        original_price: '',\n        flash_price: '',\n        stock: '',\n        limit_quantity: 1\n      },\n\n      // 商品列表\n      goodsList: [],\n\n      // 系统配置\n      systemConfig: {\n        round_duration: 300,\n        break_duration: 120,\n        auto_start_next: true,\n        daily_start_time: '09:00:00',\n        daily_end_time: '22:00:00',\n        is_enabled: true\n      },\n\n      // 定时器\n      refreshTimer: null,\n      countdownTimer: null\n    }\n  },\n\n  mounted() {\n    this.loadData();\n    this.startAutoRefresh();\n  },\n\n  beforeDestroy() {\n    this.stopAutoRefresh();\n  },\n\n  methods: {\n    // 加载所有数据\n    async loadData() {\n      await Promise.all([\n        this.loadStatistics(),\n        this.loadCurrentRounds(),\n        this.loadRoundsList(),\n        this.loadGoodsList(),\n        this.loadSystemConfig()\n      ]);\n    },\n\n    // 加载统计数据\n    async loadStatistics() {\n      try {\n        const response = await this.axios.get('flashsalerounds/statistics');\n        if (response.data.errno === 0) {\n          this.statistics = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载统计数据失败:', error);\n      }\n    },\n\n    // 加载当前轮次\n    async loadCurrentRounds() {\n      try {\n        const response = await this.axios.get('flashsalerounds/current');\n        if (response.data.errno === 0) {\n          this.currentRounds = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载当前轮次失败:', error);\n      }\n    },\n\n    // 加载轮次列表\n    async loadRoundsList() {\n      try {\n        const params = {\n          page: this.currentPage,\n          limit: 20\n        };\n\n        if (this.filterStatus) {\n          params.status = this.filterStatus;\n        }\n\n        const response = await this.axios.get('flashsalerounds/list', { params });\n        if (response.data.errno === 0) {\n          this.roundsList = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载轮次列表失败:', error);\n      }\n    },\n\n    // 加载商品列表\n    async loadGoodsList() {\n      try {\n        const response = await this.axios.get('flashsalerounds/goods');\n        if (response.data.errno === 0) {\n          this.goodsList = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载商品列表失败:', error);\n      }\n    },\n\n    // 加载系统配置\n    async loadSystemConfig() {\n      try {\n        const response = await this.axios.get('flashsalerounds/config');\n        if (response.data.errno === 0) {\n          this.systemConfig = response.data.data;\n        }\n      } catch (error) {\n        console.error('加载系统配置失败:', error);\n      }\n    },\n\n    // 创建新轮次\n    async createRound() {\n      try {\n        const response = await this.axios.post('flashsalerounds/create', this.newRound);\n        if (response.data.errno === 0) {\n          this.showAddModal = false;\n          this.$message.success('轮次创建成功');\n          this.loadData();\n          this.resetNewRound();\n        } else {\n          this.$message.error(response.data.errmsg || '创建失败');\n        }\n      } catch (error) {\n        console.error('创建轮次失败:', error);\n        this.$message.error('创建失败');\n      }\n    },\n\n    // 重置新轮次表单\n    resetNewRound() {\n      this.newRound = {\n        goods_id: '',\n        goods_name: '',\n        goods_image: '',\n        original_price: '',\n        flash_price: '',\n        stock: '',\n        limit_quantity: 1\n      };\n    },\n\n    // 选择商品\n    onGoodsSelect() {\n      const selectedGoods = this.goodsList.find(goods => goods.id == this.newRound.goods_id);\n      if (selectedGoods) {\n        this.newRound.goods_name = selectedGoods.name;\n        this.newRound.goods_image = selectedGoods.list_pic_url;\n        this.newRound.original_price = selectedGoods.retail_price;\n      }\n    },\n\n    // 分页\n    changePage(page) {\n      if (page >= 1 && page <= this.roundsList.last_page) {\n        this.currentPage = page;\n        this.loadRoundsList();\n      }\n    },\n\n    // 刷新数据\n    refreshData() {\n      this.loadData();\n      this.$message.success('数据刷新成功');\n    },\n\n    // 保存系统配置\n    async saveConfig() {\n      try {\n        const response = await this.axios.post('flashsalerounds/config', this.systemConfig);\n        if (response.data.errno === 0) {\n          this.showConfigModal = false;\n          this.$message.success('配置保存成功');\n        } else {\n          this.$message.error(response.data.errmsg || '保存失败');\n        }\n      } catch (error) {\n        console.error('保存配置失败:', error);\n        this.$message.error('保存失败');\n      }\n    },\n\n    // 开始自动刷新\n    startAutoRefresh() {\n      // 每10秒刷新一次当前轮次\n      this.refreshTimer = setInterval(() => {\n        this.loadCurrentRounds();\n        this.loadStatistics();\n      }, 10000);\n\n      // 每秒更新倒计时\n      this.countdownTimer = setInterval(() => {\n        this.updateCountdowns();\n      }, 1000);\n    },\n\n    // 停止自动刷新\n    stopAutoRefresh() {\n      if (this.refreshTimer) {\n        clearInterval(this.refreshTimer);\n        this.refreshTimer = null;\n      }\n      if (this.countdownTimer) {\n        clearInterval(this.countdownTimer);\n        this.countdownTimer = null;\n      }\n    },\n\n    // 更新倒计时\n    updateCountdowns() {\n      // 更新当前轮次倒计时\n      this.currentRounds.current.forEach(round => {\n        if (round.countdown > 0) {\n          round.countdown--;\n        }\n      });\n\n      this.currentRounds.upcoming.forEach(round => {\n        if (round.countdown > 0) {\n          round.countdown--;\n        }\n      });\n\n      // 更新列表中的倒计时\n      this.roundsList.data.forEach(round => {\n        if (round.countdown > 0) {\n          round.countdown--;\n        }\n      });\n    },\n\n    // 格式化倒计时\n    formatCountdown(seconds) {\n      if (seconds <= 0) return '00:00';\n\n      const minutes = Math.floor(seconds / 60);\n      const remainingSeconds = seconds % 60;\n\n      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n    },\n\n    // 格式化日期时间\n    formatDateTime(dateTime) {\n      if (!dateTime) return '-';\n      const date = new Date(dateTime);\n      return date.toLocaleString('zh-CN', {\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    // 获取状态样式\n    getStatusClass(status) {\n      const classes = {\n        'upcoming': 'bg-orange-100 text-orange-800',\n        'active': 'bg-red-100 text-red-800',\n        'ended': 'bg-gray-100 text-gray-800',\n        'cancelled': 'bg-gray-100 text-gray-800'\n      };\n      return classes[status] || 'bg-gray-100 text-gray-800';\n    },\n\n    // 获取状态文本\n    getStatusText(status) {\n      const texts = {\n        'upcoming': '即将开始',\n        'active': '进行中',\n        'ended': '已结束',\n        'cancelled': '已取消'\n      };\n      return texts[status] || '未知';\n    }\n  }\n}\n</script>\n\n<style scoped>\n.flash-sale-rounds-page {\n  min-height: 100vh;\n  background-color: #f9fafb;\n}\n\n.page-header {\n  position: sticky;\n  top: 0;\n  z-index: 10;\n}\n\n/* 状态指示灯动画 */\n.status-indicator {\n  position: relative;\n}\n\n.status-indicator.active::before {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 8px;\n  height: 8px;\n  background-color: #ef4444;\n  border-radius: 50%;\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    transform: translate(-50%, -50%) scale(0.95);\n    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);\n  }\n\n  70% {\n    transform: translate(-50%, -50%) scale(1);\n    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);\n  }\n\n  100% {\n    transform: translate(-50%, -50%) scale(0.95);\n    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);\n  }\n}\n\n/* 倒计时数字动画 */\n.countdown-number {\n  transition: all 0.3s ease;\n}\n\n.countdown-number.urgent {\n  color: #ef4444;\n  font-weight: bold;\n  animation: shake 0.5s ease-in-out;\n}\n\n@keyframes shake {\n  0%, 100% { transform: translateX(0); }\n  25% { transform: translateX(-2px); }\n  75% { transform: translateX(2px); }\n}\n\n/* 库存进度条动画 */\n.stock-progress {\n  transition: width 0.5s ease;\n}\n\n/* 卡片悬停效果 */\n.round-card {\n  transition: all 0.3s ease;\n}\n\n.round-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\n}\n</style>\n"]}]}