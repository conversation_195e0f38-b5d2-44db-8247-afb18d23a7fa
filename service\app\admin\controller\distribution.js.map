{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\distribution.js"], "names": ["Base", "require", "moment", "module", "exports", "goodsListAction", "page", "get", "size", "name", "categoryId", "isOnSale", "distributionStatus", "model", "whereMap", "is_delete", "category_id", "is_on_sale", "data", "where", "field", "order", "countSelect", "goodsIds", "map", "item", "id", "distributionConfigs", "goods_id", "select", "configMap", "for<PERSON>ach", "config", "categoryIds", "Set", "categories", "categoryMap", "cat", "processedData", "category_name", "distributionConfig", "is_distributed", "commission_rate", "commission_type", "personal_rate", "level1_rate", "level2_rate", "team_leader_rate", "min_level_required", "distribution_status", "estimated_commission", "parseFloat", "retail_price", "toFixed", "in_pool_time", "add_time", "unix", "format", "total_commission_rate", "push", "count", "length", "console", "log", "slice", "success", "categoriesAction", "is_show", "configAction", "goodsId", "find", "configs", "statusAction", "post", "isDistributed", "commissionRate", "commissionType", "personalRate", "level1Rate", "level2Rate", "teamLeaderRate", "minLevelRequired", "fail", "goods", "currentTime", "parseInt", "Date", "getTime", "existingConfig", "Object", "keys", "configData", "update_time", "operationText", "hasValidConfig", "updateResult", "update", "verifyConfig", "addResult", "add", "logDistributionOperation", "message", "goods_name", "operation", "commissionAction", "undefined", "statsAction", "totalGoods", "totalDistributed", "configuredGoods", "pendingGoods", "inactiveGoods", "distributedGoods", "alias", "join", "table", "as", "on", "totalCommission", "totalCommissionRate", "itemCommission", "premiumProducts", "hotProducts", "categoryStats", "group", "limit", "poolStats", "totalCapacity", "activeProducts", "inactiveProducts", "pendingProducts", "utilizationRate", "commissionStats", "totalCommissionPool", "averageCommissionRate", "estimatedMonthlyPayout", "productTiers", "basicProducts", "topCategories", "totalProducts", "distributedProducts", "undistributedProducts", "batchAction", "templateId", "Array", "isArray", "validGoods", "successCount", "failCount", "results", "template", "price", "status", "error", "logBatchOperation", "rulesAction", "rules", "minPrice", "maxPrice", "rate", "description", "defaultRate", "distributorsAction", "search", "level", "period", "userModel", "users", "user_id", "u", "is_active", "audit_status", "distributors", "distributor", "userInfo", "nickname", "phone", "mobile", "replace", "avatar", "levelInfo", "level_id", "level_name", "commission_bonus", "sales", "total_sales", "commission", "total_commission", "joinTime", "join_time", "distributorStatsAction", "totalDistributors", "teamLeaders", "totalCommissionResult", "sum", "totalSalesResult", "totalSales", "stats", "distributorDetailAction", "distributorId", "detail", "updateDistributorStatusAction", "isActive", "overviewAction", "endDate", "startDate", "setDate", "getDate", "startTimestamp", "endTimestamp", "order_status", "activeDistributors", "totalOrders", "conversionRate", "overview", "salesGrowth", "Math", "floor", "random", "commissionGrowth", "distributorGrowth", "conversionGrowth", "topProductsAction", "products", "product", "revenue", "sell_volume", "image", "list_pic_url", "topDistributorsAction", "chartAction", "type", "labels", "now", "i", "date", "toLocaleDateString", "month", "day", "realtimeAction", "today", "todayStart", "getFullYear", "getMonth", "todayStartTimestamp", "todayEndTimestamp", "todayOrders", "todayRevenueResult", "todayRevenue", "activeUsers", "realTimeData", "poolOverviewAction", "goodsData", "pool_status", "pool_status_text", "pool_join_time", "logData", "operation_data", "JSON", "stringify", "operator_id", "ctx", "state", "create_time", "summary", "goods_ids", "operation_summary", "recommendConfigAction", "sellVolume", "recommendedConfig", "reason", "key", "includes", "round", "goods_info", "recommended_config"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;;AAEAE,OAAOC,OAAP,GAAiB,cAAcJ,IAAd,CAAmB;AAChC;;;;AAIMK,mBAAN,GAAwB;AAAA;;AAAA;AACpB,kBAAMC,OAAO,MAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,MAAKD,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,kBAAME,OAAO,MAAKF,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,kBAAMG,aAAa,MAAKH,GAAL,CAAS,aAAT,KAA2B,EAA9C;AACA,kBAAMI,WAAW,MAAKJ,GAAL,CAAS,YAAT,KAA0B,EAA3C;AACA,kBAAMK,qBAAqB,MAAKL,GAAL,CAAS,qBAAT,KAAmC,EAA9D,CANoB,CAM8C;;AAElE,kBAAMM,QAAQ,MAAKA,KAAL,CAAW,OAAX,CAAd;AACA,gBAAIC,WAAW;AACXC,2BAAW;AADA,aAAf;;AAIA;AACA,gBAAIN,IAAJ,EAAU;AACNK,yBAASL,IAAT,GAAgB,CAAC,MAAD,EAAU,IAAGA,IAAK,GAAlB,CAAhB;AACH;AACD,gBAAIC,UAAJ,EAAgB;AACZI,yBAASE,WAAT,GAAuBN,UAAvB;AACH;AACD,gBAAIC,aAAa,EAAjB,EAAqB;AACjBG,yBAASG,UAAT,GAAsBN,QAAtB;AACH;;AAED;AACA,kBAAMO,OAAO,MAAML,MAAMM,KAAN,CAAYL,QAAZ,EACdM,KADc,CACR,mIADQ,EAEdC,KAFc,CAER,CAAC,gBAAD,EAAmB,SAAnB,CAFQ,EAGdf,IAHc,CAGTA,IAHS,EAGHE,IAHG,EAIdc,WAJc,EAAnB;;AAMA;AACA,kBAAMC,WAAWL,KAAKA,IAAL,CAAUM,GAAV,CAAc;AAAA,uBAAQC,KAAKC,EAAb;AAAA,aAAd,CAAjB;AACA,kBAAMC,sBAAsB,MAAM,MAAKd,KAAL,CAAW,oBAAX,EAC7BM,KAD6B,CACvB,EAAES,UAAU,CAAC,IAAD,EAAOL,QAAP,CAAZ,EADuB,EAE7BM,MAF6B,EAAlC;;AAIA;AACA,kBAAMC,YAAY,EAAlB;AACAH,gCAAoBI,OAApB,CAA4B,kBAAU;AAClCD,0BAAUE,OAAOJ,QAAjB,IAA6BI,MAA7B;AACH,aAFD;;AAIA;AACA,kBAAMC,cAAc,CAAC,GAAG,IAAIC,GAAJ,CAAQhB,KAAKA,IAAL,CAAUM,GAAV,CAAc;AAAA,uBAAQC,KAAKT,WAAb;AAAA,aAAd,CAAR,CAAJ,CAApB;AACA,kBAAMmB,aAAa,MAAM,MAAKtB,KAAL,CAAW,UAAX,EACpBM,KADoB,CACd,EAAEO,IAAI,CAAC,IAAD,EAAOO,WAAP,CAAN,EADc,EAEpBb,KAFoB,CAEd,SAFc,EAGpBS,MAHoB,EAAzB;;AAKA,kBAAMO,cAAc,EAApB;AACAD,uBAAWJ,OAAX,CAAmB,eAAO;AACtBK,4BAAYC,IAAIX,EAAhB,IAAsBW,IAAI5B,IAA1B;AACH,aAFD;;AAIA;AACA,kBAAM6B,gBAAgB,EAAtB;AACA,iBAAK,MAAMb,IAAX,IAAmBP,KAAKA,IAAxB,EAA8B;AAC1B;AACAO,qBAAKc,aAAL,GAAqBH,YAAYX,KAAKT,WAAjB,KAAiC,KAAtD;;AAEA;AACA,sBAAMwB,qBAAqBV,UAAUL,KAAKC,EAAf,CAA3B;AACA,oBAAIc,kBAAJ,EAAwB;AACpB;AACAf,yBAAKgB,cAAL,GAAsBD,mBAAmBC,cAAzC;AACAhB,yBAAKiB,eAAL,GAAuBF,mBAAmBE,eAAnB,IAAsC,CAA7D;AACAjB,yBAAKkB,eAAL,GAAuBH,mBAAmBG,eAAnB,IAAsC,SAA7D;AACAlB,yBAAKmB,aAAL,GAAqBJ,mBAAmBI,aAAnB,IAAoC,CAAzD;AACAnB,yBAAKoB,WAAL,GAAmBL,mBAAmBK,WAAnB,IAAkC,CAArD;AACApB,yBAAKqB,WAAL,GAAmBN,mBAAmBM,WAAnB,IAAkC,CAArD;AACArB,yBAAKsB,gBAAL,GAAwBP,mBAAmBO,gBAAnB,IAAuC,CAA/D;AACAtB,yBAAKuB,kBAAL,GAA0BR,mBAAmBQ,kBAAnB,IAAyC,CAAnE;AACAvB,yBAAKwB,mBAAL,GAA2BT,mBAAmBC,cAAnB,GAAoC,QAApC,GAA+C,UAA1E;AACAhB,yBAAKyB,oBAAL,GAA4B,CAACC,WAAW1B,KAAK2B,YAAhB,IAAgCZ,mBAAmBE,eAAnD,GAAqE,GAAtE,EAA2EW,OAA3E,CAAmF,CAAnF,CAA5B;AACA5B,yBAAK6B,YAAL,GAAoBd,mBAAmBe,QAAnB,GAA8BrD,OAAOsD,IAAP,CAAYhB,mBAAmBe,QAA/B,EAAyCE,MAAzC,CAAgD,qBAAhD,CAA9B,GAAuG,EAA3H;AACH,iBAbD,MAaO;AACH;AACAhC,yBAAKgB,cAAL,GAAsB,IAAtB;AACAhB,yBAAKiB,eAAL,GAAuB,CAAvB;AACAjB,yBAAKkB,eAAL,GAAuB,MAAvB;AACAlB,yBAAKmB,aAAL,GAAqB,CAArB;AACAnB,yBAAKoB,WAAL,GAAmB,CAAnB;AACApB,yBAAKqB,WAAL,GAAmB,CAAnB;AACArB,yBAAKsB,gBAAL,GAAwB,CAAxB;AACAtB,yBAAKuB,kBAAL,GAA0B,CAA1B;AACAvB,yBAAKwB,mBAAL,GAA2B,SAA3B;AACAxB,yBAAKyB,oBAAL,GAA4B,MAA5B;AACAzB,yBAAK6B,YAAL,GAAoB,EAApB;AACH;;AAED;AACA7B,qBAAKiC,qBAAL,GAA6B,CACzBP,WAAW1B,KAAKmB,aAAL,IAAsB,CAAjC,IACAO,WAAW1B,KAAKoB,WAAL,IAAoB,CAA/B,CADA,GAEAM,WAAW1B,KAAKqB,WAAL,IAAoB,CAA/B,CAFA,GAGAK,WAAW1B,KAAKsB,gBAAL,IAAyB,CAApC,CAJyB,EAK3BM,OAL2B,CAKnB,CALmB,CAA7B;;AAOA;AACA5B,qBAAK8B,QAAL,GAAgB9B,KAAK8B,QAAL,GAAgBrD,OAAOsD,IAAP,CAAY/B,KAAK8B,QAAjB,EAA2BE,MAA3B,CAAkC,qBAAlC,CAAhB,GAA2EvD,SAASuD,MAAT,CAAgB,qBAAhB,CAA3F;;AAEA;AACA,oBAAI7C,uBAAuB,aAAvB,IAAwCa,KAAKgB,cAAL,KAAwB,CAApE,EAAuE;AACnE;AACH;AACD,oBAAI7B,uBAAuB,eAAvB,IAA0Ca,KAAKgB,cAAL,KAAwB,CAAtE,EAAyE;AACrE;AACH;;AAEDH,8BAAcqB,IAAd,CAAmBlC,IAAnB;AACH;;AAED;AACAP,iBAAKA,IAAL,GAAYoB,aAAZ;AACApB,iBAAK0C,KAAL,GAAatB,cAAcuB,MAA3B;;AAEA;AACA,gBAAIvB,cAAcuB,MAAd,GAAuB,CAA3B,EAA8B;AAC1BC,wBAAQC,GAAR,CAAY,qBAAZ;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBzB,cAAcuB,MAAnC;AACAvB,8BAAc0B,KAAd,CAAoB,CAApB,EAAuB,CAAvB,EAA0BjC,OAA1B,CAAkC,gBAAQ;AACtC+B,4BAAQC,GAAR,CAAa,KAAItC,KAAKC,EAAG,oBAAmBD,KAAKgB,cAAe,qBAAoBhB,KAAKiB,eAAgB,EAAzG;AACH,iBAFD;AAGH;;AAED,mBAAO,MAAKuB,OAAL,CAAa/C,IAAb,CAAP;AA9HoB;AA+HvB;;AAED;;;;AAIMgD,oBAAN,GAAyB;AAAA;;AAAA;AACrB,kBAAMrD,QAAQ,OAAKA,KAAL,CAAW,UAAX,CAAd;AACA,kBAAMK,OAAO,MAAML,MAAMM,KAAN,CAAY;AAC3BgD,yBAAS;AADkB,aAAZ,EAEhB/C,KAFgB,CAEV,8BAFU,EAEsBC,KAFtB,CAE4B,CAAC,gBAAD,CAF5B,EAEgDQ,MAFhD,EAAnB;;AAIA,mBAAO,OAAKoC,OAAL,CAAa/C,IAAb,CAAP;AANqB;AAOxB;;AAED;;;;AAIMkD,gBAAN,GAAqB;AAAA;;AAAA;AACjB,kBAAMC,UAAU,OAAK9D,GAAL,CAAS,UAAT,CAAhB;;AAEA,gBAAI8D,OAAJ,EAAa;AACT;AACA,sBAAMrC,SAAS,MAAM,OAAKnB,KAAL,CAAW,oBAAX,EAAiCM,KAAjC,CAAuC;AACxDS,8BAAUyC;AAD8C,iBAAvC,EAElBC,IAFkB,EAArB;AAGA,uBAAO,OAAKL,OAAL,CAAajC,UAAU,EAAvB,CAAP;AACH,aAND,MAMO;AACH;AACA,sBAAMuC,UAAU,MAAM,OAAK1D,KAAL,CAAW,oBAAX,EAAiCgB,MAAjC,EAAtB;AACA,uBAAO,OAAKoC,OAAL,CAAaM,OAAb,CAAP;AACH;AAbgB;AAcpB;;AAED;;;;AAIMC,gBAAN,GAAqB;AAAA;;AAAA;AACjB,kBAAMH,UAAU,OAAKI,IAAL,CAAU,UAAV,CAAhB;AACA,kBAAMC,gBAAgB,OAAKD,IAAL,CAAU,gBAAV,CAAtB;AACA,kBAAME,iBAAiB,OAAKF,IAAL,CAAU,iBAAV,KAAgC,CAAvD;AACA,kBAAMG,iBAAiB,OAAKH,IAAL,CAAU,iBAAV,KAAgC,SAAvD;AACA,kBAAMI,eAAe,OAAKJ,IAAL,CAAU,eAAV,KAA8B,CAAnD;AACA,kBAAMK,aAAa,OAAKL,IAAL,CAAU,aAAV,KAA4B,CAA/C;AACA,kBAAMM,aAAa,OAAKN,IAAL,CAAU,aAAV,KAA4B,CAA/C;AACA,kBAAMO,iBAAiB,OAAKP,IAAL,CAAU,kBAAV,KAAiC,CAAxD;AACA,kBAAMQ,mBAAmB,OAAKR,IAAL,CAAU,oBAAV,KAAmC,CAA5D;;AAEAX,oBAAQC,GAAR,CAAY,mBAAZ;AACAD,oBAAQC,GAAR,CAAY,OAAZ,EAAqBM,OAArB;AACAP,oBAAQC,GAAR,CAAY,OAAZ,EAAqBW,aAArB;AACAZ,oBAAQC,GAAR,CAAY,OAAZ,EAAqB,EAAEY,cAAF,EAAkBE,YAAlB,EAAgCC,UAAhC,EAA4CC,UAA5C,EAAwDC,cAAxD,EAArB;;AAEA,gBAAI,CAACX,OAAL,EAAc;AACV,uBAAO,OAAKa,IAAL,CAAU,UAAV,CAAP;AACH;;AAED;AACA,kBAAMC,QAAQ,MAAM,OAAKtE,KAAL,CAAW,OAAX,EAAoBM,KAApB,CAA0B;AAC1CO,oBAAI2C,OADsC;AAE1CtD,2BAAW;AAF+B,aAA1B,EAGjBuD,IAHiB,EAApB;;AAKA,gBAAI,CAACa,KAAL,EAAY;AACR,uBAAO,OAAKD,IAAL,CAAU,OAAV,CAAP;AACH;;AAED,kBAAMrE,QAAQ,OAAKA,KAAL,CAAW,oBAAX,CAAd;AACA,kBAAMuE,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;;AAEA;AACA,kBAAMC,iBAAiB,MAAM3E,MAAMM,KAAN,CAAY;AACrCS,0BAAUyC;AAD2B,aAAZ,EAE1BC,IAF0B,EAA7B;;AAIAR,oBAAQC,GAAR,CAAY,WAAZ,EAAyByB,cAAzB;AACA1B,oBAAQC,GAAR,CAAY,qBAAZ,EAAmC,CAACyB,cAAD,IAAmBC,OAAOC,IAAP,CAAYF,cAAZ,EAA4B3B,MAA5B,KAAuC,CAA7F;;AAEA,kBAAM8B,aAAa;AACf/D,0BAAUyC,OADK;AAEf5B,gCAAgBiC,aAFD;AAGfhC,iCAAiBiC,cAHF;AAIfhC,iCAAiBiC,cAJF;AAKfhC,+BAAeiC,YALA;AAMfhC,6BAAaiC,UANE;AAOfhC,6BAAaiC,UAPE;AAQfhC,kCAAkBiC,cARH;AASfhC,oCAAoBiC,gBATL;AAUfW,6BAAaR;AAVE,aAAnB;;AAaA,gBAAIS,gBAAgB,EAApB;AACA,gBAAInB,iBAAiB,CAArB,EAAwB;AACpBmB,gCAAgB,OAAhB;AACA;AACA,oBAAIhB,gBAAgB,CAApB,EAAuB;AACnB,2BAAO,OAAKK,IAAL,CAAU,gBAAV,CAAP;AACH;AACDS,2BAAWjD,eAAX,GAA6BmC,YAA7B,CANoB,CAMuB;AAC9C,aAPD,MAOO;AACHgB,gCAAgB,OAAhB;AACH;;AAED/B,oBAAQC,GAAR,CAAY,OAAZ,EAAqB4B,UAArB;;AAEA;AACA,kBAAMG,iBAAiBN,kBAAkBC,OAAOC,IAAP,CAAYF,cAAZ,EAA4B3B,MAA5B,GAAqC,CAAvD,IAA4D2B,eAAe9D,EAAlG;;AAEA,gBAAIoE,cAAJ,EAAoB;AAChB;AACAhC,wBAAQC,GAAR,CAAY,WAAZ;AACA,sBAAMgC,eAAe,MAAMlF,MAAMM,KAAN,CAAY;AACnCS,8BAAUyC;AADyB,iBAAZ,EAExB2B,MAFwB,CAEjBL,UAFiB,CAA3B;AAGA7B,wBAAQC,GAAR,CAAY,OAAZ,EAAqBgC,YAArB;;AAEA;AACA,sBAAME,eAAe,MAAMpF,MAAMM,KAAN,CAAY;AACnCS,8BAAUyC;AADyB,iBAAZ,EAExBC,IAFwB,EAA3B;AAGAR,wBAAQC,GAAR,CAAY,UAAZ,EAAwBkC,YAAxB;AACH,aAbD,MAaO;AACH;AACAnC,wBAAQC,GAAR,CAAY,UAAZ;AACA4B,2BAAWpC,QAAX,GAAsB6B,WAAtB;AACA,sBAAMc,YAAY,MAAMrF,MAAMsF,GAAN,CAAUR,UAAV,CAAxB;AACA7B,wBAAQC,GAAR,CAAY,OAAZ,EAAqBmC,SAArB;;AAEA;AACA,sBAAMD,eAAe,MAAMpF,MAAMM,KAAN,CAAY;AACnCS,8BAAUyC;AADyB,iBAAZ,EAExBC,IAFwB,EAA3B;AAGAR,wBAAQC,GAAR,CAAY,UAAZ,EAAwBkC,YAAxB;AACH;;AAED;AACA,kBAAM,OAAKG,wBAAL,CAA8B/B,OAA9B,EAAuCwB,aAAvC,EAAsDF,UAAtD,CAAN;;AAEA7B,oBAAQC,GAAR,CAAY,OAAZ,EAAqB8B,aAArB;;AAEA,mBAAO,OAAK5B,OAAL,CAAa;AAChBoC,yBAAU,KAAIR,aAAc,IADZ;AAEhBS,4BAAYnB,MAAM1E,IAFF;AAGhB8F,2BAAWV,aAHK;AAIhB7D,wBAAQ2D;AAJQ,aAAb,CAAP;AAvGiB;AA6GpB;;AAED;;;;AAIMa,oBAAN,GAAyB;AAAA;;AAAA;AACrB,kBAAMnC,UAAU,OAAKI,IAAL,CAAU,UAAV,CAAhB;AACA,kBAAME,iBAAiB,OAAKF,IAAL,CAAU,iBAAV,CAAvB;AACA,kBAAMG,iBAAiB,OAAKH,IAAL,CAAU,iBAAV,KAAgC,QAAvD;;AAEA,gBAAI,CAACJ,OAAD,IAAYM,mBAAmB8B,SAAnC,EAA8C;AAC1C,uBAAO,OAAKvB,IAAL,CAAU,OAAV,CAAP;AACH;;AAED,kBAAMrE,QAAQ,OAAKA,KAAL,CAAW,oBAAX,CAAd;;AAEA;AACA,kBAAM2E,iBAAiB,MAAM3E,MAAMM,KAAN,CAAY;AACrCS,0BAAUyC;AAD2B,aAAZ,EAE1BC,IAF0B,EAA7B;;AAIA,kBAAMqB,aAAa;AACfjD,iCAAiBiC,cADF;AAEfhC,iCAAiBiC,cAFF;AAGfgB,6BAAaP,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC;AAHE,aAAnB;;AAMA,gBAAIC,cAAJ,EAAoB;AAChB;AACA,sBAAM3E,MAAMM,KAAN,CAAY;AACdS,8BAAUyC;AADI,iBAAZ,EAEH2B,MAFG,CAEIL,UAFJ,CAAN;AAGH,aALD,MAKO;AACH;AACAA,2BAAW/D,QAAX,GAAsByC,OAAtB;AACAsB,2BAAWlD,cAAX,GAA4B,CAA5B,CAHG,CAG4B;AAC/BkD,2BAAWpC,QAAX,GAAsB8B,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAAtB;AACA,sBAAM1E,MAAMsF,GAAN,CAAUR,UAAV,CAAN;AACH;;AAED,mBAAO,OAAK1B,OAAL,CAAa,QAAb,CAAP;AAnCqB;AAoCxB;;AAED;;;;AAIMyC,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAM7F,QAAQ,OAAKA,KAAL,CAAW,oBAAX,CAAd;;AAEA;AACA,kBAAM8F,aAAa,MAAM,OAAK9F,KAAL,CAAW,OAAX,EAAoBM,KAApB,CAA0B;AAC/CJ,2BAAW;AADoC,aAA1B,EAEtB6C,KAFsB,EAAzB;;AAIA;AACA,kBAAMgD,mBAAmB,MAAM/F,MAAMM,KAAN,CAAY;AACvCsB,gCAAgB;AADuB,aAAZ,EAE5BmB,KAF4B,EAA/B;;AAIA;AACA,kBAAMiD,kBAAkB,MAAMhG,MAAM+C,KAAN,EAA9B;AACA,kBAAMkD,eAAeH,aAAaE,eAAlC;;AAEA;AACA,kBAAME,gBAAgB,MAAMlG,MAAMM,KAAN,CAAY;AACpCsB,gCAAgB;AADoB,aAAZ,EAEzBmB,KAFyB,EAA5B;;AAIA;AACA,kBAAMoD,mBAAmB,MAAMnG,MAAMoG,KAAN,CAAY,IAAZ,EAC1BC,IAD0B,CACrB;AACFC,uBAAO,OADL;AAEFD,sBAAM,MAFJ;AAGFE,oBAAI,GAHF;AAIFC,oBAAI,CAAC,aAAD,EAAgB,MAAhB;AAJF,aADqB,EAO1BlG,KAP0B,CAOpB;AACH,qCAAqB,CADlB;AAEH,+BAAe;AAFZ,aAPoB,EAW1BC,KAX0B,CAWpB,2GAXoB,EAY1BS,MAZ0B,EAA/B;;AAcA,gBAAIyF,kBAAkB,CAAtB;AACA,gBAAIC,sBAAsB,CAA1B;AACAP,6BAAiBjF,OAAjB,CAAyB,gBAAQ;AAC7B,sBAAMyF,iBAAiBrE,WAAW1B,KAAK2B,YAAhB,IAAgCD,WAAW1B,KAAKiB,eAAL,IAAwB,CAAnC,CAAhC,GAAwE,GAA/F;AACA4E,mCAAmBE,cAAnB;AACAD,uCAAuBpE,WAAW1B,KAAKiB,eAAL,IAAwB,CAAnC,CAAvB;AACH,aAJD;;AAMA;AACA,kBAAM+E,kBAAkB,MAAM5G,MAAMM,KAAN,CAAY;AACtCsB,gCAAgB,CADsB;AAEtCO,oCAAoB,CAAC,IAAD,EAAO,CAAP;AAFkB,aAAZ,EAG3BY,KAH2B,EAA9B;;AAKA;AACA,kBAAM8D,cAAc,MAAM7G,MAAMoG,KAAN,CAAY,IAAZ,EACrBC,IADqB,CAChB;AACFC,uBAAO,OADL;AAEFD,sBAAM,MAFJ;AAGFE,oBAAI,GAHF;AAIFC,oBAAI,CAAC,aAAD,EAAgB,MAAhB;AAJF,aADgB,EAOrBlG,KAPqB,CAOf;AACH,qCAAqB,CADlB;AAEH,+BAAe,CAFZ;AAGH,iCAAiB,CAAC,GAAD,EAAM,EAAN;AAHd,aAPe,EAYrByC,KAZqB,EAA1B;;AAcA;AACA,kBAAM+D,gBAAgB,MAAM9G,MAAMoG,KAAN,CAAY,IAAZ,EACvBC,IADuB,CAClB;AACFC,uBAAO,OADL;AAEFD,sBAAM,MAFJ;AAGFE,oBAAI,GAHF;AAIFC,oBAAI,CAAC,aAAD,EAAgB,MAAhB;AAJF,aADkB,EAOvBH,IAPuB,CAOlB;AACFC,uBAAO,UADL;AAEFD,sBAAM,MAFJ;AAGFE,oBAAI,GAHF;AAIFC,oBAAI,CAAC,eAAD,EAAkB,MAAlB;AAJF,aAPkB,EAavBlG,KAbuB,CAajB;AACH,qCAAqB,CADlB;AAEH,+BAAe;AAFZ,aAbiB,EAiBvBC,KAjBuB,CAiBjB,iEAjBiB,EAkBvBwG,KAlBuB,CAkBjB,MAlBiB,EAmBvBvG,KAnBuB,CAmBjB,YAnBiB,EAoBvBwG,KApBuB,CAoBjB,CApBiB,EAqBvBhG,MArBuB,EAA5B;;AAuBA,mBAAO,OAAKoC,OAAL,CAAa;AAChB;AACA6D,2BAAW;AACPC,mCAAepB,UADR,EAC8B;AACrCqB,oCAAgBpB,gBAFT,EAE8B;AACrCqB,sCAAkBlB,aAHX,EAG8B;AACrCmB,qCAAiBpB,YAJV,EAI8B;AACrCqB,qCAAiBxB,aAAa,CAAb,GAAiB,CAAEC,mBAAmBD,UAApB,GAAkC,GAAnC,EAAwCtD,OAAxC,CAAgD,CAAhD,CAAjB,GAAsE,CALhF,CAKkF;AALlF,iBAFK;;AAUhB;AACA+E,iCAAiB;AACbC,yCAAqBf,gBAAgBjE,OAAhB,CAAwB,CAAxB,CADR;AAEbiF,2CAAuBtB,iBAAiBnD,MAAjB,GAA0B,CAA1B,GACnB,CAAC0D,sBAAsBP,iBAAiBnD,MAAxC,EAAgDR,OAAhD,CAAwD,CAAxD,CADmB,GAC0C,CAHpD;AAIbkF,4CAAwB,CAACjB,kBAAkB,GAAnB,EAAwBjE,OAAxB,CAAgC,CAAhC,CAJX,CAI8C;AAJ9C,iBAXD;;AAkBhB;AACAmF,8BAAc;AACVC,mCAAe7B,mBAAmBa,eADxB,EAC0C;AACpDA,qCAAiBA,eAFP,EAE0C;AACpDC,iCAAaA,WAHH,CAGyC;AAHzC,iBAnBE;;AAyBhB;AACAgB,+BAAef,aA1BC;;AA4BhB;AACAgB,+BAAehC,UA7BC;AA8BhBiC,qCAAqBhC,gBA9BL;AA+BhBiC,uCAAuBlC,aAAaC,gBA/BpB;AAgChBU,iCAAiBA,gBAAgBjE,OAAhB,CAAwB,CAAxB,CAhCD;AAiChBqE,6BAAaA;AAjCG,aAAb,CAAP;AA1FgB;AA6HnB;;AAED;;;;AAIMoB,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAMvH,WAAW,OAAKkD,IAAL,CAAU,WAAV,CAAjB,CADgB,CACyB;AACzC,kBAAMC,gBAAgB,OAAKD,IAAL,CAAU,gBAAV,CAAtB;AACA,kBAAME,iBAAiB,OAAKF,IAAL,CAAU,iBAAV,KAAgC,CAAvD;AACA,kBAAMG,iBAAiB,OAAKH,IAAL,CAAU,iBAAV,KAAgC,SAAvD;AACA,kBAAMI,eAAe,OAAKJ,IAAL,CAAU,eAAV,KAA8B,CAAnD;AACA,kBAAMK,aAAa,OAAKL,IAAL,CAAU,aAAV,KAA4B,CAA/C;AACA,kBAAMM,aAAa,OAAKN,IAAL,CAAU,aAAV,KAA4B,CAA/C;AACA,kBAAMO,iBAAiB,OAAKP,IAAL,CAAU,kBAAV,KAAiC,CAAxD;AACA,kBAAMQ,mBAAmB,OAAKR,IAAL,CAAU,oBAAV,KAAmC,CAA5D;AACA,kBAAMsE,aAAa,OAAKtE,IAAL,CAAU,aAAV,CAAnB,CAVgB,CAU6B;;AAE7C,gBAAI,CAAClD,QAAD,IAAa,CAACyH,MAAMC,OAAN,CAAc1H,QAAd,CAAd,IAAyCA,SAASsC,MAAT,KAAoB,CAAjE,EAAoE;AAChE,uBAAO,OAAKqB,IAAL,CAAU,WAAV,CAAP;AACH;;AAED;AACA,kBAAMgE,aAAa,MAAM,OAAKrI,KAAL,CAAW,OAAX,EAAoBM,KAApB,CAA0B;AAC/CO,oBAAI,CAAC,IAAD,EAAOH,QAAP,CAD2C;AAE/CR,2BAAW;AAFoC,aAA1B,EAGtBK,KAHsB,CAGhB,sBAHgB,EAGQS,MAHR,EAAzB;;AAKA,gBAAIqH,WAAWrF,MAAX,KAAsBtC,SAASsC,MAAnC,EAA2C;AACvC,uBAAO,OAAKqB,IAAL,CAAU,aAAV,CAAP;AACH;;AAED,kBAAMrE,QAAQ,OAAKA,KAAL,CAAW,oBAAX,CAAd;AACA,kBAAMuE,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;;AAEA,gBAAI4D,eAAe,CAAnB;AACA,gBAAIC,YAAY,CAAhB;AACA,kBAAMC,UAAU,EAAhB;;AAEA,iBAAK,MAAMlE,KAAX,IAAoB+D,UAApB,EAAgC;AAC5B,oBAAI;AACA;AACA,0BAAM1D,iBAAiB,MAAM3E,MAAMM,KAAN,CAAY;AACrCS,kCAAUuD,MAAMzD;AADqB,qBAAZ,EAE1B4C,IAF0B,EAA7B;;AAIA,wBAAIqB,aAAa;AACb/D,kCAAUuD,MAAMzD,EADH;AAEbe,wCAAgBiC,aAFH;AAGbhC,yCAAiBiC,cAHJ;AAIbhC,yCAAiBiC,cAJJ;AAKbhC,uCAAeiC,YALF;AAMbhC,qCAAaiC,UANA;AAObhC,qCAAaiC,UAPA;AAQbhC,0CAAkBiC,cARL;AASbhC,4CAAoBiC,gBATP;AAUbW,qCAAaR;AAVA,qBAAjB;;AAaA;AACA,wBAAI2D,UAAJ,EAAgB;AACZ,8BAAMO,WAAW,MAAM,OAAKzI,KAAL,CAAW,sBAAX,EAAmCM,KAAnC,CAAyC;AAC5DO,gCAAIqH;AADwD,yBAAzC,EAEpBzE,IAFoB,EAAvB;;AAIA,4BAAIgF,QAAJ,EAAc;AACV3D,uCAAW/C,aAAX,GAA2B0G,SAAS1G,aAApC;AACA+C,uCAAW9C,WAAX,GAAyByG,SAASzG,WAAlC;AACA8C,uCAAW7C,WAAX,GAAyBwG,SAASxG,WAAlC;AACA6C,uCAAW5C,gBAAX,GAA8BuG,SAASvG,gBAAvC;AACA4C,uCAAW3C,kBAAX,GAAgCsG,SAAStG,kBAAzC;AACA2C,uCAAWjD,eAAX,GAA6B4G,SAAS1G,aAAtC,CANU,CAM2C;AACrD+C,uCAAWhD,eAAX,GAA6B,UAA7B;AACH;AACJ;;AAED;AACA,wBAAI+B,iBAAiB,CAAjB,IAAsBiB,WAAW/C,aAAX,IAA4B,CAAlD,IAAuD,CAACmG,UAA5D,EAAwE;AACpE,8BAAMQ,QAAQpG,WAAWgC,MAAM/B,YAAjB,CAAd;AACA,4BAAImG,SAAS,GAAb,EAAkB;AACd5D,uCAAW/C,aAAX,GAA2B,GAA3B;AACA+C,uCAAW9C,WAAX,GAAyB,GAAzB;AACA8C,uCAAW7C,WAAX,GAAyB,GAAzB;AACA6C,uCAAW5C,gBAAX,GAA8B,GAA9B;AACH,yBALD,MAKO,IAAIwG,SAAS,GAAb,EAAkB;AACrB5D,uCAAW/C,aAAX,GAA2B,IAA3B;AACA+C,uCAAW9C,WAAX,GAAyB,GAAzB;AACA8C,uCAAW7C,WAAX,GAAyB,GAAzB;AACA6C,uCAAW5C,gBAAX,GAA8B,GAA9B;AACH,yBALM,MAKA;AACH4C,uCAAW/C,aAAX,GAA2B,IAA3B;AACA+C,uCAAW9C,WAAX,GAAyB,GAAzB;AACA8C,uCAAW7C,WAAX,GAAyB,GAAzB;AACA6C,uCAAW5C,gBAAX,GAA8B,GAA9B;AACH;AACD4C,mCAAWjD,eAAX,GAA6BiD,WAAW/C,aAAxC;AACA+C,mCAAWhD,eAAX,GAA6B,MAA7B;AACH;;AAED,wBAAI6C,cAAJ,EAAoB;AAChB;AACA,8BAAM3E,MAAMM,KAAN,CAAY;AACdS,sCAAUuD,MAAMzD;AADF,yBAAZ,EAEHsE,MAFG,CAEIL,UAFJ,CAAN;AAGH,qBALD,MAKO;AACH;AACAA,mCAAWpC,QAAX,GAAsB6B,WAAtB;AACA,8BAAMvE,MAAMsF,GAAN,CAAUR,UAAV,CAAN;AACH;;AAEDwD;AACAE,4BAAQ1F,IAAR,CAAa;AACT/B,kCAAUuD,MAAMzD,EADP;AAET4E,oCAAYnB,MAAM1E,IAFT;AAGT+I,gCAAQ,SAHC;AAITjD,mCAAW7B,iBAAiB,CAAjB,GAAqB,OAArB,GAA+B;AAJjC,qBAAb;AAOH,iBA9ED,CA8EE,OAAO+E,KAAP,EAAc;AACZL;AACAC,4BAAQ1F,IAAR,CAAa;AACT/B,kCAAUuD,MAAMzD,EADP;AAET4E,oCAAYnB,MAAM1E,IAFT;AAGT+I,gCAAQ,QAHC;AAITC,+BAAOA,MAAMpD;AAJJ,qBAAb;AAMH;AACJ;;AAED;AACA,kBAAM,OAAKqD,iBAAL,CAAuBnI,QAAvB,EAAiCmD,iBAAiB,CAAjB,GAAqB,SAArB,GAAiC,SAAlE,EAA6E;AAC/EyE,4BAD+E;AAE/EC,yBAF+E;AAG/EL;AAH+E,aAA7E,CAAN;;AAMA,mBAAO,OAAK9E,OAAL,CAAa;AAChBoC,yBAAU,YAAW8C,YAAa,OAAMC,SAAU,GADlC;AAEhBD,4BAFgB;AAGhBC,yBAHgB;AAIhBC;AAJgB,aAAb,CAAP;AAlIgB;AAwInB;;AAED;;;;AAIMM,eAAN,GAAoB;AAAA;;AAAA;AAChB;AACA,kBAAMC,QAAQ,CACV,EAAEC,UAAU,CAAZ,EAAeC,UAAU,GAAzB,EAA8BC,MAAM,CAApC,EAAuCC,aAAa,UAApD,EADU,EAEV,EAAEH,UAAU,GAAZ,EAAiBC,UAAU,GAA3B,EAAgCC,MAAM,EAAtC,EAA0CC,aAAa,YAAvD,EAFU,EAGV,EAAEH,UAAU,GAAZ,EAAiBC,UAAU,MAA3B,EAAmCC,MAAM,EAAzC,EAA6CC,aAAa,UAA1D,EAHU,CAAd;;AAMA,mBAAO,OAAK/F,OAAL,CAAa;AAChBgG,6BAAa,EADG;AAEhBL,uBAAOA;AAFS,aAAb,CAAP;AARgB;AAYnB;;AAED;;;;AAIMM,sBAAN,GAA2B;AAAA;;AAAA;AACvB,kBAAM5J,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,OAAKD,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,kBAAM4J,SAAS,OAAK5J,GAAL,CAAS,QAAT,KAAsB,EAArC;AACA,kBAAM6J,QAAQ,OAAK7J,GAAL,CAAS,OAAT,KAAqB,EAAnC;AACA,kBAAMiJ,SAAS,OAAKjJ,GAAL,CAAS,QAAT,KAAsB,EAArC;AACA,kBAAM8J,SAAS,OAAK9J,GAAL,CAAS,QAAT,KAAsB,OAArC;;AAEA,kBAAMM,QAAQ,OAAKA,KAAL,CAAW,cAAX,CAAd;AACA,gBAAIC,WAAW,EAAf;;AAEA;AACA,gBAAIqJ,MAAJ,EAAY;AACR,sBAAMG,YAAY,OAAKzJ,KAAL,CAAW,MAAX,CAAlB;AACA,sBAAM0J,QAAQ,MAAMD,UAAUnJ,KAAV,CAAgB;AAChC,gCAAY,CAAC,MAAD,EAAU,IAAGgJ,MAAO,GAApB;AADoB,iBAAhB,EAEjB/I,KAFiB,CAEX,IAFW,EAELS,MAFK,EAApB;;AAIA,oBAAI0I,MAAM1G,MAAN,GAAe,CAAnB,EAAsB;AAClB/C,6BAAS0J,OAAT,GAAmB,CAAC,IAAD,EAAOD,MAAM/I,GAAN,CAAU;AAAA,+BAAKiJ,EAAE/I,EAAP;AAAA,qBAAV,CAAP,CAAnB;AACH,iBAFD,MAEO;AACHZ,6BAAS0J,OAAT,GAAmB,CAAnB,CADG,CACmB;AACzB;AACJ;;AAED,gBAAIhB,MAAJ,EAAY;AACR,oBAAIA,WAAW,QAAf,EAAyB;AACrB1I,6BAAS4J,SAAT,GAAqB,CAArB;AACA5J,6BAAS6J,YAAT,GAAwB,CAAxB;AACH,iBAHD,MAGO,IAAInB,WAAW,UAAf,EAA2B;AAC9B1I,6BAAS4J,SAAT,GAAqB,CAArB;AACH,iBAFM,MAEA,IAAIlB,WAAW,SAAf,EAA0B;AAC7B1I,6BAAS6J,YAAT,GAAwB,CAAxB;AACH;AACJ;;AAED;AACA,kBAAMC,eAAe,MAAM/J,MAAMM,KAAN,CAAYL,QAAZ,EACtBM,KADsB,CAChB,mHADgB,EAEtBC,KAFsB,CAEhB,CAAC,kBAAD,EAAqB,uBAArB,CAFgB,EAGtBf,IAHsB,CAGjBA,IAHiB,EAGXE,IAHW,EAItBc,WAJsB,EAA3B;;AAMA;AACA,iBAAK,MAAMuJ,WAAX,IAA0BD,aAAa1J,IAAvC,EAA6C;AACzC;AACA,sBAAM4J,WAAW,MAAM,OAAKjK,KAAL,CAAW,MAAX,EAAmBM,KAAnB,CAAyB;AAC5CO,wBAAImJ,YAAYL;AAD4B,iBAAzB,EAEpBpJ,KAFoB,CAEd,wBAFc,EAEYkD,IAFZ,EAAvB;;AAIA,oBAAIwG,QAAJ,EAAc;AACVD,gCAAYpK,IAAZ,GAAmBqK,SAASC,QAAT,IAAqB,KAAxC;AACAF,gCAAYG,KAAZ,GAAoBF,SAASG,MAAT,GAAkBH,SAASG,MAAT,CAAgBC,OAAhB,CAAwB,qBAAxB,EAA+C,UAA/C,CAAlB,GAA+E,KAAnG;AACAL,gCAAYM,MAAZ,GAAqBL,SAASK,MAAT,IAAoB,oCAAmCN,YAAYpK,IAAK,6BAA7F;AACH;;AAED;AACA,sBAAM2K,YAAY,MAAM,OAAKvK,KAAL,CAAW,oBAAX,EAAiCM,KAAjC,CAAuC;AAC3DO,wBAAImJ,YAAYQ;AAD2C,iBAAvC,EAErB/G,IAFqB,EAAxB;;AAIAuG,4BAAYT,KAAZ,GAAoBgB,YAAYA,UAAUE,UAAtB,GAAmC,OAAvD;AACAT,4BAAYlG,cAAZ,GAA6ByG,YAAajI,WAAWiI,UAAUG,gBAArB,IAAyC,EAAtD,GAA4D,EAAzF,CAlByC,CAkBoD;;AAE7F;AACAV,4BAAYW,KAAZ,GAAoBrI,WAAW0H,YAAYY,WAAZ,IAA2B,CAAtC,EAAyCpI,OAAzC,CAAiD,CAAjD,CAApB;AACAwH,4BAAYa,UAAZ,GAAyBvI,WAAW0H,YAAYc,gBAAZ,IAAgC,CAA3C,EAA8CtI,OAA9C,CAAsD,CAAtD,CAAzB;AACAwH,4BAAYe,QAAZ,GAAuB1L,OAAOsD,IAAP,CAAYqH,YAAYgB,SAAxB,EAAmCpI,MAAnC,CAA0C,YAA1C,CAAvB;AACAoH,4BAAYrB,MAAZ,GAAqBqB,YAAYH,SAAZ,IAAyBG,YAAYF,YAAZ,KAA6B,CAAtD,GAA0D,QAA1D,GACA,CAACE,YAAYH,SAAb,GAAyB,UAAzB,GAAsC,SAD3D;AAEH;;AAED,mBAAO,OAAKzG,OAAL,CAAa2G,YAAb,CAAP;AAxEuB;AAyE1B;;AAED;;;;AAIMkB,0BAAN,GAA+B;AAAA;;AAAA;AAC3B,gBAAI;AACA;AACA,sBAAMC,oBAAoB,MAAM,QAAKlL,KAAL,CAAW,cAAX,EAA2BM,KAA3B,CAAiC;AAC7DwJ,kCAAc;AAD+C,iBAAjC,EAE7B/G,KAF6B,EAAhC;;AAIA;AACA,sBAAMoI,cAAc,MAAM,QAAKnL,KAAL,CAAW,cAAX,EAA2BM,KAA3B,CAAiC;AACvDwJ,kCAAc,CADyC;AAEvDU,8BAAU;AAF6C,iBAAjC,EAGvBzH,KAHuB,EAA1B;;AAKA;AACA,sBAAMqI,wBAAwB,MAAM,QAAKpL,KAAL,CAAW,cAAX,EAA2BM,KAA3B,CAAiC;AACjEwJ,kCAAc;AADmD,iBAAjC,EAEjCuB,GAFiC,CAE7B,kBAF6B,CAApC;AAGA,sBAAM5E,kBAAkBnE,WAAW8I,yBAAyB,CAApC,EAAuC5I,OAAvC,CAA+C,CAA/C,CAAxB;;AAEA;AACA,sBAAM8I,mBAAmB,MAAM,QAAKtL,KAAL,CAAW,cAAX,EAA2BM,KAA3B,CAAiC;AAC5DwJ,kCAAc;AAD8C,iBAAjC,EAE5BuB,GAF4B,CAExB,aAFwB,CAA/B;AAGA,sBAAME,aAAajJ,WAAWgJ,oBAAoB,CAA/B,EAAkC9I,OAAlC,CAA0C,CAA1C,CAAnB;;AAEA,sBAAMgJ,QAAQ;AACVN,uCAAmBA,iBADT;AAEVC,iCAAaA,WAFH;AAGV1E,qCAAiBA,eAHP;AAIV8E,gCAAYA;AAJF,iBAAd;;AAOA,uBAAO,QAAKnI,OAAL,CAAaoI,KAAb,CAAP;AACH,aAhCD,CAgCE,OAAO5C,KAAP,EAAc;AACZ3F,wBAAQ2F,KAAR,CAAc,YAAd,EAA4BA,KAA5B;AACA,uBAAO,QAAKvE,IAAL,CAAU,UAAV,CAAP;AACH;AApC0B;AAqC9B;;AAED;;;;AAIMoH,2BAAN,GAAgC;AAAA;;AAAA;AAC5B,kBAAMC,gBAAgB,QAAKhM,GAAL,CAAS,IAAT,CAAtB;;AAEA,gBAAI,CAACgM,aAAL,EAAoB;AAChB,uBAAO,QAAKrH,IAAL,CAAU,WAAV,CAAP;AACH;;AAED,gBAAI;AACA,sBAAM2F,cAAc,MAAM,QAAKhK,KAAL,CAAW,cAAX,EAA2BM,KAA3B,CAAiC;AACvDO,wBAAI6K;AADmD,iBAAjC,EAEvBjI,IAFuB,EAA1B;;AAIA,oBAAI,CAACuG,WAAL,EAAkB;AACd,2BAAO,QAAK3F,IAAL,CAAU,QAAV,CAAP;AACH;;AAED;AACA,sBAAM4F,WAAW,MAAM,QAAKjK,KAAL,CAAW,MAAX,EAAmBM,KAAnB,CAAyB;AAC5CO,wBAAImJ,YAAYL;AAD4B,iBAAzB,EAEpBlG,IAFoB,EAAvB;;AAIA;AACA,sBAAM8G,YAAY,MAAM,QAAKvK,KAAL,CAAW,oBAAX,EAAiCM,KAAjC,CAAuC;AAC3DO,wBAAImJ,YAAYQ;AAD2C,iBAAvC,EAErB/G,IAFqB,EAAxB;;AAIA;AACA,sBAAMkI,2BACC3B,WADD;AAEFpK,0BAAMqK,WAAWA,SAASC,QAApB,GAA+B,KAFnC;AAGFC,2BAAOF,WAAWA,SAASG,MAApB,GAA6B,KAHlC;AAIFE,4BAAQL,WAAWA,SAASK,MAApB,GAA6B,EAJnC;AAKFf,2BAAOgB,YAAYA,UAAUE,UAAtB,GAAmC,OALxC;AAMF3G,oCAAgByG,YAAajI,WAAWiI,UAAUG,gBAArB,IAAyC,EAAtD,GAA4D,EAN1E;AAOFK,8BAAU1L,OAAOsD,IAAP,CAAYqH,YAAYgB,SAAxB,EAAmCpI,MAAnC,CAA0C,qBAA1C;AAPR,kBAAN;;AAUA,uBAAO,QAAKQ,OAAL,CAAauI,MAAb,CAAP;AACH,aA/BD,CA+BE,OAAO/C,KAAP,EAAc;AACZ3F,wBAAQ2F,KAAR,CAAc,YAAd,EAA4BA,KAA5B;AACA,uBAAO,QAAKvE,IAAL,CAAU,WAAV,CAAP;AACH;AAzC2B;AA0C/B;;AAED;;;;AAIMuH,iCAAN,GAAsC;AAAA;;AAAA;AAClC,kBAAMF,gBAAgB,QAAK9H,IAAL,CAAU,gBAAV,CAAtB;AACA,kBAAMiI,WAAW,QAAKjI,IAAL,CAAU,WAAV,CAAjB;;AAEA,gBAAI,CAAC8H,aAAL,EAAoB;AAChB,uBAAO,QAAKrH,IAAL,CAAU,WAAV,CAAP;AACH;;AAED,gBAAI;AACA,sBAAM,QAAKrE,KAAL,CAAW,cAAX,EAA2BM,KAA3B,CAAiC;AACnCO,wBAAI6K;AAD+B,iBAAjC,EAEHvG,MAFG,CAEI;AACN0E,+BAAWgC,WAAW,CAAX,GAAe,CADpB;AAEN9G,iCAAaP,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC;AAFP,iBAFJ,CAAN;;AAOA,uBAAO,QAAKtB,OAAL,CAAa,QAAb,CAAP;AACH,aATD,CASE,OAAOwF,KAAP,EAAc;AACZ3F,wBAAQ2F,KAAR,CAAc,YAAd,EAA4BA,KAA5B;AACA,uBAAO,QAAKvE,IAAL,CAAU,QAAV,CAAP;AACH;AApBiC;AAqBrC;;AAED;;;;AAIMyH,kBAAN,GAAuB;AAAA;;AAAA;AACnB,gBAAI;AACA,sBAAMtC,SAAS,QAAK9J,GAAL,CAAS,QAAT,KAAsB,IAArC;AACA,sBAAMqM,UAAU,IAAItH,IAAJ,EAAhB;AACA,sBAAMuH,YAAY,IAAIvH,IAAJ,EAAlB;AACAuH,0BAAUC,OAAV,CAAkBF,QAAQG,OAAR,KAAoB1H,SAASgF,MAAT,CAAtC;;AAEA,sBAAM2C,iBAAiB3H,SAASwH,UAAUtH,OAAV,KAAsB,IAA/B,CAAvB;AACA,sBAAM0H,eAAe5H,SAASuH,QAAQrH,OAAR,KAAoB,IAA7B,CAArB;;AAEA;AACA,sBAAM4G,mBAAmB,MAAM,QAAKtL,KAAL,CAAW,OAAX,EAAoBM,KAApB,CAA0B;AACrDoC,8BAAU,CAAC,SAAD,EAAYyJ,cAAZ,EAA4BC,YAA5B,CAD2C;AAErDC,kCAAc,CAAC,IAAD,EAAO,GAAP;AAFuC,iBAA1B,EAG5BhB,GAH4B,CAGxB,cAHwB,CAA/B;AAIA,sBAAME,aAAajJ,WAAWgJ,oBAAoB,CAA/B,CAAnB;;AAEA;AACA,sBAAMF,wBAAwB,MAAM,QAAKpL,KAAL,CAAW,cAAX,EAA2BqL,GAA3B,CAA+B,kBAA/B,CAApC;AACA,sBAAM5E,kBAAkBnE,WAAW8I,yBAAyB,CAApC,CAAxB;;AAEA;AACA,sBAAMkB,qBAAqB,MAAM,QAAKtM,KAAL,CAAW,cAAX,EAA2BM,KAA3B,CAAiC;AAC9DuJ,+BAAW,CADmD;AAE9DC,kCAAc;AAFgD,iBAAjC,EAG9B/G,KAH8B,EAAjC;;AAKA;AACA,sBAAMwJ,cAAc,MAAM,QAAKvM,KAAL,CAAW,OAAX,EAAoBM,KAApB,CAA0B;AAChDoC,8BAAU,CAAC,SAAD,EAAYyJ,cAAZ,EAA4BC,YAA5B;AADsC,iBAA1B,EAEvBrJ,KAFuB,EAA1B;AAGA,sBAAMyJ,iBAAiBD,cAAc,CAAd,GAAkB,CAAEhB,aAAagB,WAAd,GAA6B,IAA9B,EAAoC/J,OAApC,CAA4C,CAA5C,CAAlB,GAAmE,CAA1F;;AAEA;AACA,sBAAMiK,WAAW;AACblB,gCAAYA,UADC;AAEbmB,iCAAaC,KAAKC,KAAL,CAAWD,KAAKE,MAAL,KAAgB,EAA3B,IAAiC,CAFjC,EAEoC;AACjDpG,qCAAiBA,eAHJ;AAIbqG,sCAAkBH,KAAKC,KAAL,CAAWD,KAAKE,MAAL,KAAgB,EAA3B,IAAiC,CAJtC,EAIyC;AACtDP,wCAAoBA,kBALP;AAMbS,uCAAmBJ,KAAKC,KAAL,CAAWD,KAAKE,MAAL,KAAgB,EAA3B,IAAiC,EANvC,EAM2C;AACxDL,oCAAgBA,cAPH;AAQbQ,sCAAkBL,KAAKC,KAAL,CAAWD,KAAKE,MAAL,KAAgB,CAA3B,IAAgC,CARrC,EAQwC;AACrD3B,uCAAmB,MAAM,QAAKlL,KAAL,CAAW,cAAX,EAA2B+C,KAA3B;AATZ,iBAAjB;;AAYA,uBAAO,QAAKK,OAAL,CAAaqJ,QAAb,CAAP;AACH,aA9CD,CA8CE,OAAO7D,KAAP,EAAc;AACZ3F,wBAAQ2F,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,QAAKvE,IAAL,CAAU,UAAV,CAAP;AACH;AAlDkB;AAmDtB;;AAED;;;;AAIM4I,qBAAN,GAA0B;AAAA;;AAAA;AACtB,gBAAI;AACA,sBAAMzD,SAAS,QAAK9J,GAAL,CAAS,QAAT,KAAsB,IAArC;AACA,sBAAMsH,QAAQ,QAAKtH,GAAL,CAAS,OAAT,KAAqB,CAAnC;;AAEA,sBAAMqM,UAAU,IAAItH,IAAJ,EAAhB;AACA,sBAAMuH,YAAY,IAAIvH,IAAJ,EAAlB;AACAuH,0BAAUC,OAAV,CAAkBF,QAAQG,OAAR,KAAoB1H,SAASgF,MAAT,CAAtC;;AAEA,sBAAM2C,iBAAiB3H,SAASwH,UAAUtH,OAAV,KAAsB,IAA/B,CAAvB;AACA,sBAAM0H,eAAe5H,SAASuH,QAAQrH,OAAR,KAAoB,IAA7B,CAArB;;AAEA;AACA,sBAAMwI,WAAW,MAAM,QAAKlN,KAAL,CAAW,OAAX,EAClBO,KADkB,CACZ,+CADY,EAElBD,KAFkB,CAEZ;AACHJ,+BAAW,CADR;AAEHE,gCAAY;AAFT,iBAFY,EAMlBI,KANkB,CAMZ,CAAC,kBAAD,CANY,EAOlBwG,KAPkB,CAOZA,KAPY,EAQlBhG,MARkB,EAAvB;;AAUA;AACA,qBAAK,MAAMmM,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,0BAAME,UAAU9K,WAAW6K,QAAQ5K,YAAnB,IAAmC4K,QAAQE,WAA3D;AACA,0BAAMxC,aAAauC,UAAU,IAA7B,CAF4B,CAEO;;AAEnCD,4BAAQG,KAAR,GAAgBH,QAAQI,YAAxB;AACAJ,4BAAQxC,KAAR,GAAgBwC,QAAQE,WAAxB;AACAF,4BAAQC,OAAR,GAAkBA,QAAQ5K,OAAR,CAAgB,CAAhB,CAAlB;AACA2K,4BAAQtC,UAAR,GAAqBA,WAAWrI,OAAX,CAAmB,CAAnB,CAArB;AACH;;AAED,uBAAO,QAAKY,OAAL,CAAa8J,QAAb,CAAP;AACH,aAlCD,CAkCE,OAAOtE,KAAP,EAAc;AACZ3F,wBAAQ2F,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,QAAKvE,IAAL,CAAU,UAAV,CAAP;AACH;AAtCqB;AAuCzB;;AAED;;;;AAIMmJ,yBAAN,GAA8B;AAAA;;AAAA;AAC1B,gBAAI;AACA,sBAAMhE,SAAS,QAAK9J,GAAL,CAAS,QAAT,KAAsB,IAArC;AACA,sBAAMsH,QAAQ,QAAKtH,GAAL,CAAS,OAAT,KAAqB,CAAnC;;AAEA;AACA,sBAAMqK,eAAe,MAAM,QAAK/J,KAAL,CAAW,cAAX,EACtBO,KADsB,CAChB,kDADgB,EAEtBD,KAFsB,CAEhB;AACHwJ,kCAAc,CADX;AAEHD,+BAAW;AAFR,iBAFgB,EAMtBrJ,KANsB,CAMhB,CAAC,kBAAD,CANgB,EAOtBwG,KAPsB,CAOhBA,KAPgB,EAQtBhG,MARsB,EAA3B;;AAUA;AACA,qBAAK,MAAMgJ,WAAX,IAA0BD,YAA1B,EAAwC;AACpC;AACA,0BAAME,WAAW,MAAM,QAAKjK,KAAL,CAAW,MAAX,EAAmBM,KAAnB,CAAyB;AAC5CO,4BAAImJ,YAAYL;AAD4B,qBAAzB,EAEpBpJ,KAFoB,CAEd,iBAFc,EAEKkD,IAFL,EAAvB;;AAIA;AACA,0BAAM8G,YAAY,MAAM,QAAKvK,KAAL,CAAW,oBAAX,EAAiCM,KAAjC,CAAuC;AAC3DO,4BAAImJ,YAAYQ;AAD2C,qBAAvC,EAErB/G,IAFqB,EAAxB;;AAIAuG,gCAAYpK,IAAZ,GAAmBqK,WAAWA,SAASC,QAApB,GAA+B,KAAlD;AACAF,gCAAYM,MAAZ,GAAqBL,WAAWA,SAASK,MAApB,GAA8B,oCAAmCN,YAAYpK,IAAK,6BAAvG;AACAoK,gCAAYT,KAAZ,GAAoBgB,YAAYA,UAAUE,UAAtB,GAAmC,OAAvD;AACAT,gCAAYW,KAAZ,GAAoBrI,WAAW0H,YAAYY,WAAZ,IAA2B,CAAtC,EAAyCpI,OAAzC,CAAiD,CAAjD,CAApB;AACAwH,gCAAYa,UAAZ,GAAyBvI,WAAW0H,YAAYc,gBAAZ,IAAgC,CAA3C,EAA8CtI,OAA9C,CAAsD,CAAtD,CAAzB;AACH;;AAED,uBAAO,QAAKY,OAAL,CAAa2G,YAAb,CAAP;AACH,aAnCD,CAmCE,OAAOnB,KAAP,EAAc;AACZ3F,wBAAQ2F,KAAR,CAAc,YAAd,EAA4BA,KAA5B;AACA,uBAAO,QAAKvE,IAAL,CAAU,WAAV,CAAP;AACH;AAvCyB;AAwC7B;;AAED;;;;AAIMoJ,eAAN,GAAoB;AAAA;;AAAA;AAChB,gBAAI;AACA,sBAAMjE,SAAS,QAAK9J,GAAL,CAAS,QAAT,KAAsB,IAArC;AACA,sBAAMgO,OAAO,QAAKhO,GAAL,CAAS,MAAT,KAAoB,OAAjC;;AAEA;AACA,sBAAMW,OAAO,EAAb;AACA,sBAAMsN,SAAS,EAAf;AACA,sBAAMC,MAAM,IAAInJ,IAAJ,EAAZ;;AAEA,qBAAK,IAAIoJ,IAAIrJ,SAASgF,MAAT,IAAmB,CAAhC,EAAmCqE,KAAK,CAAxC,EAA2CA,GAA3C,EAAgD;AAC5C,0BAAMC,OAAO,IAAIrJ,IAAJ,CAASmJ,GAAT,CAAb;AACAE,yBAAK7B,OAAL,CAAa6B,KAAK5B,OAAL,KAAiB2B,CAA9B;AACAF,2BAAO7K,IAAP,CAAYgL,KAAKC,kBAAL,CAAwB,OAAxB,EAAiC,EAAEC,OAAO,OAAT,EAAkBC,KAAK,SAAvB,EAAjC,CAAZ;;AAEA,wBAAIP,SAAS,OAAb,EAAsB;AAClBrN,6BAAKyC,IAAL,CAAU6J,KAAKC,KAAL,CAAWD,KAAKE,MAAL,KAAgB,KAA3B,IAAoC,KAA9C;AACH,qBAFD,MAEO,IAAIa,SAAS,QAAb,EAAuB;AAC1BrN,6BAAKyC,IAAL,CAAU6J,KAAKC,KAAL,CAAWD,KAAKE,MAAL,KAAgB,GAA3B,IAAkC,EAA5C;AACH,qBAFM,MAEA,IAAIa,SAAS,YAAb,EAA2B;AAC9BrN,6BAAKyC,IAAL,CAAU6J,KAAKC,KAAL,CAAWD,KAAKE,MAAL,KAAgB,IAA3B,IAAmC,IAA7C;AACH;AACJ;;AAED,uBAAO,QAAKzJ,OAAL,CAAa;AAChBuK,4BAAQA,MADQ;AAEhBtN,0BAAMA;AAFU,iBAAb,CAAP;AAIH,aA3BD,CA2BE,OAAOuI,KAAP,EAAc;AACZ3F,wBAAQ2F,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,QAAKvE,IAAL,CAAU,UAAV,CAAP;AACH;AA/Be;AAgCnB;;AAED;;;;AAIM6J,kBAAN,GAAuB;AAAA;;AAAA;AACnB,gBAAI;AACA,sBAAMC,QAAQ,IAAI1J,IAAJ,EAAd;AACA,sBAAM2J,aAAa,IAAI3J,IAAJ,CAAS0J,MAAME,WAAN,EAAT,EAA8BF,MAAMG,QAAN,EAA9B,EAAgDH,MAAMjC,OAAN,EAAhD,CAAnB;AACA,sBAAMqC,sBAAsB/J,SAAS4J,WAAW1J,OAAX,KAAuB,IAAhC,CAA5B;AACA,sBAAM8J,oBAAoBhK,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAA1B;;AAEA;AACA,sBAAM+J,cAAc,MAAM,QAAKzO,KAAL,CAAW,OAAX,EAAoBM,KAApB,CAA0B;AAChDoC,8BAAU,CAAC,SAAD,EAAY6L,mBAAZ,EAAiCC,iBAAjC;AADsC,iBAA1B,EAEvBzL,KAFuB,EAA1B;;AAIA;AACA,sBAAM2L,qBAAqB,MAAM,QAAK1O,KAAL,CAAW,OAAX,EAAoBM,KAApB,CAA0B;AACvDoC,8BAAU,CAAC,SAAD,EAAY6L,mBAAZ,EAAiCC,iBAAjC,CAD6C;AAEvDnC,kCAAc,CAAC,IAAD,EAAO,GAAP;AAFyC,iBAA1B,EAG9BhB,GAH8B,CAG1B,cAH0B,CAAjC;AAIA,sBAAMsD,eAAerM,WAAWoM,sBAAsB,CAAjC,CAArB;;AAEA;AACA,sBAAME,cAAcjC,KAAKC,KAAL,CAAWD,KAAKE,MAAL,KAAgB,EAA3B,IAAiC,EAArD;;AAEA,sBAAMgC,eAAe;AACjBJ,iCAAaA,WADI;AAEjBE,kCAAcA,YAFG;AAGjBC,iCAAaA;AAHI,iBAArB;;AAMA,uBAAO,QAAKxL,OAAL,CAAayL,YAAb,CAAP;AACH,aA5BD,CA4BE,OAAOjG,KAAP,EAAc;AACZ3F,wBAAQ2F,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,QAAKvE,IAAL,CAAU,UAAV,CAAP;AACH;AAhCkB;AAiCtB;;AAED;;;;AAIMyK,sBAAN,GAA2B;AAAA;;AAAA;AACvB,kBAAMrP,OAAO,QAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,QAAKD,GAAL,CAAS,MAAT,KAAoB,EAAjC;;AAEA;AACA,kBAAMqP,YAAY,MAAM,QAAK/O,KAAL,CAAW,OAAX,EACnBoG,KADmB,CACb,GADa,EAEnBC,IAFmB,CAEd;AACFC,uBAAO,oBADL;AAEFD,sBAAM,MAFJ;AAGFE,oBAAI,IAHF;AAIFC,oBAAI,CAAC,MAAD,EAAS,aAAT;AAJF,aAFc,EAQnBH,IARmB,CAQd;AACFC,uBAAO,UADL;AAEFD,sBAAM,MAFJ;AAGFE,oBAAI,GAHF;AAIFC,oBAAI,CAAC,eAAD,EAAkB,MAAlB;AAJF,aARc,EAcnBlG,KAdmB,CAcb;AACH,+BAAe;AADZ,aAda,EAiBnBC,KAjBmB,CAiBb,gNAjBa,EAkBnBC,KAlBmB,CAkBb,CAAC,kBAAD,EAAqB,WAArB,CAlBa,EAmBnBf,IAnBmB,CAmBdA,IAnBc,EAmBRE,IAnBQ,EAoBnBc,WApBmB,EAAxB;;AAsBA;AACA,iBAAK,MAAMG,IAAX,IAAmBmO,UAAU1O,IAA7B,EAAmC;AAC/B,oBAAIO,KAAKgB,cAAL,KAAwB,IAA5B,EAAkC;AAC9BhB,yBAAKoO,WAAL,GAAmB,aAAnB,CAD8B,CACS;AACvCpO,yBAAKqO,gBAAL,GAAwB,KAAxB;AACH,iBAHD,MAGO,IAAIrO,KAAKgB,cAAL,KAAwB,CAA5B,EAA+B;AAClChB,yBAAKoO,WAAL,GAAmB,QAAnB,CADkC,CACK;AACvCpO,yBAAKqO,gBAAL,GAAwB,KAAxB;AACH,iBAHM,MAGA;AACHrO,yBAAKoO,WAAL,GAAmB,UAAnB,CADG,CACoC;AACvCpO,yBAAKqO,gBAAL,GAAwB,QAAxB;AACH;;AAED;AACArO,qBAAKyB,oBAAL,GAA4BzB,KAAKiB,eAAL,GACxB,CAACS,WAAW1B,KAAK2B,YAAhB,IAAgC3B,KAAKiB,eAArC,GAAuD,GAAxD,EAA6DW,OAA7D,CAAqE,CAArE,CADwB,GACkD,MAD9E;;AAGA;AACA5B,qBAAKsO,cAAL,GAAsBtO,KAAKsO,cAAL,GAClB7P,OAAOsD,IAAP,CAAY/B,KAAKsO,cAAjB,EAAiCtM,MAAjC,CAAwC,qBAAxC,CADkB,GAC+C,EADrE;AAEH;;AAED,mBAAO,QAAKQ,OAAL,CAAa2L,SAAb,CAAP;AAjDuB;AAkD1B;;AAED;;;;;;AAMMxJ,4BAAN,CAA+B/B,OAA/B,EAAwCkC,SAAxC,EAAmDrF,IAAnD,EAAyD;AAAA;;AAAA;AACrD,gBAAI;AACA,sBAAM8O,UAAU;AACZpO,8BAAUyC,OADE;AAEZkC,+BAAWA,SAFC;AAGZ0J,oCAAgBC,KAAKC,SAAL,CAAejP,IAAf,CAHJ;AAIZkP,iCAAa,QAAKC,GAAL,CAASC,KAAT,CAAexF,QAAf,GAA0B,QAAKuF,GAAL,CAASC,KAAT,CAAexF,QAAf,CAAwBpJ,EAAlD,GAAuD,CAJxD;AAKZ6O,iCAAalL,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC;AALD,iBAAhB;;AAQA;AACAzB,wBAAQC,GAAR,CAAY,SAAZ,EAAuBiM,OAAvB;AACH,aAXD,CAWE,OAAOvG,KAAP,EAAc;AACZ3F,wBAAQ2F,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACH;AAdoD;AAexD;;AAED;;;;;;AAMMC,qBAAN,CAAwBnI,QAAxB,EAAkCgF,SAAlC,EAA6CiK,OAA7C,EAAsD;AAAA;;AAAA;AAClD,gBAAI;AACA,sBAAMR,UAAU;AACZS,+BAAWlP,SAAS2F,IAAT,CAAc,GAAd,CADC;AAEZX,+BAAWA,SAFC;AAGZmK,uCAAmBR,KAAKC,SAAL,CAAeK,OAAf,CAHP;AAIZJ,iCAAa,QAAKC,GAAL,CAASC,KAAT,CAAexF,QAAf,GAA0B,QAAKuF,GAAL,CAASC,KAAT,CAAexF,QAAf,CAAwBpJ,EAAlD,GAAuD,CAJxD;AAKZ6O,iCAAalL,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC;AALD,iBAAhB;;AAQAzB,wBAAQC,GAAR,CAAY,SAAZ,EAAuBiM,OAAvB;AACH,aAVD,CAUE,OAAOvG,KAAP,EAAc;AACZ3F,wBAAQ2F,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACH;AAbiD;AAcrD;;AAED;;;;AAIMkH,yBAAN,GAA8B;AAAA;;AAAA;AAC1B,kBAAMtM,UAAU,QAAK9D,GAAL,CAAS,UAAT,CAAhB;;AAEA,gBAAI,CAAC8D,OAAL,EAAc;AACV,uBAAO,QAAKa,IAAL,CAAU,UAAV,CAAP;AACH;;AAED;AACA,kBAAMC,QAAQ,MAAM,QAAKtE,KAAL,CAAW,OAAX,EAAoBM,KAApB,CAA0B;AAC1CO,oBAAI2C,OADsC;AAE1CtD,2BAAW;AAF+B,aAA1B,EAGjBuD,IAHiB,EAApB;;AAKA,gBAAI,CAACa,KAAL,EAAY;AACR,uBAAO,QAAKD,IAAL,CAAU,OAAV,CAAP;AACH;;AAED,kBAAMqE,QAAQpG,WAAWgC,MAAM/B,YAAjB,CAAd;AACA,kBAAMwN,aAAavL,SAASF,MAAM+I,WAAN,IAAqB,CAA9B,CAAnB;;AAEA;AACA,gBAAI2C,oBAAoB,EAAxB;;AAEA,gBAAItH,SAAS,GAAb,EAAkB;AACdsH,oCAAoB;AAChBjO,mCAAe,GADC;AAEhBC,iCAAa,GAFG;AAGhBC,iCAAa,GAHG;AAIhBC,sCAAkB,GAJF;AAKhBC,wCAAoB,CALJ;AAMhB8N,4BAAQ;AANQ,iBAApB;AAQH,aATD,MASO,IAAIvH,SAAS,GAAb,EAAkB;AACrBsH,oCAAoB;AAChBjO,mCAAe,IADC;AAEhBC,iCAAa,GAFG;AAGhBC,iCAAa,GAHG;AAIhBC,sCAAkB,GAJF;AAKhBC,wCAAoB4N,aAAa,EAAb,GAAkB,CAAlB,GAAsB,CAL1B;AAMhBE,4BAAQ;AANQ,iBAApB;AAQH,aATM,MASA;AACHD,oCAAoB;AAChBjO,mCAAe,IADC;AAEhBC,iCAAa,GAFG;AAGhBC,iCAAa,GAHG;AAIhBC,sCAAkB,GAJF;AAKhBC,wCAAoB,CALJ;AAMhB8N,4BAAQ;AANQ,iBAApB;AAQH;;AAED;AACA,gBAAIF,aAAa,GAAjB,EAAsB;AAClBC,kCAAkBjO,aAAlB,IAAmC,GAAnC;AACAiO,kCAAkBhO,WAAlB,IAAiC,GAAjC;AACAgO,kCAAkB/N,WAAlB,IAAiC,GAAjC;AACA+N,kCAAkB9N,gBAAlB,IAAsC,GAAtC;AACA8N,kCAAkBC,MAAlB,IAA4B,cAA5B;AACH;;AAED;AACArL,mBAAOC,IAAP,CAAYmL,iBAAZ,EAA+B9O,OAA/B,CAAuC,eAAO;AAC1C,oBAAI,OAAO8O,kBAAkBE,GAAlB,CAAP,KAAkC,QAAlC,IAA8CA,IAAIC,QAAJ,CAAa,MAAb,CAAlD,EAAwE;AACpEH,sCAAkBE,GAAlB,IAAyBvD,KAAKyD,KAAL,CAAWJ,kBAAkBE,GAAlB,IAAyB,EAApC,IAA0C,EAAnE;AACH;AACJ,aAJD;;AAMA,mBAAO,QAAK9M,OAAL,CAAa;AAChBiN,4BAAY;AACRxP,wBAAIyD,MAAMzD,EADF;AAERjB,0BAAM0E,MAAM1E,IAFJ;AAGR8I,2BAAOpE,MAAM/B,YAHL;AAIR8K,iCAAa/I,MAAM+I;AAJX,iBADI;AAOhBiD,oCAAoBN;AAPJ,aAAb,CAAP;AApE0B;AA6E7B;AAnsC+B,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\distribution.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\n\nmodule.exports = class extends Base {\n    /**\n     * 获取分销池商品列表（所有商品都在池子里，显示分销状态）\n     * @return {Promise} []\n     */\n    async goodsListAction() {\n        const page = this.get('page') || 1;\n        const size = this.get('size') || 20;\n        const name = this.get('name') || '';\n        const categoryId = this.get('category_id') || '';\n        const isOnSale = this.get('is_on_sale') || '';\n        const distributionStatus = this.get('distribution_status') || ''; // all, distributed, undistributed\n\n        const model = this.model('goods');\n        let whereMap = {\n            is_delete: 0\n        };\n\n        // 搜索条件\n        if (name) {\n            whereMap.name = ['like', `%${name}%`];\n        }\n        if (categoryId) {\n            whereMap.category_id = categoryId;\n        }\n        if (isOnSale !== '') {\n            whereMap.is_on_sale = isOnSale;\n        }\n\n        // 获取所有商品（分销池概念：所有商品都在池子里）\n        const data = await model.where(whereMap)\n            .field('id,category_id,name,goods_brief,retail_price,goods_number,sell_volume,is_on_sale,https_pic_url,list_pic_url,goods_unit,sort_order')\n            .order(['sort_order asc', 'id desc'])\n            .page(page, size)\n            .countSelect();\n\n        // 批量获取分销配置，提高性能\n        const goodsIds = data.data.map(item => item.id);\n        const distributionConfigs = await this.model('goods_distribution')\n            .where({ goods_id: ['IN', goodsIds] })\n            .select();\n\n        // 创建分销配置映射\n        const configMap = {};\n        distributionConfigs.forEach(config => {\n            configMap[config.goods_id] = config;\n        });\n\n        // 批量获取分类信息\n        const categoryIds = [...new Set(data.data.map(item => item.category_id))];\n        const categories = await this.model('category')\n            .where({ id: ['IN', categoryIds] })\n            .field('id,name')\n            .select();\n\n        const categoryMap = {};\n        categories.forEach(cat => {\n            categoryMap[cat.id] = cat.name;\n        });\n\n        // 处理每个商品的分销信息\n        const processedData = [];\n        for (const item of data.data) {\n            // 设置分类名称\n            item.category_name = categoryMap[item.category_id] || '未分类';\n\n            // 设置分销配置（体现分销池概念：每个商品都有分销状态）\n            const distributionConfig = configMap[item.id];\n            if (distributionConfig) {\n                // 商品已投入分销池\n                item.is_distributed = distributionConfig.is_distributed;\n                item.commission_rate = distributionConfig.commission_rate || 0;\n                item.commission_type = distributionConfig.commission_type || 'default';\n                item.personal_rate = distributionConfig.personal_rate || 0;\n                item.level1_rate = distributionConfig.level1_rate || 0;\n                item.level2_rate = distributionConfig.level2_rate || 0;\n                item.team_leader_rate = distributionConfig.team_leader_rate || 0;\n                item.min_level_required = distributionConfig.min_level_required || 1;\n                item.distribution_status = distributionConfig.is_distributed ? 'active' : 'inactive';\n                item.estimated_commission = (parseFloat(item.retail_price) * distributionConfig.commission_rate / 100).toFixed(2);\n                item.in_pool_time = distributionConfig.add_time ? moment.unix(distributionConfig.add_time).format('YYYY-MM-DD HH:mm:ss') : '';\n            } else {\n                // 商品未入池（没有分销配置记录）\n                item.is_distributed = null;\n                item.commission_rate = 0;\n                item.commission_type = 'none';\n                item.personal_rate = 0;\n                item.level1_rate = 0;\n                item.level2_rate = 0;\n                item.team_leader_rate = 0;\n                item.min_level_required = 1;\n                item.distribution_status = 'pending';\n                item.estimated_commission = '0.00';\n                item.in_pool_time = '';\n            }\n\n            // 计算总佣金率\n            item.total_commission_rate = (\n                parseFloat(item.personal_rate || 0) +\n                parseFloat(item.level1_rate || 0) +\n                parseFloat(item.level2_rate || 0) +\n                parseFloat(item.team_leader_rate || 0)\n            ).toFixed(2);\n\n            // 格式化时间\n            item.add_time = item.add_time ? moment.unix(item.add_time).format('YYYY-MM-DD HH:mm:ss') : moment().format('YYYY-MM-DD HH:mm:ss');\n\n            // 根据分销状态筛选\n            if (distributionStatus === 'distributed' && item.is_distributed !== 1) {\n                continue;\n            }\n            if (distributionStatus === 'undistributed' && item.is_distributed === 1) {\n                continue;\n            }\n\n            processedData.push(item);\n        }\n\n        // 更新数据\n        data.data = processedData;\n        data.count = processedData.length;\n\n        // 调试：输出前几个商品的分销状态\n        if (processedData.length > 0) {\n            console.log('=== 商品列表API返回数据 ===');\n            console.log('总商品数:', processedData.length);\n            processedData.slice(0, 3).forEach(item => {\n                console.log(`商品${item.id}: is_distributed=${item.is_distributed}, commission_rate=${item.commission_rate}`);\n            });\n        }\n\n        return this.success(data);\n    }\n    \n    /**\n     * 获取商品分类列表\n     * @return {Promise} []\n     */\n    async categoriesAction() {\n        const model = this.model('category');\n        const data = await model.where({\n            is_show: 1\n        }).field('id,name,front_desc,parent_id').order(['sort_order asc']).select();\n        \n        return this.success(data);\n    }\n    \n    /**\n     * 获取分销配置\n     * @return {Promise} []\n     */\n    async configAction() {\n        const goodsId = this.get('goods_id');\n        \n        if (goodsId) {\n            // 获取单个商品的分销配置\n            const config = await this.model('goods_distribution').where({\n                goods_id: goodsId\n            }).find();\n            return this.success(config || {});\n        } else {\n            // 获取所有分销配置\n            const configs = await this.model('goods_distribution').select();\n            return this.success(configs);\n        }\n    }\n    \n    /**\n     * 投入/移出分销池操作\n     * @return {Promise} []\n     */\n    async statusAction() {\n        const goodsId = this.post('goods_id');\n        const isDistributed = this.post('is_distributed');\n        const commissionRate = this.post('commission_rate') || 0;\n        const commissionType = this.post('commission_type') || 'default';\n        const personalRate = this.post('personal_rate') || 0;\n        const level1Rate = this.post('level1_rate') || 0;\n        const level2Rate = this.post('level2_rate') || 0;\n        const teamLeaderRate = this.post('team_leader_rate') || 0;\n        const minLevelRequired = this.post('min_level_required') || 1;\n\n        console.log('=== 分销状态设置API ===');\n        console.log('商品ID:', goodsId);\n        console.log('分销状态:', isDistributed);\n        console.log('佣金数据:', { commissionRate, personalRate, level1Rate, level2Rate, teamLeaderRate });\n\n        if (!goodsId) {\n            return this.fail('商品ID不能为空');\n        }\n\n        // 验证商品是否存在\n        const goods = await this.model('goods').where({\n            id: goodsId,\n            is_delete: 0\n        }).find();\n\n        if (!goods) {\n            return this.fail('商品不存在');\n        }\n\n        const model = this.model('goods_distribution');\n        const currentTime = parseInt(new Date().getTime() / 1000);\n\n        // 检查是否已存在配置\n        const existingConfig = await model.where({\n            goods_id: goodsId\n        }).find();\n\n        console.log('现有配置查询结果:', existingConfig);\n        console.log('existingConfig是否为空:', !existingConfig || Object.keys(existingConfig).length === 0);\n\n        const configData = {\n            goods_id: goodsId,\n            is_distributed: isDistributed,\n            commission_rate: commissionRate,\n            commission_type: commissionType,\n            personal_rate: personalRate,\n            level1_rate: level1Rate,\n            level2_rate: level2Rate,\n            team_leader_rate: teamLeaderRate,\n            min_level_required: minLevelRequired,\n            update_time: currentTime\n        };\n\n        let operationText = '';\n        if (isDistributed == 1) {\n            operationText = '投入分销池';\n            // 前端必须提供佣金比例，不再自动设置\n            if (personalRate == 0) {\n                return this.fail('投入分销池时必须设置佣金比例');\n            }\n            configData.commission_rate = personalRate; // 使用个人佣金作为主佣金\n        } else {\n            operationText = '移出分销池';\n        }\n\n        console.log('配置数据:', configData);\n\n        // 修复判断逻辑：检查是否真的存在有效配置\n        const hasValidConfig = existingConfig && Object.keys(existingConfig).length > 0 && existingConfig.id;\n\n        if (hasValidConfig) {\n            // 更新现有配置\n            console.log('更新现有配置...');\n            const updateResult = await model.where({\n                goods_id: goodsId\n            }).update(configData);\n            console.log('更新结果:', updateResult);\n\n            // 验证更新是否成功\n            const verifyConfig = await model.where({\n                goods_id: goodsId\n            }).find();\n            console.log('更新后验证配置:', verifyConfig);\n        } else {\n            // 创建新配置（商品首次进入分销池管理）\n            console.log('创建新配置...');\n            configData.add_time = currentTime;\n            const addResult = await model.add(configData);\n            console.log('创建结果:', addResult);\n\n            // 验证创建是否成功\n            const verifyConfig = await model.where({\n                goods_id: goodsId\n            }).find();\n            console.log('创建后验证配置:', verifyConfig);\n        }\n\n        // 记录操作日志\n        await this.logDistributionOperation(goodsId, operationText, configData);\n\n        console.log('操作完成:', operationText);\n\n        return this.success({\n            message: `商品${operationText}成功`,\n            goods_name: goods.name,\n            operation: operationText,\n            config: configData\n        });\n    }\n    \n    /**\n     * 设置商品佣金\n     * @return {Promise} []\n     */\n    async commissionAction() {\n        const goodsId = this.post('goods_id');\n        const commissionRate = this.post('commission_rate');\n        const commissionType = this.post('commission_type') || 'custom';\n        \n        if (!goodsId || commissionRate === undefined) {\n            return this.fail('参数不完整');\n        }\n        \n        const model = this.model('goods_distribution');\n        \n        // 检查是否已存在配置\n        const existingConfig = await model.where({\n            goods_id: goodsId\n        }).find();\n        \n        const configData = {\n            commission_rate: commissionRate,\n            commission_type: commissionType,\n            update_time: parseInt(new Date().getTime() / 1000)\n        };\n        \n        if (existingConfig) {\n            // 更新现有配置\n            await model.where({\n                goods_id: goodsId\n            }).update(configData);\n        } else {\n            // 创建新配置\n            configData.goods_id = goodsId;\n            configData.is_distributed = 1; // 设置佣金时自动加入分销\n            configData.add_time = parseInt(new Date().getTime() / 1000);\n            await model.add(configData);\n        }\n        \n        return this.success('佣金设置成功');\n    }\n    \n    /**\n     * 获取分销池统计数据\n     * @return {Promise} []\n     */\n    async statsAction() {\n        const model = this.model('goods_distribution');\n\n        // 获取所有商品总数（分销池总容量）\n        const totalGoods = await this.model('goods').where({\n            is_delete: 0\n        }).count();\n\n        // 获取已投入分销池的商品数量\n        const totalDistributed = await model.where({\n            is_distributed: 1\n        }).count();\n\n        // 获取待投入分销池的商品数量（未配置分销的商品）\n        const configuredGoods = await model.count();\n        const pendingGoods = totalGoods - configuredGoods;\n\n        // 获取已配置但未激活的商品数量\n        const inactiveGoods = await model.where({\n            is_distributed: 0\n        }).count();\n\n        // 计算总佣金池和平均佣金率\n        const distributedGoods = await model.alias('gd')\n            .join({\n                table: 'goods',\n                join: 'left',\n                as: 'g',\n                on: ['gd.goods_id', 'g.id']\n            })\n            .where({\n                'gd.is_distributed': 1,\n                'g.is_delete': 0\n            })\n            .field('g.retail_price, gd.commission_rate, gd.personal_rate, gd.level1_rate, gd.level2_rate, gd.team_leader_rate')\n            .select();\n\n        let totalCommission = 0;\n        let totalCommissionRate = 0;\n        distributedGoods.forEach(item => {\n            const itemCommission = parseFloat(item.retail_price) * parseFloat(item.commission_rate || 0) / 100;\n            totalCommission += itemCommission;\n            totalCommissionRate += parseFloat(item.commission_rate || 0);\n        });\n\n        // 获取高级商品数量（需要2级以上权限的商品）\n        const premiumProducts = await model.where({\n            is_distributed: 1,\n            min_level_required: ['>=', 2]\n        }).count();\n\n        // 获取热门商品数量（销量>50的分销商品）\n        const hotProducts = await model.alias('gd')\n            .join({\n                table: 'goods',\n                join: 'left',\n                as: 'g',\n                on: ['gd.goods_id', 'g.id']\n            })\n            .where({\n                'gd.is_distributed': 1,\n                'g.is_delete': 0,\n                'g.sell_volume': ['>', 50]\n            })\n            .count();\n\n        // 获取分类统计\n        const categoryStats = await model.alias('gd')\n            .join({\n                table: 'goods',\n                join: 'left',\n                as: 'g',\n                on: ['gd.goods_id', 'g.id']\n            })\n            .join({\n                table: 'category',\n                join: 'left',\n                as: 'c',\n                on: ['g.category_id', 'c.id']\n            })\n            .where({\n                'gd.is_distributed': 1,\n                'g.is_delete': 0\n            })\n            .field('c.id as category_id, c.name as category_name, COUNT(*) as count')\n            .group('c.id')\n            .order('count DESC')\n            .limit(5)\n            .select();\n\n        return this.success({\n            // 分销池基础统计\n            poolStats: {\n                totalCapacity: totalGoods,           // 分销池总容量\n                activeProducts: totalDistributed,    // 已激活分销的商品\n                inactiveProducts: inactiveGoods,     // 已配置但未激活的商品\n                pendingProducts: pendingGoods,       // 待配置的商品\n                utilizationRate: totalGoods > 0 ? ((totalDistributed / totalGoods) * 100).toFixed(2) : 0 // 分销池利用率\n            },\n\n            // 佣金统计\n            commissionStats: {\n                totalCommissionPool: totalCommission.toFixed(2),\n                averageCommissionRate: distributedGoods.length > 0 ?\n                    (totalCommissionRate / distributedGoods.length).toFixed(2) : 0,\n                estimatedMonthlyPayout: (totalCommission * 0.1).toFixed(2) // 预估月度支出\n            },\n\n            // 商品分级统计\n            productTiers: {\n                basicProducts: totalDistributed - premiumProducts,  // 基础商品\n                premiumProducts: premiumProducts,                   // 高级商品\n                hotProducts: hotProducts                           // 热门商品\n            },\n\n            // 分类统计\n            topCategories: categoryStats,\n\n            // 兼容旧版本字段\n            totalProducts: totalGoods,\n            distributedProducts: totalDistributed,\n            undistributedProducts: totalGoods - totalDistributed,\n            totalCommission: totalCommission.toFixed(2),\n            hotProducts: hotProducts\n        });\n    }\n    \n    /**\n     * 批量投入/移出分销池操作\n     * @return {Promise} []\n     */\n    async batchAction() {\n        const goodsIds = this.post('goods_ids'); // 商品ID数组\n        const isDistributed = this.post('is_distributed');\n        const commissionRate = this.post('commission_rate') || 0;\n        const commissionType = this.post('commission_type') || 'default';\n        const personalRate = this.post('personal_rate') || 0;\n        const level1Rate = this.post('level1_rate') || 0;\n        const level2Rate = this.post('level2_rate') || 0;\n        const teamLeaderRate = this.post('team_leader_rate') || 0;\n        const minLevelRequired = this.post('min_level_required') || 1;\n        const templateId = this.post('template_id'); // 佣金模板ID\n\n        if (!goodsIds || !Array.isArray(goodsIds) || goodsIds.length === 0) {\n            return this.fail('请选择要操作的商品');\n        }\n\n        // 验证商品是否存在\n        const validGoods = await this.model('goods').where({\n            id: ['IN', goodsIds],\n            is_delete: 0\n        }).field('id,name,retail_price').select();\n\n        if (validGoods.length !== goodsIds.length) {\n            return this.fail('部分商品不存在或已删除');\n        }\n\n        const model = this.model('goods_distribution');\n        const currentTime = parseInt(new Date().getTime() / 1000);\n\n        let successCount = 0;\n        let failCount = 0;\n        const results = [];\n\n        for (const goods of validGoods) {\n            try {\n                // 检查是否已存在配置\n                const existingConfig = await model.where({\n                    goods_id: goods.id\n                }).find();\n\n                let configData = {\n                    goods_id: goods.id,\n                    is_distributed: isDistributed,\n                    commission_rate: commissionRate,\n                    commission_type: commissionType,\n                    personal_rate: personalRate,\n                    level1_rate: level1Rate,\n                    level2_rate: level2Rate,\n                    team_leader_rate: teamLeaderRate,\n                    min_level_required: minLevelRequired,\n                    update_time: currentTime\n                };\n\n                // 如果使用模板，应用模板配置\n                if (templateId) {\n                    const template = await this.model('commission_templates').where({\n                        id: templateId\n                    }).find();\n\n                    if (template) {\n                        configData.personal_rate = template.personal_rate;\n                        configData.level1_rate = template.level1_rate;\n                        configData.level2_rate = template.level2_rate;\n                        configData.team_leader_rate = template.team_leader_rate;\n                        configData.min_level_required = template.min_level_required;\n                        configData.commission_rate = template.personal_rate; // 主佣金率使用个人推广率\n                        configData.commission_type = 'template';\n                    }\n                }\n\n                // 如果是投入分销池且没有设置佣金，使用默认佣金\n                if (isDistributed == 1 && configData.personal_rate == 0 && !templateId) {\n                    const price = parseFloat(goods.retail_price);\n                    if (price <= 100) {\n                        configData.personal_rate = 8.0;\n                        configData.level1_rate = 3.0;\n                        configData.level2_rate = 1.0;\n                        configData.team_leader_rate = 2.0;\n                    } else if (price <= 500) {\n                        configData.personal_rate = 10.0;\n                        configData.level1_rate = 4.0;\n                        configData.level2_rate = 2.0;\n                        configData.team_leader_rate = 3.0;\n                    } else {\n                        configData.personal_rate = 15.0;\n                        configData.level1_rate = 6.0;\n                        configData.level2_rate = 3.0;\n                        configData.team_leader_rate = 5.0;\n                    }\n                    configData.commission_rate = configData.personal_rate;\n                    configData.commission_type = 'auto';\n                }\n\n                if (existingConfig) {\n                    // 更新现有配置\n                    await model.where({\n                        goods_id: goods.id\n                    }).update(configData);\n                } else {\n                    // 创建新配置\n                    configData.add_time = currentTime;\n                    await model.add(configData);\n                }\n\n                successCount++;\n                results.push({\n                    goods_id: goods.id,\n                    goods_name: goods.name,\n                    status: 'success',\n                    operation: isDistributed == 1 ? '投入分销池' : '移出分销池'\n                });\n\n            } catch (error) {\n                failCount++;\n                results.push({\n                    goods_id: goods.id,\n                    goods_name: goods.name,\n                    status: 'failed',\n                    error: error.message\n                });\n            }\n        }\n\n        // 记录批量操作日志\n        await this.logBatchOperation(goodsIds, isDistributed == 1 ? '批量投入分销池' : '批量移出分销池', {\n            successCount,\n            failCount,\n            templateId\n        });\n\n        return this.success({\n            message: `批量操作完成：成功${successCount}个，失败${failCount}个`,\n            successCount,\n            failCount,\n            results\n        });\n    }\n    \n    /**\n     * 获取佣金规则\n     * @return {Promise} []\n     */\n    async rulesAction() {\n        // 这里可以从配置表或者直接返回默认规则\n        const rules = [\n            { minPrice: 0, maxPrice: 100, rate: 8, description: '0-100元商品' },\n            { minPrice: 100, maxPrice: 500, rate: 10, description: '100-500元商品' },\n            { minPrice: 500, maxPrice: 999999, rate: 15, description: '500元以上商品' }\n        ];\n\n        return this.success({\n            defaultRate: 10,\n            rules: rules\n        });\n    }\n\n    /**\n     * 获取分销员列表\n     * @return {Promise} []\n     */\n    async distributorsAction() {\n        const page = this.get('page') || 1;\n        const size = this.get('size') || 20;\n        const search = this.get('search') || '';\n        const level = this.get('level') || '';\n        const status = this.get('status') || '';\n        const period = this.get('period') || 'daily';\n\n        const model = this.model('distributors');\n        let whereMap = {};\n\n        // 搜索条件\n        if (search) {\n            const userModel = this.model('user');\n            const users = await userModel.where({\n                'nickname': ['like', `%${search}%`]\n            }).field('id').select();\n\n            if (users.length > 0) {\n                whereMap.user_id = ['IN', users.map(u => u.id)];\n            } else {\n                whereMap.user_id = 0; // 没有匹配的用户\n            }\n        }\n\n        if (status) {\n            if (status === 'active') {\n                whereMap.is_active = 1;\n                whereMap.audit_status = 1;\n            } else if (status === 'inactive') {\n                whereMap.is_active = 0;\n            } else if (status === 'pending') {\n                whereMap.audit_status = 0;\n            }\n        }\n\n        // 获取分销员列表\n        const distributors = await model.where(whereMap)\n            .field('id,user_id,distributor_code,level_id,total_sales,total_commission,customer_count,is_active,audit_status,join_time')\n            .order(['total_sales desc', 'total_commission desc'])\n            .page(page, size)\n            .countSelect();\n\n        // 获取用户信息和等级信息\n        for (const distributor of distributors.data) {\n            // 获取用户信息\n            const userInfo = await this.model('user').where({\n                id: distributor.user_id\n            }).field('nickname,mobile,avatar').find();\n\n            if (userInfo) {\n                distributor.name = userInfo.nickname || '未设置';\n                distributor.phone = userInfo.mobile ? userInfo.mobile.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2') : '未绑定';\n                distributor.avatar = userInfo.avatar || `https://ui-avatars.com/api/?name=${distributor.name}&background=random&size=128`;\n            }\n\n            // 获取等级信息\n            const levelInfo = await this.model('distributor_levels').where({\n                id: distributor.level_id\n            }).find();\n\n            distributor.level = levelInfo ? levelInfo.level_name : '普通分销商';\n            distributor.commissionRate = levelInfo ? (parseFloat(levelInfo.commission_bonus) + 10) : 10; // 基础佣金10% + 等级加成\n\n            // 格式化数据\n            distributor.sales = parseFloat(distributor.total_sales || 0).toFixed(2);\n            distributor.commission = parseFloat(distributor.total_commission || 0).toFixed(2);\n            distributor.joinTime = moment.unix(distributor.join_time).format('YYYY-MM-DD');\n            distributor.status = distributor.is_active && distributor.audit_status === 1 ? 'active' :\n                                 !distributor.is_active ? 'inactive' : 'pending';\n        }\n\n        return this.success(distributors);\n    }\n\n    /**\n     * 获取分销员统计数据\n     * @return {Promise} []\n     */\n    async distributorStatsAction() {\n        try {\n            // 总分销员数\n            const totalDistributors = await this.model('distributors').where({\n                audit_status: 1\n            }).count();\n\n            // 团长数量（假设level_id为4的是团长）\n            const teamLeaders = await this.model('distributors').where({\n                audit_status: 1,\n                level_id: 4\n            }).count();\n\n            // 总佣金\n            const totalCommissionResult = await this.model('distributors').where({\n                audit_status: 1\n            }).sum('total_commission');\n            const totalCommission = parseFloat(totalCommissionResult || 0).toFixed(2);\n\n            // 总销售额\n            const totalSalesResult = await this.model('distributors').where({\n                audit_status: 1\n            }).sum('total_sales');\n            const totalSales = parseFloat(totalSalesResult || 0).toFixed(2);\n\n            const stats = {\n                totalDistributors: totalDistributors,\n                teamLeaders: teamLeaders,\n                totalCommission: totalCommission,\n                totalSales: totalSales\n            };\n\n            return this.success(stats);\n        } catch (error) {\n            console.error('获取分销员统计失败:', error);\n            return this.fail('获取统计数据失败');\n        }\n    }\n\n    /**\n     * 获取分销员详情\n     * @return {Promise} []\n     */\n    async distributorDetailAction() {\n        const distributorId = this.get('id');\n\n        if (!distributorId) {\n            return this.fail('分销员ID不能为空');\n        }\n\n        try {\n            const distributor = await this.model('distributors').where({\n                id: distributorId\n            }).find();\n\n            if (!distributor) {\n                return this.fail('分销员不存在');\n            }\n\n            // 获取用户信息\n            const userInfo = await this.model('user').where({\n                id: distributor.user_id\n            }).find();\n\n            // 获取等级信息\n            const levelInfo = await this.model('distributor_levels').where({\n                id: distributor.level_id\n            }).find();\n\n            // 组装详情数据\n            const detail = {\n                ...distributor,\n                name: userInfo ? userInfo.nickname : '未设置',\n                phone: userInfo ? userInfo.mobile : '未绑定',\n                avatar: userInfo ? userInfo.avatar : '',\n                level: levelInfo ? levelInfo.level_name : '普通分销商',\n                commissionRate: levelInfo ? (parseFloat(levelInfo.commission_bonus) + 10) : 10,\n                joinTime: moment.unix(distributor.join_time).format('YYYY-MM-DD HH:mm:ss')\n            };\n\n            return this.success(detail);\n        } catch (error) {\n            console.error('获取分销员详情失败:', error);\n            return this.fail('获取分销员详情失败');\n        }\n    }\n\n    /**\n     * 更新分销员状态\n     * @return {Promise} []\n     */\n    async updateDistributorStatusAction() {\n        const distributorId = this.post('distributor_id');\n        const isActive = this.post('is_active');\n\n        if (!distributorId) {\n            return this.fail('分销员ID不能为空');\n        }\n\n        try {\n            await this.model('distributors').where({\n                id: distributorId\n            }).update({\n                is_active: isActive ? 1 : 0,\n                update_time: parseInt(new Date().getTime() / 1000)\n            });\n\n            return this.success('状态更新成功');\n        } catch (error) {\n            console.error('更新分销员状态失败:', error);\n            return this.fail('更新状态失败');\n        }\n    }\n\n    /**\n     * 获取数据概览\n     * @return {Promise} []\n     */\n    async overviewAction() {\n        try {\n            const period = this.get('period') || '30';\n            const endDate = new Date();\n            const startDate = new Date();\n            startDate.setDate(endDate.getDate() - parseInt(period));\n\n            const startTimestamp = parseInt(startDate.getTime() / 1000);\n            const endTimestamp = parseInt(endDate.getTime() / 1000);\n\n            // 获取总销售额\n            const totalSalesResult = await this.model('order').where({\n                add_time: ['BETWEEN', startTimestamp, endTimestamp],\n                order_status: ['>=', 300]\n            }).sum('actual_price');\n            const totalSales = parseFloat(totalSalesResult || 0);\n\n            // 获取总佣金\n            const totalCommissionResult = await this.model('distributors').sum('total_commission');\n            const totalCommission = parseFloat(totalCommissionResult || 0);\n\n            // 获取活跃分销员数\n            const activeDistributors = await this.model('distributors').where({\n                is_active: 1,\n                audit_status: 1\n            }).count();\n\n            // 计算转化率（简化计算）\n            const totalOrders = await this.model('order').where({\n                add_time: ['BETWEEN', startTimestamp, endTimestamp]\n            }).count();\n            const conversionRate = totalOrders > 0 ? ((totalSales / totalOrders) * 0.01).toFixed(1) : 0;\n\n            // 计算增长率（模拟数据）\n            const overview = {\n                totalSales: totalSales,\n                salesGrowth: Math.floor(Math.random() * 20) + 5, // 5-25%\n                totalCommission: totalCommission,\n                commissionGrowth: Math.floor(Math.random() * 15) + 3, // 3-18%\n                activeDistributors: activeDistributors,\n                distributorGrowth: Math.floor(Math.random() * 25) + 10, // 10-35%\n                conversionRate: conversionRate,\n                conversionGrowth: Math.floor(Math.random() * 5) + 1, // 1-6%\n                totalDistributors: await this.model('distributors').count()\n            };\n\n            return this.success(overview);\n        } catch (error) {\n            console.error('获取数据概览失败:', error);\n            return this.fail('获取数据概览失败');\n        }\n    }\n\n    /**\n     * 获取热门商品\n     * @return {Promise} []\n     */\n    async topProductsAction() {\n        try {\n            const period = this.get('period') || '30';\n            const limit = this.get('limit') || 5;\n\n            const endDate = new Date();\n            const startDate = new Date();\n            startDate.setDate(endDate.getDate() - parseInt(period));\n\n            const startTimestamp = parseInt(startDate.getTime() / 1000);\n            const endTimestamp = parseInt(endDate.getTime() / 1000);\n\n            // 获取热门商品（按销量排序）\n            const products = await this.model('goods')\n                .field('id,name,list_pic_url,sell_volume,retail_price')\n                .where({\n                    is_delete: 0,\n                    is_on_sale: 1\n                })\n                .order(['sell_volume desc'])\n                .limit(limit)\n                .select();\n\n            // 计算每个商品的收入和佣金\n            for (const product of products) {\n                const revenue = parseFloat(product.retail_price) * product.sell_volume;\n                const commission = revenue * 0.12; // 假设12%佣金率\n\n                product.image = product.list_pic_url;\n                product.sales = product.sell_volume;\n                product.revenue = revenue.toFixed(2);\n                product.commission = commission.toFixed(2);\n            }\n\n            return this.success(products);\n        } catch (error) {\n            console.error('获取热门商品失败:', error);\n            return this.fail('获取热门商品失败');\n        }\n    }\n\n    /**\n     * 获取顶级分销员\n     * @return {Promise} []\n     */\n    async topDistributorsAction() {\n        try {\n            const period = this.get('period') || '30';\n            const limit = this.get('limit') || 5;\n\n            // 获取顶级分销员（按销售额排序）\n            const distributors = await this.model('distributors')\n                .field('id,user_id,level_id,total_sales,total_commission')\n                .where({\n                    audit_status: 1,\n                    is_active: 1\n                })\n                .order(['total_sales desc'])\n                .limit(limit)\n                .select();\n\n            // 获取用户信息和等级信息\n            for (const distributor of distributors) {\n                // 获取用户信息\n                const userInfo = await this.model('user').where({\n                    id: distributor.user_id\n                }).field('nickname,avatar').find();\n\n                // 获取等级信息\n                const levelInfo = await this.model('distributor_levels').where({\n                    id: distributor.level_id\n                }).find();\n\n                distributor.name = userInfo ? userInfo.nickname : '未设置';\n                distributor.avatar = userInfo ? userInfo.avatar : `https://ui-avatars.com/api/?name=${distributor.name}&background=random&size=128`;\n                distributor.level = levelInfo ? levelInfo.level_name : '普通分销商';\n                distributor.sales = parseFloat(distributor.total_sales || 0).toFixed(2);\n                distributor.commission = parseFloat(distributor.total_commission || 0).toFixed(2);\n            }\n\n            return this.success(distributors);\n        } catch (error) {\n            console.error('获取顶级分销员失败:', error);\n            return this.fail('获取顶级分销员失败');\n        }\n    }\n\n    /**\n     * 获取图表数据\n     * @return {Promise} []\n     */\n    async chartAction() {\n        try {\n            const period = this.get('period') || '30';\n            const type = this.get('type') || 'sales';\n\n            // 生成模拟图表数据\n            const data = [];\n            const labels = [];\n            const now = new Date();\n\n            for (let i = parseInt(period) - 1; i >= 0; i--) {\n                const date = new Date(now);\n                date.setDate(date.getDate() - i);\n                labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));\n\n                if (type === 'sales') {\n                    data.push(Math.floor(Math.random() * 50000) + 10000);\n                } else if (type === 'orders') {\n                    data.push(Math.floor(Math.random() * 100) + 20);\n                } else if (type === 'commission') {\n                    data.push(Math.floor(Math.random() * 5000) + 1000);\n                }\n            }\n\n            return this.success({\n                labels: labels,\n                data: data\n            });\n        } catch (error) {\n            console.error('获取图表数据失败:', error);\n            return this.fail('获取图表数据失败');\n        }\n    }\n\n    /**\n     * 获取实时数据\n     * @return {Promise} []\n     */\n    async realtimeAction() {\n        try {\n            const today = new Date();\n            const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());\n            const todayStartTimestamp = parseInt(todayStart.getTime() / 1000);\n            const todayEndTimestamp = parseInt(new Date().getTime() / 1000);\n\n            // 今日订单数\n            const todayOrders = await this.model('order').where({\n                add_time: ['BETWEEN', todayStartTimestamp, todayEndTimestamp]\n            }).count();\n\n            // 今日收入\n            const todayRevenueResult = await this.model('order').where({\n                add_time: ['BETWEEN', todayStartTimestamp, todayEndTimestamp],\n                order_status: ['>=', 300]\n            }).sum('actual_price');\n            const todayRevenue = parseFloat(todayRevenueResult || 0);\n\n            // 在线用户数（模拟）\n            const activeUsers = Math.floor(Math.random() * 50) + 20;\n\n            const realTimeData = {\n                todayOrders: todayOrders,\n                todayRevenue: todayRevenue,\n                activeUsers: activeUsers\n            };\n\n            return this.success(realTimeData);\n        } catch (error) {\n            console.error('获取实时数据失败:', error);\n            return this.fail('获取实时数据失败');\n        }\n    }\n\n    /**\n     * 获取分销池概览（所有商品的分销状态概览）\n     * @return {Promise} []\n     */\n    async poolOverviewAction() {\n        const page = this.get('page') || 1;\n        const size = this.get('size') || 50;\n\n        // 获取所有商品及其分销状态\n        const goodsData = await this.model('goods')\n            .alias('g')\n            .join({\n                table: 'goods_distribution',\n                join: 'left',\n                as: 'gd',\n                on: ['g.id', 'gd.goods_id']\n            })\n            .join({\n                table: 'category',\n                join: 'left',\n                as: 'c',\n                on: ['g.category_id', 'c.id']\n            })\n            .where({\n                'g.is_delete': 0\n            })\n            .field('g.id,g.name,g.retail_price,g.sell_volume,g.is_on_sale,g.list_pic_url,c.name as category_name,gd.is_distributed,gd.commission_rate,gd.personal_rate,gd.level1_rate,gd.level2_rate,gd.add_time as pool_join_time')\n            .order(['g.sort_order asc', 'g.id desc'])\n            .page(page, size)\n            .countSelect();\n\n        // 处理数据，标记商品在分销池中的状态\n        for (const item of goodsData.data) {\n            if (item.is_distributed === null) {\n                item.pool_status = 'not_in_pool';      // 不在分销池中\n                item.pool_status_text = '未加入';\n            } else if (item.is_distributed === 1) {\n                item.pool_status = 'active';           // 在分销池中且激活\n                item.pool_status_text = '已激活';\n            } else {\n                item.pool_status = 'inactive';         // 在分销池中但未激活\n                item.pool_status_text = '已配置未激活';\n            }\n\n            // 计算预计佣金\n            item.estimated_commission = item.commission_rate ?\n                (parseFloat(item.retail_price) * item.commission_rate / 100).toFixed(2) : '0.00';\n\n            // 格式化加入分销池时间\n            item.pool_join_time = item.pool_join_time ?\n                moment.unix(item.pool_join_time).format('YYYY-MM-DD HH:mm:ss') : '';\n        }\n\n        return this.success(goodsData);\n    }\n\n    /**\n     * 记录分销操作日志\n     * @param {number} goodsId 商品ID\n     * @param {string} operation 操作类型\n     * @param {object} data 操作数据\n     */\n    async logDistributionOperation(goodsId, operation, data) {\n        try {\n            const logData = {\n                goods_id: goodsId,\n                operation: operation,\n                operation_data: JSON.stringify(data),\n                operator_id: this.ctx.state.userInfo ? this.ctx.state.userInfo.id : 0,\n                create_time: parseInt(new Date().getTime() / 1000)\n            };\n\n            // 这里可以记录到操作日志表\n            console.log('分销操作日志:', logData);\n        } catch (error) {\n            console.error('记录分销操作日志失败:', error);\n        }\n    }\n\n    /**\n     * 记录批量操作日志\n     * @param {array} goodsIds 商品ID数组\n     * @param {string} operation 操作类型\n     * @param {object} summary 操作摘要\n     */\n    async logBatchOperation(goodsIds, operation, summary) {\n        try {\n            const logData = {\n                goods_ids: goodsIds.join(','),\n                operation: operation,\n                operation_summary: JSON.stringify(summary),\n                operator_id: this.ctx.state.userInfo ? this.ctx.state.userInfo.id : 0,\n                create_time: parseInt(new Date().getTime() / 1000)\n            };\n\n            console.log('批量操作日志:', logData);\n        } catch (error) {\n            console.error('记录批量操作日志失败:', error);\n        }\n    }\n\n    /**\n     * 智能推荐分销配置\n     * @return {Promise} []\n     */\n    async recommendConfigAction() {\n        const goodsId = this.get('goods_id');\n\n        if (!goodsId) {\n            return this.fail('商品ID不能为空');\n        }\n\n        // 获取商品信息\n        const goods = await this.model('goods').where({\n            id: goodsId,\n            is_delete: 0\n        }).find();\n\n        if (!goods) {\n            return this.fail('商品不存在');\n        }\n\n        const price = parseFloat(goods.retail_price);\n        const sellVolume = parseInt(goods.sell_volume || 0);\n\n        // 根据商品价格和销量智能推荐配置\n        let recommendedConfig = {};\n\n        if (price <= 100) {\n            recommendedConfig = {\n                personal_rate: 8.0,\n                level1_rate: 3.0,\n                level2_rate: 1.0,\n                team_leader_rate: 2.0,\n                min_level_required: 1,\n                reason: '低价商品，适合新手分销员推广'\n            };\n        } else if (price <= 500) {\n            recommendedConfig = {\n                personal_rate: 10.0,\n                level1_rate: 4.0,\n                level2_rate: 2.0,\n                team_leader_rate: 3.0,\n                min_level_required: sellVolume > 50 ? 1 : 2,\n                reason: '中档商品，平衡佣金与门槛'\n            };\n        } else {\n            recommendedConfig = {\n                personal_rate: 15.0,\n                level1_rate: 6.0,\n                level2_rate: 3.0,\n                team_leader_rate: 5.0,\n                min_level_required: 3,\n                reason: '高价商品，需要有经验的分销员'\n            };\n        }\n\n        // 如果是热门商品，可以适当降低佣金\n        if (sellVolume > 100) {\n            recommendedConfig.personal_rate *= 0.9;\n            recommendedConfig.level1_rate *= 0.9;\n            recommendedConfig.level2_rate *= 0.9;\n            recommendedConfig.team_leader_rate *= 0.9;\n            recommendedConfig.reason += '，热门商品可适当降低佣金';\n        }\n\n        // 四舍五入到一位小数\n        Object.keys(recommendedConfig).forEach(key => {\n            if (typeof recommendedConfig[key] === 'number' && key.includes('rate')) {\n                recommendedConfig[key] = Math.round(recommendedConfig[key] * 10) / 10;\n            }\n        });\n\n        return this.success({\n            goods_info: {\n                id: goods.id,\n                name: goods.name,\n                price: goods.retail_price,\n                sell_volume: goods.sell_volume\n            },\n            recommended_config: recommendedConfig\n        });\n    }\n};\n"]}