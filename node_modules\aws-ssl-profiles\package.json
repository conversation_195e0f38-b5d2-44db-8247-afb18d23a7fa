{"_from": "aws-ssl-profiles@^1.1.1", "_id": "aws-ssl-profiles@1.1.2", "_inBundle": false, "_integrity": "sha512-NZKeq9AfyQvEeNlN0zSYAaWrmBffJh3IELMZfRpJVWgrpEbtEpnjvzqBPf+mxoI287JohRDoa+/nsfqqiZmF6g==", "_location": "/aws-ssl-profiles", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "aws-ssl-profiles@^1.1.1", "name": "aws-ssl-profiles", "escapedName": "aws-ssl-profiles", "rawSpec": "^1.1.1", "saveSpec": null, "fetchSpec": "^1.1.1"}, "_requiredBy": ["/mysql2"], "_resolved": "https://registry.npmjs.org/aws-ssl-profiles/-/aws-ssl-profiles-1.1.2.tgz", "_shasum": "157dd77e9f19b1d123678e93f120e6f193022641", "_spec": "aws-ssl-profiles@^1.1.1", "_where": "D:\\py-ide\\hioshop-miniprogram-master\\node_modules\\mysql2", "author": {"name": "https://github.com/wellwelwel"}, "bugs": {"url": "https://github.com/mysqljs/aws-ssl-profiles/issues"}, "bundleDependencies": false, "deprecated": false, "description": "AWS RDS SSL certificates bundles.", "devDependencies": {"@biomejs/biome": "^1.8.3", "@types/node": "^22.5.1", "@types/x509.js": "^1.0.3", "poku": "^2.5.0", "prettier": "^3.3.3", "tsx": "^4.19.0", "typescript": "^5.5.4", "x509.js": "^1.0.0"}, "engines": {"node": ">= 6.0.0"}, "files": ["lib"], "homepage": "https://github.com/mysqljs/aws-ssl-profiles#readme", "keywords": ["mysql", "mysql2", "pg", "postgres", "aws", "rds", "ssl", "certificates", "ca", "bundle"], "license": "MIT", "main": "lib/index.js", "name": "aws-ssl-profiles", "repository": {"type": "git", "url": "git+https://github.com/mysqljs/aws-ssl-profiles.git"}, "scripts": {"build": "npx tsc", "lint": "npx @biomejs/biome lint && prettier --check .", "lint:fix": "npx @biomejs/biome lint --write . && prettier --write .", "postbuild": "cp src/index.d.ts lib/index.d.ts", "pretest": "npm run build", "test": "poku --parallel ./test", "test:ci": "npm run lint && npm run test"}, "version": "1.1.2"}