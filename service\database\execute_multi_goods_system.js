const mysql = require('mysql2/promise');
const fs = require('fs');

async function executeMultiGoodsSystem() {
  try {
    const connection = await mysql.createConnection({
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: '19841020',
      database: 'hiolabsDB'
    });

    console.log('✅ 数据库连接成功');

    const sqlContent = fs.readFileSync('./database/flash_sale_multi_goods_system.sql', 'utf8');
    
    // 分割SQL语句
    const statements = sqlContent.split(';').filter(stmt => stmt.trim().length > 0);
    
    console.log(`📝 开始执行 ${statements.length} 条SQL语句...`);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement && !statement.startsWith('--') && !statement.startsWith('SELECT')) {
        try {
          console.log(`执行第 ${i + 1} 条语句...`);
          await connection.execute(statement);
          console.log(`✅ 第 ${i + 1} 条语句执行成功`);
        } catch (error) {
          console.error(`❌ 第 ${i + 1} 条语句执行失败:`, error.message);
          console.error('语句内容:', statement.substring(0, 100) + '...');
        }
      }
    }
    
    console.log('✅ SQL脚本执行完成');

    await connection.end();
    console.log('✅ 数据库连接已关闭');
    
  } catch (error) {
    console.error('❌ 执行失败:', error.message);
    process.exit(1);
  }
}

executeMultiGoodsSystem();
