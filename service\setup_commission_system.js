const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'root',
  database: 'hiolabs'
};

async function setupCommissionSystem() {
  let connection;
  
  try {
    console.log('🚀 开始设置佣金系统...');
    
    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 读取SQL文件
    const sqlFile = path.join(__dirname, 'create_commission_tables.sql');
    const sqlContent = fs.readFileSync(sqlFile, 'utf8');
    
    // 分割SQL语句
    const sqlStatements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);
    
    console.log(`📝 找到 ${sqlStatements.length} 条SQL语句`);
    
    // 执行每条SQL语句
    for (let i = 0; i < sqlStatements.length; i++) {
      const sql = sqlStatements[i];
      console.log(`⏳ 执行第 ${i + 1} 条SQL...`);
      
      try {
        await connection.execute(sql);
        console.log(`✅ 第 ${i + 1} 条SQL执行成功`);
      } catch (error) {
        if (error.code === 'ER_DUP_FIELDNAME') {
          console.log(`⚠️  第 ${i + 1} 条SQL跳过（字段已存在）`);
        } else if (error.code === 'ER_TABLE_EXISTS_ERROR') {
          console.log(`⚠️  第 ${i + 1} 条SQL跳过（表已存在）`);
        } else {
          console.error(`❌ 第 ${i + 1} 条SQL执行失败:`, error.message);
        }
      }
    }
    
    console.log('🎉 佣金系统设置完成！');
    console.log('');
    console.log('📋 已创建/更新的表：');
    console.log('  - hiolabs_user_commission (用户佣金账户表)');
    console.log('  - hiolabs_commission_log (佣金变动日志表)');
    console.log('  - hiolabs_promotion_orders (推广订单表 - 添加佣金字段)');
    console.log('  - hiolabs_personal_promoters (个人推广员表 - 添加佣金统计)');
    console.log('  - hiolabs_goods_distribution (商品分销配置表 - 添加佣金比例字段)');
    
  } catch (error) {
    console.error('❌ 设置佣金系统失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行设置
setupCommissionSystem();
