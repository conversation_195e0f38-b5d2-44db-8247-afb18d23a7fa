/**
 * 多商品秒杀轮次状态调度服务
 * 负责自动更新轮次状态：upcoming -> active -> ended
 */

module.exports = class FlashSaleMultiScheduler {
  
  /**
   * 更新所有轮次状态
   * @param {Object} model 数据模型
   */
  async updateRoundStatus(model) {
    try {
      const roundModel = model('flash_sale_rounds');
      const now = new Date();
      const nowStr = now.toISOString().slice(0, 19).replace('T', ' ');
      
      console.log(`[${nowStr}] 开始检查轮次状态...`);
      
      // 1. 将到达开始时间的upcoming轮次设为active
      const upcomingToActive = await roundModel.where({
        start_time: ['<=', nowStr],
        status: 'upcoming'
      }).select();
      
      if (upcomingToActive.length > 0) {
        await roundModel.where({
          start_time: ['<=', nowStr],
          status: 'upcoming'
        }).update({ status: 'active' });
        
        console.log(`✅ 启动了 ${upcomingToActive.length} 个轮次:`, 
          upcomingToActive.map(r => `轮次${r.round_number}(${r.round_name})`).join(', '));
      }
      
      // 2. 将超过结束时间的active轮次设为ended
      const activeToEnded = await roundModel.where({
        end_time: ['<', nowStr],
        status: 'active'
      }).select();
      
      if (activeToEnded.length > 0) {
        await roundModel.where({
          end_time: ['<', nowStr],
          status: 'active'
        }).update({ status: 'ended' });
        
        console.log(`⏰ 结束了 ${activeToEnded.length} 个轮次:`, 
          activeToEnded.map(r => `轮次${r.round_number}(${r.round_name})`).join(', '));
      }
      
      // 3. 将超过结束时间的upcoming轮次也设为ended（防止遗漏）
      const upcomingToEnded = await roundModel.where({
        end_time: ['<', nowStr],
        status: 'upcoming'
      }).select();
      
      if (upcomingToEnded.length > 0) {
        await roundModel.where({
          end_time: ['<', nowStr],
          status: 'upcoming'
        }).update({ status: 'ended' });
        
        console.log(`⚠️ 直接结束了 ${upcomingToEnded.length} 个未启动的过期轮次:`, 
          upcomingToEnded.map(r => `轮次${r.round_number}(${r.round_name})`).join(', '));
      }
      
      const totalUpdated = upcomingToActive.length + activeToEnded.length + upcomingToEnded.length;
      if (totalUpdated === 0) {
        console.log('📊 所有轮次状态正常，无需更新');
      }
      
      return {
        activated: upcomingToActive.length,
        ended: activeToEnded.length + upcomingToEnded.length,
        total: totalUpdated
      };
      
    } catch (error) {
      console.error('❌ 更新轮次状态失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取当前活跃轮次统计
   * @param {Object} model 数据模型
   */
  async getActiveRoundsStats(model) {
    try {
      const roundModel = model('flash_sale_rounds');
      
      const stats = await roundModel.field('status, COUNT(*) as count')
        .group('status')
        .select();
      
      const result = {
        upcoming: 0,
        active: 0,
        ended: 0,
        cancelled: 0
      };
      
      stats.forEach(stat => {
        result[stat.status] = stat.count;
      });
      
      return result;
      
    } catch (error) {
      console.error('获取轮次统计失败:', error);
      throw error;
    }
  }
  
  /**
   * 清理过期数据
   * @param {Object} model 数据模型
   * @param {number} daysToKeep 保留天数
   */
  async cleanupExpiredData(model, daysToKeep = 30) {
    try {
      const roundModel = model('flash_sale_rounds');
      const roundGoodsModel = model('flash_sale_round_goods');
      
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      const cutoffStr = cutoffDate.toISOString().slice(0, 19).replace('T', ' ');
      
      // 获取要删除的轮次
      const expiredRounds = await roundModel.where({
        end_time: ['<', cutoffStr],
        status: 'ended'
      }).field('id, round_name').select();
      
      if (expiredRounds.length > 0) {
        const roundIds = expiredRounds.map(r => r.id);
        
        // 删除轮次商品
        await roundGoodsModel.where({
          round_id: ['IN', roundIds]
        }).delete();
        
        // 删除轮次
        await roundModel.where({
          id: ['IN', roundIds]
        }).delete();
        
        console.log(`🗑️ 清理了 ${expiredRounds.length} 个过期轮次 (${daysToKeep}天前):`, 
          expiredRounds.map(r => r.round_name).join(', '));
      }
      
      return expiredRounds.length;
      
    } catch (error) {
      console.error('清理过期数据失败:', error);
      throw error;
    }
  }
};
