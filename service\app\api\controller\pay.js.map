{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\pay.js"], "names": ["Base", "require", "moment", "generate", "Jushuitan", "WangDianSync", "module", "exports", "preWeixinPayaAction", "orderId", "get", "orderInfo", "model", "where", "id", "find", "userId", "user_id", "result", "transaction_id", "time_end", "parseInt", "Date", "getTime", "orderModel", "updatePayData", "afterPay", "success", "preWeixinPayAction", "orderGoods", "order_id", "is_delete", "select", "checkPrice", "checkStock", "item", "product", "product_id", "number", "goods_number", "retail_price", "fail", "think", "isEmpty", "pay_status", "openid", "getField", "WeixinSerivce", "service", "returnParams", "createUnifiedOrder", "body", "order_sn", "out_trade_no", "total_fee", "actual_price", "spbill_create_ip", "err", "notifyAction", "console", "log", "ip", "toISOString", "data", "post", "payNotify", "echo", "json", "getOrderByOrderSn", "bool", "checkPayStatus", "order_type", "orderGoodsList", "cartItem", "goods_id", "specification", "goods_specifition_name_value", "decrement", "increment", "updatedOrderInfo", "order_status", "wangdianSync", "pushOrder", "error", "message", "refundNotifyAction", "info", "return_code", "ctx", "type", "return_msg"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;AACA,MAAME,WAAWF,QAAQ,iBAAR,CAAjB;AACA,MAAMG,YAAYH,QAAQ,WAAR,CAAlB;AACA,MAAMI,eAAeJ,QAAQ,+BAAR,CAArB;AACAK,OAAOC,OAAP,GAAiB,cAAcP,IAAd,CAAmB;AAChC;;;;AAIA;AACMQ,uBAAN,GAA4B;AAAA;;AAAA;AACxB,kBAAMC,UAAU,MAAKC,GAAL,CAAS,SAAT,CAAhB;AACA,kBAAMC,YAAY,MAAM,MAAKC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC9CC,oBAAIL;AAD0C,aAA1B,EAErBM,IAFqB,EAAxB;AAGA,gBAAIC,SAASL,UAAUM,OAAvB;AACA,gBAAIC,SAAS;AACZC,gCAAgB,YADJ;AAEZC,0BAAUC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC;AAFE,aAAb;AAIA,kBAAMC,aAAa,MAAKZ,KAAL,CAAW,OAAX,CAAnB;AACA,kBAAMY,WAAWC,aAAX,CAAyBd,UAAUG,EAAnC,EAAuCI,MAAvC,CAAN;AACA,kBAAM,MAAKQ,QAAL,CAAcf,SAAd,CAAN;AACN,mBAAO,MAAKgB,OAAL,EAAP;AAb8B;AAc3B;AACD;AACMC,sBAAN,GAA2B;AAAA;;AAAA;AACvB,kBAAMnB,UAAU,OAAKC,GAAL,CAAS,SAAT,CAAhB;AACA,kBAAMC,YAAY,MAAM,OAAKC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC9CC,oBAAIL;AAD0C,aAA1B,EAErBM,IAFqB,EAAxB;AAGA;AACA,gBAAIc,aAAa,MAAM,OAAKjB,KAAL,CAAW,aAAX,EAA0BC,KAA1B,CAAgC;AACnDiB,0BAASrB,OAD0C;AAEnDsB,2BAAU;AAFyC,aAAhC,EAGpBC,MAHoB,EAAvB;AAIA,gBAAIC,aAAa,CAAjB;AACA,gBAAIC,aAAa,CAAjB;AACA,iBAAI,MAAMC,IAAV,IAAkBN,UAAlB,EAA6B;AACzB,oBAAIO,UAAU,MAAM,OAAKxB,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC5CC,wBAAGqB,KAAKE;AADoC,iBAA5B,EAEjBtB,IAFiB,EAApB;AAGA,oBAAGoB,KAAKG,MAAL,GAAcF,QAAQG,YAAzB,EAAsC;AAClCL;AACH;AACD,oBAAGC,KAAKK,YAAL,IAAqBJ,QAAQI,YAAhC,EAA6C;AACzCP;AACH;AACJ;AACD,gBAAGC,aAAa,CAAhB,EAAkB;AACd,uBAAO,OAAKO,IAAL,CAAU,GAAV,EAAe,YAAf,CAAP;AACH;AACD,gBAAGR,aAAa,CAAhB,EAAkB;AACd,uBAAO,OAAKQ,IAAL,CAAU,GAAV,EAAe,cAAf,CAAP;AACH;AACD,gBAAIC,MAAMC,OAAN,CAAchC,SAAd,CAAJ,EAA8B;AAC1B,uBAAO,OAAK8B,IAAL,CAAU,GAAV,EAAe,OAAf,CAAP;AACH;AACD,gBAAIpB,SAASV,UAAUiC,UAAnB,MAAmC,CAAvC,EAA0C;AACtC,uBAAO,OAAKH,IAAL,CAAU,GAAV,EAAe,eAAf,CAAP;AACH;AACD,kBAAMI,SAAS,MAAM,OAAKjC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC1CC,oBAAIH,UAAUM;AAD4B,aAAzB,EAElB6B,QAFkB,CAET,eAFS,EAEQ,IAFR,CAArB;AAGA,gBAAIJ,MAAMC,OAAN,CAAcE,MAAd,CAAJ,EAA2B;AACvB,uBAAO,OAAKJ,IAAL,CAAU,GAAV,EAAe,iBAAf,CAAP;AACH;AACD,kBAAMM,gBAAgB,OAAKC,OAAL,CAAa,QAAb,EAAuB,KAAvB,CAAtB;AACA,gBAAI;AACA,sBAAMC,eAAe,MAAMF,cAAcG,kBAAd,CAAiC;AACxDL,4BAAQA,MADgD;AAExDM,0BAAM,YAAYxC,UAAUyC,QAF4B;AAGxDC,kCAAc1C,UAAUyC,QAHgC;AAIxDE,+BAAWjC,SAASV,UAAU4C,YAAV,GAAyB,GAAlC,CAJ6C;AAKxDC,sCAAkB;AALsC,iBAAjC,CAA3B;AAOA,uBAAO,OAAK7B,OAAL,CAAasB,YAAb,CAAP;AACH,aATD,CASE,OAAOQ,GAAP,EAAY;AACV,uBAAO,OAAKhB,IAAL,CAAU,GAAV,EAAe,SAAf,CAAP;AACH;AArDsB;AAsD1B;AACKiB,gBAAN,GAAqB;AAAA;;AAAA;AACjBC,oBAAQC,GAAR,CAAY,oBAAZ;AACAD,oBAAQC,GAAR,CAAY,OAAZ,EAAqB,OAAKC,EAA1B;AACAF,oBAAQC,GAAR,CAAY,OAAZ,EAAqB,IAAItC,IAAJ,GAAWwC,WAAX,EAArB;;AAEA,kBAAMf,gBAAgB,OAAKC,OAAL,CAAa,QAAb,EAAuB,KAAvB,CAAtB;AACA,kBAAMe,OAAO,OAAKC,IAAL,CAAU,KAAV,CAAb;AACAL,oBAAQC,GAAR,CAAY,WAAZ,EAAyBG,IAAzB;;AAEA,kBAAM7C,SAAS,MAAM6B,cAAckB,SAAd,CAAwB,OAAKD,IAAL,CAAU,KAAV,CAAxB,CAArB;AACAL,oBAAQC,GAAR,CAAY,SAAZ,EAAuB1C,MAAvB;;AAEA,gBAAI,CAACA,MAAL,EAAa;AACTyC,wBAAQC,GAAR,CAAY,eAAZ;AACA,oBAAIM,OAAO,MAAX;AACA,uBAAO,OAAKC,IAAL,CAAUD,IAAV,CAAP;AACH;AACD,kBAAM1C,aAAa,OAAKZ,KAAL,CAAW,OAAX,CAAnB;AACA,kBAAMD,YAAY,MAAMa,WAAW4C,iBAAX,CAA6BlD,OAAOmC,YAApC,CAAxB;AACAM,oBAAQC,GAAR,CAAY,WAAZ,EAAyBjD,SAAzB;;AAEA,gBAAI+B,MAAMC,OAAN,CAAchC,SAAd,CAAJ,EAA8B;AAC1BgD,wBAAQC,GAAR,CAAY,YAAZ,EAA0B1C,OAAOmC,YAAjC;AACA,oBAAIa,OAAO,MAAX;AACA,uBAAO,OAAKC,IAAL,CAAUD,IAAV,CAAP;AACH;;AAED,gBAAIG,OAAO,MAAM7C,WAAW8C,cAAX,CAA0B3D,UAAUG,EAApC,CAAjB;AACA6C,oBAAQC,GAAR,CAAY,aAAZ,EAA2BS,IAA3B,EAAiC,OAAjC,EAA0C1D,UAAUG,EAApD;;AAEA,gBAAIuD,QAAQ,IAAZ,EAAkB;AACd,oBAAI1D,UAAU4D,UAAV,IAAwB,CAA5B,EAA+B;AAAE;AAC7BZ,4BAAQC,GAAR,CAAY,eAAZ;AACA,0BAAMpC,WAAWC,aAAX,CAAyBd,UAAUG,EAAnC,EAAuCI,MAAvC,CAAN;AACAyC,4BAAQC,GAAR,CAAY,YAAZ;;AAEAD,4BAAQC,GAAR,CAAY,mBAAZ;AACA,0BAAM,OAAKlC,QAAL,CAAcf,SAAd,CAAN;AACAgD,4BAAQC,GAAR,CAAY,cAAZ;AACH;AACJ,aAVD,MAUO;AACHD,wBAAQC,GAAR,CAAY,aAAZ;AACA,uBAAO,kGAAP;AACH;;AAEDD,oBAAQC,GAAR,CAAY,sBAAZ;AACA,gBAAIM,OAAO,SAAX;AACA,mBAAO,OAAKC,IAAL,CAAUD,IAAV,CAAP;AA/CiB;AAgDpB;AACKxC,YAAN,CAAef,SAAf,EAA0B;AAAA;;AAAA;AACtB,gBAAIA,UAAU4D,UAAV,IAAwB,CAA5B,EAA+B;AAC3B,oBAAIC,iBAAiB,MAAM,OAAK5D,KAAL,CAAW,aAAX,EAA0BC,KAA1B,CAAgC;AACvDiB,8BAAUnB,UAAUG;AADmC,iBAAhC,EAExBkB,MAFwB,EAA3B;;AAIA;AACA,qBAAK,MAAMyC,QAAX,IAAuBD,cAAvB,EAAuC;AACnC,wBAAIE,WAAWD,SAASC,QAAxB;AACA,wBAAIrC,aAAaoC,SAASpC,UAA1B;AACA,wBAAIC,SAASmC,SAASnC,MAAtB;AACA,wBAAIqC,gBAAgBF,SAASG,4BAA7B;AACA,0BAAM,OAAKhE,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5BC,4BAAI4D;AADwB,qBAA1B,EAEHG,SAFG,CAEO,cAFP,EAEuBvC,MAFvB,CAAN;AAGA,0BAAM,OAAK1B,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5BC,4BAAI4D;AADwB,qBAA1B,EAEHI,SAFG,CAEO,aAFP,EAEsBxC,MAFtB,CAAN;AAGA,0BAAM,OAAK1B,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC9BC,4BAAIuB;AAD0B,qBAA5B,EAEHwC,SAFG,CAEO,cAFP,EAEuBvC,MAFvB,CAAN;AAGH;;AAED;AACA,sBAAMyC,mBAAmB,MAAM,OAAKnE,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AACrDC,wBAAIH,UAAUG;AADuC,iBAA1B,EAE5BC,IAF4B,EAA/B;;AAIA;AACA,oBAAIgE,iBAAiBC,YAAjB,KAAkC,GAAtC,EAA2C;AACvC,wBAAI;AACArB,gCAAQC,GAAR,CAAa,wCAAuCmB,iBAAiB3B,QAAS,EAA9E;AACA,8BAAM6B,eAAe,IAAI5E,YAAJ,QAArB;AACA,8BAAMa,SAAS,MAAM+D,aAAaC,SAAb,CAAuBH,gBAAvB,EAAyCP,cAAzC,CAArB;;AAEA,4BAAItD,OAAOS,OAAX,EAAoB;AAChBgC,oCAAQC,GAAR,CAAa,cAAamB,iBAAiB3B,QAAS,OAApD;AACH,yBAFD,MAEO;AACHO,oCAAQwB,KAAR,CAAe,cAAaJ,iBAAiB3B,QAAS,UAASlC,OAAOkE,OAAQ,EAA9E;AACH;AACJ,qBAVD,CAUE,OAAOD,KAAP,EAAc;AACZxB,gCAAQwB,KAAR,CAAc,oBAAd,EAAoCA,KAApC;AACA;AACH;AACJ,iBAfD,MAeO;AACHxB,4BAAQC,GAAR,CAAa,gCAA+BmB,iBAAiBC,YAAa,EAA1E;AACH;;AAED;AACH;AAjDqB;AAkDzB;;AAED;;;AAGMK,sBAAN,GAA2B;AAAA;;AAAA;AACvB1B,oBAAQC,GAAR,CAAY,oBAAZ;AACAD,oBAAQC,GAAR,CAAY,OAAZ,EAAqB,OAAKC,EAA1B;AACAF,oBAAQC,GAAR,CAAY,OAAZ,EAAqB,IAAItC,IAAJ,GAAWwC,WAAX,EAArB;;AAEA,gBAAIwB,OAAO,OAAKtB,IAAL,EAAX;AACAL,oBAAQC,GAAR,CAAY,aAAZ,EAA2B0B,IAA3B;;AAEA,gBAAI;AACA;AACA;;AAEA,oBAAIA,KAAKC,WAAL,KAAqB,SAAzB,EAAoC;AAChC5B,4BAAQC,GAAR,CAAY,cAAZ;;AAEA;AACA;;AAEA;AACA,2BAAK4B,GAAL,CAASC,IAAT,GAAgB,UAAhB;AACA,2BAAO,kGAAP;AACH,iBATD,MASO;AACH9B,4BAAQwB,KAAR,CAAc,aAAd,EAA6BG,KAAKI,UAAlC;AACA,2BAAKF,GAAL,CAASC,IAAT,GAAgB,UAAhB;AACA,2BAAO,iGAAP;AACH;AAEJ,aAnBD,CAmBE,OAAON,KAAP,EAAc;AACZxB,wBAAQwB,KAAR,CAAc,eAAd,EAA+BA,KAA/B;AACA,uBAAKK,GAAL,CAASC,IAAT,GAAgB,UAAhB;AACA,uBAAO,iGAAP;AACH;AA/BsB;AAgC1B;AArN+B,CAApC", "file": "..\\..\\..\\src\\api\\controller\\pay.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\nconst generate = require('nanoid/generate');\nconst Jushuitan = require('jushuitan');\nconst WangDianSync = require('../../common/wangdian_sync.js');\nmodule.exports = class extends Base {\n    /**\n     * 获取支付的请求参数\n     * @returns {Promise<PreventPromise|void|Promise>}\n     */\n    // 测试时付款，将真实接口注释。 在小程序的services/pay.js中按照提示注释和打开\n    async preWeixinPayaAction() {\n        const orderId = this.get('orderId');\n        const orderInfo = await this.model('order').where({\n            id: orderId\n        }).find();\n        let userId = orderInfo.user_id;\n        let result = {\n        \ttransaction_id: 123123123123,\n        \ttime_end: parseInt(new Date().getTime() / 1000),\n        }\n        const orderModel = this.model('order');\n        await orderModel.updatePayData(orderInfo.id, result);\n        await this.afterPay(orderInfo);\n\t\treturn this.success();\n    }\n    // 真实的付款接口\n    async preWeixinPayAction() {\n        const orderId = this.get('orderId');\n        const orderInfo = await this.model('order').where({\n            id: orderId\n        }).find();\n        // 再次确认库存和价格\n        let orderGoods = await this.model('order_goods').where({\n            order_id:orderId,\n            is_delete:0\n        }).select();\n        let checkPrice = 0;\n        let checkStock = 0;\n        for(const item of orderGoods){\n            let product = await this.model('product').where({\n                id:item.product_id\n            }).find();\n            if(item.number > product.goods_number){\n                checkStock++;\n            }\n            if(item.retail_price != product.retail_price){\n                checkPrice++;\n            }\n        }\n        if(checkStock > 0){\n            return this.fail(400, '库存不足，请重新下单');\n        }\n        if(checkPrice > 0){\n            return this.fail(400, '价格发生变化，请重新下单');\n        }\n        if (think.isEmpty(orderInfo)) {\n            return this.fail(400, '订单已取消');\n        }\n        if (parseInt(orderInfo.pay_status) !== 0) {\n            return this.fail(400, '订单已支付，请不要重复操作');\n        }\n        const openid = await this.model('user').where({\n            id: orderInfo.user_id\n        }).getField('weixin_openid', true);\n        if (think.isEmpty(openid)) {\n            return this.fail(400, '微信支付失败，没有openid');\n        }\n        const WeixinSerivce = this.service('weixin', 'api');\n        try {\n            const returnParams = await WeixinSerivce.createUnifiedOrder({\n                openid: openid,\n                body: '[海风小店]：' + orderInfo.order_sn,\n                out_trade_no: orderInfo.order_sn,\n                total_fee: parseInt(orderInfo.actual_price * 100),\n                spbill_create_ip: ''\n            });\n            return this.success(returnParams);\n        } catch (err) {\n            return this.fail(400, '微信支付失败?');\n        }\n    }\n    async notifyAction() {\n        console.log('=== 微信支付异步通知开始 ===');\n        console.log('请求IP:', this.ip);\n        console.log('请求时间:', new Date().toISOString());\n\n        const WeixinSerivce = this.service('weixin', 'api');\n        const data = this.post('xml');\n        console.log('接收到的通知数据:', data);\n\n        const result = await WeixinSerivce.payNotify(this.post('xml'));\n        console.log('签名验证结果:', result);\n\n        if (!result) {\n            console.log('签名验证失败，返回FAIL');\n            let echo = 'FAIL';\n            return this.json(echo);\n        }\n        const orderModel = this.model('order');\n        const orderInfo = await orderModel.getOrderByOrderSn(result.out_trade_no);\n        console.log('查询到的订单信息:', orderInfo);\n\n        if (think.isEmpty(orderInfo)) {\n            console.log('订单不存在，订单号:', result.out_trade_no);\n            let echo = 'FAIL';\n            return this.json(echo);\n        }\n\n        let bool = await orderModel.checkPayStatus(orderInfo.id);\n        console.log('订单支付状态检查结果:', bool, '订单ID:', orderInfo.id);\n\n        if (bool == true) {\n            if (orderInfo.order_type == 0) { //普通订单和秒杀订单\n                console.log('开始更新订单支付状态...');\n                await orderModel.updatePayData(orderInfo.id, result);\n                console.log('订单支付状态更新完成');\n\n                console.log('开始执行afterPay处理...');\n                await this.afterPay(orderInfo);\n                console.log('afterPay处理完成');\n            }\n        } else {\n            console.log('订单已支付，不重复处理');\n            return '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[订单已支付]]></return_msg></xml>';\n        }\n\n        console.log('=== 微信支付异步通知处理完成 ===');\n        let echo = 'SUCCESS'\n        return this.json(echo);\n    }\n    async afterPay(orderInfo) {\n        if (orderInfo.order_type == 0) {\n            let orderGoodsList = await this.model('order_goods').where({\n                order_id: orderInfo.id\n            }).select();\n\n            // 更新库存和销量\n            for (const cartItem of orderGoodsList) {\n                let goods_id = cartItem.goods_id;\n                let product_id = cartItem.product_id;\n                let number = cartItem.number;\n                let specification = cartItem.goods_specifition_name_value;\n                await this.model('goods').where({\n                    id: goods_id\n                }).decrement('goods_number', number);\n                await this.model('goods').where({\n                    id: goods_id\n                }).increment('sell_volume', number);\n                await this.model('product').where({\n                    id: product_id\n                }).decrement('goods_number', number);\n            }\n\n            // 获取更新后的订单信息（确保order_status是201）\n            const updatedOrderInfo = await this.model('order').where({\n                id: orderInfo.id\n            }).find();\n\n            // 检查订单状态是否为201（已付款），如果是则推送到旺店通ERP\n            if (updatedOrderInfo.order_status === 201) {\n                try {\n                    console.log(`[旺店通同步] 订单状态为201（已付款），开始推送订单到旺店通ERP: ${updatedOrderInfo.order_sn}`);\n                    const wangdianSync = new WangDianSync(this);\n                    const result = await wangdianSync.pushOrder(updatedOrderInfo, orderGoodsList);\n\n                    if (result.success) {\n                        console.log(`[旺店通同步] 订单 ${updatedOrderInfo.order_sn} 推送成功`);\n                    } else {\n                        console.error(`[旺店通同步] 订单 ${updatedOrderInfo.order_sn} 推送失败: ${result.message}`);\n                    }\n                } catch (error) {\n                    console.error('[旺店通同步] 推送订单时发生异常:', error);\n                    // 不抛出异常，避免影响主要的支付流程\n                }\n            } else {\n                console.log(`[旺店通同步] 订单状态不是201，跳过推送。当前状态: ${updatedOrderInfo.order_status}`);\n            }\n\n            // version 1.01\n        }\n    }\n\n    /**\n     * 微信退款通知处理\n     */\n    async refundNotifyAction() {\n        console.log('=== 微信退款异步通知开始 ===');\n        console.log('请求IP:', this.ip);\n        console.log('请求时间:', new Date().toISOString());\n\n        let info = this.post();\n        console.log('接收到的退款通知数据:', info);\n\n        try {\n            // 微信退款通知的数据格式与支付通知不同\n            // 这里简化处理，主要是确认收到通知\n\n            if (info.return_code === 'SUCCESS') {\n                console.log('✅ 微信退款通知处理成功');\n\n                // 可以在这里添加额外的退款后处理逻辑\n                // 比如发送退款成功通知给用户、更新订单状态等\n\n                // 返回成功响应给微信\n                this.ctx.type = 'text/xml';\n                return '<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>';\n            } else {\n                console.error('❌ 微信退款通知失败:', info.return_msg);\n                this.ctx.type = 'text/xml';\n                return '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[处理失败]]></return_msg></xml>';\n            }\n\n        } catch (error) {\n            console.error('❌ 处理微信退款通知异常:', error);\n            this.ctx.type = 'text/xml';\n            return '<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[系统异常]]></return_msg></xml>';\n        }\n    }\n};"]}