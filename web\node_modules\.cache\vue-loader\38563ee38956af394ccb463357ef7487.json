{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSalePage.vue?vue&type=style&index=0&id=621a3ebd&scoped=true&lang=css&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSalePage.vue", "mtime": 1753723289742}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmZsYXNoLXNhbGUtcGFnZSB7CiAgbWluLWhlaWdodDogMTAwdmg7CiAgYmFja2dyb3VuZC1jb2xvcjogI2Y5ZmFmYjsKfQoKLnBhZ2UtaGVhZGVyIHsKICBwb3NpdGlvbjogc3RpY2t5OwogIHRvcDogMDsKICB6LWluZGV4OiAxMDsKfQoKLyog6KGo5qC85qC35byPICovCi5vdmVyZmxvdy14LWF1dG8gewogIG92ZXJmbG93LXg6IGF1dG87Cn0KCnRhYmxlIHsKICBtaW4td2lkdGg6IDEwMCU7Cn0KCi8qIOaWh+acrOaIquaWrSAqLwoubGluZS1jbGFtcC0yIHsKICBkaXNwbGF5OiAtd2Via2l0LWJveDsKICAtd2Via2l0LWxpbmUtY2xhbXA6IDI7CiAgLXdlYmtpdC1ib3gtb3JpZW50OiB2ZXJ0aWNhbDsKICBvdmVyZmxvdzogaGlkZGVuOwp9CgovKiDlk43lupTlvI/orr7orqEgKi8KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7CiAgLmdyaWQtY29scy0xLm1kXFw6Z3JpZC1jb2xzLTQgewogICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMiwgbWlubWF4KDAsIDFmcikpOwogIH0KCiAgLmdyaWQtY29scy0yLm1kXFw6Z3JpZC1jb2xzLTQubGdcXDpncmlkLWNvbHMtNiB7CiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCBtaW5tYXgoMCwgMWZyKSk7CiAgfQp9Cg=="}, {"version": 3, "sources": ["FlashSalePage.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy2BA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "FlashSalePage.vue", "sourceRoot": "src/components/Marketing", "sourcesContent": ["<template>\n  <div class=\"flash-sale-page\">\n    <!-- 页面头部 -->\n    <div class=\"page-header bg-white shadow-sm border-b\">\n      <div class=\"px-6 py-4\">\n        <div class=\"flex items-center justify-between\">\n          <div>\n            <h1 class=\"text-2xl font-semibold text-gray-900\">轮次秒杀</h1>\n            <p class=\"text-sm text-gray-500 mt-1\">每5分钟一轮，间隔2分钟，可选择不同商品参与秒杀</p>\n          </div>\n          <div class=\"flex items-center space-x-3\">\n            <button @click=\"showAddModal = true\" class=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n              <i class=\"ri-add-line mr-2\"></i>\n              创建轮次\n            </button>\n            <button @click=\"showConfigModal = true\" class=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\">\n              <i class=\"ri-settings-line mr-2\"></i>\n              系统配置\n            </button>\n            <button @click=\"refreshData\" class=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n              <i class=\"ri-refresh-line mr-2\"></i>\n              刷新数据\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"p-6 max-w-7xl mx-auto\">\n      <!-- 统计卡片 -->\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center\">\n              <i class=\"ri-flashlight-line text-xl text-red-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm text-gray-500\">秒杀活动总数</p>\n              <p class=\"text-2xl font-bold text-gray-900\">{{ statistics.totalFlashSales }}</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <i class=\"ri-play-circle-line text-xl text-green-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm text-gray-500\">进行中</p>\n              <p class=\"text-2xl font-bold text-gray-900\">{{ statistics.activeFlashSales }}</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\n              <i class=\"ri-shopping-cart-line text-xl text-yellow-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm text-gray-500\">秒杀订单</p>\n              <p class=\"text-2xl font-bold text-gray-900\">{{ statistics.flashSaleOrders }}</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div class=\"flex items-center\">\n            <div class=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <i class=\"ri-money-dollar-circle-line text-xl text-purple-600\"></i>\n            </div>\n            <div class=\"ml-4\">\n              <p class=\"text-sm text-gray-500\">秒杀销售额</p>\n              <p class=\"text-2xl font-bold text-gray-900\">¥{{ statistics.flashSaleSales }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 实时轮次状态 -->\n      <div class=\"bg-white rounded-lg shadow-sm border mb-6\">\n        <div class=\"p-6\">\n          <div class=\"flex items-center justify-between mb-4\">\n            <h3 class=\"text-lg font-semibold text-gray-900\">实时秒杀轮次</h3>\n            <div class=\"flex items-center space-x-2\">\n              <span class=\"text-sm text-gray-500\">自动刷新</span>\n              <div class=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n            </div>\n          </div>\n\n          <!-- 当前进行中的轮次 -->\n          <div v-if=\"currentRounds.length > 0\" class=\"mb-6\">\n            <h4 class=\"text-md font-medium text-gray-800 mb-3 flex items-center\">\n              <span class=\"w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse\"></span>\n              正在进行 ({{ currentRounds.length }}场)\n            </h4>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              <div\n                v-for=\"round in currentRounds\"\n                :key=\"round.id\"\n                class=\"p-4 border-2 border-red-200 bg-red-50 rounded-lg\"\n              >\n                <div class=\"flex items-center justify-between mb-2\">\n                  <span class=\"text-sm font-medium text-red-700\">第{{ round.round_number }}轮</span>\n                  <span class=\"px-2 py-1 bg-red-500 text-white text-xs rounded-full\">进行中</span>\n                </div>\n                <div class=\"text-lg font-semibold text-gray-900 mb-1\">{{ round.campaign.name }}</div>\n                <div class=\"text-sm text-gray-600 mb-2\">\n                  ¥{{ round.campaign.flash_price }}\n                  <span class=\"line-through text-gray-400 ml-1\">¥{{ round.campaign.original_price }}</span>\n                </div>\n                <div class=\"flex justify-between text-xs text-gray-500\">\n                  <span>库存: {{ round.stock - round.sold_count }}/{{ round.stock }}</span>\n                  <span>{{ formatTime(round.end_time) }} 结束</span>\n                </div>\n                <div class=\"mt-2 bg-gray-200 rounded-full h-2\">\n                  <div\n                    class=\"bg-red-500 h-2 rounded-full transition-all duration-300\"\n                    :style=\"{ width: (round.sold_count / round.stock * 100) + '%' }\"\n                  ></div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 即将开始的轮次 -->\n          <div v-if=\"upcomingRounds.length > 0\">\n            <h4 class=\"text-md font-medium text-gray-800 mb-3 flex items-center\">\n              <span class=\"w-2 h-2 bg-orange-500 rounded-full mr-2\"></span>\n              即将开始 ({{ upcomingRounds.length }}场)\n            </h4>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              <div\n                v-for=\"round in upcomingRounds\"\n                :key=\"round.id\"\n                class=\"p-4 border border-orange-200 bg-orange-50 rounded-lg\"\n              >\n                <div class=\"flex items-center justify-between mb-2\">\n                  <span class=\"text-sm font-medium text-orange-700\">第{{ round.round_number }}轮</span>\n                  <span class=\"px-2 py-1 bg-orange-500 text-white text-xs rounded-full\">\n                    {{ formatCountdown(round.countdown) }}\n                  </span>\n                </div>\n                <div class=\"text-md font-semibold text-gray-900 mb-1\">{{ round.campaign.name }}</div>\n                <div class=\"text-sm text-gray-600 mb-2\">\n                  ¥{{ round.campaign.flash_price }}\n                </div>\n                <div class=\"text-xs text-gray-500\">\n                  {{ formatTime(round.start_time) }} 开始\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 无轮次时的提示 -->\n          <div v-if=\"currentRounds.length === 0 && upcomingRounds.length === 0\" class=\"text-center py-8\">\n            <i class=\"ri-time-line text-4xl text-gray-300 mb-2\"></i>\n            <p class=\"text-gray-500\">暂无进行中或即将开始的秒杀轮次</p>\n            <p class=\"text-sm text-gray-400 mt-1\">请创建秒杀活动或等待下一轮开始</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- 筛选和搜索 -->\n      <div class=\"bg-white rounded-lg shadow-sm border mb-6\">\n        <div class=\"p-6\">\n          <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">搜索商品</label>\n              <input\n                v-model=\"searchQuery\"\n                type=\"text\"\n                placeholder=\"输入商品名称\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">活动状态</label>\n              <select\n                v-model=\"filterStatus\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"\">全部状态</option>\n                <option value=\"upcoming\">即将开始</option>\n                <option value=\"active\">进行中</option>\n                <option value=\"ended\">已结束</option>\n                <option value=\"disabled\">已停用</option>\n              </select>\n            </div>\n\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">商品分类</label>\n              <select\n                v-model=\"filterCategory\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"\">全部分类</option>\n                <option value=\"electronics\">数码电器</option>\n                <option value=\"clothing\">服装鞋帽</option>\n                <option value=\"home\">家居用品</option>\n                <option value=\"beauty\">美妆护肤</option>\n              </select>\n            </div>\n\n            <div class=\"flex items-end\">\n              <button @click=\"resetFilters\" class=\"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors\">\n                <i class=\"ri-refresh-line mr-2\"></i>\n                重置\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 秒杀商品列表 -->\n      <div class=\"bg-white rounded-lg shadow-sm border\">\n        <div class=\"px-6 py-4 border-b\">\n          <div class=\"flex items-center justify-between\">\n            <h2 class=\"text-lg font-semibold text-gray-900\">\n              秒杀商品列表\n              <span v-if=\"selectedTimeSlot\" class=\"text-sm font-normal text-gray-500 ml-2\">\n                ({{ selectedTimeSlot.name }} {{ selectedTimeSlot.time }})\n              </span>\n            </h2>\n            <div class=\"flex items-center space-x-2\">\n              <button\n                @click=\"viewMode = 'grid'\"\n                :class=\"[\n                  'px-3 py-1 text-sm rounded-md transition-colors',\n                  viewMode === 'grid' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n                ]\"\n              >\n                <i class=\"ri-grid-line mr-1\"></i>\n                网格视图\n              </button>\n              <button\n                @click=\"viewMode = 'list'\"\n                :class=\"[\n                  'px-3 py-1 text-sm rounded-md transition-colors',\n                  viewMode === 'list' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n                ]\"\n              >\n                <i class=\"ri-list-check mr-1\"></i>\n                列表视图\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <!-- 网格视图 -->\n        <div v-if=\"viewMode === 'grid'\" class=\"p-6\">\n          <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            <div v-for=\"product in filteredProducts\" :key=\"product.id\" class=\"bg-white border rounded-lg overflow-hidden hover:shadow-lg transition-shadow\">\n              <!-- 商品图片 -->\n              <div class=\"relative\">\n                <img :src=\"product.image\" :alt=\"product.name\" class=\"w-full h-48 object-cover\">\n                <div class=\"absolute top-2 left-2\">\n                  <span :class=\"[\n                    'px-2 py-1 text-xs rounded-full',\n                    product.status === 'active' ? 'bg-red-100 text-red-800' :\n                    product.status === 'upcoming' ? 'bg-yellow-100 text-yellow-800' :\n                    product.status === 'ended' ? 'bg-gray-100 text-gray-800' :\n                    'bg-red-100 text-red-800'\n                  ]\">\n                    {{ getStatusLabel(product.status) }}\n                  </span>\n                </div>\n                <div class=\"absolute top-2 right-2\">\n                  <span class=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">\n                    {{ product.category }}\n                  </span>\n                </div>\n                <div v-if=\"product.status === 'active'\" class=\"absolute bottom-2 left-2\">\n                  <div class=\"bg-red-600 text-white px-2 py-1 rounded text-xs\">\n                    <i class=\"ri-time-line mr-1\"></i>\n                    {{ product.remainingTime }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- 商品信息 -->\n              <div class=\"p-4\">\n                <h3 class=\"font-medium text-gray-900 mb-2 line-clamp-2\">{{ product.name }}</h3>\n                <div class=\"space-y-2 text-sm\">\n                  <div class=\"flex justify-between items-center\">\n                    <span class=\"text-gray-500\">原价:</span>\n                    <span class=\"text-gray-400 line-through\">¥{{ product.originalPrice }}</span>\n                  </div>\n                  <div class=\"flex justify-between items-center\">\n                    <span class=\"text-gray-500\">秒杀价:</span>\n                    <span class=\"font-medium text-red-600 text-lg\">¥{{ product.flashPrice }}</span>\n                  </div>\n                  <div class=\"flex justify-between items-center\">\n                    <span class=\"text-gray-500\">库存:</span>\n                    <span class=\"font-medium\">{{ product.stock }}</span>\n                  </div>\n                  <div class=\"flex justify-between items-center\">\n                    <span class=\"text-gray-500\">已售:</span>\n                    <span class=\"font-medium text-green-600\">{{ product.soldCount }}</span>\n                  </div>\n                </div>\n\n                <!-- 进度条 -->\n                <div class=\"mt-3\">\n                  <div class=\"flex justify-between text-xs text-gray-500 mb-1\">\n                    <span>销售进度</span>\n                    <span>{{ Math.round((product.soldCount / (product.soldCount + product.stock)) * 100) }}%</span>\n                  </div>\n                  <div class=\"w-full bg-gray-200 rounded-full h-2\">\n                    <div\n                      class=\"bg-red-600 h-2 rounded-full transition-all duration-300\"\n                      :style=\"{ width: Math.round((product.soldCount / (product.soldCount + product.stock)) * 100) + '%' }\"\n                    ></div>\n                  </div>\n                </div>\n\n                <!-- 操作按钮 -->\n                <div class=\"mt-4 space-y-2\">\n                  <div class=\"flex space-x-2\">\n                    <button @click=\"viewProduct(product)\" class=\"flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700\">\n                      详情\n                    </button>\n                    <button @click=\"editProduct(product)\" class=\"flex-1 px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700\">\n                      编辑\n                    </button>\n                  </div>\n                  <button @click=\"toggleProductStatus(product)\" :class=\"[\n                    'w-full px-3 py-2 text-sm rounded transition-colors',\n                    product.status === 'active'\n                      ? 'bg-red-600 text-white hover:bg-red-700'\n                      : 'bg-green-600 text-white hover:bg-green-700'\n                  ]\">\n                    {{ product.status === 'active' ? '停止秒杀' : '开始秒杀' }}\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 列表视图 -->\n        <div v-else class=\"overflow-x-auto\">\n          <table class=\"w-full\">\n            <thead class=\"bg-gray-50\">\n              <tr>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">商品信息</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">分类</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">原价</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">秒杀价</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">库存</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">已售</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">状态</th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">操作</th>\n              </tr>\n            </thead>\n            <tbody class=\"bg-white divide-y divide-gray-200\">\n              <tr v-for=\"product in filteredProducts\" :key=\"product.id\" class=\"hover:bg-gray-50\">\n                <!-- 商品信息 -->\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"flex items-center\">\n                    <img :src=\"product.image\" :alt=\"product.name\" class=\"w-12 h-12 rounded-lg object-cover\">\n                    <div class=\"ml-3\">\n                      <div class=\"text-sm font-medium text-gray-900 max-w-xs truncate\">{{ product.name }}</div>\n                      <div class=\"text-sm text-gray-500\">ID: {{ product.id }}</div>\n                    </div>\n                  </div>\n                </td>\n\n                <!-- 分类 -->\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <span class=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">\n                    {{ product.category }}\n                  </span>\n                </td>\n\n                <!-- 原价 -->\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-400 line-through\">\n                  ¥{{ product.originalPrice }}\n                </td>\n\n                <!-- 秒杀价 -->\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600\">\n                  ¥{{ product.flashPrice }}\n                </td>\n\n                <!-- 库存 -->\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                  {{ product.stock }}\n                </td>\n\n                <!-- 已售 -->\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600\">\n                  {{ product.soldCount }}\n                </td>\n\n                <!-- 状态 -->\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <span :class=\"[\n                    'px-2 py-1 text-xs rounded-full',\n                    product.status === 'active' ? 'bg-red-100 text-red-800' :\n                    product.status === 'upcoming' ? 'bg-yellow-100 text-yellow-800' :\n                    product.status === 'ended' ? 'bg-gray-100 text-gray-800' :\n                    'bg-red-100 text-red-800'\n                  ]\">\n                    {{ getStatusLabel(product.status) }}\n                  </span>\n                </td>\n\n                <!-- 操作 -->\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                  <button @click=\"viewProduct(product)\" class=\"text-blue-600 hover:text-blue-900\">详情</button>\n                  <button @click=\"editProduct(product)\" class=\"text-green-600 hover:text-green-900\">编辑</button>\n                  <button @click=\"toggleProductStatus(product)\" :class=\"[\n                    'transition-colors',\n                    product.status === 'active' ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'\n                  ]\">\n                    {{ product.status === 'active' ? '停止' : '开始' }}\n                  </button>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n\n        <!-- 分页 -->\n        <div class=\"px-6 py-4 border-t bg-gray-50\">\n          <div class=\"flex items-center justify-between\">\n            <div class=\"text-sm text-gray-500\">\n              显示 {{ Math.min(filteredProducts.length, 20) }} 条，共 {{ filteredProducts.length }} 条记录\n            </div>\n            <div class=\"flex items-center space-x-2\">\n              <button class=\"px-3 py-1 border rounded-md text-sm hover:bg-gray-100\">\n                上一页\n              </button>\n              <span class=\"px-3 py-1 text-sm\">1 / 1</span>\n              <button class=\"px-3 py-1 border rounded-md text-sm hover:bg-gray-100\">\n                下一页\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 创建秒杀弹窗 -->\n    <div v-if=\"showAddModal\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div class=\"bg-white rounded-lg p-6 w-full max-w-3xl mx-4 max-h-[90vh] overflow-y-auto\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h3 class=\"text-lg font-semibold text-gray-900\">创建秒杀活动</h3>\n          <button @click=\"showAddModal = false\" class=\"text-gray-400 hover:text-gray-600\">\n            <i class=\"ri-close-line text-xl\"></i>\n          </button>\n        </div>\n\n        <div class=\"space-y-4\">\n          <div class=\"grid grid-cols-2 gap-4\">\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">选择商品</label>\n              <select\n                v-model=\"newFlashSale.productId\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"\">请选择商品</option>\n                <option value=\"1\">轻奢纯棉刺绣水洗四件套</option>\n                <option value=\"2\">秋冬保暖加厚澳洲羊毛被</option>\n              </select>\n            </div>\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">选择时段</label>\n              <select\n                v-model=\"newFlashSale.timeSlotId\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"\">请选择时段</option>\n                <option v-for=\"slot in timeSlots\" :key=\"slot.id\" :value=\"slot.id\">\n                  {{ slot.name }} {{ slot.time }}\n                </option>\n              </select>\n            </div>\n          </div>\n\n          <div class=\"grid grid-cols-3 gap-4\">\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">秒杀价格</label>\n              <input\n                v-model=\"newFlashSale.flashPrice\"\n                type=\"number\"\n                step=\"0.01\"\n                placeholder=\"请输入秒杀价格\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">秒杀库存</label>\n              <input\n                v-model=\"newFlashSale.stock\"\n                type=\"number\"\n                placeholder=\"请输入秒杀库存\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">限购数量</label>\n              <input\n                v-model=\"newFlashSale.limitQuantity\"\n                type=\"number\"\n                placeholder=\"每人限购数量\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n          </div>\n\n          <div class=\"grid grid-cols-2 gap-4\">\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">开始时间</label>\n              <input\n                v-model=\"newFlashSale.startTime\"\n                type=\"datetime-local\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 mb-2\">结束时间</label>\n              <input\n                v-model=\"newFlashSale.endTime\"\n                type=\"datetime-local\"\n                class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n          </div>\n\n          <div class=\"flex justify-end space-x-3 pt-4 border-t\">\n            <button @click=\"showAddModal = false\" class=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\">\n              取消\n            </button>\n            <button @click=\"createFlashSale\" class=\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700\">\n              创建秒杀\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 时段管理弹窗 -->\n    <div v-if=\"showTimeSlotModal\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div class=\"bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h3 class=\"text-lg font-semibold text-gray-900\">秒杀时段管理</h3>\n          <button @click=\"showTimeSlotModal = false\" class=\"text-gray-400 hover:text-gray-600\">\n            <i class=\"ri-close-line text-xl\"></i>\n          </button>\n        </div>\n\n        <div class=\"space-y-4\">\n          <div class=\"grid grid-cols-1 gap-4\">\n            <div v-for=\"slot in timeSlots\" :key=\"slot.id\" class=\"flex items-center justify-between p-4 border rounded-lg\">\n              <div>\n                <div class=\"font-medium text-gray-900\">{{ slot.name }}</div>\n                <div class=\"text-sm text-gray-500\">{{ slot.time }}</div>\n                <div class=\"text-xs text-gray-400\">{{ slot.productCount }} 个商品</div>\n              </div>\n              <div class=\"flex items-center space-x-2\">\n                <button class=\"px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700\">\n                  编辑\n                </button>\n                <button class=\"px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700\">\n                  删除\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"flex justify-end space-x-3 pt-4 border-t\">\n            <button @click=\"showTimeSlotModal = false\" class=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\">\n              关闭\n            </button>\n            <button class=\"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700\">\n              添加时段\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FlashSalePage',\n  data() {\n    return {\n      // 视图模式\n      viewMode: 'grid',\n\n      // 搜索和筛选\n      searchQuery: '',\n      filterStatus: '',\n      filterCategory: '',\n\n      // 弹窗状态\n      showAddModal: false,\n      showTimeSlotModal: false,\n\n      // 统计数据\n      statistics: {\n        totalCampaigns: 0,\n        activeCampaigns: 0,\n        todayRounds: 0,\n        flashSaleOrders: 0,\n        flashSaleSales: '0.00'\n      },\n\n      // 新秒杀活动数据\n      newFlashSale: {\n        name: '',\n        goodsId: '',\n        flashPrice: '',\n        originalPrice: '',\n        totalStock: '',\n        stockPerRound: '',\n        limitQuantity: 1,\n        startDate: '',\n        endDate: '',\n        dailyStartTime: '09:00:00',\n        dailyEndTime: '22:00:00',\n        roundDuration: 300,\n        breakDuration: 0,\n        autoStart: true\n      },\n\n      // 当前轮次\n      currentRounds: [],\n\n      // 即将开始的轮次\n      upcomingRounds: [],\n\n      // 活动列表\n      campaigns: [],\n\n      // 加载状态\n      loading: false,\n      statisticsLoading: false,\n      timeSlotsLoading: false\n    }\n  },\n\n  mounted() {\n    this.loadData();\n    // 设置定时刷新轮次数据\n    this.refreshTimer = setInterval(() => {\n      this.loadRounds();\n    }, 10000); // 每10秒刷新一次\n  },\n\n  beforeDestroy() {\n    if (this.refreshTimer) {\n      clearInterval(this.refreshTimer);\n    }\n  },\n\n  computed: {\n    filteredProducts() {\n      let filtered = this.products;\n\n      // 根据选中的时段筛选\n      if (this.selectedTimeSlot) {\n        filtered = filtered.filter(product => product.timeSlotId === this.selectedTimeSlot.id);\n      }\n\n      if (this.searchQuery) {\n        filtered = filtered.filter(product =>\n          product.name.toLowerCase().includes(this.searchQuery.toLowerCase())\n        );\n      }\n\n      if (this.filterStatus) {\n        filtered = filtered.filter(product => product.status === this.filterStatus);\n      }\n\n      if (this.filterCategory) {\n        filtered = filtered.filter(product => product.category === this.filterCategory);\n      }\n\n      return filtered;\n    }\n  },\n\n  mounted() {\n    this.loadData();\n  },\n\n  methods: {\n    // 加载所有数据\n    async loadData() {\n      await Promise.all([\n        this.loadStatistics(),\n        this.loadRounds(),\n        this.loadCampaigns()\n      ]);\n    },\n\n    // 加载统计数据\n    async loadStatistics() {\n      this.statisticsLoading = true;\n      try {\n        const response = await this.axios.get('flashsale/statistics');\n        if (response.data.errno === 0) {\n          this.statistics = response.data.data;\n        } else {\n          console.error('获取统计数据失败:', response.data.errmsg);\n        }\n      } catch (error) {\n        console.error('获取统计数据失败:', error);\n        this.$message.error('获取统计数据失败');\n      } finally {\n        this.statisticsLoading = false;\n      }\n    },\n\n    // 加载轮次数据\n    async loadRounds() {\n      try {\n        const response = await this.axios.get('flashsale/rounds');\n        if (response.data.errno === 0) {\n          this.currentRounds = response.data.data.current || [];\n          this.upcomingRounds = response.data.data.upcoming || [];\n\n          // 为即将开始的轮次计算倒计时\n          this.upcomingRounds.forEach(round => {\n            const startTime = new Date(round.start_time);\n            const now = new Date();\n            round.countdown = Math.max(0, Math.floor((startTime - now) / 1000));\n          });\n        } else {\n          console.error('获取轮次数据失败:', response.data.errmsg);\n        }\n      } catch (error) {\n        console.error('获取轮次数据失败:', error);\n      }\n    },\n\n    // 加载活动列表\n    async loadCampaigns() {\n      this.loading = true;\n      try {\n        const params = {\n          search: this.searchQuery,\n          status: this.filterStatus,\n          page: 1,\n          limit: 50\n        };\n\n        const response = await this.axios.get('flashsale/campaigns', { params });\n        if (response.data.errno === 0) {\n          this.campaigns = response.data.data;\n        } else {\n          console.error('获取活动列表失败:', response.data.errmsg);\n        }\n      } catch (error) {\n        console.error('获取活动列表失败:', error);\n        this.$message.error('获取活动列表失败');\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 获取状态标签\n    getStatusLabel(status) {\n      const labels = {\n        active: '进行中',\n        upcoming: '即将开始',\n        ended: '已结束',\n        disabled: '已停用'\n      };\n      return labels[status] || status;\n    },\n\n    // 选择时段\n    selectTimeSlot(timeSlot) {\n      this.selectedTimeSlot = this.selectedTimeSlot?.id === timeSlot.id ? null : timeSlot;\n      this.loadProducts(); // 重新加载商品列表\n    },\n\n    // 重置筛选\n    resetFilters() {\n      this.searchQuery = '';\n      this.filterStatus = '';\n      this.filterCategory = '';\n    },\n\n    // 格式化时间显示\n    formatTime(timeStr) {\n      const date = new Date(timeStr);\n      return date.toLocaleString('zh-CN', {\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    // 格式化倒计时\n    formatCountdown(seconds) {\n      if (seconds <= 0) return '即将开始';\n\n      const minutes = Math.floor(seconds / 60);\n      const remainingSeconds = seconds % 60;\n\n      if (minutes > 0) {\n        return `${minutes}分${remainingSeconds}秒`;\n      } else {\n        return `${remainingSeconds}秒`;\n      }\n    },\n\n    // 查看商品详情\n    viewProduct(product) {\n      console.log('查看商品详情:', product);\n      this.$message.info('查看功能开发中');\n    },\n\n    // 编辑商品\n    editProduct(product) {\n      console.log('编辑商品:', product);\n      this.$message.info('编辑功能开发中');\n    },\n\n    // 切换商品状态\n    toggleProductStatus(product) {\n      console.log('切换商品状态:', product);\n      this.$message.info('状态切换功能开发中');\n    },\n\n    // 创建秒杀活动\n    async createFlashSale() {\n      try {\n        const response = await this.axios.post('flashsale/create', this.newFlashSale);\n        if (response.data.errno === 0) {\n          this.showAddModal = false;\n          this.$message.success('秒杀活动创建成功');\n          this.loadData(); // 重新加载数据\n          // 重置表单\n          this.newFlashSale = {\n            productId: '',\n            timeSlotId: '',\n            flashPrice: '',\n            stock: '',\n            limitQuantity: '',\n            startTime: '',\n            endTime: ''\n          };\n        } else {\n          this.$message.error(response.data.errmsg || '创建失败');\n        }\n      } catch (error) {\n        console.error('创建秒杀活动失败:', error);\n        this.$message.error('创建失败');\n      }\n    },\n\n    // 刷新数据\n    refreshData() {\n      this.loadData();\n      this.$message.success('数据刷新成功');\n    }\n  }\n}\n</script>\n\n<style scoped>\n.flash-sale-page {\n  min-height: 100vh;\n  background-color: #f9fafb;\n}\n\n.page-header {\n  position: sticky;\n  top: 0;\n  z-index: 10;\n}\n\n/* 表格样式 */\n.overflow-x-auto {\n  overflow-x: auto;\n}\n\ntable {\n  min-width: 100%;\n}\n\n/* 文本截断 */\n.line-clamp-2 {\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .grid-cols-1.md\\\\:grid-cols-4 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .grid-cols-2.md\\\\:grid-cols-4.lg\\\\:grid-cols-6 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n}\n</style>"]}]}