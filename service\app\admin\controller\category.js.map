{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\category.js"], "names": ["Base", "require", "module", "exports", "indexAction", "model", "data", "order", "select", "topCategory", "filter", "item", "parent_id", "categoryList", "map", "level", "push", "child", "id", "is_show", "is_channel", "is_category", "success", "updateSortAction", "post", "sort", "where", "update", "sort_order", "topCategoryAction", "infoAction", "get", "find", "storeAction", "isPost", "values", "add", "destoryAction", "length", "fail", "limit", "delete", "showStatusAction", "status", "ele", "channelStatusAction", "stat", "categoryStatusAction", "deleteBannerImageAction", "img_url", "deleteIconImageAction", "icon_url"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACAC,OAAOC,OAAP,GAAiB,cAAcH,IAAd,CAAmB;AAChC;;;;AAIMI,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAMC,QAAQ,MAAKA,KAAL,CAAW,UAAX,CAAd;AACA,kBAAMC,OAAO,MAAMD,MAAME,KAAN,CAAY,CAAC,gBAAD,CAAZ,EAAgCC,MAAhC,EAAnB;AACA,kBAAMC,cAAcH,KAAKI,MAAL,CAAY,UAACC,IAAD,EAAU;AACtC,uBAAOA,KAAKC,SAAL,KAAmB,CAA1B;AACH,aAFmB,CAApB;AAGA,kBAAMC,eAAe,EAArB;AACAJ,wBAAYK,GAAZ,CAAgB,UAACH,IAAD,EAAU;AACtBA,qBAAKI,KAAL,GAAa,CAAb;AACAF,6BAAaG,IAAb,CAAkBL,IAAlB;AACAL,qBAAKQ,GAAL,CAAS,UAACG,KAAD,EAAW;AAChB,wBAAIA,MAAML,SAAN,KAAoBD,KAAKO,EAA7B,EAAiC;AAC7BD,8BAAMF,KAAN,GAAc,CAAd;AACAF,qCAAaG,IAAb,CAAkBC,KAAlB;AACH;AACD,wBAAIA,MAAME,OAAN,IAAiB,CAArB,EAAwB;AACpBF,8BAAME,OAAN,GAAgB,IAAhB;AACH,qBAFD,MAEO;AACHF,8BAAME,OAAN,GAAgB,KAAhB;AACH;AACD,wBAAIF,MAAMG,UAAN,IAAoB,CAAxB,EAA2B;AACvBH,8BAAMG,UAAN,GAAmB,IAAnB;AACH,qBAFD,MAEO;AACHH,8BAAMG,UAAN,GAAmB,KAAnB;AACH;AACD,wBAAIH,MAAMI,WAAN,IAAqB,CAAzB,EAA4B;AACxBJ,8BAAMI,WAAN,GAAoB,IAApB;AACH,qBAFD,MAEO;AACHJ,8BAAMI,WAAN,GAAoB,KAApB;AACH;AACJ,iBApBD;AAqBH,aAxBD;AAyBA,mBAAO,MAAKC,OAAL,CAAaT,YAAb,CAAP;AAhCgB;AAiCnB;AACKU,oBAAN,GAAyB;AAAA;;AAAA;AACrB,kBAAML,KAAK,OAAKM,IAAL,CAAU,IAAV,CAAX;AACA,kBAAMC,OAAO,OAAKD,IAAL,CAAU,MAAV,CAAb;AACA,kBAAMnB,QAAQ,OAAKA,KAAL,CAAW,UAAX,CAAd;AACA,kBAAMC,OAAO,MAAMD,MAAMqB,KAAN,CAAY;AAC3BR,oBAAIA;AADuB,aAAZ,EAEhBS,MAFgB,CAET;AACNC,4BAAYH;AADN,aAFS,CAAnB;AAKA,mBAAO,OAAKH,OAAL,CAAahB,IAAb,CAAP;AATqB;AAUxB;AACKuB,qBAAN,GAA0B;AAAA;;AAAA;AACtB,kBAAMxB,QAAQ,OAAKA,KAAL,CAAW,UAAX,CAAd;AACA,kBAAMC,OAAO,MAAMD,MAAMqB,KAAN,CAAY;AAC3Bd,2BAAW;AADgB,aAAZ,EAEhBL,KAFgB,CAEV,CAAC,QAAD,CAFU,EAEEC,MAFF,EAAnB;AAGA,mBAAO,OAAKc,OAAL,CAAahB,IAAb,CAAP;AALsB;AAMzB;AACKwB,cAAN,GAAmB;AAAA;;AAAA;AACf,kBAAMZ,KAAK,OAAKa,GAAL,CAAS,IAAT,CAAX;AACA,kBAAM1B,QAAQ,OAAKA,KAAL,CAAW,UAAX,CAAd;AACA,kBAAMC,OAAO,MAAMD,MAAMqB,KAAN,CAAY;AAC3BR,oBAAIA;AADuB,aAAZ,EAEhBc,IAFgB,EAAnB;AAGA,mBAAO,OAAKV,OAAL,CAAahB,IAAb,CAAP;AANe;AAOlB;AACK2B,eAAN,GAAoB;AAAA;;AAAA;AAChB,gBAAI,CAAC,OAAKC,MAAV,EAAkB;AACd,uBAAO,KAAP;AACH;AACD,kBAAMC,SAAS,OAAKX,IAAL,EAAf;AACA,kBAAMN,KAAK,OAAKM,IAAL,CAAU,IAAV,CAAX;AACA,kBAAMnB,QAAQ,OAAKA,KAAL,CAAW,UAAX,CAAd;AACA8B,mBAAOhB,OAAP,GAAiBgB,OAAOhB,OAAP,GAAiB,CAAjB,GAAqB,CAAtC;AACAgB,mBAAOf,UAAP,GAAoBe,OAAOf,UAAP,GAAoB,CAApB,GAAwB,CAA5C;AACAe,mBAAOd,WAAP,GAAqBc,OAAOd,WAAP,GAAqB,CAArB,GAAyB,CAA9C;AACA,gBAAIH,KAAK,CAAT,EAAY;AACR,sBAAMb,MAAMqB,KAAN,CAAY;AACdR,wBAAIA;AADU,iBAAZ,EAEHS,MAFG,CAEIQ,MAFJ,CAAN;AAGH,aAJD,MAIO;AACH,uBAAOA,OAAOjB,EAAd;AACA,sBAAMb,MAAM+B,GAAN,CAAUD,MAAV,CAAN;AACH;AACD,mBAAO,OAAKb,OAAL,CAAaa,MAAb,CAAP;AAlBgB;AAmBnB;AACKE,iBAAN,GAAsB;AAAA;;AAAA;AAClB,kBAAMnB,KAAK,OAAKM,IAAL,CAAU,IAAV,CAAX;AACA,gBAAIlB,OAAO,MAAM,OAAKD,KAAL,CAAW,UAAX,EAAuBqB,KAAvB,CAA6B;AAC1Cd,2BAAWM;AAD+B,aAA7B,EAEdV,MAFc,EAAjB;AAGA,gBAAIF,KAAKgC,MAAL,GAAc,CAAlB,EAAqB;AACjB,uBAAO,OAAKC,IAAL,EAAP;AACH,aAFD,MAEO;AACH,sBAAM,OAAKlC,KAAL,CAAW,UAAX,EAAuBqB,KAAvB,CAA6B;AAC/BR,wBAAIA;AAD2B,iBAA7B,EAEHsB,KAFG,CAEG,CAFH,EAEMC,MAFN,EAAN;AAGA,uBAAO,OAAKnB,OAAL,EAAP;AACH;AAZiB;AAarB;AACKoB,oBAAN,GAAyB;AAAA;;AAAA;AACrB,kBAAMxB,KAAK,OAAKa,GAAL,CAAS,IAAT,CAAX;AACA,kBAAMY,SAAS,OAAKZ,GAAL,CAAS,QAAT,CAAf;AACA,gBAAIa,MAAM,CAAV;AACA,gBAAID,UAAU,MAAd,EAAsB;AAClBC,sBAAM,CAAN;AACH;AACD,kBAAMvC,QAAQ,OAAKA,KAAL,CAAW,UAAX,CAAd;AACA,kBAAMA,MAAMqB,KAAN,CAAY;AACdR,oBAAIA;AADU,aAAZ,EAEHS,MAFG,CAEI;AACNR,yBAASyB;AADH,aAFJ,CAAN;AARqB;AAaxB;AACKC,uBAAN,GAA4B;AAAA;;AAAA;AACxB,kBAAM3B,KAAK,OAAKa,GAAL,CAAS,IAAT,CAAX;AACA,kBAAMY,SAAS,OAAKZ,GAAL,CAAS,QAAT,CAAf;AACA,gBAAIe,OAAO,CAAX;AACA,gBAAIH,UAAU,MAAd,EAAsB;AAClBG,uBAAO,CAAP;AACH;AACD,kBAAMzC,QAAQ,OAAKA,KAAL,CAAW,UAAX,CAAd;AACA,kBAAMA,MAAMqB,KAAN,CAAY;AACdR,oBAAIA;AADU,aAAZ,EAEHS,MAFG,CAEI;AACNP,4BAAY0B;AADN,aAFJ,CAAN;AARwB;AAa3B;AACKC,wBAAN,GAA6B;AAAA;;AAAA;AACzB,kBAAM7B,KAAK,OAAKa,GAAL,CAAS,IAAT,CAAX;AACA,kBAAMY,SAAS,OAAKZ,GAAL,CAAS,QAAT,CAAf;AACA,gBAAIe,OAAO,CAAX;AACA,gBAAIH,UAAU,MAAd,EAAsB;AAClBG,uBAAO,CAAP;AACH;AACD,kBAAMzC,QAAQ,OAAKA,KAAL,CAAW,UAAX,CAAd;AACA,kBAAMA,MAAMqB,KAAN,CAAY;AACdR,oBAAIA;AADU,aAAZ,EAEHS,MAFG,CAEI;AACNN,6BAAayB;AADP,aAFJ,CAAN;AARyB;AAa5B;AACKE,2BAAN,GAAgC;AAAA;;AAAA;AAC5B,gBAAI9B,KAAK,QAAKM,IAAL,CAAU,IAAV,CAAT;AACA,kBAAM,QAAKnB,KAAL,CAAW,UAAX,EAAuBqB,KAAvB,CAA6B;AAC/BR,oBAAIA;AAD2B,aAA7B,EAEHS,MAFG,CAEI;AACNsB,yBAAS;AADH,aAFJ,CAAN;AAKA,mBAAO,QAAK3B,OAAL,EAAP;AAP4B;AAQ/B;AACK4B,yBAAN,GAA8B;AAAA;;AAAA;AAC1B,gBAAIhC,KAAK,QAAKM,IAAL,CAAU,IAAV,CAAT;AACA,kBAAM,QAAKnB,KAAL,CAAW,UAAX,EAAuBqB,KAAvB,CAA6B;AAC/BR,oBAAIA;AAD2B,aAA7B,EAEHS,MAFG,CAEI;AACNwB,0BAAU;AADJ,aAFJ,CAAN;AAKA,mBAAO,QAAK7B,OAAL,EAAP;AAP0B;AAQ7B;AA9J+B,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\category.js", "sourcesContent": ["const Base = require('./base.js');\nmodule.exports = class extends Base {\n    /**\n     * index action\n     * @return {Promise} []\n     */\n    async indexAction() {\n        const model = this.model('category');\n        const data = await model.order(['sort_order ASC']).select();\n        const topCategory = data.filter((item) => {\n            return item.parent_id === 0;\n        });\n        const categoryList = [];\n        topCategory.map((item) => {\n            item.level = 1;\n            categoryList.push(item);\n            data.map((child) => {\n                if (child.parent_id === item.id) {\n                    child.level = 2;\n                    categoryList.push(child);\n                }\n                if (child.is_show == 1) {\n                    child.is_show = true;\n                } else {\n                    child.is_show = false;\n                }\n                if (child.is_channel == 1) {\n                    child.is_channel = true;\n                } else {\n                    child.is_channel = false;\n                }\n                if (child.is_category == 1) {\n                    child.is_category = true;\n                } else {\n                    child.is_category = false;\n                }\n            });\n        });\n        return this.success(categoryList);\n    }\n    async updateSortAction() {\n        const id = this.post('id');\n        const sort = this.post('sort');\n        const model = this.model('category');\n        const data = await model.where({\n            id: id\n        }).update({\n            sort_order: sort\n        });\n        return this.success(data);\n    }\n    async topCategoryAction() {\n        const model = this.model('category');\n        const data = await model.where({\n            parent_id: 0\n        }).order(['id ASC']).select();\n        return this.success(data);\n    }\n    async infoAction() {\n        const id = this.get('id');\n        const model = this.model('category');\n        const data = await model.where({\n            id: id\n        }).find();\n        return this.success(data);\n    }\n    async storeAction() {\n        if (!this.isPost) {\n            return false;\n        }\n        const values = this.post();\n        const id = this.post('id');\n        const model = this.model('category');\n        values.is_show = values.is_show ? 1 : 0;\n        values.is_channel = values.is_channel ? 1 : 0;\n        values.is_category = values.is_category ? 1 : 0;\n        if (id > 0) {\n            await model.where({\n                id: id\n            }).update(values);\n        } else {\n            delete values.id;\n            await model.add(values);\n        }\n        return this.success(values);\n    }\n    async destoryAction() {\n        const id = this.post('id');\n        let data = await this.model('category').where({\n            parent_id: id\n        }).select();\n        if (data.length > 0) {\n            return this.fail();\n        } else {\n            await this.model('category').where({\n                id: id\n            }).limit(1).delete();\n            return this.success();\n        }\n    }\n    async showStatusAction() {\n        const id = this.get('id');\n        const status = this.get('status');\n        let ele = 0;\n        if (status == 'true') {\n            ele = 1;\n        }\n        const model = this.model('category');\n        await model.where({\n            id: id\n        }).update({\n            is_show: ele\n        });\n    }\n    async channelStatusAction() {\n        const id = this.get('id');\n        const status = this.get('status');\n        let stat = 0;\n        if (status == 'true') {\n            stat = 1;\n        }\n        const model = this.model('category');\n        await model.where({\n            id: id\n        }).update({\n            is_channel: stat\n        });\n    }\n    async categoryStatusAction() {\n        const id = this.get('id');\n        const status = this.get('status');\n        let stat = 0;\n        if (status == 'true') {\n            stat = 1;\n        }\n        const model = this.model('category');\n        await model.where({\n            id: id\n        }).update({\n            is_category: stat\n        });\n    }\n    async deleteBannerImageAction() {\n        let id = this.post('id');\n        await this.model('category').where({\n            id: id\n        }).update({\n            img_url: ''\n        });\n        return this.success();\n    }\n    async deleteIconImageAction() {\n        let id = this.post('id');\n        await this.model('category').where({\n            id: id\n        }).update({\n            icon_url: ''\n        });\n        return this.success();\n    }\n};"]}