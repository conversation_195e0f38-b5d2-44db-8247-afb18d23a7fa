{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\auth.js"], "names": ["rp", "require", "module", "exports", "think", "Controller", "confirmQrLoginAction", "qrToken", "post", "openid", "get", "fail", "currentTime", "parseInt", "Date", "now", "qrData", "model", "query", "qr<PERSON><PERSON><PERSON>", "length", "expire_time", "status", "user", "where", "weixin_openid", "is_delete", "find", "isEmpty", "userType", "checkUserRole", "id", "success", "message", "userName", "nickname", "error", "console", "userId", "promoter", "user_id", "distributor", "is_active", "audit_status", "loginByWeixinAction", "code", "userInfo", "log", "getTime", "clientIp", "options", "method", "url", "qs", "grant_type", "js_code", "secret", "config", "appid", "sessionData", "JSON", "parse", "getField", "is_new", "buffer", "<PERSON><PERSON><PERSON>", "from", "toString", "avatarUrl", "nick<PERSON><PERSON>", "nick<PERSON><PERSON>er", "startsWith", "add", "username", "uuid", "password", "register_time", "register_ip", "last_login_time", "last_login_ip", "mobile", "avatar", "is_new_user", "autoDistributeNewUserCoupons", "updateResult", "update", "newUserInfo", "field", "is_authorized", "TokenSerivce", "service", "<PERSON><PERSON><PERSON>", "create", "token", "logoutAction", "getPhoneNumberAction", "currentUserId", "ctx", "header", "tokenService", "getUserId", "tokenOptions", "tokenResponse", "access_token", "phoneOptions", "body", "json", "phoneResponse", "<PERSON><PERSON><PERSON>", "phoneInfo", "phone_info", "userModel", "phoneNumber", "updated_at", "updateError", "purePhoneNumber", "countryCode", "errmsg", "hasNewUserCoupon", "alias", "join", "count", "newUserCoupons", "type", "auto_distribute", "start_time", "end_time", "select", "coupon", "couponCode", "generateCouponCode", "expireAt", "valid_days", "coupon_id", "coupon_code", "expire_at", "source", "name", "timestamp", "random", "Math", "substr", "toUpperCase"], "mappings": ";;AAAA,MAAMA,KAAKC,QAAQ,iBAAR,CAAX;AACAC,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,UAApB,CAA+B;;AAE9C;AACMC,sBAAN,GAA6B;AAAA;;AAAA;AAC3B,UAAI;AACF,cAAMC,UAAU,MAAKC,IAAL,CAAU,SAAV,CAAhB;AACA,cAAMC,SAAS,MAAKD,IAAL,CAAU,QAAV,KAAuB,MAAKE,GAAL,CAAS,QAAT,CAAtC;;AAEA,YAAI,CAACH,OAAL,EAAc;AACZ,iBAAO,MAAKI,IAAL,CAAU,YAAV,CAAP;AACD;;AAED,YAAI,CAACF,MAAL,EAAa;AACX,iBAAO,MAAKE,IAAL,CAAU,OAAV,CAAP;AACD;;AAED,cAAMC,cAAcC,SAASC,KAAKC,GAAL,KAAa,IAAtB,CAApB;;AAEA;AACA,cAAMC,SAAS,MAAM,MAAKC,KAAL,GAAaC,KAAb,CAAoB;+DACgBX,OAAQ;OAD5C,CAArB;AAGA,cAAMY,WAAWH,UAAUA,OAAOI,MAAP,GAAgB,CAA1B,GAA8BJ,OAAO,CAAP,CAA9B,GAA0C,IAA3D;;AAEA,YAAI,CAACG,QAAL,EAAe;AACb,iBAAO,MAAKR,IAAL,CAAU,QAAV,CAAP;AACD;;AAED;AACA,YAAIC,cAAcO,SAASE,WAA3B,EAAwC;AACtC,gBAAM,MAAKJ,KAAL,GAAaC,KAAb,CAAoB;iFAC+CX,OAAQ;SAD3E,CAAN;AAGA,iBAAO,MAAKI,IAAL,CAAU,eAAV,CAAP;AACD;;AAED;AACA,YAAIQ,SAASG,MAAT,KAAoB,SAAxB,EAAmC;AACjC,iBAAO,MAAKX,IAAL,CAAU,SAAV,CAAP;AACD;;AAED;AACA,cAAMY,OAAO,MAAM,MAAKN,KAAL,CAAW,MAAX,EAAmBO,KAAnB,CAAyB;AAC1CC,yBAAehB,MAD2B;AAE1CiB,qBAAW;AAF+B,SAAzB,EAGhBC,IAHgB,EAAnB;;AAKA,YAAIvB,MAAMwB,OAAN,CAAcL,IAAd,CAAJ,EAAyB;AACvB,iBAAO,MAAKZ,IAAL,CAAU,OAAV,CAAP;AACD;;AAED;AACA,cAAMkB,WAAW,MAAM,MAAKC,aAAL,CAAmBP,KAAKQ,EAAxB,CAAvB;AACA,YAAI,CAACF,QAAL,EAAe;AACb,iBAAO,MAAKlB,IAAL,CAAU,sBAAV,CAAP;AACD;;AAED;AACA,cAAM,MAAKM,KAAL,GAAaC,KAAb,CAAoB;;4CAEYK,KAAKQ,EAAG,kBAAiBF,QAAS;0BACpDjB,WAAY,kBAAiBA,WAAY;yBAC1CL,OAAQ;OAJrB,CAAN;;AAOA,eAAO,MAAKyB,OAAL,CAAa;AAClBC,mBAAS,gBADS;AAElBJ,oBAAUA,QAFQ;AAGlBK,oBAAUX,KAAKY;AAHG,SAAb,CAAP;AAMD,OAnED,CAmEE,OAAOC,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,MAAKzB,IAAL,CAAU,YAAV,CAAP;AACD;AAvE0B;AAwE5B;;AAED;AACMmB,eAAN,CAAoBQ,MAApB,EAA4B;AAAA;;AAAA;AAC1B,UAAI;AACF;AACA,cAAMC,WAAW,MAAM,OAAKtB,KAAL,CAAW,oBAAX,EAAiCO,KAAjC,CAAuC;AAC5DgB,mBAASF,MADmD;AAE5DhB,kBAAQ;AAFoD,SAAvC,EAGpBK,IAHoB,EAAvB;;AAKA,YAAI,CAACvB,MAAMwB,OAAN,CAAcW,QAAd,CAAL,EAA8B;AAC5B,iBAAO,UAAP;AACD;;AAED;AACA,cAAME,cAAc,MAAM,OAAKxB,KAAL,CAAW,cAAX,EAA2BO,KAA3B,CAAiC;AACzDgB,mBAASF,MADgD;AAEzDI,qBAAW,CAF8C;AAGzDC,wBAAc;AAH2C,SAAjC,EAIvBhB,IAJuB,EAA1B;;AAMA,YAAI,CAACvB,MAAMwB,OAAN,CAAca,WAAd,CAAL,EAAiC;AAC/B,iBAAO,aAAP;AACD;;AAED,eAAO,IAAP;AACD,OAvBD,CAuBE,OAAOL,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,IAAP;AACD;AA3ByB;AA4B3B;AACKQ,qBAAN,GAA4B;AAAA;;AAAA;AAC1B;AACA,YAAMC,OAAO,OAAKrC,IAAL,CAAU,MAAV,CAAb;AACA,YAAMsC,WAAW,OAAKtC,IAAL,CAAU,UAAV,CAAjB,CAH0B,CAGc;;AAExC;AACA6B,cAAQU,GAAR,CAAY,kBAAZ;AACAV,cAAQU,GAAR,CAAY,WAAZ,EAAyBF,IAAzB;AACAR,cAAQU,GAAR,CAAY,eAAZ,EAA6BD,QAA7B;AACAT,cAAQU,GAAR,CAAY,aAAZ,EAA2B,OAAOD,QAAlC;AACAT,cAAQU,GAAR,CAAY,eAAZ,EAA6B3C,MAAMwB,OAAN,CAAckB,QAAd,CAA7B;;AAEA,UAAIlC,cAAcC,SAAS,IAAIC,IAAJ,GAAWkC,OAAX,KAAuB,IAAhC,CAAlB;AACA,YAAMC,WAAW,EAAjB,CAb0B,CAaL;;AAErB;AACA,YAAMC,UAAU;AACdC,gBAAQ,KADM;AAEdC,aAAK,8CAFS;AAGdC,YAAI;AACFC,sBAAY,oBADV;AAEFC,mBAASV,IAFP;AAGFW,kBAAQpD,MAAMqD,MAAN,CAAa,eAAb,CAHN;AAIFC,iBAAOtD,MAAMqD,MAAN,CAAa,cAAb;AAJL;AAHU,OAAhB;;AAWA,UAAIE,cAAc,MAAM3D,GAAGkD,OAAH,CAAxB;AACAS,oBAAcC,KAAKC,KAAL,CAAWF,WAAX,CAAd;AACA,UAAI,CAACA,YAAYlD,MAAjB,EAAyB;AACvB,eAAO,OAAKE,IAAL,CAAU,eAAV,CAAP;AACD;;AAED;AACA,UAAI2B,SAAS,MAAM,OAAKrB,KAAL,CAAW,MAAX,EAChBO,KADgB,CACV;AACLC,uBAAekC,YAAYlD;AADtB,OADU,EAIhBqD,QAJgB,CAIP,IAJO,EAID,IAJC,CAAnB;;AAMA,UAAIC,SAAS,CAAb;;AAEA;AACA;AACA,YAAMC,SAASC,OAAOC,IAAP,CAAY,MAAZ,CAAf;AACA,UAAI/B,WAAW6B,OAAOG,QAAP,CAAgB,QAAhB,CAAf;AACA,UAAIC,YAAY,qCAAhB;;AAEA;AACA,UAAItB,QAAJ,EAAc;AACZ,YAAIA,SAASuB,QAAb,EAAuB;AACrB;AACA,gBAAMC,aAAaL,OAAOC,IAAP,CAAYpB,SAASuB,QAArB,CAAnB;AACAlC,qBAAWmC,WAAWH,QAAX,CAAoB,QAApB,CAAX;AACD;;AAED,YAAIrB,SAASsB,SAAb,EAAwB;AACtB;AACA,cAAItB,SAASsB,SAAT,CAAmBG,UAAnB,CAA8B,WAA9B,CAAJ,EAAgD;AAC9ClC,oBAAQU,GAAR,CAAY,wBAAZ,EAAsCD,SAASsB,SAA/C;AACA;AACAA,wBAAYtB,SAASsB,SAArB;AACD,WAJD,MAIO;AACL;AACAA,wBAAYtB,SAASsB,SAArB;AACD;AACD/B,kBAAQU,GAAR,CAAY,UAAZ,EAAwBqB,SAAxB;AACD;AACF;;AAED,UAAIhE,MAAMwB,OAAN,CAAcU,MAAd,CAAJ,EAA2B;AACzB;AACAA,iBAAS,MAAM,OAAKrB,KAAL,CAAW,MAAX,EAAmBuD,GAAnB,CAAuB;AACpCC,oBAAU,SAASrE,MAAMsE,IAAN,CAAW,CAAX,CADiB,EACF;AAClCC,oBAAUhB,YAAYlD,MAFc;AAGpCmE,yBAAehE,WAHqB;AAIpCiE,uBAAa5B,QAJuB;AAKpC6B,2BAAiBlE,WALmB;AAMpCmE,yBAAe9B,QANqB;AAOpC+B,kBAAQ,EAP4B;AAQpCvD,yBAAekC,YAAYlD,MARS;AASpC0B,oBAAUA,QAT0B;AAUpC8C,kBAAQb,SAV4B;AAWpCc,uBAAa,CAXuB,CAWrB;AAXqB,SAAvB,CAAf;AAaAnB,iBAAS,CAAT;;AAEA;AACA,YAAI;AACF,gBAAM,OAAKoB,4BAAL,CAAkC7C,MAAlC,CAAN;AACD,SAFD,CAEE,OAAOF,KAAP,EAAc;AACdC,kBAAQU,GAAR,CAAY,YAAZ,EAA0BX,KAA1B;AACA;AACD;AACF,OAxBD,MAwBO,IAAIU,QAAJ,EAAc;AACnB;AACAT,gBAAQU,GAAR,CAAY,iBAAZ;AACAV,gBAAQU,GAAR,CAAY,OAAZ,EAAqBT,MAArB;AACAD,gBAAQU,GAAR,CAAY,OAAZ,EAAqBZ,QAArB;AACAE,gBAAQU,GAAR,CAAY,OAAZ,EAAqBqB,SAArB;;AAEA,cAAMgB,eAAe,MAAM,OAAKnE,KAAL,CAAW,MAAX,EACxBO,KADwB,CAClB;AACLO,cAAIO;AADC,SADkB,EAIxB+C,MAJwB,CAIjB;AACNlD,oBAAUA,QADJ;AAEN8C,kBAAQb,SAFF;AAGNU,2BAAiBlE,WAHX;AAINmE,yBAAe9B;AAJT,SAJiB,CAA3B;;AAWAZ,gBAAQU,GAAR,CAAY,UAAZ,EAAwBqC,YAAxB;AACD,OAnBM,MAmBA;AACL;AACA/C,gBAAQU,GAAR,CAAY,6BAAZ;AACAV,gBAAQU,GAAR,CAAY,OAAZ,EAAqBT,MAArB;;AAEA,cAAM,OAAKrB,KAAL,CAAW,MAAX,EACHO,KADG,CACG;AACLO,cAAIO;AADC,SADH,EAIH+C,MAJG,CAII;AACNP,2BAAiBlE,WADX;AAENmE,yBAAe9B;AAFT,SAJJ,CAAN;AAQD;;AAEDU,kBAAYnB,OAAZ,GAAsBF,MAAtB;;AAEA,YAAMgD,cAAc,MAAM,OAAKrE,KAAL,CAAW,MAAX,EACvBsE,KADuB,CACjB,8BADiB,EAEvB/D,KAFuB,CAEjB;AACLO,YAAIO;AADC,OAFiB,EAKvBX,IALuB,EAA1B;;AAOA2D,kBAAYnD,QAAZ,GAAuB8B,OAAOC,IAAP,CACrBoB,YAAYnD,QADS,EAErB,QAFqB,EAGrBgC,QAHqB,EAAvB;;AAKA;AACA;AACAmB,kBAAYE,aAAZ,GAA4B1C,WAAW,IAAX,GAAkB,KAA9C;;AAEA,YAAM2C,eAAe,OAAKC,OAAL,CAAa,OAAb,EAAsB,KAAtB,CAArB;AACA,YAAMC,aAAa,MAAMF,aAAaG,MAAb,CAAoBjC,WAApB,CAAzB;;AAEA,UAAIvD,MAAMwB,OAAN,CAAc0D,WAAd,KAA8BlF,MAAMwB,OAAN,CAAc+D,UAAd,CAAlC,EAA6D;AAC3D,eAAO,OAAKhF,IAAL,CAAU,MAAV,CAAP;AACD;;AAED,aAAO,OAAKqB,OAAL,CAAa;AAClB6D,eAAOF,UADW;AAElB7C,kBAAUwC,WAFQ;AAGlBvB,gBAAQA,MAHU;AAIlBtD,gBAAQkD,YAAYlD;AAJF,OAAb,CAAP;AAzJ0B;AA+J3B;AACKqF,cAAN,GAAqB;AAAA;;AAAA;AACnB,aAAO,OAAK9D,OAAL,EAAP;AADmB;AAEpB;;AAED;;;AAGM+D,sBAAN,GAA6B;AAAA;;AAAA;AAC3B,UAAI;AACF;AACA,cAAMlD,OAAO,OAAKrC,IAAL,CAAU,MAAV,CAAb;AACA,cAAM8B,SAAS,OAAK9B,IAAL,CAAU,QAAV,CAAf,CAHE,CAGkC;;AAEpC6B,gBAAQU,GAAR,CAAY,mBAAZ;AACAV,gBAAQU,GAAR,CAAY,WAAZ,EAAyBF,IAAzB;AACAR,gBAAQU,GAAR,CAAY,OAAZ,EAAqBT,MAArB;;AAEA,YAAI,CAACO,IAAL,EAAW;AACT,iBAAO,OAAKlC,IAAL,CAAU,GAAV,EAAe,YAAf,CAAP;AACD;;AAED;AACA,YAAIqF,gBAAgB1D,MAApB;AACA,YAAI,CAAC0D,aAAL,EAAoB;AAClB,gBAAMH,QAAQ,OAAKI,GAAL,CAASC,MAAT,CAAgB,iBAAhB,KAAsC,OAAKD,GAAL,CAASC,MAAT,CAAgB,iBAAhB,CAAtC,IAA4E,EAA1F;AACA,cAAIL,KAAJ,EAAW;AACT,kBAAMM,eAAe/F,MAAMsF,OAAN,CAAc,OAAd,EAAuB,KAAvB,CAArB;AACAM,4BAAgB,MAAMG,aAAaC,SAAb,CAAuBP,KAAvB,CAAtB;AACAxD,oBAAQU,GAAR,CAAY,iBAAZ,EAA+BiD,aAA/B;AACD;AACF;;AAED;AACA,cAAMK,eAAe;AACnBlD,kBAAQ,KADW;AAEnBC,eAAK,yCAFc;AAGnBC,cAAI;AACFC,wBAAY,mBADV;AAEFI,mBAAOtD,MAAMqD,MAAN,CAAa,cAAb,CAFL;AAGFD,oBAAQpD,MAAMqD,MAAN,CAAa,eAAb;AAHN;AAHe,SAArB;;AAUA,YAAI6C,gBAAgB,MAAMtG,GAAGqG,YAAH,CAA1B;AACAC,wBAAgB1C,KAAKC,KAAL,CAAWyC,aAAX,CAAhB;;AAEAjE,gBAAQU,GAAR,CAAY,mBAAZ,EAAiCuD,aAAjC;;AAEA,YAAI,CAACA,cAAcC,YAAnB,EAAiC;AAC/BlE,kBAAQU,GAAR,CAAY,mBAAZ,EAAiCuD,aAAjC;AACA,iBAAO,OAAK3F,IAAL,CAAU,GAAV,EAAe,kBAAf,CAAP;AACD;;AAED;AACA,cAAM6F,eAAe;AACnBrD,kBAAQ,MADW;AAEnBC,eAAK,2DAFc;AAGnBC,cAAI;AACFkD,0BAAcD,cAAcC;AAD1B,WAHe;AAMnBE,gBAAM;AACJ5D,kBAAMA;AADF,WANa;AASnB6D,gBAAM;AATa,SAArB;;AAYA,YAAIC,gBAAgB,MAAM3G,GAAGwG,YAAH,CAA1B;;AAEAnE,gBAAQU,GAAR,CAAY,UAAZ,EAAwB4D,aAAxB;;AAEA,YAAIA,cAAcC,OAAd,KAA0B,CAA9B,EAAiC;AAC/B;AACA,gBAAMC,YAAYF,cAAcG,UAAhC;AACAzE,kBAAQU,GAAR,CAAY,QAAZ,EAAsB8D,SAAtB;;AAEA;AACA,cAAIb,aAAJ,EAAmB;AACjB,gBAAI;AACF,oBAAMe,YAAY,OAAK9F,KAAL,CAAW,MAAX,CAAlB;AACA,oBAAM8F,UAAUvF,KAAV,CAAgB;AACpBO,oBAAIiE;AADgB,eAAhB,EAEHX,MAFG,CAEI;AACRL,wBAAQ6B,UAAUG,WADV;AAERC,4BAAY,IAAInG,IAAJ;AAFJ,eAFJ,CAAN;AAMAuB,sBAAQU,GAAR,CAAY,YAAZ,EAA0BiD,aAA1B,EAAyCa,UAAUG,WAAnD;AACD,aATD,CASE,OAAOE,WAAP,EAAoB;AACpB7E,sBAAQD,KAAR,CAAc,YAAd,EAA4B8E,WAA5B;AACA;AACD;AACF,WAdD,MAcO;AACL7E,oBAAQU,GAAR,CAAY,iBAAZ;AACD;;AAED,iBAAO,OAAKf,OAAL,CAAa;AAClBgF,yBAAaH,UAAUG,WADL;AAElBG,6BAAiBN,UAAUM,eAFT;AAGlBC,yBAAaP,UAAUO,WAHL;AAIlB9E,oBAAQ0D,aAJU,CAII;AAJJ,WAAb,CAAP;AAMD,SA9BD,MA8BO;AACL;AACA3D,kBAAQU,GAAR,CAAY,WAAZ,EAAyB4D,aAAzB;AACA,iBAAO,OAAKhG,IAAL,CAAUgG,cAAcC,OAAxB,EAAiCD,cAAcU,MAAd,IAAwB,SAAzD,CAAP;AACD;AAEF,OAlGD,CAkGE,OAAOjF,KAAP,EAAc;AACdC,gBAAQU,GAAR,CAAY,UAAZ,EAAwBX,KAAxB;AACA,eAAO,OAAKzB,IAAL,CAAU,GAAV,EAAe,SAAf,CAAP;AACD;AAtG0B;AAuG5B;;AAED;;;AAGMwE,8BAAN,CAAmC7C,MAAnC,EAA2C;AAAA;;AAAA;AACzC,UAAI;AACF;AACA,cAAMgF,mBAAmB,MAAM,OAAKrG,KAAL,CAAW,cAAX,EAA2BsG,KAA3B,CAAiC,IAAjC,EAC5BC,IAD4B,CACvB,0CADuB,EAE5BhG,KAF4B,CAEtB;AACL,wBAAcc,MADT;AAEL,oBAAU;AAFL,SAFsB,EAK1BmF,KAL0B,EAA/B;;AAOA,YAAIH,mBAAmB,CAAvB,EAA0B;AACxBjF,kBAAQU,GAAR,CAAY,gBAAZ;AACA;AACD;;AAED;AACA,cAAM2E,iBAAiB,MAAM,OAAKzG,KAAL,CAAW,SAAX,EAAsBO,KAAtB,CAA4B;AACvDmG,gBAAM,SADiD;AAEvDrG,kBAAQ,QAF+C;AAGvDsG,2BAAiB,CAHsC;AAIvDlG,qBAAW,CAJ4C;AAKvDmG,sBAAY,CAAC,IAAD,EAAO,IAAI/G,IAAJ,EAAP,CAL2C;AAMvDgH,oBAAU,CAAC,IAAD,EAAO,IAAIhH,IAAJ,EAAP;AAN6C,SAA5B,EAO1BiH,MAP0B,EAA7B;;AASA1F,gBAAQU,GAAR,CAAY,YAAZ,EAA0B2E,eAAetG,MAAzC,EAAiD,GAAjD;;AAEA;AACA,YAAIsG,eAAetG,MAAf,GAAwB,CAA5B,EAA+B;AAC7B,gBAAM4G,SAASN,eAAe,CAAf,CAAf,CAD6B,CACK;AAClC,cAAI;AACF;AACA,kBAAMO,aAAa,OAAKC,kBAAL,EAAnB;AACA,kBAAMC,WAAWH,OAAOI,UAAP,GACf,IAAItH,IAAJ,CAASA,KAAKC,GAAL,KAAaiH,OAAOI,UAAP,GAAoB,EAApB,GAAyB,EAAzB,GAA8B,EAA9B,GAAmC,IAAzD,CADe,GAEf,IAAItH,IAAJ,CAASkH,OAAOF,QAAhB,CAFF;;AAIA,kBAAM,OAAK7G,KAAL,CAAW,cAAX,EAA2BuD,GAA3B,CAA+B;AACnChC,uBAASF,MAD0B;AAEnC+F,yBAAWL,OAAOjG,EAFiB;AAGnCuG,2BAAaL,UAHsB;AAInCM,yBAAWJ,QAJwB;AAKnCK,sBAAQ;AAL2B,aAA/B,CAAN;;AAQAnG,oBAAQU,GAAR,CAAY,UAAZ,EAAwBiF,OAAOS,IAA/B,EAAqC,MAArC,EAA6CnG,MAA7C;AACD,WAhBD,CAgBE,OAAOF,KAAP,EAAc;AACdC,oBAAQU,GAAR,CAAY,UAAZ,EAAwBiF,OAAOS,IAA/B,EAAqCrG,KAArC;AACD;AACF;AACF,OAjDD,CAiDE,OAAOA,KAAP,EAAc;AACdC,gBAAQU,GAAR,CAAY,YAAZ,EAA0BX,KAA1B;AACA,cAAMA,KAAN;AACD;AArDwC;AAsD1C;;AAED;;;AAGA8F,uBAAqB;AACnB,UAAMQ,YAAY5H,KAAKC,GAAL,GAAWoD,QAAX,CAAoB,EAApB,CAAlB;AACA,UAAMwE,SAASC,KAAKD,MAAL,GAAcxE,QAAd,CAAuB,EAAvB,EAA2B0E,MAA3B,CAAkC,CAAlC,EAAqC,CAArC,CAAf;AACA,WAAQ,MAAKH,SAAU,GAAEC,MAAO,EAAzB,CAA2BG,WAA3B,EAAP;AACD;AA7b6C,CAAhD", "file": "..\\..\\..\\src\\api\\controller\\auth.js", "sourcesContent": ["const rp = require(\"request-promise\");\nmodule.exports = class extends think.Controller {\n\n  // 小程序扫码确认登录\n  async confirmQrLoginAction() {\n    try {\n      const qrToken = this.post('qrToken');\n      const openid = this.post('openid') || this.get('openid');\n\n      if (!qrToken) {\n        return this.fail('缺少二维码token');\n      }\n\n      if (!openid) {\n        return this.fail('用户未登录');\n      }\n\n      const currentTime = parseInt(Date.now() / 1000);\n\n      // 查询二维码token\n      const qrData = await this.model().query(`\n        SELECT * FROM hiolabs_qr_login_tokens WHERE token = '${qrToken}' LIMIT 1\n      `);\n      const qrRecord = qrData && qrData.length > 0 ? qrData[0] : null;\n\n      if (!qrRecord) {\n        return this.fail('无效的二维码');\n      }\n\n      // 检查是否过期\n      if (currentTime > qrRecord.expire_time) {\n        await this.model().query(`\n          UPDATE hiolabs_qr_login_tokens SET status = 'expired' WHERE token = '${qrToken}'\n        `);\n        return this.fail('二维码已过期，请刷新后重试');\n      }\n\n      // 检查是否已经被使用\n      if (qrRecord.status === 'success') {\n        return this.fail('二维码已被使用');\n      }\n\n      // 通过openid查找用户\n      const user = await this.model('user').where({\n        weixin_openid: openid,\n        is_delete: 0\n      }).find();\n\n      if (think.isEmpty(user)) {\n        return this.fail('用户不存在');\n      }\n\n      // 检查用户是否为推广员或分销商\n      const userType = await this.checkUserRole(user.id);\n      if (!userType) {\n        return this.fail('您还不是推广员或分销商，无法登录管理后台');\n      }\n\n      // 更新二维码状态\n      await this.model().query(`\n        UPDATE hiolabs_qr_login_tokens\n        SET status = 'success', user_id = ${user.id}, user_type = '${userType}',\n            scan_time = ${currentTime}, login_time = ${currentTime}\n        WHERE token = '${qrToken}'\n      `);\n\n      return this.success({\n        message: '登录确认成功，请在网页中查看',\n        userType: userType,\n        userName: user.nickname\n      });\n\n    } catch (error) {\n      console.error('扫码登录确认失败:', error);\n      return this.fail('登录确认失败，请重试');\n    }\n  }\n\n  // 检查用户角色\n  async checkUserRole(userId) {\n    try {\n      // 检查是否为个人推广员\n      const promoter = await this.model('personal_promoters').where({\n        user_id: userId,\n        status: 1\n      }).find();\n\n      if (!think.isEmpty(promoter)) {\n        return 'promoter';\n      }\n\n      // 检查是否为分销商\n      const distributor = await this.model('distributors').where({\n        user_id: userId,\n        is_active: 1,\n        audit_status: 1\n      }).find();\n\n      if (!think.isEmpty(distributor)) {\n        return 'distributor';\n      }\n\n      return null;\n    } catch (error) {\n      console.error('检查用户角色失败:', error);\n      return null;\n    }\n  }\n  async loginByWeixinAction() {\n    // 获取code和用户信息\n    const code = this.post(\"code\");\n    const userInfo = this.post(\"userInfo\"); // 获取前端传来的用户信息\n\n    // 添加调试日志\n    console.log('=== 授权登录调试信息 ===');\n    console.log('接收到的code:', code);\n    console.log('接收到的userInfo:', userInfo);\n    console.log('userInfo类型:', typeof userInfo);\n    console.log('userInfo是否为空:', think.isEmpty(userInfo));\n\n    let currentTime = parseInt(new Date().getTime() / 1000);\n    const clientIp = \"\"; // 暂时不记录 ip test git\n\n    // 获取openid\n    const options = {\n      method: \"GET\",\n      url: \"https://api.weixin.qq.com/sns/jscode2session\",\n      qs: {\n        grant_type: \"authorization_code\",\n        js_code: code,\n        secret: think.config(\"weixin.secret\"),\n        appid: think.config(\"weixin.appid\"),\n      },\n    };\n\n    let sessionData = await rp(options);\n    sessionData = JSON.parse(sessionData);\n    if (!sessionData.openid) {\n      return this.fail(\"登录失败，openid无效\");\n    }\n\n    // 根据openid查找用户是否已经注册\n    let userId = await this.model(\"user\")\n      .where({\n        weixin_openid: sessionData.openid,\n      })\n      .getField(\"id\", true);\n\n    let is_new = 0;\n\n    // 准备用户数据\n    // 默认昵称和头像\n    const buffer = Buffer.from('微信用户');\n    let nickname = buffer.toString(\"base64\");\n    let avatarUrl = '/images/icon/default_avatar_big.jpg';\n\n    // 如果前端传来了用户信息，则使用前端传来的昵称和头像\n    if (userInfo) {\n      if (userInfo.nickName) {\n        // 对昵称进行Base64编码存储\n        const nickBuffer = Buffer.from(userInfo.nickName);\n        nickname = nickBuffer.toString(\"base64\");\n      }\n\n      if (userInfo.avatarUrl) {\n        // 检查是否是wxfile://路径（小程序本地临时文件）\n        if (userInfo.avatarUrl.startsWith('wxfile://')) {\n          console.log('检测到wxfile://路径，保持原样存储:', userInfo.avatarUrl);\n          // 对于wxfile://路径，直接存储，前端可以正常显示\n          avatarUrl = userInfo.avatarUrl;\n        } else {\n          // 其他情况（如https://路径）直接存储\n          avatarUrl = userInfo.avatarUrl;\n        }\n        console.log('最终头像URL:', avatarUrl);\n      }\n    }\n\n    if (think.isEmpty(userId)) {\n      // 注册新用户\n      userId = await this.model(\"user\").add({\n        username: \"微信用户\" + think.uuid(6), // 保持原有username生成逻辑\n        password: sessionData.openid,\n        register_time: currentTime,\n        register_ip: clientIp,\n        last_login_time: currentTime,\n        last_login_ip: clientIp,\n        mobile: \"\",\n        weixin_openid: sessionData.openid,\n        nickname: nickname,\n        avatar: avatarUrl,\n        is_new_user: 1 // 标记为新用户\n      });\n      is_new = 1;\n\n      // 自动发放新人券\n      try {\n        await this.autoDistributeNewUserCoupons(userId);\n      } catch (error) {\n        console.log('自动发放新人券失败:', error);\n        // 不影响注册流程，只记录错误\n      }\n    } else if (userInfo) {\n      // 如果用户已存在且前端传来了用户信息，则更新用户的昵称和头像\n      console.log('=== 执行数据库更新 ===');\n      console.log('用户ID:', userId);\n      console.log('更新昵称:', nickname);\n      console.log('更新头像:', avatarUrl);\n\n      const updateResult = await this.model(\"user\")\n        .where({\n          id: userId,\n        })\n        .update({\n          nickname: nickname,\n          avatar: avatarUrl,\n          last_login_time: currentTime,\n          last_login_ip: clientIp,\n        });\n\n      console.log('数据库更新结果:', updateResult);\n    } else {\n      // 仅更新登录信息\n      console.log('=== 仅更新登录信息（未传入userInfo）===');\n      console.log('用户ID:', userId);\n\n      await this.model(\"user\")\n        .where({\n          id: userId,\n        })\n        .update({\n          last_login_time: currentTime,\n          last_login_ip: clientIp,\n        });\n    }\n\n    sessionData.user_id = userId;\n\n    const newUserInfo = await this.model(\"user\")\n      .field(\"id,username,nickname, avatar\")\n      .where({\n        id: userId,\n      })\n      .find();\n\n    newUserInfo.nickname = Buffer.from(\n      newUserInfo.nickname,\n      \"base64\"\n    ).toString();\n\n    // 添加授权标识字段\n    // 如果前端传来了userInfo，说明用户已授权；否则为静默登录（未授权）\n    newUserInfo.is_authorized = userInfo ? true : false;\n\n    const TokenSerivce = this.service(\"token\", \"api\");\n    const sessionKey = await TokenSerivce.create(sessionData);\n\n    if (think.isEmpty(newUserInfo) || think.isEmpty(sessionKey)) {\n      return this.fail(\"登录失败\");\n    }\n\n    return this.success({\n      token: sessionKey,\n      userInfo: newUserInfo,\n      is_new: is_new,\n      openid: sessionData.openid,\n    });\n  }\n  async logoutAction() {\n    return this.success();\n  }\n\n  /**\n   * 获取用户手机号\n   */\n  async getPhoneNumberAction() {\n    try {\n      // 获取前端传来的code和用户ID\n      const code = this.post(\"code\");\n      const userId = this.post(\"userId\"); // 前端需要传递用户ID\n\n      console.log('=== 获取手机号接口调用 ===');\n      console.log('接收到的code:', code);\n      console.log('用户ID:', userId);\n\n      if (!code) {\n        return this.fail(400, 'code参数必须提供');\n      }\n\n      // 如果没有传递userId，尝试从token获取\n      let currentUserId = userId;\n      if (!currentUserId) {\n        const token = this.ctx.header['x-hioshop-token'] || this.ctx.header['X-Hioshop-Token'] || '';\n        if (token) {\n          const tokenService = think.service('token', 'api');\n          currentUserId = await tokenService.getUserId(token);\n          console.log('从token获取到的用户ID:', currentUserId);\n        }\n      }\n\n      // 获取access_token\n      const tokenOptions = {\n        method: \"GET\",\n        url: \"https://api.weixin.qq.com/cgi-bin/token\",\n        qs: {\n          grant_type: \"client_credential\",\n          appid: think.config(\"weixin.appid\"),\n          secret: think.config(\"weixin.secret\"),\n        },\n      };\n\n      let tokenResponse = await rp(tokenOptions);\n      tokenResponse = JSON.parse(tokenResponse);\n\n      console.log('获取access_token响应:', tokenResponse);\n\n      if (!tokenResponse.access_token) {\n        console.log('获取access_token失败:', tokenResponse);\n        return this.fail(500, '获取access_token失败');\n      }\n\n      // 调用微信接口获取手机号\n      const phoneOptions = {\n        method: \"POST\",\n        url: \"https://api.weixin.qq.com/wxa/business/getuserphonenumber\",\n        qs: {\n          access_token: tokenResponse.access_token\n        },\n        body: {\n          code: code\n        },\n        json: true\n      };\n\n      let phoneResponse = await rp(phoneOptions);\n\n      console.log('获取手机号响应:', phoneResponse);\n\n      if (phoneResponse.errcode === 0) {\n        // 成功获取手机号\n        const phoneInfo = phoneResponse.phone_info;\n        console.log('手机号信息:', phoneInfo);\n\n        // 如果有用户ID，更新用户表中的手机号\n        if (currentUserId) {\n          try {\n            const userModel = this.model('user');\n            await userModel.where({\n              id: currentUserId\n            }).update({\n              mobile: phoneInfo.phoneNumber,\n              updated_at: new Date()\n            });\n            console.log('用户手机号更新成功:', currentUserId, phoneInfo.phoneNumber);\n          } catch (updateError) {\n            console.error('更新用户手机号失败:', updateError);\n            // 不影响手机号返回，只记录错误\n          }\n        } else {\n          console.log('未找到用户ID，跳过手机号更新');\n        }\n\n        return this.success({\n          phoneNumber: phoneInfo.phoneNumber,\n          purePhoneNumber: phoneInfo.purePhoneNumber,\n          countryCode: phoneInfo.countryCode,\n          userId: currentUserId // 返回用户ID供前端使用\n        });\n      } else {\n        // 获取手机号失败\n        console.log('微信接口返回错误:', phoneResponse);\n        return this.fail(phoneResponse.errcode, phoneResponse.errmsg || '获取手机号失败');\n      }\n\n    } catch (error) {\n      console.log('获取手机号异常:', error);\n      return this.fail(500, '服务器内部错误');\n    }\n  }\n\n  /**\n   * 自动发放新人券\n   */\n  async autoDistributeNewUserCoupons(userId) {\n    try {\n      // 首先检查用户是否已经有新人券\n      const hasNewUserCoupon = await this.model('user_coupons').alias('uc')\n        .join('hiolabs_coupons c ON uc.coupon_id = c.id')\n        .where({\n          'uc.user_id': userId,\n          'c.type': 'newuser'\n        }).count();\n\n      if (hasNewUserCoupon > 0) {\n        console.log('用户已有新人券，跳过自动发放');\n        return;\n      }\n\n      // 获取所有自动发放的新人券\n      const newUserCoupons = await this.model('coupons').where({\n        type: 'newuser',\n        status: 'active',\n        auto_distribute: 1,\n        is_delete: 0,\n        start_time: ['<=', new Date()],\n        end_time: ['>=', new Date()]\n      }).select();\n\n      console.log('找到自动发放新人券:', newUserCoupons.length, '张');\n\n      // 只发放第一张可用的新人券（每人限一张）\n      if (newUserCoupons.length > 0) {\n        const coupon = newUserCoupons[0]; // 选择第一张\n        try {\n          // 生成优惠券码\n          const couponCode = this.generateCouponCode();\n          const expireAt = coupon.valid_days ?\n            new Date(Date.now() + coupon.valid_days * 24 * 60 * 60 * 1000) :\n            new Date(coupon.end_time);\n\n          await this.model('user_coupons').add({\n            user_id: userId,\n            coupon_id: coupon.id,\n            coupon_code: couponCode,\n            expire_at: expireAt,\n            source: 'auto'\n          });\n\n          console.log('成功发放新人券:', coupon.name, '给用户:', userId);\n        } catch (error) {\n          console.log('发放新人券失败:', coupon.name, error);\n        }\n      }\n    } catch (error) {\n      console.log('自动发放新人券异常:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 生成优惠券码\n   */\n  generateCouponCode() {\n    const timestamp = Date.now().toString(36);\n    const random = Math.random().toString(36).substr(2, 5);\n    return `CPN${timestamp}${random}`.toUpperCase();\n  }\n};\n"]}