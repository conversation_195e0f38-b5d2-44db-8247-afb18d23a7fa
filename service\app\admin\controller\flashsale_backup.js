function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

const Base = require('./base.js');
const moment = require('moment');

module.exports = class extends Base {

  /**
   * 获取秒杀统计数据
   * GET /admin/flashsale/statistics
   */
  statisticsAction() {
    var _this = this;

    return _asyncToGenerator(function* () {
      try {
        console.log('=== 获取秒杀统计数据 ===');

        const flashSaleModel = _this.model('flash_sales');
        const orderModel = _this.model('flash_sale_orders');

        // 获取总秒杀活动数
        const totalFlashSales = yield flashSaleModel.count();

        // 获取进行中的秒杀活动数
        const activeFlashSales = yield flashSaleModel.where({
          status: 'active'
        }).count();

        // 获取秒杀订单数
        const flashSaleOrders = yield orderModel.count();

        // 获取秒杀销售额
        const salesResult = yield orderModel.sum('total_amount');
        const flashSaleSales = salesResult || 0;

        const statistics = {
          totalFlashSales,
          activeFlashSales,
          flashSaleOrders,
          flashSaleSales: flashSaleSales.toFixed(2)
        };

        console.log('统计数据:', statistics);
        return _this.success(statistics);
      } catch (error) {
        console.error('获取统计数据失败:', error);
        return _this.fail('获取统计数据失败');
      }
    })();
  }

  /**
   * 获取秒杀日期列表
   * GET /admin/flashsale/dates
   */
  datesAction() {
    var _this2 = this;

    return _asyncToGenerator(function* () {
      try {
        console.log('=== 获取秒杀日期列表 ===');

        const flashSaleDateModel = _this2.model('flash_sale_dates');
        const flashSaleModel = _this2.model('flash_sales');

        // 自动更新过期日期状态
        yield flashSaleDateModel.updateExpiredDates();

        // 获取所有启用的日期
        const flashSaleDates = yield flashSaleDateModel.where({
          is_active: 1
        }).order('sort_order ASC').select();

        // 为每个日期统计商品数量
        for (const flashSaleDate of flashSaleDates) {
          const productCount = yield flashSaleModel.where({
            sale_date_id: flashSaleDate.id,
            status: ['IN', ['upcoming', 'active']]
          }).count();

          flashSaleDate.productCount = productCount;

          // 格式化日期显示
          const saleDate = new Date(flashSaleDate.sale_date);
          const today = new Date();
          const tomorrow = new Date(today);
          tomorrow.setDate(today.getDate() + 1);

          if (saleDate.toDateString() === today.toDateString()) {
            flashSaleDate.dateLabel = '今天';
          } else if (saleDate.toDateString() === tomorrow.toDateString()) {
            flashSaleDate.dateLabel = '明天';
          } else {
            flashSaleDate.dateLabel = saleDate.toLocaleDateString('zh-CN', {
              month: 'short',
              day: 'numeric'
            });
          }

          flashSaleDate.weekday = saleDate.toLocaleDateString('zh-CN', {
            weekday: 'short'
          });
        }

        console.log('日期列表:', flashSaleDates);
        return _this2.success(flashSaleDates);
      } catch (error) {
        console.error('获取日期列表失败:', error);
        return _this2.fail('获取日期列表失败');
      }
    })();
  }

  /**
   * 获取秒杀商品列表
   * GET /admin/flashsale/products
   */
  productsAction() {
    var _this3 = this;

    return _asyncToGenerator(function* () {
      try {
        console.log('=== 获取秒杀商品列表 ===');

        const search = _this3.get('search') || '';
        const status = _this3.get('status') || '';
        const category = _this3.get('category') || '';
        const saleDateId = _this3.get('saleDateId') || '';

        const flashSaleModel = _this3.model('flash_sales');
        const goodsModel = _this3.model('goods');
        const categoryModel = _this3.model('category');

        let where = {};

        // 状态筛选
        if (status) {
          where.status = status;
        }

        // 日期筛选
        if (saleDateId) {
          where.sale_date_id = saleDateId;
        }

        // 获取秒杀活动列表
        const flashSales = yield flashSaleModel.where(where).order('created_at DESC').select();

        const products = [];

        // 为每个秒杀活动获取商品信息
        for (const flashSale of flashSales) {
          const goods = yield goodsModel.where({
            id: flashSale.goods_id
          }).find();

          if (!think.isEmpty(goods)) {
            // 搜索过滤
            if (search && !goods.name.toLowerCase().includes(search.toLowerCase())) {
              continue;
            }

            // 获取分类信息
            let categoryName = '';
            if (goods.category_id) {
              const categoryInfo = yield categoryModel.where({
                id: goods.category_id
              }).find();
              if (!think.isEmpty(categoryInfo)) {
                categoryName = categoryInfo.name;
              }
            }

            // 分类过滤
            if (category && categoryName !== category) {
              continue;
            }

            const product = {
              id: flashSale.id,
              goodsId: goods.id,
              name: goods.name,
              originalPrice: goods.retail_price,
              flashPrice: flashSale.flash_price,
              stock: flashSale.stock,
              soldCount: flashSale.sold_count,
              limitQuantity: flashSale.limit_quantity,
              status: flashSale.status,
              startTime: flashSale.start_time,
              endTime: flashSale.end_time,
              saleDateId: flashSale.sale_date_id,
              categoryName: categoryName,
              image: goods.list_pic_url || goods.primary_pic_url || '',
              createdAt: flashSale.created_at
            };

            products.push(product);
          }
        }

        console.log('商品列表:', products.length, '个商品');
        return _this3.success(products);
      } catch (error) {
        console.error('获取商品列表失败:', error);
        return _this3.fail('获取商品列表失败');
      }
    })();
  }

  /**
   * 创建秒杀活动
   * POST /admin/flashsale/create
   */
  createAction() {
    var _this4 = this;

    return _asyncToGenerator(function* () {
      try {
        console.log('=== 创建秒杀活动 ===');

        const data = _this4.post();
        console.log('创建数据:', data);

        // 验证必填字段
        if (!data.productId || !data.saleDateId || !data.flashPrice || !data.stock) {
          return _this4.fail('请填写完整的秒杀信息');
        }

        // 验证时间
        if (!data.startTime || !data.endTime) {
          return _this4.fail('请设置活动时间');
        }

        if (new Date(data.startTime) >= new Date(data.endTime)) {
          return _this4.fail('开始时间必须早于结束时间');
        }

        const flashSaleModel = _this4.model('flash_sales');

        // 检查商品是否已经在同日期有秒杀活动
        const existingFlashSale = yield flashSaleModel.where({
          goods_id: data.productId,
          sale_date_id: data.saleDateId,
          status: ['IN', ['upcoming', 'active']]
        }).find();

        if (!think.isEmpty(existingFlashSale)) {
          return _this4.fail('该商品在此日期已有秒杀活动');
        }

        // 创建秒杀活动
        const flashSaleData = {
          goods_id: data.productId,
          sale_date_id: data.saleDateId,
          flash_price: data.flashPrice,
          stock: data.stock,
          limit_quantity: data.limitQuantity || 1,
          start_time: data.startTime,
          end_time: data.endTime,
          status: 'upcoming'
        };

        const result = yield flashSaleModel.add(flashSaleData);

        if (result) {
          console.log('秒杀活动创建成功，ID:', result);
          return _this4.success({ id: result });
        } else {
          return _this4.fail('创建失败');
        }
      } catch (error) {
        console.error('创建秒杀活动失败:', error);
        return _this4.fail('创建失败: ' + error.message);
      }
    })();
  }
};