/* 签到页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFF5EE 0%, #FFF8F0 100%);
  padding-bottom: 40rpx;
}

/* 签到状态区 */
.status-section {
  margin: 40rpx 30rpx;
}

.status-card {
  background: linear-gradient(135deg, rgba(255, 140, 0, 0.1) 0%, rgba(255, 140, 0, 0.05) 100%);
  border-radius: 24rpx;
  padding: 40rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(255, 140, 0, 0.1);
}

.status-content {
  text-align: center;
}

.consecutive-days {
  font-size: 32rpx;
  color: #4A4A4A;
  margin-bottom: 20rpx;
}

.total-points {
  font-size: 28rpx;
  color: #4A4A4A;
  margin-bottom: 20rpx;
}

.highlight {
  color: #FF8C00;
  font-weight: 700;
  font-size: 36rpx;
}

.today-status {
  background: rgba(255, 255, 255, 0.8);
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  color: rgba(139, 69, 19, 0.8);
  display: inline-block;
  margin-top: 10rpx;
}

.today-status.signed {
  color: #FF8C00;
}

/* 日历区域 */
.calendar-section {
  margin: 0 30rpx 40rpx;
}

.calendar-card {
  background: white;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.month-nav {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8B4513;
  font-size: 32rpx;
}

.nav-arrow {
  font-size: 40rpx;
  font-weight: bold;
}

.current-month {
  font-size: 32rpx;
  font-weight: 600;
  color: #4A4A4A;
}

.week-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  margin-bottom: 16rpx;
  background: rgba(255, 140, 0, 0.05);
  border-radius: 16rpx;
  padding: 16rpx 8rpx;
}

.week-day {
  text-align: center;
  font-size: 24rpx;
  color: #8B4513;
  font-weight: 500;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  background: white;
  border: 2rpx solid #f0f0f0;
  position: relative;
  transition: all 0.3s ease;
}

.calendar-day.today {
  background: #FF8C00;
  color: white;
  font-weight: bold;
  box-shadow: 0 6rpx 20rpx rgba(255, 140, 0, 0.3);
}

.calendar-day.signed {
  background: rgba(255, 140, 0, 0.1);
  border-color: rgba(255, 140, 0, 0.3);
}

.calendar-day.clickable {
  background: rgba(255, 140, 0, 0.05);
  border-color: rgba(255, 140, 0, 0.2);
}

/* 可签到日期显示红色 */
.calendar-day.can-sign {
  background: rgba(255, 52, 86, 0.1) !important;
  border-color: #ff3456 !important;
  color: #ff3456 !important;
  font-weight: bold !important;
}

.calendar-day.can-sign .day-number {
  color: #ff3456 !important;
  font-weight: bold !important;
}

.calendar-day.future {
  color: #ccc;
  background: #f8f8f8;
}

.calendar-day.empty {
  background: transparent;
  border: none;
}

.day-number {
  font-size: 28rpx;
}

.signed-mark {
  width: 32rpx;
  height: 32rpx;
  background: rgba(255, 140, 0, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 4rpx;
}

.check-icon {
  font-size: 20rpx;
  color: #FF8C00;
  font-weight: bold;
}

/* 签到动画 */
.calendar-day.signing {
  animation: signAnimation 0.8s ease-out;
}

@keyframes signAnimation {
  0% { 
    transform: scale(1); 
    box-shadow: 0 0 0 0 rgba(255, 140, 0, 0.7); 
  }
  50% { 
    transform: scale(1.1); 
    box-shadow: 0 0 0 20rpx rgba(255, 140, 0, 0); 
  }
  100% { 
    transform: scale(1); 
    box-shadow: 0 0 0 0 rgba(255, 140, 0, 0); 
  }
}

/* 规则说明 */
.rules-section {
  margin: 0 30rpx 40rpx;
}

.rules-card {
  background: white;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.rules-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #8B4513;
  margin-bottom: 24rpx;
}

.rules-list {
  space-y: 16rpx;
}

.rule-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.rule-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FF8C00;
  font-size: 20rpx;
  font-weight: bold;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.rule-text {
  flex: 1;
  font-size: 26rpx;
  color: #4A4A4A;
  line-height: 1.5;
}

/* 积分用途 */
.usage-section {
  margin: 0 30rpx;
}

.usage-card {
  background: white;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
}

.usage-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #8B4513;
  margin-bottom: 24rpx;
}

.usage-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.usage-item {
  background: rgba(255, 140, 0, 0.1);
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.usage-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.usage-text {
  font-size: 22rpx;
  color: #4A4A4A;
}

/* 成功弹窗 */
.success-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background: white;
  border-radius: 24rpx;
  padding: 60rpx;
  width: 80%;
  max-width: 600rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1001;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 140, 0, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #FF8C00;
  margin-bottom: 30rpx;
}

.success-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #4A4A4A;
  margin-bottom: 20rpx;
}

.success-message {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
}

.earned-points {
  color: #FF8C00;
  font-weight: bold;
}

.success-btn {
  background: #FF8C00;
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.success-btn::after {
  border: none;
}
