{"version": 3, "sources": ["..\\..\\..\\src\\api\\model\\region.js"], "names": ["_", "require", "module", "exports", "think", "Model", "getFullRegionName", "provinceId", "cityId", "districtId", "isFullRegion", "checkFullRegion", "regionList", "limit", "order", "where", "id", "select", "isEmpty", "length", "flatMap", "join", "get", "getRegionName", "regionId", "getField", "getRegionList", "parentId", "parent_id", "getRegionInfo", "find"], "mappings": ";;AAAA,MAAMA,IAAIC,QAAQ,QAAR,CAAV;AACAC,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;AACvC;;;;;;;AAOMC,qBAAN,CAAwBC,UAAxB,EAAoCC,MAApC,EAA4CC,UAA5C,EAAwD;AAAA;;AAAA;AACpD,kBAAMC,eAAe,MAAM,MAAKC,eAAL,CAAqBJ,UAArB,EAAiCC,MAAjC,EAAyCC,UAAzC,CAA3B;AACA,gBAAI,CAACC,YAAL,EAAmB;AACf,uBAAO,EAAP;AACH;AACD,kBAAME,aAAa,MAAM,MAAKC,KAAL,CAAW,CAAX,EAAcC,KAAd,CAAoB;AACzC,sBAAM;AADmC,aAApB,EAEtBC,KAFsB,CAEhB;AACLC,oBAAI;AACA,0BAAM,CAACT,UAAD,EAAaC,MAAb,EAAqBC,UAArB;AADN;AADC,aAFgB,EAMtBQ,MANsB,EAAzB;AAOA,gBAAIb,MAAMc,OAAN,CAAcN,UAAd,KAA6BA,WAAWO,MAAX,KAAsB,CAAvD,EAA0D;AACtD,uBAAO,EAAP;AACH;AACD,mBAAOnB,EAAEoB,OAAF,CAAUR,UAAV,EAAsB,MAAtB,EAA8BS,IAA9B,CAAmC,EAAnC,CAAP;AAfoD;AAgBvD;AACD;;;;;;;AAOMV,mBAAN,CAAsBJ,UAAtB,EAAkCC,MAAlC,EAA0CC,UAA1C,EAAsD;AAAA;;AAAA;AAClD,gBAAIL,MAAMc,OAAN,CAAcX,UAAd,KAA6BH,MAAMc,OAAN,CAAcV,MAAd,CAA7B,IAAsDJ,MAAMc,OAAN,CAAcT,UAAd,CAA1D,EAAqF;AACjF,uBAAO,KAAP;AACH;AACD,kBAAMG,aAAa,MAAM,OAAKC,KAAL,CAAW,CAAX,EAAcC,KAAd,CAAoB;AACzC,sBAAM;AADmC,aAApB,EAEtBC,KAFsB,CAEhB;AACLC,oBAAI;AACA,0BAAM,CAACT,UAAD,EAAaC,MAAb,EAAqBC,UAArB;AADN;AADC,aAFgB,EAMtBQ,MANsB,EAAzB;AAOA,gBAAIb,MAAMc,OAAN,CAAcN,UAAd,KAA6BA,WAAWO,MAAX,KAAsB,CAAvD,EAA0D;AACtD,uBAAO,KAAP;AACH;AACD;AACA,gBAAInB,EAAEsB,GAAF,CAAMV,UAAN,EAAkB,CAAC,GAAD,EAAM,IAAN,CAAlB,MAAmCZ,EAAEsB,GAAF,CAAMV,UAAN,EAAkB,CAAC,GAAD,EAAM,WAAN,CAAlB,CAAvC,EAA8E;AAC1E,uBAAO,KAAP;AACH;AACD,gBAAIZ,EAAEsB,GAAF,CAAMV,UAAN,EAAkB,CAAC,GAAD,EAAM,IAAN,CAAlB,MAAmCZ,EAAEsB,GAAF,CAAMV,UAAN,EAAkB,CAAC,GAAD,EAAM,WAAN,CAAlB,CAAvC,EAA8E;AAC1E,uBAAO,KAAP;AACH;AACD,mBAAO,IAAP;AArBkD;AAsBrD;AACD;;;;;AAKMW,iBAAN,CAAoBC,QAApB,EAA8B;AAAA;;AAAA;AAC1B,mBAAO,OAAKT,KAAL,CAAW;AACdC,oBAAIQ;AADU,aAAX,EAEJC,QAFI,CAEK,MAFL,EAEa,IAFb,CAAP;AAD0B;AAI7B;AACD;;;;;AAKMC,iBAAN,CAAoBC,QAApB,EAA8B;AAAA;;AAAA;AAC1B,mBAAO,OAAKZ,KAAL,CAAW;AACda,2BAAWD;AADG,aAAX,EAEJV,MAFI,EAAP;AAD0B;AAI7B;AACD;;;;;AAKMY,iBAAN,CAAoBL,QAApB,EAA8B;AAAA;;AAAA;AAC1B,mBAAO,OAAKT,KAAL,CAAW;AACdC,oBAAIQ;AADU,aAAX,EAEJM,IAFI,EAAP;AAD0B;AAI7B;AApFsC,CAA3C", "file": "..\\..\\..\\src\\api\\model\\region.js", "sourcesContent": ["const _ = require('lodash');\nmodule.exports = class extends think.Model {\n    /**\n     * 获取完整的省市区名称组成的字符串\n     * @param provinceId\n     * @param cityId\n     * @param districtId\n     * @returns {Promise.<*>}\n     */\n    async getFullRegionName(provinceId, cityId, districtId) {\n        const isFullRegion = await this.checkFullRegion(provinceId, cityId, districtId);\n        if (!isFullRegion) {\n            return '';\n        }\n        const regionList = await this.limit(3).order({\n            'id': 'asc'\n        }).where({\n            id: {\n                'in': [provinceId, cityId, districtId]\n            }\n        }).select();\n        if (think.isEmpty(regionList) || regionList.length !== 3) {\n            return '';\n        }\n        return _.flatMap(regionList, 'name').join('');\n    }\n    /**\n     * 检查省市区信息是否完整和正确\n     * @param provinceId\n     * @param cityId\n     * @param districtId\n     * @returns {Promise.<boolean>}\n     */\n    async checkFullRegion(provinceId, cityId, districtId) {\n        if (think.isEmpty(provinceId) || think.isEmpty(cityId) || think.isEmpty(districtId)) {\n            return false;\n        }\n        const regionList = await this.limit(3).order({\n            'id': 'asc'\n        }).where({\n            id: {\n                'in': [provinceId, cityId, districtId]\n            }\n        }).select();\n        if (think.isEmpty(regionList) || regionList.length !== 3) {\n            return false;\n        }\n        // 上下级关系检查\n        if (_.get(regionList, ['0', 'id']) !== _.get(regionList, ['1', 'parent_id'])) {\n            return false;\n        }\n        if (_.get(regionList, ['1', 'id']) !== _.get(regionList, ['2', 'parent_id'])) {\n            return false;\n        }\n        return true;\n    }\n    /**\n     * 获取区域的名称\n     * @param regionId\n     * @returns {Promise.<*>}\n     */\n    async getRegionName(regionId) {\n        return this.where({\n            id: regionId\n        }).getField('name', true);\n    }\n    /**\n     * 获取下级的地区列表\n     * @param parentId\n     * @returns {Promise.<*>}\n     */\n    async getRegionList(parentId) {\n        return this.where({\n            parent_id: parentId\n        }).select();\n    }\n    /**\n     * 获取区域的信息\n     * @param regionId\n     * @returns {Promise.<*>}\n     */\n    async getRegionInfo(regionId) {\n        return this.where({\n            id: regionId\n        }).find();\n    }\n};"]}