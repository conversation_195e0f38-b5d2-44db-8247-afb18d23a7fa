const Application = require('thinkjs');
const path = require('path');

// 初始化ThinkJS应用
const instance = new Application({
  ROOT_PATH: path.join(__dirname, '../'),
  APP_PATH: path.join(__dirname, '../src'),
  proxy: true, // use proxy
  env: 'development'
});

async function testQuery() {
  try {
    console.log('=== 初始化ThinkJS ===');
    
    // 启动应用
    await instance.ready();
    
    console.log('=== 测试模型查询 ===');
    
    // 获取模型实例
    const roundModel = think.model('flash_sale_rounds', {}, 'common');
    const roundGoodsModel = think.model('flash_sale_round_goods', {}, 'common');
    
    console.log('模型实例创建成功');
    
    // 测试基本查询
    console.log('\n1. 测试基本查询...');
    const allRounds = await roundModel.select();
    console.log('所有轮次数量:', allRounds.length);
    
    // 测试分页查询
    console.log('\n2. 测试分页查询...');
    const pagedRounds = await roundModel
      .order('round_number DESC')
      .page(1, 20)
      .countSelect();
    console.log('分页查询结果:', {
      count: pagedRounds.count,
      totalPages: pagedRounds.totalPages,
      dataLength: pagedRounds.data ? pagedRounds.data.length : 0
    });
    
    if (pagedRounds.data && pagedRounds.data.length > 0) {
      console.log('第一个轮次:', pagedRounds.data[0]);
      
      // 测试轮次商品查询
      console.log('\n3. 测试轮次商品查询...');
      const firstRoundId = pagedRounds.data[0].id;
      const goods = await roundGoodsModel.where({ round_id: firstRoundId }).select();
      console.log(`轮次 ${firstRoundId} 的商品数量:`, goods.length);
    }
    
    console.log('\n✅ 所有查询测试完成');
    
  } catch (error) {
    console.error('❌ 查询测试失败:', error);
    console.error('错误堆栈:', error.stack);
  } finally {
    process.exit(0);
  }
}

testQuery();
