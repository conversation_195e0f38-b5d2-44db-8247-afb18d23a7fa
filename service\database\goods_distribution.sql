-- 商品分销配置表
CREATE TABLE IF NOT EXISTS `hiolabs_goods_distribution` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_id` int(10) unsigned NOT NULL COMMENT '商品ID',
  `is_distributed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已分销 0:未分销 1:已分销',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '佣金比例（百分比）',
  `commission_type` varchar(20) NOT NULL DEFAULT 'default' COMMENT '佣金类型 default:默认规则 custom:自定义',
  `min_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最低佣金金额',
  `max_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最高佣金金额',
  `distributor_level` tinyint(3) NOT NULL DEFAULT '1' COMMENT '分销商等级要求',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0:禁用 1:启用',
  `start_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '分销开始时间',
  `end_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '分销结束时间',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `goods_id` (`goods_id`),
  KEY `is_distributed` (`is_distributed`),
  KEY `commission_rate` (`commission_rate`),
  KEY `is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分销配置表';

-- 佣金规则配置表
CREATE TABLE IF NOT EXISTS `hiolabs_commission_rules` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `min_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最低价格',
  `max_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最高价格',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '佣金比例（百分比）',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认规则',
  `sort_order` int(10) NOT NULL DEFAULT '0' COMMENT '排序',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `min_price` (`min_price`),
  KEY `max_price` (`max_price`),
  KEY `is_active` (`is_active`),
  KEY `sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='佣金规则配置表';

-- 插入默认佣金规则
INSERT INTO `hiolabs_commission_rules` (`rule_name`, `min_price`, `max_price`, `commission_rate`, `is_default`, `sort_order`, `is_active`, `add_time`, `update_time`) VALUES
('低价商品佣金', 0.00, 100.00, 8.00, 1, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('中价商品佣金', 100.00, 500.00, 10.00, 1, 2, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('高价商品佣金', 500.00, 999999.00, 15.00, 1, 3, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 分销商等级表
CREATE TABLE IF NOT EXISTS `hiolabs_distributor_levels` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `level_name` varchar(50) NOT NULL COMMENT '等级名称',
  `level_code` varchar(20) NOT NULL COMMENT '等级代码',
  `min_sales` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最低销售额要求',
  `min_orders` int(10) NOT NULL DEFAULT '0' COMMENT '最低订单数要求',
  `commission_bonus` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '佣金加成比例',
  `sort_order` int(10) NOT NULL DEFAULT '0' COMMENT '排序',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `level_code` (`level_code`),
  KEY `sort_order` (`sort_order`),
  KEY `is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销商等级表';

-- 插入默认分销商等级
INSERT INTO `hiolabs_distributor_levels` (`level_name`, `level_code`, `min_sales`, `min_orders`, `commission_bonus`, `sort_order`, `is_active`, `add_time`, `update_time`) VALUES
('普通分销商', 'normal', 0.00, 0, 0.00, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('银牌分销商', 'silver', 1000.00, 10, 2.00, 2, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('金牌分销商', 'gold', 5000.00, 50, 5.00, 3, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('钻石分销商', 'diamond', 10000.00, 100, 10.00, 4, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 分销商信息表
CREATE TABLE IF NOT EXISTS `hiolabs_distributors` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `distributor_code` varchar(32) NOT NULL COMMENT '分销商编码',
  `level_id` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '分销商等级ID',
  `parent_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '上级分销商ID',
  `total_sales` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '累计销售额',
  `total_orders` int(10) NOT NULL DEFAULT '0' COMMENT '累计订单数',
  `total_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '累计佣金',
  `available_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '可提现佣金',
  `frozen_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '冻结佣金',
  `customer_count` int(10) NOT NULL DEFAULT '0' COMMENT '客户数量',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `audit_status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '审核状态 0:待审核 1:已通过 2:已拒绝',
  `join_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '加入时间',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  UNIQUE KEY `distributor_code` (`distributor_code`),
  KEY `level_id` (`level_id`),
  KEY `parent_id` (`parent_id`),
  KEY `is_active` (`is_active`),
  KEY `audit_status` (`audit_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销商信息表';

-- 招募规则配置表
CREATE TABLE IF NOT EXISTS `hiolabs_recruitment_rules` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `join_condition` varchar(20) NOT NULL DEFAULT 'with_conditions' COMMENT '加入条件类型 with_conditions:有条件 no_conditions:无条件',
  `conditions` text COMMENT '加入条件配置JSON',
  `application_method` varchar(20) NOT NULL DEFAULT 'manual_apply' COMMENT '申请方式 manual_apply:手动申请 auto_apply:自动申请',
  `require_application_info` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否需要填写申请信息',
  `audit_method` varchar(20) NOT NULL DEFAULT 'manual_audit' COMMENT '审核方式 manual_audit:人工审核 auto_audit:自动审核',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='招募规则配置表';

-- 插入默认招募规则
INSERT INTO `hiolabs_recruitment_rules` (`join_condition`, `conditions`, `application_method`, `require_application_info`, `audit_method`, `is_active`, `add_time`, `update_time`) VALUES
('with_conditions', '{"requirePurchase":false,"requireAmount":true,"minAmount":99.00,"requireOrders":false,"minOrders":1}', 'manual_apply', 0, 'auto_audit', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
