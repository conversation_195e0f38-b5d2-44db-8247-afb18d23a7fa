<!--pages/ucenter/coupons/index.wxml-->
<view class="container">
  <!-- 标签页 -->
  <view class="tabs">
    <view class="tab-item {{activeTab === 'unused' ? 'active' : ''}}" 
          bindtap="switchTab" 
          data-tab="unused">
      <text>未使用</text>
      <view class="count" wx:if="{{coupons.unused.length > 0}}">{{coupons.unused.length}}</view>
    </view>
    <view class="tab-item {{activeTab === 'used' ? 'active' : ''}}" 
          bindtap="switchTab" 
          data-tab="used">
      <text>已使用</text>
      <view class="count" wx:if="{{coupons.used.length > 0}}">{{coupons.used.length}}</view>
    </view>
    <view class="tab-item {{activeTab === 'expired' ? 'active' : ''}}" 
          bindtap="switchTab" 
          data-tab="expired">
      <text>已过期</text>
      <view class="count" wx:if="{{coupons.expired.length > 0}}">{{coupons.expired.length}}</view>
    </view>
  </view>

  <!-- 优惠券列表 -->
  <scroll-view class="coupon-list" scroll-y enhanced>
    <!-- 加载状态 -->
    <view class="loading" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>

    <!-- 优惠券项 -->
    <view class="coupon-item {{activeTab}} {{item.type}}"
          wx:for="{{coupons[activeTab]}}"
          wx:key="id">
      
      <view class="coupon-content">
        <!-- 左侧优惠信息 -->
        <view class="coupon-left">
          <view class="discount-amount">
            <text class="symbol">¥</text>
            <text class="amount">{{item.discount_value}}</text>
          </view>
          <view class="condition">{{getConditionText(item)}}</view>
        </view>

        <!-- 右侧详细信息 -->
        <view class="coupon-right">
          <view class="coupon-name">{{item.name}}</view>
          <view class="coupon-type">{{getTypeLabel(item.type)}}</view>
          
          <!-- 时间信息 -->
          <view class="time-info">
            <text wx:if="{{activeTab === 'unused'}}">
              有效期至：{{formatTime(item.expire_at)}}
            </text>
            <text wx:elif="{{activeTab === 'used'}}">
              使用时间：{{formatTime(item.used_at)}}
            </text>
            <text wx:else>
              过期时间：{{formatTime(item.expire_at)}}
            </text>
          </view>

          <!-- 操作按钮 -->
          <view class="coupon-actions" wx:if="{{activeTab === 'unused'}}">
            <button class="use-btn"
                    catchtap="useCoupon"
                    data-coupon="{{item}}">
              立即使用
            </button>
          </view>
        </view>
      </view>

      <!-- 状态标识 -->
      <view class="status-badge {{activeTab}}">
        <text wx:if="{{activeTab === 'used'}}">已使用</text>
        <text wx:elif="{{activeTab === 'expired'}}">已过期</text>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && coupons[activeTab].length === 0}}">
      <image class="empty-icon" src="/images/icon/empty-coupon.png" mode="aspectFit"></image>
      <text class="empty-text">
        <text wx:if="{{activeTab === 'unused'}}">暂无可用优惠券</text>
        <text wx:elif="{{activeTab === 'used'}}">暂无已使用优惠券</text>
        <text wx:else>暂无过期优惠券</text>
      </text>
      <button class="go-get-btn" wx:if="{{activeTab === 'unused'}}" bindtap="goGetCoupons">
        去领取优惠券
      </button>
    </view>
  </scroll-view>
</view>
