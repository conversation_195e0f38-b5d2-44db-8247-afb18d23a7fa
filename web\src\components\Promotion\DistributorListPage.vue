<template>
  <div class="distributor-list-page">
    <!-- 页面头部 -->
    <div class="page-header bg-white shadow-sm border-b">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">分销员列表</h1>
            <p class="text-sm text-gray-500 mt-1">查看和管理所有分销员信息及排行榜</p>
          </div>
          <div class="flex items-center space-x-3">
            <button @click="exportData" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <i class="ri-download-line mr-2"></i>
              导出数据
            </button>
            <button @click="refreshData" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <i class="ri-refresh-line mr-2"></i>
              刷新数据
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="p-6">
      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="ri-team-line text-2xl text-blue-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">总分销员</p>
              <p class="text-2xl font-semibold text-gray-900">{{ statistics.totalDistributors }}</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="ri-vip-crown-line text-2xl text-green-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">团长数量</p>
              <p class="text-2xl font-semibold text-gray-900">{{ statistics.teamLeaders }}</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <i class="ri-money-dollar-circle-line text-2xl text-yellow-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">总佣金</p>
              <p class="text-2xl font-semibold text-gray-900">¥{{ statistics.totalCommission }}</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <i class="ri-shopping-cart-line text-2xl text-purple-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">总销售额</p>
              <p class="text-2xl font-semibold text-gray-900">¥{{ statistics.totalSales }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <div class="bg-white rounded-lg shadow-sm border mb-6 p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">搜索分销员</label>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="姓名、手机号"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">身份等级</label>
            <select
              v-model="filterLevel"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部等级</option>
              <option value="团长">团长</option>
              <option value="一级分销">一级分销</option>
              <option value="二级分销">二级分销</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
            <select
              v-model="filterStatus"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部状态</option>
              <option value="active">正常</option>
              <option value="inactive">禁用</option>
              <option value="pending">待审核</option>
            </select>
          </div>
          <div class="flex items-end">
            <button @click="searchDistributors" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              搜索
            </button>
          </div>
        </div>
      </div>

      <!-- 排行榜切换 -->
      <div class="bg-white rounded-lg shadow-sm border mb-6">
        <div class="px-6 py-4 border-b">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900">分销员排行榜</h2>
            <div class="flex items-center space-x-2">
              <button 
                v-for="period in rankingPeriods" 
                :key="period.value"
                @click="currentRankingPeriod = period.value"
                :class="[
                  'px-4 py-2 text-sm rounded-lg transition-colors',
                  currentRankingPeriod === period.value 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                ]"
              >
                {{ period.label }}
              </button>
            </div>
          </div>
        </div>
        
        <!-- 排行榜内容 -->
        <div class="p-6">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead>
                <tr class="border-b">
                  <th class="text-left py-3 px-4 font-medium text-gray-900">排名</th>
                  <th class="text-left py-3 px-4 font-medium text-gray-900">分销员</th>
                  <th class="text-left py-3 px-4 font-medium text-gray-900">身份等级</th>
                  <th class="text-left py-3 px-4 font-medium text-gray-900">佣金率</th>
                  <th class="text-left py-3 px-4 font-medium text-gray-900">销售额</th>
                  <th class="text-left py-3 px-4 font-medium text-gray-900">佣金金额</th>
                  <th class="text-left py-3 px-4 font-medium text-gray-900">客户数</th>
                  <th class="text-left py-3 px-4 font-medium text-gray-900">操作</th>
                </tr>
              </thead>
              <tbody>
                <tr 
                  v-for="(distributor, index) in currentRankingList" 
                  :key="distributor.id"
                  class="border-b hover:bg-gray-50"
                >
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <div 
                        :class="[
                          'w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold',
                          index === 0 ? 'bg-yellow-100 text-yellow-800' :
                          index === 1 ? 'bg-gray-100 text-gray-800' :
                          index === 2 ? 'bg-orange-100 text-orange-800' :
                          'bg-blue-100 text-blue-800'
                        ]"
                      >
                        {{ index + 1 }}
                      </div>
                      <i 
                        v-if="index < 3"
                        :class="[
                          'ml-2 text-lg',
                          index === 0 ? 'ri-trophy-line text-yellow-500' :
                          index === 1 ? 'ri-medal-line text-gray-500' :
                          'ri-award-line text-orange-500'
                        ]"
                      ></i>
                    </div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <img 
                        :src="distributor.avatar" 
                        :alt="distributor.name"
                        class="w-10 h-10 rounded-full mr-3"
                      />
                      <div>
                        <p class="font-medium text-gray-900">{{ distributor.name }}</p>
                        <p class="text-sm text-gray-500">{{ distributor.phone }}</p>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4">
                    <span 
                      :class="[
                        'px-3 py-1 rounded-full text-xs font-medium',
                        getLevelStyle(distributor.level)
                      ]"
                    >
                      {{ distributor.level }}
                    </span>
                  </td>
                  <td class="py-4 px-4">
                    <span class="font-medium text-blue-600">{{ distributor.commissionRate }}%</span>
                  </td>
                  <td class="py-4 px-4">
                    <span class="font-medium text-gray-900">¥{{ distributor.sales }}</span>
                  </td>
                  <td class="py-4 px-4">
                    <span class="font-medium text-green-600">¥{{ distributor.commission }}</span>
                  </td>
                  <td class="py-4 px-4">
                    <span class="text-gray-900">{{ distributor.customerCount }}</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex items-center space-x-2">
                      <button 
                        @click="viewDistributor(distributor)"
                        class="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        查看
                      </button>
                      <button 
                        @click="editDistributor(distributor)"
                        class="text-green-600 hover:text-green-800 text-sm"
                      >
                        编辑
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <!-- 分页 -->
          <div class="flex items-center justify-between mt-6">
            <div class="text-sm text-gray-500">
              显示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条，共 {{ totalCount }} 条
            </div>
            <div class="flex items-center space-x-2">
              <button 
                @click="changePage(currentPage - 1)"
                :disabled="currentPage <= 1"
                class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                上一页
              </button>
              <span class="px-3 py-1 bg-blue-600 text-white rounded text-sm">{{ currentPage }}</span>
              <button 
                @click="changePage(currentPage + 1)"
                :disabled="currentPage >= totalPages"
                class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分销员详情弹窗 -->
    <div v-if="showDetailModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">分销员详情</h3>
          <button @click="showDetailModal = false" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>

        <div v-if="selectedDistributor" class="space-y-6">
          <!-- 基本信息 -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-3">基本信息</h4>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="text-sm text-gray-500">姓名</p>
                <p class="font-medium">{{ selectedDistributor.name }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">手机号</p>
                <p class="font-medium">{{ selectedDistributor.phone }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">身份等级</p>
                <p class="font-medium">{{ selectedDistributor.level }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">加入时间</p>
                <p class="font-medium">{{ selectedDistributor.joinTime }}</p>
              </div>
            </div>
          </div>

          <!-- 业绩统计 -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-3">业绩统计</h4>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="text-sm text-gray-500">总销售额</p>
                <p class="font-medium text-green-600">¥{{ selectedDistributor.sales }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">总佣金</p>
                <p class="font-medium text-blue-600">¥{{ selectedDistributor.commission }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">客户数量</p>
                <p class="font-medium">{{ selectedDistributor.customerCount }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">佣金率</p>
                <p class="font-medium">{{ selectedDistributor.commissionRate }}%</p>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-end mt-6">
          <button @click="showDetailModal = false" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700">
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDistributorList, getDistributorStats, updateDistributorStatus } from '@/api/distribution'

export default {
  name: 'DistributorListPage',
  data() {
    return {
      // 统计数据
      statistics: {
        totalDistributors: 0,
        teamLeaders: 0,
        totalCommission: '0.00',
        totalSales: '0.00'
      },

      // 搜索和筛选
      searchQuery: '',
      filterLevel: '',
      filterStatus: '',

      // 排行榜周期
      rankingPeriods: [
        { label: '日榜', value: 'daily' },
        { label: '周榜', value: 'weekly' },
        { label: '月榜', value: 'monthly' }
      ],
      currentRankingPeriod: 'daily',

      // 分销员列表数据
      distributorList: [],

      // 分页
      currentPage: 1,
      pageSize: 20,
      totalCount: 0,

      // 弹窗状态
      showDetailModal: false,
      selectedDistributor: null
    }
  },

  computed: {
    // 当前排行榜列表
    currentRankingList() {
      return this.distributorList.slice(0, 50); // 显示前50名
    },

    // 总页数
    totalPages() {
      return Math.ceil(this.totalCount / this.pageSize);
    }
  },

  mounted() {
    console.log('分销员列表页面已加载');
    this.loadData();
  },

  methods: {
    // 加载数据
    async loadData() {
      await Promise.all([
        this.loadStatistics(),
        this.loadDistributorList()
      ]);
    },

    // 加载统计数据
    async loadStatistics() {
      try {
        const response = await getDistributorStats();

        if (response.data && response.data.errno === 0) {
          this.statistics = response.data.data;
        } else {
          console.error('加载统计数据失败:', response.data);
        }
      } catch (error) {
        console.error('加载统计数据失败:', error);
        // 使用模拟数据
        this.statistics = {
          totalDistributors: 156,
          teamLeaders: 23,
          totalCommission: '28,650.00',
          totalSales: '186,420.00'
        };
      }
    },

    // 加载分销员列表
    async loadDistributorList() {
      try {
        const params = {
          page: this.currentPage,
          size: this.pageSize,
          period: this.currentRankingPeriod,
          search: this.searchQuery,
          level: this.filterLevel,
          status: this.filterStatus
        };

        const response = await getDistributorList(params);

        if (response.data && response.data.errno === 0) {
          this.distributorList = response.data.data.data || [];
          this.totalCount = response.data.data.count || 0;
        } else {
          console.error('加载分销员列表失败:', response.data);
          this.loadMockData();
        }
      } catch (error) {
        console.error('加载分销员列表失败:', error);
        this.loadMockData();
      }
    },

    // 加载模拟数据
    loadMockData() {
      this.distributorList = [
        {
          id: 1,
          name: '张团长',
          phone: '138****8888',
          avatar: 'https://ui-avatars.com/api/?name=张团长&background=random&size=128',
          level: '团长',
          commissionRate: 15.0,
          sales: '125,680.00',
          commission: '18,852.00',
          customerCount: 89,
          joinTime: '2023-08-15',
          status: 'active'
        },
        {
          id: 2,
          name: '李分销',
          phone: '139****6666',
          avatar: 'https://ui-avatars.com/api/?name=李分销&background=random&size=128',
          level: '一级分销',
          commissionRate: 12.0,
          sales: '98,420.00',
          commission: '11,810.40',
          customerCount: 67,
          joinTime: '2023-09-20',
          status: 'active'
        },
        {
          id: 3,
          name: '王小销',
          phone: '136****5555',
          avatar: 'https://ui-avatars.com/api/?name=王小销&background=random&size=128',
          level: '二级分销',
          commissionRate: 8.0,
          sales: '76,350.00',
          commission: '6,108.00',
          customerCount: 45,
          joinTime: '2023-10-10',
          status: 'active'
        },
        {
          id: 4,
          name: '赵推广',
          phone: '135****4444',
          avatar: 'https://ui-avatars.com/api/?name=赵推广&background=random&size=128',
          level: '团长',
          commissionRate: 15.0,
          sales: '65,280.00',
          commission: '9,792.00',
          customerCount: 38,
          joinTime: '2023-11-05',
          status: 'active'
        },
        {
          id: 5,
          name: '钱营销',
          phone: '134****3333',
          avatar: 'https://ui-avatars.com/api/?name=钱营销&background=random&size=128',
          level: '一级分销',
          commissionRate: 12.0,
          sales: '54,670.00',
          commission: '6,560.40',
          customerCount: 32,
          joinTime: '2023-11-15',
          status: 'active'
        }
      ];
      this.totalCount = this.distributorList.length;
    },

    // 获取等级样式
    getLevelStyle(level) {
      const styles = {
        '团长': 'bg-red-100 text-red-800',
        '一级分销': 'bg-blue-100 text-blue-800',
        '二级分销': 'bg-green-100 text-green-800'
      };
      return styles[level] || 'bg-gray-100 text-gray-800';
    },

    // 搜索分销员
    searchDistributors() {
      this.currentPage = 1;
      this.loadDistributorList();
    },

    // 切换页面
    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page;
        this.loadDistributorList();
      }
    },

    // 查看分销员详情
    viewDistributor(distributor) {
      this.selectedDistributor = distributor;
      this.showDetailModal = true;
    },

    // 编辑分销员
    editDistributor(distributor) {
      // 这里可以跳转到编辑页面或打开编辑弹窗
      console.log('编辑分销员:', distributor);
      this.$message.info('编辑功能开发中');
    },

    // 刷新数据
    async refreshData() {
      await this.loadData();
      this.$message.success('数据刷新成功');
    },

    // 导出数据
    exportData() {
      console.log('导出分销员数据');
      this.$message.info('导出功能开发中');
    }
  },

  watch: {
    // 监听排行榜周期变化
    currentRankingPeriod() {
      this.loadDistributorList();
    }
  }
}
</script>

<style scoped>
.distributor-list-page {
  min-height: 100vh;
  background-color: #f9fafb;
}

.page-header {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 表格样式 */
.overflow-x-auto {
  overflow-x: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

/* 排名徽章动画 */
.w-8.h-8 {
  transition: all 0.3s ease;
}

.w-8.h-8:hover {
  transform: scale(1.1);
}

/* 头像样式 */
.w-10.h-10 {
  object-fit: cover;
}
</style>
