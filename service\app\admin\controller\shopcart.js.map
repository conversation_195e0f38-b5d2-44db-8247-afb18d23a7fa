{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\shopcart.js"], "names": ["Base", "require", "moment", "module", "exports", "indexAction", "page", "get", "size", "name", "model", "data", "alias", "join", "table", "as", "on", "where", "field", "order", "countSelect", "item", "add_time", "unix", "format", "nickname", "<PERSON><PERSON><PERSON>", "from", "user_nickname", "toString", "success", "deleteAction", "id", "fail", "update", "is_delete", "error", "console", "log", "message"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;AACAE,OAAOC,OAAP,GAAiB,cAAcJ,IAAd,CAAmB;AAChC;;;;AAIMK,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAMC,OAAO,MAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,MAAKD,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,kBAAME,OAAO,MAAKF,GAAL,CAAS,MAAT,KAAoB,EAAjC;;AAEA;AACA,kBAAMG,QAAQ,MAAKA,KAAL,CAAW,MAAX,CAAd;AACA,kBAAMC,OAAO,MAAMD,MAAME,KAAN,CAAY,GAAZ,EACdC,IADc,CACT;AACFC,uBAAO,MADL;AAEFD,sBAAM,OAFJ,EAEc;AAChBE,oBAAI,GAHF;AAIFC,oBAAI,CAAC,WAAD,EAAc,MAAd;AAJF,aADS,EAOdC,KAPc,CAOR;AACH,gCAAgB,CAAC,MAAD,EAAU,IAAGR,IAAK,GAAlB;AADb,aAPQ,EAUdS,KAVc,CAUR,kCAVQ,EAWdC,KAXc,CAWR,CAAC,WAAD,CAXQ,EAYdb,IAZc,CAYTA,IAZS,EAYHE,IAZG,EAadY,WAbc,EAAnB;;AAeA,iBAAK,MAAMC,IAAX,IAAmBV,KAAKA,IAAxB,EAA8B;AAC1BU,qBAAKC,QAAL,GAAgBpB,OAAOqB,IAAP,CAAYF,KAAKC,QAAjB,EAA2BE,MAA3B,CAAkC,qBAAlC,CAAhB;AACA;AACAH,qBAAKI,QAAL,GAAgBC,OAAOC,IAAP,CAAYN,KAAKO,aAAjB,EAAgC,QAAhC,EAA0CC,QAA1C,EAAhB;AACH;;AAED,mBAAO,MAAKC,OAAL,CAAanB,IAAb,CAAP;AA5BgB;AA6BnB;;AAED;AACMoB,gBAAN,GAAqB;AAAA;;AAAA;AACjB,kBAAMC,KAAK,OAAKzB,GAAL,CAAS,IAAT,CAAX;AACA,gBAAI,CAACyB,EAAL,EAAS;AACL,uBAAO,OAAKC,IAAL,CAAU,WAAV,CAAP;AACH;;AAED,gBAAI;AACA;AACA,sBAAM,OAAKvB,KAAL,CAAW,MAAX,EAAmBO,KAAnB,CAAyB,EAAEe,IAAIA,EAAN,EAAzB,EAAqCE,MAArC,CAA4C,EAAEC,WAAW,CAAb,EAA5C,CAAN;AACA,uBAAO,OAAKL,OAAL,CAAa,MAAb,CAAP;AACH,aAJD,CAIE,OAAOM,KAAP,EAAc;AACZC,wBAAQC,GAAR,CAAY,WAAZ,EAAyBF,MAAMG,OAA/B;AACA,uBAAO,OAAKN,IAAL,CAAU,MAAV,CAAP;AACH;AAbgB;AAcpB;;AAnD+B,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\shopcart.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\nmodule.exports = class extends Base {\n    /**\n     * index action\n     * @return {Promise} []\n     */\n    async indexAction() {\n        const page = this.get('page') || 1;\n        const size = this.get('size') || 10;\n        const name = this.get('name') || '';\n\n        // 使用JOIN查询，只获取用户存在的购物车记录\n        const model = this.model('cart');\n        const data = await model.alias('c')\n            .join({\n                table: 'user',\n                join: 'inner',  // 使用inner join过滤掉用户不存在的记录\n                as: 'u',\n                on: ['c.user_id', 'u.id']\n            })\n            .where({\n                'c.goods_name': ['like', `%${name}%`]\n            })\n            .field('c.*, u.nickname as user_nickname')\n            .order(['c.id DESC'])\n            .page(page, size)\n            .countSelect();\n\n        for (const item of data.data) {\n            item.add_time = moment.unix(item.add_time).format('YYYY-MM-DD HH:mm:ss');\n            // 直接使用JOIN查询得到的nickname，无需再次查询用户表\n            item.nickname = Buffer.from(item.user_nickname, 'base64').toString();\n        }\n\n        return this.success(data);\n    }\n\n    // 删除购物车项\n    async deleteAction() {\n        const id = this.get('id');\n        if (!id) {\n            return this.fail('缺少购物车ID参数');\n        }\n\n        try {\n            // 软删除，设置is_delete为1\n            await this.model('cart').where({ id: id }).update({ is_delete: 1 });\n            return this.success('删除成功');\n        } catch (error) {\n            console.log('删除购物车项失败:', error.message);\n            return this.fail('删除失败');\n        }\n    }\n\n};\n"]}