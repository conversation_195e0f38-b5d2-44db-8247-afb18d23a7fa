const util = require('../../utils/util.js');
const api = require('../../config/api.js');
const auth = require('../../utils/auth.js');
const app = getApp();

Page({
  data: {
    // 用户状态
    hasUserInfo: false,
    userInfo: {},
    needCompleteInfo: false, // 是否需要完善信息

    // 弹窗状态
    showAuthModal: false, // 是否显示授权弹窗
    authStep: 1, // 授权步骤：1-获取手机号，2-设置昵称
    tempPhoneNumber: '', // 临时存储的手机号
    tempNickname: '', // 临时存储的昵称
    tempAvatarUrl: '/images/icon/default_avatar_big.jpg', // 临时存储的头像

    // 表单数据
    orderNumber: '',
    inputFocus: false,
    inputFocused: false, // 输入框是否获得焦点

    // 状态控制
    isExchanging: false,

    // 兑换记录
    exchangeRecords: [],

    // 弹窗状态
    showSuccessModal: false,

    // 成功信息
    successMessage: '',
    earnedPoints: 0
  },

  onLoad: function(options) {
    console.log('订单兑换页面加载，参数:', options);

    // 处理从外部链接进入的参数
    this.handleExternalParams(options);

    // 检查用户授权状态并更新页面显示
    this.checkUserAuth();
  },



  /**
   * 处理从外部链接进入的参数
   */
  handleExternalParams: function(options) {
    console.log('处理外部参数:', options);

    // 如果有订单号参数，自动填入
    if (options.orderNumber) {
      console.log('从外部链接获取到订单号:', options.orderNumber);
      this.setData({
        orderNumber: options.orderNumber
      });
    }

    // 记录来源信息（用于数据分析）
    if (options.source) {
      console.log('来源:', options.source);
      // 可以保存到本地存储或发送给后端进行统计
      wx.setStorageSync('entry_source', options.source);
    }

    // 记录UTM参数（用于营销分析）
    if (options.utm) {
      console.log('UTM参数:', options.utm);
      wx.setStorageSync('utm_params', options.utm);
    }

    // 记录用户ID（如果有）
    if (options.userId) {
      console.log('用户ID:', options.userId);
      wx.setStorageSync('external_user_id', options.userId);
    }

    // 如果是从微信消息进入，显示欢迎提示
    if (options.source === 'wechat_message') {
      setTimeout(() => {
        wx.showToast({
          title: '欢迎使用订单有礼',
          icon: 'none',
          duration: 2000
        });
      }, 500);
    }
  },

  onShow: function() {
    // 每次显示页面时检查用户授权状态
    this.checkUserAuth();
  },

  /**
   * 检查用户授权状态
   */
  checkUserAuth: function() {
    console.log('检查用户授权状态');

    // 使用严格的权限检查，与签到功能保持一致
    if (auth.isFullyAuthorized()) {
      const userInfo = wx.getStorageSync('userInfo');
      console.log('用户已完整授权:', userInfo);

      this.setData({
        hasUserInfo: true,
        userInfo: userInfo,
        needCompleteInfo: false, // 已完善信息，隐藏提示
        showAuthModal: false // 隐藏授权弹窗
      });

      // 加载兑换记录
      this.loadExchangeRecords();
    } else {
      console.log('用户未完整授权，显示授权弹窗');
      this.setData({
        hasUserInfo: false,
        userInfo: {},
        needCompleteInfo: true, // 需要完善信息
        showAuthModal: true, // 显示授权弹窗
        authStep: 1, // 从第一步开始
        exchangeRecords: [] // 清空兑换记录
      });
    }
  },

  /**
   * 关闭授权弹窗
   */
  closeAuthModal: function() {
    this.setData({
      showAuthModal: false,
      authStep: 1,
      tempPhoneNumber: '',
      tempNickname: '',
      tempAvatarUrl: '/images/icon/default_avatar_big.jpg'
    });
  },

  /**
   * 弹窗中获取手机号
   */
  modalGetPhoneNumber: function(e) {
    console.log('=== 弹窗中获取手机号 ===');
    console.log('手机号回调:', e.detail);

    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      const code = e.detail.code;
      console.log('获取到code:', code);

      wx.showLoading({
        title: '获取手机号中...'
      });

      // 获取当前用户ID（如果已登录）
      const userInfo = wx.getStorageSync('userInfo');
      const userId = userInfo ? userInfo.id : null;

      // 调用后端接口，用code换取手机号
      const requestData = { code: code };
      if (userId) {
        requestData.userId = userId;
      }

      util.request(api.GetPhoneNumber, requestData, 'POST').then((res) => {
        wx.hideLoading();

        if (res.errno === 0) {
          const phoneNumber = res.data.phoneNumber;
          console.log('获取手机号成功:', phoneNumber);

          // 生成默认昵称
          const defaultNickname = this.generateDefaultNickname(phoneNumber);

          this.setData({
            tempPhoneNumber: phoneNumber,
            tempNickname: defaultNickname,
            authStep: 2 // 进入第二步：设置昵称
          });

          wx.showToast({
            title: '手机号获取成功',
            icon: 'success'
          });
        } else {
          console.log('获取手机号失败:', res.errmsg);
          wx.showToast({
            title: res.errmsg || '获取手机号失败',
            icon: 'none'
          });
        }
      }).catch((err) => {
        wx.hideLoading();
        console.log('获取手机号请求失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      });
    } else {
      console.log('用户拒绝授权手机号');
      wx.showToast({
        title: '需要手机号才能使用兑换功能',
        icon: 'none'
      });
    }
  },

  /**
   * 生成默认昵称
   */
  generateDefaultNickname: function(phoneNumber) {
    const last4 = phoneNumber.slice(-4);
    return `用户****${last4}`;
  },

  /**
   * 弹窗中昵称输入
   */
  modalNicknameInput: function(e) {
    this.setData({
      tempNickname: e.detail.value
    });
  },

  /**
   * 弹窗中头像选择
   */
  modalChooseAvatar: function(e) {
    const { avatarUrl } = e.detail;
    this.setData({
      tempAvatarUrl: avatarUrl
    });
  },

  /**
   * 完成弹窗授权
   */
  completeModalAuth: function() {
    const { tempPhoneNumber, tempNickname, tempAvatarUrl } = this.data;

    if (!tempNickname || tempNickname.trim().length === 0) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      });
      return;
    }

    console.log('=== 完成弹窗授权 ===');
    console.log('手机号:', tempPhoneNumber);
    console.log('昵称:', tempNickname);
    console.log('头像:', tempAvatarUrl);

    wx.showLoading({
      title: '正在保存...'
    });

    // 调用wx.login获取code
    wx.login({
      success: (loginRes) => {
        console.log('wx.login成功，code:', loginRes.code);

        // 构造用户信息对象
        const userInfo = {
          nickName: tempNickname.trim(),
          avatarUrl: tempAvatarUrl,
          phoneNumber: tempPhoneNumber,
          gender: 0,
          language: 'zh_CN',
          city: '',
          province: '',
          country: '',
          is_demote: false
        };

        const loginParams = {
          code: loginRes.code,
          userInfo: userInfo,
          encryptedData: '',
          iv: '',
          rawData: JSON.stringify(userInfo),
          signature: ''
        };

        // 调用登录接口
        util.request(api.AuthLoginByWeixin, loginParams, 'POST').then((res) => {
          wx.hideLoading();

          if (res.errno === 0) {
            // 保存用户信息
            wx.setStorageSync('userInfo', res.data.userInfo);
            wx.setStorageSync('token', res.data.token);
            wx.setStorageSync('openid', res.data.openid);

            // 更新页面状态
            this.setData({
              hasUserInfo: true,
              userInfo: res.data.userInfo,
              needCompleteInfo: false,
              showAuthModal: false // 关闭弹窗
            });

            wx.showToast({
              title: '信息完善成功',
              icon: 'success'
            });

            // 加载兑换记录
            this.loadExchangeRecords();
          } else {
            wx.showToast({
              title: res.errmsg || '保存失败',
              icon: 'none'
            });
          }
        }).catch((err) => {
          wx.hideLoading();
          console.log('登录请求失败:', err);
          wx.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          });
        });
      },
      fail: (loginErr) => {
        wx.hideLoading();
        console.log('wx.login失败:', loginErr);
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 跳转到商城授权页面
   */
  goToAuth: function() {
    console.log('跳转到授权页面');
    wx.navigateTo({
      url: '/pages/app-auth/index?from=order-exchange'
    });
  },

  /**
   * 加载兑换记录
   */
  loadExchangeRecords: function() {
    const openid = wx.getStorageSync('openid');
    if (!openid) {
      return;
    }

    console.log('加载兑换记录');

    util.request(api.OrderExchangeRecords, {
      openid: openid
    }, 'GET').then((res) => {
      if (res.errno === 0) {
        console.log('兑换记录加载成功:', res.data);
        this.setData({
          exchangeRecords: res.data || []
        });
      } else {
        console.log('加载兑换记录失败:', res.errmsg);
      }
    }).catch((err) => {
      console.error('加载兑换记录请求失败:', err);
    });
  },



  /**
   * 订单号输入
   */
  onOrderNumberInput: function(e) {
    const value = e.detail.value.trim();
    console.log('输入订单号:', value);

    this.setData({
      orderNumber: value
    });
  },

  /**
   * 输入框获得焦点
   */
  onInputFocus: function(e) {
    console.log('输入框获得焦点');
    this.setData({
      inputFocused: true
    });
  },

  /**
   * 输入框失去焦点
   */
  onInputBlur: function(e) {
    console.log('输入框失去焦点');
    this.setData({
      inputFocused: false
    });
  },

  /**
   * 清空输入框
   */
  clearInput: function() {
    this.setData({
      orderNumber: '',
      inputFocus: true,
      inputFocused: true // 清空后保持焦点状态
    });
  },



  /**
   * 提交兑换
   */
  submitExchange: function() {
    const { orderNumber, isExchanging } = this.data;

    // 如果输入框为空，添加错误样式提示
    if (!orderNumber || orderNumber.trim() === '') {
      wx.showToast({
        title: '请输入订单号',
        icon: 'none',
        duration: 1000
      });
      return;
    }

    if (isExchanging) {
      return;
    }

    // 检查用户是否已完整授权
    if (!auth.isFullyAuthorized()) {
      wx.showToast({
        title: '请先完善个人信息',
        icon: 'none'
      });
      return;
    }

    const openid = wx.getStorageSync('openid');
    if (!openid) {
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      });
      return;
    }

    console.log('提交兑换请求');

    // 调用真实的兑换API
    this.setData({ isExchanging: true });

    util.request(api.OrderExchange, {
      orderNumber: orderNumber.trim(),
      openid: openid
    }, 'POST').then((res) => {
      this.setData({ isExchanging: false });

      if (res.errno === 0) {
        // 兑换成功
        console.log('兑换成功:', res.data);

        wx.showToast({
          title: res.data.message || '兑换成功',
          icon: 'success',
          duration: 2000
        });

        this.setData({
          showSuccessModal: true,
          orderNumber: '',
          successMessage: res.data.message,
          earnedPoints: res.data.earnedPoints
        });

        // 刷新兑换记录
        this.loadExchangeRecords();
      } else {
        // 兑换失败
        console.log('兑换失败:', res.errmsg);
        wx.showToast({
          title: res.errmsg || '兑换失败',
          icon: 'none',
          duration: 2000
        });
      }
    }).catch((err) => {
      this.setData({ isExchanging: false });
      console.error('兑换请求失败:', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none',
        duration: 2000
      });
    });
  },



  /**
   * 关闭成功弹窗
   */
  closeSuccessModal: function() {
    this.setData({
      showSuccessModal: false
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation: function() {
    // 阻止事件冒泡，防止点击模态窗内容时关闭模态窗
  },



  /**
   * 页面分享
   */
  onShareAppMessage: function() {
    return {
      title: '订单兑换 - 输入订单号获取礼品',
      path: '/pages/order-exchange/index',
      imageUrl: '/images/share/order-exchange.jpg'
    };
  }
});
