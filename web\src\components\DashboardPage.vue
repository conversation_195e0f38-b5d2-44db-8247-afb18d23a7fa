<style scoped>
    body {
        background: #f5f7fa;
        display: block;
    }

    .container {
        height: 100vh;
        width: 100vw;
        background: #f9fafb;
        position: relative;
    }

    .main-content {
        transition: all 0.3s;
        background: #f9fafb;
        overflow: auto;
        box-sizing: border-box;
    }

    .footer {
        margin-top: 10px;
        text-align: center;
        line-height: 30px;
        color: #999;
        display: block;
    }

    /* 路由过渡动画 */
    .router-fade-enter-active, .router-fade-leave-active {
        transition: opacity 0.3s;
    }
    .router-fade-enter, .router-fade-leave-to {
        opacity: 0;
    }

    /* Tailwind CSS margin 类 */
    .ml-60 {
        margin-left: 240px;
    }

    .ml-20 {
        margin-left: 80px;
    }

    /* 确保主内容区域占满宽度 */
    .w-full {
        width: 100%;
    }
</style>
<template>
    <div class="container">
        <sidebar @sidebar-toggle="handleSidebarToggle"></sidebar>
        <!-- <navbar></navbar> -->
        <div id="main-content" :class="['main-content', 'w-full', 'min-h-screen', 'transition-all', 'duration-300', sidebarCollapsed ? 'ml-20' : 'ml-60']">
            <div class="p-6">
                <transition name="router-fade" mode="out-in">
                    <router-view></router-view>
                </transition>
            </div>
        </div>
    </div>
</template>
<script>
    import Sidebar from './Common/Sidebar';
    import Navbar from './Common/Navbar';

    export default {
        data() {
            return {
                sidebarCollapsed: false
            }
        },
        components: {
            Sidebar,
            Navbar
        },
        methods: {
            handleSidebarToggle(isCollapsed) {
                this.sidebarCollapsed = isCollapsed;
            },
            handleOpen(key, keyPath) {
                console.log(key, keyPath);
            },
            handleClose(key, keyPath) {
                console.log(key, keyPath);
            }
        },
    }

</script>
