<view class="container">
  <!-- 提示信息 -->
  <view class="tips">
    <view class="tips-text">
      • 申请售后前请仔细阅读售后政策
      • 退款将在1-3个工作日内处理完成
      • 如有疑问请联系客服
    </view>
  </view>

  <!-- 订单信息 -->
  <view class="form-section">
    <view class="order-info">
      <view class="order-sn">订单号：{{orderInfo.order_sn}}</view>
      <view class="goods-item" wx:for="{{orderGoods}}" wx:key="id">
        <image class="goods-image" src="{{item.list_pic_url}}" mode="aspectFill"></image>
        <view class="goods-info">
          <view class="goods-name">{{item.goods_name}}</view>
          <view class="goods-spec" wx:if="{{item.goods_specifition_name_value}}">{{item.goods_specifition_name_value}}</view>
          <view class="goods-price">¥{{item.retail_price}} × {{item.number}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 退款类型 -->
  <view class="form-section">
    <view class="refund-type">
      <view class="form-label" style="margin-bottom: 20rpx;">退款类型</view>
      <view class="type-options">
        <view class="type-option {{refundType === 'refund_only' ? 'active' : ''}}" 
              bindtap="onRefundTypeChange" data-value="refund_only">
          仅退款
        </view>
        <view class="type-option {{refundType === 'return_refund' ? 'active' : ''}}" 
              bindtap="onRefundTypeChange" data-value="return_refund">
          退货退款
        </view>
      </view>
    </view>
  </view>

  <!-- 退款原因 -->
  <view class="form-section">
    <view class="form-item" bindtap="showReasonPicker">
      <view class="form-label">退款原因</view>
      <view class="form-content">
        <view class="form-picker {{refundReason ? '' : 'placeholder'}}">
          {{refundReason || '请选择退款原因'}}
        </view>
        <view class="arrow-right"></view>
      </view>
    </view>
  </view>

  <!-- 退款金额 -->
  <view class="form-section">
    <view class="form-item">
      <view class="form-label">退款金额</view>
      <view class="form-content">
        <input class="form-input amount-input" 
               type="digit" 
               placeholder="请输入退款金额" 
               value="{{refundAmount}}"
               bindinput="onRefundAmountInput" />
      </view>
    </view>
    <view class="max-amount" style="padding: 0 30rpx 20rpx;">
      最多可退：¥{{maxRefundAmount}}
    </view>
  </view>

  <!-- 退款说明 -->
  <view class="form-section">
    <view class="form-item" style="align-items: flex-start;">
      <view class="form-label" style="margin-top: 10rpx;">退款说明</view>
      <view class="form-content">
        <textarea class="desc-textarea" 
                  placeholder="请详细说明退款原因（选填）" 
                  value="{{refundDesc}}"
                  bindinput="onRefundDescInput"
                  maxlength="200">
        </textarea>
      </view>
    </view>
  </view>

  <!-- 图片上传 -->
  <view class="form-section">
    <view class="image-upload">
      <view class="upload-title">上传凭证（选填，最多{{maxImages}}张）</view>
      <view class="image-list">
        <view class="image-item" wx:for="{{uploadedImages}}" wx:key="index">
          <image class="uploaded-image" 
                 src="{{item}}" 
                 mode="aspectFill"
                 bindtap="previewImage"
                 data-src="{{item}}">
          </image>
          <view class="delete-btn" 
                bindtap="deleteImage" 
                data-index="{{index}}">×</view>
        </view>
        <view class="add-image" 
              wx:if="{{uploadedImages.length < maxImages}}"
              bindtap="chooseImage">
          <view class="add-icon">+</view>
          <view>添加图片</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <button class="submit-btn" bindtap="submitRefund">
    提交申请
  </button>
</view>
