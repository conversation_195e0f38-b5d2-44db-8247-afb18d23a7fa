{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\flashsale_backup.js"], "names": ["Base", "require", "moment", "module", "exports", "statisticsAction", "console", "log", "flashSaleModel", "model", "orderModel", "totalFlashSales", "count", "activeFlashSales", "where", "status", "flashSaleOrders", "salesResult", "sum", "flashSaleSales", "statistics", "toFixed", "success", "error", "fail", "datesAction", "flashSaleDateModel", "updateExpiredDates", "flashSaleDates", "is_active", "order", "select", "flashSaleDate", "productCount", "sale_date_id", "id", "saleDate", "Date", "sale_date", "today", "tomorrow", "setDate", "getDate", "toDateString", "<PERSON><PERSON><PERSON><PERSON>", "toLocaleDateString", "month", "day", "weekday", "productsAction", "search", "get", "category", "saleDateId", "goodsModel", "categoryModel", "flashSales", "products", "flashSale", "goods", "goods_id", "find", "think", "isEmpty", "name", "toLowerCase", "includes", "categoryName", "category_id", "categoryInfo", "product", "goodsId", "originalPrice", "retail_price", "flashPrice", "flash_price", "stock", "soldCount", "sold_count", "limitQuantity", "limit_quantity", "startTime", "start_time", "endTime", "end_time", "image", "list_pic_url", "primary_pic_url", "createdAt", "created_at", "push", "length", "createAction", "data", "post", "productId", "existingFlashSale", "flashSaleData", "result", "add", "message"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;;AAEAE,OAAOC,OAAP,GAAiB,cAAcJ,IAAd,CAAmB;;AAElC;;;;AAIMK,kBAAN,GAAyB;AAAA;;AAAA;AACvB,UAAI;AACFC,gBAAQC,GAAR,CAAY,kBAAZ;;AAEA,cAAMC,iBAAiB,MAAKC,KAAL,CAAW,aAAX,CAAvB;AACA,cAAMC,aAAa,MAAKD,KAAL,CAAW,mBAAX,CAAnB;;AAEA;AACA,cAAME,kBAAkB,MAAMH,eAAeI,KAAf,EAA9B;;AAEA;AACA,cAAMC,mBAAmB,MAAML,eAAeM,KAAf,CAAqB;AAClDC,kBAAQ;AAD0C,SAArB,EAE5BH,KAF4B,EAA/B;;AAIA;AACA,cAAMI,kBAAkB,MAAMN,WAAWE,KAAX,EAA9B;;AAEA;AACA,cAAMK,cAAc,MAAMP,WAAWQ,GAAX,CAAe,cAAf,CAA1B;AACA,cAAMC,iBAAiBF,eAAe,CAAtC;;AAEA,cAAMG,aAAa;AACjBT,yBADiB;AAEjBE,0BAFiB;AAGjBG,yBAHiB;AAIjBG,0BAAgBA,eAAeE,OAAf,CAAuB,CAAvB;AAJC,SAAnB;;AAOAf,gBAAQC,GAAR,CAAY,OAAZ,EAAqBa,UAArB;AACA,eAAO,MAAKE,OAAL,CAAaF,UAAb,CAAP;AAED,OA/BD,CA+BE,OAAOG,KAAP,EAAc;AACdjB,gBAAQiB,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,MAAKC,IAAL,CAAU,UAAV,CAAP;AACD;AAnCsB;AAoCxB;;AAED;;;;AAIMC,aAAN,GAAoB;AAAA;;AAAA;AAClB,UAAI;AACFnB,gBAAQC,GAAR,CAAY,kBAAZ;;AAEA,cAAMmB,qBAAqB,OAAKjB,KAAL,CAAW,kBAAX,CAA3B;AACA,cAAMD,iBAAiB,OAAKC,KAAL,CAAW,aAAX,CAAvB;;AAEA;AACA,cAAMiB,mBAAmBC,kBAAnB,EAAN;;AAEA;AACA,cAAMC,iBAAiB,MAAMF,mBAAmBZ,KAAnB,CAAyB;AACpDe,qBAAW;AADyC,SAAzB,EAE1BC,KAF0B,CAEpB,gBAFoB,EAEFC,MAFE,EAA7B;;AAIA;AACA,aAAK,MAAMC,aAAX,IAA4BJ,cAA5B,EAA4C;AAC1C,gBAAMK,eAAe,MAAMzB,eAAeM,KAAf,CAAqB;AAC9CoB,0BAAcF,cAAcG,EADkB;AAE9CpB,oBAAQ,CAAC,IAAD,EAAO,CAAC,UAAD,EAAa,QAAb,CAAP;AAFsC,WAArB,EAGxBH,KAHwB,EAA3B;;AAKAoB,wBAAcC,YAAd,GAA6BA,YAA7B;;AAEA;AACA,gBAAMG,WAAW,IAAIC,IAAJ,CAASL,cAAcM,SAAvB,CAAjB;AACA,gBAAMC,QAAQ,IAAIF,IAAJ,EAAd;AACA,gBAAMG,WAAW,IAAIH,IAAJ,CAASE,KAAT,CAAjB;AACAC,mBAASC,OAAT,CAAiBF,MAAMG,OAAN,KAAkB,CAAnC;;AAEA,cAAIN,SAASO,YAAT,OAA4BJ,MAAMI,YAAN,EAAhC,EAAsD;AACpDX,0BAAcY,SAAd,GAA0B,IAA1B;AACD,WAFD,MAEO,IAAIR,SAASO,YAAT,OAA4BH,SAASG,YAAT,EAAhC,EAAyD;AAC9DX,0BAAcY,SAAd,GAA0B,IAA1B;AACD,WAFM,MAEA;AACLZ,0BAAcY,SAAd,GAA0BR,SAASS,kBAAT,CAA4B,OAA5B,EAAqC;AAC7DC,qBAAO,OADsD;AAE7DC,mBAAK;AAFwD,aAArC,CAA1B;AAID;;AAEDf,wBAAcgB,OAAd,GAAwBZ,SAASS,kBAAT,CAA4B,OAA5B,EAAqC;AAC3DG,qBAAS;AADkD,WAArC,CAAxB;AAGD;;AAED1C,gBAAQC,GAAR,CAAY,OAAZ,EAAqBqB,cAArB;AACA,eAAO,OAAKN,OAAL,CAAaM,cAAb,CAAP;AAED,OAhDD,CAgDE,OAAOL,KAAP,EAAc;AACdjB,gBAAQiB,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKC,IAAL,CAAU,UAAV,CAAP;AACD;AApDiB;AAqDnB;;AAED;;;;AAIMyB,gBAAN,GAAuB;AAAA;;AAAA;AACrB,UAAI;AACF3C,gBAAQC,GAAR,CAAY,kBAAZ;;AAEA,cAAM2C,SAAS,OAAKC,GAAL,CAAS,QAAT,KAAsB,EAArC;AACA,cAAMpC,SAAS,OAAKoC,GAAL,CAAS,QAAT,KAAsB,EAArC;AACA,cAAMC,WAAW,OAAKD,GAAL,CAAS,UAAT,KAAwB,EAAzC;AACA,cAAME,aAAa,OAAKF,GAAL,CAAS,YAAT,KAA0B,EAA7C;;AAEA,cAAM3C,iBAAiB,OAAKC,KAAL,CAAW,aAAX,CAAvB;AACA,cAAM6C,aAAa,OAAK7C,KAAL,CAAW,OAAX,CAAnB;AACA,cAAM8C,gBAAgB,OAAK9C,KAAL,CAAW,UAAX,CAAtB;;AAEA,YAAIK,QAAQ,EAAZ;;AAEA;AACA,YAAIC,MAAJ,EAAY;AACVD,gBAAMC,MAAN,GAAeA,MAAf;AACD;;AAED;AACA,YAAIsC,UAAJ,EAAgB;AACdvC,gBAAMoB,YAAN,GAAqBmB,UAArB;AACD;;AAED;AACA,cAAMG,aAAa,MAAMhD,eAAeM,KAAf,CAAqBA,KAArB,EACtBgB,KADsB,CAChB,iBADgB,EAEtBC,MAFsB,EAAzB;;AAIA,cAAM0B,WAAW,EAAjB;;AAEA;AACA,aAAK,MAAMC,SAAX,IAAwBF,UAAxB,EAAoC;AAClC,gBAAMG,QAAQ,MAAML,WAAWxC,KAAX,CAAiB;AACnCqB,gBAAIuB,UAAUE;AADqB,WAAjB,EAEjBC,IAFiB,EAApB;;AAIA,cAAI,CAACC,MAAMC,OAAN,CAAcJ,KAAd,CAAL,EAA2B;AACzB;AACA,gBAAIT,UAAU,CAACS,MAAMK,IAAN,CAAWC,WAAX,GAAyBC,QAAzB,CAAkChB,OAAOe,WAAP,EAAlC,CAAf,EAAwE;AACtE;AACD;;AAED;AACA,gBAAIE,eAAe,EAAnB;AACA,gBAAIR,MAAMS,WAAV,EAAuB;AACrB,oBAAMC,eAAe,MAAMd,cAAczC,KAAd,CAAoB;AAC7CqB,oBAAIwB,MAAMS;AADmC,eAApB,EAExBP,IAFwB,EAA3B;AAGA,kBAAI,CAACC,MAAMC,OAAN,CAAcM,YAAd,CAAL,EAAkC;AAChCF,+BAAeE,aAAaL,IAA5B;AACD;AACF;;AAED;AACA,gBAAIZ,YAAYe,iBAAiBf,QAAjC,EAA2C;AACzC;AACD;;AAED,kBAAMkB,UAAU;AACdnC,kBAAIuB,UAAUvB,EADA;AAEdoC,uBAASZ,MAAMxB,EAFD;AAGd6B,oBAAML,MAAMK,IAHE;AAIdQ,6BAAeb,MAAMc,YAJP;AAKdC,0BAAYhB,UAAUiB,WALR;AAMdC,qBAAOlB,UAAUkB,KANH;AAOdC,yBAAWnB,UAAUoB,UAPP;AAQdC,6BAAerB,UAAUsB,cARX;AASdjE,sBAAQ2C,UAAU3C,MATJ;AAUdkE,yBAAWvB,UAAUwB,UAVP;AAWdC,uBAASzB,UAAU0B,QAXL;AAYd/B,0BAAYK,UAAUxB,YAZR;AAadiC,4BAAcA,YAbA;AAcdkB,qBAAO1B,MAAM2B,YAAN,IAAsB3B,MAAM4B,eAA5B,IAA+C,EAdxC;AAedC,yBAAW9B,UAAU+B;AAfP,aAAhB;;AAkBAhC,qBAASiC,IAAT,CAAcpB,OAAd;AACD;AACF;;AAEDhE,gBAAQC,GAAR,CAAY,OAAZ,EAAqBkD,SAASkC,MAA9B,EAAsC,KAAtC;AACA,eAAO,OAAKrE,OAAL,CAAamC,QAAb,CAAP;AAED,OApFD,CAoFE,OAAOlC,KAAP,EAAc;AACdjB,gBAAQiB,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKC,IAAL,CAAU,UAAV,CAAP;AACD;AAxFoB;AAyFtB;;AAED;;;;AAIMoE,cAAN,GAAqB;AAAA;;AAAA;AACnB,UAAI;AACFtF,gBAAQC,GAAR,CAAY,gBAAZ;;AAEA,cAAMsF,OAAO,OAAKC,IAAL,EAAb;AACAxF,gBAAQC,GAAR,CAAY,OAAZ,EAAqBsF,IAArB;;AAEA;AACA,YAAI,CAACA,KAAKE,SAAN,IAAmB,CAACF,KAAKxC,UAAzB,IAAuC,CAACwC,KAAKnB,UAA7C,IAA2D,CAACmB,KAAKjB,KAArE,EAA4E;AAC1E,iBAAO,OAAKpD,IAAL,CAAU,YAAV,CAAP;AACD;;AAED;AACA,YAAI,CAACqE,KAAKZ,SAAN,IAAmB,CAACY,KAAKV,OAA7B,EAAsC;AACpC,iBAAO,OAAK3D,IAAL,CAAU,SAAV,CAAP;AACD;;AAED,YAAI,IAAIa,IAAJ,CAASwD,KAAKZ,SAAd,KAA4B,IAAI5C,IAAJ,CAASwD,KAAKV,OAAd,CAAhC,EAAwD;AACtD,iBAAO,OAAK3D,IAAL,CAAU,cAAV,CAAP;AACD;;AAED,cAAMhB,iBAAiB,OAAKC,KAAL,CAAW,aAAX,CAAvB;;AAEA;AACA,cAAMuF,oBAAoB,MAAMxF,eAAeM,KAAf,CAAqB;AACnD8C,oBAAUiC,KAAKE,SADoC;AAEnD7D,wBAAc2D,KAAKxC,UAFgC;AAGnDtC,kBAAQ,CAAC,IAAD,EAAO,CAAC,UAAD,EAAa,QAAb,CAAP;AAH2C,SAArB,EAI7B8C,IAJ6B,EAAhC;;AAMA,YAAI,CAACC,MAAMC,OAAN,CAAciC,iBAAd,CAAL,EAAuC;AACrC,iBAAO,OAAKxE,IAAL,CAAU,eAAV,CAAP;AACD;;AAED;AACA,cAAMyE,gBAAgB;AACpBrC,oBAAUiC,KAAKE,SADK;AAEpB7D,wBAAc2D,KAAKxC,UAFC;AAGpBsB,uBAAakB,KAAKnB,UAHE;AAIpBE,iBAAOiB,KAAKjB,KAJQ;AAKpBI,0BAAgBa,KAAKd,aAAL,IAAsB,CALlB;AAMpBG,sBAAYW,KAAKZ,SANG;AAOpBG,oBAAUS,KAAKV,OAPK;AAQpBpE,kBAAQ;AARY,SAAtB;;AAWA,cAAMmF,SAAS,MAAM1F,eAAe2F,GAAf,CAAmBF,aAAnB,CAArB;;AAEA,YAAIC,MAAJ,EAAY;AACV5F,kBAAQC,GAAR,CAAY,cAAZ,EAA4B2F,MAA5B;AACA,iBAAO,OAAK5E,OAAL,CAAa,EAAEa,IAAI+D,MAAN,EAAb,CAAP;AACD,SAHD,MAGO;AACL,iBAAO,OAAK1E,IAAL,CAAU,MAAV,CAAP;AACD;AAEF,OAtDD,CAsDE,OAAOD,KAAP,EAAc;AACdjB,gBAAQiB,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO,OAAKC,IAAL,CAAU,WAAWD,MAAM6E,OAA3B,CAAP;AACD;AA1DkB;AA2DpB;AArQiC,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\flashsale_backup.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\n\nmodule.exports = class extends Base {\n  \n  /**\n   * 获取秒杀统计数据\n   * GET /admin/flashsale/statistics\n   */\n  async statisticsAction() {\n    try {\n      console.log('=== 获取秒杀统计数据 ===');\n      \n      const flashSaleModel = this.model('flash_sales');\n      const orderModel = this.model('flash_sale_orders');\n      \n      // 获取总秒杀活动数\n      const totalFlashSales = await flashSaleModel.count();\n      \n      // 获取进行中的秒杀活动数\n      const activeFlashSales = await flashSaleModel.where({\n        status: 'active'\n      }).count();\n      \n      // 获取秒杀订单数\n      const flashSaleOrders = await orderModel.count();\n      \n      // 获取秒杀销售额\n      const salesResult = await orderModel.sum('total_amount');\n      const flashSaleSales = salesResult || 0;\n      \n      const statistics = {\n        totalFlashSales,\n        activeFlashSales,\n        flashSaleOrders,\n        flashSaleSales: flashSaleSales.toFixed(2)\n      };\n      \n      console.log('统计数据:', statistics);\n      return this.success(statistics);\n      \n    } catch (error) {\n      console.error('获取统计数据失败:', error);\n      return this.fail('获取统计数据失败');\n    }\n  }\n  \n  /**\n   * 获取秒杀日期列表\n   * GET /admin/flashsale/dates\n   */\n  async datesAction() {\n    try {\n      console.log('=== 获取秒杀日期列表 ===');\n\n      const flashSaleDateModel = this.model('flash_sale_dates');\n      const flashSaleModel = this.model('flash_sales');\n\n      // 自动更新过期日期状态\n      await flashSaleDateModel.updateExpiredDates();\n\n      // 获取所有启用的日期\n      const flashSaleDates = await flashSaleDateModel.where({\n        is_active: 1\n      }).order('sort_order ASC').select();\n\n      // 为每个日期统计商品数量\n      for (const flashSaleDate of flashSaleDates) {\n        const productCount = await flashSaleModel.where({\n          sale_date_id: flashSaleDate.id,\n          status: ['IN', ['upcoming', 'active']]\n        }).count();\n\n        flashSaleDate.productCount = productCount;\n\n        // 格式化日期显示\n        const saleDate = new Date(flashSaleDate.sale_date);\n        const today = new Date();\n        const tomorrow = new Date(today);\n        tomorrow.setDate(today.getDate() + 1);\n\n        if (saleDate.toDateString() === today.toDateString()) {\n          flashSaleDate.dateLabel = '今天';\n        } else if (saleDate.toDateString() === tomorrow.toDateString()) {\n          flashSaleDate.dateLabel = '明天';\n        } else {\n          flashSaleDate.dateLabel = saleDate.toLocaleDateString('zh-CN', {\n            month: 'short',\n            day: 'numeric'\n          });\n        }\n\n        flashSaleDate.weekday = saleDate.toLocaleDateString('zh-CN', {\n          weekday: 'short'\n        });\n      }\n\n      console.log('日期列表:', flashSaleDates);\n      return this.success(flashSaleDates);\n\n    } catch (error) {\n      console.error('获取日期列表失败:', error);\n      return this.fail('获取日期列表失败');\n    }\n  }\n  \n  /**\n   * 获取秒杀商品列表\n   * GET /admin/flashsale/products\n   */\n  async productsAction() {\n    try {\n      console.log('=== 获取秒杀商品列表 ===');\n      \n      const search = this.get('search') || '';\n      const status = this.get('status') || '';\n      const category = this.get('category') || '';\n      const saleDateId = this.get('saleDateId') || '';\n\n      const flashSaleModel = this.model('flash_sales');\n      const goodsModel = this.model('goods');\n      const categoryModel = this.model('category');\n\n      let where = {};\n\n      // 状态筛选\n      if (status) {\n        where.status = status;\n      }\n\n      // 日期筛选\n      if (saleDateId) {\n        where.sale_date_id = saleDateId;\n      }\n      \n      // 获取秒杀活动列表\n      const flashSales = await flashSaleModel.where(where)\n        .order('created_at DESC')\n        .select();\n      \n      const products = [];\n      \n      // 为每个秒杀活动获取商品信息\n      for (const flashSale of flashSales) {\n        const goods = await goodsModel.where({\n          id: flashSale.goods_id\n        }).find();\n        \n        if (!think.isEmpty(goods)) {\n          // 搜索过滤\n          if (search && !goods.name.toLowerCase().includes(search.toLowerCase())) {\n            continue;\n          }\n          \n          // 获取分类信息\n          let categoryName = '';\n          if (goods.category_id) {\n            const categoryInfo = await categoryModel.where({\n              id: goods.category_id\n            }).find();\n            if (!think.isEmpty(categoryInfo)) {\n              categoryName = categoryInfo.name;\n            }\n          }\n          \n          // 分类过滤\n          if (category && categoryName !== category) {\n            continue;\n          }\n          \n          const product = {\n            id: flashSale.id,\n            goodsId: goods.id,\n            name: goods.name,\n            originalPrice: goods.retail_price,\n            flashPrice: flashSale.flash_price,\n            stock: flashSale.stock,\n            soldCount: flashSale.sold_count,\n            limitQuantity: flashSale.limit_quantity,\n            status: flashSale.status,\n            startTime: flashSale.start_time,\n            endTime: flashSale.end_time,\n            saleDateId: flashSale.sale_date_id,\n            categoryName: categoryName,\n            image: goods.list_pic_url || goods.primary_pic_url || '',\n            createdAt: flashSale.created_at\n          };\n          \n          products.push(product);\n        }\n      }\n      \n      console.log('商品列表:', products.length, '个商品');\n      return this.success(products);\n      \n    } catch (error) {\n      console.error('获取商品列表失败:', error);\n      return this.fail('获取商品列表失败');\n    }\n  }\n  \n  /**\n   * 创建秒杀活动\n   * POST /admin/flashsale/create\n   */\n  async createAction() {\n    try {\n      console.log('=== 创建秒杀活动 ===');\n      \n      const data = this.post();\n      console.log('创建数据:', data);\n      \n      // 验证必填字段\n      if (!data.productId || !data.saleDateId || !data.flashPrice || !data.stock) {\n        return this.fail('请填写完整的秒杀信息');\n      }\n      \n      // 验证时间\n      if (!data.startTime || !data.endTime) {\n        return this.fail('请设置活动时间');\n      }\n      \n      if (new Date(data.startTime) >= new Date(data.endTime)) {\n        return this.fail('开始时间必须早于结束时间');\n      }\n      \n      const flashSaleModel = this.model('flash_sales');\n      \n      // 检查商品是否已经在同日期有秒杀活动\n      const existingFlashSale = await flashSaleModel.where({\n        goods_id: data.productId,\n        sale_date_id: data.saleDateId,\n        status: ['IN', ['upcoming', 'active']]\n      }).find();\n\n      if (!think.isEmpty(existingFlashSale)) {\n        return this.fail('该商品在此日期已有秒杀活动');\n      }\n      \n      // 创建秒杀活动\n      const flashSaleData = {\n        goods_id: data.productId,\n        sale_date_id: data.saleDateId,\n        flash_price: data.flashPrice,\n        stock: data.stock,\n        limit_quantity: data.limitQuantity || 1,\n        start_time: data.startTime,\n        end_time: data.endTime,\n        status: 'upcoming'\n      };\n      \n      const result = await flashSaleModel.add(flashSaleData);\n      \n      if (result) {\n        console.log('秒杀活动创建成功，ID:', result);\n        return this.success({ id: result });\n      } else {\n        return this.fail('创建失败');\n      }\n      \n    } catch (error) {\n      console.error('创建秒杀活动失败:', error);\n      return this.fail('创建失败: ' + error.message);\n    }\n  }\n};\n"]}