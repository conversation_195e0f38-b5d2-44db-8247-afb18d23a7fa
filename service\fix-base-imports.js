const fs = require('fs');
const path = require('path');

// 修复所有引用base.js的文件
function fixBaseImports() {
  const modelDir = path.join(__dirname, 'app/api/model');
  
  // 读取模型目录中的所有文件
  const files = fs.readdirSync(modelDir);
  
  files.forEach(file => {
    if (file.endsWith('.js') && !file.endsWith('.map')) {
      const filePath = path.join(modelDir, file);
      let content = fs.readFileSync(filePath, 'utf8');
      
      // 检查是否包含base.js引用
      if (content.includes("require('./base.js')")) {
        console.log(`修复文件: ${file}`);
        
        // 替换base.js引用
        content = content.replace(
          /const Base = require\('\.\/base\.js'\);\s*\n\s*module\.exports = class extends Base \{/g,
          'module.exports = class extends think.Model {'
        );
        
        // 写回文件
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ ${file} 修复完成`);
      }
    }
  });
  
  console.log('所有文件修复完成！');
}

// 执行修复
try {
  fixBaseImports();
} catch (error) {
  console.error('修复过程中出错:', error);
}
