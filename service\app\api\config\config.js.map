{"version": 3, "sources": ["..\\..\\..\\src\\api\\config\\config.js"], "names": ["module", "exports", "publicController", "publicAction"], "mappings": "AAAA;AACAA,OAAOC,OAAP,GAAiB;AACf;AACAC,oBAAkB;AAChB;AACA,SAFgB,EAGhB,SAHgB,EAIhB,MAJgB,EAKhB,OALgB,EAMhB,QANgB,EAOhB,QAPgB,EAQhB,SARgB,EAShB,QATgB,CAFH;;AAcf;AACAC,gBAAc;AACZ;AACA,cAFY,EAGZ,UAHY,EAIZ,cAJY,EAKZ,aALY,EAMZ,aANY,EAOZ,iBAPY,EAQZ,YARY;AAfC,CAAjB", "file": "..\\..\\..\\src\\api\\config\\config.js", "sourcesContent": ["// default config\nmodule.exports = {\n  // 可以公开访问的Controller\n  publicController: [\n    // 格式为controller\n    'index',\n    'catalog',\n    'auth',\n    'goods',\n    'search',\n    'region',\n    'address',\n    'signin'\n  ],\n\n  // 可以公开访问的Action\n  publicAction: [\n    // 格式为： controller+action\n    'cart/index',\n    'cart/add',\n    'cart/checked',\n    'cart/update',\n    'cart/delete',\n    'cart/goodscount',\n    'pay/notify'\n  ]\n};\n"]}