<view class="container">
  <!-- 签到状态区 -->
  <view class="status-section">
    <view class="status-card">
      <view class="status-content">
        <view class="consecutive-days">
          已连续签到 <text class="highlight">{{consecutiveDays}}</text> 天
        </view>
        <view class="total-points">
          当前积分：<text class="highlight">{{totalPoints}}</text>
        </view>
        <view class="today-status {{todaySignedClass}}">
          {{todayStatusText}}
        </view>
      </view>
    </view>
  </view>

  <!-- 日历区域 -->
  <view class="calendar-section">
    <view class="calendar-card">
      <!-- 年月显示 -->
      <view class="calendar-header">
        <view class="month-nav" bindtap="prevMonth">
          <text class="nav-arrow">‹</text>
        </view>
        <view class="current-month">{{currentMonthText}}</view>
        <view class="month-nav" bindtap="nextMonth">
          <text class="nav-arrow">›</text>
        </view>
      </view>

      <!-- 星期表头 -->
      <view class="week-header">
        <view class="week-day">一</view>
        <view class="week-day">二</view>
        <view class="week-day">三</view>
        <view class="week-day">四</view>
        <view class="week-day">五</view>
        <view class="week-day">六</view>
        <view class="week-day">日</view>
      </view>

      <!-- 日历格子 -->
      <view class="calendar-grid">
        <view 
          wx:for="{{calendarDays}}" 
          wx:key="index"
          class="calendar-day {{item.className}}"
          bindtap="onDayTap"
          data-date="{{item.date}}"
          data-index="{{index}}">
          <view class="day-number">{{item.day}}</view>
          <view wx:if="{{item.isSigned}}" class="signed-mark">
            <text class="check-icon">✓</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 签到规则说明 -->
  <view class="rules-section">
    <view class="rules-card">
      <view class="rules-title">签到规则</view>
      <view class="rules-list">
        <view class="rule-item">
          <text class="rule-icon">✓</text>
          <text class="rule-text">连续签到可获得额外积分奖励</text>
        </view>
        <view class="rule-item">
          <text class="rule-icon">✓</text>
          <text class="rule-text">首次签到：+5积分</text>
        </view>
        <view class="rule-item">
          <text class="rule-icon">✓</text>
          <text class="rule-text">连续3天：+10积分</text>
        </view>
        <view class="rule-item">
          <text class="rule-icon">✓</text>
          <text class="rule-text">连续7天：+15积分</text>
        </view>
        <view class="rule-item">
          <text class="rule-icon">✓</text>
          <text class="rule-text">连续15天：+20积分</text>
        </view>
        <view class="rule-item">
          <text class="rule-icon">✓</text>
          <text class="rule-text">连续30天：+50积分</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 积分用途说明 -->
  <view class="usage-section">
    <view class="usage-card">
      <view class="usage-title">积分用途</view>
      <view class="usage-grid">
        <view class="usage-item">
          <view class="usage-icon">🎫</view>
          <text class="usage-text">兑换优惠券</text>
        </view>
        <view class="usage-item">
          <view class="usage-icon">🎁</view>
          <text class="usage-text">兑换礼品</text>
        </view>
        <view class="usage-item">
          <view class="usage-icon">👑</view>
          <text class="usage-text">会员特权</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 签到成功弹窗 -->
  <view wx:if="{{showSuccessModal}}" class="success-modal">
    <view class="modal-overlay" bindtap="closeSuccessModal"></view>
    <view class="modal-content">
      <view class="success-icon">✓</view>
      <view class="success-title">签到成功</view>
      <view class="success-message">
        恭喜获得 <text class="earned-points">{{earnedPoints}}</text> 积分
      </view>
      <button class="success-btn" bindtap="closeSuccessModal">知道了</button>
    </view>
  </view>
</view>
