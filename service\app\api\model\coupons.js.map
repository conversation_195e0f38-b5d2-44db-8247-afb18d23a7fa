{"version": 3, "sources": ["..\\..\\..\\src\\api\\model\\coupons.js"], "names": ["module", "exports", "think", "Model", "tableName"], "mappings": "AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;AACzC,MAAIC,SAAJ,GAAgB;AACd,WAAO,iBAAP;AACD;AAHwC,CAA3C", "file": "..\\..\\..\\src\\api\\model\\coupons.js", "sourcesContent": ["module.exports = class extends think.Model {\n  get tableName() {\n    return 'hiolabs_coupons';\n  }\n};\n"]}