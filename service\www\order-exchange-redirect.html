<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单兑换 - 跳转小程序</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
        }
        .icon {
            font-size: 60px;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
        }
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            min-width: 120px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e9ecef;
        }
        .loading {
            display: none;
            margin-top: 20px;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .tips {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🎁</div>
        <h1>订单有礼</h1>
        <p class="subtitle">点击下方按钮打开小程序<br>开始您的订单兑换之旅</p>
        
        <button class="btn" onclick="openMiniProgram()">打开小程序</button>
        <button class="btn btn-secondary" onclick="copyScheme()">复制链接</button>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在打开小程序...</p>
        </div>
        
        <div class="tips">
            <strong>使用说明：</strong><br>
            • 在微信中打开此页面可直接跳转小程序<br>
            • 在其他浏览器中需要复制链接到微信打开<br>
            • 如无法跳转，请手动搜索"美汐缘"小程序
        </div>
    </div>

    <script>
        // 从URL参数获取订单号等信息
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                orderNumber: params.get('orderNumber') || '',
                source: params.get('source') || '',
                utm: params.get('utm') || ''
            };
        }

        // 生成URL Scheme
        function generateScheme() {
            const params = getUrlParams();
            const appid = 'wx919ca2ec612e6ecb'; // 您的小程序appid
            const path = 'pages/order-exchange/index';
            
            // 构建query参数
            let query = '';
            if (params.orderNumber) {
                query += `orderNumber=${encodeURIComponent(params.orderNumber)}`;
            }
            if (params.source) {
                query += query ? '&' : '';
                query += `source=${encodeURIComponent(params.source)}`;
            }
            if (params.utm) {
                query += query ? '&' : '';
                query += `utm=${encodeURIComponent(params.utm)}`;
            }
            
            // 生成明文URL Scheme
            const scheme = `weixin://dl/business/?appid=${appid}&path=${path}&query=${encodeURIComponent(query)}&env_version=release`;
            return scheme;
        }

        // 检测是否在微信中
        function isWechat() {
            return /micromessenger/i.test(navigator.userAgent);
        }

        // 检测是否在移动设备
        function isMobile() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }

        // 打开小程序
        function openMiniProgram() {
            const loading = document.getElementById('loading');
            loading.style.display = 'block';
            
            const scheme = generateScheme();
            
            if (isWechat()) {
                // 在微信中，尝试使用微信开放标签（需要配置）
                // 这里使用URL Scheme作为备选方案
                window.location.href = scheme;
            } else {
                // 在外部浏览器中
                if (isMobile()) {
                    // 移动设备尝试直接跳转
                    window.location.href = scheme;
                    
                    // 3秒后隐藏loading，显示提示
                    setTimeout(() => {
                        loading.style.display = 'none';
                        alert('如果没有自动跳转，请复制链接到微信中打开');
                    }, 3000);
                } else {
                    // 桌面设备显示二维码或提示
                    loading.style.display = 'none';
                    alert('请在手机微信中打开此链接，或复制链接发送到手机微信');
                }
            }
        }

        // 复制URL Scheme
        function copyScheme() {
            const scheme = generateScheme();
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(scheme).then(() => {
                    alert('链接已复制，请在微信中粘贴打开');
                }).catch(() => {
                    fallbackCopy(scheme);
                });
            } else {
                fallbackCopy(scheme);
            }
        }

        // 备用复制方法
        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.opacity = '0';
            document.body.appendChild(textArea);
            textArea.select();
            
            try {
                document.execCommand('copy');
                alert('链接已复制，请在微信中粘贴打开');
            } catch (err) {
                prompt('请手动复制以下链接:', text);
            }
            
            document.body.removeChild(textArea);
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            // 如果在微信中且是移动设备，可以考虑自动跳转
            if (isWechat() && isMobile()) {
                // 可以在这里添加自动跳转逻辑
                // setTimeout(openMiniProgram, 1000);
            }
            
            // 显示当前参数（调试用）
            const params = getUrlParams();
            if (params.orderNumber) {
                console.log('订单号:', params.orderNumber);
            }
        };
    </script>
</body>
</html>
