{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\crontab.js"], "names": ["Base", "require", "moment", "rp", "http", "module", "exports", "timetaskAction", "console", "log", "currentTime", "parseInt", "Date", "getTime", "newday", "setHours", "newday_over", "notice", "model", "where", "is_delete", "select", "length", "noticeItem", "notice_exptime", "end_time", "id", "update", "expiretime", "orderList", "order_status", "add_time", "item", "orderId", "ad_info", "enabled", "map", "ele", "noConfirmTime", "noConfirmList", "shipping_time", "citem", "confirm_time", "resetSqlAction", "time", "info", "find", "reset", "countdown"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;AACA,MAAME,KAAKF,QAAQ,iBAAR,CAAX;AACA,MAAMG,OAAOH,QAAQ,MAAR,CAAb;AACAI,OAAOC,OAAP,GAAiB,cAAcN,IAAd,CAAmB;AAC1BO,kBAAN,GAAuB;AAAA;;AAAA;AACnBC,oBAAQC,GAAR,CAAY,6BAAZ;AACA,gBAAIC,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAAlB;AACA,gBAAIC,SAAS,IAAIF,IAAJ,CAAS,IAAIA,IAAJ,GAAWG,QAAX,CAAoB,CAApB,EAAuB,CAAvB,EAA0B,CAA1B,EAA6B,CAA7B,CAAT,IAA4C,IAAzD;AACA,gBAAIC,cAAc,IAAIJ,IAAJ,CAAS,IAAIA,IAAJ,GAAWG,QAAX,CAAoB,CAApB,EAAuB,CAAvB,EAA0B,EAA1B,EAA8B,CAA9B,CAAT,IAA6C,IAA/D;AACA,gBAAIL,cAAcI,MAAd,IAAwBJ,cAAcM,WAA1C,EAAuD,CACtD;AACD;AACA,gBAAIC,SAAS,MAAM,MAAKC,KAAL,CAAW,QAAX,EAAqBC,KAArB,CAA2B;AAC1CC,2BAAW;AAD+B,aAA3B,EAEhBC,MAFgB,EAAnB;AAGA,gBAAIJ,OAAOK,MAAP,GAAgB,CAApB,EAAuB;AACnB,qBAAK,MAAMC,UAAX,IAAyBN,MAAzB,EAAiC;AAC7B,wBAAIO,iBAAiBD,WAAWE,QAAhC;AACA,wBAAIf,cAAcc,cAAlB,EAAkC;AAC9B,8BAAM,MAAKN,KAAL,CAAW,QAAX,EAAqBC,KAArB,CAA2B;AAC7BO,gCAAIH,WAAWG;AADc,yBAA3B,EAEHC,MAFG,CAEI;AACNP,uCAAW;AADL,yBAFJ,CAAN;AAKH;AACJ;AACJ;AACD,kBAAMQ,aAAajB,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,IAAwC,KAAK,EAAL,GAAU,EAArE;AACA,gBAAIgB,YAAY,MAAM,MAAKX,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5CW,8BAAc,CAAC,IAAD,EAAO,SAAP,CAD8B;AAE5CC,0BAAU,CAAC,GAAD,EAAMH,UAAN,CAFkC;AAG5CR,2BAAW;AAHiC,aAA1B,EAInBC,MAJmB,EAAtB;AAKA,gBAAIQ,UAAUP,MAAV,IAAoB,CAAxB,EAA2B;AACvB;AACA,qBAAK,MAAMU,IAAX,IAAmBH,SAAnB,EAA8B;;AAE1B,wBAAII,UAAUD,KAAKN,EAAnB;AACA,0BAAM,MAAKR,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5BO,4BAAIO;AADwB,qBAA1B,EAEHN,MAFG,CAEI;AACNG,sCAAc;AADR,qBAFJ,CAAN;AAKH;AACJ;AACD;AACA,gBAAII,UAAU,MAAM,MAAKhB,KAAL,CAAW,IAAX,EAAiBC,KAAjB,CAAuB;AACvCM,0BAAU,CAAC,GAAD,EAAMf,WAAN,CAD6B;AAEvCyB,yBAAS;AAF8B,aAAvB,EAGjBd,MAHiB,EAApB;AAIA,gBAAIa,QAAQZ,MAAR,IAAkB,CAAtB,EAAyB;AACrB,sBAAM,MAAKJ,KAAL,CAAW,IAAX,EAAiBC,KAAjB,CAAuB;AACzBO,wBAAI,CAAC,IAAD,EAAOQ,QAAQE,GAAR,CAAY,UAACC,GAAD;AAAA,+BAASA,IAAIX,EAAb;AAAA,qBAAZ,CAAP;AADqB,iBAAvB,EAEHC,MAFG,CAEI;AACNQ,6BAAS;AADH,iBAFJ,CAAN;AAKH;AACD;AACA,kBAAMG,gBAAgB3B,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,IAAwC,IAAI,EAAJ,GAAS,EAAT,GAAc,EAA5E;AACA;AACA,gBAAI0B,gBAAgB,MAAM,MAAKrB,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAChDW,8BAAc,GADkC;AAEhDU,+BAAe;AACX,0BAAMF,aADK;AAEX,0BAAM;AAFK,iBAFiC;AAMhDlB,2BAAW;AANqC,aAA1B,EAOvBC,MAPuB,EAA1B;AAQA,gBAAIkB,cAAcjB,MAAd,IAAwB,CAA5B,EAA+B;AAC3B,qBAAK,MAAMmB,KAAX,IAAoBF,aAApB,EAAmC;AAC/B,wBAAIN,UAAUQ,MAAMf,EAApB;AACA,0BAAM,MAAKR,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5BO,4BAAIO;AADwB,qBAA1B,EAEHN,MAFG,CAEI;AACNG,sCAAc,GADR;AAENY,sCAAchC;AAFR,qBAFJ,CAAN;AAMH;AACJ;AA1EkB;AA2EtB;AACKiC,kBAAN,GAAuB;AAAA;;AAAA;AACnB,gBAAIC,OAAOjC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAvB,GAA8B,GAAvC,CAAX;AACA,gBAAIgC,OAAO,MAAM,OAAK3B,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B,EAACO,IAAG,CAAJ,EAA7B,EAAqCoB,IAArC,EAAjB;AACA,gBAAGD,KAAKE,KAAL,IAAc,CAAjB,EAAmB;AACf,sBAAM,OAAK7B,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B,EAACO,IAAG,CAAJ,EAA7B,EAAqCC,MAArC,CAA4C,EAACqB,WAAUJ,IAAX,EAAgBG,OAAM,CAAtB,EAA5C,CAAN;AACAvC,wBAAQC,GAAR,CAAY,MAAZ;AACH;AACDD,oBAAQC,GAAR,CAAY,OAAZ;AAPmB;AAQtB;AArF+B,CAApC", "file": "..\\..\\..\\src\\api\\controller\\crontab.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\nconst rp = require('request-promise');\nconst http = require(\"http\");\nmodule.exports = class extends Base {\n    async timetaskAction() {\n        console.log(\"=============开始============\");\n        let currentTime = parseInt(new Date().getTime() / 1000);\n        let newday = new Date(new Date().setHours(3, 0, 0, 0)) / 1000;\n        let newday_over = new Date(new Date().setHours(3, 0, 59, 0)) / 1000;\n        if (currentTime > newday && currentTime < newday_over) {\n        }\n        // 将公告下掉\n        let notice = await this.model('notice').where({\n            is_delete: 0\n        }).select();\n        if (notice.length > 0) {\n            for (const noticeItem of notice) {\n                let notice_exptime = noticeItem.end_time;\n                if (currentTime > notice_exptime) {\n                    await this.model('notice').where({\n                        id: noticeItem.id\n                    }).update({\n                        is_delete: 1\n                    });\n                }\n            }\n        }\n        const expiretime = parseInt(new Date().getTime() / 1000) - 24 * 60 * 60;\n        let orderList = await this.model('order').where({\n            order_status: ['IN', '101,801'],\n            add_time: ['<', expiretime],\n            is_delete: 0,\n        }).select();\n        if (orderList.length != 0) {\n            // await this.model('order').where({id: ['IN', orderList.map((ele) => ele.id)]}).update({order_status: 102});\n            for (const item of orderList) {\n\n                let orderId = item.id;\n                await this.model('order').where({\n                    id: orderId\n                }).update({\n                    order_status: 102\n                });\n            }\n        }\n        // 定时将到期的广告停掉\n        let ad_info = await this.model('ad').where({\n            end_time: ['<', currentTime],\n            enabled: 1\n        }).select();\n        if (ad_info.length != 0) {\n            await this.model('ad').where({\n                id: ['IN', ad_info.map((ele) => ele.id)]\n            }).update({\n                enabled: 0\n            });\n        }\n        //定时将长时间没收货的订单确认收货\n        const noConfirmTime = parseInt(new Date().getTime() / 1000) - 5 * 24 * 60 * 60;\n        // 5天没确认收货就自动确认\n        let noConfirmList = await this.model('order').where({\n            order_status: 301,\n            shipping_time: {\n                '<=': noConfirmTime,\n                '<>': 0\n            },\n            is_delete: 0,\n        }).select();\n        if (noConfirmList.length != 0) {\n            for (const citem of noConfirmList) {\n                let orderId = citem.id;\n                await this.model('order').where({\n                    id: orderId\n                }).update({\n                    order_status: 401,\n                    confirm_time: currentTime\n                });\n            }\n        }\n    }\n    async resetSqlAction() {\n        let time = parseInt(new Date().getTime() / 1000 + 300);\n        let info = await this.model('settings').where({id:1}).find();\n        if(info.reset == 0){\n            await this.model('settings').where({id:1}).update({countdown:time,reset:1});\n            console.log('重置了！');\n        }\n        console.log('还没到呢！');\n    }\n};"]}