<!--链接测试页面-->
<view class="page-container">
  <!-- 顶部标题 -->
  <view class="header">
    <view class="title">🔗 链接跳转测试</view>
    <view class="subtitle">测试各种跳转到订单兑换页面的方式</view>
  </view>

  <!-- 测试链接列表 -->
  <view class="link-list">
    <view 
      class="link-item" 
      wx:for="{{testLinks}}" 
      wx:key="index"
      bindtap="handleLinkTap"
      data-index="{{index}}"
    >
      <view class="link-content">
        <view class="link-title">{{item.title}}</view>
        <view class="link-desc">{{item.desc}}</view>
        <view class="link-url">{{item.url}}</view>
      </view>
      <view class="link-arrow">→</view>
    </view>
  </view>

  <!-- 特殊功能按钮 -->
  <view class="special-actions">
    <view class="section-title">🧪 特殊测试</view>
    
    <button class="action-btn primary" bindtap="generateDynamicLink">
      <text class="btn-icon">⚡</text>
      <text class="btn-text">生成动态链接</text>
    </button>
    
    <button class="action-btn secondary" bindtap="testExternalEntry">
      <text class="btn-icon">🌐</text>
      <text class="btn-text">模拟外部进入</text>
    </button>

    <button class="action-btn backend-test" bindtap="testBackendRedirect">
      <text class="btn-icon">🔗</text>
      <text class="btn-text">测试后端跳转接口</text>
    </button>
  </view>

  <!-- 说明信息 -->
  <view class="info-section">
    <view class="info-title">📋 测试说明</view>
    <view class="info-content">
      <text class="info-item">• 点击上方链接测试不同的跳转方式</text>
      <text class="info-item">• 观察控制台输出查看跳转结果</text>
      <text class="info-item">• URL Scheme 会复制到剪贴板</text>
      <text class="info-item">• 动态链接会生成带时间戳的参数</text>
      <text class="info-item">• 外部进入模拟从外部链接进入场景</text>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions">
    <button class="home-btn" bindtap="goHome">
      <text class="home-icon">🏠</text>
      <text>返回首页</text>
    </button>
  </view>
</view>
