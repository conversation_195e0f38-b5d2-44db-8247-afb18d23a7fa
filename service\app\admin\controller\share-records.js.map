{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\share-records.js"], "names": ["Base", "require", "module", "exports", "listAction", "page", "get", "limit", "keyword", "actionType", "startDate", "endDate", "promoterUserId", "console", "log", "whereCondition", "startTime", "parseInt", "Date", "getTime", "endTime", "sql", "offset", "limitSql", "records", "model", "query", "countSql", "count<PERSON><PERSON><PERSON>", "total", "formattedRecords", "map", "record", "id", "user_action", "promoterNickname", "decodeNickname", "promoter_nickname", "visitorNickname", "visitor_nickname", "shareSourceMap", "distributionChain", "push", "level", "userId", "promoter_user_id", "nickname", "mobile", "promoter_mobile", "commission", "parseFloat", "personal_commission", "parent_promoter_user_id", "parentPromoterNickname", "parent_promoter_nickname", "parent_promoter_mobile", "level1_commission", "formattedRecord", "goodsId", "goods_id", "goodsName", "goods_name", "goodsImage", "goods_image", "shareSource", "share_source", "shareSourceText", "visitTime", "visit_time", "visitTimeFormatted", "toLocaleString", "visitorUserId", "visitor_user_id", "userAction", "userActionText", "userActionBadge", "orderInfo", "orderId", "order_id", "orderSn", "order_sn", "orderAmount", "order_amount", "totalCommission", "commission_amount", "personalCommission", "level1Commission", "level2Commission", "level2_commission", "orderStatus", "order_status", "statsResult", "getShareRecordsStats", "success", "data", "stats", "error", "fail", "statsSql", "result", "totalVisits", "total_visits", "uniqueVisitors", "unique_visitors", "totalOrders", "total_orders", "browsedOnly", "totalOrderAmount", "total_order_amount", "total_commission", "conversionRate", "conversion_rate", "statsAction", "count", "totalAmountResult", "sum", "totalAmount", "totalCommissionResult", "today", "todayStart", "getFullYear", "getMonth", "getDate", "todayVisits", "where", "todayOrders", "create_time", "activePromoters", "status", "toFixed", "statsData", "total_amount", "today_visits", "today_orders", "active_promoters", "rankingAction", "type", "orderField", "ranking", "alias", "join", "table", "as", "on", "field", "order", "select", "i", "length", "rank", "total_views", "level_text", "getLevelText", "status_text", "exportAction", "message", "levelMap", "promotersAction", "promoterList", "countSelect", "promoter", "create_time_formatted", "month_commission", "avatar", "startsWith", "promostatsAction", "totalPromoters", "totalViewsResult", "totalViews", "totalOrdersResult", "total_promoters", "<PERSON><PERSON><PERSON>", "from", "toString"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;;AAEAC,OAAOC,OAAP,GAAiB,cAAcH,IAAd,CAAmB;;AAEhC;;;;AAIMI,cAAN,GAAmB;AAAA;;AAAA;AACf,gBAAI;AACA,sBAAMC,OAAO,MAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,sBAAMC,QAAQ,MAAKD,GAAL,CAAS,OAAT,KAAqB,EAAnC;AACA,sBAAME,UAAU,MAAKF,GAAL,CAAS,SAAT,KAAuB,EAAvC;AACA,sBAAMG,aAAa,MAAKH,GAAL,CAAS,YAAT,KAA0B,EAA7C,CAJA,CAIiD;AACjD,sBAAMI,YAAY,MAAKJ,GAAL,CAAS,WAAT,KAAyB,EAA3C;AACA,sBAAMK,UAAU,MAAKL,GAAL,CAAS,SAAT,KAAuB,EAAvC;AACA,sBAAMM,iBAAiB,MAAKN,GAAL,CAAS,gBAAT,KAA8B,EAArD;;AAEAO,wBAAQC,GAAR,CAAY,aAAZ,EAA2B,EAAET,IAAF,EAAQE,KAAR,EAAeC,OAAf,EAAwBC,UAAxB,EAAoCC,SAApC,EAA+CC,OAA/C,EAAwDC,cAAxD,EAA3B;;AAEA;AACA,oBAAIG,iBAAiB,KAArB;;AAEA;AACA,oBAAIH,cAAJ,EAAoB;AAChBG,sCAAmB,6BAA4BH,cAAe,EAA9D;AACH;;AAED;AACA,oBAAIJ,OAAJ,EAAa;AACTO,sCAAmB,4BAA2BP,OAAQ,4BAA2BA,OAAQ,uBAAsBA,OAAQ,KAAvH;AACH;;AAED;AACA,oBAAIE,aAAaC,OAAjB,EAA0B;AACtB,0BAAMK,YAAYC,SAAS,IAAIC,IAAJ,CAASR,SAAT,EAAoBS,OAApB,KAAgC,IAAzC,CAAlB;AACA,0BAAMC,UAAUH,SAAS,IAAIC,IAAJ,CAASP,OAAT,EAAkBQ,OAAlB,KAA8B,IAAvC,CAAhB;AACAJ,sCAAmB,6BAA4BC,SAAU,QAAOI,OAAQ,EAAxE;AACH;;AAED;AACA,oBAAIC,MAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAyCCN,cAAe;aAzC3B;;AA4CA;AACA,oBAAIN,eAAe,SAAnB,EAA8B;AAC1BY,2BAAQ,iCAAR;AACH,iBAFD,MAEO,IAAIZ,eAAe,SAAnB,EAA8B;AACjCY,2BAAQ,iCAAR;AACH;;AAEDA,uBAAQ,6BAAR;;AAEA;AACA,sBAAMC,SAAS,CAACjB,OAAO,CAAR,IAAaE,KAA5B;AACA,sBAAMgB,WAAWF,MAAO,UAASC,MAAO,KAAIf,KAAM,EAAlD;;AAEA;AACA,sBAAMiB,UAAU,MAAM,MAAKC,KAAL,GAAaC,KAAb,CAAmBH,QAAnB,CAAtB;;AAEA;AACA,sBAAMI,WAAY,kCAAiCN,GAAI,WAAvD;AACA,sBAAMO,cAAc,MAAM,MAAKH,KAAL,GAAaC,KAAb,CAAmBC,QAAnB,CAA1B;AACA,sBAAME,QAAQD,YAAY,CAAZ,EAAeC,KAA7B;;AAEAhB,wBAAQC,GAAR,CAAY,aAAZ,EAA2Be,KAA3B;;AAEA;AACA,sBAAMC,mBAAmBN,QAAQO,GAAR,CAAY,kBAAU;AAC3ClB,4BAAQC,GAAR,CAAY,OAAZ,EAAqBkB,OAAOC,EAA5B,EAAgC,OAAhC,EAAyCD,OAAOE,WAAhD;;AAEA;AACA,0BAAMC,mBAAmB,MAAKC,cAAL,CAAoBJ,OAAOK,iBAA3B,CAAzB;AACA,0BAAMC,kBAAkB,MAAKF,cAAL,CAAoBJ,OAAOO,gBAA3B,CAAxB;;AAEA;AACA,0BAAMC,iBAAiB;AACnB,kCAAU,MADS;AAEnB,kCAAU,OAFS;AAGnB,iCAAS,IAHU;AAInB,uCAAe,KAJI;AAKnB,kCAAU,IALS;AAMnB,gCAAQ;AANW,qBAAvB;;AASA;AACA,0BAAMC,oBAAoB,EAA1B;;AAEA;AACAA,sCAAkBC,IAAlB,CAAuB;AACnBC,+BAAO,CADY;AAEnBC,gCAAQZ,OAAOa,gBAFI;AAGnBC,kCAAUX,oBAAoB,OAHX;AAInBY,gCAAQf,OAAOgB,eAAP,IAA0B,EAJf;AAKnBC,oCAAYjB,OAAOE,WAAP,KAAuB,SAAvB,GAAmCgB,WAAWlB,OAAOmB,mBAAP,IAA8B,CAAzC,CAAnC,GAAiF;AAL1E,qBAAvB;;AAQA;AACA,wBAAInB,OAAOoB,uBAAP,IAAkCpB,OAAOE,WAAP,KAAuB,SAA7D,EAAwE;AACpE,8BAAMmB,yBAAyB,MAAKjB,cAAL,CAAoBJ,OAAOsB,wBAA3B,CAA/B;AACAb,0CAAkBC,IAAlB,CAAuB;AACnBC,mCAAO,CADY;AAEnBC,oCAAQZ,OAAOoB,uBAFI;AAGnBN,sCAAUO,0BAA0B,OAHjB;AAInBN,oCAAQf,OAAOuB,sBAAP,IAAiC,EAJtB;AAKnBN,wCAAYC,WAAWlB,OAAOwB,iBAAP,IAA4B,CAAvC;AALO,yBAAvB;AAOH;;AAED,0BAAMC,kBAAkB;AACpBxB,4BAAID,OAAOC,EADS;AAEpB;AACAyB,iCAAS1B,OAAO2B,QAHI;AAIpBC,mCAAW5B,OAAO6B,UAAP,IAAqB,OAJZ;AAKpBC,oCAAY9B,OAAO+B,WALC;AAMpB;AACAC,qCAAahC,OAAOiC,YAPA;AAQpBC,yCAAiB1B,eAAeR,OAAOiC,YAAtB,KAAuC,IARpC;AASpBE,mCAAWnC,OAAOoC,UATE;AAUpBC,4CAAoB,IAAInD,IAAJ,CAASc,OAAOoC,UAAP,GAAoB,IAA7B,EAAmCE,cAAnC,CAAkD,OAAlD,CAVA;AAWpB;AACAC,uCAAevC,OAAOwC,eAZF;AAapBlC,yCAAiBN,OAAOwC,eAAP,KAA2B,CAA3B,GAA+B,MAA/B,GAAyClC,mBAAmB,MAbzD;AAcpB;AACAmC,oCAAYzC,OAAOE,WAfC;AAgBpBwC,wCAAgB1C,OAAOE,WAAP,KAAuB,SAAvB,GAAmC,KAAnC,GAA2C,KAhBvC;AAiBpByC,yCAAiB3C,OAAOE,WAAP,KAAuB,SAAvB,GAAmC,SAAnC,GAA+C,MAjB5C;AAkBpB;AACAO,2CAAmBA,iBAnBC;AAoBpB;AACAmC,mCAAW5C,OAAOE,WAAP,KAAuB,SAAvB,GAAmC;AAC1C2C,qCAAS7C,OAAO8C,QAD0B;AAE1CC,qCAAS/C,OAAOgD,QAF0B;AAG1CC,yCAAa/B,WAAWlB,OAAOkD,YAAP,IAAuB,CAAlC,CAH6B;AAI1CC,6CAAiBjC,WAAWlB,OAAOoD,iBAAP,IAA4B,CAAvC,CAJyB;AAK1CC,gDAAoBnC,WAAWlB,OAAOmB,mBAAP,IAA8B,CAAzC,CALsB;AAM1CmC,8CAAkBpC,WAAWlB,OAAOwB,iBAAP,IAA4B,CAAvC,CANwB;AAO1C+B,8CAAkBrC,WAAWlB,OAAOwD,iBAAP,IAA4B,CAAvC,CAPwB;AAQ1CC,yCAAazD,OAAO0D;AARsB,yBAAnC,GASP;AA9BgB,qBAAxB;;AAiCA7E,4BAAQC,GAAR,CAAY,SAAZ,EAAuB2C,gBAAgBxB,EAAvC,EAA2C,KAA3C,EAAkDwB,gBAAgBiB,cAAlE;AACA,2BAAOjB,eAAP;AACH,iBA5EwB,CAAzB;;AA8EA;AACA,sBAAMkC,cAAc,MAAM,MAAKC,oBAAL,CAA0B7E,cAA1B,CAA1B;;AAEA,uBAAO,MAAK8E,OAAL,CAAa;AAChBC,0BAAMhE,gBADU;AAEhBD,2BAAOA,KAFS;AAGhBxB,0BAAMY,SAASZ,IAAT,CAHU;AAIhBE,2BAAOU,SAASV,KAAT,CAJS;AAKhBwF,2BAAOJ;AALS,iBAAb,CAAP;AAQH,aA7LD,CA6LE,OAAOK,KAAP,EAAc;AACZnF,wBAAQmF,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO,MAAKC,IAAL,CAAU,GAAV,EAAe,YAAf,CAAP;AACH;AAjMc;AAkMlB;;AAED;;;AAGML,wBAAN,CAA2B7E,cAA3B,EAA2C;AAAA;;AAAA;AACvC,gBAAI;AACA,sBAAMmF,WAAY;;;;;;;;;;;;;;;;wBAgBNnF,cAAe;aAhB3B;;AAmBAF,wBAAQC,GAAR,CAAY,UAAZ,EAAwBoF,QAAxB;AACA,sBAAMP,cAAc,MAAM,OAAKlE,KAAL,GAAaC,KAAb,CAAmBwE,QAAnB,CAA1B;AACA,sBAAMH,QAAQJ,YAAY,CAAZ,CAAd;AACA9E,wBAAQC,GAAR,CAAY,WAAZ,EAAyBiF,KAAzB;;AAEA,sBAAMI,SAAS;AACXC,iCAAanF,SAAS8E,MAAMM,YAAf,CADF,EAC0C;AACrDC,oCAAgBrF,SAAS8E,MAAMQ,eAAf,CAFL,EAE0C;AACrDC,iCAAavF,SAAS8E,MAAMU,YAAf,CAHF,EAG0C;AACrDC,iCAAazF,SAAS8E,MAAMM,YAAf,IAA+BpF,SAAS8E,MAAMU,YAAf,CAJjC,EAI+D;AAC1EE,sCAAkBzD,WAAW6C,MAAMa,kBAAjB,CALP,EAK6C;AACxDzB,qCAAiBjC,WAAW6C,MAAMc,gBAAjB,CANN,EAM6C;AACxDC,oCAAgB5D,WAAW6C,MAAMgB,eAAjB,CAPL,CAO6C;AAP7C,iBAAf;;AAUAlG,wBAAQC,GAAR,CAAY,YAAZ,EAA0BqF,MAA1B;AACA,uBAAOA,MAAP;AAEH,aAtCD,CAsCE,OAAOH,KAAP,EAAc;AACZnF,wBAAQmF,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO;AACHI,iCAAa,CADV;AAEHE,oCAAgB,CAFb;AAGHE,iCAAa,CAHV;AAIHE,iCAAa,CAJV;AAKHC,sCAAkB,CALf;AAMHxB,qCAAiB,CANd;AAOH2B,oCAAgB;AAPb,iBAAP;AASH;AAlDsC;AAmD1C;;AAED;;;;AAIME,eAAN,GAAoB;AAAA;;AAAA;AAChB,gBAAI;AACAnG,wBAAQC,GAAR,CAAY,UAAZ;;AAEA;AACA,sBAAMsF,cAAc,MAAM,OAAK3E,KAAL,CAAW,kBAAX,EAA+BwF,KAA/B,EAA1B;;AAEA;AACA,sBAAMT,cAAc,MAAM,OAAK/E,KAAL,CAAW,kBAAX,EAA+BwF,KAA/B,EAA1B;;AAEA;AACA,sBAAMC,oBAAoB,MAAM,OAAKzF,KAAL,CAAW,kBAAX,EAA+B0F,GAA/B,CAAmC,cAAnC,CAAhC;AACA,sBAAMC,cAAclE,WAAWgE,qBAAqB,CAAhC,CAApB;;AAEA;AACA,sBAAMG,wBAAwB,MAAM,OAAK5F,KAAL,CAAW,kBAAX,EAA+B0F,GAA/B,CAAmC,mBAAnC,CAApC;AACA,sBAAMhC,kBAAkBjC,WAAWmE,yBAAyB,CAApC,CAAxB;;AAEA;AACA,sBAAMC,QAAQ,IAAIpG,IAAJ,EAAd;AACA,sBAAMqG,aAAatG,SAAS,IAAIC,IAAJ,CAASoG,MAAME,WAAN,EAAT,EAA8BF,MAAMG,QAAN,EAA9B,EAAgDH,MAAMI,OAAN,EAAhD,EAAiEvG,OAAjE,KAA6E,IAAtF,CAAnB;AACAN,wBAAQC,GAAR,CAAY,UAAZ,EAAwByG,UAAxB,EAAoC,OAApC,EAA6C,IAAIrG,IAAJ,CAASqG,aAAa,IAAtB,CAA7C;;AAEA,sBAAMI,cAAc,MAAM,OAAKlG,KAAL,CAAW,kBAAX,EAA+BmG,KAA/B,CAAqC;AAC3DxD,gCAAY,CAAC,IAAD,EAAOmD,UAAP;AAD+C,iBAArC,EAEvBN,KAFuB,EAA1B;;AAIA,sBAAMY,cAAc,MAAM,OAAKpG,KAAL,CAAW,kBAAX,EAA+BmG,KAA/B,CAAqC;AAC3DE,iCAAa,CAAC,IAAD,EAAOP,UAAP;AAD8C,iBAArC,EAEvBN,KAFuB,EAA1B;;AAIA;AACA,sBAAMc,kBAAkB,MAAM,OAAKtG,KAAL,CAAW,oBAAX,EAAiCmG,KAAjC,CAAuC;AACjEI,4BAAQ;AADyD,iBAAvC,EAE3Bf,KAF2B,EAA9B;;AAIA;AACA,sBAAMH,iBAAiBV,cAAc,CAAd,GAAkB,CAAEI,cAAcJ,WAAf,GAA8B,GAA/B,EAAoC6B,OAApC,CAA4C,CAA5C,CAAlB,GAAmE,CAA1F;;AAEA,sBAAMC,YAAY;AACd7B,kCAAcD,WADA;AAEdK,kCAAcD,WAFA;AAGd2B,kCAAcf,YAAYa,OAAZ,CAAoB,CAApB,CAHA;AAIdpB,sCAAkB1B,gBAAgB8C,OAAhB,CAAwB,CAAxB,CAJJ;AAKdG,kCAAcT,WALA;AAMdU,kCAAcR,WANA;AAOdS,sCAAkBP,eAPJ;AAQdhB,qCAAiBD;AARH,iBAAlB;;AAWAjG,wBAAQC,GAAR,CAAY,SAAZ,EAAuBoH,SAAvB;;AAEA,uBAAO,OAAKrC,OAAL,CAAaqC,SAAb,CAAP;AAEH,aArDD,CAqDE,OAAOlC,KAAP,EAAc;AACZnF,wBAAQmF,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO,OAAKC,IAAL,CAAU,GAAV,EAAe,UAAf,CAAP;AACH;AAzDe;AA0DnB;;AAED;;;;AAIMsC,iBAAN,GAAsB;AAAA;;AAAA;AAClB,gBAAI;AACA,sBAAMC,OAAO,OAAKlI,GAAL,CAAS,MAAT,KAAoB,QAAjC,CADA,CAC2C;AAC3C,sBAAMC,QAAQ,OAAKD,GAAL,CAAS,OAAT,KAAqB,EAAnC;;AAEAO,wBAAQC,GAAR,CAAY,UAAZ,EAAwB,EAAE0H,IAAF,EAAQjI,KAAR,EAAxB;;AAEA,oBAAIkI,aAAa,cAAjB;;AAEA,oBAAID,SAAS,QAAb,EAAuB;AACnBC,iCAAa,kBAAb,CADmB,CACc;AACpC,iBAFD,MAEO,IAAID,SAAS,YAAb,EAA2B;AAC9BC,iCAAa,kBAAb;AACH,iBAFM,MAEA,IAAID,SAAS,QAAb,EAAuB;AAC1BC,iCAAa,aAAb;AACH;;AAED,sBAAMC,UAAU,MAAM,OAAKjH,KAAL,CAAW,oBAAX,EACjBkH,KADiB,CACX,IADW,EAEjBC,IAFiB,CAEZ;AACFC,2BAAO,MADL;AAEFD,0BAAM,MAFJ;AAGFE,wBAAI,GAHF;AAIFC,wBAAI,CAAC,YAAD,EAAe,MAAf;AAJF,iBAFY,EAQjBnB,KARiB,CAQX;AACH,iCAAa;AADV,iBARW,EAWjBoB,KAXiB,CAWV,sCAXU,EAYjBC,KAZiB,CAYV,MAAKR,UAAW,OAZN,EAajBlI,KAbiB,CAaXA,KAbW,EAcjB2I,MAdiB,EAAtB;;AAgBA;AACA,qBAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAIT,QAAQU,MAA5B,EAAoCD,GAApC,EAAyC;AACrCT,4BAAQS,CAAR,EAAWE,IAAX,GAAkBF,IAAI,CAAtB;AACAT,4BAAQS,CAAR,EAAWG,WAAX,GAAyBrI,SAASyH,QAAQS,CAAR,EAAWG,WAAX,IAA0B,CAAnC,CAAzB;AACAZ,4BAAQS,CAAR,EAAW1C,YAAX,GAA0BxF,SAASyH,QAAQS,CAAR,EAAW1C,YAAX,IAA2B,CAApC,CAA1B;AACAiC,4BAAQS,CAAR,EAAWtC,gBAAX,GAA8B3D,WAAWwF,QAAQS,CAAR,EAAWtC,gBAAX,IAA+B,CAA1C,CAA9B;AACA6B,4BAAQS,CAAR,EAAWI,UAAX,GAAwB,OAAKC,YAAL,CAAkBd,QAAQS,CAAR,EAAWxG,KAA7B,CAAxB;AACA+F,4BAAQS,CAAR,EAAWM,WAAX,GAAyBf,QAAQS,CAAR,EAAWnB,MAAX,KAAsB,CAAtB,GAA0B,IAA1B,GAAiC,IAA1D;;AAEA;AACAU,4BAAQS,CAAR,EAAWrG,QAAX,GAAsB,OAAKV,cAAL,CAAoBsG,QAAQS,CAAR,EAAWrG,QAA/B,CAAtB;AACH;;AAEDjC,wBAAQC,GAAR,CAAY,UAAZ,EAAwB4H,QAAQU,MAAhC;;AAEA,uBAAO,OAAKvD,OAAL,CAAa6C,OAAb,CAAP;AAEH,aAjDD,CAiDE,OAAO1C,KAAP,EAAc;AACZnF,wBAAQmF,KAAR,CAAc,YAAd,EAA4BA,KAA5B;AACA,uBAAO,OAAKC,IAAL,CAAU,GAAV,EAAe,SAAf,CAAP;AACH;AArDiB;AAsDrB;;AAED;;;;AAIMyD,gBAAN,GAAqB;AAAA;;AAAA;AACjB,gBAAI;AACA;AACA;AACA,uBAAO,OAAK7D,OAAL,CAAa,EAAE8D,SAAS,SAAX,EAAb,CAAP;AAEH,aALD,CAKE,OAAO3D,KAAP,EAAc;AACZnF,wBAAQmF,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,OAAKC,IAAL,CAAU,GAAV,EAAe,MAAf,CAAP;AACH;AATgB;AAUpB;;AAED;;;;AAIAuD,iBAAa7G,KAAb,EAAoB;AAChB,cAAMiH,WAAW;AACb,eAAG,IADU;AAEb,eAAG,IAFU;AAGb,eAAG,IAHU;AAIb,eAAG;AAJU,SAAjB;AAMA,eAAOA,SAASjH,KAAT,KAAmB,IAA1B;AACH;;AAED;;;;AAIMkH,mBAAN,GAAwB;AAAA;;AAAA;AACpB,gBAAI;AACA,sBAAMxJ,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,sBAAMC,QAAQ,OAAKD,GAAL,CAAS,OAAT,KAAqB,EAAnC;AACA,sBAAME,UAAU,OAAKF,GAAL,CAAS,SAAT,KAAuB,EAAvC;AACA,sBAAMqC,QAAQ,OAAKrC,GAAL,CAAS,OAAT,KAAqB,EAAnC;AACA,sBAAM0H,SAAS,OAAK1H,GAAL,CAAS,QAAT,KAAsB,EAArC;;AAEAO,wBAAQC,GAAR,CAAY,YAAZ,EAA0B,EAAET,IAAF,EAAQE,KAAR,EAAeC,OAAf,EAAwBmC,KAAxB,EAA+BqF,MAA/B,EAA1B;;AAEA;AACA,oBAAIjH,iBAAiB,EAArB;;AAEA,oBAAIP,OAAJ,EAAa;AACTO,mCAAe,YAAf,IAA+B,CAAC,MAAD,EAAU,IAAGP,OAAQ,GAArB,CAA/B;AACH;;AAED,oBAAImC,KAAJ,EAAW;AACP5B,mCAAe,UAAf,IAA6B4B,KAA7B;AACH;;AAED,oBAAIqF,WAAW,EAAf,EAAmB;AACfjH,mCAAe,WAAf,IAA8BiH,MAA9B;AACH;;AAED;AACA,sBAAM8B,eAAe,MAAM,OAAKrI,KAAL,CAAW,oBAAX,EACtBkH,KADsB,CAChB,IADgB,EAEtBC,IAFsB,CAEjB;AACFC,2BAAO,MADL;AAEFD,0BAAM,MAFJ;AAGFE,wBAAI,GAHF;AAIFC,wBAAI,CAAC,YAAD,EAAe,MAAf;AAJF,iBAFiB,EAQtBnB,KARsB,CAQhB7G,cARgB,EAStBiI,KATsB,CAShB,sCATgB,EAUtBC,KAVsB,CAUhB,qBAVgB,EAWtB5I,IAXsB,CAWjBA,IAXiB,EAWXE,KAXW,EAYtBwJ,WAZsB,EAA3B;;AAcAlJ,wBAAQC,GAAR,CAAY,YAAZ,EAA0BgJ,aAAa7C,KAAvC;;AAEA;AACA,qBAAK,IAAI+C,QAAT,IAAqBF,aAAahE,IAAlC,EAAwC;AACpC;AACAkE,6BAASlH,QAAT,GAAoB,OAAKV,cAAL,CAAoB4H,SAASlH,QAA7B,CAApB;;AAEA;AACAkH,6BAASC,qBAAT,GAAiC,IAAI/I,IAAJ,CAAS8I,SAASlC,WAAT,GAAuB,IAAhC,EAAsCxD,cAAtC,CAAqD,OAArD,CAAjC;;AAEA;AACA0F,6BAASnD,gBAAT,GAA4B3D,WAAW8G,SAASnD,gBAAT,IAA6B,CAAxC,CAA5B;AACAmD,6BAASE,gBAAT,GAA4BhH,WAAW8G,SAASE,gBAAT,IAA6B,CAAxC,CAA5B;;AAEA;AACAF,6BAAST,UAAT,GAAsB,OAAKC,YAAL,CAAkBQ,SAASrH,KAA3B,CAAtB;AACAqH,6BAASP,WAAT,GAAuBO,SAAShC,MAAT,KAAoB,CAApB,GAAwB,IAAxB,GAA+B,IAAtD;;AAEA;AACA,wBAAI,CAACgC,SAASG,MAAV,IAAoBH,SAASG,MAAT,KAAoB,EAAxC,IAA8CH,SAASG,MAAT,CAAgBC,UAAhB,CAA2B,WAA3B,CAAlD,EAA2F;AACvF;AACAJ,iCAASG,MAAT,GAAkB,2DAAlB;AACH,qBAHD,MAGO,IAAI,CAACH,SAASG,MAAT,CAAgBC,UAAhB,CAA2B,SAA3B,CAAD,IAA0C,CAACJ,SAASG,MAAT,CAAgBC,UAAhB,CAA2B,UAA3B,CAA/C,EAAuF;AAC1F;AACAJ,iCAASG,MAAT,GAAmB,yBAAwBH,SAASG,MAAO,EAA3D;AACH;AACJ;;AAED,uBAAO,OAAKtE,OAAL,CAAa;AAChBC,0BAAMgE,aAAahE,IADH;AAEhBjE,2BAAOiI,aAAa7C,KAFJ;AAGhB5G,0BAAMY,SAASZ,IAAT,CAHU;AAIhBE,2BAAOU,SAASV,KAAT;AAJS,iBAAb,CAAP;AAOH,aA1ED,CA0EE,OAAOyF,KAAP,EAAc;AACZnF,wBAAQmF,KAAR,CAAc,YAAd,EAA4BA,KAA5B;AACA,uBAAO,OAAKC,IAAL,CAAU,GAAV,EAAe,WAAf,CAAP;AACH;AA9EmB;AA+EvB;;AAED;;;;AAIMoE,oBAAN,GAAyB;AAAA;;AAAA;AACrB,gBAAI;AACAxJ,wBAAQC,GAAR,CAAY,WAAZ;;AAEA;AACA,sBAAMwJ,iBAAiB,MAAM,OAAK7I,KAAL,CAAW,oBAAX,EAAiCwF,KAAjC,EAA7B;;AAEA;AACA,sBAAMc,kBAAkB,MAAM,OAAKtG,KAAL,CAAW,oBAAX,EAAiCmG,KAAjC,CAAuC;AACjEI,4BAAQ;AADyD,iBAAvC,EAE3Bf,KAF2B,EAA9B;;AAIA;AACA,sBAAMsD,mBAAmB,MAAM,OAAK9I,KAAL,CAAW,oBAAX,EAAiC0F,GAAjC,CAAqC,aAArC,CAA/B;AACA,sBAAMqD,aAAavJ,SAASsJ,oBAAoB,CAA7B,CAAnB;;AAEA;AACA,sBAAME,oBAAoB,MAAM,OAAKhJ,KAAL,CAAW,oBAAX,EAAiC0F,GAAjC,CAAqC,cAArC,CAAhC;AACA,sBAAMX,cAAcvF,SAASwJ,qBAAqB,CAA9B,CAApB;;AAEA;AACA,sBAAMpD,wBAAwB,MAAM,OAAK5F,KAAL,CAAW,oBAAX,EAAiC0F,GAAjC,CAAqC,kBAArC,CAApC;AACA,sBAAMhC,kBAAkBjC,WAAWmE,yBAAyB,CAApC,CAAxB;;AAEA,sBAAMa,YAAY;AACdwC,qCAAiBJ,cADH;AAEdhC,sCAAkBP,eAFJ;AAGduB,iCAAakB,UAHC;AAId/D,kCAAcD,WAJA;AAKdK,sCAAkB1B,gBAAgB8C,OAAhB,CAAwB,CAAxB;AALJ,iBAAlB;;AAQApH,wBAAQC,GAAR,CAAY,UAAZ,EAAwBoH,SAAxB;;AAEA,uBAAO,OAAKrC,OAAL,CAAaqC,SAAb,CAAP;AAEH,aAnCD,CAmCE,OAAOlC,KAAP,EAAc;AACZnF,wBAAQmF,KAAR,CAAc,cAAd,EAA8BA,KAA9B;AACA,uBAAO,OAAKC,IAAL,CAAU,GAAV,EAAe,UAAf,CAAP;AACH;AAvCoB;AAwCxB;;AAED;;;;AAIA7D,mBAAeU,QAAf,EAAyB;AACrB,YAAI,CAACA,QAAL,EAAe,OAAOA,QAAP;AACf,YAAI;AACA,mBAAO6H,OAAOC,IAAP,CAAY9H,QAAZ,EAAsB,QAAtB,EAAgC+H,QAAhC,CAAyC,MAAzC,CAAP;AACH,SAFD,CAEE,OAAO7E,KAAP,EAAc;AACZnF,oBAAQC,GAAR,CAAY,SAAZ,EAAuBkF,KAAvB;AACA,mBAAOlD,QAAP,CAFY,CAEK;AACpB;AACJ;AA3iB+B,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\share-records.js", "sourcesContent": ["const Base = require('./base.js');\n\nmodule.exports = class extends Base {\n    \n    /**\n     * 获取分享记录列表（区分浏览和下单）\n     * GET /admin/share-records/list\n     */\n    async listAction() {\n        try {\n            const page = this.get('page') || 1;\n            const limit = this.get('limit') || 20;\n            const keyword = this.get('keyword') || '';\n            const actionType = this.get('actionType') || ''; // all, browsed, ordered\n            const startDate = this.get('startDate') || '';\n            const endDate = this.get('endDate') || '';\n            const promoterUserId = this.get('promoterUserId') || '';\n\n            console.log('获取分享记录列表参数:', { page, limit, keyword, actionType, startDate, endDate, promoterUserId });\n\n            // 构建查询条件\n            let whereCondition = '1=1';\n\n            // 推广员筛选\n            if (promoterUserId) {\n                whereCondition += ` AND v.promoter_user_id = ${promoterUserId}`;\n            }\n\n            // 关键词搜索（推广员昵称、访问者昵称或商品名称）\n            if (keyword) {\n                whereCondition += ` AND (pu.nickname LIKE '%${keyword}%' OR vu.nickname LIKE '%${keyword}%' OR g.name LIKE '%${keyword}%')`;\n            }\n\n            // 日期范围筛选\n            if (startDate && endDate) {\n                const startTime = parseInt(new Date(startDate).getTime() / 1000);\n                const endTime = parseInt(new Date(endDate).getTime() / 1000);\n                whereCondition += ` AND v.visit_time BETWEEN ${startTime} AND ${endTime}`;\n            }\n\n            // 构建主查询SQL - 包含分销链信息\n            let sql = `\n                SELECT\n                    v.id,\n                    v.promoter_user_id,\n                    v.visitor_user_id,\n                    v.goods_id,\n                    v.visit_time,\n                    v.share_source,\n                    v.ip_address,\n                    v.is_new_visitor,\n                    CASE\n                        WHEN o.id IS NOT NULL THEN 'ordered'\n                        ELSE 'browsed'\n                    END as user_action,\n                    o.id as order_record_id,\n                    o.order_id,\n                    o.order_sn,\n                    o.order_amount,\n                    o.commission_amount,\n                    o.personal_commission,\n                    o.level1_commission,\n                    o.level2_commission,\n                    o.parent_promoter_user_id,\n                    o.status as order_status,\n                    g.name as goods_name,\n                    g.list_pic_url as goods_image,\n                    pu.nickname as promoter_nickname,\n                    pu.mobile as promoter_mobile,\n                    vu.nickname as visitor_nickname,\n                    vu.mobile as visitor_mobile,\n                    ppu.nickname as parent_promoter_nickname,\n                    ppu.mobile as parent_promoter_mobile\n                FROM hiolabs_promotion_visits v\n                LEFT JOIN hiolabs_promotion_orders o\n                    ON v.visitor_user_id = o.buyer_user_id\n                    AND v.goods_id = o.goods_id\n                    AND v.promoter_user_id = o.promoter_user_id\n                LEFT JOIN hiolabs_goods g ON v.goods_id = g.id\n                LEFT JOIN hiolabs_user pu ON v.promoter_user_id = pu.id\n                LEFT JOIN hiolabs_user vu ON v.visitor_user_id = vu.id\n                LEFT JOIN hiolabs_user ppu ON o.parent_promoter_user_id = ppu.id\n                WHERE ${whereCondition}\n            `;\n\n            // 根据行为类型筛选\n            if (actionType === 'browsed') {\n                sql += ` HAVING user_action = 'browsed'`;\n            } else if (actionType === 'ordered') {\n                sql += ` HAVING user_action = 'ordered'`;\n            }\n\n            sql += ` ORDER BY v.visit_time DESC`;\n\n            // 分页查询\n            const offset = (page - 1) * limit;\n            const limitSql = sql + ` LIMIT ${offset}, ${limit}`;\n\n            // 执行查询\n            const records = await this.model().query(limitSql);\n\n            // 查询总数\n            const countSql = `SELECT COUNT(*) as total FROM (${sql}) as temp`;\n            const countResult = await this.model().query(countSql);\n            const total = countResult[0].total;\n\n            console.log('查询到的分享记录数量:', total);\n\n            // 格式化数据\n            const formattedRecords = records.map(record => {\n                console.log('处理记录:', record.id, '用户行为:', record.user_action);\n\n                // 推广员名称和访问者名称base64解码\n                const promoterNickname = this.decodeNickname(record.promoter_nickname);\n                const visitorNickname = this.decodeNickname(record.visitor_nickname);\n\n                // 分享来源中文显示\n                const shareSourceMap = {\n                    'friend': '好友分享',\n                    'qrcode': '二维码分享',\n                    'other': '其他',\n                    'miniprogram': '小程序',\n                    'wechat': '微信',\n                    'link': '链接'\n                };\n\n                // 构建分销链信息\n                const distributionChain = [];\n\n                // 一级推广员（直接分享者）\n                distributionChain.push({\n                    level: 1,\n                    userId: record.promoter_user_id,\n                    nickname: promoterNickname || '未知推广员',\n                    mobile: record.promoter_mobile || '',\n                    commission: record.user_action === 'ordered' ? parseFloat(record.personal_commission || 0) : 0\n                });\n\n                // 二级推广员（上级推广员）\n                if (record.parent_promoter_user_id && record.user_action === 'ordered') {\n                    const parentPromoterNickname = this.decodeNickname(record.parent_promoter_nickname);\n                    distributionChain.push({\n                        level: 2,\n                        userId: record.parent_promoter_user_id,\n                        nickname: parentPromoterNickname || '未知推广员',\n                        mobile: record.parent_promoter_mobile || '',\n                        commission: parseFloat(record.level1_commission || 0)\n                    });\n                }\n\n                const formattedRecord = {\n                    id: record.id,\n                    // 商品信息\n                    goodsId: record.goods_id,\n                    goodsName: record.goods_name || '商品已删除',\n                    goodsImage: record.goods_image,\n                    // 分享信息\n                    shareSource: record.share_source,\n                    shareSourceText: shareSourceMap[record.share_source] || '其他',\n                    visitTime: record.visit_time,\n                    visitTimeFormatted: new Date(record.visit_time * 1000).toLocaleString('zh-CN'),\n                    // 访问者信息\n                    visitorUserId: record.visitor_user_id,\n                    visitorNickname: record.visitor_user_id === 0 ? '匿名用户' : (visitorNickname || '未知用户'),\n                    // 用户行为\n                    userAction: record.user_action,\n                    userActionText: record.user_action === 'ordered' ? '已下单' : '仅浏览',\n                    userActionBadge: record.user_action === 'ordered' ? 'success' : 'info',\n                    // 分销链\n                    distributionChain: distributionChain,\n                    // 订单信息\n                    orderInfo: record.user_action === 'ordered' ? {\n                        orderId: record.order_id,\n                        orderSn: record.order_sn,\n                        orderAmount: parseFloat(record.order_amount || 0),\n                        totalCommission: parseFloat(record.commission_amount || 0),\n                        personalCommission: parseFloat(record.personal_commission || 0),\n                        level1Commission: parseFloat(record.level1_commission || 0),\n                        level2Commission: parseFloat(record.level2_commission || 0),\n                        orderStatus: record.order_status\n                    } : null\n                };\n\n                console.log('格式化后记录:', formattedRecord.id, '行为:', formattedRecord.userActionText);\n                return formattedRecord;\n            });\n\n            // 获取统计数据\n            const statsResult = await this.getShareRecordsStats(whereCondition);\n\n            return this.success({\n                data: formattedRecords,\n                total: total,\n                page: parseInt(page),\n                limit: parseInt(limit),\n                stats: statsResult\n            });\n            \n        } catch (error) {\n            console.error('获取分享记录列表失败:', error);\n            return this.fail(500, '获取分享记录列表失败');\n        }\n    }\n\n    /**\n     * 获取分享记录统计数据\n     */\n    async getShareRecordsStats(whereCondition) {\n        try {\n            const statsSql = `\n                SELECT\n                    COUNT(v.id) as total_visits,\n                    COUNT(DISTINCT v.visitor_user_id) as unique_visitors,\n                    COUNT(o.id) as total_orders,\n                    COALESCE(SUM(o.order_amount), 0) as total_order_amount,\n                    COALESCE(SUM(o.commission_amount), 0) as total_commission,\n                    ROUND(COUNT(o.id) * 100.0 / COUNT(v.id), 2) as conversion_rate\n                FROM hiolabs_promotion_visits v\n                LEFT JOIN hiolabs_promotion_orders o\n                    ON v.visitor_user_id = o.buyer_user_id\n                    AND v.goods_id = o.goods_id\n                    AND v.promoter_user_id = o.promoter_user_id\n                LEFT JOIN hiolabs_goods g ON v.goods_id = g.id\n                LEFT JOIN hiolabs_user pu ON v.promoter_user_id = pu.id\n                LEFT JOIN hiolabs_user vu ON v.visitor_user_id = vu.id\n                WHERE ${whereCondition}\n            `;\n\n            console.log('统计查询SQL:', statsSql);\n            const statsResult = await this.model().query(statsSql);\n            const stats = statsResult[0];\n            console.log('统计查询原始结果:', stats);\n\n            const result = {\n                totalVisits: parseInt(stats.total_visits),           // 总访问次数\n                uniqueVisitors: parseInt(stats.unique_visitors),     // 独立访客数\n                totalOrders: parseInt(stats.total_orders),           // 总下单数\n                browsedOnly: parseInt(stats.total_visits) - parseInt(stats.total_orders), // 仅浏览数\n                totalOrderAmount: parseFloat(stats.total_order_amount), // 总订单金额\n                totalCommission: parseFloat(stats.total_commission),    // 总佣金\n                conversionRate: parseFloat(stats.conversion_rate)       // 转化率\n            };\n\n            console.log('统计查询格式化结果:', result);\n            return result;\n\n        } catch (error) {\n            console.error('获取统计数据失败:', error);\n            return {\n                totalVisits: 0,\n                uniqueVisitors: 0,\n                totalOrders: 0,\n                browsedOnly: 0,\n                totalOrderAmount: 0,\n                totalCommission: 0,\n                conversionRate: 0\n            };\n        }\n    }\n    \n    /**\n     * 获取分享统计数据\n     * GET /admin/share-records/stats\n     */\n    async statsAction() {\n        try {\n            console.log('获取推广统计数据');\n\n            // 总访问记录数\n            const totalVisits = await this.model('promotion_visits').count();\n\n            // 总推广订单数\n            const totalOrders = await this.model('promotion_orders').count();\n\n            // 总订单金额\n            const totalAmountResult = await this.model('promotion_orders').sum('order_amount');\n            const totalAmount = parseFloat(totalAmountResult || 0);\n\n            // 总佣金\n            const totalCommissionResult = await this.model('promotion_orders').sum('commission_amount');\n            const totalCommission = parseFloat(totalCommissionResult || 0);\n\n            // 今日统计 - 修复时间戳计算\n            const today = new Date();\n            const todayStart = parseInt(new Date(today.getFullYear(), today.getMonth(), today.getDate()).getTime() / 1000);\n            console.log('今日开始时间戳:', todayStart, '对应时间:', new Date(todayStart * 1000));\n\n            const todayVisits = await this.model('promotion_visits').where({\n                visit_time: ['>=', todayStart]\n            }).count();\n\n            const todayOrders = await this.model('promotion_orders').where({\n                create_time: ['>=', todayStart]\n            }).count();\n\n            // 活跃推广员数量\n            const activePromoters = await this.model('personal_promoters').where({\n                status: 1\n            }).count();\n            \n            // 转化率\n            const conversionRate = totalVisits > 0 ? ((totalOrders / totalVisits) * 100).toFixed(2) : 0;\n\n            const statsData = {\n                total_visits: totalVisits,\n                total_orders: totalOrders,\n                total_amount: totalAmount.toFixed(2),\n                total_commission: totalCommission.toFixed(2),\n                today_visits: todayVisits,\n                today_orders: todayOrders,\n                active_promoters: activePromoters,\n                conversion_rate: conversionRate\n            };\n            \n            console.log('分享统计数据:', statsData);\n            \n            return this.success(statsData);\n            \n        } catch (error) {\n            console.error('获取分享统计数据失败:', error);\n            return this.fail(500, '获取统计数据失败');\n        }\n    }\n    \n    /**\n     * 获取分享排行榜\n     * GET /admin/share-records/ranking\n     */\n    async rankingAction() {\n        try {\n            const type = this.get('type') || 'orders'; // orders, amount, commission\n            const limit = this.get('limit') || 10;\n            \n            console.log('获取分享排行榜:', { type, limit });\n            \n            let orderField = 'total_orders';\n\n            if (type === 'amount') {\n                orderField = 'total_commission'; // 按佣金排序，因为没有单独的金额字段\n            } else if (type === 'commission') {\n                orderField = 'total_commission';\n            } else if (type === 'visits') {\n                orderField = 'total_views';\n            }\n            \n            const ranking = await this.model('personal_promoters')\n                .alias('pp')\n                .join({\n                    table: 'user',\n                    join: 'left',\n                    as: 'u',\n                    on: ['pp.user_id', 'u.id']\n                })\n                .where({\n                    'pp.status': 1\n                })\n                .field(`pp.*, u.nickname, u.mobile, u.avatar`)\n                .order(`pp.${orderField} DESC`)\n                .limit(limit)\n                .select();\n            \n            // 格式化数据\n            for (let i = 0; i < ranking.length; i++) {\n                ranking[i].rank = i + 1;\n                ranking[i].total_views = parseInt(ranking[i].total_views || 0);\n                ranking[i].total_orders = parseInt(ranking[i].total_orders || 0);\n                ranking[i].total_commission = parseFloat(ranking[i].total_commission || 0);\n                ranking[i].level_text = this.getLevelText(ranking[i].level);\n                ranking[i].status_text = ranking[i].status === 1 ? '正常' : '禁用';\n\n                // 推广员名称base64解码\n                ranking[i].nickname = this.decodeNickname(ranking[i].nickname);\n            }\n            \n            console.log('分享排行榜数据:', ranking.length);\n            \n            return this.success(ranking);\n            \n        } catch (error) {\n            console.error('获取分享排行榜失败:', error);\n            return this.fail(500, '获取排行榜失败');\n        }\n    }\n    \n    /**\n     * 导出推广记录\n     * GET /admin/share-records/export\n     */\n    async exportAction() {\n        try {\n            // 这里可以实现导出Excel功能\n            // 暂时返回成功消息\n            return this.success({ message: '导出功能开发中' });\n\n        } catch (error) {\n            console.error('导出推广记录失败:', error);\n            return this.fail(500, '导出失败');\n        }\n    }\n\n    /**\n     * 获取推广员等级文本\n     * @param {number} level 等级\n     */\n    getLevelText(level) {\n        const levelMap = {\n            1: '新手',\n            2: '优秀',\n            3: '金牌',\n            4: '钻石'\n        };\n        return levelMap[level] || '未知';\n    }\n\n    /**\n     * 获取推广员列表\n     * GET /admin/share-records/promoters\n     */\n    async promotersAction() {\n        try {\n            const page = this.get('page') || 1;\n            const limit = this.get('limit') || 20;\n            const keyword = this.get('keyword') || '';\n            const level = this.get('level') || '';\n            const status = this.get('status') || '';\n\n            console.log('获取推广员列表参数:', { page, limit, keyword, level, status });\n\n            // 构建查询条件\n            let whereCondition = {};\n\n            if (keyword) {\n                whereCondition['u.nickname'] = ['like', `%${keyword}%`];\n            }\n\n            if (level) {\n                whereCondition['pp.level'] = level;\n            }\n\n            if (status !== '') {\n                whereCondition['pp.status'] = status;\n            }\n\n            // 查询推广员列表\n            const promoterList = await this.model('personal_promoters')\n                .alias('pp')\n                .join({\n                    table: 'user',\n                    join: 'left',\n                    as: 'u',\n                    on: ['pp.user_id', 'u.id']\n                })\n                .where(whereCondition)\n                .field('pp.*, u.nickname, u.mobile, u.avatar')\n                .order('pp.create_time DESC')\n                .page(page, limit)\n                .countSelect();\n\n            console.log('查询到的推广员数量:', promoterList.count);\n\n            // 格式化推广员数据\n            for (let promoter of promoterList.data) {\n                // 推广员名称base64解码\n                promoter.nickname = this.decodeNickname(promoter.nickname);\n\n                // 格式化时间\n                promoter.create_time_formatted = new Date(promoter.create_time * 1000).toLocaleString('zh-CN');\n\n                // 格式化数值\n                promoter.total_commission = parseFloat(promoter.total_commission || 0);\n                promoter.month_commission = parseFloat(promoter.month_commission || 0);\n\n                // 等级文本\n                promoter.level_text = this.getLevelText(promoter.level);\n                promoter.status_text = promoter.status === 1 ? '正常' : '禁用';\n\n                // 处理头像URL\n                if (!promoter.avatar || promoter.avatar === '' || promoter.avatar.startsWith('wxfile://')) {\n                    // 如果没有头像或者是微信临时文件，使用默认头像\n                    promoter.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';\n                } else if (!promoter.avatar.startsWith('http://') && !promoter.avatar.startsWith('https://')) {\n                    // 如果是相对路径，补充完整的域名\n                    promoter.avatar = `https://ht.rxkjsdj.com${promoter.avatar}`;\n                }\n            }\n\n            return this.success({\n                data: promoterList.data,\n                total: promoterList.count,\n                page: parseInt(page),\n                limit: parseInt(limit)\n            });\n\n        } catch (error) {\n            console.error('获取推广员列表失败:', error);\n            return this.fail(500, '获取推广员列表失败');\n        }\n    }\n\n    /**\n     * 获取推广员统计数据\n     * GET /admin/share-records/promostats\n     */\n    async promostatsAction() {\n        try {\n            console.log('获取推广员统计数据');\n\n            // 总推广员数\n            const totalPromoters = await this.model('personal_promoters').count();\n\n            // 活跃推广员数量\n            const activePromoters = await this.model('personal_promoters').where({\n                status: 1\n            }).count();\n\n            // 总浏览次数\n            const totalViewsResult = await this.model('personal_promoters').sum('total_views');\n            const totalViews = parseInt(totalViewsResult || 0);\n\n            // 总成交次数\n            const totalOrdersResult = await this.model('personal_promoters').sum('total_orders');\n            const totalOrders = parseInt(totalOrdersResult || 0);\n\n            // 总佣金\n            const totalCommissionResult = await this.model('personal_promoters').sum('total_commission');\n            const totalCommission = parseFloat(totalCommissionResult || 0);\n\n            const statsData = {\n                total_promoters: totalPromoters,\n                active_promoters: activePromoters,\n                total_views: totalViews,\n                total_orders: totalOrders,\n                total_commission: totalCommission.toFixed(2)\n            };\n\n            console.log('推广员统计数据:', statsData);\n\n            return this.success(statsData);\n\n        } catch (error) {\n            console.error('获取推广员统计数据失败:', error);\n            return this.fail(500, '获取统计数据失败');\n        }\n    }\n\n    /**\n     * Base64解码昵称\n     * @param {string} nickname base64编码的昵称\n     */\n    decodeNickname(nickname) {\n        if (!nickname) return nickname;\n        try {\n            return Buffer.from(nickname, 'base64').toString('utf8');\n        } catch (error) {\n            console.log('昵称解码失败:', error);\n            return nickname; // 如果解码失败，返回原值\n        }\n    }\n};\n"]}