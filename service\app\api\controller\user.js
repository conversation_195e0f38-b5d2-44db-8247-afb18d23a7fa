function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

const Base = require('./base.js');
const fs = require('fs');
const _ = require('lodash');
const moment = require('moment');

module.exports = class extends Base {

    /**
     * 获取用户积分信息
     * GET /api/user/points
     */
    pointsAction() {
        var _this = this;

        return _asyncToGenerator(function* () {
            try {
                const userId = yield _this.getLoginUserId();

                console.log('=== 获取用户积分信息（已移除积分系统，返回默认值）===');
                console.log('用户ID:', userId);

                // 积分系统已移除，直接返回默认值以保持API兼容性
                const pointsInfo = {
                    points: 0,
                    totalPoints: 0,
                    availablePoints: 0,
                    usedPoints: 0
                };

                console.log('返回默认积分信息:', pointsInfo);
                return _this.success(pointsInfo);
            } catch (error) {
                console.error('获取用户积分信息失败:', error);
                return _this.fail('获取积分信息失败: ' + error.message);
            }
        })();
    }
};