module.exports = class extends think.Controller {
	async __before() {
		// 根据token值获取用户id
		const token = this.ctx.header['x-hioshop-token'] || this.ctx.header['X-Hioshop-Token'] || '';
		const tokenSerivce = think.service('token', 'api');
		think.userId = await tokenSerivce.getUserId(token);

		// 只在签到相关请求时打印调试信息
		if (this.ctx.url.includes('/signin/')) {
			console.log('=== Base控制器Token验证 ===');
			console.log('URL:', this.ctx.url);
			console.log('最终userId:', think.userId);
		}
	}
	/**
	 * 获取时间戳
	 * @returns {Number}
	 */
	getTime() {
		return parseInt(Date.now() / 1000);
	}
	/**
	 * 获取当前登录用户的id
	 * @returns {*}
	 */
	async getLoginUserId() {
		const token = this.ctx.header['x-hioshop-token'] || this.ctx.header['X-Hioshop-Token'] || '';
		const tokenSerivce = think.service('token', 'api');
		return await tokenSerivce.getUserId(token);
	}
};
