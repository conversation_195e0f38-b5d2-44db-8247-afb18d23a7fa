{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js!D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Test\\FlashSaleApiTest.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Test\\FlashSaleApiTest.vue", "mtime": 1753712463451}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\babel.config.js", "mtime": 1717470108000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA;EACAA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAEA;cACA;cAAA;cAAA,OAEA;YAAA;cAAAC;cAEA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAEA;cACA;cAAA;cAAA,OAEA;YAAA;cAAAD;cAEA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;IAEAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;cAEA;cACA;cAAA;cAAA,OAEA;YAAA;cAAAF;cAEA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA", "names": ["name", "data", "testResult", "errorMessage", "currentToken", "baseURL", "mounted", "methods", "testTimeSlots", "response", "testStatistics", "testProducts"], "sourceRoot": "src/components/Test", "sources": ["FlashSaleApiTest.vue"], "sourcesContent": ["<template>\n  <div class=\"api-test-container\">\n    <h2>秒杀API测试</h2>\n    \n    <div class=\"test-section\">\n      <h3>API测试结果</h3>\n      <div class=\"test-buttons\">\n        <el-button @click=\"testTimeSlots\" type=\"primary\">测试时段列表API</el-button>\n        <el-button @click=\"testStatistics\" type=\"success\">测试统计数据API</el-button>\n        <el-button @click=\"testProducts\" type=\"info\">测试商品列表API</el-button>\n      </div>\n      \n      <div v-if=\"errorMessage\" class=\"error-message\">\n        <h4>错误信息:</h4>\n        <pre>{{ errorMessage }}</pre>\n      </div>\n      \n      <div v-if=\"testResult\" class=\"test-result\">\n        <h4>测试结果:</h4>\n        <pre>{{ testResult }}</pre>\n      </div>\n    </div>\n    \n    <div class=\"debug-section\">\n      <h3>调试信息</h3>\n      <p><strong>当前Token:</strong> {{ currentToken || '无' }}</p>\n      <p><strong>API Base URL:</strong> {{ baseURL }}</p>\n      <p><strong>完整URL示例:</strong> {{ baseURL }}flash-sale/time-slots</p>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FlashSaleApiTest',\n  data() {\n    return {\n      testResult: '',\n      errorMessage: '',\n      currentToken: '',\n      baseURL: ''\n    }\n  },\n  \n  mounted() {\n    this.currentToken = localStorage.getItem('token') || '';\n    this.baseURL = this.axios.defaults.baseURL || '';\n  },\n  \n  methods: {\n    async testTimeSlots() {\n      try {\n        this.errorMessage = '';\n        this.testResult = '正在测试时段列表API...';\n        \n        const response = await this.axios.get('flash-sale/time-slots');\n        \n        this.testResult = JSON.stringify(response.data, null, 2);\n      } catch (error) {\n        this.errorMessage = `时段列表API测试失败: ${error.message}`;\n        this.testResult = JSON.stringify(error.response?.data || error, null, 2);\n      }\n    },\n    \n    async testStatistics() {\n      try {\n        this.errorMessage = '';\n        this.testResult = '正在测试统计数据API...';\n        \n        const response = await this.axios.get('flash-sale/statistics');\n        \n        this.testResult = JSON.stringify(response.data, null, 2);\n      } catch (error) {\n        this.errorMessage = `统计数据API测试失败: ${error.message}`;\n        this.testResult = JSON.stringify(error.response?.data || error, null, 2);\n      }\n    },\n    \n    async testProducts() {\n      try {\n        this.errorMessage = '';\n        this.testResult = '正在测试商品列表API...';\n        \n        const response = await this.axios.get('flash-sale/products');\n        \n        this.testResult = JSON.stringify(response.data, null, 2);\n      } catch (error) {\n        this.errorMessage = `商品列表API测试失败: ${error.message}`;\n        this.testResult = JSON.stringify(error.response?.data || error, null, 2);\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.api-test-container {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.test-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n}\n\n.debug-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #e6a23c;\n  border-radius: 8px;\n  background-color: #fdf6ec;\n}\n\n.test-buttons {\n  margin-bottom: 20px;\n}\n\n.test-buttons .el-button {\n  margin-right: 10px;\n  margin-bottom: 10px;\n}\n\n.error-message {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #fef0f0;\n  border: 1px solid #fbc4c4;\n  border-radius: 4px;\n  color: #f56c6c;\n}\n\n.test-result {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f0f9ff;\n  border: 1px solid #c4e1ff;\n  border-radius: 4px;\n}\n\npre {\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  max-height: 400px;\n  overflow-y: auto;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n  line-height: 1.4;\n}\n\nh2 {\n  color: #303133;\n  margin-bottom: 20px;\n}\n\nh3 {\n  color: #606266;\n  margin-bottom: 15px;\n}\n\nh4 {\n  color: #909399;\n  margin-bottom: 10px;\n}\n</style>\n"]}]}