const Base = require('./base.js');

module.exports = class extends Base {
    
    /**
     * 获取分享记录列表（区分浏览和下单）
     * GET /admin/share-records/list
     */
    async listAction() {
        try {
            const page = this.get('page') || 1;
            const limit = this.get('limit') || 20;
            const keyword = this.get('keyword') || '';
            const actionType = this.get('actionType') || ''; // all, browsed, ordered
            const startDate = this.get('startDate') || '';
            const endDate = this.get('endDate') || '';
            const promoterUserId = this.get('promoterUserId') || '';

            console.log('获取分享记录列表参数:', { page, limit, keyword, actionType, startDate, endDate, promoterUserId });

            // 构建查询条件
            let whereCondition = '1=1';

            // 推广员筛选
            if (promoterUserId) {
                whereCondition += ` AND v.promoter_user_id = ${promoterUserId}`;
            }

            // 关键词搜索（推广员昵称、访问者昵称或商品名称）
            if (keyword) {
                whereCondition += ` AND (pu.nickname LIKE '%${keyword}%' OR vu.nickname LIKE '%${keyword}%' OR g.name LIKE '%${keyword}%')`;
            }

            // 日期范围筛选
            if (startDate && endDate) {
                const startTime = parseInt(new Date(startDate).getTime() / 1000);
                const endTime = parseInt(new Date(endDate).getTime() / 1000);
                whereCondition += ` AND v.visit_time BETWEEN ${startTime} AND ${endTime}`;
            }

            // 构建主查询SQL - 包含分销链信息
            let sql = `
                SELECT
                    v.id,
                    v.promoter_user_id,
                    v.visitor_user_id,
                    v.goods_id,
                    v.visit_time,
                    v.share_source,
                    v.ip_address,
                    v.is_new_visitor,
                    CASE
                        WHEN o.id IS NOT NULL THEN 'ordered'
                        ELSE 'browsed'
                    END as user_action,
                    o.id as order_record_id,
                    o.order_id,
                    o.order_sn,
                    o.order_amount,
                    o.commission_amount,
                    o.personal_commission,
                    o.level1_commission,
                    o.level2_commission,
                    o.parent_promoter_user_id,
                    o.status as order_status,
                    g.name as goods_name,
                    g.list_pic_url as goods_image,
                    pu.nickname as promoter_nickname,
                    pu.mobile as promoter_mobile,
                    vu.nickname as visitor_nickname,
                    vu.mobile as visitor_mobile,
                    ppu.nickname as parent_promoter_nickname,
                    ppu.mobile as parent_promoter_mobile
                FROM hiolabs_promotion_visits v
                LEFT JOIN hiolabs_promotion_orders o
                    ON v.visitor_user_id = o.buyer_user_id
                    AND v.goods_id = o.goods_id
                    AND v.promoter_user_id = o.promoter_user_id
                LEFT JOIN hiolabs_goods g ON v.goods_id = g.id
                LEFT JOIN hiolabs_user pu ON v.promoter_user_id = pu.id
                LEFT JOIN hiolabs_user vu ON v.visitor_user_id = vu.id
                LEFT JOIN hiolabs_user ppu ON o.parent_promoter_user_id = ppu.id
                WHERE ${whereCondition}
            `;

            // 根据行为类型筛选
            if (actionType === 'browsed') {
                sql += ` HAVING user_action = 'browsed'`;
            } else if (actionType === 'ordered') {
                sql += ` HAVING user_action = 'ordered'`;
            }

            sql += ` ORDER BY v.visit_time DESC`;

            // 分页查询
            const offset = (page - 1) * limit;
            const limitSql = sql + ` LIMIT ${offset}, ${limit}`;

            // 执行查询
            const records = await this.model().query(limitSql);

            // 查询总数
            const countSql = `SELECT COUNT(*) as total FROM (${sql}) as temp`;
            const countResult = await this.model().query(countSql);
            const total = countResult[0].total;

            console.log('查询到的分享记录数量:', total);

            // 格式化数据
            const formattedRecords = records.map(record => {
                console.log('处理记录:', record.id, '用户行为:', record.user_action);

                // 推广员名称和访问者名称base64解码
                const promoterNickname = this.decodeNickname(record.promoter_nickname);
                const visitorNickname = this.decodeNickname(record.visitor_nickname);

                // 分享来源中文显示
                const shareSourceMap = {
                    'friend': '好友分享',
                    'qrcode': '二维码分享',
                    'other': '其他',
                    'miniprogram': '小程序',
                    'wechat': '微信',
                    'link': '链接'
                };

                // 构建分销链信息
                const distributionChain = [];

                // 一级推广员（直接分享者）
                distributionChain.push({
                    level: 1,
                    userId: record.promoter_user_id,
                    nickname: promoterNickname || '未知推广员',
                    mobile: record.promoter_mobile || '',
                    commission: record.user_action === 'ordered' ? parseFloat(record.personal_commission || 0) : 0
                });

                // 二级推广员（上级推广员）
                if (record.parent_promoter_user_id && record.user_action === 'ordered') {
                    const parentPromoterNickname = this.decodeNickname(record.parent_promoter_nickname);
                    distributionChain.push({
                        level: 2,
                        userId: record.parent_promoter_user_id,
                        nickname: parentPromoterNickname || '未知推广员',
                        mobile: record.parent_promoter_mobile || '',
                        commission: parseFloat(record.level1_commission || 0)
                    });
                }

                const formattedRecord = {
                    id: record.id,
                    // 商品信息
                    goodsId: record.goods_id,
                    goodsName: record.goods_name || '商品已删除',
                    goodsImage: record.goods_image,
                    // 分享信息
                    shareSource: record.share_source,
                    shareSourceText: shareSourceMap[record.share_source] || '其他',
                    visitTime: record.visit_time,
                    visitTimeFormatted: new Date(record.visit_time * 1000).toLocaleString('zh-CN'),
                    // 访问者信息
                    visitorUserId: record.visitor_user_id,
                    visitorNickname: record.visitor_user_id === 0 ? '匿名用户' : (visitorNickname || '未知用户'),
                    // 用户行为
                    userAction: record.user_action,
                    userActionText: record.user_action === 'ordered' ? '已下单' : '仅浏览',
                    userActionBadge: record.user_action === 'ordered' ? 'success' : 'info',
                    // 分销链
                    distributionChain: distributionChain,
                    // 订单信息
                    orderInfo: record.user_action === 'ordered' ? {
                        orderId: record.order_id,
                        orderSn: record.order_sn,
                        orderAmount: parseFloat(record.order_amount || 0),
                        totalCommission: parseFloat(record.commission_amount || 0),
                        personalCommission: parseFloat(record.personal_commission || 0),
                        level1Commission: parseFloat(record.level1_commission || 0),
                        level2Commission: parseFloat(record.level2_commission || 0),
                        orderStatus: record.order_status
                    } : null
                };

                console.log('格式化后记录:', formattedRecord.id, '行为:', formattedRecord.userActionText);
                return formattedRecord;
            });

            // 获取统计数据
            const statsResult = await this.getShareRecordsStats(whereCondition);

            return this.success({
                data: formattedRecords,
                total: total,
                page: parseInt(page),
                limit: parseInt(limit),
                stats: statsResult
            });
            
        } catch (error) {
            console.error('获取分享记录列表失败:', error);
            return this.fail(500, '获取分享记录列表失败');
        }
    }

    /**
     * 获取分享记录统计数据
     */
    async getShareRecordsStats(whereCondition) {
        try {
            const statsSql = `
                SELECT
                    COUNT(v.id) as total_visits,
                    COUNT(DISTINCT v.visitor_user_id) as unique_visitors,
                    COUNT(o.id) as total_orders,
                    COALESCE(SUM(o.order_amount), 0) as total_order_amount,
                    COALESCE(SUM(o.commission_amount), 0) as total_commission,
                    ROUND(COUNT(o.id) * 100.0 / COUNT(v.id), 2) as conversion_rate
                FROM hiolabs_promotion_visits v
                LEFT JOIN hiolabs_promotion_orders o
                    ON v.visitor_user_id = o.buyer_user_id
                    AND v.goods_id = o.goods_id
                    AND v.promoter_user_id = o.promoter_user_id
                LEFT JOIN hiolabs_goods g ON v.goods_id = g.id
                LEFT JOIN hiolabs_user pu ON v.promoter_user_id = pu.id
                LEFT JOIN hiolabs_user vu ON v.visitor_user_id = vu.id
                WHERE ${whereCondition}
            `;

            console.log('统计查询SQL:', statsSql);
            const statsResult = await this.model().query(statsSql);
            const stats = statsResult[0];
            console.log('统计查询原始结果:', stats);

            const result = {
                totalVisits: parseInt(stats.total_visits),           // 总访问次数
                uniqueVisitors: parseInt(stats.unique_visitors),     // 独立访客数
                totalOrders: parseInt(stats.total_orders),           // 总下单数
                browsedOnly: parseInt(stats.total_visits) - parseInt(stats.total_orders), // 仅浏览数
                totalOrderAmount: parseFloat(stats.total_order_amount), // 总订单金额
                totalCommission: parseFloat(stats.total_commission),    // 总佣金
                conversionRate: parseFloat(stats.conversion_rate)       // 转化率
            };

            console.log('统计查询格式化结果:', result);
            return result;

        } catch (error) {
            console.error('获取统计数据失败:', error);
            return {
                totalVisits: 0,
                uniqueVisitors: 0,
                totalOrders: 0,
                browsedOnly: 0,
                totalOrderAmount: 0,
                totalCommission: 0,
                conversionRate: 0
            };
        }
    }
    
    /**
     * 获取分享统计数据
     * GET /admin/share-records/stats
     */
    async statsAction() {
        try {
            console.log('获取推广统计数据');

            // 总访问记录数
            const totalVisits = await this.model('promotion_visits').count();

            // 总推广订单数
            const totalOrders = await this.model('promotion_orders').count();

            // 总订单金额
            const totalAmountResult = await this.model('promotion_orders').sum('order_amount');
            const totalAmount = parseFloat(totalAmountResult || 0);

            // 总佣金
            const totalCommissionResult = await this.model('promotion_orders').sum('commission_amount');
            const totalCommission = parseFloat(totalCommissionResult || 0);

            // 今日统计 - 修复时间戳计算
            const today = new Date();
            const todayStart = parseInt(new Date(today.getFullYear(), today.getMonth(), today.getDate()).getTime() / 1000);
            console.log('今日开始时间戳:', todayStart, '对应时间:', new Date(todayStart * 1000));

            const todayVisits = await this.model('promotion_visits').where({
                visit_time: ['>=', todayStart]
            }).count();

            const todayOrders = await this.model('promotion_orders').where({
                create_time: ['>=', todayStart]
            }).count();

            // 活跃推广员数量
            const activePromoters = await this.model('personal_promoters').where({
                status: 1
            }).count();
            
            // 转化率
            const conversionRate = totalVisits > 0 ? ((totalOrders / totalVisits) * 100).toFixed(2) : 0;

            const statsData = {
                total_visits: totalVisits,
                total_orders: totalOrders,
                total_amount: totalAmount.toFixed(2),
                total_commission: totalCommission.toFixed(2),
                today_visits: todayVisits,
                today_orders: todayOrders,
                active_promoters: activePromoters,
                conversion_rate: conversionRate
            };
            
            console.log('分享统计数据:', statsData);
            
            return this.success(statsData);
            
        } catch (error) {
            console.error('获取分享统计数据失败:', error);
            return this.fail(500, '获取统计数据失败');
        }
    }
    
    /**
     * 获取分享排行榜
     * GET /admin/share-records/ranking
     */
    async rankingAction() {
        try {
            const type = this.get('type') || 'orders'; // orders, amount, commission
            const limit = this.get('limit') || 10;
            
            console.log('获取分享排行榜:', { type, limit });
            
            let orderField = 'total_orders';

            if (type === 'amount') {
                orderField = 'total_commission'; // 按佣金排序，因为没有单独的金额字段
            } else if (type === 'commission') {
                orderField = 'total_commission';
            } else if (type === 'visits') {
                orderField = 'total_views';
            }
            
            const ranking = await this.model('personal_promoters')
                .alias('pp')
                .join({
                    table: 'user',
                    join: 'left',
                    as: 'u',
                    on: ['pp.user_id', 'u.id']
                })
                .where({
                    'pp.status': 1
                })
                .field(`pp.*, u.nickname, u.mobile, u.avatar`)
                .order(`pp.${orderField} DESC`)
                .limit(limit)
                .select();
            
            // 格式化数据
            for (let i = 0; i < ranking.length; i++) {
                ranking[i].rank = i + 1;
                ranking[i].total_views = parseInt(ranking[i].total_views || 0);
                ranking[i].total_orders = parseInt(ranking[i].total_orders || 0);
                ranking[i].total_commission = parseFloat(ranking[i].total_commission || 0);
                ranking[i].level_text = this.getLevelText(ranking[i].level);
                ranking[i].status_text = ranking[i].status === 1 ? '正常' : '禁用';

                // 推广员名称base64解码
                ranking[i].nickname = this.decodeNickname(ranking[i].nickname);
            }
            
            console.log('分享排行榜数据:', ranking.length);
            
            return this.success(ranking);
            
        } catch (error) {
            console.error('获取分享排行榜失败:', error);
            return this.fail(500, '获取排行榜失败');
        }
    }
    
    /**
     * 导出推广记录
     * GET /admin/share-records/export
     */
    async exportAction() {
        try {
            // 这里可以实现导出Excel功能
            // 暂时返回成功消息
            return this.success({ message: '导出功能开发中' });

        } catch (error) {
            console.error('导出推广记录失败:', error);
            return this.fail(500, '导出失败');
        }
    }

    /**
     * 获取推广员等级文本
     * @param {number} level 等级
     */
    getLevelText(level) {
        const levelMap = {
            1: '新手',
            2: '优秀',
            3: '金牌',
            4: '钻石'
        };
        return levelMap[level] || '未知';
    }

    /**
     * 获取推广员列表
     * GET /admin/share-records/promoters
     */
    async promotersAction() {
        try {
            const page = this.get('page') || 1;
            const limit = this.get('limit') || 20;
            const keyword = this.get('keyword') || '';
            const level = this.get('level') || '';
            const status = this.get('status') || '';

            console.log('获取推广员列表参数:', { page, limit, keyword, level, status });

            // 构建查询条件
            let whereCondition = {};

            if (keyword) {
                whereCondition['u.nickname'] = ['like', `%${keyword}%`];
            }

            if (level) {
                whereCondition['pp.level'] = level;
            }

            if (status !== '') {
                whereCondition['pp.status'] = status;
            }

            // 查询推广员列表
            const promoterList = await this.model('personal_promoters')
                .alias('pp')
                .join({
                    table: 'user',
                    join: 'left',
                    as: 'u',
                    on: ['pp.user_id', 'u.id']
                })
                .where(whereCondition)
                .field('pp.*, u.nickname, u.mobile, u.avatar')
                .order('pp.create_time DESC')
                .page(page, limit)
                .countSelect();

            console.log('查询到的推广员数量:', promoterList.count);

            // 格式化推广员数据
            for (let promoter of promoterList.data) {
                // 推广员名称base64解码
                promoter.nickname = this.decodeNickname(promoter.nickname);

                // 格式化时间
                promoter.create_time_formatted = new Date(promoter.create_time * 1000).toLocaleString('zh-CN');

                // 格式化数值
                promoter.total_commission = parseFloat(promoter.total_commission || 0);
                promoter.month_commission = parseFloat(promoter.month_commission || 0);

                // 等级文本
                promoter.level_text = this.getLevelText(promoter.level);
                promoter.status_text = promoter.status === 1 ? '正常' : '禁用';

                // 处理头像URL
                if (!promoter.avatar || promoter.avatar === '' || promoter.avatar.startsWith('wxfile://')) {
                    // 如果没有头像或者是微信临时文件，使用默认头像
                    promoter.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';
                } else if (!promoter.avatar.startsWith('http://') && !promoter.avatar.startsWith('https://')) {
                    // 如果是相对路径，补充完整的域名
                    promoter.avatar = `https://ht.rxkjsdj.com${promoter.avatar}`;
                }
            }

            return this.success({
                data: promoterList.data,
                total: promoterList.count,
                page: parseInt(page),
                limit: parseInt(limit)
            });

        } catch (error) {
            console.error('获取推广员列表失败:', error);
            return this.fail(500, '获取推广员列表失败');
        }
    }

    /**
     * 获取推广员统计数据
     * GET /admin/share-records/promostats
     */
    async promostatsAction() {
        try {
            console.log('获取推广员统计数据');

            // 总推广员数
            const totalPromoters = await this.model('personal_promoters').count();

            // 活跃推广员数量
            const activePromoters = await this.model('personal_promoters').where({
                status: 1
            }).count();

            // 总浏览次数
            const totalViewsResult = await this.model('personal_promoters').sum('total_views');
            const totalViews = parseInt(totalViewsResult || 0);

            // 总成交次数
            const totalOrdersResult = await this.model('personal_promoters').sum('total_orders');
            const totalOrders = parseInt(totalOrdersResult || 0);

            // 总佣金
            const totalCommissionResult = await this.model('personal_promoters').sum('total_commission');
            const totalCommission = parseFloat(totalCommissionResult || 0);

            const statsData = {
                total_promoters: totalPromoters,
                active_promoters: activePromoters,
                total_views: totalViews,
                total_orders: totalOrders,
                total_commission: totalCommission.toFixed(2)
            };

            console.log('推广员统计数据:', statsData);

            return this.success(statsData);

        } catch (error) {
            console.error('获取推广员统计数据失败:', error);
            return this.fail(500, '获取统计数据失败');
        }
    }

    /**
     * Base64解码昵称
     * @param {string} nickname base64编码的昵称
     */
    decodeNickname(nickname) {
        if (!nickname) return nickname;
        try {
            return Buffer.from(nickname, 'base64').toString('utf8');
        } catch (error) {
            console.log('昵称解码失败:', error);
            return nickname; // 如果解码失败，返回原值
        }
    }
};
