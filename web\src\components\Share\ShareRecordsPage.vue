<template>
  <div class="content-page bg-gray-50 min-h-screen">
    <!-- 面包屑导航 -->
    <div class="flex items-center text-sm text-gray-500 mb-4">
      <a href="#" class="hover:text-primary">首页</a>
      <span class="mx-2">/</span>
      <span class="text-gray-700">分享记录管理</span>
    </div>

    <!-- 统计卡片 -->
    <div style="display: flex; gap: 1.5rem; margin-bottom: 1.5rem; flex-wrap: nowrap; overflow-x: auto;">
      <div style="flex: 1; min-width: 200px; background-color: white; border-radius: 0.5rem; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); padding: 1.25rem;">
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <div>
            <p style="color: #6b7280; font-size: 0.875rem;">总访问数</p>
            <h3 style="font-size: 1.5rem; font-weight: 700; margin-top: 0.25rem;">{{ stats.totalVisits || 0 }}</h3>
            <p style="color: #10b981; font-size: 0.875rem; margin-top: 0.25rem;">
              分享访问统计
            </p>
          </div>
          <div style="width: 3rem; height: 3rem; display: flex; align-items: center; justify-content: center; background-color: #dbeafe; color: #4f46e5; border-radius: 9999px;">
            <i class="el-icon-view" style="font-size: 1.5rem;"></i>
          </div>
        </div>
      </div>
      <div style="flex: 1; min-width: 200px; background-color: white; border-radius: 0.5rem; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); padding: 1.25rem;">
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <div>
            <p style="color: #6b7280; font-size: 0.875rem;">总订单数</p>
            <h3 style="font-size: 1.5rem; font-weight: 700; margin-top: 0.25rem;">{{ stats.totalOrders || 0 }}</h3>
            <p style="color: #f59e0b; font-size: 0.875rem; margin-top: 0.25rem;">
              转化订单
            </p>
          </div>
          <div style="width: 3rem; height: 3rem; display: flex; align-items: center; justify-content: center; background-color: #fef3c7; color: #f59e0b; border-radius: 9999px;">
            <i class="el-icon-s-order" style="font-size: 1.5rem;"></i>
          </div>
        </div>
      </div>
      <div style="flex: 1; min-width: 200px; background-color: white; border-radius: 0.5rem; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); padding: 1.25rem;">
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <div>
            <p style="color: #6b7280; font-size: 0.875rem;">总订单金额</p>
            <h3 style="font-size: 1.5rem; font-weight: 700; margin-top: 0.25rem;">¥{{ stats.totalOrderAmount || 0 }}</h3>
            <p style="color: #10b981; font-size: 0.875rem; margin-top: 0.25rem;">
              销售额
            </p>
          </div>
          <div style="width: 3rem; height: 3rem; display: flex; align-items: center; justify-content: center; background-color: #d1fae5; color: #10b981; border-radius: 9999px;">
            <i class="el-icon-money" style="font-size: 1.5rem;"></i>
          </div>
        </div>
      </div>
      <div style="flex: 1; min-width: 200px; background-color: white; border-radius: 0.5rem; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); padding: 1.25rem;">
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <div>
            <p style="color: #6b7280; font-size: 0.875rem;">总佣金</p>
            <h3 style="font-size: 1.5rem; font-weight: 700; margin-top: 0.25rem;">¥{{ stats.totalCommission || 0 }}</h3>
            <p style="color: #8b5cf6; font-size: 0.875rem; margin-top: 0.25rem;">
              推广收益
            </p>
          </div>
          <div style="width: 3rem; height: 3rem; display: flex; align-items: center; justify-content: center; background-color: #e9d5ff; color: #8b5cf6; border-radius: 9999px;">
            <i class="el-icon-coin" style="font-size: 1.5rem;"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- 分享记录管理标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold">分享记录管理</h1>
    </div>

    <!-- 筛选区域 -->
    <div class="bg-white rounded shadow mb-6 p-4">
      <div style="display: flex; align-items: center; gap: 1rem; flex-wrap: wrap;">
        <input
          v-model="searchForm.keyword"
          placeholder="分享人昵称或商品名称..."
          class="px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary w-64 text-sm"
        />
        <select
          v-model="searchForm.actionType"
          class="px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary text-sm"
        >
          <option value="">全部行为</option>
          <option value="browsed">仅浏览</option>
          <option value="ordered">已下单</option>
        </select>
        <select
          v-model="searchForm.shareType"
          class="px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary text-sm"
        >
          <option value="">全部类型</option>
          <option value="friend">好友分享</option>
          <option value="qrcode">二维码分享</option>
          <option value="other">其他</option>
        </select>
        <button
          @click="searchRecords"
          class="bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-button text-gray-700 flex items-center whitespace-nowrap"
        >
          <i class="el-icon-search mr-1"></i> 搜索
        </button>
        <button
          @click="resetSearch"
          class="bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-button text-gray-700 flex items-center whitespace-nowrap"
        >
          <i class="el-icon-refresh mr-1"></i> 重置
        </button>
        <button
          @click="exportRecords"
          class="bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-button text-gray-700 flex items-center ml-auto whitespace-nowrap"
        >
          <i class="el-icon-download mr-1"></i> 导出
        </button>
      </div>
    </div>

    <!-- 分享记录列表 -->
    <div class="table-container">
      <div v-if="records.length === 0" class="p-4 text-center text-gray-500">
        暂无分享记录数据
      </div>
      <div v-else class="p-2 text-xs text-gray-400">
        共 {{ records.length }} 条分享记录
      </div>

      <table class="w-full" style="width: 100%; border-collapse: collapse;">
        <thead>
          <tr style="background-color: #f9fafb; color: #6b7280; font-size: 0.75rem;">
            <th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 5%;">
              <input type="checkbox" style="width: 1rem; height: 1rem;" />
            </th>
            <th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 25%;">商品信息</th>
            <th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 12%;">推广员</th>
            <th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 12%;">访问者</th>
            <th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 8%;">分享来源</th>
            <th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 10%;">用户行为</th>
            <th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 18%;">订单信息</th>
            <th style="padding: 0.75rem 1rem; font-weight: 500; text-align: left; width: 15%;">操作</th>
          </tr>
        </thead>
        <tbody style="border-top: 1px solid #e5e7eb;">
          <tr v-for="item in records" :key="item.id"
              style="border-bottom: 1px solid #f3f4f6; cursor: pointer;"
              @mouseover="$event.target.style.backgroundColor='#f9fafb'"
              @mouseout="$event.target.style.backgroundColor=''">

            <!-- 复选框 -->
            <td style="padding: 1rem; vertical-align: middle; text-align: center;">
              <input type="checkbox" style="width: 1rem; height: 1rem;" />
            </td>

            <!-- 商品信息 -->
            <td style="padding: 1rem; vertical-align: middle;">
              <div style="display: flex; align-items: center;">
                <img
                  :src="item.goodsImage || '/static/images/default-goods.png'"
                  alt="商品图片"
                  style="width: 3rem; height: 3rem; object-fit: cover; border-radius: 0.5rem; margin-right: 0.75rem; flex-shrink: 0;"
                />
                <div style="flex: 1; min-width: 0;">
                  <div style="font-weight: 500; font-size: 0.875rem; margin-bottom: 0.25rem; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                    {{ item.goodsName }}
                  </div>
                  <div style="color: #6b7280; font-size: 0.75rem; margin-bottom: 0.125rem;">
                    记录ID：{{ item.id }}
                  </div>
                  <div style="color: #6b7280; font-size: 0.75rem;">
                    访问时间：{{ item.visitTimeFormatted }}
                  </div>
                </div>
              </div>
            </td>

            <!-- 推广员 -->
            <td style="padding: 1rem; vertical-align: middle;">
              <div style="font-size: 0.875rem; font-weight: 500; margin-bottom: 0.25rem;">
                {{ item.promoterNickname || '未知推广员' }}
              </div>
              <div style="color: #6b7280; font-size: 0.75rem;">
                {{ item.promoterMobile || '未绑定手机' }}
              </div>
            </td>

            <!-- 访问者 -->
            <td style="padding: 1rem; vertical-align: middle;">
              <div style="font-size: 0.875rem; font-weight: 500; margin-bottom: 0.25rem;">
                {{ item.visitorNickname || '匿名用户' }}
              </div>
              <div style="color: #6b7280; font-size: 0.75rem;">
                {{ item.visitorMobile || '未知' }}
              </div>
              <div v-if="item.isNewVisitor" style="color: #10b981; font-size: 0.75rem; margin-top: 0.125rem;">
                新访客
              </div>
            </td>

            <!-- 分享来源 -->
            <td style="padding: 1rem; vertical-align: middle;">
              <span :style="getShareSourceStyle(item.shareSourceText)">
                {{ item.shareSourceText }}
              </span>
            </td>

            <!-- 用户行为 -->
            <td style="padding: 1rem; vertical-align: middle;">
              <span :style="getUserActionStyle(item.userAction)">
                {{ item.userActionText }}
              </span>
            </td>

            <!-- 订单信息 -->
            <td style="padding: 1rem; vertical-align: middle;">
              <div v-if="item.userAction === 'ordered' && item.orderInfo">
                <div style="font-size: 0.875rem; font-weight: 500; margin-bottom: 0.25rem;">
                  订单号：{{ item.orderInfo.orderId }}
                </div>
                <div style="color: #10b981; font-size: 0.75rem; margin-bottom: 0.125rem;">
                  金额：¥{{ item.orderInfo.orderAmount }}
                </div>
                <div style="color: #8b5cf6; font-size: 0.75rem;">
                  佣金：¥{{ item.orderInfo.commissionAmount }}
                </div>
              </div>
              <div v-else style="color: #6b7280; font-size: 0.75rem;">
                未产生订单
              </div>
            </td>

            <!-- 操作 -->
            <td style="padding: 1rem; vertical-align: middle;">
              <button
                style="color: #4f46e5; font-size: 0.75rem; white-space: nowrap; background: none; border: none; cursor: pointer; text-align: left; padding: 0;"
                @click="viewDetail(item)"
              >
                查看详情
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      :visible.sync="detailVisible"
      title="分享记录详情"
      width="800px"
      class="share-detail-dialog"
      :before-close="handleClose"
    >
      <div v-if="currentRecord" class="record-detail">
        <!-- 分享信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            <i class="el-icon-share"></i>
            分享信息
          </h3>
          <div class="info-grid">
            <div class="info-item">
              <label>访问时间</label>
              <span>{{ currentRecord.visitTimeFormatted }}</span>
            </div>
            <div class="info-item">
              <label>分享来源</label>
              <span :style="getShareSourceStyle(currentRecord.shareSourceText)">
                {{ currentRecord.shareSourceText }}
              </span>
            </div>
            <div class="info-item">
              <label>用户行为</label>
              <span :style="getUserActionStyle(currentRecord.userAction)">
                {{ currentRecord.userActionText }}
              </span>
            </div>
            <div class="info-item">
              <label>访问者</label>
              <span>{{ currentRecord.visitorNickname }}</span>
            </div>
          </div>
        </div>

        <!-- 商品信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            <i class="el-icon-goods"></i>
            商品信息
          </h3>
          <div class="goods-info">
            <div class="goods-image">
              <img
                :src="currentRecord.goodsImage || '/static/images/default-goods.png'"
                alt="商品图片"
                @error="handleImageError"
              />
            </div>
            <div class="goods-details">
              <div class="goods-name">{{ currentRecord.goodsName }}</div>
            </div>
          </div>
        </div>

        <!-- 分销链信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            <i class="el-icon-connection"></i>
            分销链信息
          </h3>
          <div class="distribution-chain">
            <div
              v-for="(promoter, index) in currentRecord.distributionChain"
              :key="promoter.userId"
              class="chain-item"
            >
              <div class="chain-level">
                <span class="level-badge" :class="'level-' + promoter.level">
                  {{ promoter.level === 1 ? '一级推广员' : '二级推广员' }}
                </span>
              </div>
              <div class="chain-info">
                <div class="promoter-name">{{ promoter.nickname }}</div>
                <div class="promoter-mobile">{{ promoter.mobile || '未绑定手机' }}</div>
                <div v-if="currentRecord.userAction === 'ordered'" class="promoter-commission">
                  佣金：¥{{ promoter.commission }}
                </div>
              </div>
              <div v-if="index < currentRecord.distributionChain.length - 1" class="chain-arrow">
                ↓
              </div>
            </div>
          </div>
        </div>

        <!-- 订单信息（仅当已下单时显示） -->
        <div v-if="currentRecord.userAction === 'ordered' && currentRecord.orderInfo" class="detail-section">
          <h3 class="section-title">
            <i class="el-icon-s-order"></i>
            订单信息
          </h3>
          <div class="order-info">
            <div class="info-grid">
              <div class="info-item">
                <label>订单号</label>
                <span class="order-id">{{ currentRecord.orderInfo.orderSn || currentRecord.orderInfo.orderId }}</span>
              </div>
              <div class="info-item">
                <label>订单状态</label>
                <span>{{ getOrderStatusText(currentRecord.orderInfo.orderStatus) }}</span>
              </div>
              <div class="info-item">
                <label>订单金额</label>
                <span class="amount-text">¥{{ currentRecord.orderInfo.orderAmount }}</span>
              </div>
              <div class="info-item">
                <label>总佣金</label>
                <span class="commission-text">¥{{ currentRecord.orderInfo.totalCommission }}</span>
              </div>
            </div>

            <!-- 佣金分配详情 -->
            <div class="commission-breakdown">
              <h4 class="breakdown-title">佣金分配明细</h4>
              <div class="breakdown-list">
                <div class="breakdown-item">
                  <span class="breakdown-label">一级推广员佣金：</span>
                  <span class="breakdown-value personal">¥{{ currentRecord.orderInfo.personalCommission }}</span>
                </div>
                <div v-if="currentRecord.orderInfo.level1Commission > 0" class="breakdown-item">
                  <span class="breakdown-label">二级推广员佣金：</span>
                  <span class="breakdown-value level1">¥{{ currentRecord.orderInfo.level1Commission }}</span>
                </div>
                <div v-if="currentRecord.orderInfo.level2Commission > 0" class="breakdown-item">
                  <span class="breakdown-label">三级推广员佣金：</span>
                  <span class="breakdown-value level2">¥{{ currentRecord.orderInfo.level2Commission }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 无订单提示 -->
        <div v-else-if="currentRecord.userAction === 'browsed'" class="detail-section">
          <h3 class="section-title">
            <i class="el-icon-warning"></i>
            订单信息
          </h3>
          <div class="no-order-tip">
            <i class="el-icon-info"></i>
            <span>该访问者仅浏览了商品，未产生订单</span>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="currentRecord && currentRecord.userAction === 'ordered'" type="primary" @click="viewOrder">
          查看订单详情
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ShareRecordsPage',
  data() {
    return {
      loading: false,
      records: [],
      stats: {
        totalVisits: 0,
        totalOrders: 0,
        totalOrderAmount: 0,
        totalCommission: 0,
        browsedOnly: 0,
        uniqueVisitors: 0,
        conversionRate: 0
      },
      searchForm: {
        keyword: '',
        actionType: '',
        shareType: ''
      },
      dateRange: [],
      currentPage: 1,
      pageSize: 20,
      total: 0,
      detailVisible: false,
      currentRecord: null
    }
  },
  mounted() {
    this.loadStats();
    this.loadRecords();
  },
  methods: {
    async loadStats() {
      try {
        const response = await this.axios.get('share-records/stats');
        if (response.data.errno === 0) {
          this.stats = response.data.data;
        }
      } catch (error) {
        console.error('加载统计数据失败:', error);
      }
    },
    async loadRecords() {
      this.loading = true;
      try {
        const params = {
          page: this.currentPage,
          limit: this.pageSize,
          keyword: this.searchForm.keyword,
          actionType: this.searchForm.actionType,
          shareType: this.searchForm.shareType
        };
        
        if (this.dateRange && this.dateRange.length === 2) {
          params.startDate = this.dateRange[0];
          params.endDate = this.dateRange[1];
        }
        
        const response = await this.axios.get('share-records/list', { params });
        if (response.data.errno === 0) {
          this.records = response.data.data.data || response.data.data;
          this.total = response.data.data.total;
          // 更新统计数据
          if (response.data.data.stats) {
            this.stats = response.data.data.stats;
          }
        }
      } catch (error) {
        console.error('加载分享记录失败:', error);
        this.$message.error('加载数据失败');
      } finally {
        this.loading = false;
      }
    },
    searchRecords() {
      this.currentPage = 1;
      this.loadRecords();
    },
    resetSearch() {
      this.searchForm = {
        keyword: '',
        actionType: '',
        shareType: ''
      };
      this.dateRange = [];
      this.currentPage = 1;
      this.loadRecords();
    },
    async exportRecords() {
      try {
        const response = await this.axios.get('share-records/export');
        if (response.data.errno === 0) {
          this.$message.success('导出成功');
        }
      } catch (error) {
        console.error('导出失败:', error);
        this.$message.error('导出失败');
      }
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.loadRecords();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.loadRecords();
    },
    viewDetail(record) {
      this.currentRecord = record;
      this.detailVisible = true;
    },

    handleClose(done) {
      this.detailVisible = false;
      this.currentRecord = null;
      if (typeof done === 'function') {
        done();
      }
    },


    getStatusType(status) {
      const typeMap = {
        'visit': '',
        'order': 'warning',
        'paid': 'success',
        'settled': 'info'
      };
      return typeMap[status] || '';
    },

    // 获取分享来源样式
    getShareSourceStyle(source) {
      const styleMap = {
        '好友分享': 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #dbeafe; color: #1e40af;',
        '二维码分享': 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #d1fae5; color: #065f46;',
        '其他': 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #f3f4f6; color: #374151;'
      };
      return styleMap[source] || styleMap['其他'];
    },

    // 获取状态样式
    getStatusStyle(status) {
      const styleMap = {
        'visit': 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #f3f4f6; color: #374151;',
        'order': 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #fef3c7; color: #92400e;',
        'paid': 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #d1fae5; color: #065f46;',
        'settled': 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #e9d5ff; color: #7c3aed;'
      };
      return styleMap[status] || styleMap['visit'];
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'visit': '访问',
        'order': '下单',
        'paid': '已支付',
        'settled': '已结算'
      };
      return textMap[status] || '未知';
    },

    // 获取用户行为样式
    getUserActionStyle(action) {
      const styleMap = {
        'browsed': 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #f3f4f6; color: #6b7280;',
        'ordered': 'padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 9999px; background-color: #d1fae5; color: #065f46;'
      };
      return styleMap[action] || styleMap['browsed'];
    },

    // 获取订单状态文本
    getOrderStatusText(status) {
      const statusMap = {
        0: '待付款',
        1: '已付款',
        2: '已发货',
        3: '已收货',
        4: '已评价',
        5: '已取消',
        6: '已退款'
      };
      return statusMap[status] || '未知状态';
    },



    // 处理图片加载错误
    handleImageError(event) {
      event.target.src = '/static/images/default-goods.png';
    },

    // 查看订单详情
    viewOrder() {
      if (this.currentRecord && this.currentRecord.orderInfo) {
        // 这里可以跳转到订单详情页面或打开订单详情弹窗
        this.$message.info(`订单号：${this.currentRecord.orderInfo.orderId}`);
        // 实际项目中可以这样跳转：
        // this.$router.push(`/order/detail/${this.currentRecord.orderInfo.orderId}`);
      }
    }
  }
}
</script>

<style scoped>
.share-records-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.search-filters {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.records-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.goods-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.goods-image {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

.mobile {
  font-size: 12px;
  color: #909399;
}

.anonymous {
  color: #C0C4CC;
  font-style: italic;
}

.amount {
  color: #E6A23C;
  font-weight: bold;
}

.commission {
  color: #67C23A;
  font-weight: bold;
}

.no-order {
  color: #C0C4CC;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.record-detail {
  padding: 0;
  font-size: 14px;
}

/* 详情弹窗样式 */
.share-detail-dialog .el-dialog__body {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.section-title {
  background: #f5f7fa;
  padding: 12px 16px;
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title i {
  color: #409eff;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  padding: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.info-item span {
  font-size: 14px;
  color: #303133;
}

.text-success {
  color: #67c23a !important;
}

.text-muted {
  color: #909399 !important;
}

/* 商品信息样式 */
.goods-info {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
}

.goods-info .goods-image {
  flex-shrink: 0;
}

.goods-info .goods-image img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.goods-details {
  flex: 1;
}

.goods-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1.4;
}

.goods-id {
  font-size: 12px;
  color: #909399;
}

/* 分销链样式 */
.distribution-chain {
  padding: 16px;
}

.chain-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 12px;
}

.chain-item:last-child {
  margin-bottom: 0;
}

.chain-level {
  margin-right: 16px;
}

.level-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.level-badge.level-1 {
  background: #409eff;
}

.level-badge.level-2 {
  background: #67c23a;
}

.chain-info {
  flex: 1;
}

.promoter-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.promoter-mobile {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.promoter-commission {
  font-size: 12px;
  color: #e6a23c;
  font-weight: 600;
}

.chain-arrow {
  font-size: 18px;
  color: #409eff;
  margin: 8px 0;
  text-align: center;
}

/* 订单信息样式 */
.order-info {
  padding: 16px;
}

.order-id {
  font-family: 'Courier New', monospace;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 13px;
}

.amount-text {
  color: #e6a23c;
  font-weight: 600;
}

.commission-text {
  color: #67c23a;
  font-weight: 600;
}

/* 佣金分配明细样式 */
.commission-breakdown {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.breakdown-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
}

.breakdown-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.breakdown-label {
  font-size: 13px;
  color: #606266;
}

.breakdown-value {
  font-size: 14px;
  font-weight: 600;
}

.breakdown-value.personal {
  color: #409eff;
}

.breakdown-value.level1 {
  color: #67c23a;
}

.breakdown-value.level2 {
  color: #e6a23c;
}

.order-summary {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-item .label {
  color: #606266;
  font-size: 14px;
}

.summary-item .value {
  color: #409eff;
  font-weight: 600;
  font-size: 16px;
}

/* 无订单提示样式 */
.no-order-tip {
  padding: 24px;
  text-align: center;
  color: #909399;
  background: #fafafa;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.no-order-tip i {
  font-size: 18px;
  color: #e6a23c;
}

/* 弹窗底部样式 */
.dialog-footer {
  text-align: right;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .goods-info {
    flex-direction: column;
    align-items: flex-start;
  }

  .share-detail-dialog .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }
}
</style>
