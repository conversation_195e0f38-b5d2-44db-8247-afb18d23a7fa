<template>
  <div class="review-status-page">
    <!-- 页面头部 -->
    <div class="page-header bg-white shadow-sm border-b">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">团长审核</h1>
            <p class="text-sm text-gray-500 mt-1">管理团长申请的审核状态</p>
          </div>
          <div class="flex items-center space-x-3">
            <button @click="refreshData" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <i class="ri-refresh-line mr-2"></i>
              刷新数据
            </button>
            <button @click="exportData" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <i class="ri-download-line mr-2"></i>
              导出数据
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="p-6 max-w-7xl mx-auto">
      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="ri-file-list-line text-xl text-blue-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">总申请数</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.totalApplications }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <i class="ri-time-line text-xl text-yellow-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">待审核</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.pendingApplications }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="ri-check-line text-xl text-green-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">已通过</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.approvedApplications }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <i class="ri-close-line text-xl text-red-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">已拒绝</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.rejectedApplications }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 筛选和搜索 -->
      <div class="bg-white rounded-lg shadow-sm border mb-6">
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">搜索申请人</label>
              <input
                v-model="searchQuery"
                type="text"
                placeholder="输入姓名、手机号或微信昵称"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">审核状态</label>
              <select
                v-model="filterStatus"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">全部状态</option>
                <option value="pending">待审核</option>
                <option value="approved">已通过</option>
                <option value="rejected">已拒绝</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">邀请方式</label>
              <select
                v-model="filterInviteType"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">全部方式</option>
                <option value="self">自主申请</option>
                <option value="admin">管理邀请</option>
              </select>
            </div>

            <div class="flex items-end">
              <button @click="resetFilters" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                <i class="ri-refresh-line mr-2"></i>
                重置
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 团长申请列表 -->
      <div class="bg-white rounded-lg shadow-sm border">
        <div class="px-6 py-4 border-b">
          <h2 class="text-lg font-semibold text-gray-900">团长申请列表</h2>
        </div>

        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请人信息</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">邀请方式</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请时间</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">审核状态</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">审核时间</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">审核人</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="application in filteredApplications" :key="application.id" class="hover:bg-gray-50">
                <!-- 申请人信息 -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <img :src="application.avatar" :alt="application.name" class="w-10 h-10 rounded-full object-cover">
                    <div class="ml-3">
                      <div class="text-sm font-medium text-gray-900">{{ application.name }}</div>
                      <div class="text-sm text-gray-500">{{ application.phone }}</div>
                      <div class="text-xs text-gray-400">{{ application.wechatName }}</div>
                    </div>
                  </div>
                </td>

                <!-- 邀请方式 -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="[
                    'px-2 py-1 text-xs rounded-full',
                    application.inviteType === 'self' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
                  ]">
                    {{ application.inviteType === 'self' ? '自主申请' : '管理邀请' }}
                  </span>
                  <div v-if="application.inviteType === 'admin' && application.inviter" class="text-xs text-gray-500 mt-1">
                    邀请人: {{ application.inviter }}
                  </div>
                </td>

                <!-- 申请时间 -->
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ application.applyTime }}
                </td>

                <!-- 审核状态 -->
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="[
                    'px-2 py-1 text-xs rounded-full',
                    application.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                    application.status === 'approved' ? 'bg-green-100 text-green-800' :
                    'bg-red-100 text-red-800'
                  ]">
                    {{ getStatusText(application.status) }}
                  </span>
                  <div v-if="application.rejectReason" class="text-xs text-red-500 mt-1">
                    {{ application.rejectReason }}
                  </div>
                </td>

                <!-- 审核时间 -->
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ application.reviewTime || '-' }}
                </td>

                <!-- 审核人 -->
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ application.reviewer || '-' }}
                </td>

                <!-- 操作 -->
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button @click="viewApplicationDetail(application)" class="text-blue-600 hover:text-blue-900">
                    详情
                  </button>
                  <button
                    v-if="application.status === 'pending'"
                    @click="approveApplication(application)"
                    class="text-green-600 hover:text-green-900"
                  >
                    通过
                  </button>
                  <button
                    v-if="application.status === 'pending'"
                    @click="rejectApplication(application)"
                    class="text-red-600 hover:text-red-900"
                  >
                    拒绝
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div class="px-6 py-4 border-t bg-gray-50">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500">
              显示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredApplications.length) }}
              条，共 {{ filteredApplications.length }} 条记录
            </div>
            <div class="flex items-center space-x-2">
              <button
                @click="currentPage--"
                :disabled="currentPage <= 1"
                class="px-3 py-1 border rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
              >
                上一页
              </button>
              <span class="px-3 py-1 text-sm">{{ currentPage }} / {{ totalPages }}</span>
              <button
                @click="currentPage++"
                :disabled="currentPage >= totalPages"
                class="px-3 py-1 border rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100"
              >
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 申请详情弹窗 -->
    <div v-if="showDetailModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">团长申请详情</h3>
          <button @click="showDetailModal = false" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>

        <div v-if="selectedApplication" class="space-y-4">
          <!-- 申请人信息 -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-3">申请人信息</h4>
            <div class="flex items-center space-x-4 mb-4">
              <img :src="selectedApplication.avatar" :alt="selectedApplication.name" class="w-16 h-16 rounded-full object-cover">
              <div>
                <h5 class="text-lg font-semibold text-gray-900">{{ selectedApplication.name }}</h5>
                <p class="text-gray-500">{{ selectedApplication.phone }}</p>
                <p class="text-sm text-gray-400">微信: {{ selectedApplication.wechatName }}</p>
              </div>
            </div>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-500">邀请方式:</span>
                <span class="ml-2 font-medium">{{ selectedApplication.inviteType === 'self' ? '自主申请' : '管理邀请' }}</span>
              </div>
              <div v-if="selectedApplication.inviteType === 'admin'">
                <span class="text-gray-500">邀请人:</span>
                <span class="ml-2 font-medium">{{ selectedApplication.inviter }}</span>
              </div>
              <div>
                <span class="text-gray-500">申请时间:</span>
                <span class="ml-2 font-medium">{{ selectedApplication.applyTime }}</span>
              </div>
              <div>
                <span class="text-gray-500">当前状态:</span>
                <span :class="[
                  'ml-2 px-2 py-1 text-xs rounded-full',
                  selectedApplication.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                  selectedApplication.status === 'approved' ? 'bg-green-100 text-green-800' :
                  'bg-red-100 text-red-800'
                ]">
                  {{ getStatusText(selectedApplication.status) }}
                </span>
              </div>
            </div>
          </div>

          <!-- 申请理由 -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-2">申请理由</h4>
            <p class="text-gray-700">{{ selectedApplication.reason || '无' }}</p>
          </div>

          <!-- 审核信息 -->
          <div v-if="selectedApplication.status !== 'pending'" class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-3">审核信息</h4>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-500">审核人:</span>
                <span class="ml-2 font-medium">{{ selectedApplication.reviewer }}</span>
              </div>
              <div>
                <span class="text-gray-500">审核时间:</span>
                <span class="ml-2 font-medium">{{ selectedApplication.reviewTime }}</span>
              </div>
            </div>
            <div v-if="selectedApplication.rejectReason" class="mt-3">
              <span class="text-gray-500">拒绝理由:</span>
              <p class="mt-1 text-red-600">{{ selectedApplication.rejectReason }}</p>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div v-if="selectedApplication.status === 'pending'" class="flex justify-end space-x-3 pt-4 border-t">
            <button @click="rejectApplication(selectedApplication)" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
              拒绝申请
            </button>
            <button @click="approveApplication(selectedApplication)" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
              通过申请
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 拒绝理由弹窗 -->
    <div v-if="showRejectModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">拒绝申请</h3>
          <button @click="showRejectModal = false" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>

        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">拒绝理由</label>
            <textarea
              v-model="rejectReason"
              rows="4"
              placeholder="请输入拒绝理由..."
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            ></textarea>
          </div>

          <div class="flex justify-end space-x-3">
            <button @click="showRejectModal = false" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
              取消
            </button>
            <button @click="confirmReject" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
              确认拒绝
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ReviewStatusPage',
  data() {
    return {
      // 搜索和筛选
      searchQuery: '',
      filterStatus: '',
      filterInviteType: '',

      // 分页
      currentPage: 1,
      pageSize: 10,

      // 弹窗状态
      showDetailModal: false,
      showRejectModal: false,
      selectedApplication: null,
      rejectReason: '',

      // 统计数据
      statistics: {
        totalApplications: 28,
        pendingApplications: 8,
        approvedApplications: 15,
        rejectedApplications: 5
      },

      // 团长申请数据
      applications: [
        {
          id: 1,
          name: '张小明',
          phone: '138****8888',
          wechatName: '张小明_wx',
          avatar: 'https://ui-avatars.com/api/?name=张小明&background=random&size=128',
          inviteType: 'self',
          inviter: null,
          applyTime: '2024-01-15 14:30:25',
          status: 'pending',
          reviewTime: null,
          reviewer: null,
          reason: '我有丰富的销售经验，希望能成为团长，为公司带来更多客户。',
          rejectReason: null
        },
        {
          id: 2,
          name: '李美丽',
          phone: '139****6666',
          wechatName: '美丽人生',
          avatar: 'https://ui-avatars.com/api/?name=李美丽&background=random&size=128',
          inviteType: 'admin',
          inviter: '管理员王总',
          applyTime: '2024-01-14 10:15:30',
          status: 'approved',
          reviewTime: '2024-01-14 16:20:15',
          reviewer: '管理员王总',
          reason: '被管理员邀请成为团长',
          rejectReason: null
        },
        {
          id: 3,
          name: '王大力',
          phone: '137****5555',
          wechatName: '大力出奇迹',
          avatar: 'https://ui-avatars.com/api/?name=王大力&background=random&size=128',
          inviteType: 'self',
          inviter: null,
          applyTime: '2024-01-13 09:45:12',
          status: 'rejected',
          reviewTime: '2024-01-13 18:30:45',
          reviewer: '管理员张总',
          reason: '想要成为团长，推广产品',
          rejectReason: '暂时不符合团长要求，建议先从分销员做起'
        },
        {
          id: 4,
          name: '陈小花',
          phone: '135****7777',
          wechatName: '花花世界',
          avatar: 'https://ui-avatars.com/api/?name=陈小花&background=random&size=128',
          inviteType: 'self',
          inviter: null,
          applyTime: '2024-01-12 16:20:08',
          status: 'pending',
          reviewTime: null,
          reviewer: null,
          reason: '我在社交媒体上有很多粉丝，可以帮助推广产品。',
          rejectReason: null
        },
        {
          id: 5,
          name: '刘强东',
          phone: '136****4444',
          wechatName: '强东哥',
          avatar: 'https://ui-avatars.com/api/?name=刘强东&background=random&size=128',
          inviteType: 'admin',
          inviter: '管理员李总',
          applyTime: '2024-01-11 11:30:22',
          status: 'approved',
          reviewTime: '2024-01-11 14:15:30',
          reviewer: '管理员李总',
          reason: '被管理员邀请成为团长',
          rejectReason: null
        },
        {
          id: 6,
          name: '赵小红',
          phone: '134****3333',
          wechatName: '小红帽',
          avatar: 'https://ui-avatars.com/api/?name=赵小红&background=random&size=128',
          inviteType: 'self',
          inviter: null,
          applyTime: '2024-01-10 08:45:15',
          status: 'pending',
          reviewTime: null,
          reviewer: null,
          reason: '我有线下店铺，可以帮助推广线上产品。',
          rejectReason: null
        }
      ]
    }
  },

  computed: {
    // 过滤后的申请列表
    filteredApplications() {
      return this.applications.filter(app => {
        const matchesSearch = !this.searchQuery ||
          app.name.includes(this.searchQuery) ||
          app.phone.includes(this.searchQuery) ||
          app.wechatName.includes(this.searchQuery);
        const matchesStatus = !this.filterStatus || app.status === this.filterStatus;
        const matchesInviteType = !this.filterInviteType || app.inviteType === this.filterInviteType;

        return matchesSearch && matchesStatus && matchesInviteType;
      });
    },

    // 总页数
    totalPages() {
      return Math.ceil(this.filteredApplications.length / this.pageSize);
    }
  },

  methods: {
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending': '待审核',
        'approved': '已通过',
        'rejected': '已拒绝'
      };
      return statusMap[status] || status;
    },

    // 查看申请详情
    viewApplicationDetail(application) {
      this.selectedApplication = application;
      this.showDetailModal = true;
    },

    // 通过申请
    approveApplication(application) {
      if (confirm(`确认通过 ${application.name} 的团长申请吗？`)) {
        application.status = 'approved';
        application.reviewTime = new Date().toLocaleString('zh-CN');
        application.reviewer = '当前管理员'; // 实际应该是当前登录的管理员

        // 更新统计数据
        this.statistics.pendingApplications--;
        this.statistics.approvedApplications++;

        console.log('通过申请:', application);
        alert('申请已通过');

        // 关闭详情弹窗
        this.showDetailModal = false;
      }
    },

    // 拒绝申请
    rejectApplication(application) {
      this.selectedApplication = application;
      this.rejectReason = '';
      this.showRejectModal = true;
      this.showDetailModal = false;
    },

    // 确认拒绝
    confirmReject() {
      if (!this.rejectReason.trim()) {
        alert('请输入拒绝理由');
        return;
      }

      this.selectedApplication.status = 'rejected';
      this.selectedApplication.reviewTime = new Date().toLocaleString('zh-CN');
      this.selectedApplication.reviewer = '当前管理员';
      this.selectedApplication.rejectReason = this.rejectReason;

      // 更新统计数据
      this.statistics.pendingApplications--;
      this.statistics.rejectedApplications++;

      console.log('拒绝申请:', this.selectedApplication);
      alert('申请已拒绝');

      // 关闭弹窗
      this.showRejectModal = false;
      this.selectedApplication = null;
      this.rejectReason = '';
    },

    // 重置筛选条件
    resetFilters() {
      this.searchQuery = '';
      this.filterStatus = '';
      this.filterInviteType = '';
      this.currentPage = 1;
    },

    // 刷新数据
    refreshData() {
      console.log('刷新团长申请数据');
      // 实际项目中这里会重新请求API
      alert('数据已刷新');
    },

    // 导出数据
    exportData() {
      console.log('导出团长申请数据');
      alert('数据导出功能开发中');
    }
  },

  mounted() {
    console.log('团长审核页面已加载');
  }
}
</script>

<style scoped>
.review-status-page {
  min-height: 100vh;
  background-color: #f9fafb;
}

.page-header {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 表格样式 */
.overflow-x-auto {
  overflow-x: auto;
}

table {
  min-width: 100%;
}

th, td {
  white-space: nowrap;
}

/* 头像样式 */
img {
  object-fit: cover;
}

/* 按钮悬停效果 */
.hover\:bg-blue-700:hover {
  background-color: #1d4ed8;
}

.hover\:bg-green-700:hover {
  background-color: #15803d;
}

.hover\:bg-red-700:hover {
  background-color: #b91c1c;
}

.hover\:bg-gray-700:hover {
  background-color: #374151;
}

.hover\:bg-gray-400:hover {
  background-color: #9ca3af;
}

.hover\:bg-gray-100:hover {
  background-color: #f3f4f6;
}

/* 文本颜色悬停 */
.hover\:text-blue-900:hover {
  color: #1e3a8a;
}

.hover\:text-green-900:hover {
  color: #14532d;
}

.hover\:text-red-900:hover {
  color: #7f1d1d;
}

.hover\:text-gray-600:hover {
  color: #4b5563;
}

/* 行悬停效果 */
.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}

/* 过渡动画 */
.transition-colors {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* 输入框和选择框聚焦效果 */
input:focus, select:focus, textarea:focus {
  outline: none;
  ring: 2px;
  ring-color: #3b82f6;
  border-color: transparent;
}

/* 弹窗样式 */
.fixed {
  position: fixed;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.z-50 {
  z-index: 50;
}

.max-h-\[90vh\] {
  max-height: 90vh;
}

.overflow-y-auto {
  overflow-y: auto;
}

/* 禁用状态 */
.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

/* 网格布局 */
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

/* 响应式设计 */
@media (min-width: 768px) {
  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* 间距 */
.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

/* 分割线 */
.divide-y > * + * {
  border-top-width: 1px;
}

.divide-gray-200 > * + * {
  border-color: #e5e7eb;
}

/* 状态标签颜色 */
.bg-blue-100 {
  background-color: #dbeafe;
}

.text-blue-800 {
  color: #1e40af;
}

.bg-purple-100 {
  background-color: #e9d5ff;
}

.text-purple-800 {
  color: #6b21a8;
}

.bg-yellow-100 {
  background-color: #fef3c7;
}

.text-yellow-800 {
  color: #92400e;
}

.bg-green-100 {
  background-color: #dcfce7;
}

.text-green-800 {
  color: #166534;
}

.bg-red-100 {
  background-color: #fee2e2;
}

.text-red-800 {
  color: #991b1b;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 图标样式 */
.ri-file-list-line,
.ri-time-line,
.ri-check-line,
.ri-close-line,
.ri-refresh-line,
.ri-download-line {
  font-size: inherit;
}

/* 表格头部样式 */
.uppercase {
  text-transform: uppercase;
}

.tracking-wider {
  letter-spacing: 0.05em;
}

/* 文本截断 */
.whitespace-nowrap {
  white-space: nowrap;
}

/* 边框样式 */
.border-t {
  border-top-width: 1px;
}

.border-gray-300 {
  border-color: #d1d5db;
}

/* 背景色 */
.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-gray-300 {
  background-color: #d1d5db;
}

/* 文本颜色 */
.text-gray-400 {
  color: #9ca3af;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-900 {
  color: #111827;
}

.text-red-500 {
  color: #ef4444;
}

.text-red-600 {
  color: #dc2626;
}
</style>
