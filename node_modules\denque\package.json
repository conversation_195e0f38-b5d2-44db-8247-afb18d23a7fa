{"_from": "denque@^2.1.0", "_id": "denque@2.1.0", "_inBundle": false, "_integrity": "sha512-HVQE3AAb/pxF8fQAoiqpvg9i3evqug3hoiwakOyZAwJm+6vZehbkYXZ0l4JxS+I3QxM97v5aaRNhj8v5oBhekw==", "_location": "/denque", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "denque@^2.1.0", "name": "<PERSON><PERSON>", "escapedName": "<PERSON><PERSON>", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/mysql2"], "_resolved": "https://registry.npmjs.org/denque/-/denque-2.1.0.tgz", "_shasum": "e93e1a6569fb5e66f16a3c2a2964617d349d6ab1", "_spec": "denque@^2.1.0", "_where": "D:\\py-ide\\hioshop-miniprogram-master\\node_modules\\mysql2", "author": {"name": "Invertase", "email": "<EMAIL>", "url": "http://github.com/invertase/"}, "bugs": {"url": "https://github.com/invertase/denque/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "<PERSON><PERSON><PERSON>"}], "deprecated": false, "description": "The fastest javascript implementation of a double-ended queue. Used by the official Redis, MongoDB, MariaDB & MySQL libraries for Node.js and many other libraries. Maintains compatability with deque.", "devDependencies": {"benchmark": "^2.1.4", "codecov": "^3.8.3", "double-ended-queue": "^2.1.0-0", "istanbul": "^0.4.5", "mocha": "^3.5.3", "typescript": "^3.4.1"}, "engines": {"node": ">=0.10"}, "homepage": "https://docs.page/invertase/denque", "keywords": ["data-structure", "data-structures", "queue", "double", "end", "ended", "deque", "<PERSON><PERSON>", "double-ended-queue"], "license": "Apache-2.0", "main": "index.js", "name": "<PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/invertase/denque.git"}, "scripts": {"benchmark_2mil": "node benchmark/two_million", "benchmark_fromArray": "node benchmark/fromArray", "benchmark_growth": "node benchmark/growth", "benchmark_remove": "node benchmark/remove", "benchmark_removeOne": "node benchmark/removeOne", "benchmark_splice": "node benchmark/splice", "benchmark_thousand": "node benchmark/thousand", "benchmark_toArray": "node benchmark/toArray", "coveralls": "cat ./coverage/lcov.info | coveralls", "test": "istanbul cover --report lcov _mocha && npm run typescript", "typescript": "tsc --project ./test/type/tsconfig.json"}, "version": "2.1.0"}