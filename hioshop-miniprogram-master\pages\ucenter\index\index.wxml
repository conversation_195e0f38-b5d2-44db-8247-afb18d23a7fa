<view class="container">
    <!-- 用户信息区域 -->
    <view class="user-section">
        <view class="user-content">
            <!-- 顶部功能按钮 -->
            <view class="top-actions">
                <view class="action-btn" bindtap="signIn">
                    <image class="action-icon" src="/images/icon/success-w.png"></image>
                    <text class="action-text">签到</text>
                </view>
                <view class="action-btn" bindtap="showMemberCode">
                    <image class="action-icon" src="/images/icon/weixin-w.png"></image>
                    <text class="action-text">会员码</text>
                </view>
            </view>

            <!-- 用户信息 -->
            <view class="user-info" wx:if="{{hasUserInfo}}" bindtap='goProfile'>
                <view class="user-avatar">
                    <image class='avatar' src="{{userInfo.avatar}}"></image>
                </view>
                <view class="user-details">
                    <view class="user-id">{{userInfo.nickname || '65465'}}</view>
                    <view class="user-level">会员中心</view>
                </view>
            </view>
            <view class="user-info" wx:else bindtap='goAuth'>
                <view class="user-avatar">
                    <image class='avatar' src="/images/icon/default_avatar_big.jpg?v=1"></image>
                </view>
                <view class="user-details">
                    <view class="user-id">点击登录</view>
                    <view class="user-level">会员中心</view>
                </view>
            </view>

            <!-- 推广员提示横幅 -->
            <view class="member-banner">
                <view class="member-text">
                    <text>🎯 成为推广员，享受推广奖励!</text>
                </view>
                <view class="register-btn" bindtap="registerMember">
                    <text>走吧 ></text>
                </view>
            </view>
        </view>
    </view>

    <!-- 数据统计区域 -->
    <view class="stats-section">
        <view class="stats-grid">
            <view class="stats-item" bindtap="toCommission">
                <view class="stats-value">{{commissionInfo.availableCommission || '0.00'}}</view>
                <view class="stats-label">佣金</view>
            </view>
            <view class="stats-item" bindtap="toPoints">
                <view class="stats-value">{{userPoints || '0'}}</view>
                <view class="stats-label">积分</view>
            </view>
            <view class="stats-item" bindtap="toMyCoupons">
                <view class="stats-value">{{couponCount || '0'}}</view>
                <view class="stats-label">优惠券</view>
            </view>
            <view class="stats-item" bindtap="toDataCenter">
                <view class="stats-value">{{orderCount || '0'}}</view>
                <view class="stats-label">数据</view>
            </view>
        </view>
    </view>

    <!-- 营销横幅 - 复制自个人中心.html -->
    <view class="banner-section">
        <image class="banner-image" src="https://readdy.ai/api/search-image?query=clean%20modern%20product%20banner%2C%20mint%20green%20background%2C%20minimalist%20product%20bottles%2C%20natural%20skincare%20products%2C%20fresh%20and%20elegant%20design%20style&width=375&height=100&seq=2&orientation=landscape" mode="aspectFill"></image>
    </view>
    <!-- 我的订单 -->
    <view class="order-section">
        <view class="section-header">
            <view class="section-title">我的订单</view>
            <view class="section-more" bindtap='toOrderListTap' data-index='0'>
                <text>查看全部订单 ></text>
            </view>
        </view>
        <view class="order-icons">
            <view class="order-item" data-index='1' bindtap='toOrderListTap'>
                <view class="order-icon-wrap">
                    <image class="order-icon" src="/images/icon/icon-pay-r.png"></image>
                    <view wx:if="{{status.toPay > 0}}" class="badge">{{status.toPay}}</view>
                </view>
                <text class="order-text">待付款</text>
            </view>
            <view class="order-item" data-index='2' bindtap='toOrderListTap'>
                <view class="order-icon-wrap">
                    <image class="order-icon" src="/images/icon/icon-delivery-r.png"></image>
                    <view wx:if="{{status.toDelivery > 0}}" class="badge">{{status.toDelivery}}</view>
                </view>
                <text class="order-text">待发货</text>
            </view>
            <view class="order-item" data-index='3' bindtap='toOrderListTap'>
                <view class="order-icon-wrap">
                    <image class="order-icon" src="/images/icon/icon-onroad-r.png"></image>
                    <view wx:if="{{status.toReceive > 0}}" class="badge">{{status.toReceive}}</view>
                </view>
                <text class="order-text">待收货</text>
            </view>
            <view class="order-item" bindtap="toOrderExchange">
                <view class="order-icon-wrap">
                    <image class="order-icon" src="/images/icon/dingdanyouli.png"></image>
                </view>
                <text class="order-text">订单有礼</text>
            </view>
        </view>
    </view>

    <!-- 常用功能 -->
    <view class="order-section">
        <view class="section-header">
            <view class="section-title">常用功能</view>
        </view>
        <view class="order-icons">
            <view class="order-item" bindtap="toMyCoupons">
                <view class="order-icon-wrap">
                    <image class="order-icon" src="/images/icon/优惠券.png"></image>
                    <view wx:if="{{couponCount > 0}}" class="badge">{{couponCount}}</view>
                </view>
                <text class="order-text">我的优惠券</text>
            </view>
            <view class="order-item" bindtap="toAddressList">
                <view class="order-icon-wrap">
                    <image class="order-icon" src="/images/icon/icon-address-r.png"></image>
                </view>
                <text class="order-text">地址管理</text>
            </view>
            <view class="order-item" bindtap="toFootprint">
                <view class="order-icon-wrap">
                    <image class="order-icon" src="/images/icon/icon-footprint-r.png"></image>
                </view>
                <text class="order-text">我的足迹</text>
            </view>
            <button class="order-item" session-from='{"nickName":"{{userInfo.nickname}}","avatarUrl":"{{userInfo.avatar}}"}' open-type="contact" show-message-card="true" hover-class="none">
                <view class="order-icon-wrap">
                    <image class="order-icon" src="/images/icon/icon-service-r.png"></image>
                </view>
                <text class="order-text">联系客服</text>
            </button>
        </view>
    </view>



    <!-- 底部公司信息 -->
    <view class="company">
        <view class="c-wrap" bindtap="getOrderInfo">
            <view class="text">美汐缘</view>
            <view class="line"></view>
        </view>
        <view class="tip">微信小程序商城</view>
    </view>


</view>