/* pages/app-authorization/index.wxss */

.container {
    background-color: #fff;
    min-height: 100%;
    align-items: stretch;
    overflow-x: hidden;
    position: relative;
    width: 100%;
    padding-top: 100rpx;
}

.logo{
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 60rpx;
}

.logo .logo-img{
    width: 120rpx;
    height: 120rpx;
}


.logo-name{
    width: 100%;
    text-align: center;
    height: 50rpx;
    line-height: 50rpx;
    font-size: 32rpx;
    margin-top: 40rpx;
}

.intro{
    width: 100%;
    text-align: center;
    height: 80rpx;
    line-height: 80rpx;
    font-size: 26rpx;
    color: #555;
    margin-bottom: 30rpx;
}

.login{
    width: 100%;
    height: 80rpx;
    margin-top: 80rpx;
    font-size: 28rpx;
    color: #999;
    text-align: center;
}

.btn-login{
    display: flex;
    justify-content: center;
    height: 100rpx;
    width: 600rpx;
    background: #54b635;
    align-items: center;
    border-radius: 50rpx;
    margin: 0 auto;
    padding: 0;
    border: none;
}

.button-hover {
    background: #48a12e !important;
    opacity: 0.8;
}

.img-w{
    height: 40rpx;
    width: 40rpx;
}

.text{
    font-size: 30rpx;
    color: #fff;
    margin-left: 10rpx;
}

.cancel{
    width: 100%;
    height: 100rpx;
    line-height: 100rpx;
    text-align: center;
    font-size: 28rpx;
    color: #555;
    margin-top: 30rpx;
}

/* 新的美观设计样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 顶部装饰 */
.header-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 300rpx;
  overflow: hidden;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: -100rpx;
  right: -50rpx;
  animation: float 6s ease-in-out infinite;
}

.circle-2 {
  width: 120rpx;
  height: 120rpx;
  top: 50rpx;
  left: -30rpx;
  animation: float 4s ease-in-out infinite reverse;
}

.circle-3 {
  width: 80rpx;
  height: 80rpx;
  top: 150rpx;
  right: 100rpx;
  animation: float 5s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* 标题区域 */
.title-section {
  padding: 120rpx 60rpx 80rpx;
  text-align: center;
  position: relative;
  z-index: 2;
}

.main-title {
  font-size: 48rpx;
  color: white;
  font-weight: 700;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.sub-title {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
}

/* 设置卡片 */
.profile-card {
  margin: 0 40rpx 60rpx;
  background: white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

/* 区域标签 */
.section-label {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  position: relative;
}

.label-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.label-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  flex: 1;
}

.status-badge {
  width: 40rpx;
  height: 40rpx;
  background: #07c160;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

/* 头像区域 */
.avatar-section {
  margin-bottom: 60rpx;
}

.avatar-btn {
  display: block;
  width: 200rpx;
  height: 200rpx;
  margin: 0 auto;
  background: none;
  border: none;
  padding: 0;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.avatar-btn::after {
  border: none;
}

.avatar-btn:active {
  transform: scale(0.95);
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 16rpx;
  text-align: center;
  font-size: 22rpx;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.avatar-btn:hover .avatar-overlay,
.avatar-btn:active .avatar-overlay {
  transform: translateY(0);
}

/* 分割线 */
.divider {
  height: 2rpx;
  background: #f0f0f0;
  margin: 40rpx 0;
}

/* 昵称区域 */
.nickname-section {
  margin-bottom: 40rpx;
}

/* 手机号区域 */
.phone-section {
  margin-bottom: 40rpx;
}

.phone-btn {
  width: 100%;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  color: #495057;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}

.phone-btn::after {
  border: none;
}

.phone-btn:active {
  background: #e9ecef;
  border-color: #dee2e6;
}

.phone-btn-text {
  font-size: 28rpx;
  color: #495057;
}

.phone-display {
  padding: 16rpx 20rpx;
  background: #e8f5e8;
  border-radius: 8rpx;
  border: 1rpx solid #c3e6c3;
}

.phone-text {
  font-size: 26rpx;
  color: #155724;
  font-weight: 500;
}

.input-wrapper {
  position: relative;
}

.nickname-input {
  width: 100%;
  height: 100rpx;
  border: 3rpx solid #f0f0f0;
  border-radius: 16rpx;
  padding: 0 30rpx;
  font-size: 32rpx;
  box-sizing: border-box;
  transition: all 0.3s ease;
  background: #fafafa;
}

.nickname-input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.input-placeholder {
  color: #ccc;
}

.input-counter {
  position: absolute;
  right: 20rpx;
  bottom: -40rpx;
  font-size: 24rpx;
  color: #999;
}

/* 完成按钮 */
.action-section {
  margin: 0 40rpx 60rpx;
  position: relative;
  z-index: 2;
}

.complete-btn {
  width: 100%;
  height: 100rpx;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.complete-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.3);
}

.complete-btn.disabled {
  background: #f0f0f0;
  color: #ccc;
}

.complete-btn::after {
  border: none;
}

.complete-btn:active {
  transform: scale(0.98);
}

.btn-text {
  margin-right: 10rpx;
}

.btn-icon {
  font-size: 28rpx;
  transition: transform 0.3s ease;
}

.complete-btn.active:active .btn-icon {
  transform: translateX(10rpx);
}

/* 底部提示 */
.footer-tips {
  margin: 0 60rpx 40rpx;
  position: relative;
  z-index: 2;
}

.tip-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.tip-icon {
  font-size: 28rpx;
  margin-right: 16rpx;
}

.tip-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

