/**
 * 旺店通ERP配置
 */

const isDev = process.env.NODE_ENV === 'development';

module.exports = {
    // 当前使用的配置 - 默认使用测试环境
    current: 'test',

    // 测试环境配置
    test: {
        sid: 'rx00002', // 卖家账号
        appkey: 'rx00002-gw', // 接口账号
        appsecret: '42af07635c3b606a075ff68146c93405', // 接口密钥
        baseUrl: 'https://api.wangdian.cn/openapi2', // 生产环境
        shopNo: 'XCX0001', // 店铺编号
        warehouseNo: '0021', // 仓库编号
        platformId: 127, // 平台ID
        enabled: true // 是否启用
    },

    // 生产环境配置
    production: {
        sid: 'your_production_sid', // 生产环境卖家账号
        appkey: 'your_production_appkey', // 生产环境接口账号
        appsecret: 'your_production_secret', // 生产环境接口密钥
        baseUrl: 'https://api.wangdian.cn/openapi2', // 生产环境
        shopNo: 'your_production_shop', // 生产环境店铺编号
        warehouseNo: 'your_production_warehouse', // 生产环境仓库编号
        platformId: 127, // 平台ID
        enabled: false // 生产环境暂时禁用，需要配置后启用
    }
};