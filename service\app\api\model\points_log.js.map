{"version": 3, "sources": ["..\\..\\..\\src\\api\\model\\points_log.js"], "names": ["module", "exports", "think", "Model", "tableName"], "mappings": "AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;;AAEzC,MAAIC,SAAJ,GAAgB;AACd,WAAO,YAAP;AACD;;AAJwC,CAA3C", "file": "..\\..\\..\\src\\api\\model\\points_log.js", "sourcesContent": ["module.exports = class extends think.Model {\n  \n  get tableName() {\n    return 'points_log';\n  }\n  \n};\n"]}