{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\user.js"], "names": ["Base", "require", "fs", "_", "moment", "module", "exports", "pointsAction", "userId", "getLoginUserId", "console", "log", "pointsInfo", "points", "totalPoints", "availablePoints", "usedPoints", "success", "error", "fail", "message"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,KAAKD,QAAQ,IAAR,CAAX;AACA,MAAME,IAAIF,QAAQ,QAAR,CAAV;AACA,MAAMG,SAASH,QAAQ,QAAR,CAAf;;AAEAI,OAAOC,OAAP,GAAiB,cAAcN,IAAd,CAAmB;;AAEhC;;;;AAIMO,gBAAN,GAAqB;AAAA;;AAAA;AACjB,gBAAI;AACA,sBAAMC,SAAS,MAAM,MAAKC,cAAL,EAArB;;AAEAC,wBAAQC,GAAR,CAAY,gCAAZ;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBH,MAArB;;AAEA;AACA,sBAAMI,aAAa;AACfC,4BAAQ,CADO;AAEfC,iCAAa,CAFE;AAGfC,qCAAiB,CAHF;AAIfC,gCAAY;AAJG,iBAAnB;;AAOAN,wBAAQC,GAAR,CAAY,WAAZ,EAAyBC,UAAzB;AACA,uBAAO,MAAKK,OAAL,CAAaL,UAAb,CAAP;AAEH,aAjBD,CAiBE,OAAOM,KAAP,EAAc;AACZR,wBAAQQ,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO,MAAKC,IAAL,CAAU,eAAeD,MAAME,OAA/B,CAAP;AACH;AArBgB;AAsBpB;AA5B+B,CAApC", "file": "..\\..\\..\\src\\api\\controller\\user.js", "sourcesContent": ["const Base = require('./base.js');\nconst fs = require('fs');\nconst _ = require('lodash');\nconst moment = require('moment');\n\nmodule.exports = class extends Base {\n\n    /**\n     * 获取用户积分信息\n     * GET /api/user/points\n     */\n    async pointsAction() {\n        try {\n            const userId = await this.getLoginUserId();\n\n            console.log('=== 获取用户积分信息（已移除积分系统，返回默认值）===');\n            console.log('用户ID:', userId);\n\n            // 积分系统已移除，直接返回默认值以保持API兼容性\n            const pointsInfo = {\n                points: 0,\n                totalPoints: 0,\n                availablePoints: 0,\n                usedPoints: 0\n            };\n\n            console.log('返回默认积分信息:', pointsInfo);\n            return this.success(pointsInfo);\n\n        } catch (error) {\n            console.error('获取用户积分信息失败:', error);\n            return this.fail('获取积分信息失败: ' + error.message);\n        }\n    }\n};"]}