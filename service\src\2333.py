import random, flask, time, requests, hashlib,json
from flask import request
from urllib.parse import quote

requests.packages.urllib3.disable_warnings()
from requests.packages.urllib3.exceptions import InsecureRequestWarning
# 禁用安全请求警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)
def get_gorgon(__url, header_list):
    start_time = time.time()
    proxies = {"http": "http://127.0.0.1:8887",
               "https": "http://127.0.0.1:8887"}
    url = f"http://193.112.191.153:8870/api/dy/encrypt"
    header_str = json.dumps(header_list, ensure_ascii=False)

    data = f"url={quote(__url)}&headerList={quote(header_str)}"
    headers = {
        'content-type': 'application/x-www-form-urlencoded'
    }
    response = requests.post(url, headers=headers, data=data,verify=False)
    res = response.json()
    # print(json.dumps(res, indent=2))
    print(f"耗时：{time.time() - start_time}")
    return res
server = flask.Flask(__name__)
@server.route('/', methods=['get', 'post'])
def reg():
    code = request.form.get('code')
    if code == 'sku':
        install_id = str(request.form.get('install_id'))
        device_id = str(request.form.get('device_id'))
        itemId = str(request.form.get('itemId'))
        cookie = str(request.form.get('cookie'))
        token = str(request.form.get('token'))
        print(install_id,device_id)
        url = 'https://ecom5-normal-hl.ecombdapi.com/aweme/v2/sku/yata/query?klink_egdi=AAIDss-2YdJtl-KUmWmBYJJyMOc39i6bFa0SSqo3VWs6fIpVdsZrSRFe&iid=' + install_id + '&device_id=' + device_id + '&ac=wifi&channel=douyin-huidu-gw-aweme-3150&aid=1128&app_name=aweme&version_code=310400&version_name=31.4.0&device_platform=android&os=android&ssmix=a&device_type=Pixel+4a&device_brand=google&language=zh&os_api=30&os_version=11&manifest_version_code=310401&resolution=1080*2160&dpi=440&update_version_code=31409900&_rticket=1752265641206&first_launch_timestamp=1725809108&last_deeplink_update_version_code=0&cpu_support64=true&host_abi=arm64-v8a&is_guest_mode=0&app_type=normal&minor_status=0&appTheme=light&is_preinstall=0&need_personal_recommend=1&is_android_pad=0&is_android_fold=0&ts=1752265891&cdid=c9cd88ac-4278-4fce-9085-52e3a4f0c356'
        data = '{"ex_params":{"sku_schema_param":{"product_info":{"product_id":"' + itemId + '","promotion_id":"' + itemId + '","promotion_source":6,"is_multi":true,"shop_id":"163248794"}}}}'
        """构造抖音话题请求"""
        url_prefix, url_suffix = url.split('?')
        url_suffix_map = {i.split("=")[0]: i.split("=")[-1] for i in url_suffix.split("&")}
        # url_suffix_map['ts'] = int(time.time())
        url_suffix_map['iid'] = install_id
        url_suffix_map['device_id'] = device_id
        url = url_prefix + '?' + '&'.join([f"{i}={url_suffix_map[i]}" for i in url_suffix_map])
        headers = {'Activity_now_client': '1752265429529',
                   'sdk-version': '2',
                   'Cookie': cookie,
                   'X-vc-bdturing-sdk-version': '3.7.3.cn',
                   'passport-sdk-version': '35160',
                   'User-Agent': 'com.ss.android.ugc.aweme/310401 (Linux; U; Android 11; zh_CN_#Hans; Pixel 4a; Build/RQ3A.211001.001;tt-ok/*********-tiktok)',
                   'Host': 'ecom5-normal-hl.ecombdapi.com',
                    'X-Tt-Token': token,
                   'x-tt-passport-mfa-token': 'CjzBbp9nZeGn/EyKcGqxMde7uYYtAo2/qj3m2THnP/4ft7ew6LbelFHy5q2IkQd2C24kr9LMYfHMvR3cLMwaSgo8AAAAAAAAAAAAAE7pLmjKFjowFtxzvqXViXCaqMCUgtK3UvZJ78rmn2M+yviRLWiEYdVrgjcZnuoGv4IaEJq+7w0Y9rHRbCACIgEDrgSnfg==',
                   'x-tt-token-supplement': '03d676809d0864b9469d148cf2eff09eb066b3fbb79e123f50548f933972c5d4829b319c403071a29f37b7835411994880f0a4d3485133fdf5d41d436fb15d87d18',
                   'Content-Type': 'application/json; charset=UTF-8'}
        if data:
            headers['x-ss-stub'] = hashlib.md5(data.encode()).hexdigest().upper()
        header_list = list()
        for key in sorted(headers):
            header_list.append(key)
            header_list.append(headers[key])
        x_gorgon_headers = get_gorgon(url, header_list)
        for k, v in x_gorgon_headers.items():
            headers[k.lower()] = v
        if data:
            response = requests.request('POST', url, headers=headers, data=data, verify=False)
        else:
            response = requests.request('GET', url, headers=headers, verify=False)
        try:
            print('调用成功')
            return response.json()
        except:
            print('调用失败')
            return ''
    else:
        return '参数错误'


server.config['JSON_AS_ASCII'] = False
server.run(port=2333, debug=False, host='0.0.0.0', threaded=True)