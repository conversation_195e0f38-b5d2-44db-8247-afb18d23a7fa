{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSalePage.vue?vue&type=template&id=621a3ebd&scoped=true&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Marketing\\FlashSalePage.vue", "mtime": 1753723289742}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}