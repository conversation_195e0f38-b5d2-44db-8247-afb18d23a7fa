{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Test\\FlashSaleApiTest.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Test\\FlashSaleApiTest.vue", "mtime": 1753712463451}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["FlashSaleApiTest.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "FlashSaleApiTest.vue", "sourceRoot": "src/components/Test", "sourcesContent": ["<template>\n  <div class=\"api-test-container\">\n    <h2>秒杀API测试</h2>\n    \n    <div class=\"test-section\">\n      <h3>API测试结果</h3>\n      <div class=\"test-buttons\">\n        <el-button @click=\"testTimeSlots\" type=\"primary\">测试时段列表API</el-button>\n        <el-button @click=\"testStatistics\" type=\"success\">测试统计数据API</el-button>\n        <el-button @click=\"testProducts\" type=\"info\">测试商品列表API</el-button>\n      </div>\n      \n      <div v-if=\"errorMessage\" class=\"error-message\">\n        <h4>错误信息:</h4>\n        <pre>{{ errorMessage }}</pre>\n      </div>\n      \n      <div v-if=\"testResult\" class=\"test-result\">\n        <h4>测试结果:</h4>\n        <pre>{{ testResult }}</pre>\n      </div>\n    </div>\n    \n    <div class=\"debug-section\">\n      <h3>调试信息</h3>\n      <p><strong>当前Token:</strong> {{ currentToken || '无' }}</p>\n      <p><strong>API Base URL:</strong> {{ baseURL }}</p>\n      <p><strong>完整URL示例:</strong> {{ baseURL }}flash-sale/time-slots</p>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FlashSaleApiTest',\n  data() {\n    return {\n      testResult: '',\n      errorMessage: '',\n      currentToken: '',\n      baseURL: ''\n    }\n  },\n  \n  mounted() {\n    this.currentToken = localStorage.getItem('token') || '';\n    this.baseURL = this.axios.defaults.baseURL || '';\n  },\n  \n  methods: {\n    async testTimeSlots() {\n      try {\n        this.errorMessage = '';\n        this.testResult = '正在测试时段列表API...';\n        \n        const response = await this.axios.get('flash-sale/time-slots');\n        \n        this.testResult = JSON.stringify(response.data, null, 2);\n      } catch (error) {\n        this.errorMessage = `时段列表API测试失败: ${error.message}`;\n        this.testResult = JSON.stringify(error.response?.data || error, null, 2);\n      }\n    },\n    \n    async testStatistics() {\n      try {\n        this.errorMessage = '';\n        this.testResult = '正在测试统计数据API...';\n        \n        const response = await this.axios.get('flash-sale/statistics');\n        \n        this.testResult = JSON.stringify(response.data, null, 2);\n      } catch (error) {\n        this.errorMessage = `统计数据API测试失败: ${error.message}`;\n        this.testResult = JSON.stringify(error.response?.data || error, null, 2);\n      }\n    },\n    \n    async testProducts() {\n      try {\n        this.errorMessage = '';\n        this.testResult = '正在测试商品列表API...';\n        \n        const response = await this.axios.get('flash-sale/products');\n        \n        this.testResult = JSON.stringify(response.data, null, 2);\n      } catch (error) {\n        this.errorMessage = `商品列表API测试失败: ${error.message}`;\n        this.testResult = JSON.stringify(error.response?.data || error, null, 2);\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.api-test-container {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.test-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n}\n\n.debug-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #e6a23c;\n  border-radius: 8px;\n  background-color: #fdf6ec;\n}\n\n.test-buttons {\n  margin-bottom: 20px;\n}\n\n.test-buttons .el-button {\n  margin-right: 10px;\n  margin-bottom: 10px;\n}\n\n.error-message {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #fef0f0;\n  border: 1px solid #fbc4c4;\n  border-radius: 4px;\n  color: #f56c6c;\n}\n\n.test-result {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f0f9ff;\n  border: 1px solid #c4e1ff;\n  border-radius: 4px;\n}\n\npre {\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  max-height: 400px;\n  overflow-y: auto;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n  line-height: 1.4;\n}\n\nh2 {\n  color: #303133;\n  margin-bottom: 20px;\n}\n\nh3 {\n  color: #606266;\n  margin-bottom: 15px;\n}\n\nh4 {\n  color: #909399;\n  margin-bottom: 10px;\n}\n</style>\n"]}]}