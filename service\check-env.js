const path = require('path');
const fs = require('fs');
const os = require('os');

console.log('=== 环境检测 ===');
console.log('操作系统:', os.platform());
console.log('Node.js版本:', process.version);
console.log('当前工作目录:', process.cwd());

const ROOT_PATH = __dirname;
const APP_PATH = path.join(ROOT_PATH, 'app');
const SRC_PATH = path.join(ROOT_PATH, 'src');

console.log('\n=== 路径检测 ===');
console.log('项目根目录:', ROOT_PATH);
console.log('源码目录:', SRC_PATH);
console.log('编译输出目录:', APP_PATH);

console.log('\n=== 文件检测 ===');

// 检查关键目录
const checkDir = (dirPath, name) => {
  if (fs.existsSync(dirPath)) {
    console.log(`✓ ${name} 存在: ${dirPath}`);
    return true;
  } else {
    console.log(`✗ ${name} 不存在: ${dirPath}`);
    return false;
  }
};

// 检查关键文件
const checkFile = (filePath, name) => {
  if (fs.existsSync(filePath)) {
    console.log(`✓ ${name} 存在: ${filePath}`);
    return true;
  } else {
    console.log(`✗ ${name} 不存在: ${filePath}`);
    return false;
  }
};

// 检查目录结构
checkDir(SRC_PATH, '源码目录');
checkDir(path.join(SRC_PATH, 'admin'), 'admin目录');
checkDir(path.join(SRC_PATH, 'admin', 'controller'), 'controller目录');

// 检查源文件
checkFile(path.join(SRC_PATH, 'admin', 'controller', 'auth.js'), '源码 auth.js');
checkFile(path.join(SRC_PATH, 'admin', 'controller', 'base.js'), '源码 base.js');

// 检查编译后的文件
checkDir(APP_PATH, '编译输出目录');
if (fs.existsSync(APP_PATH)) {
  checkDir(path.join(APP_PATH, 'admin'), '编译后 admin目录');
  checkDir(path.join(APP_PATH, 'admin', 'controller'), '编译后 controller目录');
  checkFile(path.join(APP_PATH, 'admin', 'controller', 'auth.js'), '编译后 auth.js');
  checkFile(path.join(APP_PATH, 'admin', 'controller', 'base.js'), '编译后 base.js');
}

// 检查package.json
checkFile(path.join(ROOT_PATH, 'package.json'), 'package.json');

// 检查node_modules
checkDir(path.join(ROOT_PATH, 'node_modules'), 'node_modules');

console.log('\n=== 依赖检测 ===');

// 检查关键依赖
const checkDependency = (name) => {
  try {
    require.resolve(name);
    console.log(`✓ ${name} 已安装`);
    return true;
  } catch (error) {
    console.log(`✗ ${name} 未安装`);
    return false;
  }
};

checkDependency('thinkjs');
checkDependency('think-babel');
checkDependency('think-watcher');
checkDependency('babel-cli');
checkDependency('babel-preset-think-node');

console.log('\n=== 修复建议 ===');

if (!fs.existsSync(APP_PATH)) {
  console.log('1. 运行编译命令: npm run compile');
}

if (!fs.existsSync(path.join(ROOT_PATH, 'node_modules'))) {
  console.log('2. 安装依赖: npm install');
}

if (!fs.existsSync(path.join(APP_PATH, 'admin', 'controller', 'auth.js'))) {
  console.log('3. 重新编译: npm run build');
}

console.log('\n=== 检测完成 ===');
