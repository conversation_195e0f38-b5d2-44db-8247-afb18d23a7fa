function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

const Base = require('./base.js');
const moment = require('moment');
const rp = require('request-promise');
const fs = require('fs');
const http = require("http");
module.exports = class extends Base {
    /**
     * 获取订单列表
     * @return {Promise} []
     */
    listAction() {
        var _this = this;

        return _asyncToGenerator(function* () {
            const userId = yield _this.getLoginUserId();
            const showType = _this.get('showType');
            const page = _this.get('page');
            const size = _this.get('size');
            let status = [];
            status = yield _this.model('order').getOrderStatus(showType);
            let is_delete = 0;
            const orderList = yield _this.model('order').field('id,add_time,actual_price,freight_price,offline_pay').where({
                user_id: userId,
                is_delete: is_delete,
                order_type: ['<', 7],
                order_status: ['IN', status]
            }).page(page, size).order('add_time DESC').countSelect();
            const newOrderList = [];
            for (const item of orderList.data) {
                // 订单的商品
                item.goodsList = yield _this.model('order_goods').field('id,goods_name,goods_aka,list_pic_url,number,retail_price,goods_specifition_name_value').where({
                    user_id: userId,
                    order_id: item.id,
                    is_delete: 0
                }).select();
                item.goodsCount = 0;
                item.goodsList.forEach(function (v) {
                    item.goodsCount += v.number;
                });
                item.add_time = moment.unix((yield _this.model('order').getOrderAddTime(item.id))).format('YYYY-MM-DD HH:mm:ss');
                // item.dealdone_time = moment.unix(await this.model('order').getOrderAddTime(item.id)).format('YYYY-MM-DD HH:mm:ss');
                // item.add_time =this.timestampToTime(await this.model('order').getOrderAddTime(item.id));
                // 订单状态的处理
                item.order_status_text = yield _this.model('order').getOrderStatusText(item.id);
                // 可操作的选项
                item.handleOption = yield _this.model('order').getOrderHandleOption(item.id);

                // 获取售后申请信息
                let refundApply = yield _this.model('refund_apply').where({
                    order_id: item.id
                }).find();

                if (!think.isEmpty(refundApply)) {
                    // 订单列表只需要基本的售后状态信息
                    item.refundInfo = {
                        status: refundApply.status,
                        refund_type: refundApply.refund_type,
                        status_text: _this.getRefundStatusText(refundApply.status),
                        type_text: _this.getRefundTypeText(refundApply.refund_type)
                    };
                    item.hasRefund = true;

                    // 如果有售后申请，优先显示售后状态
                    item.display_status_text = item.refundInfo.type_text + item.refundInfo.status_text;
                } else {
                    item.refundInfo = null;
                    item.hasRefund = false;
                    item.display_status_text = item.order_status_text;
                }

                newOrderList.push(item);
            }
            orderList.data = newOrderList;
            return _this.success(orderList);
        })();
    }
    // 获得订单数量
    //
    countAction() {
        var _this2 = this;

        return _asyncToGenerator(function* () {
            const showType = _this2.get('showType');
            const userId = yield _this2.getLoginUserId();
            let status = [];
            status = yield _this2.model('order').getOrderStatus(showType);
            let is_delete = 0;
            const allCount = yield _this2.model('order').where({
                user_id: userId,
                is_delete: is_delete,
                order_status: ['IN', status]
            }).count('id');
            return _this2.success({
                allCount: allCount
            });
        })();
    }
    // 获得订单数量状态
    //
    orderCountAction() {
        var _this3 = this;

        return _asyncToGenerator(function* () {
            // const user_id = this.getLoginUserId();;
            const user_id = yield _this3.getLoginUserId();
            if (user_id != 0) {
                let toPay = yield _this3.model('order').where({
                    user_id: user_id,
                    is_delete: 0,
                    order_type: ['<', 7],
                    order_status: ['IN', '101,801']
                }).count('id');
                // 获取有售后申请的订单ID
                const refundOrderIds = yield _this3.model('refund_apply').where({
                    user_id: user_id
                }).field('order_id').select();
                const excludeOrderIds = refundOrderIds.map(function (item) {
                    return item.order_id;
                });

                let toDeliveryCondition = {
                    user_id: user_id,
                    is_delete: 0,
                    order_type: ['<', 7],
                    order_status: ['IN', '201,300'] // 包括已付款201和待发货300状态
                };

                // 排除有售后申请的订单
                if (excludeOrderIds.length > 0) {
                    toDeliveryCondition.id = ['NOT IN', excludeOrderIds];
                }

                let toDelivery = yield _this3.model('order').where(toDeliveryCondition).count('id');
                let toReceiveCondition = {
                    user_id: user_id,
                    order_type: ['<', 7],
                    is_delete: 0,
                    order_status: 301
                };

                // 排除有售后申请的订单
                if (excludeOrderIds.length > 0) {
                    toReceiveCondition.id = ['NOT IN', excludeOrderIds];
                }

                let toReceive = yield _this3.model('order').where(toReceiveCondition).count('id');
                let newStatus = {
                    toPay: toPay,
                    toDelivery: toDelivery,
                    toReceive: toReceive
                };
                return _this3.success(newStatus);
            }
        })();
    }
    detailAction() {
        var _this4 = this;

        return _asyncToGenerator(function* () {
            const orderId = _this4.get('orderId');
            const userId = yield _this4.getLoginUserId();
            const orderInfo = yield _this4.model('order').where({
                user_id: userId,
                id: orderId
            }).find();
            const currentTime = parseInt(new Date().getTime() / 1000);
            if (think.isEmpty(orderInfo)) {
                return _this4.fail('订单不存在');
            }
            orderInfo.province_name = yield _this4.model('region').where({
                id: orderInfo.province
            }).getField('name', true);
            orderInfo.city_name = yield _this4.model('region').where({
                id: orderInfo.city
            }).getField('name', true);
            orderInfo.district_name = yield _this4.model('region').where({
                id: orderInfo.district
            }).getField('name', true);
            orderInfo.full_region = orderInfo.province_name + orderInfo.city_name + orderInfo.district_name;
            orderInfo.postscript = Buffer.from(orderInfo.postscript, 'base64').toString();
            const orderGoods = yield _this4.model('order_goods').where({
                user_id: userId,
                order_id: orderId,
                is_delete: 0
            }).select();
            var goodsCount = 0;
            for (const gitem of orderGoods) {
                goodsCount += gitem.number;
            }
            // 订单状态的处理
            orderInfo.order_status_text = yield _this4.model('order').getOrderStatusText(orderId);
            if (think.isEmpty(orderInfo.confirm_time)) {
                orderInfo.confirm_time = 0;
            } else orderInfo.confirm_time = moment.unix(orderInfo.confirm_time).format('YYYY-MM-DD HH:mm:ss');
            if (think.isEmpty(orderInfo.dealdone_time)) {
                orderInfo.dealdone_time = 0;
            } else orderInfo.dealdone_time = moment.unix(orderInfo.dealdone_time).format('YYYY-MM-DD HH:mm:ss');
            if (think.isEmpty(orderInfo.pay_time)) {
                orderInfo.pay_time = 0;
            } else orderInfo.pay_time = moment.unix(orderInfo.pay_time).format('YYYY-MM-DD HH:mm:ss');
            if (think.isEmpty(orderInfo.shipping_time)) {
                orderInfo.shipping_time = 0;
            } else {
                orderInfo.confirm_remainTime = orderInfo.shipping_time + 10 * 24 * 60 * 60;
                orderInfo.shipping_time = moment.unix(orderInfo.shipping_time).format('YYYY-MM-DD HH:mm:ss');
            }
            // 订单支付倒计时
            if (orderInfo.order_status === 101 || orderInfo.order_status === 801) {
                // if (moment().subtract(60, 'minutes') < moment(orderInfo.add_time)) {
                orderInfo.final_pay_time = orderInfo.add_time + 24 * 60 * 60; //支付倒计时2小时
                if (orderInfo.final_pay_time < currentTime) {
                    //超过时间不支付，更新订单状态为取消
                    let updateInfo = {
                        order_status: 102
                    };
                    yield _this4.model('order').where({
                        id: orderId
                    }).update(updateInfo);
                }
            }
            orderInfo.add_time = moment.unix(orderInfo.add_time).format('YYYY-MM-DD HH:mm:ss');
            orderInfo.order_status = '';
            // 订单可操作的选择,删除，支付，收货，评论，退换货
            const handleOption = yield _this4.model('order').getOrderHandleOption(orderId);
            const textCode = yield _this4.model('order').getOrderTextCode(orderId);
            return _this4.success({
                orderInfo: orderInfo,
                orderGoods: orderGoods,
                handleOption: handleOption,
                textCode: textCode,
                goodsCount: goodsCount
            });
        })();
    }
    /**
     * order 和 order-check 的goodslist
     * @return {Promise} []
     */
    orderGoodsAction() {
        var _this5 = this;

        return _asyncToGenerator(function* () {
            const userId = yield _this5.getLoginUserId();
            const orderId = _this5.get('orderId');
            if (orderId > 0) {
                const orderGoods = yield _this5.model('order_goods').field('id,goods_name,goods_aka,list_pic_url,number,retail_price,goods_specifition_name_value,goods_id,product_id').where({
                    user_id: userId,
                    order_id: orderId,
                    is_delete: 0
                }).select();
                var goodsCount = 0;
                for (const gitem of orderGoods) {
                    goodsCount += gitem.number;
                }
                return _this5.success(orderGoods);
            } else {
                const cartList = yield _this5.model('cart').where({
                    user_id: userId,
                    checked: 1,
                    is_delete: 0,
                    is_fast: 0
                }).select();
                return _this5.success(cartList);
            }
        })();
    }
    /**
     * 取消订单
     * @return {Promise} []
     */
    cancelAction() {
        var _this6 = this;

        return _asyncToGenerator(function* () {
            const orderId = _this6.post('orderId');
            const userId = yield _this6.getLoginUserId();
            // 检测是否能够取消
            const handleOption = yield _this6.model('order').getOrderHandleOption(orderId);
            // console.log('--------------' + handleOption.cancel);
            if (!handleOption.cancel) {
                return _this6.fail('订单不能取消');
            }
            // 设置订单已取消状态
            let updateInfo = {
                order_status: 102
            };
            let orderInfo = yield _this6.model('order').field('order_type').where({
                id: orderId,
                user_id: userId
            }).find();
            //取消订单，还原库存
            const goodsInfo = yield _this6.model('order_goods').where({
                order_id: orderId,
                user_id: userId
            }).select();
            for (const item of goodsInfo) {
                let goods_id = item.goods_id;
                let product_id = item.product_id;
                let number = item.number;
                yield _this6.model('goods').where({
                    id: goods_id
                }).increment('goods_number', number);
                yield _this6.model('product').where({
                    id: product_id
                }).increment('goods_number', number);
            }
            const succesInfo = yield _this6.model('order').where({
                id: orderId
            }).update(updateInfo);
            return _this6.success(succesInfo);
        })();
    }
    /**
     * 删除订单
     * @return {Promise} []
     */
    deleteAction() {
        var _this7 = this;

        return _asyncToGenerator(function* () {
            const orderId = _this7.post('orderId');
            // 检测是否能够取消
            const handleOption = yield _this7.model('order').getOrderHandleOption(orderId);
            if (!handleOption.delete) {
                return _this7.fail('订单不能删除');
            }
            const succesInfo = yield _this7.model('order').orderDeleteById(orderId);
            return _this7.success(succesInfo);
        })();
    }
    /**
     * 确认订单
     * @return {Promise} []
     */
    confirmAction() {
        var _this8 = this;

        return _asyncToGenerator(function* () {
            const orderId = _this8.post('orderId');
            // 检测是否能够取消
            const handleOption = yield _this8.model('order').getOrderHandleOption(orderId);
            if (!handleOption.confirm) {
                return _this8.fail('订单不能确认');
            }
            // 设置订单已完成状态
            const currentTime = parseInt(new Date().getTime() / 1000);
            let updateInfo = {
                order_status: 401,
                confirm_time: currentTime
            };
            const succesInfo = yield _this8.model('order').where({
                id: orderId
            }).update(updateInfo);

            // 确认收货后，处理推广佣金奖励
            yield _this8.handlePromotionCommissionRewards(orderId);

            return _this8.success(succesInfo);
        })();
    }

    /**
     * 订单退款处理
     * @return {Promise} []
     */
    refundAction() {
        var _this9 = this;

        return _asyncToGenerator(function* () {
            const orderId = _this9.post('orderId');
            const refundReason = _this9.post('refundReason') || '用户申请退款';

            try {
                console.log('=== 处理订单退款 ===');
                console.log('订单ID:', orderId, '退款原因:', refundReason);

                // 1. 检查订单状态
                const order = yield _this9.model('order').where({ id: orderId }).find();
                if (think.isEmpty(order)) {
                    return _this9.fail('订单不存在');
                }

                if (order.order_status === 203) {
                    return _this9.fail('订单已经退款');
                }

                // 2. 更新订单状态为已退款
                const currentTime = parseInt(new Date().getTime() / 1000);
                yield _this9.model('order').where({ id: orderId }).update({
                    order_status: 203, // 已退款
                    refund_time: currentTime
                });

                // 3. 处理佣金退款
                const commissionService = _this9.service('commission');
                const refundResult = yield commissionService.handleRefundCommission(orderId);

                if (refundResult.success) {
                    console.log('✅ 订单退款处理完成');
                    return _this9.success({
                        message: '退款处理成功',
                        refundResult: refundResult
                    });
                } else {
                    console.log('❌ 佣金退款处理失败:', refundResult.message);
                    return _this9.fail('佣金退款处理失败: ' + refundResult.message);
                }
            } catch (error) {
                console.error('订单退款处理失败:', error);
                return _this9.fail('退款处理失败: ' + error.message);
            }
        })();
    }

    /**
     * 申请售后
     * @return {Promise} []
     */
    refundApplyAction() {
        var _this10 = this;

        return _asyncToGenerator(function* () {
            const userId = yield _this10.getLoginUserId();
            const orderId = _this10.post('orderId');
            const refundType = _this10.post('refundType') || 'refund_only'; // refund_only: 仅退款, return_refund: 退货退款
            const refundAmount = parseFloat(_this10.post('refundAmount')) || 0;
            const refundReason = _this10.post('refundReason') || '';
            const refundDesc = _this10.post('refundDesc') || '';
            const images = _this10.post('images') || [];

            try {
                console.log('=== 用户申请售后 ===');
                console.log('用户ID:', userId, '订单ID:', orderId);
                console.log('退款类型:', refundType, '退款金额:', refundAmount);

                // 0. 验证用户登录
                if (!userId) {
                    return _this10.fail('请先登录');
                }

                // 1. 验证订单
                const order = yield _this10.model('order').where({
                    id: orderId,
                    user_id: userId
                }).find();

                if (think.isEmpty(order)) {
                    return _this10.fail('订单不存在或无权限访问');
                }

                // 2. 检查订单状态是否可以申请售后
                const handleOption = yield _this10.model('order').getOrderHandleOption(orderId);
                if (!handleOption.apply_refund) {
                    return _this10.fail('当前订单状态不支持申请售后');
                }

                // 3. 验证退款金额
                if (refundAmount <= 0 || refundAmount > parseFloat(order.actual_price)) {
                    return _this10.fail('退款金额不正确');
                }

                // 4. 检查是否已有售后申请（同一订单不允许重复申请）
                const existingApply = yield _this10.model('refund_apply').where({
                    order_id: orderId
                }).find();

                if (!think.isEmpty(existingApply)) {
                    // 根据不同状态返回不同的错误信息
                    switch (existingApply.status) {
                        case 'pending':
                            return _this10.fail('该订单已有售后申请待处理，请耐心等待');
                        case 'processing':
                            return _this10.fail('该订单售后申请正在处理中，请耐心等待');
                        case 'approved':
                            return _this10.fail('该订单售后申请已通过，无需重复申请');
                        case 'rejected':
                            return _this10.fail('该订单售后申请已被拒绝，如有疑问请联系客服');
                        case 'completed':
                            return _this10.fail('该订单售后已完成，无需重复申请');
                        default:
                            return _this10.fail('该订单已有售后申请记录，无法重复申请');
                    }
                }

                // 5. 创建售后申请记录
                const currentTime = parseInt(new Date().getTime() / 1000);
                const applyData = {
                    order_id: orderId,
                    user_id: userId,
                    order_sn: order.order_sn,
                    refund_type: refundType,
                    refund_amount: refundAmount,
                    refund_reason: refundReason,
                    refund_desc: refundDesc,
                    images: JSON.stringify(images),
                    status: 'pending', // pending: 待处理, processing: 处理中, approved: 已同意, rejected: 已拒绝, completed: 已完成
                    apply_time: currentTime,
                    created_at: new Date(),
                    updated_at: new Date()
                };

                const applyId = yield _this10.model('refund_apply').add(applyData);

                console.log('✅ 售后申请提交成功，申请ID:', applyId);
                return _this10.success({
                    message: '售后申请提交成功，我们会尽快处理',
                    applyId: applyId
                });
            } catch (error) {
                console.error('售后申请失败:', error);
                return _this10.fail('售后申请失败: ' + error.message);
            }
        })();
    }

    /**
     * 获取售后详情
     * @return {Promise} []
     */
    refundDetailAction() {
        var _this11 = this;

        return _asyncToGenerator(function* () {
            const userId = yield _this11.getLoginUserId();
            const applyId = _this11.get('applyId');

            try {
                console.log('=== 获取售后详情 ===');
                console.log('用户ID:', userId, '申请ID:', applyId);

                if (!userId) {
                    return _this11.fail('请先登录');
                }

                // 获取售后申请详情
                const refundApply = yield _this11.model('refund_apply').where({
                    id: applyId,
                    user_id: userId
                }).find();

                if (think.isEmpty(refundApply)) {
                    return _this11.fail('售后申请不存在或无权限访问');
                }

                // 获取订单信息
                const orderInfo = yield _this11.model('order').where({
                    id: refundApply.order_id
                }).find();

                // 获取订单商品
                const orderGoods = yield _this11.model('order_goods').where({
                    order_id: refundApply.order_id
                }).select();

                // 处理图片字段
                if (refundApply.images) {
                    try {
                        refundApply.images = JSON.parse(refundApply.images);
                    } catch (e) {
                        refundApply.images = [];
                    }
                } else {
                    refundApply.images = [];
                }

                // 格式化申请时间
                refundApply.apply_time_text = new Date(refundApply.apply_time * 1000).toLocaleString();

                console.log('✅ 售后详情获取成功');
                return _this11.success({
                    refundInfo: refundApply,
                    orderInfo: orderInfo,
                    orderGoods: orderGoods
                });
            } catch (error) {
                console.error('获取售后详情失败:', error);
                return _this11.fail('获取售后详情失败: ' + error.message);
            }
        })();
    }

    /**
     * 撤销售后申请
     * @return {Promise} []
     */
    refundCancelAction() {
        var _this12 = this;

        return _asyncToGenerator(function* () {
            const userId = yield _this12.getLoginUserId();
            const applyId = _this12.post('applyId');

            try {
                console.log('=== 撤销售后申请 ===');
                console.log('用户ID:', userId, '申请ID:', applyId);

                if (!userId) {
                    return _this12.fail('请先登录');
                }

                // 检查售后申请
                const refundApply = yield _this12.model('refund_apply').where({
                    id: applyId,
                    user_id: userId
                }).find();

                if (think.isEmpty(refundApply)) {
                    return _this12.fail('售后申请不存在或无权限访问');
                }

                // 只有待处理状态可以撤销
                if (refundApply.status !== 'pending') {
                    return _this12.fail('当前状态不支持撤销操作');
                }

                // 删除售后申请记录
                yield _this12.model('refund_apply').where({
                    id: applyId
                }).delete();

                console.log('✅ 售后申请撤销成功');
                return _this12.success({
                    message: '售后申请已撤销'
                });
            } catch (error) {
                console.error('撤销售后申请失败:', error);
                return _this12.fail('撤销售后申请失败: ' + error.message);
            }
        })();
    }

    /**
     * 获取售后状态文本
     */
    getRefundStatusText(status) {
        const statusMap = {
            'pending': '待处理',
            'processing': '处理中',
            'approved': '已同意',
            'rejected': '已拒绝',
            'wait_return': '等待退货',
            'returned': '已退货',
            'completed': '已完成'
        };
        return statusMap[status] || '未知状态';
    }

    /**
     * 获取退款类型文本
     */
    getRefundTypeText(refundType) {
        const typeMap = {
            'refund_only': '仅退款',
            'return_refund': '退货退款'
        };
        return typeMap[refundType] || '售后';
    }

    /**
     * 用户填写退货物流信息
     * @return {Promise} []
     */
    submitReturnLogisticsAction() {
        var _this13 = this;

        return _asyncToGenerator(function* () {
            const userId = yield _this13.getLoginUserId();
            const applyId = _this13.post('applyId');
            const logisticsCompany = _this13.post('logisticsCompany');
            const logisticsNo = _this13.post('logisticsNo');

            try {
                console.log('=== 用户提交退货物流信息 ===');
                console.log('用户ID:', userId, '申请ID:', applyId);

                if (!userId) {
                    return _this13.fail('请先登录');
                }

                if (!logisticsCompany || !logisticsNo) {
                    return _this13.fail('请填写完整的物流信息');
                }

                // 检查售后申请
                const refundApply = yield _this13.model('refund_apply').where({
                    id: applyId,
                    user_id: userId
                }).find();

                if (think.isEmpty(refundApply)) {
                    return _this13.fail('售后申请不存在或无权限访问');
                }

                // 只有等待退货状态可以填写物流信息
                if (refundApply.status !== 'wait_return') {
                    return _this13.fail('当前状态不支持填写物流信息');
                }

                // 更新物流信息
                yield _this13.model('refund_apply').where({
                    id: applyId
                }).update({
                    user_logistics_company: logisticsCompany,
                    user_logistics_no: logisticsNo,
                    user_return_time: parseInt(new Date().getTime() / 1000),
                    status: 'returned',
                    updated_at: new Date()
                });

                console.log('✅ 退货物流信息提交成功');
                return _this13.success({
                    message: '退货物流信息提交成功，请等待商家确认收货'
                });
            } catch (error) {
                console.error('提交退货物流信息失败:', error);
                return _this13.fail('提交退货物流信息失败: ' + error.message);
            }
        })();
    }
    /**
     * 完成评论后的订单
     * @return {Promise} []
     */
    completeAction() {
        var _this14 = this;

        return _asyncToGenerator(function* () {
            const orderId = _this14.get('orderId');
            // 设置订单已完成
            const currentTime = parseInt(new Date().getTime() / 1000);
            let updateInfo = {
                order_status: 401,
                dealdone_time: currentTime
            };
            const succesInfo = yield _this14.model('order').where({
                id: orderId
            }).update(updateInfo);
            return _this14.success(succesInfo);
        })();
    }
    /**
     * 提交订单
     * @returns {Promise.<void>}
     */
    submitAction() {
        var _this15 = this;

        return _asyncToGenerator(function* () {
            // 获取收货地址信息和计算运费
            const userId = yield _this15.getLoginUserId();
            const addressId = _this15.post('addressId');
            const freightPrice = _this15.post('freightPrice');
            const offlinePay = _this15.post('offlinePay');
            const userCouponId = _this15.post('userCouponId'); // 用户选择的优惠券ID
            let postscript = _this15.post('postscript');
            const buffer = Buffer.from(postscript); // 留言
            const checkedAddress = yield _this15.model('address').where({
                id: addressId
            }).find();
            if (think.isEmpty(checkedAddress)) {
                return _this15.fail('请选择收货地址');
            }
            // 获取要购买的商品
            const checkedGoodsList = yield _this15.model('cart').where({
                user_id: userId,
                checked: 1,
                is_delete: 0
            }).select();
            if (think.isEmpty(checkedGoodsList)) {
                return _this15.fail('请选择商品');
            }
            let checkPrice = 0;
            let checkStock = 0;
            for (const item of checkedGoodsList) {
                let product = yield _this15.model('product').where({
                    id: item.product_id
                }).find();
                if (item.number > product.goods_number) {
                    checkStock++;
                }
                if (item.retail_price != item.add_price) {
                    checkPrice++;
                }
            }
            if (checkStock > 0) {
                return _this15.fail(400, '库存不足，请重新下单');
            }
            if (checkPrice > 0) {
                return _this15.fail(400, '价格发生变化，请重新下单');
            }
            // 获取订单使用的红包
            // 如果有用红包，则将红包的数量减少，当减到0时，将该条红包删除
            // 统计商品总价
            let goodsTotalPrice = 0.00;
            for (const cartItem of checkedGoodsList) {
                goodsTotalPrice += cartItem.number * cartItem.retail_price;
            }
            // 处理优惠券
            let discountAmount = 0;
            let selectedUserCoupon = null;

            if (userCouponId) {
                // 验证并使用指定的优惠券
                const couponResult = yield _this15.validateAndUseCoupon(userId, userCouponId, goodsTotalPrice);
                if (couponResult.success) {
                    discountAmount = couponResult.discountAmount;
                    selectedUserCoupon = couponResult.userCoupon;
                } else {
                    return _this15.fail(couponResult.message);
                }
            } else {
                // 自动选择最优优惠券
                const bestCouponResult = yield _this15.autoSelectBestCoupon(userId, goodsTotalPrice);
                if (bestCouponResult.success && bestCouponResult.userCoupon) {
                    discountAmount = bestCouponResult.discountAmount;
                    selectedUserCoupon = bestCouponResult.userCoupon;
                }
            }

            // 订单价格计算
            const orderTotalPrice = goodsTotalPrice + freightPrice; // 订单的总价
            const actualPrice = orderTotalPrice - discountAmount; // 减去优惠券优惠后的实际支付金额
            const currentTime = parseInt(new Date().getTime() / 1000);
            let print_info = '';
            for (const item in checkedGoodsList) {
                let i = Number(item) + 1;
                print_info = print_info + i + '、' + checkedGoodsList[item].goods_aka + '【' + checkedGoodsList[item].number + '】 ';
            }
            let def = yield _this15.model('settings').where({
                id: 1
            }).find();
            let sender_name = def.Name;
            let sender_mobile = def.Tel;
            // let sender_address = '';
            let userInfo = yield _this15.model('user').where({
                id: userId
            }).find();
            // const checkedAddress = await this.model('address').where({id: addressId}).find();
            // 获取推广员信息（从前端传递或本地存储）
            const promoterUserId = _this15.post('shareUserId') || 0;
            let promoterId = null;
            let parentPromoterUserId = null;

            // 如果有推广员，处理推广员关系
            if (promoterUserId > 0) {
                console.log('检测到推广下单，推广员用户ID:', promoterUserId, '下单人ID:', userId);

                // 查找推广员记录
                const promoter = yield _this15.model('personal_promoters').where({
                    user_id: promoterUserId
                }).find();

                if (!think.isEmpty(promoter)) {
                    promoterId = promoter.id;
                    console.log('找到推广员ID:', promoterId);

                    // 建立推广员关系：下单人成为推广员的下级（如果还没有上级的话）
                    yield _this15.establishPromoterRelation(userId, promoterUserId);

                    // 查找当前分享者的上级（用于二级佣金）
                    // 注意：佣金基于当前分享者，与购买者的上级关系无关
                    parentPromoterUserId = promoter.parent_user_id;
                    if (parentPromoterUserId) {
                        console.log('找到当前分享者的上级:', parentPromoterUserId, '将获得二级佣金');
                    }
                    console.log('佣金将发放给当前分享者:', promoterUserId, '和其上级:', parentPromoterUserId);
                } else {
                    console.log('推广员记录不存在，可能需要先创建');
                }
            }

            const orderInfo = {
                order_sn: _this15.model('order').generateOrderNumber(),
                user_id: userId,
                // 收货地址和运费
                consignee: checkedAddress.name,
                mobile: checkedAddress.mobile,
                province: checkedAddress.province_id,
                city: checkedAddress.city_id,
                district: checkedAddress.district_id,
                address: checkedAddress.address,
                order_status: 101, // 订单初始状态为 101
                // 根据城市得到运费，这里需要建立表：所在城市的具体运费
                freight_price: freightPrice,
                postscript: buffer.toString('base64'),
                add_time: currentTime,
                goods_price: goodsTotalPrice,
                order_price: orderTotalPrice,
                actual_price: actualPrice,
                change_price: actualPrice,
                print_info: print_info,
                offline_pay: offlinePay,
                // 优惠券相关字段
                user_coupon_id: selectedUserCoupon ? selectedUserCoupon.id : null,
                discount_amount: discountAmount,
                original_amount: orderTotalPrice
                // 推广相关字段（如果需要在订单表中记录）
                // promoter_id: promoterId,
                // promoter_user_id: promoterUserId > 0 ? promoterUserId : null
            };
            // 开启事务，插入订单信息和订单商品
            const orderId = yield _this15.model('order').add(orderInfo);
            orderInfo.id = orderId;
            if (!orderId) {
                return _this15.fail('订单提交失败');
            }
            // 将商品信息录入数据库
            const orderGoodsData = [];
            for (const goodsItem of checkedGoodsList) {
                orderGoodsData.push({
                    user_id: userId,
                    order_id: orderId,
                    goods_id: goodsItem.goods_id,
                    product_id: goodsItem.product_id,
                    goods_name: goodsItem.goods_name,
                    goods_aka: goodsItem.goods_aka,
                    list_pic_url: goodsItem.list_pic_url,
                    retail_price: goodsItem.retail_price,
                    number: goodsItem.number,
                    goods_specifition_name_value: goodsItem.goods_specifition_name_value,
                    goods_specifition_ids: goodsItem.goods_specifition_ids
                });
            }
            yield _this15.model('order_goods').addMany(orderGoodsData);

            // 如果使用了优惠券，标记为已使用并记录使用日志
            if (selectedUserCoupon) {
                yield _this15.markCouponAsUsed(selectedUserCoupon, orderId, orderTotalPrice, discountAmount, actualPrice);
            }

            // 如果是推广订单，创建推广订单记录（分享有礼佣金等确认收货后发放）
            if (promoterId && promoterUserId) {
                yield _this15.createPromotionOrder(promoterId, promoterUserId, userId, orderId, checkedGoodsList, actualPrice);

                // 记录分享有礼佣金（等确认收货后发放）
                yield _this15.recordShareCommissionRewards(promoterUserId, orderId, checkedGoodsList, parentPromoterUserId);
            }

            yield _this15.model('cart').clearBuyGoods();
            return _this15.success({
                orderInfo: orderInfo,
                discountAmount: discountAmount,
                couponUsed: selectedUserCoupon ? true : false
            });
        })();
    }
    updateAction() {
        var _this16 = this;

        return _asyncToGenerator(function* () {
            const addressId = _this16.post('addressId');
            const orderId = _this16.post('orderId');
            // 备注
            // let postscript = this.post('postscript');
            // const buffer = Buffer.from(postscript);
            const updateAddress = yield _this16.model('address').where({
                id: addressId
            }).find();
            const currentTime = parseInt(new Date().getTime() / 1000);
            const orderInfo = {
                // 收货地址和运费
                consignee: updateAddress.name,
                mobile: updateAddress.mobile,
                province: updateAddress.province_id,
                city: updateAddress.city_id,
                district: updateAddress.district_id,
                address: updateAddress.address
                // TODO 根据地址计算运费
                // freight_price: 0.00,
                // 备注
                // postscript: buffer.toString('base64'),
                // add_time: currentTime
            };
            const updateInfo = yield _this16.model('order').where({
                id: orderId
            }).update(orderInfo);
            return _this16.success(updateInfo);
        })();
    }

    /**
     * 创建推广订单记录
     * @param {number} promoterId 推广员ID
     * @param {number} promoterUserId 推广员用户ID
     * @param {number} buyerUserId 购买者用户ID
     * @param {number} orderId 订单ID
     * @param {array} goodsList 商品列表
     * @param {number} orderAmount 订单金额
     */
    createPromotionOrder(promoterId, promoterUserId, buyerUserId, orderId, goodsList, orderAmount) {
        var _this17 = this;

        return _asyncToGenerator(function* () {
            try {
                console.log('创建推广订单记录:', { promoterId, promoterUserId, buyerUserId, orderId, orderAmount });

                // 获取订单信息
                const orderInfo = yield _this17.model('order').where({ id: orderId }).find();

                // 为每个商品创建推广订单记录
                for (const goods of goodsList) {
                    // 获取佣金配置
                    const commissionConfig = yield _this17.getCommissionConfig(goods.goods_id);
                    const commissionRate = commissionConfig ? commissionConfig.commission_rate : 5.0; // 默认5%
                    const commissionAmount = (goods.goods_price * goods.number * commissionRate / 100).toFixed(2);

                    // 检查是否是首次购买
                    const isFirstOrder = yield _this17.checkFirstOrder(buyerUserId);

                    const promotionOrderData = {
                        promoter_id: promoterId,
                        promoter_user_id: promoterUserId,
                        buyer_user_id: buyerUserId,
                        order_id: orderId,
                        order_sn: orderInfo.order_sn,
                        goods_id: goods.goods_id,
                        goods_name: goods.goods_name,
                        goods_price: goods.goods_price,
                        order_amount: goods.goods_price * goods.number,
                        commission_rate: commissionRate,
                        commission_amount: commissionAmount,
                        share_source: 'other', // 默认为其他来源
                        status: 'pending',
                        is_first_order: isFirstOrder ? 1 : 0,
                        create_time: parseInt(new Date().getTime() / 1000),
                        update_time: parseInt(new Date().getTime() / 1000)
                    };

                    yield _this17.model('promotion_orders').add(promotionOrderData);
                    console.log('推广订单记录创建成功，商品ID:', goods.goods_id, '佣金:', commissionAmount);
                }

                // 更新推广员统计
                yield _this17.updatePromoterOrderStats(promoterId);
            } catch (error) {
                console.error('创建推广订单记录失败:', error);
            }
        })();
    }

    /**
     * 获取佣金配置
     * @param {number} goodsId 商品ID
     */
    getCommissionConfig(goodsId) {
        var _this18 = this;

        return _asyncToGenerator(function* () {
            try {
                // 先查找商品特定配置
                let config = yield _this18.model('commission_config').where({
                    goods_id: goodsId,
                    is_active: 1
                }).find();

                if (think.isEmpty(config)) {
                    // 查找分类配置或全局配置
                    config = yield _this18.model('commission_config').where({
                        goods_id: 0,
                        is_active: 1
                    }).find();
                }

                return config;
            } catch (error) {
                console.error('获取佣金配置失败:', error);
                return null;
            }
        })();
    }

    /**
     * 检查是否首次购买
     * @param {number} userId 用户ID
     */
    checkFirstOrder(userId) {
        var _this19 = this;

        return _asyncToGenerator(function* () {
            try {
                const orderCount = yield _this19.model('order').where({
                    user_id: userId,
                    order_status: ['in', [201, 301, 302, 401]] // 已付款及以上状态
                }).count();

                return orderCount === 0;
            } catch (error) {
                console.error('检查首次购买失败:', error);
                return false;
            }
        })();
    }

    /**
     * 更新推广员订单统计
     * @param {number} promoterId 推广员ID
     */
    updatePromoterOrderStats(promoterId) {
        var _this20 = this;

        return _asyncToGenerator(function* () {
            try {
                const currentTime = parseInt(new Date().getTime() / 1000);

                yield _this20.model('personal_promoters').where({
                    id: promoterId
                }).update({
                    total_orders: ['exp', 'total_orders + 1'],
                    month_orders: ['exp', 'month_orders + 1'],
                    update_time: currentTime
                });

                console.log('推广员订单统计更新成功');
            } catch (error) {
                console.error('更新推广员订单统计失败:', error);
            }
        })();
    }

    /**
     * 处理推广佣金奖励（确认收货时调用）
     * @param {number} orderId 订单ID
     */
    handlePromotionCommissionRewards(orderId) {
        var _this21 = this;

        return _asyncToGenerator(function* () {
            try {
                console.log('=== 开始处理推广佣金奖励 ===');
                console.log('订单ID:', orderId);

                // 查找该订单的推广记录
                const promotionOrders = yield _this21.model('promotion_orders').where({
                    order_id: orderId,
                    status: 'pending' // 只处理待结算的推广订单
                }).select();

                if (think.isEmpty(promotionOrders)) {
                    console.log('该订单无推广记录或已结算，跳过佣金奖励');
                    return;
                }

                console.log(`找到 ${promotionOrders.length} 条推广记录`);

                // 处理每个推广记录
                for (const promotionOrder of promotionOrders) {
                    console.log('处理推广记录:', promotionOrder.id, '推广员:', promotionOrder.promoter_user_id);

                    // 获取商品的分销配置
                    const distributionConfig = yield _this21.model('goods_distribution').where({
                        goods_id: promotionOrder.goods_id
                    }).find();

                    if (think.isEmpty(distributionConfig) || distributionConfig.is_distributed !== 1) {
                        console.log('商品未在分销池中或已移出，跳过佣金发放');
                        continue;
                    }

                    // 计算各级佣金
                    const orderAmount = parseFloat(promotionOrder.order_amount);
                    const personalCommission = (orderAmount * (distributionConfig.personal_rate || 0) / 100).toFixed(2);
                    const level1Commission = (orderAmount * (distributionConfig.level1_rate || 0) / 100).toFixed(2);
                    const level2Commission = (orderAmount * (distributionConfig.level2_rate || 0) / 100).toFixed(2);
                    const teamLeaderCommission = (orderAmount * (distributionConfig.team_leader_rate || 0) / 100).toFixed(2);

                    console.log(`订单金额: ${orderAmount}元，个人佣金: ${personalCommission}元`);

                    // 使用佣金服务发放佣金
                    const commissionService = _this21.service('commission');

                    // 发放当前分享者佣金（一级佣金）
                    if (parseFloat(personalCommission) > 0) {
                        const result = yield commissionService.settleCommission(promotionOrder.promoter_user_id, parseFloat(personalCommission), orderId, promotionOrder.id, `分享商品${promotionOrder.goods_name}获得佣金`);
                        console.log(`✅ 分享者佣金发放成功: 用户${promotionOrder.promoter_user_id} 获得 ${personalCommission}元`);
                    }

                    // 发放分享者的上级佣金（二级佣金）
                    if (promotionOrder.parent_promoter_user_id && parseFloat(level1Commission) > 0) {
                        const result = yield commissionService.settleCommission(promotionOrder.parent_promoter_user_id, parseFloat(level1Commission), orderId, promotionOrder.id, `下级分享商品${promotionOrder.goods_name}获得上级佣金`);
                        console.log(`✅ 分享者上级佣金发放成功: 用户${promotionOrder.parent_promoter_user_id} 获得 ${level1Commission}元`);
                    }

                    // 更新推广订单状态为已结算
                    yield _this21.model('promotion_orders').where({
                        id: promotionOrder.id
                    }).update({
                        status: 'settled',
                        settle_time: parseInt(new Date().getTime() / 1000),
                        update_time: parseInt(new Date().getTime() / 1000),
                        personal_commission: personalCommission,
                        level1_commission: level1Commission,
                        level2_commission: level2Commission,
                        team_leader_commission: teamLeaderCommission
                    });

                    console.log(`✅ 推广员 ${promotionOrder.promoter_user_id} 佣金发放成功: ${personalCommission}元`);
                }

                console.log('=== 推广佣金奖励处理完成 ===');
            } catch (error) {
                console.error('处理推广佣金奖励失败:', error);
                // 不抛出错误，避免影响确认收货流程
            }
        })();
    }

    /**
     * 建立推广员关系：下单人成为推广员的下级（仅在没有上级时）
     * 注意：关系建立与佣金分配是分开的，佣金始终给当前分享者
     * @param {number} buyerUserId 购买者用户ID
     * @param {number} promoterUserId 推广员用户ID
     */
    establishPromoterRelation(buyerUserId, promoterUserId) {
        var _this22 = this;

        return _asyncToGenerator(function* () {
            try {
                console.log('=== 检查并建立推广员关系 ===');
                console.log('购买者ID:', buyerUserId, '当前分享者ID:', promoterUserId);

                // 检查购买者是否已有推广员关系
                const existingPromoter = yield _this22.model('personal_promoters').where({
                    user_id: buyerUserId
                }).find();

                if (!think.isEmpty(existingPromoter)) {
                    if (existingPromoter.parent_user_id) {
                        console.log('购买者已有上级推广员:', existingPromoter.parent_user_id, '保持现有关系');
                        console.log('注意：佣金仍然给当前分享者:', promoterUserId, '与购买者的上级关系无关');
                        return;
                    } else {
                        // 已是推广员但没有上级，建立关系
                        yield _this22.model('personal_promoters').where({
                            user_id: buyerUserId
                        }).update({
                            parent_user_id: promoterUserId,
                            update_time: parseInt(new Date().getTime() / 1000)
                        });
                        console.log('✅ 为现有推广员建立上级关系');
                    }
                } else {
                    // 购买者不是推广员，创建推广员记录并建立关系
                    const currentTime = parseInt(new Date().getTime() / 1000);
                    const promoterData = {
                        user_id: buyerUserId,
                        parent_user_id: promoterUserId,
                        level: 1,
                        total_views: 0,
                        total_orders: 0,
                        total_commission: 0.00,
                        month_views: 0,
                        month_orders: 0,
                        month_commission: 0.00,
                        status: 1,
                        first_share_time: currentTime,
                        create_time: currentTime,
                        update_time: currentTime
                    };

                    yield _this22.model('personal_promoters').add(promoterData);
                    console.log('✅ 创建推广员记录并建立上级关系');
                }
            } catch (error) {
                console.error('建立推广员关系失败:', error);
            }
        })();
    }

    /**
     * 记录分享有礼佣金（等确认收货后发放）
     * @param {number} promoterUserId 推广员用户ID
     * @param {number} orderId 订单ID
     * @param {array} goodsList 商品列表
     * @param {number} parentPromoterUserId 上级推广员用户ID
     */
    recordShareCommissionRewards(promoterUserId, orderId, goodsList, parentPromoterUserId = null) {
        var _this23 = this;

        return _asyncToGenerator(function* () {
            try {
                console.log('=== 开始记录分享有礼佣金 ===');
                console.log('推广员用户ID:', promoterUserId, '订单ID:', orderId);

                const commissionService = _this23.service('points');

                // 处理每个商品的分享佣金记录
                for (const goods of goodsList) {
                    const orderAmount = goods.goods_price * goods.number;

                    console.log(`记录商品: ${goods.goods_name}, 金额: ${orderAmount}元`);

                    // 使用支持上级推广员的佣金记录方法
                    const result = yield commissionService.addShareCommissionWithParent(promoterUserId, orderId, goods.goods_id, orderAmount, parentPromoterUserId, 'miniprogram');

                    if (result.success) {
                        console.log(`✅ 商品 ${goods.goods_name} 分享佣金记录成功: ${result.commission}元`);
                    } else {
                        console.log(`❌ 商品 ${goods.goods_name} 分享佣金记录失败: ${result.message}`);
                    }
                }

                console.log('=== 分享有礼佣金记录完成 ===');
            } catch (error) {
                console.error('记录分享有礼佣金失败:', error);
                // 不抛出错误，避免影响下单流程
            }
        })();
    }
    /**
     * 查询物流信息asd
     * @returns {Promise.<void>}
     */
    expressAction() {
        var _this24 = this;

        return _asyncToGenerator(function* () {
            const currentTime = parseInt(new Date().getTime() / 1000);
            const orderId = _this24.get('orderId');
            let info = yield _this24.model('order_express').where({
                order_id: orderId
            }).find();
            if (think.isEmpty(info)) {
                return _this24.fail(400, '暂无物流信息');
            }
            const expressInfo = yield _this24.model('order_express').where({
                order_id: orderId
            }).find();
            // 如果is_finish == 1；或者 updateTime 小于 1分钟，
            let updateTime = info.update_time;
            let com = (currentTime - updateTime) / 60;
            let is_finish = info.is_finish;
            if (is_finish == 1) {
                return _this24.success(expressInfo);
            } else if (updateTime != 0 && com < 20) {
                return _this24.success(expressInfo);
            } else {
                let shipperCode = expressInfo.shipper_code;
                let expressNo = expressInfo.logistic_code;
                let lastExpressInfo = yield _this24.getExpressInfo(shipperCode, expressNo);
                let deliverystatus = lastExpressInfo.deliverystatus;
                let newUpdateTime = lastExpressInfo.updateTime;
                newUpdateTime = parseInt(new Date(newUpdateTime).getTime() / 1000);
                deliverystatus = yield _this24.getDeliverystatus(deliverystatus);
                let issign = lastExpressInfo.issign;
                let traces = lastExpressInfo.list;
                traces = JSON.stringify(traces);
                let dataInfo = {
                    express_status: deliverystatus,
                    is_finish: issign,
                    traces: traces,
                    update_time: newUpdateTime
                };
                yield _this24.model('order_express').where({
                    order_id: orderId
                }).update(dataInfo);
                let express = yield _this24.model('order_express').where({
                    order_id: orderId
                }).find();
                return _this24.success(express);
            }
            // return this.success(latestExpressInfo);
        })();
    }
    getExpressInfo(shipperCode, expressNo) {
        return _asyncToGenerator(function* () {
            let appCode = "APPCODE " + think.config('aliexpress.appcode');
            const options = {
                method: 'GET',
                url: 'http://wuliu.market.alicloudapi.com/kdi?no=' + expressNo + '&type=' + shipperCode,
                headers: {
                    "Content-Type": "application/json; charset=utf-8",
                    "Authorization": appCode
                }
            };
            let sessionData = yield rp(options);
            sessionData = JSON.parse(sessionData);
            return sessionData.result;
        })();
    }
    getDeliverystatus(status) {
        return _asyncToGenerator(function* () {
            if (status == 0) {
                return '快递收件(揽件)';
            } else if (status == 1) {
                return '在途中';
            } else if (status == 2) {
                return '正在派件';
            } else if (status == 3) {
                return '已签收';
            } else if (status == 4) {
                return '派送失败(无法联系到收件人或客户要求择日派送，地址不详或手机号不清)';
            } else if (status == 5) {
                return '疑难件(收件人拒绝签收，地址有误或不能送达派送区域，收费等原因无法正常派送)';
            } else if (status == 6) {
                return '退件签收';
            }
        })();
    }

    /**
     * 验证并使用优惠券
     */
    validateAndUseCoupon(userId, userCouponId, orderAmount) {
        var _this25 = this;

        return _asyncToGenerator(function* () {
            try {
                const userCoupon = yield _this25.model('user_coupons').alias('uc').join('coupons c ON uc.coupon_id = c.id').where({
                    'uc.id': userCouponId,
                    'uc.user_id': userId,
                    'uc.status': 'unused',
                    'uc.expire_at': ['>=', new Date()],
                    'c.is_delete': 0
                }).field('uc.*, c.*').find();

                if (think.isEmpty(userCoupon)) {
                    return { success: false, message: '优惠券不可用' };
                }

                // 验证使用条件
                if (orderAmount < userCoupon.min_amount) {
                    return { success: false, message: `订单金额需满${userCoupon.min_amount}元` };
                }

                // 计算优惠金额
                const discountAmount = _this25.calculateDiscount(userCoupon, orderAmount);

                return {
                    success: true,
                    userCoupon: userCoupon,
                    discountAmount: discountAmount
                };
            } catch (error) {
                think.logger.error('验证优惠券失败:', error);
                return { success: false, message: '优惠券验证失败' };
            }
        })();
    }

    /**
     * 自动选择最优优惠券
     */
    autoSelectBestCoupon(userId, orderAmount) {
        var _this26 = this;

        return _asyncToGenerator(function* () {
            try {
                const availableCoupons = yield _this26.model('user_coupons').alias('uc').join('coupons c ON uc.coupon_id = c.id').where({
                    'uc.user_id': userId,
                    'uc.status': 'unused',
                    'uc.expire_at': ['>=', new Date()],
                    'c.min_amount': ['<=', orderAmount],
                    'c.is_delete': 0
                }).field('uc.*, c.*').select();

                if (availableCoupons.length === 0) {
                    return { success: true, userCoupon: null, discountAmount: 0 };
                }

                let bestCoupon = null;
                let maxDiscount = 0;

                for (let coupon of availableCoupons) {
                    const discount = _this26.calculateDiscount(coupon, orderAmount);
                    if (discount > maxDiscount) {
                        maxDiscount = discount;
                        bestCoupon = coupon;
                    }
                }

                return {
                    success: true,
                    userCoupon: bestCoupon,
                    discountAmount: maxDiscount
                };
            } catch (error) {
                think.logger.error('自动选择优惠券失败:', error);
                return { success: false, message: '自动选择优惠券失败' };
            }
        })();
    }

    /**
     * 计算优惠金额
     */
    calculateDiscount(coupon, amount) {
        if (coupon.discount_type === 'fixed') {
            return Math.min(coupon.discount_value, amount);
        } else {
            const discount = amount * (coupon.discount_value / 100);
            return Math.min(discount, coupon.max_discount || discount);
        }
    }

    /**
     * 标记优惠券为已使用并记录日志
     */
    markCouponAsUsed(userCoupon, orderId, originalAmount, discountAmount, finalAmount) {
        var _this27 = this;

        return _asyncToGenerator(function* () {
            try {
                // 更新用户优惠券状态
                yield _this27.model('user_coupons').where({ id: userCoupon.id }).update({
                    status: 'used',
                    used_at: new Date(),
                    order_id: orderId
                });

                // 记录使用日志
                yield _this27.model('coupon_usage_logs').add({
                    user_id: userCoupon.user_id,
                    coupon_id: userCoupon.coupon_id,
                    user_coupon_id: userCoupon.id,
                    order_id: orderId,
                    original_amount: originalAmount,
                    discount_amount: discountAmount,
                    final_amount: finalAmount
                });

                console.log('优惠券使用成功:', userCoupon.coupon_code, '优惠金额:', discountAmount);
            } catch (error) {
                think.logger.error('标记优惠券使用失败:', error);
                throw error;
            }
        })();
    }
};