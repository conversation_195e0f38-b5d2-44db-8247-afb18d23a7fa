# 微信支付API证书说明

## 📁 证书文件放置位置

请将从微信商户平台下载的API证书文件放置在此目录下：

```
cert/
├── apiclient_cert.pem    # API证书文件
├── apiclient_key.pem     # API私钥文件
└── README.md             # 本说明文件
```

## 🔐 如何获取API证书

### 1. 登录微信商户平台
访问：https://pay.weixin.qq.com

### 2. 进入API安全设置
```
账户中心 > API安全 > API证书
```

### 3. 下载证书
- 点击"下载证书"
- 下载后会得到一个压缩包
- 解压后包含以下文件：
  - `apiclient_cert.pem` - API证书
  - `apiclient_key.pem` - API私钥
  - `apiclient_cert.p12` - PKCS12格式证书

### 4. 放置证书文件
将 `apiclient_cert.pem` 和 `apiclient_key.pem` 复制到此目录

## ⚠️ 重要提醒

1. **证书安全**：
   - 证书文件包含敏感信息，请妥善保管
   - 不要将证书文件提交到版本控制系统
   - 建议设置适当的文件权限

2. **证书用途**：
   - 微信退款接口需要双向证书验证
   - 没有证书文件，退款功能将无法正常工作

3. **证书有效期**：
   - API证书有有效期限制
   - 过期前需要重新下载更新

## 🧪 验证证书配置

运行以下命令检查证书配置：
```bash
node 检查微信退款权限.js
```

## 📞 获取帮助

如果在获取或配置证书时遇到问题：
- 微信支付客服：95017
- 微信商户平台帮助中心
- 微信支付开发文档：https://pay.weixin.qq.com/wiki/doc/api/
