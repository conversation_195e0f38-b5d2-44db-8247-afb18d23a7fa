-- 初始化分享记录相关表
-- 请在MySQL中执行此脚本

USE hiolabs;

-- 检查并创建分享记录表
CREATE TABLE IF NOT EXISTS `hiolabs_share_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sharer_user_id` int(11) NOT NULL COMMENT '分享人用户ID',
  `visitor_user_id` int(11) DEFAULT NULL COMMENT '访问者用户ID（未登录时为NULL）',
  `goods_id` int(11) NOT NULL COMMENT '分享的商品ID',
  `share_type` enum('friend','qrcode','other') NOT NULL DEFAULT 'friend' COMMENT '分享类型：friend好友分享，qrcode二维码分享，other其他',
  `visit_time` int(11) NOT NULL COMMENT '访问时间戳',
  `order_id` int(11) DEFAULT NULL COMMENT '产生的订单ID（如果有下单）',
  `order_amount` decimal(10,2) DEFAULT 0.00 COMMENT '订单金额',
  `commission_amount` decimal(10,2) DEFAULT 0.00 COMMENT '佣金金额',
  `commission_rate` decimal(5,2) DEFAULT 0.00 COMMENT '佣金比例',
  `status` enum('visit','order','paid','settled') NOT NULL DEFAULT 'visit' COMMENT '状态：visit访问，order下单，paid支付，settled结算',
  `ip_address` varchar(45) DEFAULT NULL COMMENT '访问IP地址',
  `user_agent` text COMMENT '用户代理信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sharer_user_id` (`sharer_user_id`),
  KEY `idx_visitor_user_id` (`visitor_user_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_share_type` (`share_type`),
  KEY `idx_status` (`status`),
  KEY `idx_visit_time` (`visit_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分享记录表';

-- 检查并创建分享统计表
CREATE TABLE IF NOT EXISTS `hiolabs_share_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '分享人用户ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `total_shares` int(11) DEFAULT 0 COMMENT '总分享次数',
  `total_visits` int(11) DEFAULT 0 COMMENT '总访问次数',
  `total_orders` int(11) DEFAULT 0 COMMENT '总订单数',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '总订单金额',
  `total_commission` decimal(10,2) DEFAULT 0.00 COMMENT '总佣金',
  `friend_shares` int(11) DEFAULT 0 COMMENT '好友分享次数',
  `moments_shares` int(11) DEFAULT 0 COMMENT '朋友圈分享次数',
  `qrcode_shares` int(11) DEFAULT 0 COMMENT '二维码分享次数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_date` (`user_id`, `stat_date`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分享统计表';

-- 检查订单表是否已有分享相关字段，如果没有则添加
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE TABLE_SCHEMA = 'hiolabs' 
   AND TABLE_NAME = 'hiolabs_order' 
   AND COLUMN_NAME = 'share_record_id') = 0,
  'ALTER TABLE hiolabs_order ADD COLUMN share_record_id int(11) DEFAULT NULL COMMENT "关联的分享记录ID"',
  'SELECT "share_record_id column already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE TABLE_SCHEMA = 'hiolabs' 
   AND TABLE_NAME = 'hiolabs_order' 
   AND COLUMN_NAME = 'sharer_user_id') = 0,
  'ALTER TABLE hiolabs_order ADD COLUMN sharer_user_id int(11) DEFAULT NULL COMMENT "分享人用户ID"',
  'SELECT "sharer_user_id column already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引（如果不存在）
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
   WHERE TABLE_SCHEMA = 'hiolabs' 
   AND TABLE_NAME = 'hiolabs_order' 
   AND INDEX_NAME = 'idx_share_record_id') = 0,
  'ALTER TABLE hiolabs_order ADD KEY idx_share_record_id (share_record_id)',
  'SELECT "idx_share_record_id index already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
   WHERE TABLE_SCHEMA = 'hiolabs' 
   AND TABLE_NAME = 'hiolabs_order' 
   AND INDEX_NAME = 'idx_sharer_user_id') = 0,
  'ALTER TABLE hiolabs_order ADD KEY idx_sharer_user_id (sharer_user_id)',
  'SELECT "idx_sharer_user_id index already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 插入一些测试数据（可选）
-- INSERT INTO hiolabs_share_records (sharer_user_id, visitor_user_id, goods_id, share_type, visit_time, status, ip_address) 
-- VALUES (1, 2, 1, 'friend', UNIX_TIMESTAMP(), 'visit', '127.0.0.1');

SELECT '✅ 分享记录表初始化完成' as result;
