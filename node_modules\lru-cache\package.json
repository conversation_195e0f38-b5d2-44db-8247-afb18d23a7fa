{"_from": "lru-cache@^7.14.1", "_id": "lru-cache@7.18.3", "_inBundle": false, "_integrity": "sha512-jumlc0BIUrS3qJGgIkWZsyfAM7NCWiBcCDhnd+3NNM5KbBmLTgHVfWBcg6W+rLUsIpzpERPsvwUP7CckAQSOoA==", "_location": "/lru-cache", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "lru-cache@^7.14.1", "name": "lru-cache", "escapedName": "lru-cache", "rawSpec": "^7.14.1", "saveSpec": null, "fetchSpec": "^7.14.1"}, "_requiredBy": ["/named-placeholders"], "_resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.3.tgz", "_shasum": "f793896e0fd0e954a59dfdd82f0773808df6aa89", "_spec": "lru-cache@^7.14.1", "_where": "D:\\py-ide\\hioshop-miniprogram-master\\node_modules\\named-placeholders", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A cache object that deletes the least-recently-used items.", "devDependencies": {"@size-limit/preset-small-lib": "^7.0.8", "@types/node": "^17.0.31", "@types/tap": "^15.0.6", "benchmark": "^2.1.4", "c8": "^7.11.2", "clock-mock": "^1.0.6", "eslint-config-prettier": "^8.5.0", "prettier": "^2.6.2", "size-limit": "^7.0.8", "tap": "^16.3.4", "ts-node": "^10.7.0", "tslib": "^2.4.0", "typedoc": "^0.23.24", "typescript": "^4.6.4"}, "engines": {"node": ">=12"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "files": ["index.js", "index.mjs", "index.d.ts"], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "keywords": ["mru", "lru", "cache"], "license": "ISC", "main": "./index.js", "module": "./index.mjs", "name": "lru-cache", "prettier": {"semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-lru-cache.git"}, "scripts": {"build": "npm run prepare", "format": "prettier --write .", "postversion": "npm publish", "prepare": "node ./scripts/transpile-to-esm.js", "prepublishOnly": "git push origin --follow-tags", "presnap": "npm run prepare", "pretest": "npm run prepare", "preversion": "npm test", "size": "size-limit", "snap": "tap", "test": "tap", "typedoc": "typedoc ./index.d.ts"}, "sideEffects": false, "size-limit": [{"path": "./index.js"}], "tap": {"nyc-arg": ["--include=index.js"], "node-arg": ["--expose-gc", "--require", "ts-node/register"], "ts": false}, "type": "commonjs", "types": "./index.d.ts", "version": "7.18.3"}