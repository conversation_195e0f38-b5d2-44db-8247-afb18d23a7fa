function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

module.exports = class extends think.Model {

  /**
   * 获取活动的基本信息
   */
  getCampaignInfo(campaignId) {
    var _this = this;

    return _asyncToGenerator(function* () {
      const campaign = yield _this.where({ id: campaignId }).find();
      if (think.isEmpty(campaign)) {
        return null;
      }

      // 获取商品信息
      const goodsModel = _this.model('goods');
      const goods = yield goodsModel.where({ id: campaign.goods_id }).find();

      return Object.assign({}, campaign, {
        goods: goods
      });
    })();
  }

  /**
   * 检查活动是否可以参与
   */
  canParticipate(campaignId) {
    var _this2 = this;

    return _asyncToGenerator(function* () {
      const campaign = yield _this2.where({ id: campaignId }).find();
      if (think.isEmpty(campaign)) {
        return { canParticipate: false, reason: '活动不存在' };
      }

      if (campaign.status !== 'active') {
        return { canParticipate: false, reason: '活动未开始或已结束' };
      }

      const now = new Date();
      const startDate = new Date(campaign.start_date);
      const endDate = new Date(campaign.end_date + ' 23:59:59');

      if (now < startDate || now > endDate) {
        return { canParticipate: false, reason: '不在活动时间范围内' };
      }

      return { canParticipate: true };
    })();
  }

  /**
   * 获取用户在活动中的参与记录
   */
  getUserRecord(campaignId, userId) {
    var _this3 = this;

    return _asyncToGenerator(function* () {
      const recordModel = _this3.model('flash_sale_user_records');
      return yield recordModel.where({
        campaign_id: campaignId,
        user_id: userId
      }).find();
    })();
  }

  /**
   * 更新用户参与记录
   */
  updateUserRecord(campaignId, userId, quantity) {
    var _this4 = this;

    return _asyncToGenerator(function* () {
      const recordModel = _this4.model('flash_sale_user_records');

      const existing = yield recordModel.where({
        campaign_id: campaignId,
        user_id: userId
      }).find();

      if (think.isEmpty(existing)) {
        // 创建新记录
        return yield recordModel.add({
          campaign_id: campaignId,
          user_id: userId,
          total_purchased: quantity,
          last_purchase_time: new Date()
        });
      } else {
        // 更新现有记录
        return yield recordModel.where({
          campaign_id: campaignId,
          user_id: userId
        }).update({
          total_purchased: existing.total_purchased + quantity,
          last_purchase_time: new Date()
        });
      }
    })();
  }
};