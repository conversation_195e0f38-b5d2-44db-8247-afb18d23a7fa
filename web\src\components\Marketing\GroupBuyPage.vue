<template>
  <div class="group-buy-page">
    <!-- 页面头部 -->
    <div class="page-header bg-white shadow-sm border-b">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">团购活动</h1>
            <p class="text-sm text-gray-500 mt-1">创建和管理团购活动，通过社交分享提升销量</p>
          </div>
          <div class="flex items-center space-x-3">
            <button @click="showAddModal = true" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <i class="ri-add-line mr-2"></i>
              创建团购
            </button>
            <button @click="showGroupModal = true" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
              <i class="ri-group-line mr-2"></i>
              团购记录
            </button>
            <button @click="refreshData" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <i class="ri-refresh-line mr-2"></i>
              刷新数据
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="p-6 max-w-7xl mx-auto">
      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <i class="ri-group-line text-xl text-orange-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">团购活动总数</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.totalGroupBuys }}</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="ri-play-circle-line text-xl text-green-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">进行中</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.activeGroupBuys }}</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="ri-team-line text-xl text-blue-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">成功团购</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.successfulGroups }}</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <i class="ri-money-dollar-circle-line text-xl text-purple-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">团购销售额</p>
              <p class="text-2xl font-bold text-gray-900">¥{{ statistics.groupBuySales }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 筛选和搜索 -->
      <div class="bg-white rounded-lg shadow-sm border mb-6">
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">搜索活动</label>
              <input
                v-model="searchQuery"
                type="text"
                placeholder="输入活动名称或商品名称"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">活动状态</label>
              <select
                v-model="filterStatus"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">全部状态</option>
                <option value="active">进行中</option>
                <option value="upcoming">即将开始</option>
                <option value="ended">已结束</option>
                <option value="disabled">已停用</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">团购类型</label>
              <select
                v-model="filterType"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">全部类型</option>
                <option value="normal">普通团购</option>
                <option value="ladder">阶梯团购</option>
                <option value="spike">秒杀团购</option>
              </select>
            </div>

            <div class="flex items-end">
              <button @click="resetFilters" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                <i class="ri-refresh-line mr-2"></i>
                重置
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 团购活动列表 -->
      <div class="bg-white rounded-lg shadow-sm border">
        <div class="px-6 py-4 border-b">
          <h2 class="text-lg font-semibold text-gray-900">团购活动列表</h2>
        </div>

        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div v-for="activity in filteredActivities" :key="activity.id" class="bg-white border rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
              <!-- 商品图片 -->
              <div class="relative">
                <img :src="activity.productImage" :alt="activity.productName" class="w-full h-48 object-cover">
                <div class="absolute top-2 left-2">
                  <span :class="[
                    'px-2 py-1 text-xs rounded-full',
                    activity.status === 'active' ? 'bg-green-100 text-green-800' :
                    activity.status === 'upcoming' ? 'bg-yellow-100 text-yellow-800' :
                    activity.status === 'ended' ? 'bg-gray-100 text-gray-800' :
                    'bg-red-100 text-red-800'
                  ]">
                    {{ getStatusLabel(activity.status) }}
                  </span>
                </div>
                <div class="absolute top-2 right-2">
                  <span :class="[
                    'px-2 py-1 text-xs rounded-full',
                    activity.type === 'normal' ? 'bg-blue-100 text-blue-800' :
                    activity.type === 'ladder' ? 'bg-purple-100 text-purple-800' :
                    'bg-red-100 text-red-800'
                  ]">
                    {{ getTypeLabel(activity.type) }}
                  </span>
                </div>
                <div v-if="activity.status === 'active'" class="absolute bottom-2 left-2">
                  <div class="bg-orange-600 text-white px-2 py-1 rounded text-xs">
                    <i class="ri-time-line mr-1"></i>
                    {{ activity.remainingTime }}
                  </div>
                </div>
              </div>

              <!-- 活动信息 -->
              <div class="p-4">
                <h3 class="font-medium text-gray-900 mb-2 line-clamp-2">{{ activity.name }}</h3>
                <p class="text-sm text-gray-600 mb-3 line-clamp-1">{{ activity.productName }}</p>
                
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-500">单买价:</span>
                    <span class="text-gray-400 line-through">¥{{ activity.originalPrice }}</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-500">团购价:</span>
                    <span class="font-medium text-orange-600 text-lg">¥{{ activity.groupPrice }}</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-500">成团人数:</span>
                    <span class="font-medium">{{ activity.minPeople }}人</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-500">已成团:</span>
                    <span class="font-medium text-green-600">{{ activity.successGroups }}个</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-500">参团人数:</span>
                    <span class="font-medium text-blue-600">{{ activity.participants }}人</span>
                  </div>
                </div>

                <!-- 进度条 -->
                <div class="mt-3">
                  <div class="flex justify-between text-xs text-gray-500 mb-1">
                    <span>参团进度</span>
                    <span>{{ Math.round((activity.participants / activity.targetParticipants) * 100) }}%</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      class="bg-orange-600 h-2 rounded-full transition-all duration-300"
                      :style="{ width: Math.min(Math.round((activity.participants / activity.targetParticipants) * 100), 100) + '%' }"
                    ></div>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="mt-4 space-y-2">
                  <div class="flex space-x-2">
                    <button @click="viewActivity(activity)" class="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                      详情
                    </button>
                    <button @click="editActivity(activity)" class="flex-1 px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700">
                      编辑
                    </button>
                  </div>
                  <button @click="toggleActivityStatus(activity)" :class="[
                    'w-full px-3 py-2 text-sm rounded transition-colors',
                    activity.status === 'active'
                      ? 'bg-red-600 text-white hover:bg-red-700'
                      : 'bg-green-600 text-white hover:bg-green-700'
                  ]">
                    {{ activity.status === 'active' ? '停止团购' : '开始团购' }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="px-6 py-4 border-t bg-gray-50">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500">
              显示 {{ Math.min(filteredActivities.length, 20) }} 条，共 {{ filteredActivities.length }} 条记录
            </div>
            <div class="flex items-center space-x-2">
              <button class="px-3 py-1 border rounded-md text-sm hover:bg-gray-100">
                上一页
              </button>
              <span class="px-3 py-1 text-sm">1 / 1</span>
              <button class="px-3 py-1 border rounded-md text-sm hover:bg-gray-100">
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建团购弹窗 -->
    <div v-if="showAddModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-3xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">创建团购活动</h3>
          <button @click="showAddModal = false" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>

        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">活动名称</label>
            <input
              v-model="newGroupBuy.name"
              type="text"
              placeholder="请输入团购活动名称"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">选择商品</label>
              <select
                v-model="newGroupBuy.productId"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">请选择商品</option>
                <option value="1">轻奢纯棉刺绣水洗四件套</option>
                <option value="2">秋冬保暖加厚澳洲羊毛被</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">团购类型</label>
              <select
                v-model="newGroupBuy.type"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="normal">普通团购</option>
                <option value="ladder">阶梯团购</option>
                <option value="spike">秒杀团购</option>
              </select>
            </div>
          </div>

          <div class="grid grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">团购价格</label>
              <input
                v-model="newGroupBuy.groupPrice"
                type="number"
                step="0.01"
                placeholder="请输入团购价格"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">成团人数</label>
              <input
                v-model="newGroupBuy.minPeople"
                type="number"
                placeholder="最少成团人数"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">团购时长(小时)</label>
              <input
                v-model="newGroupBuy.duration"
                type="number"
                placeholder="团购持续时间"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">开始时间</label>
              <input
                v-model="newGroupBuy.startTime"
                type="datetime-local"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">结束时间</label>
              <input
                v-model="newGroupBuy.endTime"
                type="datetime-local"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div class="flex justify-end space-x-3 pt-4 border-t">
            <button @click="showAddModal = false" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
              取消
            </button>
            <button @click="createGroupBuy" class="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700">
              创建团购
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 团购记录弹窗 -->
    <div v-if="showGroupModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">团购记录</h3>
          <button @click="showGroupModal = false" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>

        <div class="space-y-4">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">团购ID</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">团长</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">商品</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">参团人数</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">状态</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">创建时间</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="group in groupRecords" :key="group.id" class="hover:bg-gray-50">
                  <td class="px-4 py-4 text-sm text-gray-900">#{{ group.id }}</td>
                  <td class="px-4 py-4 text-sm text-gray-900">{{ group.leaderName }}</td>
                  <td class="px-4 py-4 text-sm text-gray-900">{{ group.productName }}</td>
                  <td class="px-4 py-4 text-sm text-gray-900">{{ group.memberCount }}/{{ group.requiredCount }}</td>
                  <td class="px-4 py-4">
                    <span :class="[
                      'px-2 py-1 text-xs rounded-full',
                      group.status === 'success' ? 'bg-green-100 text-green-800' :
                      group.status === 'ongoing' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    ]">
                      {{ group.status === 'success' ? '成功' : group.status === 'ongoing' ? '进行中' : '失败' }}
                    </span>
                  </td>
                  <td class="px-4 py-4 text-sm text-gray-500">{{ group.createTime }}</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="flex justify-end pt-4 border-t">
            <button @click="showGroupModal = false" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GroupBuyPage',
  data() {
    return {
      // 搜索和筛选
      searchQuery: '',
      filterStatus: '',
      filterType: '',

      // 弹窗状态
      showAddModal: false,
      showGroupModal: false,

      // 统计数据
      statistics: {
        totalGroupBuys: 36,
        activeGroupBuys: 8,
        successfulGroups: 156,
        groupBuySales: '189,450.00'
      },

      // 新团购活动数据
      newGroupBuy: {
        name: '',
        productId: '',
        type: 'normal',
        groupPrice: '',
        minPeople: '',
        duration: '',
        startTime: '',
        endTime: ''
      },

      // 团购活动列表
      activities: [
        {
          id: 1,
          name: '四件套团购活动',
          productName: '轻奢纯棉刺绣水洗四件套',
          productImage: 'http://yanxuan.nosdn.127.net/8ab2d3287af0cefa2cc539e40600621d.png',
          type: 'normal',
          originalPrice: 299.00,
          groupPrice: 199.00,
          minPeople: 3,
          participants: 156,
          targetParticipants: 200,
          successGroups: 52,
          status: 'active',
          remainingTime: '23:45:12'
        },
        {
          id: 2,
          name: '羊毛被阶梯团购',
          productName: '秋冬保暖加厚澳洲羊毛被',
          productImage: 'http://yanxuan.nosdn.127.net/66425d1ed50b3968fed27c822fdd32e0.png',
          type: 'ladder',
          originalPrice: 459.00,
          groupPrice: 299.00,
          minPeople: 5,
          participants: 89,
          targetParticipants: 150,
          successGroups: 17,
          status: 'active',
          remainingTime: '15:32:08'
        }
      ],

      // 团购记录
      groupRecords: [
        {
          id: 1001,
          leaderName: '张三',
          productName: '轻奢纯棉刺绣水洗四件套',
          memberCount: 3,
          requiredCount: 3,
          status: 'success',
          createTime: '2024-01-15 14:30'
        },
        {
          id: 1002,
          leaderName: '李四',
          productName: '秋冬保暖加厚澳洲羊毛被',
          memberCount: 2,
          requiredCount: 5,
          status: 'ongoing',
          createTime: '2024-01-15 16:20'
        }
      ]
    }
  },

  computed: {
    filteredActivities() {
      let filtered = this.activities;

      if (this.searchQuery) {
        filtered = filtered.filter(activity =>
          activity.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
          activity.productName.toLowerCase().includes(this.searchQuery.toLowerCase())
        );
      }

      if (this.filterStatus) {
        filtered = filtered.filter(activity => activity.status === this.filterStatus);
      }

      if (this.filterType) {
        filtered = filtered.filter(activity => activity.type === this.filterType);
      }

      return filtered;
    }
  },

  mounted() {
    console.log('团购活动页面已加载');
  },

  methods: {
    // 获取状态标签
    getStatusLabel(status) {
      const labels = {
        active: '进行中',
        upcoming: '即将开始',
        ended: '已结束',
        disabled: '已停用'
      };
      return labels[status] || status;
    },

    // 获取类型标签
    getTypeLabel(type) {
      const labels = {
        normal: '普通团购',
        ladder: '阶梯团购',
        spike: '秒杀团购'
      };
      return labels[type] || type;
    },

    // 重置筛选
    resetFilters() {
      this.searchQuery = '';
      this.filterStatus = '';
      this.filterType = '';
    },

    // 查看活动详情
    viewActivity(activity) {
      console.log('查看活动详情:', activity);
      this.$message.info('查看功能开发中');
    },

    // 编辑活动
    editActivity(activity) {
      console.log('编辑活动:', activity);
      this.$message.info('编辑功能开发中');
    },

    // 切换活动状态
    toggleActivityStatus(activity) {
      console.log('切换活动状态:', activity);
      this.$message.info('状态切换功能开发中');
    },

    // 创建团购活动
    createGroupBuy() {
      console.log('创建团购活动:', this.newGroupBuy);
      this.showAddModal = false;
      this.$message.success('团购活动创建成功');
    },

    // 刷新数据
    refreshData() {
      console.log('刷新数据');
      this.$message.success('数据刷新成功');
    }
  }
}
</script>

<style scoped>
.group-buy-page {
  min-height: 100vh;
  background-color: #f9fafb;
}

.page-header {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 文本截断 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-1.md\\:grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}
</style>
