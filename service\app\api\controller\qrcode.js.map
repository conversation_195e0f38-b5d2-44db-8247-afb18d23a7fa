{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\qrcode.js"], "names": ["Base", "require", "rp", "fs", "http", "path", "module", "exports", "getBase64Action", "goodsId", "post", "userId", "page", "sceneData", "toString", "console", "log", "length", "fail", "options", "method", "url", "qs", "grant_type", "secret", "think", "config", "appid", "sessionData", "JSON", "parse", "token", "access_token", "data", "stringify", "options2", "host", "headers", "uploadFunc", "Promise", "resolve", "reject", "req", "request", "res", "statusCode", "setEncoding", "imgData", "on", "chunk", "error", "write", "end", "e", "success", "generateOrderExchangeSchemeAction", "query", "envVersion", "tokenOptions", "schemeData", "jump_wxa", "is_expire", "expire_type", "expire_interval", "schemeOptions", "body", "schemeResult", "schemeResponse", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "encodeURIComponent", "plainScheme", "encrypted_scheme", "openlink", "plain_scheme", "expire_time", "errmsg", "generateOrderExchangeLinkAction", "orderNumber", "source", "utm", "baseUrl", "redirectUrl", "params", "push", "join", "directScheme", "https_link", "direct_scheme", "qr_code_data", "generateShortLinkAction", "longUrl", "shortCode", "generateShortCode", "shortUrl", "short_url", "long_url", "short_code", "generateWechatMessageLinkAction", "queryParams", "wechatScheme", "wechat_scheme", "original_params", "usage_tips", "chars", "result", "i", "char<PERSON>t", "Math", "floor", "random"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,KAAKD,QAAQ,iBAAR,CAAX;AACA,MAAME,KAAKF,QAAQ,IAAR,CAAX;AACA,MAAMG,OAAOH,QAAQ,OAAR,CAAb;AACA,MAAMI,OAAOJ,QAAQ,MAAR,CAAb;AACA;AACAK,OAAOC,OAAP,GAAiB,cAAcP,IAAd,CAAmB;AAC1BQ,mBAAN,GAAwB;AAAA;;AAAA;AACpB,gBAAIC,UAAU,MAAKC,IAAL,CAAU,SAAV,CAAd;AACA,gBAAIC,SAAS,MAAKD,IAAL,CAAU,QAAV,KAAuB,CAApC;AACA,gBAAIE,OAAO,mBAAX;;AAEA;AACA,gBAAIC,YAAYF,SAAS,CAAT,GAAc,GAAEF,OAAQ,IAAGE,MAAO,EAAlC,GAAsCF,QAAQK,QAAR,EAAtD;;AAEAC,oBAAQC,GAAR,CAAY,eAAZ;AACAD,oBAAQC,GAAR,CAAY,OAAZ,EAAqBP,OAArB;AACAM,oBAAQC,GAAR,CAAY,QAAZ,EAAsBL,MAAtB;AACAI,oBAAQC,GAAR,CAAY,MAAZ,EAAoBH,SAApB;AACAE,oBAAQC,GAAR,CAAY,QAAZ,EAAsBH,UAAUI,MAAhC;;AAEA;AACA,gBAAIJ,UAAUI,MAAV,GAAmB,EAAvB,EAA2B;AACvBF,wBAAQC,GAAR,CAAY,eAAZ;AACA,uBAAO,MAAKE,IAAL,CAAU,GAAV,EAAe,OAAf,CAAP;AACH;AACD,kBAAMC,UAAU;AACZC,wBAAQ,MADI;AAEZC,qBAAK,yCAFO;AAGZC,oBAAI;AACAC,gCAAY,mBADZ;AAEAC,4BAAQC,MAAMC,MAAN,CAAa,eAAb,CAFR;AAGAC,2BAAOF,MAAMC,MAAN,CAAa,cAAb;AAHP;AAHQ,aAAhB;AASA,gBAAIE,cAAc,MAAM1B,GAAGiB,OAAH,CAAxB;AACAS,0BAAcC,KAAKC,KAAL,CAAWF,WAAX,CAAd;AACA,gBAAIG,QAAQH,YAAYI,YAAxB;AACA,gBAAIC,OAAO;AACP,yBAASpB,SADF;AAEP,wBAAQD,IAFD;AAGP,yBAAS,GAHF,EAGQ;AACf,8BAAc,KAJP;AAKP,8BAAc,EAAC,KAAK,CAAN,EAAS,KAAK,CAAd,EAAiB,KAAK,CAAtB,EALP;AAMP,8BAAc;AANP,aAAX;;AASAG,oBAAQC,GAAR,CAAY,UAAZ,EAAwBiB,IAAxB;AACAA,mBAAOJ,KAAKK,SAAL,CAAeD,IAAf,CAAP;AACA,gBAAIE,WAAW;AACXf,wBAAQ,MADG;AAEXgB,sBAAM,mBAFK;AAGX/B,sBAAM,yCAAyC0B,KAHpC;AAIXM,yBAAS;AACL,oCAAgB,kBADX;AAEL,sCAAkBJ,KAAKhB;AAFlB;AAJE,aAAf;AASA,kBAAMqB;AAAA,6CAAa,aAAY;AAC3B,2BAAO,IAAIC,OAAJ,CAAY,UAACC,OAAD,EAAUC,MAAV,EAAqB;AACpC,4BAAI;AACA,gCAAIC,MAAMtC,KAAKuC,OAAL,CAAaR,QAAb,EAAuB,UAASS,GAAT,EAAc;AAC3C7B,wCAAQC,GAAR,CAAY,aAAZ,EAA2B4B,IAAIC,UAA/B;AACA9B,wCAAQC,GAAR,CAAY,WAAZ,EAAyB4B,IAAIP,OAA7B;;AAEA,oCAAIO,IAAIC,UAAJ,KAAmB,GAAvB,EAA4B;AACxB9B,4CAAQC,GAAR,CAAY,iBAAZ,EAA+B4B,IAAIC,UAAnC;AACA,2CAAOL,QAAQ,IAAR,CAAP;AACH;;AAEDI,oCAAIE,WAAJ,CAAgB,QAAhB;AACA,oCAAIC,UAAU,EAAd;AACAH,oCAAII,EAAJ,CAAO,MAAP,EAAe,UAASC,KAAT,EAAgB;AAC3BF,+CAAWE,KAAX;AACH,iCAFD;AAGAL,oCAAII,EAAJ,CAAO,KAAP,EAAc,YAAW;AACrBjC,4CAAQC,GAAR,CAAY,iBAAZ,EAA+B+B,QAAQ9B,MAAvC;AACA,2CAAOuB,QAAQO,OAAR,CAAP;AACH,iCAHD;AAIAH,oCAAII,EAAJ,CAAO,OAAP,EAAgB,UAASE,KAAT,EAAgB;AAC5BnC,4CAAQC,GAAR,CAAY,YAAZ,EAA0BkC,KAA1B;AACA,2CAAOV,QAAQ,IAAR,CAAP;AACH,iCAHD;AAIH,6BAtBS,CAAV;;AAwBAE,gCAAIM,EAAJ,CAAO,OAAP,EAAgB,UAASE,KAAT,EAAgB;AAC5BnC,wCAAQC,GAAR,CAAY,cAAZ,EAA4BkC,KAA5B;AACA,uCAAOV,QAAQ,IAAR,CAAP;AACH,6BAHD;;AAKAE,gCAAIS,KAAJ,CAAUlB,IAAV;AACAS,gCAAIU,GAAJ;AACH,yBAhCD,CAgCE,OAAOC,CAAP,EAAU;AACRtC,oCAAQC,GAAR,CAAY,YAAZ,EAA0BqC,CAA1B;AACA,mCAAOb,QAAQ,IAAR,CAAP;AACH;AACJ,qBArCM,CAAP;AAsCH,iBAvCK;;AAAA;AAAA;AAAA;AAAA,gBAAN;;AAyCAzB,oBAAQC,GAAR,CAAY,mBAAZ;AACA,kBAAMK,MAAM,MAAMiB,YAAlB;;AAEA,gBAAIjB,GAAJ,EAAS;AACLN,wBAAQC,GAAR,CAAY,WAAZ;AACA,uBAAO,MAAKsC,OAAL,CAAajC,GAAb,CAAP;AACH,aAHD,MAGO;AACHN,wBAAQC,GAAR,CAAY,WAAZ;AACA,uBAAO,MAAKE,IAAL,CAAU,GAAV,EAAe,SAAf,CAAP;AACH;AArGmB;AAsGvB;;AAED;;;AAGMqC,qCAAN,GAA0C;AAAA;;AAAA;AACtC,gBAAI;AACA;AACA,sBAAMC,QAAQ,OAAK9C,IAAL,CAAU,OAAV,KAAsB,EAApC;AACA,sBAAM+C,aAAa,OAAK/C,IAAL,CAAU,aAAV,KAA4B,SAA/C;;AAEA;AACA,sBAAMgD,eAAe;AACjBtC,4BAAQ,MADS;AAEjBC,yBAAK,yCAFY;AAGjBC,wBAAI;AACAC,oCAAY,mBADZ;AAEAC,gCAAQC,MAAMC,MAAN,CAAa,eAAb,CAFR;AAGAC,+BAAOF,MAAMC,MAAN,CAAa,cAAb;AAHP;AAHa,iBAArB;;AAUA,oBAAIE,cAAc,MAAM1B,GAAGwD,YAAH,CAAxB;AACA9B,8BAAcC,KAAKC,KAAL,CAAWF,WAAX,CAAd;;AAEA,oBAAI,CAACA,YAAYI,YAAjB,EAA+B;AAC3B,2BAAO,OAAKd,IAAL,CAAU,oBAAV,CAAP;AACH;;AAED,sBAAMa,QAAQH,YAAYI,YAA1B;;AAEA;AACA,sBAAM2B,aAAa;AACfC,8BAAU;AACNvD,8BAAM,4BADA;AAENmD,+BAAOA;AAFD,qBADK;AAKfK,+BAAW,KALI;AAMfC,iCAAa,CANE;AAOfC,qCAAiB,EAPF,CAOK;AAPL,iBAAnB;;AAUA;AACA,sBAAMC,gBAAgB;AAClB5C,4BAAQ,MADU;AAElBC,yBAAM,6DAA4DU,KAAM,EAFtD;AAGlBM,6BAAS;AACL,wCAAgB;AADX,qBAHS;AAMlB4B,0BAAMpC,KAAKK,SAAL,CAAeyB,UAAf;AANY,iBAAtB;;AASA,sBAAMO,eAAe,MAAMhE,GAAG8D,aAAH,CAA3B;AACA,sBAAMG,iBAAiBtC,KAAKC,KAAL,CAAWoC,YAAX,CAAvB;;AAEA,oBAAIC,eAAeC,OAAf,KAA2B,CAA/B,EAAkC;AAC9B;AACA,0BAAMzC,QAAQF,MAAMC,MAAN,CAAa,cAAb,CAAd;AACA,0BAAMrB,OAAO,4BAAb;AACA,0BAAMgE,eAAeC,mBAAmBd,KAAnB,CAArB;;AAEA,0BAAMe,cAAe,+BAA8B5C,KAAM,SAAQtB,IAAK,UAASgE,YAAa,gBAAeZ,UAAW,EAAtH;;AAEA,2BAAO,OAAKH,OAAL,CAAa;AAChBkB,0CAAkBL,eAAeM,QADjB,EAC2B;AAC3CC,sCAAcH,WAFE,EAEW;AAC3BI,qCAAaR,eAAeQ,WAAf,IAA8B;AAH3B,qBAAb,CAAP;AAKH,iBAbD,MAaO;AACH,2BAAO,OAAKzD,IAAL,CAAW,mBAAkBiD,eAAeS,MAAO,EAAnD,CAAP;AACH;AAEJ,aAlED,CAkEE,OAAO1B,KAAP,EAAc;AACZnC,wBAAQmC,KAAR,CAAc,iBAAd,EAAiCA,KAAjC;AACA,uBAAO,OAAKhC,IAAL,CAAU,sBAAV,CAAP;AACH;AAtEqC;AAuEzC;;AAED;;;AAGM2D,mCAAN,GAAwC;AAAA;;AAAA;AACpC,gBAAI;AACA;AACA,sBAAMC,cAAc,OAAKpE,IAAL,CAAU,aAAV,KAA4B,EAAhD;AACA,sBAAMqE,SAAS,OAAKrE,IAAL,CAAU,QAAV,KAAuB,EAAtC;AACA,sBAAMsE,MAAM,OAAKtE,IAAL,CAAU,KAAV,KAAoB,EAAhC;;AAEA;AACA,sBAAMuE,UAAU,sDAAhB,CAPA,CAOwE;AACxE,oBAAIC,cAAcD,OAAlB;;AAEA,sBAAME,SAAS,EAAf;AACA,oBAAIL,WAAJ,EAAiBK,OAAOC,IAAP,CAAa,eAAcd,mBAAmBQ,WAAnB,CAAgC,EAA3D;AACjB,oBAAIC,MAAJ,EAAYI,OAAOC,IAAP,CAAa,UAASd,mBAAmBS,MAAnB,CAA2B,EAAjD;AACZ,oBAAIC,GAAJ,EAASG,OAAOC,IAAP,CAAa,OAAMd,mBAAmBU,GAAnB,CAAwB,EAA3C;;AAET,oBAAIG,OAAOlE,MAAP,GAAgB,CAApB,EAAuB;AACnBiE,mCAAe,MAAMC,OAAOE,IAAP,CAAY,GAAZ,CAArB;AACH;;AAED;AACA,sBAAM1D,QAAQF,MAAMC,MAAN,CAAa,cAAb,CAAd;AACA,sBAAMrB,OAAO,4BAAb;;AAEA,oBAAImD,QAAQ,EAAZ;AACA,oBAAIsB,WAAJ,EAAiBtB,SAAU,eAAcc,mBAAmBQ,WAAnB,CAAgC,EAAxD;AACjB,oBAAIC,MAAJ,EAAY;AACRvB,6BAASA,QAAQ,GAAR,GAAc,EAAvB;AACAA,6BAAU,UAASc,mBAAmBS,MAAnB,CAA2B,EAA9C;AACH;AACD,oBAAIC,GAAJ,EAAS;AACLxB,6BAASA,QAAQ,GAAR,GAAc,EAAvB;AACAA,6BAAU,OAAMc,mBAAmBU,GAAnB,CAAwB,EAAxC;AACH;;AAED,sBAAMM,eAAgB,+BAA8B3D,KAAM,SAAQtB,IAAK,UAASiE,mBAAmBd,KAAnB,CAA0B,sBAA1G;;AAEA,uBAAO,OAAKF,OAAL,CAAa;AAChBiC,gCAAYL,WADI,EACgB;AAChCM,mCAAeF,YAFC,EAEgB;AAChCG,kCAAcP,WAHE,CAGgB;AAHhB,iBAAb,CAAP;AAMH,aA1CD,CA0CE,OAAOhC,KAAP,EAAc;AACZnC,wBAAQmC,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,OAAKhC,IAAL,CAAU,gBAAV,CAAP;AACH;AA9CmC;AA+CvC;;AAED;;;AAGMwE,2BAAN,GAAgC;AAAA;;AAAA;AAC5B,gBAAI;AACA,sBAAMC,UAAU,OAAKjF,IAAL,CAAU,KAAV,CAAhB;AACA,oBAAI,CAACiF,OAAL,EAAc;AACV,2BAAO,OAAKzE,IAAL,CAAU,SAAV,CAAP;AACH;;AAED;AACA,sBAAM0E,YAAY,OAAKC,iBAAL,EAAlB;AACA,sBAAMC,WAAY,6BAA4BF,SAAU,EAAxD;;AAEA,uBAAO,OAAKtC,OAAL,CAAa;AAChByC,+BAAWD,QADK;AAEhBE,8BAAUL,OAFM;AAGhBM,gCAAYL;AAHI,iBAAb,CAAP;AAMH,aAhBD,CAgBE,OAAO1C,KAAP,EAAc;AACZnC,wBAAQmC,KAAR,CAAc,UAAd,EAA0BA,KAA1B;AACA,uBAAO,OAAKhC,IAAL,CAAU,eAAV,CAAP;AACH;AApB2B;AAqB/B;;AAED;;;AAGMgF,mCAAN,GAAwC;AAAA;;AAAA;AACpC,gBAAI;AACA;AACA,sBAAMpB,cAAc,OAAKpE,IAAL,CAAU,aAAV,KAA4B,EAAhD;AACA,sBAAMqE,SAAS,OAAKrE,IAAL,CAAU,QAAV,KAAuB,gBAAtC;AACA,sBAAMsE,MAAM,OAAKtE,IAAL,CAAU,KAAV,KAAoB,EAAhC;AACA,sBAAMC,SAAS,OAAKD,IAAL,CAAU,QAAV,KAAuB,EAAtC;;AAEA,sBAAMiB,QAAQF,MAAMC,MAAN,CAAa,cAAb,CAAd;AACA,sBAAMrB,OAAO,4BAAb;;AAEA;AACA,sBAAM8F,cAAc,EAApB;AACA,oBAAIrB,WAAJ,EAAiBqB,YAAYf,IAAZ,CAAkB,eAAcd,mBAAmBQ,WAAnB,CAAgC,EAAhE;AACjB,oBAAIC,MAAJ,EAAYoB,YAAYf,IAAZ,CAAkB,UAASd,mBAAmBS,MAAnB,CAA2B,EAAtD;AACZ,oBAAIC,GAAJ,EAASmB,YAAYf,IAAZ,CAAkB,OAAMd,mBAAmBU,GAAnB,CAAwB,EAAhD;AACT,oBAAIrE,MAAJ,EAAYwF,YAAYf,IAAZ,CAAkB,UAASd,mBAAmB3D,MAAnB,CAA2B,EAAtD;;AAEZ,sBAAM6C,QAAQ2C,YAAYd,IAAZ,CAAiB,GAAjB,CAAd;AACA,sBAAMhB,eAAeC,mBAAmBd,KAAnB,CAArB;;AAEA;AACA,sBAAM4C,eAAgB,+BAA8BzE,KAAM,SAAQtB,IAAK,UAASgE,YAAa,sBAA7F;;AAEA;AACA,sBAAMuB,YAAY,OAAKC,iBAAL,EAAlB;AACA,sBAAMC,WAAY,8BAA6BF,SAAU,EAAzD;;AAEA,uBAAO,OAAKtC,OAAL,CAAa;AAChB+C,mCAAeD,YADC,EACiB;AACjCL,+BAAWD,QAFK,EAEgB;AAChCQ,qCAAiB,EAAe;AAC5BxB,mCADa;AAEbC,8BAFa;AAGbC,2BAHa;AAIbrE;AAJa,qBAHD;AAShB4F,gCAAY,CACR,0BADQ,EAER,oBAFQ,EAGR,qBAHQ;AATI,iBAAb,CAAP;AAgBH,aA3CD,CA2CE,OAAOrD,KAAP,EAAc;AACZnC,wBAAQmC,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO,OAAKhC,IAAL,CAAU,cAAV,CAAP;AACH;AA/CmC;AAgDvC;;AAED;;;AAGA2E,wBAAoB;AAChB,cAAMW,QAAQ,gEAAd;AACA,YAAIC,SAAS,EAAb;AACA,aAAK,IAAIC,IAAI,CAAb,EAAgBA,IAAI,CAApB,EAAuBA,GAAvB,EAA4B;AACxBD,sBAAUD,MAAMG,MAAN,CAAaC,KAAKC,KAAL,CAAWD,KAAKE,MAAL,KAAgBN,MAAMvF,MAAjC,CAAb,CAAV;AACH;AACD,eAAOwF,MAAP;AACH;AAlU+B,CAApC", "file": "..\\..\\..\\src\\api\\controller\\qrcode.js", "sourcesContent": ["const Base = require('./base.js');\nconst rp = require('request-promise');\nconst fs = require('fs');\nconst http = require(\"https\");\nconst path = require('path');\n// const mineType = require('mime-types');\nmodule.exports = class extends Base {\n    async getBase64Action() {\n        let goodsId = this.post('goodsId');\n        let userId = this.post('userId') || 0;\n        let page = \"pages/goods/goods\";\n\n        // 场景值限制：最多32个字符，建议使用简短格式\n        let sceneData = userId > 0 ? `${goodsId},${userId}` : goodsId.toString();\n\n        console.log('=== 生成二维码 ===');\n        console.log('商品ID:', goodsId);\n        console.log('分享人ID:', userId);\n        console.log('场景值:', sceneData);\n        console.log('场景值长度:', sceneData.length);\n\n        // 检查场景值长度\n        if (sceneData.length > 32) {\n            console.log('❌ 场景值超过32字符限制');\n            return this.fail(400, '场景值过长');\n        }\n        const options = {\n            method: 'POST',\n            url: 'https://api.weixin.qq.com/cgi-bin/token',\n            qs: {\n                grant_type: 'client_credential',\n                secret: think.config('weixin.secret'),\n                appid: think.config('weixin.appid')\n            }\n        };\n        let sessionData = await rp(options);\n        sessionData = JSON.parse(sessionData);\n        let token = sessionData.access_token;\n        let data = {\n            \"scene\": sceneData,\n            \"page\": page,\n            \"width\": 516,  // 增加二维码尺寸20%（430 * 1.2 = 516），提高清晰度\n            \"auto_color\": false,\n            \"line_color\": {\"r\": 0, \"g\": 0, \"b\": 0},\n            \"is_hyaline\": false\n        };\n\n        console.log('二维码生成参数:', data);\n        data = JSON.stringify(data);\n        var options2 = {\n            method: \"POST\",\n            host: \"api.weixin.qq.com\",\n            path: \"/wxa/getwxacodeunlimit?access_token=\" + token,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Content-Length\": data.length\n            }\n        };\n        const uploadFunc = async () => {\n            return new Promise((resolve, reject) => {\n                try {\n                    var req = http.request(options2, function(res) {\n                        console.log('微信API响应状态码:', res.statusCode);\n                        console.log('微信API响应头:', res.headers);\n\n                        if (res.statusCode !== 200) {\n                            console.log('❌ 微信API返回错误状态码:', res.statusCode);\n                            return resolve(null);\n                        }\n\n                        res.setEncoding(\"base64\");\n                        var imgData = \"\";\n                        res.on('data', function(chunk) {\n                            imgData += chunk;\n                        });\n                        res.on(\"end\", function() {\n                            console.log('✅ 二维码生成成功，数据长度:', imgData.length);\n                            return resolve(imgData);\n                        });\n                        res.on(\"error\", function(error) {\n                            console.log('❌ 接收数据时出错:', error);\n                            return resolve(null);\n                        });\n                    });\n\n                    req.on('error', function(error) {\n                        console.log('❌ 请求微信API出错:', error);\n                        return resolve(null);\n                    });\n\n                    req.write(data);\n                    req.end();\n                } catch (e) {\n                    console.log('❌ 二维码生成异常:', e);\n                    return resolve(null);\n                }\n            })\n        };\n\n        console.log('开始调用微信API生成二维码...');\n        const url = await uploadFunc();\n\n        if (url) {\n            console.log('✅ 二维码生成完成');\n            return this.success(url);\n        } else {\n            console.log('❌ 二维码生成失败');\n            return this.fail(500, '二维码生成失败');\n        }\n    }\n\n    /**\n     * 生成订单兑换页面的URL Scheme\n     */\n    async generateOrderExchangeSchemeAction() {\n        try {\n            // 获取参数\n            const query = this.post('query') || '';\n            const envVersion = this.post('env_version') || 'release';\n\n            // 获取微信access_token\n            const tokenOptions = {\n                method: 'POST',\n                url: 'https://api.weixin.qq.com/cgi-bin/token',\n                qs: {\n                    grant_type: 'client_credential',\n                    secret: think.config('weixin.secret'),\n                    appid: think.config('weixin.appid')\n                }\n            };\n\n            let sessionData = await rp(tokenOptions);\n            sessionData = JSON.parse(sessionData);\n\n            if (!sessionData.access_token) {\n                return this.fail('获取微信access_token失败');\n            }\n\n            const token = sessionData.access_token;\n\n            // 生成URL Scheme的参数\n            const schemeData = {\n                jump_wxa: {\n                    path: 'pages/order-exchange/index',\n                    query: query\n                },\n                is_expire: false,\n                expire_type: 1,\n                expire_interval: 30 // 30天有效期\n            };\n\n            // 调用微信API生成URL Scheme\n            const schemeOptions = {\n                method: 'POST',\n                url: `https://api.weixin.qq.com/wxa/generatescheme?access_token=${token}`,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(schemeData)\n            };\n\n            const schemeResult = await rp(schemeOptions);\n            const schemeResponse = JSON.parse(schemeResult);\n\n            if (schemeResponse.errcode === 0) {\n                // 同时生成明文URL Scheme作为备选\n                const appid = think.config('weixin.appid');\n                const path = 'pages/order-exchange/index';\n                const encodedQuery = encodeURIComponent(query);\n\n                const plainScheme = `weixin://dl/business/?appid=${appid}&path=${path}&query=${encodedQuery}&env_version=${envVersion}`;\n\n                return this.success({\n                    encrypted_scheme: schemeResponse.openlink, // 加密URL Scheme\n                    plain_scheme: plainScheme, // 明文URL Scheme\n                    expire_time: schemeResponse.expire_time || null\n                });\n            } else {\n                return this.fail(`生成URL Scheme失败: ${schemeResponse.errmsg}`);\n            }\n\n        } catch (error) {\n            console.error('生成URL Scheme错误:', error);\n            return this.fail('生成URL Scheme失败，请稍后重试');\n        }\n    }\n\n    /**\n     * 生成HTTPS跳转链接\n     */\n    async generateOrderExchangeLinkAction() {\n        try {\n            // 获取参数\n            const orderNumber = this.post('orderNumber') || '';\n            const source = this.post('source') || '';\n            const utm = this.post('utm') || '';\n\n            // 构建HTTPS跳转链接\n            const baseUrl = 'https://your-domain.com/order-exchange-redirect.html'; // 替换为您的域名\n            let redirectUrl = baseUrl;\n\n            const params = [];\n            if (orderNumber) params.push(`orderNumber=${encodeURIComponent(orderNumber)}`);\n            if (source) params.push(`source=${encodeURIComponent(source)}`);\n            if (utm) params.push(`utm=${encodeURIComponent(utm)}`);\n\n            if (params.length > 0) {\n                redirectUrl += '?' + params.join('&');\n            }\n\n            // 同时生成直接的URL Scheme\n            const appid = think.config('weixin.appid');\n            const path = 'pages/order-exchange/index';\n\n            let query = '';\n            if (orderNumber) query += `orderNumber=${encodeURIComponent(orderNumber)}`;\n            if (source) {\n                query += query ? '&' : '';\n                query += `source=${encodeURIComponent(source)}`;\n            }\n            if (utm) {\n                query += query ? '&' : '';\n                query += `utm=${encodeURIComponent(utm)}`;\n            }\n\n            const directScheme = `weixin://dl/business/?appid=${appid}&path=${path}&query=${encodeURIComponent(query)}&env_version=release`;\n\n            return this.success({\n                https_link: redirectUrl,        // HTTPS跳转页面\n                direct_scheme: directScheme,    // 直接URL Scheme\n                qr_code_data: redirectUrl       // 可用于生成二维码的数据\n            });\n\n        } catch (error) {\n            console.error('生成跳转链接错误:', error);\n            return this.fail('生成跳转链接失败，请稍后重试');\n        }\n    }\n\n    /**\n     * 生成短链接（可选）\n     */\n    async generateShortLinkAction() {\n        try {\n            const longUrl = this.post('url');\n            if (!longUrl) {\n                return this.fail('缺少URL参数');\n            }\n\n            // 这里可以集成第三方短链接服务\n            const shortCode = this.generateShortCode();\n            const shortUrl = `https://your-domain.com/s/${shortCode}`;\n\n            return this.success({\n                short_url: shortUrl,\n                long_url: longUrl,\n                short_code: shortCode\n            });\n\n        } catch (error) {\n            console.error('生成短链接错误:', error);\n            return this.fail('生成短链接失败，请稍后重试');\n        }\n    }\n\n    /**\n     * 生成微信消息中使用的订单兑换链接\n     */\n    async generateWechatMessageLinkAction() {\n        try {\n            // 获取参数\n            const orderNumber = this.post('orderNumber') || '';\n            const source = this.post('source') || 'wechat_message';\n            const utm = this.post('utm') || '';\n            const userId = this.post('userId') || '';\n\n            const appid = think.config('weixin.appid');\n            const path = 'pages/order-exchange/index';\n\n            // 构建query参数\n            const queryParams = [];\n            if (orderNumber) queryParams.push(`orderNumber=${encodeURIComponent(orderNumber)}`);\n            if (source) queryParams.push(`source=${encodeURIComponent(source)}`);\n            if (utm) queryParams.push(`utm=${encodeURIComponent(utm)}`);\n            if (userId) queryParams.push(`userId=${encodeURIComponent(userId)}`);\n\n            const query = queryParams.join('&');\n            const encodedQuery = encodeURIComponent(query);\n\n            // 生成明文URL Scheme（适合微信消息）\n            const wechatScheme = `weixin://dl/business/?appid=${appid}&path=${path}&query=${encodedQuery}&env_version=release`;\n\n            // 生成短链接（可选，用于短信等场景）\n            const shortCode = this.generateShortCode();\n            const shortUrl = `https://your-domain.com/go/${shortCode}`;\n\n            return this.success({\n                wechat_scheme: wechatScheme,     // 微信消息中使用的链接\n                short_url: shortUrl,            // 短链接（可选）\n                original_params: {              // 原始参数（调试用）\n                    orderNumber,\n                    source,\n                    utm,\n                    userId\n                },\n                usage_tips: [\n                    '在微信消息中直接使用 wechat_scheme',\n                    '在短信中可以使用 short_url',\n                    '用户点击后会直接打开小程序订单兑换页面'\n                ]\n            });\n\n        } catch (error) {\n            console.error('生成微信消息链接错误:', error);\n            return this.fail('生成链接失败，请稍后重试');\n        }\n    }\n\n    /**\n     * 生成短代码\n     */\n    generateShortCode() {\n        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n        let result = '';\n        for (let i = 0; i < 6; i++) {\n            result += chars.charAt(Math.floor(Math.random() * chars.length));\n        }\n        return result;\n    }\n}"]}