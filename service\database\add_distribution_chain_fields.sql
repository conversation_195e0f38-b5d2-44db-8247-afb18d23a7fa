-- 为推广员上下级关系和佣金管理添加必要字段
-- 执行此脚本来支持推广员上下级关系和10天期限佣金管理

USE hiolabs;

-- 为个人推广员表添加上级关系字段
ALTER TABLE `hiolabs_personal_promoters`
ADD COLUMN `parent_user_id` int(11) DEFAULT NULL COMMENT '上级推广员用户ID' AFTER `user_id`;

-- 为个人推广员表添加索引
ALTER TABLE `hiolabs_personal_promoters`
ADD KEY `idx_parent_user_id` (`parent_user_id`);

-- 为推广订单表添加上级推广员字段和状态管理
ALTER TABLE `hiolabs_promotion_orders`
ADD COLUMN `parent_promoter_user_id` int(11) DEFAULT NULL COMMENT '上级推广员用户ID' AFTER `promoter_user_id`,
ADD COLUMN `commission_status` varchar(20) DEFAULT 'pending' COMMENT '佣金状态 pending待发放 settled已发放 withdrawn已提现 refunded已退款' AFTER `status`,
ADD COLUMN `withdraw_time` int(11) DEFAULT 0 COMMENT '提现时间' AFTER `settle_time`,
ADD COLUMN `refund_time` int(11) DEFAULT 0 COMMENT '退款时间' AFTER `withdraw_time`;

-- 为推广订单表添加索引
ALTER TABLE `hiolabs_promotion_orders`
ADD KEY `idx_parent_promoter_user_id` (`parent_promoter_user_id`),
ADD KEY `idx_commission_status` (`commission_status`),
ADD KEY `idx_settle_time` (`settle_time`);

-- 为佣金日志表添加状态和关联字段
ALTER TABLE `hiolabs_commission_log`
ADD COLUMN `commission_status` varchar(20) DEFAULT 'settled' COMMENT '佣金状态 settled已发放 withdrawn已提现 refunded已退款' AFTER `status`,
ADD COLUMN `promotion_order_id` int(11) DEFAULT NULL COMMENT '关联推广订单ID' AFTER `source_id`,
ADD COLUMN `settle_time` int(11) DEFAULT 0 COMMENT '发放时间' AFTER `created_at`;

-- 为佣金日志表添加索引
ALTER TABLE `hiolabs_commission_log`
ADD KEY `idx_commission_status` (`commission_status`),
ADD KEY `idx_promotion_order_id` (`promotion_order_id`),
ADD KEY `idx_settle_time` (`settle_time`),
ADD KEY `idx_user_settle_time` (`user_id`, `settle_time`);

-- 创建提现记录表
CREATE TABLE IF NOT EXISTS `hiolabs_withdraw_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `method` varchar(20) NOT NULL DEFAULT 'wechat' COMMENT '提现方式 wechat微信 alipay支付宝 bank银行卡',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态 pending待处理 processing处理中 success成功 failed失败',
  `apply_time` int(11) NOT NULL COMMENT '申请时间',
  `process_time` int(11) DEFAULT 0 COMMENT '处理时间',
  `complete_time` int(11) DEFAULT 0 COMMENT '完成时间',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_apply_time` (`apply_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现记录表';

SELECT 'Commission management fields and withdraw table added successfully!' as result;
