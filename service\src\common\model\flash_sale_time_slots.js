module.exports = class extends think.Model {
  
  /**
   * 获取表名
   */
  get tableName() {
    return 'hiolabs_flash_sale_time_slots';
  }
  
  /**
   * 获取所有启用的时段
   */
  async getActiveTimeSlots() {
    return await this.where({
      is_active: 1
    }).order('sort_order ASC').select();
  }
  
  /**
   * 根据当前时间获取对应的时段
   */
  async getCurrentTimeSlot() {
    const now = new Date();
    const currentTime = now.toTimeString().substring(0, 8); // HH:MM:SS格式
    
    return await this.where({
      is_active: 1,
      start_time: ['<=', currentTime],
      end_time: ['>=', currentTime]
    }).find();
  }
  
  /**
   * 获取下一个时段
   */
  async getNextTimeSlot() {
    const now = new Date();
    const currentTime = now.toTimeString().substring(0, 8);
    
    return await this.where({
      is_active: 1,
      start_time: ['>', currentTime]
    }).order('start_time ASC').find();
  }
};
