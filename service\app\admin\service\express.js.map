{"version": 3, "sources": ["..\\..\\..\\src\\admin\\service\\express.js"], "names": ["rp", "require", "_", "module", "exports", "think", "Service", "queryExpress", "shipperCode", "logisticCode", "orderCode", "expressInfo", "success", "shipperName", "is<PERSON><PERSON><PERSON>", "traces", "fromData", "generateFromData", "isEmpty", "sendOptions", "method", "url", "config", "headers", "form", "requestResult", "parseExpressResult", "err", "requestData", "generateRequestData", "RequestData", "encodeURI", "EBusinessID", "RequestType", "DataSign", "generateDataSign", "DataType", "OrderCode", "ShipperCode", "LogisticCode", "JSON", "stringify", "<PERSON><PERSON><PERSON>", "from", "md5", "toString", "isString", "parse", "Success", "Number", "parseInt", "State", "Traces", "Array", "isArray", "map", "item", "datetime", "AcceptTime", "content", "AcceptStation", "reverse", "mianExpress", "data", "mianFromData", "parseMianExpressResult", "htmldata", "PrintTemplate", "html", "mianDataSign", "buildForm", "requestDataEncode", "APIKey", "API_URL", "dataSign", "printDataSign", "get_ip", "is_priview", "console", "log", "ip", "i", "text", "exec"], "mappings": ";;AAAA,MAAMA,KAAKC,QAAQ,iBAAR,CAAX;AACA,MAAMC,IAAID,QAAQ,QAAR,CAAV;AACAE,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,OAApB,CAA4B;AACnCC,gBAAN,CAAmBC,WAAnB,EAAgCC,YAAhC,EAA8CC,YAAY,EAA1D,EAA8D;AAAA;;AAAA;AAC1D;AACA,gBAAIC,cAAc;AACdC,yBAAS,KADK;AAEdJ,6BAAaA,WAFC;AAGdK,6BAAa,EAHC;AAIdJ,8BAAcA,YAJA;AAKdK,0BAAU,CALI;AAMdC,wBAAQ;AANM,aAAlB;AAQA;AACA,kBAAMC,WAAW,MAAKC,gBAAL,CAAsBT,WAAtB,EAAmCC,YAAnC,EAAiDC,SAAjD,CAAjB;AACA,gBAAIL,MAAMa,OAAN,CAAcF,QAAd,CAAJ,EAA6B;AACzB,uBAAOL,WAAP;AACH;AACD;AACA,kBAAMQ,cAAc;AAChBC,wBAAQ,MADQ;AAEhBC,qBAAKhB,MAAMiB,MAAN,CAAa,qBAAb,CAFW;AAGhBC,yBAAS;AACL,oCAAgB;AADX,iBAHO;AAMhBC,sBAAMR;AANU,aAApB;AAQA;AACA,gBAAI;AACA,sBAAMS,gBAAgB,MAAMzB,GAAGmB,WAAH,CAA5B;AACA,oBAAId,MAAMa,OAAN,CAAcO,aAAd,CAAJ,EAAkC;AAC9B,2BAAOd,WAAP;AACH;AACDA,8BAAc,MAAKe,kBAAL,CAAwBD,aAAxB,CAAd;AACAd,4BAAYH,WAAZ,GAA0BA,WAA1B;AACAG,4BAAYF,YAAZ,GAA2BA,YAA3B;AACA,uBAAOE,WAAP;AACH,aATD,CASE,OAAOgB,GAAP,EAAY;AACV,uBAAOhB,WAAP;AACH;AApCyD;AAqC7D;AACD;AACAM,qBAAiBT,WAAjB,EAA8BC,YAA9B,EAA4CC,SAA5C,EAAuD;AACnD,cAAMkB,cAAc,KAAKC,mBAAL,CAAyBrB,WAAzB,EAAsCC,YAAtC,EAAoDC,SAApD,CAApB;AACA,cAAMM,WAAW;AACbc,yBAAaC,UAAUH,WAAV,CADA,EACwB;AACrCI,yBAAa3B,MAAMiB,MAAN,CAAa,eAAb,CAFA,EAE+B;AAC5CW,yBAAa,MAHA,EAGQ;AACrBC,sBAAU,KAAKC,gBAAL,CAAsBP,WAAtB,CAJG,EAIiC;AAC9CQ,sBAAU,GALG,CAKC;AALD,SAAjB;AAOA,eAAOpB,QAAP;AACH;AACD;AACAa,wBAAoBrB,WAApB,EAAiCC,YAAjC,EAA+CC,YAAY,EAA3D,EAA+D;AAC3D;AACA,cAAMkB,cAAc;AAChBS,uBAAW3B,SADK;AAEhB4B,yBAAa9B,WAFG;AAGhB+B,0BAAc9B;AAHE,SAApB;AAKA,eAAO+B,KAAKC,SAAL,CAAeb,WAAf,CAAP;AACH;AACD;AACAO,qBAAiBP,WAAjB,EAA8B;AAC1B,eAAOG,UAAUW,OAAOC,IAAP,CAAYtC,MAAMuC,GAAN,CAAUhB,cAAcvB,MAAMiB,MAAN,CAAa,gBAAb,CAAxB,CAAZ,EAAqEuB,QAArE,CAA8E,QAA9E,CAAV,CAAP;AACH;AACDnB,uBAAmBD,aAAnB,EAAkC;AAC9B,cAAMd,cAAc;AAChBC,qBAAS,KADO;AAEhBJ,yBAAa,EAFG;AAGhBK,yBAAa,EAHG;AAIhBJ,0BAAc,EAJE;AAKhBK,sBAAU,CALM;AAMhBC,oBAAQ;AANQ,SAApB;AAQA,YAAIV,MAAMa,OAAN,CAAcO,aAAd,CAAJ,EAAkC;AAC9B,mBAAOd,WAAP;AACH;AACD,YAAI;AACA,gBAAIT,EAAE4C,QAAF,CAAWrB,aAAX,CAAJ,EAA+B;AAC3BA,gCAAgBe,KAAKO,KAAL,CAAWtB,aAAX,CAAhB,CAD2B,CACgB;AAC9C;AACJ,SAJD,CAIE,OAAOE,GAAP,EAAY;AACV,mBAAOhB,WAAP;AACH;AACD,YAAIN,MAAMa,OAAN,CAAcO,cAAcuB,OAA5B,CAAJ,EAA0C;AACtC,mBAAOrC,WAAP;AACH;AACD;AACA,YAAIsC,OAAOC,QAAP,CAAgBzB,cAAc0B,KAA9B,MAAyC,CAA7C,EAAgD;AAC5CxC,wBAAYG,QAAZ,GAAuB,CAAvB;AACH;AACDH,oBAAYC,OAAZ,GAAsB,IAAtB;AACA,YAAI,CAACP,MAAMa,OAAN,CAAcO,cAAc2B,MAA5B,CAAD,IAAwCC,MAAMC,OAAN,CAAc7B,cAAc2B,MAA5B,CAA5C,EAAiF;AAC7EzC,wBAAYI,MAAZ,GAAqBb,EAAEqD,GAAF,CAAM9B,cAAc2B,MAApB,EAA4BI,QAAQ;AACrD,uBAAO;AACHC,8BAAUD,KAAKE,UADZ;AAEHC,6BAASH,KAAKI;AAFX,iBAAP;AAIH,aALoB,CAArB;AAMA1D,cAAE2D,OAAF,CAAUlD,YAAYI,MAAtB;AACH;AACD,eAAOJ,WAAP;AACH;AACD;AACMmD,eAAN,CAAkBC,OAAO,EAAzB,EAA6B;AAAA;;AAAA;AACzB;AACA,gBAAIpD,cAAcoD,IAAlB;AACA;AACA,kBAAM/C,WAAW,OAAKgD,YAAL,CAAkBD,IAAlB,CAAjB;AACA,gBAAI1D,MAAMa,OAAN,CAAcF,QAAd,CAAJ,EAA6B;AACzB,uBAAOL,WAAP;AACH;AACD;AACA,kBAAMQ,cAAc;AAChBC,wBAAQ,MADQ;AAEhBC,qBAAKhB,MAAMiB,MAAN,CAAa,yBAAb,CAFW;AAGhBC,yBAAS;AACL,oCAAgB;AADX,iBAHO;AAMhBC,sBAAMR;AANU,aAApB;AAQA;AACA,gBAAI;AACA,sBAAMS,gBAAgB,MAAMzB,GAAGmB,WAAH,CAA5B;AACA,oBAAId,MAAMa,OAAN,CAAcO,aAAd,CAAJ,EAAkC;AAC9B,2BAAOd,WAAP;AACH;AACDA,8BAAc,OAAKsD,sBAAL,CAA4BxC,aAA5B,CAAd;AACA,oBAAIyC,WAAWvD,YAAYwD,aAA3B;AACA,oBAAIC,OAAOF,SAASrB,QAAT,EAAX;AACA,uBAAOlC,WAAP;AACH,aATD,CASE,OAAOgB,GAAP,EAAY;AACV,uBAAOhB,WAAP;AACH;AA7BwB;AA8B5B;AACD;AACAqD,iBAAaD,IAAb,EAAmB;AACf,cAAMnC,cAAcY,KAAKC,SAAL,CAAesB,IAAf,CAApB,CADe,CAC2B;AAC1C,cAAM/C,WAAW;AACbc,yBAAaC,UAAUH,WAAV,CADA;AAEbI,yBAAa3B,MAAMiB,MAAN,CAAa,mBAAb,CAFA;AAGbW,yBAAa,MAHA;AAIbC,sBAAU,KAAKmC,YAAL,CAAkBzC,WAAlB,CAJG;AAKbQ,sBAAU;AALG,SAAjB;AAOA;AACA,eAAOpB,QAAP;AACH;AACD;AACAqD,iBAAazC,WAAb,EAA0B;AACtB,eAAOG,UAAUW,OAAOC,IAAP,CAAYtC,MAAMuC,GAAN,CAAUhB,cAAcvB,MAAMiB,MAAN,CAAa,oBAAb,CAAxB,CAAZ,EAAyEuB,QAAzE,CAAkF,QAAlF,CAAV,CAAP;AACH;AACD;AACAoB,2BAAuBxC,aAAvB,EAAsC;AAClC,cAAMd,cAAc;AAChBC,qBAAS,KADO;AAEhBJ,yBAAa,EAFG;AAGhBK,yBAAa,EAHG;AAIhBJ,0BAAc,EAJE;AAKhBK,sBAAU,CALM;AAMhBC,oBAAQ;AANQ,SAApB;AAQA,YAAIV,MAAMa,OAAN,CAAcO,aAAd,CAAJ,EAAkC;AAC9B,mBAAOd,WAAP;AACH;AACD,YAAI;AACA,gBAAIT,EAAE4C,QAAF,CAAWrB,aAAX,CAAJ,EAA+B;AAC3BA,gCAAgBe,KAAKO,KAAL,CAAWtB,aAAX,CAAhB;AACH;AACD,mBAAOA,aAAP;AACH,SALD,CAKE,OAAOE,GAAP,EAAY;AACV,mBAAOhB,WAAP;AACH;AACD,eAAOA,WAAP;AACH;AACD;AACA;AACA;AACA;;;AAGM2D,aAAN,CAAgBP,OAAO,EAAvB,EAA2B;AAAA;;AAAA;AACvB,gBAAInC,cAAcmC,IAAlB;AACAnC,0BAAc,6DAAd;AACA;AACA;AACA,gBAAI2C,oBAAoBxC,UAAUH,WAAV,CAAxB;AACA,gBAAI4C,SAASnE,MAAMiB,MAAN,CAAa,oBAAb,CAAb;AACA,gBAAImD,UAAUpE,MAAMiB,MAAN,CAAa,uBAAb,CAAd;AACA,gBAAIoD,WAAW,OAAKC,aAAL,CAAmB,OAAKC,MAAL,EAAnB,EAAkCL,iBAAlC,CAAf;AACA;AACA,gBAAIM,aAAa,GAAjB;AACA,gBAAI7C,cAAc3B,MAAMiB,MAAN,CAAa,mBAAb,CAAlB;AACA;AACAwD,oBAAQC,GAAR,CAAY,gBAAZ;AACA,gBAAIvD,OAAO,4CAA4CiD,OAA5C,GAAsD,iDAAtD,GAA0G7C,WAA1G,GAAwH,kDAAxH,GAA6KI,WAA7K,GAA2L,+CAA3L,GAA6O0C,QAA7O,GAAwP,gDAAxP,GAA2SG,UAA3S,GAAwT,4CAAnU;AACAC,oBAAQC,GAAR,CAAYvD,IAAZ;AACA,mBAAOA,IAAP;AAhBuB;AAiB1B;AACD;AACAmD,kBAAcK,EAAd,EAAkBpD,WAAlB,EAA+B;AAC3B,eAAOG,UAAUW,OAAOC,IAAP,CAAYqC,KAAK3E,MAAMuC,GAAN,CAAUhB,cAAcvB,MAAMiB,MAAN,CAAa,oBAAb,CAAxB,CAAjB,EAA8EuB,QAA9E,CAAuF,QAAvF,CAAV,CAAP;AACH;AACD;;;;;AAKA;AACA;AACA;AACA;;;;AAIM+B,UAAN,GAAe;AAAA;AACX,kBAAMzD,cAAc;AAChBC,wBAAQ,KADQ;AAEhBC,qBAAKhB,MAAMiB,MAAN,CAAa,2BAAb,CAFW;AAGhBC,yBAAS;AACL,oCAAgB;AADX;AAHO,aAApB;AAOA;AACA,gBAAI;AACA,sBAAME,gBAAgB,MAAMzB,GAAGmB,WAAH,CAA5B;AACA,oBAAId,MAAMa,OAAN,CAAcO,aAAd,CAAJ,EAAkC;AAC9B,wBAAIwD,IAAI,CAAR;AACA,2BAAOA,CAAP;AACH;AACD,oBAAID,KAAK,oCAAT;AACA,oBAAIE,OAAOF,GAAGG,IAAH,CAAQ1D,aAAR,CAAX;AACAqD,wBAAQC,GAAR,CAAYG,KAAK,CAAL,CAAZ;AACA,uBAAOA,KAAK,CAAL,CAAP;AACH,aAVD,CAUE,OAAOvD,GAAP,EAAY;AACV,uBAAO,CAAP;AACH;AArBU;AAsBd;AACD;AA9OyC,CAA7C", "file": "..\\..\\..\\src\\admin\\service\\express.js", "sourcesContent": ["const rp = require('request-promise');\nconst _ = require('lodash');\nmodule.exports = class extends think.Service {\n    async queryExpress(shipperCode, logisticCode, orderCode = '') {\n        // 最终得到的数据，初始化\n        let expressInfo = {\n            success: false,\n            shipperCode: shipperCode,\n            shipperName: '',\n            logisticCode: logisticCode,\n            isFinish: 0,\n            traces: []\n        };\n        // 要post的数据，进行编码，签名\n        const fromData = this.generateFromData(shipperCode, logisticCode, orderCode);\n        if (think.isEmpty(fromData)) {\n            return expressInfo;\n        }\n        // post的参数\n        const sendOptions = {\n            method: 'POST',\n            url: think.config('express.request_url'),\n            headers: {\n                'content-type': 'application/x-www-form-urlencoded;charset=utf-8'\n            },\n            form: fromData\n        };\n        // post请求\n        try {\n            const requestResult = await rp(sendOptions);\n            if (think.isEmpty(requestResult)) {\n                return expressInfo;\n            }\n            expressInfo = this.parseExpressResult(requestResult);\n            expressInfo.shipperCode = shipperCode;\n            expressInfo.logisticCode = logisticCode;\n            return expressInfo;\n        } catch (err) {\n            return expressInfo;\n        }\n    }\n    // 快递物流信息请求系统级参数 要post的数据，进行编码，签名\n    generateFromData(shipperCode, logisticCode, orderCode) {\n        const requestData = this.generateRequestData(shipperCode, logisticCode, orderCode);\n        const fromData = {\n            RequestData: encodeURI(requestData), // 把字符串作为 URI 进行编码\n            EBusinessID: think.config('express.appid'), // 客户号\n            RequestType: '1002', // 请求代码\n            DataSign: this.generateDataSign(requestData), // 签名\n            DataType: '2' //数据类型：2\n        };\n        return fromData;\n    }\n    // JavaScript 值转换为 JSON 字符串。\n    generateRequestData(shipperCode, logisticCode, orderCode = '') {\n        // 参数验证\n        const requestData = {\n            OrderCode: orderCode,\n            ShipperCode: shipperCode,\n            LogisticCode: logisticCode\n        };\n        return JSON.stringify(requestData);\n    }\n    // 编码加密\n    generateDataSign(requestData) {\n        return encodeURI(Buffer.from(think.md5(requestData + think.config('express.appkey'))).toString('base64'));\n    }\n    parseExpressResult(requestResult) {\n        const expressInfo = {\n            success: false,\n            shipperCode: '',\n            shipperName: '',\n            logisticCode: '',\n            isFinish: 0,\n            traces: []\n        };\n        if (think.isEmpty(requestResult)) {\n            return expressInfo;\n        }\n        try {\n            if (_.isString(requestResult)) {\n                requestResult = JSON.parse(requestResult); // 将一个 JSON 字符串转换为对象。\n            }\n        } catch (err) {\n            return expressInfo;\n        }\n        if (think.isEmpty(requestResult.Success)) {\n            return expressInfo;\n        }\n        // 判断是否已签收\n        if (Number.parseInt(requestResult.State) === 3) {\n            expressInfo.isFinish = 1;\n        }\n        expressInfo.success = true;\n        if (!think.isEmpty(requestResult.Traces) && Array.isArray(requestResult.Traces)) {\n            expressInfo.traces = _.map(requestResult.Traces, item => {\n                return {\n                    datetime: item.AcceptTime,\n                    content: item.AcceptStation\n                };\n            });\n            _.reverse(expressInfo.traces);\n        }\n        return expressInfo;\n    }\n    // 电子面单开始\n    async mianExpress(data = {}) {\n        // 从前台传过来的数据\n        let expressInfo = data;\n        // 进行编码，签名\n        const fromData = this.mianFromData(data);\n        if (think.isEmpty(fromData)) {\n            return expressInfo;\n        }\n        // 请求的参数设置\n        const sendOptions = {\n            method: 'POST',\n            url: think.config('mianexpress.request_url'),\n            headers: {\n                'content-type': 'application/x-www-form-urlencoded;charset=utf-8'\n            },\n            form: fromData\n        };\n        // post请求\n        try {\n            const requestResult = await rp(sendOptions);\n            if (think.isEmpty(requestResult)) {\n                return expressInfo;\n            }\n            expressInfo = this.parseMianExpressResult(requestResult);\n            let htmldata = expressInfo.PrintTemplate;\n            let html = htmldata.toString();\n            return expressInfo;\n        } catch (err) {\n            return expressInfo;\n        }\n    }\n    // 电子面单信息请求系统级参数 要post的数据 进行编码，签名\n    mianFromData(data) {\n        const requestData = JSON.stringify(data); // data：post进来的 // JavaScript 值转换为 JSON 字符串。\n        const fromData = {\n            RequestData: encodeURI(requestData),\n            EBusinessID: think.config('mianexpress.appid'),\n            RequestType: '1007',\n            DataSign: this.mianDataSign(requestData),\n            DataType: '2'\n        };\n        // console.log('fromdata======');\n        return fromData;\n    }\n    // 加密签名\n    mianDataSign(requestData) {\n        return encodeURI(Buffer.from(think.md5(requestData + think.config('mianexpress.appkey'))).toString('base64'));\n    }\n    // 返回数据\n    parseMianExpressResult(requestResult) {\n        const expressInfo = {\n            success: false,\n            shipperCode: '',\n            shipperName: '',\n            logisticCode: '',\n            isFinish: 0,\n            traces: []\n        };\n        if (think.isEmpty(requestResult)) {\n            return expressInfo;\n        }\n        try {\n            if (_.isString(requestResult)) {\n                requestResult = JSON.parse(requestResult);\n            }\n            return requestResult;\n        } catch (err) {\n            return expressInfo;\n        }\n        return expressInfo;\n    }\n    // 电子面单结束\n    // 批量打印开始\n    // build_form();\n    /**\n     * 组装POST表单用于调用快递鸟批量打印接口页面\n     */\n    async buildForm(data = {}) {\n        let requestData = data;\n        requestData = '[{\"OrderCode\":\"234351215333113311353\",\"PortName\":\"打印机名称一\"}]';\n        //OrderCode:需要打印的订单号，和调用快递鸟电子面单的订单号一致，PortName：本地打印机名称，请参考使用手册设置打印机名称。支持多打印机同时打印。\n        // $request_data = '[{\"OrderCode\":\"234351215333113311353\",\"PortName\":\"打印机名称一\"},{\"OrderCode\":\"234351215333113311354\",\"PortName\":\"打印机名称二\"}]';\n        let requestDataEncode = encodeURI(requestData);\n        let APIKey = think.config('mianexpress.appkey');\n        let API_URL = think.config('mianexpress.print_url');\n        let dataSign = this.printDataSign(this.get_ip(), requestDataEncode);\n        //是否预览，0-不预览 1-预览\n        let is_priview = '0';\n        let EBusinessID = think.config('mianexpress.appid');\n        //组装表单\n        console.log('hahaaaaaaaaaa ');\n        let form = '<form id=\"form1\" method=\"POST\" action=\"' + API_URL + '\"><input type=\"text\" name=\"RequestData\" value=\"' + requestData + '\"/><input type=\"text\" name=\"EBusinessID\" value=\"' + EBusinessID + '\"/><input type=\"text\" name=\"DataSign\" value=\"' + dataSign + '\"/><input type=\"text\" name=\"IsPriview\" value=\"' + is_priview + '\"/></form><script>form1.submit();</script>';\n        console.log(form);\n        return form;\n    }\n    // 加密签名\n    printDataSign(ip, requestData) {\n        return encodeURI(Buffer.from(ip + think.md5(requestData + think.config('mianexpress.appkey'))).toString('base64'));\n    }\n    /**\n     * 判断是否为内网IP\n     * @param ip IP\n     * @return 是否内网IP\n     */\n    // function is_private_ip($ip) {\n    //     return !filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE);\n    // }\n    /**\n     * 获取客户端IP(非用户服务器IP)\n     * @return 客户端IP\n     */\n    async get_ip() {\n        const sendOptions = {\n            method: 'GET',\n            url: think.config('mianexpress.ip_server_url'),\n            headers: {\n                'content-type': 'application/x-www-form-urlencoded;charset=utf-8'\n            }\n        };\n        // post请求\n        try {\n            const requestResult = await rp(sendOptions);\n            if (think.isEmpty(requestResult)) {\n                let i = 0\n                return i;\n            }\n            var ip = /\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}/;\n            var text = ip.exec(requestResult);\n            console.log(text[0]);\n            return text[0];\n        } catch (err) {\n            return 0;\n        }\n    }\n    // 批量打印结束\n};"]}