/* 佣金页面样式 */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 20rpx;
}

/* 佣金概览 */
.commission-overview {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
  color: white;
}

.overview-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  backdrop-filter: blur(10rpx);
}

.total-commission {
  text-align: center;
  margin-bottom: 40rpx;
}

.total-commission .amount {
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.total-commission .label {
  font-size: 28rpx;
  opacity: 0.8;
}

.commission-details {
  display: flex;
  justify-content: space-around;
}

.detail-item {
  text-align: center;
  flex: 1;
}

.detail-amount {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.detail-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 提现按钮区域 */
.withdraw-section {
  margin-top: 40rpx;
  text-align: center;
}

.withdraw-btn {
  background: #ff6b6b;
  color: white;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 32rpx;
  border: none;
  margin-bottom: 20rpx;
}

.withdraw-btn[disabled] {
  background: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.6);
}

.withdraw-tip {
  font-size: 24rpx;
  opacity: 0.7;
}

/* 佣金明细区域 */
.commission-details-section {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-filter {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 28rpx;
}

.filter-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 10rpx;
}

.section-more {
  color: #666;
  font-size: 28rpx;
}

/* 明细列表 */
.details-list .detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.details-list .detail-item:last-child {
  border-bottom: none;
}

.item-left {
  flex: 1;
}

.item-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.item-time {
  font-size: 24rpx;
  color: #999;
}

.item-right {
  text-align: right;
}

.item-amount {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.item-amount.positive {
  color: #ff6b6b;
}

.item-amount.negative {
  color: #999;
}

.item-status {
  font-size: 24rpx;
  color: #666;
}

/* 提现记录区域 */
.withdraw-records-section {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
}

.records-list .record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.records-list .record-item:last-child {
  border-bottom: none;
}

.record-left {
  flex: 1;
}

.record-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-right {
  text-align: right;
}

.record-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.record-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.record-status.pending {
  background: #fff3cd;
  color: #856404;
}

.record-status.processing {
  background: #d1ecf1;
  color: #0c5460;
}

.record-status.success {
  background: #d4edda;
  color: #155724;
}

.record-status.failed {
  background: #f8d7da;
  color: #721c24;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 提现弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.withdraw-modal {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.available-amount {
  text-align: center;
  margin-bottom: 40rpx;
  font-size: 28rpx;
  color: #666;
}

.available-amount .amount {
  color: #ff6b6b;
  font-weight: bold;
}

.withdraw-form {
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.form-input {
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 30rpx;
}

.method-options {
  display: flex;
  gap: 20rpx;
}

.method-option {
  flex: 1;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.method-option.active {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.method-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 10rpx;
}

.withdraw-tips {
  background: #f8f9fa;
  border-radius: 10rpx;
  padding: 20rpx;
}

.tip-item {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  padding: 30rpx;
  font-size: 30rpx;
  border: none;
  border-radius: 0;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
}

.confirm-btn {
  background: #667eea;
  color: white;
}
