-- 创建用户佣金账户表
CREATE TABLE IF NOT EXISTS `hiolabs_user_commission` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `total_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总佣金',
  `available_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '可用佣金',
  `frozen_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '冻结佣金',
  `withdrawn_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '已提现佣金',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `available_commission` (`available_commission`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户佣金账户表';

-- 创建佣金变动日志表
CREATE TABLE IF NOT EXISTS `hiolabs_commission_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `commission_change` decimal(10,2) NOT NULL COMMENT '佣金变动金额（正数为增加，负数为减少）',
  `commission_type` varchar(50) NOT NULL COMMENT '佣金类型：promotion推广佣金,bonus奖励佣金,withdraw提现,admin管理员操作',
  `source_id` int(11) DEFAULT NULL COMMENT '来源ID（订单ID、提现ID等）',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `balance_after` decimal(10,2) NOT NULL COMMENT '变动后余额',
  `status` enum('pending','completed','failed') DEFAULT 'completed' COMMENT '状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `commission_type` (`commission_type`),
  KEY `source_id` (`source_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='佣金变动日志表';

-- 更新推广订单表，添加佣金字段
ALTER TABLE `hiolabs_promotion_orders` 
ADD COLUMN `personal_commission` decimal(10,2) DEFAULT '0.00' COMMENT '个人佣金' AFTER `commission_amount`,
ADD COLUMN `level1_commission` decimal(10,2) DEFAULT '0.00' COMMENT '一级佣金' AFTER `personal_commission`,
ADD COLUMN `level2_commission` decimal(10,2) DEFAULT '0.00' COMMENT '二级佣金' AFTER `level1_commission`,
ADD COLUMN `team_leader_commission` decimal(10,2) DEFAULT '0.00' COMMENT '团长佣金' AFTER `level2_commission`;

-- 更新个人推广员表，添加佣金统计字段
ALTER TABLE `hiolabs_personal_promoters` 
ADD COLUMN `total_commission` decimal(10,2) DEFAULT '0.00' COMMENT '总佣金收入' AFTER `total_orders`,
ADD COLUMN `order_count` int(11) DEFAULT '0' COMMENT '订单数量' AFTER `total_commission`;

-- 更新商品分销配置表，确保包含所有佣金比例字段
ALTER TABLE `hiolabs_goods_distribution` 
ADD COLUMN `personal_rate` decimal(5,2) DEFAULT '0.00' COMMENT '个人佣金比例' AFTER `commission_rate`,
ADD COLUMN `level1_rate` decimal(5,2) DEFAULT '0.00' COMMENT '一级分销佣金比例' AFTER `personal_rate`,
ADD COLUMN `level2_rate` decimal(5,2) DEFAULT '0.00' COMMENT '二级分销佣金比例' AFTER `level1_rate`,
ADD COLUMN `team_leader_rate` decimal(5,2) DEFAULT '0.00' COMMENT '团长佣金比例' AFTER `level2_rate`,
ADD COLUMN `min_level_required` int(11) DEFAULT '1' COMMENT '最低等级要求' AFTER `team_leader_rate`;
