{"version": 3, "sources": ["..\\..\\..\\src\\common\\model\\flash_sales.js"], "names": ["module", "exports", "think", "Model", "tableName", "getActiveFlashSales", "now", "Date", "where", "status", "start_time", "end_time", "select", "getUpcomingFlashSales", "order", "getEndedFlashSales", "updateStatus", "update", "decreaseStock", "flashSaleId", "quantity", "id", "stock", "increment", "decrement", "checkStock", "flashSale", "find", "isEmpty"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;;AAEzC;;;AAGA,MAAIC,SAAJ,GAAgB;AACd,WAAO,qBAAP;AACD;;AAED;;;AAGMC,qBAAN,GAA4B;AAAA;;AAAA;AAC1B,YAAMC,MAAM,IAAIC,IAAJ,EAAZ;AACA,aAAO,MAAM,MAAKC,KAAL,CAAW;AACtBC,gBAAQ,QADc;AAEtBC,oBAAY,CAAC,IAAD,EAAOJ,GAAP,CAFU;AAGtBK,kBAAU,CAAC,IAAD,EAAOL,GAAP;AAHY,OAAX,EAIVM,MAJU,EAAb;AAF0B;AAO3B;;AAED;;;AAGMC,uBAAN,GAA8B;AAAA;;AAAA;AAC5B,YAAMP,MAAM,IAAIC,IAAJ,EAAZ;AACA,aAAO,MAAM,OAAKC,KAAL,CAAW;AACtBC,gBAAQ,UADc;AAEtBC,oBAAY,CAAC,GAAD,EAAMJ,GAAN;AAFU,OAAX,EAGVQ,KAHU,CAGJ,gBAHI,EAGcF,MAHd,EAAb;AAF4B;AAM7B;;AAED;;;AAGMG,oBAAN,GAA2B;AAAA;;AAAA;AACzB,YAAMT,MAAM,IAAIC,IAAJ,EAAZ;AACA,aAAO,MAAM,OAAKC,KAAL,CAAW;AACtBG,kBAAU,CAAC,GAAD,EAAML,GAAN;AADY,OAAX,EAEVM,MAFU,EAAb;AAFyB;AAK1B;;AAED;;;AAGMI,cAAN,GAAqB;AAAA;;AAAA;AACnB,YAAMV,MAAM,IAAIC,IAAJ,EAAZ;;AAEA;AACA,YAAM,OAAKC,KAAL,CAAW;AACfC,gBAAQ,CAAC,IAAD,EAAO,CAAC,UAAD,EAAa,QAAb,CAAP,CADO;AAEfE,kBAAU,CAAC,GAAD,EAAML,GAAN;AAFK,OAAX,EAGHW,MAHG,CAGI;AACRR,gBAAQ;AADA,OAHJ,CAAN;;AAOA;AACA,YAAM,OAAKD,KAAL,CAAW;AACfC,gBAAQ,UADO;AAEfC,oBAAY,CAAC,IAAD,EAAOJ,GAAP,CAFG;AAGfK,kBAAU,CAAC,IAAD,EAAOL,GAAP;AAHK,OAAX,EAIHW,MAJG,CAII;AACRR,gBAAQ;AADA,OAJJ,CAAN;AAZmB;AAmBpB;;AAED;;;AAGMS,eAAN,CAAoBC,WAApB,EAAiCC,QAAjC,EAA2C;AAAA;;AAAA;AACzC,aAAO,MAAM,OAAKZ,KAAL,CAAW;AACtBa,YAAIF,WADkB;AAEtBG,eAAO,CAAC,IAAD,EAAOF,QAAP;AAFe,OAAX,EAGVG,SAHU,CAGA,YAHA,EAGcH,QAHd,EAGwBI,SAHxB,CAGkC,OAHlC,EAG2CJ,QAH3C,CAAb;AADyC;AAK1C;;AAED;;;AAGMK,YAAN,CAAiBN,WAAjB,EAA8BC,QAA9B,EAAwC;AAAA;;AAAA;AACtC,YAAMM,YAAY,MAAM,OAAKlB,KAAL,CAAW;AACjCa,YAAIF;AAD6B,OAAX,EAErBQ,IAFqB,EAAxB;;AAIA,UAAIzB,MAAM0B,OAAN,CAAcF,SAAd,CAAJ,EAA8B;AAC5B,eAAO,KAAP;AACD;;AAED,aAAOA,UAAUJ,KAAV,IAAmBF,QAA1B;AATsC;AAUvC;AAzFwC,CAA3C", "file": "..\\..\\..\\src\\common\\model\\flash_sales.js", "sourcesContent": ["module.exports = class extends think.Model {\n  \n  /**\n   * 获取表名\n   */\n  get tableName() {\n    return 'hiolabs_flash_sales';\n  }\n  \n  /**\n   * 获取活动状态的秒杀商品\n   */\n  async getActiveFlashSales() {\n    const now = new Date();\n    return await this.where({\n      status: 'active',\n      start_time: ['<=', now],\n      end_time: ['>=', now]\n    }).select();\n  }\n  \n  /**\n   * 获取即将开始的秒杀商品\n   */\n  async getUpcomingFlashSales() {\n    const now = new Date();\n    return await this.where({\n      status: 'upcoming',\n      start_time: ['>', now]\n    }).order('start_time ASC').select();\n  }\n  \n  /**\n   * 获取已结束的秒杀商品\n   */\n  async getEndedFlashSales() {\n    const now = new Date();\n    return await this.where({\n      end_time: ['<', now]\n    }).select();\n  }\n  \n  /**\n   * 更新秒杀状态\n   */\n  async updateStatus() {\n    const now = new Date();\n    \n    // 将已过期的活动标记为已结束\n    await this.where({\n      status: ['IN', ['upcoming', 'active']],\n      end_time: ['<', now]\n    }).update({\n      status: 'ended'\n    });\n    \n    // 将到时间的活动标记为进行中\n    await this.where({\n      status: 'upcoming',\n      start_time: ['<=', now],\n      end_time: ['>=', now]\n    }).update({\n      status: 'active'\n    });\n  }\n  \n  /**\n   * 减少库存\n   */\n  async decreaseStock(flashSaleId, quantity) {\n    return await this.where({\n      id: flashSaleId,\n      stock: ['>=', quantity]\n    }).increment('sold_count', quantity).decrement('stock', quantity);\n  }\n  \n  /**\n   * 检查库存是否充足\n   */\n  async checkStock(flashSaleId, quantity) {\n    const flashSale = await this.where({\n      id: flashSaleId\n    }).find();\n    \n    if (think.isEmpty(flashSale)) {\n      return false;\n    }\n    \n    return flashSale.stock >= quantity;\n  }\n};\n"]}