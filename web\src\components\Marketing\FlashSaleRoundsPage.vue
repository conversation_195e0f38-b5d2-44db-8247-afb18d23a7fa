<template>
  <div class="flash-sale-rounds-page">
    <!-- 页面头部 -->
    <div class="page-header bg-white shadow-sm border-b">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">轮次秒杀</h1>
            <p class="text-sm text-gray-500 mt-1">每5分钟一轮，间隔2分钟，可选择不同商品参与秒杀</p>
          </div>
          <div class="flex items-center space-x-3">
            <button @click="showAddModal = true" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <i class="ri-add-line mr-2"></i>
              创建轮次
            </button>
            <button @click="showConfigModal = true" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
              <i class="ri-settings-line mr-2"></i>
              系统配置
            </button>
            <button @click="refreshData" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <i class="ri-refresh-line mr-2"></i>
              刷新数据
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="p-6 max-w-7xl mx-auto">
      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <i class="ri-flashlight-line text-xl text-red-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">总轮次数</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.totalRounds }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="ri-play-circle-line text-xl text-green-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">进行中轮次</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.activeRounds }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="ri-shopping-cart-line text-xl text-blue-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">秒杀订单数</p>
              <p class="text-2xl font-bold text-gray-900">{{ statistics.totalOrders }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center">
            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <i class="ri-money-dollar-circle-line text-xl text-yellow-600"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm text-gray-500">秒杀销售额</p>
              <p class="text-2xl font-bold text-gray-900">¥{{ statistics.totalSales }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 当前轮次状态 -->
      <div class="bg-white rounded-lg shadow-sm border mb-6">
        <div class="px-6 py-4 border-b">
          <h2 class="text-lg font-semibold text-gray-900">当前轮次状态</h2>
        </div>
        <div class="p-6">
          <div v-if="currentRounds.current.length > 0" class="mb-6">
            <h3 class="text-md font-medium text-gray-900 mb-4">🔥 进行中轮次</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div v-for="round in currentRounds.current" :key="round.id" 
                   class="bg-red-50 border-2 border-red-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                  <span class="text-sm font-medium text-red-600">第{{ round.round_number }}轮</span>
                  <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">进行中</span>
                </div>
                <div class="mb-3">
                  <h4 class="font-medium text-gray-900">{{ round.goods_name }}</h4>
                  <div class="flex items-center space-x-2 mt-1">
                    <span class="text-lg font-bold text-red-600">¥{{ round.flash_price }}</span>
                    <span class="text-sm text-gray-500 line-through">¥{{ round.original_price }}</span>
                  </div>
                </div>
                <div class="mb-3">
                  <div class="flex justify-between text-sm text-gray-600 mb-1">
                    <span>库存进度</span>
                    <span>{{ round.sold_count }}/{{ round.stock }}</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-red-600 h-2 rounded-full" :style="{width: round.stockProgress + '%'}"></div>
                  </div>
                </div>
                <div class="text-center">
                  <div class="text-lg font-bold text-red-600">{{ formatCountdown(round.countdown) }}</div>
                  <div class="text-xs text-gray-500">剩余时间</div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="currentRounds.upcoming.length > 0">
            <h3 class="text-md font-medium text-gray-900 mb-4">⏰ 即将开始</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div v-for="round in currentRounds.upcoming" :key="round.id" 
                   class="bg-orange-50 border-2 border-orange-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                  <span class="text-sm font-medium text-orange-600">第{{ round.round_number }}轮</span>
                  <span class="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">即将开始</span>
                </div>
                <div class="mb-3">
                  <h4 class="font-medium text-gray-900">{{ round.goods_name }}</h4>
                  <div class="flex items-center space-x-2 mt-1">
                    <span class="text-lg font-bold text-orange-600">¥{{ round.flash_price }}</span>
                    <span class="text-sm text-gray-500 line-through">¥{{ round.original_price }}</span>
                  </div>
                </div>
                <div class="mb-3">
                  <div class="text-sm text-gray-600">
                    库存：{{ round.stock }}件 | 限购：{{ round.limit_quantity }}件
                  </div>
                </div>
                <div class="text-center">
                  <div class="text-lg font-bold text-orange-600">{{ formatCountdown(round.countdown) }}</div>
                  <div class="text-xs text-gray-500">开始倒计时</div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="currentRounds.current.length === 0 && currentRounds.upcoming.length === 0" 
               class="text-center py-8 text-gray-500">
            <i class="ri-time-line text-4xl mb-2"></i>
            <p>暂无进行中或即将开始的轮次</p>
          </div>
        </div>
      </div>

      <!-- 轮次列表 -->
      <div class="bg-white rounded-lg shadow-sm border">
        <div class="px-6 py-4 border-b">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900">轮次列表</h2>
            <div class="flex items-center space-x-2">
              <select v-model="filterStatus" @change="loadRoundsList" 
                      class="px-3 py-1 border border-gray-300 rounded-md text-sm">
                <option value="">全部状态</option>
                <option value="upcoming">即将开始</option>
                <option value="active">进行中</option>
                <option value="ended">已结束</option>
              </select>
            </div>
          </div>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">轮次</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">商品信息</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">价格</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">库存</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">倒计时</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="round in roundsList.data" :key="round.id">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">第{{ round.round_number }}轮</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <img v-if="round.goods_image" :src="round.goods_image" 
                         class="h-10 w-10 rounded-lg object-cover mr-3" :alt="round.goods_name">
                    <div class="w-10 h-10 bg-gray-200 rounded-lg mr-3 flex items-center justify-center" v-else>
                      <i class="ri-image-line text-gray-400"></i>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ round.goods_name }}</div>
                      <div class="text-sm text-gray-500">ID: {{ round.goods_id }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-red-600">¥{{ round.flash_price }}</div>
                  <div class="text-sm text-gray-500 line-through">¥{{ round.original_price }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ round.sold_count }}/{{ round.stock }}</div>
                  <div class="w-16 bg-gray-200 rounded-full h-1 mt-1">
                    <div class="bg-blue-600 h-1 rounded-full" :style="{width: round.stockProgress + '%'}"></div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div>{{ formatDateTime(round.start_time) }}</div>
                  <div>{{ formatDateTime(round.end_time) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getStatusClass(round.status)" 
                        class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full">
                    {{ getStatusText(round.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div v-if="round.countdown > 0" class="font-medium">
                    {{ formatCountdown(round.countdown) }}
                  </div>
                  <div v-else class="text-gray-400">-</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- 分页 -->
        <div v-if="roundsList.total > 0" class="px-6 py-4 border-t">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500">
              共 {{ roundsList.total }} 条记录，第 {{ roundsList.current_page }} / {{ roundsList.last_page }} 页
            </div>
            <div class="flex space-x-2">
              <button @click="changePage(roundsList.current_page - 1)" 
                      :disabled="roundsList.current_page <= 1"
                      class="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50">
                上一页
              </button>
              <button @click="changePage(roundsList.current_page + 1)" 
                      :disabled="roundsList.current_page >= roundsList.last_page"
                      class="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50">
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建轮次模态框 -->
    <div v-if="showAddModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">创建秒杀轮次</h3>
          <button @click="showAddModal = false" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>

        <form @submit.prevent="createRound" class="space-y-4">
          <!-- 商品选择 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">选择商品</label>
            <select v-model="newRound.goods_id" @change="onGoodsSelect"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required>
              <option value="">请选择商品</option>
              <option v-for="goods in goodsList" :key="goods.id" :value="goods.id">
                {{ goods.name }} - ¥{{ goods.retail_price }}
              </option>
            </select>
          </div>

          <!-- 商品预览 -->
          <div v-if="newRound.goods_id" class="p-4 bg-gray-50 rounded-lg">
            <div class="flex items-center space-x-4">
              <img v-if="newRound.goods_image" :src="newRound.goods_image"
                   class="w-16 h-16 object-cover rounded-lg" alt="商品图片">
              <div>
                <div class="font-medium text-gray-900">{{ newRound.goods_name }}</div>
                <div class="text-sm text-gray-500">原价: ¥{{ newRound.original_price }}</div>
              </div>
            </div>
          </div>

          <!-- 价格设置 -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">秒杀价格</label>
              <input v-model="newRound.flash_price" type="number" step="0.01" min="0"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                     placeholder="请输入秒杀价格" required>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">库存数量</label>
              <input v-model="newRound.stock" type="number" min="1"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                     placeholder="请输入库存数量" required>
            </div>
          </div>

          <!-- 限购数量 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">限购数量</label>
            <input v-model="newRound.limit_quantity" type="number" min="1"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                   placeholder="每人限购数量">
          </div>

          <!-- 提示信息 -->
          <div class="p-4 bg-blue-50 rounded-lg">
            <div class="flex items-start">
              <i class="ri-information-line text-blue-500 mt-0.5 mr-2"></i>
              <div class="text-sm text-blue-700">
                <div class="font-medium mb-1">轮次规则说明：</div>
                <ul class="list-disc list-inside space-y-1">
                  <li>每轮秒杀持续5分钟</li>
                  <li>轮次间隔2分钟</li>
                  <li>系统将自动安排开始时间</li>
                  <li>创建后立即进入排队状态</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-end space-x-3 pt-4">
            <button type="button" @click="showAddModal = false"
                    class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
              取消
            </button>
            <button type="submit"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              创建轮次
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 系统配置模态框 -->
    <div v-if="showConfigModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-lg mx-4">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900">系统配置</h3>
          <button @click="showConfigModal = false" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>

        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">轮次时长（秒）</label>
            <input v-model="systemConfig.round_duration" type="number" min="60"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">间隔时长（秒）</label>
            <input v-model="systemConfig.break_duration" type="number" min="0"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">运营时间</label>
            <div class="grid grid-cols-2 gap-4">
              <input v-model="systemConfig.daily_start_time" type="time"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <input v-model="systemConfig.daily_end_time" type="time"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
          </div>

          <div class="flex items-center">
            <input v-model="systemConfig.is_enabled" type="checkbox" id="enabled"
                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <label for="enabled" class="ml-2 block text-sm text-gray-900">启用秒杀系统</label>
          </div>
        </div>

        <div class="flex justify-end space-x-3 pt-6">
          <button @click="showConfigModal = false"
                  class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
            取消
          </button>
          <button @click="saveConfig"
                  class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
            保存配置
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FlashSaleRoundsPage',
  data() {
    return {
      // 统计数据
      statistics: {
        totalRounds: 0,
        activeRounds: 0,
        upcomingRounds: 0,
        endedRounds: 0,
        totalOrders: 0,
        todayOrders: 0,
        totalSales: 0
      },

      // 当前轮次
      currentRounds: {
        current: [],
        upcoming: [],
        total: 0
      },

      // 轮次列表
      roundsList: {
        data: [],
        total: 0,
        current_page: 1,
        last_page: 1
      },

      // 筛选状态
      filterStatus: '',
      currentPage: 1,

      // 模态框状态
      showAddModal: false,
      showConfigModal: false,

      // 新轮次表单
      newRound: {
        goods_id: '',
        goods_name: '',
        goods_image: '',
        original_price: '',
        flash_price: '',
        stock: '',
        limit_quantity: 1
      },

      // 商品列表
      goodsList: [],

      // 系统配置
      systemConfig: {
        round_duration: 300,
        break_duration: 120,
        auto_start_next: true,
        daily_start_time: '09:00:00',
        daily_end_time: '22:00:00',
        is_enabled: true
      },

      // 定时器
      refreshTimer: null,
      countdownTimer: null
    }
  },

  mounted() {
    this.loadData();
    this.startAutoRefresh();
  },

  beforeDestroy() {
    this.stopAutoRefresh();
  },

  methods: {
    // 加载所有数据
    async loadData() {
      await Promise.all([
        this.loadStatistics(),
        this.loadCurrentRounds(),
        this.loadRoundsList(),
        this.loadGoodsList(),
        this.loadSystemConfig()
      ]);
    },

    // 加载统计数据
    async loadStatistics() {
      try {
        const response = await this.axios.get('flashsalerounds/statistics');
        if (response.data.errno === 0) {
          this.statistics = response.data.data;
        }
      } catch (error) {
        console.error('加载统计数据失败:', error);
      }
    },

    // 加载当前轮次
    async loadCurrentRounds() {
      try {
        const response = await this.axios.get('flashsalerounds/current');
        if (response.data.errno === 0) {
          this.currentRounds = response.data.data;
        }
      } catch (error) {
        console.error('加载当前轮次失败:', error);
      }
    },

    // 加载轮次列表
    async loadRoundsList() {
      try {
        const params = {
          page: this.currentPage,
          limit: 20
        };

        if (this.filterStatus) {
          params.status = this.filterStatus;
        }

        const response = await this.axios.get('flashsalerounds/list', { params });
        if (response.data.errno === 0) {
          this.roundsList = response.data.data;
        }
      } catch (error) {
        console.error('加载轮次列表失败:', error);
      }
    },

    // 加载商品列表
    async loadGoodsList() {
      try {
        const response = await this.axios.get('flashsalerounds/goods');
        if (response.data.errno === 0) {
          this.goodsList = response.data.data;
        }
      } catch (error) {
        console.error('加载商品列表失败:', error);
      }
    },

    // 加载系统配置
    async loadSystemConfig() {
      try {
        const response = await this.axios.get('flashsalerounds/config');
        if (response.data.errno === 0) {
          this.systemConfig = response.data.data;
        }
      } catch (error) {
        console.error('加载系统配置失败:', error);
      }
    },

    // 创建新轮次
    async createRound() {
      try {
        const response = await this.axios.post('flashsalerounds/create', this.newRound);
        if (response.data.errno === 0) {
          this.showAddModal = false;
          this.$message.success('轮次创建成功');
          this.loadData();
          this.resetNewRound();
        } else {
          this.$message.error(response.data.errmsg || '创建失败');
        }
      } catch (error) {
        console.error('创建轮次失败:', error);
        this.$message.error('创建失败');
      }
    },

    // 重置新轮次表单
    resetNewRound() {
      this.newRound = {
        goods_id: '',
        goods_name: '',
        goods_image: '',
        original_price: '',
        flash_price: '',
        stock: '',
        limit_quantity: 1
      };
    },

    // 选择商品
    onGoodsSelect() {
      const selectedGoods = this.goodsList.find(goods => goods.id == this.newRound.goods_id);
      if (selectedGoods) {
        this.newRound.goods_name = selectedGoods.name;
        this.newRound.goods_image = selectedGoods.list_pic_url;
        this.newRound.original_price = selectedGoods.retail_price;
      }
    },

    // 分页
    changePage(page) {
      if (page >= 1 && page <= this.roundsList.last_page) {
        this.currentPage = page;
        this.loadRoundsList();
      }
    },

    // 刷新数据
    refreshData() {
      this.loadData();
      this.$message.success('数据刷新成功');
    },

    // 保存系统配置
    async saveConfig() {
      try {
        const response = await this.axios.post('flashsalerounds/config', this.systemConfig);
        if (response.data.errno === 0) {
          this.showConfigModal = false;
          this.$message.success('配置保存成功');
        } else {
          this.$message.error(response.data.errmsg || '保存失败');
        }
      } catch (error) {
        console.error('保存配置失败:', error);
        this.$message.error('保存失败');
      }
    },

    // 开始自动刷新
    startAutoRefresh() {
      // 每10秒刷新一次当前轮次
      this.refreshTimer = setInterval(() => {
        this.loadCurrentRounds();
        this.loadStatistics();
      }, 10000);

      // 每秒更新倒计时
      this.countdownTimer = setInterval(() => {
        this.updateCountdowns();
      }, 1000);
    },

    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null;
      }
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
      }
    },

    // 更新倒计时
    updateCountdowns() {
      // 更新当前轮次倒计时
      this.currentRounds.current.forEach(round => {
        if (round.countdown > 0) {
          round.countdown--;
        }
      });

      this.currentRounds.upcoming.forEach(round => {
        if (round.countdown > 0) {
          round.countdown--;
        }
      });

      // 更新列表中的倒计时
      this.roundsList.data.forEach(round => {
        if (round.countdown > 0) {
          round.countdown--;
        }
      });
    },

    // 格式化倒计时
    formatCountdown(seconds) {
      if (seconds <= 0) return '00:00';

      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;

      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-';
      const date = new Date(dateTime);
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    // 获取状态样式
    getStatusClass(status) {
      const classes = {
        'upcoming': 'bg-orange-100 text-orange-800',
        'active': 'bg-red-100 text-red-800',
        'ended': 'bg-gray-100 text-gray-800',
        'cancelled': 'bg-gray-100 text-gray-800'
      };
      return classes[status] || 'bg-gray-100 text-gray-800';
    },

    // 获取状态文本
    getStatusText(status) {
      const texts = {
        'upcoming': '即将开始',
        'active': '进行中',
        'ended': '已结束',
        'cancelled': '已取消'
      };
      return texts[status] || '未知';
    }
  }
}
</script>

<style scoped>
.flash-sale-rounds-page {
  min-height: 100vh;
  background-color: #f9fafb;
}

.page-header {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 状态指示灯动画 */
.status-indicator {
  position: relative;
}

.status-indicator.active::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background-color: #ef4444;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.95);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }

  70% {
    transform: translate(-50%, -50%) scale(1);
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }

  100% {
    transform: translate(-50%, -50%) scale(0.95);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

/* 倒计时数字动画 */
.countdown-number {
  transition: all 0.3s ease;
}

.countdown-number.urgent {
  color: #ef4444;
  font-weight: bold;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* 库存进度条动画 */
.stock-progress {
  transition: width 0.5s ease;
}

/* 卡片悬停效果 */
.round-card {
  transition: all 0.3s ease;
}

.round-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}
</style>
