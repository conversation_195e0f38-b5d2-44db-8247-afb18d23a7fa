{"version": 3, "sources": ["..\\..\\..\\src\\common\\service\\flash_sale_multi_scheduler.js"], "names": ["module", "exports", "FlashSaleMultiScheduler", "updateRoundStatus", "model", "roundModel", "now", "Date", "nowStr", "toISOString", "slice", "replace", "console", "log", "upcomingToActive", "where", "start_time", "status", "select", "length", "update", "map", "r", "round_number", "round_name", "join", "activeToEnded", "end_time", "upcomingToEnded", "totalUpdated", "activated", "ended", "total", "error", "getActiveRoundsStats", "stats", "field", "group", "result", "upcoming", "active", "cancelled", "for<PERSON>ach", "stat", "count", "cleanupExpiredData", "daysT<PERSON><PERSON>eep", "roundGoodsModel", "cutoffDate", "setDate", "getDate", "cutoffStr", "expiredRounds", "roundIds", "id", "round_id", "delete"], "mappings": ";;AAAA;;;;;AAKAA,OAAOC,OAAP,GAAiB,MAAMC,uBAAN,CAA8B;;AAE7C;;;;AAIMC,mBAAN,CAAwBC,KAAxB,EAA+B;AAAA;AAC7B,UAAI;AACF,cAAMC,aAAaD,MAAM,mBAAN,CAAnB;AACA,cAAME,MAAM,IAAIC,IAAJ,EAAZ;AACA,cAAMC,SAASF,IAAIG,WAAJ,GAAkBC,KAAlB,CAAwB,CAAxB,EAA2B,EAA3B,EAA+BC,OAA/B,CAAuC,GAAvC,EAA4C,GAA5C,CAAf;;AAEAC,gBAAQC,GAAR,CAAa,IAAGL,MAAO,eAAvB;;AAEA;AACA,cAAMM,mBAAmB,MAAMT,WAAWU,KAAX,CAAiB;AAC9CC,sBAAY,CAAC,IAAD,EAAOR,MAAP,CADkC;AAE9CS,kBAAQ;AAFsC,SAAjB,EAG5BC,MAH4B,EAA/B;;AAKA,YAAIJ,iBAAiBK,MAAjB,GAA0B,CAA9B,EAAiC;AAC/B,gBAAMd,WAAWU,KAAX,CAAiB;AACrBC,wBAAY,CAAC,IAAD,EAAOR,MAAP,CADS;AAErBS,oBAAQ;AAFa,WAAjB,EAGHG,MAHG,CAGI,EAAEH,QAAQ,QAAV,EAHJ,CAAN;;AAKAL,kBAAQC,GAAR,CAAa,SAAQC,iBAAiBK,MAAO,OAA7C,EACEL,iBAAiBO,GAAjB,CAAqB;AAAA,mBAAM,KAAIC,EAAEC,YAAa,IAAGD,EAAEE,UAAW,GAAzC;AAAA,WAArB,EAAkEC,IAAlE,CAAuE,IAAvE,CADF;AAED;;AAED;AACA,cAAMC,gBAAgB,MAAMrB,WAAWU,KAAX,CAAiB;AAC3CY,oBAAU,CAAC,GAAD,EAAMnB,MAAN,CADiC;AAE3CS,kBAAQ;AAFmC,SAAjB,EAGzBC,MAHyB,EAA5B;;AAKA,YAAIQ,cAAcP,MAAd,GAAuB,CAA3B,EAA8B;AAC5B,gBAAMd,WAAWU,KAAX,CAAiB;AACrBY,sBAAU,CAAC,GAAD,EAAMnB,MAAN,CADW;AAErBS,oBAAQ;AAFa,WAAjB,EAGHG,MAHG,CAGI,EAAEH,QAAQ,OAAV,EAHJ,CAAN;;AAKAL,kBAAQC,GAAR,CAAa,SAAQa,cAAcP,MAAO,OAA1C,EACEO,cAAcL,GAAd,CAAkB;AAAA,mBAAM,KAAIC,EAAEC,YAAa,IAAGD,EAAEE,UAAW,GAAzC;AAAA,WAAlB,EAA+DC,IAA/D,CAAoE,IAApE,CADF;AAED;;AAED;AACA,cAAMG,kBAAkB,MAAMvB,WAAWU,KAAX,CAAiB;AAC7CY,oBAAU,CAAC,GAAD,EAAMnB,MAAN,CADmC;AAE7CS,kBAAQ;AAFqC,SAAjB,EAG3BC,MAH2B,EAA9B;;AAKA,YAAIU,gBAAgBT,MAAhB,GAAyB,CAA7B,EAAgC;AAC9B,gBAAMd,WAAWU,KAAX,CAAiB;AACrBY,sBAAU,CAAC,GAAD,EAAMnB,MAAN,CADW;AAErBS,oBAAQ;AAFa,WAAjB,EAGHG,MAHG,CAGI,EAAEH,QAAQ,OAAV,EAHJ,CAAN;;AAKAL,kBAAQC,GAAR,CAAa,YAAWe,gBAAgBT,MAAO,aAA/C,EACES,gBAAgBP,GAAhB,CAAoB;AAAA,mBAAM,KAAIC,EAAEC,YAAa,IAAGD,EAAEE,UAAW,GAAzC;AAAA,WAApB,EAAiEC,IAAjE,CAAsE,IAAtE,CADF;AAED;;AAED,cAAMI,eAAef,iBAAiBK,MAAjB,GAA0BO,cAAcP,MAAxC,GAAiDS,gBAAgBT,MAAtF;AACA,YAAIU,iBAAiB,CAArB,EAAwB;AACtBjB,kBAAQC,GAAR,CAAY,kBAAZ;AACD;;AAED,eAAO;AACLiB,qBAAWhB,iBAAiBK,MADvB;AAELY,iBAAOL,cAAcP,MAAd,GAAuBS,gBAAgBT,MAFzC;AAGLa,iBAAOH;AAHF,SAAP;AAMD,OAlED,CAkEE,OAAOI,KAAP,EAAc;AACdrB,gBAAQqB,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,cAAMA,KAAN;AACD;AAtE4B;AAuE9B;;AAED;;;;AAIMC,sBAAN,CAA2B9B,KAA3B,EAAkC;AAAA;AAChC,UAAI;AACF,cAAMC,aAAaD,MAAM,mBAAN,CAAnB;;AAEA,cAAM+B,QAAQ,MAAM9B,WAAW+B,KAAX,CAAiB,2BAAjB,EACjBC,KADiB,CACX,QADW,EAEjBnB,MAFiB,EAApB;;AAIA,cAAMoB,SAAS;AACbC,oBAAU,CADG;AAEbC,kBAAQ,CAFK;AAGbT,iBAAO,CAHM;AAIbU,qBAAW;AAJE,SAAf;;AAOAN,cAAMO,OAAN,CAAc,gBAAQ;AACpBJ,iBAAOK,KAAK1B,MAAZ,IAAsB0B,KAAKC,KAA3B;AACD,SAFD;;AAIA,eAAON,MAAP;AAED,OApBD,CAoBE,OAAOL,KAAP,EAAc;AACdrB,gBAAQqB,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,cAAMA,KAAN;AACD;AAxB+B;AAyBjC;;AAED;;;;;AAKMY,oBAAN,CAAyBzC,KAAzB,EAAgC0C,aAAa,EAA7C,EAAiD;AAAA;AAC/C,UAAI;AACF,cAAMzC,aAAaD,MAAM,mBAAN,CAAnB;AACA,cAAM2C,kBAAkB3C,MAAM,wBAAN,CAAxB;;AAEA,cAAM4C,aAAa,IAAIzC,IAAJ,EAAnB;AACAyC,mBAAWC,OAAX,CAAmBD,WAAWE,OAAX,KAAuBJ,UAA1C;AACA,cAAMK,YAAYH,WAAWvC,WAAX,GAAyBC,KAAzB,CAA+B,CAA/B,EAAkC,EAAlC,EAAsCC,OAAtC,CAA8C,GAA9C,EAAmD,GAAnD,CAAlB;;AAEA;AACA,cAAMyC,gBAAgB,MAAM/C,WAAWU,KAAX,CAAiB;AAC3CY,oBAAU,CAAC,GAAD,EAAMwB,SAAN,CADiC;AAE3ClC,kBAAQ;AAFmC,SAAjB,EAGzBmB,KAHyB,CAGnB,gBAHmB,EAGDlB,MAHC,EAA5B;;AAKA,YAAIkC,cAAcjC,MAAd,GAAuB,CAA3B,EAA8B;AAC5B,gBAAMkC,WAAWD,cAAc/B,GAAd,CAAkB;AAAA,mBAAKC,EAAEgC,EAAP;AAAA,WAAlB,CAAjB;;AAEA;AACA,gBAAMP,gBAAgBhC,KAAhB,CAAsB;AAC1BwC,sBAAU,CAAC,IAAD,EAAOF,QAAP;AADgB,WAAtB,EAEHG,MAFG,EAAN;;AAIA;AACA,gBAAMnD,WAAWU,KAAX,CAAiB;AACrBuC,gBAAI,CAAC,IAAD,EAAOD,QAAP;AADiB,WAAjB,EAEHG,MAFG,EAAN;;AAIA5C,kBAAQC,GAAR,CAAa,WAAUuC,cAAcjC,MAAO,WAAU2B,UAAW,MAAjE,EACEM,cAAc/B,GAAd,CAAkB;AAAA,mBAAKC,EAAEE,UAAP;AAAA,WAAlB,EAAqCC,IAArC,CAA0C,IAA1C,CADF;AAED;;AAED,eAAO2B,cAAcjC,MAArB;AAED,OAjCD,CAiCE,OAAOc,KAAP,EAAc;AACdrB,gBAAQqB,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,cAAMA,KAAN;AACD;AArC8C;AAsChD;AAzJ4C,CAA/C", "file": "..\\..\\..\\src\\common\\service\\flash_sale_multi_scheduler.js", "sourcesContent": ["/**\n * 多商品秒杀轮次状态调度服务\n * 负责自动更新轮次状态：upcoming -> active -> ended\n */\n\nmodule.exports = class FlashSaleMultiScheduler {\n  \n  /**\n   * 更新所有轮次状态\n   * @param {Object} model 数据模型\n   */\n  async updateRoundStatus(model) {\n    try {\n      const roundModel = model('flash_sale_rounds');\n      const now = new Date();\n      const nowStr = now.toISOString().slice(0, 19).replace('T', ' ');\n      \n      console.log(`[${nowStr}] 开始检查轮次状态...`);\n      \n      // 1. 将到达开始时间的upcoming轮次设为active\n      const upcomingToActive = await roundModel.where({\n        start_time: ['<=', nowStr],\n        status: 'upcoming'\n      }).select();\n      \n      if (upcomingToActive.length > 0) {\n        await roundModel.where({\n          start_time: ['<=', nowStr],\n          status: 'upcoming'\n        }).update({ status: 'active' });\n        \n        console.log(`✅ 启动了 ${upcomingToActive.length} 个轮次:`, \n          upcomingToActive.map(r => `轮次${r.round_number}(${r.round_name})`).join(', '));\n      }\n      \n      // 2. 将超过结束时间的active轮次设为ended\n      const activeToEnded = await roundModel.where({\n        end_time: ['<', nowStr],\n        status: 'active'\n      }).select();\n      \n      if (activeToEnded.length > 0) {\n        await roundModel.where({\n          end_time: ['<', nowStr],\n          status: 'active'\n        }).update({ status: 'ended' });\n        \n        console.log(`⏰ 结束了 ${activeToEnded.length} 个轮次:`, \n          activeToEnded.map(r => `轮次${r.round_number}(${r.round_name})`).join(', '));\n      }\n      \n      // 3. 将超过结束时间的upcoming轮次也设为ended（防止遗漏）\n      const upcomingToEnded = await roundModel.where({\n        end_time: ['<', nowStr],\n        status: 'upcoming'\n      }).select();\n      \n      if (upcomingToEnded.length > 0) {\n        await roundModel.where({\n          end_time: ['<', nowStr],\n          status: 'upcoming'\n        }).update({ status: 'ended' });\n        \n        console.log(`⚠️ 直接结束了 ${upcomingToEnded.length} 个未启动的过期轮次:`, \n          upcomingToEnded.map(r => `轮次${r.round_number}(${r.round_name})`).join(', '));\n      }\n      \n      const totalUpdated = upcomingToActive.length + activeToEnded.length + upcomingToEnded.length;\n      if (totalUpdated === 0) {\n        console.log('📊 所有轮次状态正常，无需更新');\n      }\n      \n      return {\n        activated: upcomingToActive.length,\n        ended: activeToEnded.length + upcomingToEnded.length,\n        total: totalUpdated\n      };\n      \n    } catch (error) {\n      console.error('❌ 更新轮次状态失败:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * 获取当前活跃轮次统计\n   * @param {Object} model 数据模型\n   */\n  async getActiveRoundsStats(model) {\n    try {\n      const roundModel = model('flash_sale_rounds');\n      \n      const stats = await roundModel.field('status, COUNT(*) as count')\n        .group('status')\n        .select();\n      \n      const result = {\n        upcoming: 0,\n        active: 0,\n        ended: 0,\n        cancelled: 0\n      };\n      \n      stats.forEach(stat => {\n        result[stat.status] = stat.count;\n      });\n      \n      return result;\n      \n    } catch (error) {\n      console.error('获取轮次统计失败:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * 清理过期数据\n   * @param {Object} model 数据模型\n   * @param {number} daysToKeep 保留天数\n   */\n  async cleanupExpiredData(model, daysToKeep = 30) {\n    try {\n      const roundModel = model('flash_sale_rounds');\n      const roundGoodsModel = model('flash_sale_round_goods');\n      \n      const cutoffDate = new Date();\n      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);\n      const cutoffStr = cutoffDate.toISOString().slice(0, 19).replace('T', ' ');\n      \n      // 获取要删除的轮次\n      const expiredRounds = await roundModel.where({\n        end_time: ['<', cutoffStr],\n        status: 'ended'\n      }).field('id, round_name').select();\n      \n      if (expiredRounds.length > 0) {\n        const roundIds = expiredRounds.map(r => r.id);\n        \n        // 删除轮次商品\n        await roundGoodsModel.where({\n          round_id: ['IN', roundIds]\n        }).delete();\n        \n        // 删除轮次\n        await roundModel.where({\n          id: ['IN', roundIds]\n        }).delete();\n        \n        console.log(`🗑️ 清理了 ${expiredRounds.length} 个过期轮次 (${daysToKeep}天前):`, \n          expiredRounds.map(r => r.round_name).join(', '));\n      }\n      \n      return expiredRounds.length;\n      \n    } catch (error) {\n      console.error('清理过期数据失败:', error);\n      throw error;\n    }\n  }\n};\n"]}