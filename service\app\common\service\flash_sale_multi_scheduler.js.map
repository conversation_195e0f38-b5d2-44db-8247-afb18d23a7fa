{"version": 3, "sources": ["..\\..\\..\\src\\common\\service\\flash_sale_multi_scheduler.js"], "names": ["module", "exports", "FlashSaleMultiScheduler", "getCurrentLocalTimeString", "now", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "updateRoundStatus", "model", "roundModel", "nowStr", "console", "log", "allUpcoming", "where", "status", "select", "length", "for<PERSON>ach", "r", "round_number", "start_time", "end_time", "upcomingToActive", "update", "map", "round_name", "join", "activeToEnded", "upcomingToEnded", "totalUpdated", "activated", "ended", "total", "error", "getActiveRoundsStats", "stats", "field", "group", "result", "upcoming", "active", "cancelled", "stat", "count", "cleanupExpiredData", "daysT<PERSON><PERSON>eep", "roundGoodsModel", "cutoffDate", "setDate", "cutoffStr", "toISOString", "slice", "replace", "expiredRounds", "roundIds", "id", "round_id", "delete"], "mappings": ";;AAAA;;;;;AAKAA,OAAOC,OAAP,GAAiB,MAAMC,uBAAN,CAA8B;;AAE7C;;;;AAIAC,8BAA4B;AAC1B,UAAMC,MAAM,IAAIC,IAAJ,EAAZ;AACA,UAAMC,OAAOF,IAAIG,WAAJ,EAAb;AACA,UAAMC,QAAQC,OAAOL,IAAIM,QAAJ,KAAiB,CAAxB,EAA2BC,QAA3B,CAAoC,CAApC,EAAuC,GAAvC,CAAd;AACA,UAAMC,MAAMH,OAAOL,IAAIS,OAAJ,EAAP,EAAsBF,QAAtB,CAA+B,CAA/B,EAAkC,GAAlC,CAAZ;AACA,UAAMG,QAAQL,OAAOL,IAAIW,QAAJ,EAAP,EAAuBJ,QAAvB,CAAgC,CAAhC,EAAmC,GAAnC,CAAd;AACA,UAAMK,UAAUP,OAAOL,IAAIa,UAAJ,EAAP,EAAyBN,QAAzB,CAAkC,CAAlC,EAAqC,GAArC,CAAhB;AACA,UAAMO,UAAUT,OAAOL,IAAIe,UAAJ,EAAP,EAAyBR,QAAzB,CAAkC,CAAlC,EAAqC,GAArC,CAAhB;;AAEA,WAAQ,GAAEL,IAAK,IAAGE,KAAM,IAAGI,GAAI,IAAGE,KAAM,IAAGE,OAAQ,IAAGE,OAAQ,EAA9D;AACD;;AAED;;;;AAIME,mBAAN,CAAwBC,KAAxB,EAA+B;AAAA;;AAAA;AAC7B,UAAI;AACF,cAAMC,aAAaD,MAAM,mBAAN,CAAnB;AACA,cAAME,SAAS,MAAKpB,yBAAL,EAAf;;AAEAqB,gBAAQC,GAAR,CAAa,IAAGF,MAAO,eAAvB;;AAEA;AACA,cAAMG,cAAc,MAAMJ,WAAWK,KAAX,CAAiB;AACzCC,kBAAQ;AADiC,SAAjB,EAEvBC,MAFuB,EAA1B;;AAIA,YAAIH,YAAYI,MAAZ,GAAqB,CAAzB,EAA4B;AAC1BN,kBAAQC,GAAR,CAAY,oBAAZ;AACAC,sBAAYK,OAAZ,CAAoB,aAAK;AACvBP,oBAAQC,GAAR,CAAa,SAAQO,EAAEC,YAAa,KAAID,EAAEE,UAAW,MAAKF,EAAEG,QAAS,WAAUZ,MAAO,GAAtF;AACAC,oBAAQC,GAAR,CAAa,oCAAmCO,EAAEE,UAAF,IAAgBX,MAAO,EAAvE;AACD,WAHD;AAID;;AAED;AACA,cAAMa,mBAAmB,MAAMd,WAAWK,KAAX,CAAiB;AAC9CO,sBAAY,CAAC,IAAD,EAAOX,MAAP,CADkC;AAE9CK,kBAAQ;AAFsC,SAAjB,EAG5BC,MAH4B,EAA/B;;AAKA,YAAIO,iBAAiBN,MAAjB,GAA0B,CAA9B,EAAiC;AAC/B,gBAAMR,WAAWK,KAAX,CAAiB;AACrBO,wBAAY,CAAC,IAAD,EAAOX,MAAP,CADS;AAErBK,oBAAQ;AAFa,WAAjB,EAGHS,MAHG,CAGI,EAAET,QAAQ,QAAV,EAHJ,CAAN;;AAKAJ,kBAAQC,GAAR,CAAa,SAAQW,iBAAiBN,MAAO,OAA7C,EACEM,iBAAiBE,GAAjB,CAAqB;AAAA,mBAAM,KAAIN,EAAEC,YAAa,IAAGD,EAAEO,UAAW,GAAzC;AAAA,WAArB,EAAkEC,IAAlE,CAAuE,IAAvE,CADF;AAED;;AAED;AACA,cAAMC,gBAAgB,MAAMnB,WAAWK,KAAX,CAAiB;AAC3CQ,oBAAU,CAAC,GAAD,EAAMZ,MAAN,CADiC;AAE3CK,kBAAQ;AAFmC,SAAjB,EAGzBC,MAHyB,EAA5B;;AAKA,YAAIY,cAAcX,MAAd,GAAuB,CAA3B,EAA8B;AAC5B,gBAAMR,WAAWK,KAAX,CAAiB;AACrBQ,sBAAU,CAAC,GAAD,EAAMZ,MAAN,CADW;AAErBK,oBAAQ;AAFa,WAAjB,EAGHS,MAHG,CAGI,EAAET,QAAQ,OAAV,EAHJ,CAAN;;AAKAJ,kBAAQC,GAAR,CAAa,SAAQgB,cAAcX,MAAO,OAA1C,EACEW,cAAcH,GAAd,CAAkB;AAAA,mBAAM,KAAIN,EAAEC,YAAa,IAAGD,EAAEO,UAAW,GAAzC;AAAA,WAAlB,EAA+DC,IAA/D,CAAoE,IAApE,CADF;AAED;;AAED;AACA,cAAME,kBAAkB,MAAMpB,WAAWK,KAAX,CAAiB;AAC7CQ,oBAAU,CAAC,GAAD,EAAMZ,MAAN,CADmC;AAE7CK,kBAAQ;AAFqC,SAAjB,EAG3BC,MAH2B,EAA9B;;AAKA,YAAIa,gBAAgBZ,MAAhB,GAAyB,CAA7B,EAAgC;AAC9B,gBAAMR,WAAWK,KAAX,CAAiB;AACrBQ,sBAAU,CAAC,GAAD,EAAMZ,MAAN,CADW;AAErBK,oBAAQ;AAFa,WAAjB,EAGHS,MAHG,CAGI,EAAET,QAAQ,OAAV,EAHJ,CAAN;;AAKAJ,kBAAQC,GAAR,CAAa,YAAWiB,gBAAgBZ,MAAO,aAA/C,EACEY,gBAAgBJ,GAAhB,CAAoB;AAAA,mBAAM,KAAIN,EAAEC,YAAa,IAAGD,EAAEO,UAAW,GAAzC;AAAA,WAApB,EAAiEC,IAAjE,CAAsE,IAAtE,CADF;AAED;;AAED,cAAMG,eAAeP,iBAAiBN,MAAjB,GAA0BW,cAAcX,MAAxC,GAAiDY,gBAAgBZ,MAAtF;AACA,YAAIa,iBAAiB,CAArB,EAAwB;AACtBnB,kBAAQC,GAAR,CAAY,kBAAZ;AACD;;AAED,eAAO;AACLmB,qBAAWR,iBAAiBN,MADvB;AAELe,iBAAOJ,cAAcX,MAAd,GAAuBY,gBAAgBZ,MAFzC;AAGLgB,iBAAOH;AAHF,SAAP;AAMD,OA9ED,CA8EE,OAAOI,KAAP,EAAc;AACdvB,gBAAQuB,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,cAAMA,KAAN;AACD;AAlF4B;AAmF9B;;AAED;;;;AAIMC,sBAAN,CAA2B3B,KAA3B,EAAkC;AAAA;AAChC,UAAI;AACF,cAAMC,aAAaD,MAAM,mBAAN,CAAnB;;AAEA,cAAM4B,QAAQ,MAAM3B,WAAW4B,KAAX,CAAiB,2BAAjB,EACjBC,KADiB,CACX,QADW,EAEjBtB,MAFiB,EAApB;;AAIA,cAAMuB,SAAS;AACbC,oBAAU,CADG;AAEbC,kBAAQ,CAFK;AAGbT,iBAAO,CAHM;AAIbU,qBAAW;AAJE,SAAf;;AAOAN,cAAMlB,OAAN,CAAc,gBAAQ;AACpBqB,iBAAOI,KAAK5B,MAAZ,IAAsB4B,KAAKC,KAA3B;AACD,SAFD;;AAIA,eAAOL,MAAP;AAED,OApBD,CAoBE,OAAOL,KAAP,EAAc;AACdvB,gBAAQuB,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,cAAMA,KAAN;AACD;AAxB+B;AAyBjC;;AAED;;;;;AAKMW,oBAAN,CAAyBrC,KAAzB,EAAgCsC,aAAa,EAA7C,EAAiD;AAAA;AAC/C,UAAI;AACF,cAAMrC,aAAaD,MAAM,mBAAN,CAAnB;AACA,cAAMuC,kBAAkBvC,MAAM,wBAAN,CAAxB;;AAEA,cAAMwC,aAAa,IAAIxD,IAAJ,EAAnB;AACAwD,mBAAWC,OAAX,CAAmBD,WAAWhD,OAAX,KAAuB8C,UAA1C;AACA,cAAMI,YAAYF,WAAWG,WAAX,GAAyBC,KAAzB,CAA+B,CAA/B,EAAkC,EAAlC,EAAsCC,OAAtC,CAA8C,GAA9C,EAAmD,GAAnD,CAAlB;;AAEA;AACA,cAAMC,gBAAgB,MAAM7C,WAAWK,KAAX,CAAiB;AAC3CQ,oBAAU,CAAC,GAAD,EAAM4B,SAAN,CADiC;AAE3CnC,kBAAQ;AAFmC,SAAjB,EAGzBsB,KAHyB,CAGnB,gBAHmB,EAGDrB,MAHC,EAA5B;;AAKA,YAAIsC,cAAcrC,MAAd,GAAuB,CAA3B,EAA8B;AAC5B,gBAAMsC,WAAWD,cAAc7B,GAAd,CAAkB;AAAA,mBAAKN,EAAEqC,EAAP;AAAA,WAAlB,CAAjB;;AAEA;AACA,gBAAMT,gBAAgBjC,KAAhB,CAAsB;AAC1B2C,sBAAU,CAAC,IAAD,EAAOF,QAAP;AADgB,WAAtB,EAEHG,MAFG,EAAN;;AAIA;AACA,gBAAMjD,WAAWK,KAAX,CAAiB;AACrB0C,gBAAI,CAAC,IAAD,EAAOD,QAAP;AADiB,WAAjB,EAEHG,MAFG,EAAN;;AAIA/C,kBAAQC,GAAR,CAAa,WAAU0C,cAAcrC,MAAO,WAAU6B,UAAW,MAAjE,EACEQ,cAAc7B,GAAd,CAAkB;AAAA,mBAAKN,EAAEO,UAAP;AAAA,WAAlB,EAAqCC,IAArC,CAA0C,IAA1C,CADF;AAED;;AAED,eAAO2B,cAAcrC,MAArB;AAED,OAjCD,CAiCE,OAAOiB,KAAP,EAAc;AACdvB,gBAAQuB,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,cAAMA,KAAN;AACD;AArC8C;AAsChD;AArL4C,CAA/C", "file": "..\\..\\..\\src\\common\\service\\flash_sale_multi_scheduler.js", "sourcesContent": ["/**\n * 多商品秒杀轮次状态调度服务\n * 负责自动更新轮次状态：upcoming -> active -> ended\n */\n\nmodule.exports = class FlashSaleMultiScheduler {\n\n  /**\n   * 获取当前本地时间字符串（YYYY-MM-DD HH:mm:ss格式）\n   * 解决时区问题\n   */\n  getCurrentLocalTimeString() {\n    const now = new Date();\n    const year = now.getFullYear();\n    const month = String(now.getMonth() + 1).padStart(2, '0');\n    const day = String(now.getDate()).padStart(2, '0');\n    const hours = String(now.getHours()).padStart(2, '0');\n    const minutes = String(now.getMinutes()).padStart(2, '0');\n    const seconds = String(now.getSeconds()).padStart(2, '0');\n\n    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n  }\n\n  /**\n   * 更新所有轮次状态\n   * @param {Object} model 数据模型\n   */\n  async updateRoundStatus(model) {\n    try {\n      const roundModel = model('flash_sale_rounds');\n      const nowStr = this.getCurrentLocalTimeString();\n\n      console.log(`[${nowStr}] 开始检查轮次状态...`);\n      \n      // 调试：查看所有upcoming状态的轮次\n      const allUpcoming = await roundModel.where({\n        status: 'upcoming'\n      }).select();\n\n      if (allUpcoming.length > 0) {\n        console.log('📋 当前所有upcoming轮次:');\n        allUpcoming.forEach(r => {\n          console.log(`  - 轮次${r.round_number}: ${r.start_time} ~ ${r.end_time} (当前时间: ${nowStr})`);\n          console.log(`    时间比较: start_time <= nowStr ? ${r.start_time <= nowStr}`);\n        });\n      }\n\n      // 1. 将到达开始时间的upcoming轮次设为active\n      const upcomingToActive = await roundModel.where({\n        start_time: ['<=', nowStr],\n        status: 'upcoming'\n      }).select();\n\n      if (upcomingToActive.length > 0) {\n        await roundModel.where({\n          start_time: ['<=', nowStr],\n          status: 'upcoming'\n        }).update({ status: 'active' });\n\n        console.log(`✅ 启动了 ${upcomingToActive.length} 个轮次:`,\n          upcomingToActive.map(r => `轮次${r.round_number}(${r.round_name})`).join(', '));\n      }\n      \n      // 2. 将超过结束时间的active轮次设为ended\n      const activeToEnded = await roundModel.where({\n        end_time: ['<', nowStr],\n        status: 'active'\n      }).select();\n      \n      if (activeToEnded.length > 0) {\n        await roundModel.where({\n          end_time: ['<', nowStr],\n          status: 'active'\n        }).update({ status: 'ended' });\n        \n        console.log(`⏰ 结束了 ${activeToEnded.length} 个轮次:`, \n          activeToEnded.map(r => `轮次${r.round_number}(${r.round_name})`).join(', '));\n      }\n      \n      // 3. 将超过结束时间的upcoming轮次也设为ended（防止遗漏）\n      const upcomingToEnded = await roundModel.where({\n        end_time: ['<', nowStr],\n        status: 'upcoming'\n      }).select();\n      \n      if (upcomingToEnded.length > 0) {\n        await roundModel.where({\n          end_time: ['<', nowStr],\n          status: 'upcoming'\n        }).update({ status: 'ended' });\n        \n        console.log(`⚠️ 直接结束了 ${upcomingToEnded.length} 个未启动的过期轮次:`, \n          upcomingToEnded.map(r => `轮次${r.round_number}(${r.round_name})`).join(', '));\n      }\n      \n      const totalUpdated = upcomingToActive.length + activeToEnded.length + upcomingToEnded.length;\n      if (totalUpdated === 0) {\n        console.log('📊 所有轮次状态正常，无需更新');\n      }\n      \n      return {\n        activated: upcomingToActive.length,\n        ended: activeToEnded.length + upcomingToEnded.length,\n        total: totalUpdated\n      };\n      \n    } catch (error) {\n      console.error('❌ 更新轮次状态失败:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * 获取当前活跃轮次统计\n   * @param {Object} model 数据模型\n   */\n  async getActiveRoundsStats(model) {\n    try {\n      const roundModel = model('flash_sale_rounds');\n      \n      const stats = await roundModel.field('status, COUNT(*) as count')\n        .group('status')\n        .select();\n      \n      const result = {\n        upcoming: 0,\n        active: 0,\n        ended: 0,\n        cancelled: 0\n      };\n      \n      stats.forEach(stat => {\n        result[stat.status] = stat.count;\n      });\n      \n      return result;\n      \n    } catch (error) {\n      console.error('获取轮次统计失败:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * 清理过期数据\n   * @param {Object} model 数据模型\n   * @param {number} daysToKeep 保留天数\n   */\n  async cleanupExpiredData(model, daysToKeep = 30) {\n    try {\n      const roundModel = model('flash_sale_rounds');\n      const roundGoodsModel = model('flash_sale_round_goods');\n      \n      const cutoffDate = new Date();\n      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);\n      const cutoffStr = cutoffDate.toISOString().slice(0, 19).replace('T', ' ');\n      \n      // 获取要删除的轮次\n      const expiredRounds = await roundModel.where({\n        end_time: ['<', cutoffStr],\n        status: 'ended'\n      }).field('id, round_name').select();\n      \n      if (expiredRounds.length > 0) {\n        const roundIds = expiredRounds.map(r => r.id);\n        \n        // 删除轮次商品\n        await roundGoodsModel.where({\n          round_id: ['IN', roundIds]\n        }).delete();\n        \n        // 删除轮次\n        await roundModel.where({\n          id: ['IN', roundIds]\n        }).delete();\n        \n        console.log(`🗑️ 清理了 ${expiredRounds.length} 个过期轮次 (${daysToKeep}天前):`, \n          expiredRounds.map(r => r.round_name).join(', '));\n      }\n      \n      return expiredRounds.length;\n      \n    } catch (error) {\n      console.error('清理过期数据失败:', error);\n      throw error;\n    }\n  }\n};\n"]}