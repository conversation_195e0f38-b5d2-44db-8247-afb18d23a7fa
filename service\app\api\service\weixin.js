function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

const crypto = require('crypto');
const md5 = require('md5');
const moment = require('moment');
const rp = require('request-promise');
const fs = require('fs');
const http = require("http");
module.exports = class extends think.Service {
    /**
     * 解析微信登录用户数据
     * @param sessionKey
     * @param encryptedData
     * @param iv
     * @returns {Promise.<string>}
     */
    decryptUserInfoData(sessionKey, encryptedData, iv) {
        return _asyncToGenerator(function* () {
            // base64 decode
            const _sessionKey = Buffer.from(sessionKey, 'base64');
            encryptedData = Buffer.from(encryptedData, 'base64');
            iv = Buffer.from(iv, 'base64');
            let decoded = '';
            try {
                // 解密
                const decipher = crypto.createDecipheriv('aes-128-cbc', _sessionKey, iv);
                // 设置自动 padding 为 true，删除填充补位
                decipher.setAutoPadding(true);
                decoded = decipher.update(encryptedData, 'binary', 'utf8');
                decoded += decipher.final('utf8');
                decoded = JSON.parse(decoded);
            } catch (err) {
                return '';
            }
            if (decoded.watermark.appid !== think.config('weixin.appid')) {
                return '';
            }
            return decoded;
        })();
    }
    /**
     * 统一下单
     * @param payInfo
     * @returns {Promise}
     */
    createUnifiedOrder(payInfo) {
        return _asyncToGenerator(function* () {
            const WeiXinPay = require('weixinpay');
            const weixinpay = new WeiXinPay({
                appid: think.config('weixin.appid'), // 微信小程序appid
                openid: payInfo.openid, // 用户openid
                mch_id: think.config('weixin.mch_id'), // 商户帐号ID
                partner_key: think.config('weixin.partner_key') // 秘钥
            });
            return new Promise(function (resolve, reject) {
                // let total_fee = this.getTotalFee(payInfo.out_trade_no);
                weixinpay.createUnifiedOrder({
                    body: payInfo.body,
                    out_trade_no: payInfo.out_trade_no,
                    total_fee: payInfo.total_fee,
                    // total_fee: total_fee,
                    spbill_create_ip: payInfo.spbill_create_ip,
                    notify_url: think.config('weixin.notify_url'),
                    trade_type: 'JSAPI'
                }, function (res) {
                    console.log(res);
                    if (res.return_code === 'SUCCESS' && res.result_code === 'SUCCESS') {
                        const returnParams = {
                            'appid': res.appid,
                            'timeStamp': parseInt(Date.now() / 1000) + '',
                            'nonceStr': res.nonce_str,
                            'package': 'prepay_id=' + res.prepay_id,
                            'signType': 'MD5'
                        };
                        const paramStr = `appId=${returnParams.appid}&nonceStr=${returnParams.nonceStr}&package=${returnParams.package}&signType=${returnParams.signType}&timeStamp=${returnParams.timeStamp}&key=` + think.config('weixin.partner_key');
                        returnParams.paySign = md5(paramStr).toUpperCase();
                        let order_sn = payInfo.out_trade_no;
                        resolve(returnParams);
                    } else {
                        reject(res);
                    }
                });
            });
        })();
    }
    getTotalFee(sn) {
        var _this = this;

        return _asyncToGenerator(function* () {
            let total_fee = yield _this.model('order').where({
                order_sn: sn
            }).field('actual_price').find();
            let res = total_fee.actual_price;
            return res;
        })();
    }
    /**
     * 生成排序后的支付参数 query
     * @param queryObj
     * @returns {Promise.<string>}
     */
    buildQuery(queryObj) {
        const sortPayOptions = {};
        for (const key of Object.keys(queryObj).sort()) {
            sortPayOptions[key] = queryObj[key];
        }
        let payOptionQuery = '';
        for (const key of Object.keys(sortPayOptions).sort()) {
            payOptionQuery += key + '=' + sortPayOptions[key] + '&';
        }
        payOptionQuery = payOptionQuery.substring(0, payOptionQuery.length - 1);
        return payOptionQuery;
    }
    /**
     * 对 query 进行签名
     * @param queryStr
     * @returns {Promise.<string>}
     */
    signQuery(queryStr) {
        queryStr = queryStr + '&key=' + think.config('weixin.partner_key');
        const md5 = require('md5');
        const md5Sign = md5(queryStr);
        return md5Sign.toUpperCase();
    }
    /**
     * 处理微信支付回调
     * @param notifyData
     * @returns {{}}
     */
    payNotify(notifyData) {
        if (think.isEmpty(notifyData)) {
            return false;
        }
        const notifyObj = {};
        let sign = '';
        for (const key of Object.keys(notifyData)) {
            if (key !== 'sign') {
                notifyObj[key] = notifyData[key][0];
            } else {
                sign = notifyData[key][0];
            }
        }
        if (notifyObj.return_code !== 'SUCCESS' || notifyObj.result_code !== 'SUCCESS') {
            return false;
        }
        const signString = this.signQuery(this.buildQuery(notifyObj));
        if (think.isEmpty(sign) || signString !== sign) {
            return false;
        }
        let timeInfo = notifyObj.time_end;
        let pay_time = moment(timeInfo, 'YYYYMMDDHHmmss');
        notifyObj.time_end = new Date(Date.parse(pay_time)).getTime() / 1000;
        return notifyObj;
    }
    /**
     * 申请退款
     * @param refundInfo
     * @returns {Promise}
     */
    createRefund(refundInfo) {
        const crypto = require('crypto');
        const request = require('request');
        const xml2js = require('xml2js');
        const fs = require('fs');

        return new Promise((resolve, reject) => {
            console.log('=== 调用微信退款API ===');
            console.log('退款参数:', refundInfo);

            try {
                // 生成随机字符串
                const nonce_str = Math.random().toString(36).substr(2, 15);

                // 构建退款参数
                const refundParams = {
                    appid: think.config('weixin.appid'),
                    mch_id: think.config('weixin.mch_id'),
                    nonce_str: nonce_str,
                    out_trade_no: refundInfo.out_trade_no,
                    out_refund_no: refundInfo.out_refund_no,
                    total_fee: refundInfo.total_fee,
                    refund_fee: refundInfo.refund_fee,
                    refund_desc: refundInfo.refund_desc || '用户申请退款',
                    notify_url: think.config('weixin.refund_notify_url') || think.config('weixin.notify_url')
                };

                // 生成签名
                const sign = this.generateSign(refundParams, think.config('weixin.partner_key'));
                refundParams.sign = sign;

                console.log('签名后的退款参数:', refundParams);

                // 构建XML
                const builder = new xml2js.Builder();
                const xml = builder.buildObject(refundParams);

                console.log('退款请求XML:', xml);

                // 检查API证书文件
                const certPath = think.config('weixin.cert_path') || './cert/apiclient_cert.pem';
                const keyPath = think.config('weixin.key_path') || './cert/apiclient_key.pem';

                let requestOptions = {
                    url: 'https://api.mch.weixin.qq.com/pay/refund',
                    method: 'POST',
                    body: xml,
                    headers: {
                        'Content-Type': 'application/xml'
                    }
                };

                // 如果证书文件存在，添加证书配置
                if (fs.existsSync(certPath) && fs.existsSync(keyPath)) {
                    console.log('✅ 使用API证书进行请求');
                    requestOptions.cert = fs.readFileSync(certPath);
                    requestOptions.key = fs.readFileSync(keyPath);
                    requestOptions.passphrase = think.config('weixin.mch_id'); // 证书密码通常是商户号
                } else {
                    console.log('⚠️  API证书文件不存在，尝试无证书请求（可能失败）');
                    console.log(`证书路径: ${certPath}`);
                    console.log(`私钥路径: ${keyPath}`);
                }

                // 发送退款请求
                request(requestOptions, (error, response, body) => {
                    if (error) {
                        console.error('❌ 退款请求失败:', error);
                        reject({
                            success: false,
                            error_code: 'REQUEST_ERROR',
                            error_msg: error.message
                        });
                        return;
                    }

                    console.log('微信退款API响应:', body);

                    // 解析XML响应
                    xml2js.parseString(body, (err, result) => {
                        if (err) {
                            console.error('❌ 解析退款响应失败:', err);
                            reject({
                                success: false,
                                error_code: 'PARSE_ERROR',
                                error_msg: err.message
                            });
                            return;
                        }

                        // 转换响应格式
                        const res = this.parseWXReturnXML(result);
                        console.log('解析后的退款响应:', res);

                        if (res.return_code === 'SUCCESS') {
                            if (res.result_code === 'SUCCESS') {
                                // 退款成功
                                console.log('✅ 微信退款申请成功');
                                resolve({
                                    success: true,
                                    refund_id: res.refund_id,
                                    out_refund_no: res.out_refund_no,
                                    transaction_id: res.transaction_id,
                                    out_trade_no: res.out_trade_no,
                                    refund_fee: res.refund_fee,
                                    settlement_refund_fee: res.settlement_refund_fee,
                                    total_fee: res.total_fee,
                                    settlement_total_fee: res.settlement_total_fee,
                                    fee_type: res.fee_type,
                                    cash_fee: res.cash_fee,
                                    cash_refund_fee: res.cash_refund_fee
                                });
                            } else {
                                // 业务失败
                                console.error('❌ 微信退款业务失败:', res.err_code, res.err_code_des);
                                reject({
                                    success: false,
                                    error_code: res.err_code,
                                    error_msg: res.err_code_des,
                                    return_msg: res.return_msg
                                });
                            }
                        } else {
                            // 通信失败
                            console.error('❌ 微信退款通信失败:', res.return_msg);
                            reject({
                                success: false,
                                error_code: 'COMMUNICATION_ERROR',
                                error_msg: res.return_msg
                            });
                        }
                    });
                });
            } catch (error) {
                console.error('❌ 退款处理异常:', error);
                reject({
                    success: false,
                    error_code: 'SYSTEM_ERROR',
                    error_msg: error.message
                });
            }
        });
    }
    getAccessToken() {
        return _asyncToGenerator(function* () {
            const options = {
                method: 'POST',
                // url: 'https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=',
                url: 'https://api.weixin.qq.com/cgi-bin/token',
                qs: {
                    grant_type: 'client_credential',
                    secret: think.config('weixin.secret'),
                    appid: think.config('weixin.appid')
                }
            };
            let sessionData = yield rp(options);
            sessionData = JSON.parse(sessionData);
            let token = sessionData.access_token;
            return token;
        })();
    }
    getSelfToken(params) {
        return _asyncToGenerator(function* () {
            var key = ['meiweiyuxianmeiweiyuxian', params.timestamp, params.nonce].sort().join('');
            //将token （自己设置的） 、timestamp（时间戳）、nonce（随机数）三个参数进行字典排序
            var sha1 = crypto.createHash('sha1');
            //将上面三个字符串拼接成一个字符串再进行sha1加密
            sha1.update(key);
            //将加密后的字符串与signature进行对比，若成功，返回success。如果通过验证，则，注释掉这个函数
            let a = sha1.digest('hex');
            let b = params.signature;
            if (a == b) {
                return true;
            }
        })();
    }
    sendMessage(token, data) {
        return _asyncToGenerator(function* () {
            const sendInfo = {
                method: 'POST',
                url: 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=' + token,
                body: data,
                json: true
            };
            let posting = yield rp(sendInfo);
            console.log(posting);
            return posting;
        })();
    }
    getMessageATempId(type) {
        return _asyncToGenerator(function* () {
            switch (type) {
                case 1:
                    return 'TXWzXjO4C0odXCwQk4idgBtGcgSKBEWXJETYBZcRAzE';
                    break;
                // 支付成功
                case 2:
                    return 'COiQGBTzTtz_us5qYeJf0K-pFAyubBuWQh40sV1eAuw';
                    break;
                // 发货通知
                default:
                    return '400';
            }
        })();
    }
    getMessageTempId(type) {
        return _asyncToGenerator(function* () {
            switch (type) {
                case 1:
                    return 'TXWzXjO4C0odXCwQk4idgBtGcgSKBEWXJETYBZcRAzE';
                    break;
                // 支付成功
                case 2:
                    return 'COiQGBTzTtz_us5qYeJf0K-pFAyubBuWQh40sV1eAuw';
                    break;
                // 发货通知
                default:
                    return '400';
            }
        })();
    }
    sendTextMessage(data, access_token) {
        return _asyncToGenerator(function* () {
            const sendInfo = {
                method: 'POST',
                url: 'https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=' + access_token,
                body: {
                    touser: data.FromUserName,
                    msgtype: "text",
                    text: {
                        content: data.Content
                    }
                },
                json: true
            };
            let posting = yield rp(sendInfo);
            return posting;
        })();
    }
    sendImageMessage(media_id, data, access_token) {
        return _asyncToGenerator(function* () {
            const sendInfo = {
                method: 'POST',
                url: 'https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=' + access_token,
                body: {
                    touser: data.FromUserName,
                    msgtype: "image",
                    image: {
                        media_id: media_id
                    }
                },
                json: true
            };
            let posting = yield rp(sendInfo);
            return posting;
        })();
    }

    /**
     * 微信小程序订单发货信息录入接口
     * @param {Object} shippingData 发货信息
     * @returns {Promise}
     */
    uploadShippingInfo(shippingData) {
        var _this2 = this;

        return _asyncToGenerator(function* () {
            try {
                const token = yield _this2.getAccessToken();
                const options = {
                    method: 'POST',
                    url: 'https://api.weixin.qq.com/wxa/sec/order/upload_shipping_info?access_token=' + token,
                    body: shippingData,
                    json: true
                };

                console.log('=== 微信发货信息录入开始 ===');
                console.log('发货数据:', JSON.stringify(shippingData, null, 2));

                const result = yield rp(options);
                console.log('微信发货接口返回:', result);

                if (result.errcode === 0) {
                    console.log('发货信息录入成功');
                    return { success: true, data: result };
                } else {
                    console.error('发货信息录入失败:', result.errcode, result.errmsg);
                    return { success: false, error: result };
                }
            } catch (error) {
                console.error('发货信息录入异常:', error);
                return { success: false, error: error.message };
            }
        })();
    }

    /**
     * 查询订单发货状态
     * @param {Object} orderKey 订单信息
     * @returns {Promise}
     */
    getOrderShippingStatus(orderKey) {
        var _this3 = this;

        return _asyncToGenerator(function* () {
            try {
                const token = yield _this3.getAccessToken();
                const options = {
                    method: 'POST',
                    url: 'https://api.weixin.qq.com/wxa/sec/order/get_order?access_token=' + token,
                    body: orderKey,
                    json: true
                };

                const result = yield rp(options);
                console.log('查询订单发货状态返回:', result);

                if (result.errcode === 0) {
                    return { success: true, data: result };
                } else {
                    console.error('查询订单发货状态失败:', result.errcode, result.errmsg);
                    return { success: false, error: result };
                }
            } catch (error) {
                console.error('查询订单发货状态异常:', error);
                return { success: false, error: error.message };
            }
        })();
    }

    /**
     * 确认收货提醒接口
     * @param {Object} confirmData 确认收货数据
     * @returns {Promise}
     */
    notifyConfirmReceive(confirmData) {
        var _this4 = this;

        return _asyncToGenerator(function* () {
            try {
                const token = yield _this4.getAccessToken();
                const options = {
                    method: 'POST',
                    url: 'https://api.weixin.qq.com/wxa/sec/order/notify_confirm_receive?access_token=' + token,
                    body: confirmData,
                    json: true
                };

                const result = yield rp(options);
                console.log('确认收货提醒返回:', result);

                if (result.errcode === 0) {
                    return { success: true, data: result };
                } else {
                    console.error('确认收货提醒失败:', result.errcode, result.errmsg);
                    return { success: false, error: result };
                }
            } catch (error) {
                console.error('确认收货提醒异常:', error);
                return { success: false, error: error.message };
            }
        })();
    }

    /**
     * 设置消息跳转路径
     * @param {string} path 跳转路径
     * @returns {Promise}
     */
    setMsgJumpPath(path) {
        var _this5 = this;

        return _asyncToGenerator(function* () {
            try {
                const token = yield _this5.getAccessToken();
                const options = {
                    method: 'POST',
                    url: 'https://api.weixin.qq.com/wxa/sec/order/set_msg_jump_path?access_token=' + token,
                    body: { path: path },
                    json: true
                };

                const result = yield rp(options);
                console.log('设置消息跳转路径返回:', result);

                if (result.errcode === 0) {
                    return { success: true, data: result };
                } else {
                    console.error('设置消息跳转路径失败:', result.errcode, result.errmsg);
                    return { success: false, error: result };
                }
            } catch (error) {
                console.error('设置消息跳转路径异常:', error);
                return { success: false, error: error.message };
            }
        })();
    }

    /**
     * 构建发货信息数据
     * @param {Object} orderInfo 订单信息
     * @param {Object} expressInfo 快递信息
     * @param {string} openid 用户openid
     * @param {number} logisticsType 物流类型 1-快递 2-同城 3-虚拟 4-自提
     * @returns {Object}
     */
    buildShippingData(orderInfo, expressInfo, openid, logisticsType = 1) {
        // 数据验证
        const validationResult = this.validateShippingData(orderInfo, expressInfo, openid, logisticsType);
        if (!validationResult.valid) {
            throw new Error(`发货数据验证失败: ${validationResult.error}`);
        }

        const currentTime = this.formatRFC3339Time(new Date());

        // 构建基础发货数据
        const shippingData = {
            order_key: {
                order_number_type: 1, // 使用商户单号
                mchid: think.config('weixin.mch_id'),
                out_trade_no: orderInfo.order_sn
            },
            delivery_mode: 1, // 统一发货
            logistics_type: logisticsType,
            upload_time: currentTime,
            payer: {
                openid: openid
            }
        };

        // 根据物流类型构建shipping_list
        if (logisticsType === 1 && expressInfo && expressInfo.logistic_code) {
            // 快递发货
            const itemDesc = this.buildItemDescription(orderInfo.goods_list || []);
            shippingData.shipping_list = [{
                tracking_no: expressInfo.logistic_code,
                express_company: expressInfo.shipper_code || 'OTHER',
                item_desc: itemDesc
            }];

            // 如果是顺丰快递，需要添加联系方式
            if (expressInfo.shipper_code === 'SF') {
                const maskedContact = this.maskContactInfo("400-95338");
                shippingData.shipping_list[0].contact = {
                    consignor_contact: maskedContact
                };
            }
        } else if (logisticsType === 4) {
            // 自提
            shippingData.shipping_list = [{
                item_desc: this.buildItemDescription(orderInfo.goods_list || [])
            }];
        } else if (logisticsType === 3) {
            // 虚拟商品
            shippingData.shipping_list = [{
                item_desc: this.buildItemDescription(orderInfo.goods_list || [])
            }];
        } else {
            // 默认情况
            shippingData.shipping_list = [{
                item_desc: this.buildItemDescription(orderInfo.goods_list || [])
            }];
        }

        return shippingData;
    }

    /**
     * 构建商品描述
     * @param {Array} goodsList 商品列表
     * @returns {string}
     */
    buildItemDescription(goodsList) {
        if (!goodsList || goodsList.length === 0) {
            return '商品';
        }

        let description = '';
        if (goodsList.length === 1) {
            description = goodsList[0].goods_name + '*' + (goodsList[0].number || 1) + '个';
        } else {
            description = goodsList[0].goods_name + '等' + goodsList.length + '件商品';
        }

        // 限制商品描述长度为120个字符
        if (description.length > 120) {
            description = description.substring(0, 117) + '...';
        }

        return description;
    }

    /**
     * 验证发货数据
     * @param {Object} orderInfo 订单信息
     * @param {Object} expressInfo 快递信息
     * @param {string} openid 用户openid
     * @param {number} logisticsType 物流类型
     * @returns {Object}
     */
    validateShippingData(orderInfo, expressInfo, openid, logisticsType) {
        // 验证基础参数
        if (!orderInfo || !orderInfo.order_sn) {
            return { valid: false, error: '订单号不能为空' };
        }

        if (!openid || typeof openid !== 'string') {
            return { valid: false, error: '用户openid不能为空且必须为字符串' };
        }

        if (openid.length < 1 || openid.length > 128) {
            return { valid: false, error: '用户openid长度必须在1-128字符之间' };
        }

        // 验证物流类型
        if (![1, 2, 3, 4].includes(logisticsType)) {
            return { valid: false, error: '物流类型必须为1(快递)、2(同城)、3(虚拟)、4(自提)之一' };
        }

        // 验证快递信息
        if (logisticsType === 1) {
            if (!expressInfo || !expressInfo.logistic_code) {
                return { valid: false, error: '快递发货时物流单号不能为空' };
            }

            if (expressInfo.logistic_code.length > 128) {
                return { valid: false, error: '物流单号长度不能超过128字符' };
            }

            if (!expressInfo.shipper_code) {
                return { valid: false, error: '快递发货时物流公司编码不能为空' };
            }

            if (expressInfo.shipper_code.length > 128) {
                return { valid: false, error: '物流公司编码长度不能超过128字符' };
            }
        }

        // 验证商品描述
        const itemDesc = this.buildItemDescription(orderInfo.goods_list || []);
        if (!itemDesc || itemDesc.trim() === '') {
            return { valid: false, error: '商品描述不能为空' };
        }

        return { valid: true };
    }

    /**
     * 格式化时间为RFC 3339格式
     * @param {Date} date 日期对象
     * @returns {string}
     */
    formatRFC3339Time(date) {
        if (!(date instanceof Date)) {
            date = new Date();
        }

        // 确保时间格式符合RFC 3339标准
        // 例如: 2022-12-15T13:29:35.120+08:00
        return date.toISOString().replace('Z', '+08:00');
    }

    /**
     * 联系方式掩码处理
     * @param {string} contact 联系方式
     * @returns {string}
     */
    maskContactInfo(contact) {
        if (!contact || typeof contact !== 'string') {
            return '';
        }

        // 移除所有非数字和连字符
        const cleaned = contact.replace(/[^\d-]/g, '');

        if (cleaned.length < 4) {
            return contact;
        }

        // 保留最后4位数字，其他用*替换
        const lastFour = cleaned.slice(-4);
        const masked = cleaned.slice(0, -4).replace(/\d/g, '*');

        return masked + lastFour;
    }

    /**
     * 验证联系方式格式
     * @param {string} contact 联系方式
     * @returns {boolean}
     */
    validateContactFormat(contact) {
        if (!contact || typeof contact !== 'string') {
            return false;
        }

        // 联系方式长度限制
        if (contact.length > 1024) {
            return false;
        }

        // 检查是否包含最后4位数字（不能打掩码）
        const lastFourMatch = contact.match(/\d{4}$/);
        if (!lastFourMatch) {
            return false;
        }

        return true;
    }

    /**
     * 生成微信支付签名
     * @param {Object} params 参数对象
     * @param {String} key 商户密钥
     * @returns {String} 签名
     */
    generateSign(params, key) {
        // 过滤空值和特殊字段
        const filteredParams = {};
        Object.keys(params).forEach(k => {
            if (params[k] !== undefined && params[k] !== '' && ['sign', 'key'].indexOf(k) < 0) {
                filteredParams[k] = params[k];
            }
        });

        // 按字典序排序
        const sortedKeys = Object.keys(filteredParams).sort();

        // 构建签名字符串
        const stringA = sortedKeys.map(k => `${k}=${filteredParams[k]}`).join('&');
        const stringSignTemp = `${stringA}&key=${key}`;

        console.log('签名字符串:', stringSignTemp);

        // MD5签名并转大写
        const crypto = require('crypto');
        const sign = crypto.createHash('md5').update(stringSignTemp, 'utf8').digest('hex').toUpperCase();

        console.log('生成的签名:', sign);
        return sign;
    }

    /**
     * 解析微信返回的XML数据
     * @param {Object} xmlObject XML解析后的对象
     * @returns {Object} 解析后的数据
     */
    parseWXReturnXML(xmlObject) {
        const newObject = {};
        xmlObject = xmlObject.xml || {};
        for (const key in xmlObject) {
            if (xmlObject[key] && xmlObject[key][0]) {
                newObject[key] = xmlObject[key][0];
            }
        }
        return newObject;
    }
};