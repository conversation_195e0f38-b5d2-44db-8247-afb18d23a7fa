{"version": 3, "sources": ["..\\..\\..\\src\\admin\\model\\recruitment_rules.js"], "names": ["module", "exports", "think", "Model", "getRecruitmentRules", "rules", "find", "conditions", "JSON", "parse", "e", "joinCondition", "join_condition", "applicationMethod", "application_method", "requireApplicationInfo", "require_application_info", "auditMethod", "audit_method", "addTime", "add_time", "updateTime", "update_time", "saveRecruitmentRules", "rulesData", "currentTime", "parseInt", "Date", "getTime", "saveData", "stringify", "existingRules", "id", "where", "update", "add", "checkUserConditions", "userId", "canJoin", "message", "checkResults", "failedConditions", "requireAmount", "userOrders", "model", "user_id", "order_status", "sum", "totalAmount", "parseFloat", "requiredAmount", "minAmount", "push", "type", "required", "current", "passed", "requireOrders", "orderCount", "count", "requiredOrders", "minOrders", "requirePurchase", "getDefaultRules", "validateRules", "errors", "hasCondition", "<PERSON><PERSON><PERSON><PERSON>", "length"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;AACvC;;;;AAIMC,uBAAN,GAA4B;AAAA;;AAAA;AACxB,kBAAMC,QAAQ,MAAM,MAAKC,IAAL,EAApB;;AAEA,gBAAID,KAAJ,EAAW;AACP;AACA,oBAAIA,MAAME,UAAV,EAAsB;AAClB,wBAAI;AACAF,8BAAME,UAAN,GAAmBC,KAAKC,KAAL,CAAWJ,MAAME,UAAjB,CAAnB;AACH,qBAFD,CAEE,OAAOG,CAAP,EAAU;AACRL,8BAAME,UAAN,GAAmB,EAAnB;AACH;AACJ;;AAED;AACA,uBAAO;AACHI,mCAAeN,MAAMO,cADlB;AAEHL,gCAAYF,MAAME,UAAN,IAAoB,EAF7B;AAGHM,uCAAmBR,MAAMS,kBAHtB;AAIHC,4CAAwBV,MAAMW,wBAAN,KAAmC,CAJxD;AAKHC,iCAAaZ,MAAMa,YALhB;AAMHC,6BAASd,MAAMe,QANZ;AAOHC,gCAAYhB,MAAMiB;AAPf,iBAAP;AASH;;AAED,mBAAO,IAAP;AAzBwB;AA0B3B;;AAED;;;;;AAKMC,wBAAN,CAA2BC,SAA3B,EAAsC;AAAA;;AAAA;AAClC,kBAAMC,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;;AAEA,kBAAMC,WAAW;AACbjB,gCAAgBY,UAAUb,aADb;AAEbJ,4BAAYC,KAAKsB,SAAL,CAAeN,UAAUjB,UAAV,IAAwB,EAAvC,CAFC;AAGbO,oCAAoBU,UAAUX,iBAHjB;AAIbG,0CAA0BQ,UAAUT,sBAAV,GAAmC,CAAnC,GAAuC,CAJpD;AAKbG,8BAAcM,UAAUP,WALX;AAMbK,6BAAaG;AANA,aAAjB;;AASA;AACA,kBAAMM,gBAAgB,MAAM,OAAKzB,IAAL,EAA5B;;AAEA,gBAAIyB,iBAAiBA,cAAcC,EAAnC,EAAuC;AACnC;AACA,uBAAO,MAAM,OAAKC,KAAL,CAAW,EAAED,IAAID,cAAcC,EAApB,EAAX,EAAqCE,MAArC,CAA4CL,QAA5C,CAAb;AACH,aAHD,MAGO;AACH;AACAA,yBAAST,QAAT,GAAoBK,WAApB;AACA,uBAAO,MAAM,OAAKU,GAAL,CAASN,QAAT,CAAb;AACH;AAtBiC;AAuBrC;;AAED;;;;;AAKMO,uBAAN,CAA0BC,MAA1B,EAAkC;AAAA;;AAAA;AAC9B,kBAAMhC,QAAQ,MAAM,OAAKD,mBAAL,EAApB;;AAEA,gBAAI,CAACC,KAAD,IAAUA,MAAMM,aAAN,KAAwB,eAAtC,EAAuD;AACnD,uBAAO;AACH2B,6BAAS,IADN;AAEHC,6BAAS;AAFN,iBAAP;AAIH;;AAED,kBAAMhC,aAAaF,MAAME,UAAN,IAAoB,EAAvC;AACA,kBAAMiC,eAAe;AACjBF,yBAAS,IADQ;AAEjB/B,4BAAY,EAFK;AAGjBkC,kCAAkB;AAHD,aAArB;;AAMA;AACA,gBAAIlC,WAAWmC,aAAf,EAA8B;AAC1B,sBAAMC,aAAa,MAAM,OAAKC,KAAL,CAAW,OAAX,EAAoBX,KAApB,CAA0B;AAC/CY,6BAASR,MADsC;AAE/CS,kCAAc,CAAC,IAAD,EAAO,GAAP,CAFiC,CAErB;AAFqB,iBAA1B,EAGtBC,GAHsB,CAGlB,cAHkB,CAAzB;;AAKA,sBAAMC,cAAcC,WAAWN,cAAc,CAAzB,CAApB;AACA,sBAAMO,iBAAiBD,WAAW1C,WAAW4C,SAAX,IAAwB,CAAnC,CAAvB;;AAEA,oBAAIH,eAAeE,cAAnB,EAAmC;AAC/BV,iCAAajC,UAAb,CAAwB6C,IAAxB,CAA6B;AACzBC,8BAAM,QADmB;AAEzBC,kCAAUJ,cAFe;AAGzBK,iCAASP,WAHgB;AAIzBQ,gCAAQ;AAJiB,qBAA7B;AAMH,iBAPD,MAOO;AACHhB,iCAAajC,UAAb,CAAwB6C,IAAxB,CAA6B;AACzBC,8BAAM,QADmB;AAEzBC,kCAAUJ,cAFe;AAGzBK,iCAASP,WAHgB;AAIzBQ,gCAAQ;AAJiB,qBAA7B;AAMAhB,iCAAaC,gBAAb,CAA8BW,IAA9B,CAAmC,QAAnC;AACAZ,iCAAaF,OAAb,GAAuB,KAAvB;AACH;AACJ;;AAED;AACA,gBAAI/B,WAAWkD,aAAf,EAA8B;AAC1B,sBAAMC,aAAa,MAAM,OAAKd,KAAL,CAAW,OAAX,EAAoBX,KAApB,CAA0B;AAC/CY,6BAASR,MADsC;AAE/CS,kCAAc,CAAC,IAAD,EAAO,GAAP,CAFiC,CAErB;AAFqB,iBAA1B,EAGtBa,KAHsB,EAAzB;;AAKA,sBAAMC,iBAAiBlC,SAASnB,WAAWsD,SAAX,IAAwB,CAAjC,CAAvB;;AAEA,oBAAIH,cAAcE,cAAlB,EAAkC;AAC9BpB,iCAAajC,UAAb,CAAwB6C,IAAxB,CAA6B;AACzBC,8BAAM,QADmB;AAEzBC,kCAAUM,cAFe;AAGzBL,iCAASG,UAHgB;AAIzBF,gCAAQ;AAJiB,qBAA7B;AAMH,iBAPD,MAOO;AACHhB,iCAAajC,UAAb,CAAwB6C,IAAxB,CAA6B;AACzBC,8BAAM,QADmB;AAEzBC,kCAAUM,cAFe;AAGzBL,iCAASG,UAHgB;AAIzBF,gCAAQ;AAJiB,qBAA7B;AAMAhB,iCAAaC,gBAAb,CAA8BW,IAA9B,CAAmC,QAAnC;AACAZ,iCAAaF,OAAb,GAAuB,KAAvB;AACH;AACJ;;AAED;AACA,gBAAI/B,WAAWuD,eAAf,EAAgC;AAC5B;AACA;AACAtB,6BAAajC,UAAb,CAAwB6C,IAAxB,CAA6B;AACzBC,0BAAM,UADmB;AAEzBC,8BAAU,MAFe;AAGzBC,6BAAS,KAHgB;AAIzBC,4BAAQ;AAJiB,iBAA7B;AAMH;;AAED,mBAAOhB,YAAP;AAtF8B;AAuFjC;;AAED;;;;AAIAuB,sBAAkB;AACd,eAAO;AACHpD,2BAAe,iBADZ;AAEHJ,wBAAY;AACRuD,iCAAiB,KADT;AAERpB,+BAAe,IAFP;AAGRS,2BAAW,KAHH;AAIRM,+BAAe,KAJP;AAKRI,2BAAW;AALH,aAFT;AASHhD,+BAAmB,cAThB;AAUHE,oCAAwB,KAVrB;AAWHE,yBAAa;AAXV,SAAP;AAaH;;AAED;;;;;AAKA+C,kBAAcxC,SAAd,EAAyB;AACrB,cAAMyC,SAAS,EAAf;;AAEA,YAAI,CAACzC,UAAUb,aAAf,EAA8B;AAC1BsD,mBAAOb,IAAP,CAAY,WAAZ;AACH;;AAED,YAAI,CAAC5B,UAAUX,iBAAf,EAAkC;AAC9BoD,mBAAOb,IAAP,CAAY,SAAZ;AACH;;AAED,YAAI,CAAC5B,UAAUP,WAAf,EAA4B;AACxBgD,mBAAOb,IAAP,CAAY,SAAZ;AACH;;AAED;AACA,YAAI5B,UAAUb,aAAV,KAA4B,iBAAhC,EAAmD;AAC/C,kBAAMJ,aAAaiB,UAAUjB,UAAV,IAAwB,EAA3C;AACA,kBAAM2D,eAAe3D,WAAWuD,eAAX,IACFvD,WAAWmC,aADT,IAEFnC,WAAWkD,aAF9B;;AAIA,gBAAI,CAACS,YAAL,EAAmB;AACfD,uBAAOb,IAAP,CAAY,aAAZ;AACH;;AAED,gBAAI7C,WAAWmC,aAAf,EAA8B;AAC1B,oBAAI,CAACnC,WAAW4C,SAAZ,IAAyB5C,WAAW4C,SAAX,GAAuB,IAApD,EAA0D;AACtDc,2BAAOb,IAAP,CAAY,gBAAZ;AACH;AACJ;;AAED,gBAAI7C,WAAWkD,aAAf,EAA8B;AAC1B,oBAAI,CAAClD,WAAWsD,SAAZ,IAAyBtD,WAAWsD,SAAX,GAAuB,CAApD,EAAuD;AACnDI,2BAAOb,IAAP,CAAY,aAAZ;AACH;AACJ;AACJ;;AAED,eAAO;AACHe,qBAASF,OAAOG,MAAP,KAAkB,CADxB;AAEHH,oBAAQA;AAFL,SAAP;AAIH;AAjOsC,CAA3C", "file": "..\\..\\..\\src\\admin\\model\\recruitment_rules.js", "sourcesContent": ["module.exports = class extends think.Model {\n    /**\n     * 获取招募规则\n     * @returns {Promise.<*>}\n     */\n    async getRecruitmentRules() {\n        const rules = await this.find();\n        \n        if (rules) {\n            // 解析JSON字段\n            if (rules.conditions) {\n                try {\n                    rules.conditions = JSON.parse(rules.conditions);\n                } catch (e) {\n                    rules.conditions = {};\n                }\n            }\n            \n            // 转换字段名\n            return {\n                joinCondition: rules.join_condition,\n                conditions: rules.conditions || {},\n                applicationMethod: rules.application_method,\n                requireApplicationInfo: rules.require_application_info === 1,\n                auditMethod: rules.audit_method,\n                addTime: rules.add_time,\n                updateTime: rules.update_time\n            };\n        }\n        \n        return null;\n    }\n\n    /**\n     * 保存招募规则\n     * @param rulesData\n     * @returns {Promise.<*>}\n     */\n    async saveRecruitmentRules(rulesData) {\n        const currentTime = parseInt(new Date().getTime() / 1000);\n        \n        const saveData = {\n            join_condition: rulesData.joinCondition,\n            conditions: JSON.stringify(rulesData.conditions || {}),\n            application_method: rulesData.applicationMethod,\n            require_application_info: rulesData.requireApplicationInfo ? 1 : 0,\n            audit_method: rulesData.auditMethod,\n            update_time: currentTime\n        };\n        \n        // 检查是否已存在规则\n        const existingRules = await this.find();\n        \n        if (existingRules && existingRules.id) {\n            // 更新现有规则\n            return await this.where({ id: existingRules.id }).update(saveData);\n        } else {\n            // 创建新规则\n            saveData.add_time = currentTime;\n            return await this.add(saveData);\n        }\n    }\n\n    /**\n     * 检查用户是否满足招募条件\n     * @param userId\n     * @returns {Promise.<*>}\n     */\n    async checkUserConditions(userId) {\n        const rules = await this.getRecruitmentRules();\n        \n        if (!rules || rules.joinCondition === 'no_conditions') {\n            return {\n                canJoin: true,\n                message: '无条件加入'\n            };\n        }\n        \n        const conditions = rules.conditions || {};\n        const checkResults = {\n            canJoin: true,\n            conditions: [],\n            failedConditions: []\n        };\n        \n        // 检查自购金额条件\n        if (conditions.requireAmount) {\n            const userOrders = await this.model('order').where({\n                user_id: userId,\n                order_status: ['>=', 300] // 已支付订单\n            }).sum('actual_price');\n            \n            const totalAmount = parseFloat(userOrders || 0);\n            const requiredAmount = parseFloat(conditions.minAmount || 0);\n            \n            if (totalAmount >= requiredAmount) {\n                checkResults.conditions.push({\n                    type: 'amount',\n                    required: requiredAmount,\n                    current: totalAmount,\n                    passed: true\n                });\n            } else {\n                checkResults.conditions.push({\n                    type: 'amount',\n                    required: requiredAmount,\n                    current: totalAmount,\n                    passed: false\n                });\n                checkResults.failedConditions.push('自购金额不足');\n                checkResults.canJoin = false;\n            }\n        }\n        \n        // 检查消费笔数条件\n        if (conditions.requireOrders) {\n            const orderCount = await this.model('order').where({\n                user_id: userId,\n                order_status: ['>=', 300] // 已支付订单\n            }).count();\n            \n            const requiredOrders = parseInt(conditions.minOrders || 0);\n            \n            if (orderCount >= requiredOrders) {\n                checkResults.conditions.push({\n                    type: 'orders',\n                    required: requiredOrders,\n                    current: orderCount,\n                    passed: true\n                });\n            } else {\n                checkResults.conditions.push({\n                    type: 'orders',\n                    required: requiredOrders,\n                    current: orderCount,\n                    passed: false\n                });\n                checkResults.failedConditions.push('消费笔数不足');\n                checkResults.canJoin = false;\n            }\n        }\n        \n        // 检查指定商品购买条件\n        if (conditions.requirePurchase) {\n            // 这里可以添加指定商品购买检查逻辑\n            // 暂时设为通过\n            checkResults.conditions.push({\n                type: 'purchase',\n                required: '指定商品',\n                current: '已购买',\n                passed: true\n            });\n        }\n        \n        return checkResults;\n    }\n\n    /**\n     * 获取默认招募规则\n     * @returns {Object}\n     */\n    getDefaultRules() {\n        return {\n            joinCondition: 'with_conditions',\n            conditions: {\n                requirePurchase: false,\n                requireAmount: true,\n                minAmount: 99.00,\n                requireOrders: false,\n                minOrders: 1\n            },\n            applicationMethod: 'manual_apply',\n            requireApplicationInfo: false,\n            auditMethod: 'auto_audit'\n        };\n    }\n\n    /**\n     * 验证招募规则数据\n     * @param rulesData\n     * @returns {Object}\n     */\n    validateRules(rulesData) {\n        const errors = [];\n        \n        if (!rulesData.joinCondition) {\n            errors.push('请选择加入条件类型');\n        }\n        \n        if (!rulesData.applicationMethod) {\n            errors.push('请选择申请方式');\n        }\n        \n        if (!rulesData.auditMethod) {\n            errors.push('请选择审核方式');\n        }\n        \n        // 如果是有条件加入，验证条件设置\n        if (rulesData.joinCondition === 'with_conditions') {\n            const conditions = rulesData.conditions || {};\n            const hasCondition = conditions.requirePurchase || \n                               conditions.requireAmount || \n                               conditions.requireOrders;\n            \n            if (!hasCondition) {\n                errors.push('请至少设置一个加入条件');\n            }\n            \n            if (conditions.requireAmount) {\n                if (!conditions.minAmount || conditions.minAmount < 0.01) {\n                    errors.push('自购金额最低设置为0.01元');\n                }\n            }\n            \n            if (conditions.requireOrders) {\n                if (!conditions.minOrders || conditions.minOrders < 1) {\n                    errors.push('消费笔数最低设置为1笔');\n                }\n            }\n        }\n        \n        return {\n            isValid: errors.length === 0,\n            errors: errors\n        };\n    }\n};\n"]}