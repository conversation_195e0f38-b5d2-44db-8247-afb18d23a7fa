{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\index.js"], "names": ["Base", "require", "moment", "module", "exports", "checkLoginAction", "think", "userId", "fail", "indexAction", "goodsOnsale", "model", "where", "is_on_sale", "is_delete", "count", "orderToDelivery", "order_status", "user", "data", "field", "find", "timestamp", "countdown", "info", "success", "getQiniuTokenAction", "TokenSerivce", "service", "getQiniuToken", "qiniuToken", "uploadToken", "domain", "token", "url", "mainAction", "index", "get", "console", "log", "todayTimeStamp", "Date", "setHours", "yesTimeStamp", "sevenTimeStamp", "thirtyTimeStamp", "newUser", "old<PERSON>ser", "addCart", "addOrderNum", "addOrderSum", "payOrderNum", "payOrderSum", "newData", "oldData", "id", "register_time", "select", "length", "item", "nickname", "<PERSON><PERSON><PERSON>", "from", "toString", "avatar", "startsWith", "last_login_time", "add_time", "sum", "unix", "format", "getDataOverviewAction", "timeRange", "startTime", "endTime", "timeCondition", "now", "Math", "floor", "todayStart", "yesterdayStart", "sevenDaysAgo", "thirtyDaysAgo", "parseInt", "paymentAmount", "pay_time", "orderCount", "visitorCount", "group", "salesVolume", "alias", "join", "table", "as", "on", "conversionRate", "toFixed", "avgOrderValue", "yesterdayCondition", "yesterdayEnd", "prevStartTime", "prevEndTime", "prevPaymentAmount", "prevOrderCount", "prevVisitorCount", "prevSalesVolume", "prevConversionRate", "prevAvgOrderValue", "calculateChange", "current", "previous", "overview", "parseFloat", "paymentAmountChange", "orderCountChange", "visitorCountChange", "conversionRateChange", "salesVolumeChange", "avgOrderValueChange", "error", "getPaymentChartAction", "chartData", "i", "hourStart", "hourEnd", "amount", "push", "time", "padStart", "days", "startTimestamp", "ceil", "dayStart", "dayEnd", "date", "getMonth", "getDate"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;;AAEAE,OAAOC,OAAP,GAAiB,cAAcJ,IAAd,CAAmB;AAC7BK,oBAAN,GAAwB;AAAA;;AAAA;AACvB,gBAAGC,MAAMC,MAAN,IAAgB,CAAnB,EAAqB;AACpB,uBAAO,MAAKC,IAAL,CAAU,GAAV,EAAc,KAAd,CAAP;AACA;AAHsB;AAIvB;AACQC,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAMC,cAAc,MAAM,OAAKC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B,EAACC,YAAY,CAAb,EAAeC,WAAU,CAAzB,EAA1B,EAAuDC,KAAvD,EAA1B;AACA,kBAAMC,kBAAkB,MAAM,OAAKL,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B,EAACK,cAAc,GAAf,EAA1B,EAA+CF,KAA/C,EAA9B;AACA,kBAAMG,OAAO,MAAM,OAAKP,KAAL,CAAW,MAAX,EAAmBI,KAAnB,EAAnB;AACA,gBAAII,OAAO,MAAM,OAAKR,KAAL,CAAW,UAAX,EAAuBS,KAAvB,CAA6B,WAA7B,EAA0CC,IAA1C,EAAjB;AACA,gBAAIC,YAAYH,KAAKI,SAArB;AACA,gBAAIC,OAAO;AACPN,sBAAMA,IADC;AAEPR,6BAAaA,WAFN;AAGPY,2BAAUA,SAHH;AAIPN,iCAAiBA;AAJV,aAAX;AAMA,mBAAO,OAAKS,OAAL,CAAaD,IAAb,CAAP;AAZgB;AAanB;AACKE,uBAAN,GAA2B;AAAA;;AAAA;AACvB,kBAAMC,eAAe,OAAKC,OAAL,CAAa,OAAb,CAArB,CADuB,CACqB;AAC5C,gBAAIT,OAAO,MAAMQ,aAAaE,aAAb,EAAjB,CAFuB,CAEwB;AAC/C,gBAAIC,aAAaX,KAAKY,WAAtB;AACA,gBAAIC,SAASb,KAAKa,MAAlB;AACA,gBAAIR,OAAM;AACNS,uBAAMH,UADA;AAENI,qBAAIF;AAFE,aAAV;AAIA,mBAAO,OAAKP,OAAL,CAAaD,IAAb,CAAP;AATuB;AAU1B;AACKW,cAAN,GAAmB;AAAA;;AAAA;AACf,kBAAMC,QAAQ,OAAKC,GAAL,CAAS,QAAT,CAAd;AACAC,oBAAQC,GAAR,CAAY,WAAWH,KAAvB;AACA,gBAAII,iBAAiB,IAAIC,IAAJ,CAAS,IAAIA,IAAJ,GAAWC,QAAX,CAAoB,CAApB,EAAuB,CAAvB,EAA0B,CAA1B,EAA6B,CAA7B,CAAT,IAA4C,IAAjE,CAHe,CAGwD;AACvE,gBAAIC,eAAeH,iBAAiB,KAApC,CAJe,CAI4B;AAC3C,gBAAII,iBAAiBJ,iBAAiB,QAAQ,CAA9C,CALe,CAKkC;AACjD,gBAAIK,kBAAkBL,iBAAiB,QAAQ,EAA/C,CANe,CAMoC;AACnD,gBAAIM,UAAU,CAAd;AACA,gBAAIC,UAAU,CAAd;AACA,gBAAIC,UAAU,CAAd;AACA,gBAAIC,cAAc,CAAlB;AACA,gBAAIC,cAAc,CAAlB;AACA,gBAAIC,cAAc,CAAlB;AACA,gBAAIC,cAAc,CAAlB;AACA,gBAAIC,UAAU,EAAd;AACA,gBAAIC,UAAU,EAAd;AACA,gBAAIlB,SAAS,CAAb,EAAgB;AACZiB,0BAAU,MAAM,OAAK1C,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACrC2C,wBAAI,CAAC,GAAD,EAAM,CAAN,CADiC;AAErCC,mCAAe,CAAC,GAAD,EAAMhB,cAAN;AAFsB,iBAAzB,EAGbiB,MAHa,EAAhB;AAIAX,0BAAUO,QAAQK,MAAlB;AACA,qBAAI,MAAMC,IAAV,IAAkBN,OAAlB,EAA0B;AACtBM,yBAAKC,QAAL,GAAgBC,OAAOC,IAAP,CAAYH,KAAKC,QAAjB,EAA2B,QAA3B,EAAqCG,QAArC,EAAhB;AACA;AACA,wBAAI,CAACJ,KAAKK,MAAN,IAAgBL,KAAKK,MAAL,KAAgB,EAAhC,IAAsCL,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,WAAvB,CAA1C,EAA+E;AAC3E;AACAN,6BAAKK,MAAL,GAAc,2DAAd;AACH,qBAHD,MAGO,IAAI,CAACL,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,SAAvB,CAAD,IAAsC,CAACN,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,UAAvB,CAA3C,EAA+E;AAClF;AACAN,6BAAKK,MAAL,GAAe,yBAAwBL,KAAKK,MAAO,EAAnD;AACH;AACJ;AACDV,0BAAU,MAAM,OAAK3C,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACrC2C,wBAAI,CAAC,GAAD,EAAM,CAAN,CADiC;AAErCC,mCAAe,CAAC,GAAD,EAAMhB,cAAN,CAFsB;AAGrC0B,qCAAiB,CAAC,GAAD,EAAM1B,cAAN;AAHoB,iBAAzB,EAIbiB,MAJa,EAAhB;AAKA,qBAAI,MAAME,IAAV,IAAkBL,OAAlB,EAA0B;AACtBK,yBAAKC,QAAL,GAAgBC,OAAOC,IAAP,CAAYH,KAAKC,QAAjB,EAA2B,QAA3B,EAAqCG,QAArC,EAAhB;AACA;AACA,wBAAI,CAACJ,KAAKK,MAAN,IAAgBL,KAAKK,MAAL,KAAgB,EAAhC,IAAsCL,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,WAAvB,CAA1C,EAA+E;AAC3E;AACAN,6BAAKK,MAAL,GAAc,2DAAd;AACH,qBAHD,MAGO,IAAI,CAACL,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,SAAvB,CAAD,IAAsC,CAACN,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,UAAvB,CAA3C,EAA+E;AAClF;AACAN,6BAAKK,MAAL,GAAe,yBAAwBL,KAAKK,MAAO,EAAnD;AACH;AACJ;AACDjB,0BAAUO,QAAQI,MAAlB;AACAV,0BAAU,MAAM,OAAKrC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB,EAACE,WAAW,CAAZ,EAAeqD,UAAU,CAAC,GAAD,EAAM3B,cAAN,CAAzB,EAAzB,EAA0EzB,KAA1E,EAAhB;AACAkC,8BAAc,MAAM,OAAKtC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1CE,+BAAW,CAD+B;AAE1CqD,8BAAU,CAAC,GAAD,EAAM3B,cAAN;AAFgC,iBAA1B,EAGjBzB,KAHiB,EAApB;AAIAmC,8BAAc,MAAM,OAAKvC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1CE,+BAAW,CAD+B;AAE1CqD,8BAAU,CAAC,GAAD,EAAM3B,cAAN;AAFgC,iBAA1B,EAGjB4B,GAHiB,CAGb,cAHa,CAApB;AAIAjB,8BAAc,MAAM,OAAKxC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1CE,+BAAW,CAD+B;AAE1CqD,8BAAU,CAAC,GAAD,EAAM3B,cAAN,CAFgC;AAG1CvB,kCAAc,CAAC,IAAD,EAAO,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAAP;AAH4B,iBAA1B,EAIjBF,KAJiB,EAApB;AAKAqC,8BAAc,MAAM,OAAKzC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1CE,+BAAW,CAD+B;AAE1CqD,8BAAU,CAAC,GAAD,EAAM3B,cAAN,CAFgC;AAG1CvB,kCAAc,CAAC,IAAD,EAAO,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAAP;AAH4B,iBAA1B,EAIjBmD,GAJiB,CAIb,cAJa,CAApB;AAKH,aArDD,MAsDK,IAAIhC,SAAS,CAAb,EAAgB;AACjBiB,0BAAU,MAAM,OAAK1C,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACrC2C,wBAAI,CAAC,GAAD,EAAM,CAAN,CADiC;AAErCC,mCAAe,CAAC,SAAD,EAAYb,YAAZ,EAA0BH,cAA1B;AAFsB,iBAAzB,EAGbiB,MAHa,EAAhB;AAIA,qBAAI,MAAME,IAAV,IAAkBN,OAAlB,EAA0B;AACtBM,yBAAKC,QAAL,GAAgBC,OAAOC,IAAP,CAAYH,KAAKC,QAAjB,EAA2B,QAA3B,EAAqCG,QAArC,EAAhB;AACA;AACA,wBAAI,CAACJ,KAAKK,MAAN,IAAgBL,KAAKK,MAAL,KAAgB,EAAhC,IAAsCL,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,WAAvB,CAA1C,EAA+E;AAC3E;AACAN,6BAAKK,MAAL,GAAc,2DAAd;AACH,qBAHD,MAGO,IAAI,CAACL,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,SAAvB,CAAD,IAAsC,CAACN,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,UAAvB,CAA3C,EAA+E;AAClF;AACAN,6BAAKK,MAAL,GAAe,yBAAwBL,KAAKK,MAAO,EAAnD;AACH;AACJ;AACDlB,0BAAUO,QAAQK,MAAlB;AACAJ,0BAAU,MAAM,OAAK3C,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACrC2C,wBAAI,CAAC,GAAD,EAAM,CAAN,CADiC;AAErCC,mCAAe,CAAC,GAAD,EAAMb,YAAN,CAFsB;AAGrCuB,qCAAiB,CAAC,SAAD,EAAYvB,YAAZ,EAA0BH,cAA1B;AAHoB,iBAAzB,EAIbiB,MAJa,EAAhB;AAKA,qBAAI,MAAME,IAAV,IAAkBL,OAAlB,EAA0B;AACtBK,yBAAKC,QAAL,GAAgBC,OAAOC,IAAP,CAAYH,KAAKC,QAAjB,EAA2B,QAA3B,EAAqCG,QAArC,EAAhB;AACA;AACA,wBAAI,CAACJ,KAAKK,MAAN,IAAgBL,KAAKK,MAAL,KAAgB,EAAhC,IAAsCL,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,WAAvB,CAA1C,EAA+E;AAC3E;AACAN,6BAAKK,MAAL,GAAc,2DAAd;AACH,qBAHD,MAGO,IAAI,CAACL,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,SAAvB,CAAD,IAAsC,CAACN,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,UAAvB,CAA3C,EAA+E;AAClF;AACAN,6BAAKK,MAAL,GAAe,yBAAwBL,KAAKK,MAAO,EAAnD;AACH;AACJ;AACDjB,0BAAUO,QAAQI,MAAlB;AACAV,0BAAU,MAAM,OAAKrC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACrCE,+BAAW,CAD0B;AAErCqD,8BAAU,CAAC,SAAD,EAAYxB,YAAZ,EAA0BH,cAA1B;AAF2B,iBAAzB,EAGbzB,KAHa,EAAhB;AAIAkC,8BAAc,MAAM,OAAKtC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1CE,+BAAW,CAD+B;AAE1CqD,8BAAU,CAAC,SAAD,EAAYxB,YAAZ,EAA0BH,cAA1B;AAFgC,iBAA1B,EAGjBzB,KAHiB,EAApB;AAIAmC,8BAAc,MAAM,OAAKvC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1CE,+BAAW,CAD+B;AAE1CqD,8BAAU,CAAC,SAAD,EAAYxB,YAAZ,EAA0BH,cAA1B;AAFgC,iBAA1B,EAGjB4B,GAHiB,CAGb,cAHa,CAApB;AAIAjB,8BAAc,MAAM,OAAKxC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1CE,+BAAW,CAD+B;AAE1CqD,8BAAU,CAAC,SAAD,EAAYxB,YAAZ,EAA0BH,cAA1B,CAFgC;AAG1CvB,kCAAc,CAAC,IAAD,EAAO,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAAP;AAH4B,iBAA1B,EAIjBF,KAJiB,EAApB;AAKAuB,wBAAQC,GAAR,CAAY,2BAAZ;AACAD,wBAAQC,GAAR,CAAYY,WAAZ;AACAb,wBAAQC,GAAR,CAAY,4BAAZ;AACAa,8BAAc,MAAM,OAAKzC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1CE,+BAAW,CAD+B;AAE1CqD,8BAAU,CAAC,SAAD,EAAYxB,YAAZ,EAA0BH,cAA1B,CAFgC;AAG1CvB,kCAAc,CAAC,IAAD,EAAO,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAAP;AAH4B,iBAA1B,EAIjBmD,GAJiB,CAIb,cAJa,CAApB;AAKA9B,wBAAQC,GAAR,CAAY,2BAAZ;AACAD,wBAAQC,GAAR,CAAYa,WAAZ;AACAd,wBAAQC,GAAR,CAAY,2BAAZ;AAEH,aA/DI,MAgEA,IAAIH,SAAS,CAAb,EAAgB;AACjBiB,0BAAU,MAAM,OAAK1C,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACrC2C,wBAAI,CAAC,GAAD,EAAM,CAAN,CADiC;AAErCC,mCAAe,CAAC,GAAD,EAAMZ,cAAN;AAFsB,iBAAzB,EAGba,MAHa,EAAhB;AAIA,qBAAI,MAAME,IAAV,IAAkBN,OAAlB,EAA0B;AACtBM,yBAAKC,QAAL,GAAgBC,OAAOC,IAAP,CAAYH,KAAKC,QAAjB,EAA2B,QAA3B,EAAqCG,QAArC,EAAhB;AACA;AACA,wBAAI,CAACJ,KAAKK,MAAN,IAAgBL,KAAKK,MAAL,KAAgB,EAAhC,IAAsCL,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,WAAvB,CAA1C,EAA+E;AAC3E;AACAN,6BAAKK,MAAL,GAAc,2DAAd;AACH,qBAHD,MAGO,IAAI,CAACL,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,SAAvB,CAAD,IAAsC,CAACN,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,UAAvB,CAA3C,EAA+E;AAClF;AACAN,6BAAKK,MAAL,GAAe,yBAAwBL,KAAKK,MAAO,EAAnD;AACH;AACJ;AACDlB,0BAAUO,QAAQK,MAAlB;AACAJ,0BAAU,MAAM,OAAK3C,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACrC2C,wBAAI,CAAC,GAAD,EAAM,CAAN,CADiC;AAErCC,mCAAe,CAAC,GAAD,EAAMZ,cAAN,CAFsB;AAGrCsB,qCAAiB,CAAC,GAAD,EAAMtB,cAAN;AAHoB,iBAAzB,EAIba,MAJa,EAAhB;AAKA,qBAAI,MAAME,IAAV,IAAkBL,OAAlB,EAA0B;AACtBK,yBAAKC,QAAL,GAAgBC,OAAOC,IAAP,CAAYH,KAAKC,QAAjB,EAA2B,QAA3B,EAAqCG,QAArC,EAAhB;AACA;AACA,wBAAI,CAACJ,KAAKK,MAAN,IAAgBL,KAAKK,MAAL,KAAgB,EAAhC,IAAsCL,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,WAAvB,CAA1C,EAA+E;AAC3E;AACAN,6BAAKK,MAAL,GAAc,2DAAd;AACH,qBAHD,MAGO,IAAI,CAACL,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,SAAvB,CAAD,IAAsC,CAACN,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,UAAvB,CAA3C,EAA+E;AAClF;AACAN,6BAAKK,MAAL,GAAe,yBAAwBL,KAAKK,MAAO,EAAnD;AACH;AACJ;AACDjB,0BAAUO,QAAQI,MAAlB;AACAV,0BAAU,MAAM,OAAKrC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACrCE,+BAAW,CAD0B;AAErCqD,8BAAU,CAAC,GAAD,EAAMvB,cAAN;AAF2B,iBAAzB,EAGb7B,KAHa,EAAhB;AAIAkC,8BAAc,MAAM,OAAKtC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1CE,+BAAW,CAD+B;AAE1CqD,8BAAU,CAAC,GAAD,EAAMvB,cAAN;AAFgC,iBAA1B,EAGjB7B,KAHiB,EAApB;AAIAmC,8BAAc,MAAM,OAAKvC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1CE,+BAAW,CAD+B;AAE1CqD,8BAAU,CAAC,GAAD,EAAMvB,cAAN;AAFgC,iBAA1B,EAGjBwB,GAHiB,CAGb,cAHa,CAApB;AAIAjB,8BAAc,MAAM,OAAKxC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1CE,+BAAW,CAD+B;AAE1CqD,8BAAU,CAAC,GAAD,EAAMvB,cAAN,CAFgC;AAG1C3B,kCAAc,CAAC,IAAD,EAAO,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAAP;AAH4B,iBAA1B,EAIjBF,KAJiB,EAApB;AAKAqC,8BAAc,MAAM,OAAKzC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1CE,+BAAW,CAD+B;AAE1CqD,8BAAU,CAAC,GAAD,EAAMvB,cAAN,CAFgC;AAG1C3B,kCAAc,CAAC,IAAD,EAAO,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAAP;AAH4B,iBAA1B,EAIjBmD,GAJiB,CAIb,cAJa,CAApB;AAKH,aAxDI,MAyDA,IAAIhC,SAAS,CAAb,EAAgB;AACjBiB,0BAAU,MAAM,OAAK1C,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACrC2C,wBAAI,CAAC,GAAD,EAAM,CAAN,CADiC;AAErCC,mCAAe,CAAC,GAAD,EAAMX,eAAN;AAFsB,iBAAzB,EAGbY,MAHa,EAAhB;AAIA,qBAAI,MAAME,IAAV,IAAkBN,OAAlB,EAA0B;AACtBM,yBAAKC,QAAL,GAAgBC,OAAOC,IAAP,CAAYH,KAAKC,QAAjB,EAA2B,QAA3B,EAAqCG,QAArC,EAAhB;AACA;AACA,wBAAI,CAACJ,KAAKK,MAAN,IAAgBL,KAAKK,MAAL,KAAgB,EAAhC,IAAsCL,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,WAAvB,CAA1C,EAA+E;AAC3E;AACAN,6BAAKK,MAAL,GAAc,2DAAd;AACH,qBAHD,MAGO,IAAI,CAACL,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,SAAvB,CAAD,IAAsC,CAACN,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,UAAvB,CAA3C,EAA+E;AAClF;AACAN,6BAAKK,MAAL,GAAe,yBAAwBL,KAAKK,MAAO,EAAnD;AACH;AACJ;AACDlB,0BAAUO,QAAQK,MAAlB;AACAJ,0BAAU,MAAM,OAAK3C,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACrC2C,wBAAI,CAAC,GAAD,EAAM,CAAN,CADiC;AAErCC,mCAAe,CAAC,GAAD,EAAMX,eAAN,CAFsB;AAGrCqB,qCAAiB,CAAC,GAAD,EAAMrB,eAAN;AAHoB,iBAAzB,EAIbY,MAJa,EAAhB;AAKA,qBAAI,MAAME,IAAV,IAAkBL,OAAlB,EAA0B;AACtBK,yBAAKC,QAAL,GAAgBC,OAAOC,IAAP,CAAYH,KAAKC,QAAjB,EAA2B,QAA3B,EAAqCG,QAArC,EAAhB;AACA;AACA,wBAAI,CAACJ,KAAKK,MAAN,IAAgBL,KAAKK,MAAL,KAAgB,EAAhC,IAAsCL,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,WAAvB,CAA1C,EAA+E;AAC3E;AACAN,6BAAKK,MAAL,GAAc,2DAAd;AACH,qBAHD,MAGO,IAAI,CAACL,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,SAAvB,CAAD,IAAsC,CAACN,KAAKK,MAAL,CAAYC,UAAZ,CAAuB,UAAvB,CAA3C,EAA+E;AAClF;AACAN,6BAAKK,MAAL,GAAe,yBAAwBL,KAAKK,MAAO,EAAnD;AACH;AACJ;AACDjB,0BAAUO,QAAQI,MAAlB;AACAV,0BAAU,MAAM,OAAKrC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACrCE,+BAAW,CAD0B;AAErCqD,8BAAU,CAAC,GAAD,EAAMtB,eAAN;AAF2B,iBAAzB,EAGb9B,KAHa,EAAhB;AAIAkC,8BAAc,MAAM,OAAKtC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1CE,+BAAW,CAD+B;AAE1CqD,8BAAU,CAAC,GAAD,EAAMtB,eAAN;AAFgC,iBAA1B,EAGjB9B,KAHiB,EAApB;AAIAmC,8BAAc,MAAM,OAAKvC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1CE,+BAAW,CAD+B;AAE1CqD,8BAAU,CAAC,GAAD,EAAMtB,eAAN;AAFgC,iBAA1B,EAGjBuB,GAHiB,CAGb,cAHa,CAApB;AAIAjB,8BAAc,MAAM,OAAKxC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1CE,+BAAW,CAD+B;AAE1CqD,8BAAU,CAAC,GAAD,EAAMtB,eAAN,CAFgC;AAG1C5B,kCAAc,CAAC,IAAD,EAAO,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAAP;AAH4B,iBAA1B,EAIjBF,KAJiB,EAApB;AAKAqC,8BAAc,MAAM,OAAKzC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1CE,+BAAW,CAD+B;AAE1CqD,8BAAU,CAAC,GAAD,EAAMtB,eAAN,CAFgC;AAG1C5B,kCAAc,CAAC,IAAD,EAAO,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAAP;AAH4B,iBAA1B,EAIjBmD,GAJiB,CAIb,cAJa,CAApB;AAKH;AACD,gBAAIlB,eAAe,IAAnB,EAAyB;AACrBA,8BAAc,CAAd;AACH;AACD,gBAAIE,eAAe,IAAnB,EAAyB;AACrBA,8BAAc,CAAd;AACH;AACD,gBAAGC,QAAQK,MAAR,GAAiB,CAApB,EAAsB;AAClB,qBAAI,MAAMC,IAAV,IAAkBN,OAAlB,EAA0B;AACtBM,yBAAKH,aAAL,GAAsBtD,OAAOmE,IAAP,CAAYV,KAAKH,aAAjB,EAAgCc,MAAhC,CAAuC,qBAAvC,CAAtB;AACAX,yBAAKO,eAAL,GAAwBhE,OAAOmE,IAAP,CAAYV,KAAKO,eAAjB,EAAkCI,MAAlC,CAAyC,qBAAzC,CAAxB;AACH;AACJ;;AAED,gBAAGhB,QAAQI,MAAR,GAAiB,CAApB,EAAsB;AAClB,qBAAI,MAAMC,IAAV,IAAkBL,OAAlB,EAA0B;AACtBK,yBAAKH,aAAL,GAAsBtD,OAAOmE,IAAP,CAAYV,KAAKH,aAAjB,EAAgCc,MAAhC,CAAuC,qBAAvC,CAAtB;AACAX,yBAAKO,eAAL,GAAwBhE,OAAOmE,IAAP,CAAYV,KAAKO,eAAjB,EAAkCI,MAAlC,CAAyC,qBAAzC,CAAxB;AACH;AACJ;;AAED,gBAAI9C,OAAO;AACPsB,yBAASA,OADF;AAEPC,yBAASA,OAFF;AAGPC,yBAASA,OAHF;AAIPK,yBAASA,OAJF;AAKPC,yBAASA,OALF;AAMPL,6BAAaA,WANN;AAOPC,6BAAaA,WAPN;AAQPC,6BAAaA,WARN;AASPC,6BAAaA;AATN,aAAX;AAWA,mBAAO,OAAK3B,OAAL,CAAaD,IAAb,CAAP;AAvRe;AAwRlB;;AAED;AACM+C,yBAAN,GAA8B;AAAA;;AAAA;AAC1B,gBAAI;AACA,sBAAMC,YAAY,OAAKnC,GAAL,CAAS,WAAT,KAAyB,OAA3C,CADA,CACoD;AACpD,sBAAMoC,YAAY,OAAKpC,GAAL,CAAS,WAAT,CAAlB;AACA,sBAAMqC,UAAU,OAAKrC,GAAL,CAAS,SAAT,CAAhB;;AAEA,oBAAIsC,gBAAgB,EAApB;AACA,sBAAMC,MAAMC,KAAKC,KAAL,CAAWrC,KAAKmC,GAAL,KAAa,IAAxB,CAAZ;AACA,sBAAMG,aAAaF,KAAKC,KAAL,CAAW,IAAIrC,IAAJ,GAAWC,QAAX,CAAoB,CAApB,EAAuB,CAAvB,EAA0B,CAA1B,EAA6B,CAA7B,IAAkC,IAA7C,CAAnB;AACA,sBAAMsC,iBAAiBD,aAAa,KAApC;AACA,sBAAME,eAAeF,aAAa,QAAQ,CAA1C;AACA,sBAAMG,gBAAgBH,aAAa,QAAQ,EAA3C;;AAEA;AACA,wBAAQP,SAAR;AACI,yBAAK,OAAL;AACIG,wCAAgB,CAAC,IAAD,EAAOI,UAAP,CAAhB;AACA;AACJ,yBAAK,WAAL;AACIJ,wCAAgB,CAAC,SAAD,EAAYK,cAAZ,EAA4BD,aAAa,CAAzC,CAAhB;AACA;AACJ,yBAAK,OAAL;AACIJ,wCAAgB,CAAC,IAAD,EAAOM,YAAP,CAAhB;AACA;AACJ,yBAAK,QAAL;AACIN,wCAAgB,CAAC,IAAD,EAAOO,aAAP,CAAhB;AACA;AACJ,yBAAK,QAAL;AACI,4BAAIT,aAAaC,OAAjB,EAA0B;AACtBC,4CAAgB,CAAC,SAAD,EAAYQ,SAASV,SAAT,CAAZ,EAAiCU,SAAST,OAAT,CAAjC,CAAhB;AACH,yBAFD,MAEO;AACHC,4CAAgB,CAAC,IAAD,EAAOI,UAAP,CAAhB;AACH;AACD;AACJ;AACIJ,wCAAgB,CAAC,IAAD,EAAOI,UAAP,CAAhB;AArBR;;AAwBA;AACA,sBAAMK,gBAAgB,OAAM,OAAKzE,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAClDE,+BAAW,CADuC;AAElDG,kCAAc,CAAC,IAAD,EAAO,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAAP,CAFoC,EAEN;AAC5CoE,8BAAUV;AAHwC,iBAA1B,EAIzBP,GAJyB,CAIrB,cAJqB,CAAN,KAII,CAJ1B;;AAMA;AACA,sBAAMkB,aAAa,MAAM,OAAK3E,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC/CE,+BAAW,CADoC;AAE/CG,kCAAc,CAAC,IAAD,EAAO,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAAP,CAFiC;AAG/CoE,8BAAUV;AAHqC,iBAA1B,EAItB5D,KAJsB,EAAzB;;AAMA;AACA,sBAAMwE,eAAe,MAAM,OAAK5E,KAAL,CAAW,WAAX,EAAwBC,KAAxB,CAA8B;AACrDuD,8BAAUQ;AAD2C,iBAA9B,EAExBa,KAFwB,CAElB,SAFkB,EAEPzE,KAFO,EAA3B;;AAIA;AACA,sBAAM0E,cAAc,OAAM,OAAK9E,KAAL,CAAW,aAAX,EAA0B+E,KAA1B,CAAgC,IAAhC,EACrBC,IADqB,CAChB;AACFC,2BAAO,OADL;AAEFD,0BAAM,OAFJ;AAGFE,wBAAI,GAHF;AAIFC,wBAAI,CAAC,aAAD,EAAgB,MAAhB;AAJF,iBADgB,EAOrBlF,KAPqB,CAOf;AACH,oCAAgB,CADb;AAEH,mCAAe,CAFZ;AAGH,sCAAkB,CAAC,IAAD,EAAO,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAAP,CAHf;AAIH,kCAAc+D;AAJX,iBAPe,EAYnBP,GAZmB,CAYf,WAZe,CAAN,KAYO,CAZ3B;;AAcA;AACA,sBAAM2B,iBAAiBR,eAAe,CAAf,GAAmB,CAAED,aAAaC,YAAd,GAA8B,GAA/B,EAAoCS,OAApC,CAA4C,CAA5C,CAAnB,GAAoE,CAA3F;;AAEA;AACA,sBAAMC,gBAAgBX,aAAa,CAAb,GAAiB,CAACF,gBAAgBE,UAAjB,EAA6BU,OAA7B,CAAqC,CAArC,CAAjB,GAA2D,CAAjF;;AAEA;AACA,oBAAIE,qBAAqB,EAAzB;AACA,oBAAI1B,cAAc,OAAlB,EAA2B;AACvB,0BAAMQ,iBAAiBD,aAAa,KAApC;AACA,0BAAMoB,eAAepB,aAAa,CAAlC;AACAmB,yCAAqB,CAAC,SAAD,EAAYlB,cAAZ,EAA4BmB,YAA5B,CAArB;AACH,iBAJD,MAIO;AACH;AACA,wBAAIC,gBAAgBrB,aAAa,KAAjC;AACA,wBAAIsB,cAActB,aAAa,CAA/B;;AAEA,4BAAQP,SAAR;AACI,6BAAK,WAAL;AACI4B,4CAAgBrB,aAAa,QAAQ,CAArC;AACAsB,0CAActB,aAAa,KAAb,GAAqB,CAAnC;AACA;AACJ,6BAAK,OAAL;AACIqB,4CAAgBrB,aAAa,QAAQ,EAArC;AACAsB,0CAActB,aAAa,QAAQ,CAArB,GAAyB,CAAvC;AACA;AACJ,6BAAK,QAAL;AACIqB,4CAAgBrB,aAAa,QAAQ,EAArC;AACAsB,0CAActB,aAAa,QAAQ,EAArB,GAA0B,CAAxC;AACA;AAZR;AAcAmB,yCAAqB,CAAC,SAAD,EAAYE,aAAZ,EAA2BC,WAA3B,CAArB;AACH;;AAED;AACA,sBAAMC,oBAAoB,OAAM,OAAK3F,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AACtDE,+BAAW,CAD2C;AAEtDG,kCAAc,CAAC,IAAD,EAAO,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAAP,CAFwC;AAGtDoE,8BAAUa;AAH4C,iBAA1B,EAI7B9B,GAJ6B,CAIzB,cAJyB,CAAN,KAIA,CAJ1B;;AAMA,sBAAMmC,iBAAiB,MAAM,OAAK5F,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AACnDE,+BAAW,CADwC;AAEnDG,kCAAc,CAAC,IAAD,EAAO,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAAP,CAFqC;AAGnDoE,8BAAUa;AAHyC,iBAA1B,EAI1BnF,KAJ0B,EAA7B;;AAMA,sBAAMyF,mBAAmB,MAAM,OAAK7F,KAAL,CAAW,WAAX,EAAwBC,KAAxB,CAA8B;AACzDuD,8BAAU+B;AAD+C,iBAA9B,EAE5BV,KAF4B,CAEtB,SAFsB,EAEXzE,KAFW,EAA/B;;AAIA,sBAAM0F,kBAAkB,OAAM,OAAK9F,KAAL,CAAW,aAAX,EAA0B+E,KAA1B,CAAgC,IAAhC,EACzBC,IADyB,CACpB;AACFC,2BAAO,OADL;AAEFD,0BAAM,OAFJ;AAGFE,wBAAI,GAHF;AAIFC,wBAAI,CAAC,aAAD,EAAgB,MAAhB;AAJF,iBADoB,EAOzBlF,KAPyB,CAOnB;AACH,oCAAgB,CADb;AAEH,mCAAe,CAFZ;AAGH,sCAAkB,CAAC,IAAD,EAAO,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAAP,CAHf;AAIH,kCAAcsF;AAJX,iBAPmB,EAYvB9B,GAZuB,CAYnB,WAZmB,CAAN,KAYG,CAZ3B;;AAcA,sBAAMsC,qBAAqBF,mBAAmB,CAAnB,GAAyBD,iBAAiBC,gBAAlB,GAAsC,GAA9D,GAAqE,CAAhG;AACA,sBAAMG,oBAAoBJ,iBAAiB,CAAjB,GAAsBD,oBAAoBC,cAA1C,GAA4D,CAAtF;;AAEA;AACA,sBAAMK,kBAAkB,UAACC,OAAD,EAAUC,QAAV,EAAuB;AAC3C,wBAAIA,aAAa,CAAjB,EAAoB,OAAOD,UAAU,CAAV,GAAc,GAAd,GAAoB,CAA3B;AACpB,2BAAQ,CAACA,UAAUC,QAAX,IAAuBA,QAAvB,GAAkC,GAA1C;AACH,iBAHD;;AAKA,sBAAMC,WAAW;AACb3B,mCAAe4B,WAAW5B,aAAX,EAA0BY,OAA1B,CAAkC,CAAlC,CADF;AAEbiB,yCAAqBL,gBAAgBxB,aAAhB,EAA+BkB,iBAA/B,EAAkDN,OAAlD,CAA0D,CAA1D,CAFR;AAGbV,8BAHa;AAIb4B,sCAAkBN,gBAAgBtB,UAAhB,EAA4BiB,cAA5B,EAA4CP,OAA5C,CAAoD,CAApD,CAJL;AAKbT,gCALa;AAMb4B,wCAAoBP,gBAAgBrB,YAAhB,EAA8BiB,gBAA9B,EAAgDR,OAAhD,CAAwD,CAAxD,CANP;AAObD,oCAAgBiB,WAAWjB,cAAX,CAPH;AAQbqB,0CAAsBR,gBAAgBb,cAAhB,EAAgCW,kBAAhC,EAAoDV,OAApD,CAA4D,CAA5D,CART;AASbP,iCAAaN,SAASM,WAAT,CATA;AAUb4B,uCAAmBT,gBAAgBnB,WAAhB,EAA6BgB,eAA7B,EAA8CT,OAA9C,CAAsD,CAAtD,CAVN;AAWbC,mCAAee,WAAWf,aAAX,CAXF;AAYbqB,yCAAqBV,gBAAgBX,aAAhB,EAA+BU,iBAA/B,EAAkDX,OAAlD,CAA0D,CAA1D;AAZR,iBAAjB;;AAeA,uBAAO,OAAKvE,OAAL,CAAasF,QAAb,CAAP;AACH,aAjKD,CAiKE,OAAOQ,KAAP,EAAc;AACZjF,wBAAQiF,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,OAAK/G,IAAL,CAAU,UAAV,CAAP;AACH;AArKyB;AAsK7B;;AAED;AACMgH,yBAAN,GAA8B;AAAA;;AAAA;AAC1B,gBAAI;AACA,sBAAMhD,YAAY,OAAKnC,GAAL,CAAS,WAAT,KAAyB,OAA3C;AACA,sBAAMoC,YAAY,OAAKpC,GAAL,CAAS,WAAT,CAAlB;AACA,sBAAMqC,UAAU,OAAKrC,GAAL,CAAS,SAAT,CAAhB;;AAEA,oBAAIoF,YAAY,EAAhB;AACA,sBAAM7C,MAAMC,KAAKC,KAAL,CAAWrC,KAAKmC,GAAL,KAAa,IAAxB,CAAZ;AACA,sBAAMG,aAAaF,KAAKC,KAAL,CAAW,IAAIrC,IAAJ,GAAWC,QAAX,CAAoB,CAApB,EAAuB,CAAvB,EAA0B,CAA1B,EAA6B,CAA7B,IAAkC,IAA7C,CAAnB;;AAEA,oBAAI8B,cAAc,OAAd,IAAyBA,cAAc,UAA3C,EAAuD;AACnD;AACA,yBAAK,IAAIkD,IAAI,CAAb,EAAgBA,IAAI,EAApB,EAAwBA,GAAxB,EAA6B;AACzB,8BAAMC,YAAY5C,aAAa2C,IAAI,IAAnC;AACA,8BAAME,UAAUD,YAAY,IAA5B;;AAEA,8BAAME,SAAS,OAAM,OAAKlH,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC3CE,uCAAW,CADgC;AAE3CG,0CAAc,CAAC,IAAD,EAAO,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAAP,CAF6B;AAG3CoE,sCAAU,CAAC,SAAD,EAAYsC,SAAZ,EAAuBC,OAAvB;AAHiC,yBAA1B,EAIlBxD,GAJkB,CAId,cAJc,CAAN,KAIW,CAJ1B;;AAMAqD,kCAAUK,IAAV,CAAe;AACXC,kCAAO,GAAEL,EAAE3D,QAAF,GAAaiE,QAAb,CAAsB,CAAtB,EAAyB,GAAzB,CAA8B,KAD5B;AAEXH,oCAAQb,WAAWa,MAAX,EAAmB7B,OAAnB,CAA2B,CAA3B,CAFG;AAGX1E,uCAAWqG;AAHA,yBAAf;AAKH;AACJ,iBAlBD,MAkBO;AACH;AACA,wBAAIM,OAAO,CAAX;AACA,wBAAIC,iBAAiBnD,UAArB;;AAEA,4BAAQP,SAAR;AACI,6BAAK,WAAL;AACIyD,mCAAO,CAAP;AACAC,6CAAiBnD,aAAa,KAA9B;AACA;AACJ,6BAAK,OAAL;AACIkD,mCAAO,CAAP;AACAC,6CAAiBnD,aAAa,QAAQ,CAAtC;AACA;AACJ,6BAAK,QAAL;AACIkD,mCAAO,EAAP;AACAC,6CAAiBnD,aAAa,QAAQ,EAAtC;AACA;AACJ,6BAAK,QAAL;AACI,gCAAIN,aAAaC,OAAjB,EAA0B;AACtBwD,iDAAiB/C,SAASV,SAAT,CAAjB;AACAwD,uCAAOpD,KAAKsD,IAAL,CAAU,CAAChD,SAAST,OAAT,IAAoBS,SAASV,SAAT,CAArB,IAA4C,KAAtD,CAAP;AACH;AACD;AAlBR;;AAqBA,yBAAK,IAAIiD,IAAI,CAAb,EAAgBA,IAAIO,IAApB,EAA0BP,GAA1B,EAA+B;AAC3B,8BAAMU,WAAWF,iBAAiBR,IAAI,KAAtC;AACA,8BAAMW,SAASD,WAAW,KAA1B;;AAEA,8BAAMP,SAAS,OAAM,OAAKlH,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC3CE,uCAAW,CADgC;AAE3CG,0CAAc,CAAC,IAAD,EAAO,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,CAAP,CAF6B;AAG3CoE,sCAAU,CAAC,SAAD,EAAY+C,QAAZ,EAAsBC,MAAtB;AAHiC,yBAA1B,EAIlBjE,GAJkB,CAId,cAJc,CAAN,KAIW,CAJ1B;;AAMA,8BAAMkE,OAAO,IAAI7F,IAAJ,CAAS2F,WAAW,IAApB,CAAb;AACAX,kCAAUK,IAAV,CAAe;AACXC,kCAAO,GAAEO,KAAKC,QAAL,KAAkB,CAAE,IAAGD,KAAKE,OAAL,EAAe,EADpC;AAEXX,oCAAQb,WAAWa,MAAX,EAAmB7B,OAAnB,CAA2B,CAA3B,CAFG;AAGX1E,uCAAW8G;AAHA,yBAAf;AAKH;AACJ;;AAED,uBAAO,OAAK3G,OAAL,CAAagG,SAAb,CAAP;AACH,aAzED,CAyEE,OAAOF,KAAP,EAAc;AACZjF,wBAAQiF,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO,OAAK/G,IAAL,CAAU,YAAV,CAAP;AACH;AA7EyB;AA8E7B;;AAjjB+B,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\index.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\n\nmodule.exports = class extends Base {\n\tasync checkLoginAction(){\n\t\tif(think.userId == 0){\n\t\t\treturn this.fail(404,'请登录');\n\t\t}\n\t}\n    async indexAction() {\n        const goodsOnsale = await this.model('goods').where({is_on_sale: 1,is_delete:0}).count();\n        const orderToDelivery = await this.model('order').where({order_status: 300}).count();\n        const user = await this.model('user').count();\n        let data = await this.model('settings').field('countdown').find();\n        let timestamp = data.countdown;\n        let info = {\n            user: user,\n            goodsOnsale: goodsOnsale,\n            timestamp:timestamp,\n            orderToDelivery: orderToDelivery,\n        }\n        return this.success(info);\n    }\n    async getQiniuTokenAction(){\n        const TokenSerivce = this.service('qiniu'); // 服务里返回token\n        let data = await TokenSerivce.getQiniuToken(); // 取得token值 goods\n        let qiniuToken = data.uploadToken;\n        let domain = data.domain;\n        let info ={\n            token:qiniuToken,\n            url:domain\n        };\n        return this.success(info);\n    }\n    async mainAction() {\n        const index = this.get('pindex');\n        console.log('index:' + index);\n        let todayTimeStamp = new Date(new Date().setHours(0, 0, 0, 0)) / 1000; //今天零点的时间戳\n        let yesTimeStamp = todayTimeStamp - 86400; //昨天零点的时间戳\n        let sevenTimeStamp = todayTimeStamp - 86400 * 7; //7天前零点的时间戳\n        let thirtyTimeStamp = todayTimeStamp - 86400 * 30; //30天前零点的时间戳\n        let newUser = 1;\n        let oldUser = 0;\n        let addCart = 0;\n        let addOrderNum = 0;\n        let addOrderSum = 0;\n        let payOrderNum = 0;\n        let payOrderSum = 0;\n        let newData = [];\n        let oldData = [];\n        if (index == 0) {\n            newData = await this.model('user').where({\n                id: ['>', 0],\n                register_time: ['>', todayTimeStamp]\n            }).select();\n            newUser = newData.length;\n            for(const item of newData){\n                item.nickname = Buffer.from(item.nickname, 'base64').toString();\n                // 处理头像URL\n                if (!item.avatar || item.avatar === '' || item.avatar.startsWith('wxfile://')) {\n                    // 如果没有头像或者是微信临时文件，使用默认头像\n                    item.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';\n                } else if (!item.avatar.startsWith('http://') && !item.avatar.startsWith('https://')) {\n                    // 如果是相对路径，补充完整的域名\n                    item.avatar = `https://ht.rxkjsdj.com${item.avatar}`;\n                }\n            }\n            oldData = await this.model('user').where({\n                id: ['>', 0],\n                register_time: ['<', todayTimeStamp],\n                last_login_time: ['>', todayTimeStamp]\n            }).select();\n            for(const item of oldData){\n                item.nickname = Buffer.from(item.nickname, 'base64').toString();\n                // 处理头像URL\n                if (!item.avatar || item.avatar === '' || item.avatar.startsWith('wxfile://')) {\n                    // 如果没有头像或者是微信临时文件，使用默认头像\n                    item.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';\n                } else if (!item.avatar.startsWith('http://') && !item.avatar.startsWith('https://')) {\n                    // 如果是相对路径，补充完整的域名\n                    item.avatar = `https://ht.rxkjsdj.com${item.avatar}`;\n                }\n            }\n            oldUser = oldData.length;\n            addCart = await this.model('cart').where({is_delete: 0, add_time: ['>', todayTimeStamp]}).count();\n            addOrderNum = await this.model('order').where({\n                is_delete: 0,\n                add_time: ['>', todayTimeStamp]\n            }).count();\n            addOrderSum = await this.model('order').where({\n                is_delete: 0,\n                add_time: ['>', todayTimeStamp]\n            }).sum('actual_price');\n            payOrderNum = await this.model('order').where({\n                is_delete: 0,\n                add_time: ['>', todayTimeStamp],\n                order_status: ['IN', [201, 802, 300, 301]]\n            }).count();\n            payOrderSum = await this.model('order').where({\n                is_delete: 0,\n                add_time: ['>', todayTimeStamp],\n                order_status: ['IN', [201, 802, 300, 301]]\n            }).sum('actual_price');\n        }\n        else if (index == 1) {\n            newData = await this.model('user').where({\n                id: ['>', 0],\n                register_time: ['BETWEEN', yesTimeStamp, todayTimeStamp]\n            }).select();\n            for(const item of newData){\n                item.nickname = Buffer.from(item.nickname, 'base64').toString();\n                // 处理头像URL\n                if (!item.avatar || item.avatar === '' || item.avatar.startsWith('wxfile://')) {\n                    // 如果没有头像或者是微信临时文件，使用默认头像\n                    item.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';\n                } else if (!item.avatar.startsWith('http://') && !item.avatar.startsWith('https://')) {\n                    // 如果是相对路径，补充完整的域名\n                    item.avatar = `https://ht.rxkjsdj.com${item.avatar}`;\n                }\n            }\n            newUser = newData.length;\n            oldData = await this.model('user').where({\n                id: ['>', 0],\n                register_time: ['<', yesTimeStamp],\n                last_login_time: ['BETWEEN', yesTimeStamp, todayTimeStamp]\n            }).select();\n            for(const item of oldData){\n                item.nickname = Buffer.from(item.nickname, 'base64').toString();\n                // 处理头像URL\n                if (!item.avatar || item.avatar === '' || item.avatar.startsWith('wxfile://')) {\n                    // 如果没有头像或者是微信临时文件，使用默认头像\n                    item.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';\n                } else if (!item.avatar.startsWith('http://') && !item.avatar.startsWith('https://')) {\n                    // 如果是相对路径，补充完整的域名\n                    item.avatar = `https://ht.rxkjsdj.com${item.avatar}`;\n                }\n            }\n            oldUser = oldData.length;\n            addCart = await this.model('cart').where({\n                is_delete: 0,\n                add_time: ['BETWEEN', yesTimeStamp, todayTimeStamp]\n            }).count();\n            addOrderNum = await this.model('order').where({\n                is_delete: 0,\n                add_time: ['BETWEEN', yesTimeStamp, todayTimeStamp]\n            }).count();\n            addOrderSum = await this.model('order').where({\n                is_delete: 0,\n                add_time: ['BETWEEN', yesTimeStamp, todayTimeStamp]\n            }).sum('actual_price');\n            payOrderNum = await this.model('order').where({\n                is_delete: 0,\n                add_time: ['BETWEEN', yesTimeStamp, todayTimeStamp],\n                order_status: ['IN', [201, 802, 300, 301]]\n            }).count();\n            console.log('------------321----------');\n            console.log(payOrderNum);\n            console.log('-----------3321-----------');\n            payOrderSum = await this.model('order').where({\n                is_delete: 0,\n                add_time: ['BETWEEN', yesTimeStamp, todayTimeStamp],\n                order_status: ['IN', [201, 802, 300, 301]]\n            }).sum('actual_price');\n            console.log('-----------123-----------');\n            console.log(payOrderSum);\n            console.log('-----------123-----------');\n\n        }\n        else if (index == 2) {\n            newData = await this.model('user').where({\n                id: ['>', 0],\n                register_time: ['>', sevenTimeStamp]\n            }).select();\n            for(const item of newData){\n                item.nickname = Buffer.from(item.nickname, 'base64').toString();\n                // 处理头像URL\n                if (!item.avatar || item.avatar === '' || item.avatar.startsWith('wxfile://')) {\n                    // 如果没有头像或者是微信临时文件，使用默认头像\n                    item.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';\n                } else if (!item.avatar.startsWith('http://') && !item.avatar.startsWith('https://')) {\n                    // 如果是相对路径，补充完整的域名\n                    item.avatar = `https://ht.rxkjsdj.com${item.avatar}`;\n                }\n            }\n            newUser = newData.length;\n            oldData = await this.model('user').where({\n                id: ['>', 0],\n                register_time: ['<', sevenTimeStamp],\n                last_login_time: ['>', sevenTimeStamp]\n            }).select();\n            for(const item of oldData){\n                item.nickname = Buffer.from(item.nickname, 'base64').toString();\n                // 处理头像URL\n                if (!item.avatar || item.avatar === '' || item.avatar.startsWith('wxfile://')) {\n                    // 如果没有头像或者是微信临时文件，使用默认头像\n                    item.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';\n                } else if (!item.avatar.startsWith('http://') && !item.avatar.startsWith('https://')) {\n                    // 如果是相对路径，补充完整的域名\n                    item.avatar = `https://ht.rxkjsdj.com${item.avatar}`;\n                }\n            }\n            oldUser = oldData.length;\n            addCart = await this.model('cart').where({\n                is_delete: 0,\n                add_time: ['>', sevenTimeStamp]\n            }).count();\n            addOrderNum = await this.model('order').where({\n                is_delete: 0,\n                add_time: ['>', sevenTimeStamp]\n            }).count();\n            addOrderSum = await this.model('order').where({\n                is_delete: 0,\n                add_time: ['>', sevenTimeStamp]\n            }).sum('actual_price');\n            payOrderNum = await this.model('order').where({\n                is_delete: 0,\n                add_time: ['>', sevenTimeStamp],\n                order_status: ['IN', [201, 802, 300, 301]]\n            }).count();\n            payOrderSum = await this.model('order').where({\n                is_delete: 0,\n                add_time: ['>', sevenTimeStamp],\n                order_status: ['IN', [201, 802, 300, 301]]\n            }).sum('actual_price');\n        }\n        else if (index == 3) {\n            newData = await this.model('user').where({\n                id: ['>', 0],\n                register_time: ['>', thirtyTimeStamp]\n            }).select();\n            for(const item of newData){\n                item.nickname = Buffer.from(item.nickname, 'base64').toString();\n                // 处理头像URL\n                if (!item.avatar || item.avatar === '' || item.avatar.startsWith('wxfile://')) {\n                    // 如果没有头像或者是微信临时文件，使用默认头像\n                    item.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';\n                } else if (!item.avatar.startsWith('http://') && !item.avatar.startsWith('https://')) {\n                    // 如果是相对路径，补充完整的域名\n                    item.avatar = `https://ht.rxkjsdj.com${item.avatar}`;\n                }\n            }\n            newUser = newData.length;\n            oldData = await this.model('user').where({\n                id: ['>', 0],\n                register_time: ['<', thirtyTimeStamp],\n                last_login_time: ['>', thirtyTimeStamp]\n            }).select();\n            for(const item of oldData){\n                item.nickname = Buffer.from(item.nickname, 'base64').toString();\n                // 处理头像URL\n                if (!item.avatar || item.avatar === '' || item.avatar.startsWith('wxfile://')) {\n                    // 如果没有头像或者是微信临时文件，使用默认头像\n                    item.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';\n                } else if (!item.avatar.startsWith('http://') && !item.avatar.startsWith('https://')) {\n                    // 如果是相对路径，补充完整的域名\n                    item.avatar = `https://ht.rxkjsdj.com${item.avatar}`;\n                }\n            }\n            oldUser = oldData.length;\n            addCart = await this.model('cart').where({\n                is_delete: 0,\n                add_time: ['>', thirtyTimeStamp]\n            }).count();\n            addOrderNum = await this.model('order').where({\n                is_delete: 0,\n                add_time: ['>', thirtyTimeStamp]\n            }).count();\n            addOrderSum = await this.model('order').where({\n                is_delete: 0,\n                add_time: ['>', thirtyTimeStamp]\n            }).sum('actual_price');\n            payOrderNum = await this.model('order').where({\n                is_delete: 0,\n                add_time: ['>', thirtyTimeStamp],\n                order_status: ['IN', [201, 802, 300, 301]]\n            }).count();\n            payOrderSum = await this.model('order').where({\n                is_delete: 0,\n                add_time: ['>', thirtyTimeStamp],\n                order_status: ['IN', [201, 802, 300, 301]]\n            }).sum('actual_price');\n        }\n        if (addOrderSum == null) {\n            addOrderSum = 0;\n        }\n        if (payOrderSum == null) {\n            payOrderSum = 0;\n        }\n        if(newData.length > 0){\n            for(const item of newData){\n                item.register_time =  moment.unix(item.register_time).format('YYYY-MM-DD HH:mm:ss');\n                item.last_login_time =  moment.unix(item.last_login_time).format('YYYY-MM-DD HH:mm:ss');\n            }\n        }\n\n        if(oldData.length > 0){\n            for(const item of oldData){\n                item.register_time =  moment.unix(item.register_time).format('YYYY-MM-DD HH:mm:ss');\n                item.last_login_time =  moment.unix(item.last_login_time).format('YYYY-MM-DD HH:mm:ss');\n            }\n        }\n\n        let info = {\n            newUser: newUser,\n            oldUser: oldUser,\n            addCart: addCart,\n            newData: newData,\n            oldData: oldData,\n            addOrderNum: addOrderNum,\n            addOrderSum: addOrderSum,\n            payOrderNum: payOrderNum,\n            payOrderSum: payOrderSum\n        }\n        return this.success(info);\n    }\n\n    // 获取数据概览统计\n    async getDataOverviewAction() {\n        try {\n            const timeRange = this.get('timeRange') || 'today'; // today, yesterday, 7days, 30days, custom\n            const startTime = this.get('startTime');\n            const endTime = this.get('endTime');\n\n            let timeCondition = {};\n            const now = Math.floor(Date.now() / 1000);\n            const todayStart = Math.floor(new Date().setHours(0, 0, 0, 0) / 1000);\n            const yesterdayStart = todayStart - 86400;\n            const sevenDaysAgo = todayStart - 86400 * 7;\n            const thirtyDaysAgo = todayStart - 86400 * 30;\n\n            // 设置时间条件\n            switch (timeRange) {\n                case 'today':\n                    timeCondition = ['>=', todayStart];\n                    break;\n                case 'yesterday':\n                    timeCondition = ['BETWEEN', yesterdayStart, todayStart - 1];\n                    break;\n                case '7days':\n                    timeCondition = ['>=', sevenDaysAgo];\n                    break;\n                case '30days':\n                    timeCondition = ['>=', thirtyDaysAgo];\n                    break;\n                case 'custom':\n                    if (startTime && endTime) {\n                        timeCondition = ['BETWEEN', parseInt(startTime), parseInt(endTime)];\n                    } else {\n                        timeCondition = ['>=', todayStart];\n                    }\n                    break;\n                default:\n                    timeCondition = ['>=', todayStart];\n            }\n\n            // 1. 支付金额 - 已支付订单的实际支付金额\n            const paymentAmount = await this.model('order').where({\n                is_delete: 0,\n                order_status: ['IN', [201, 300, 301, 401]], // 已付款、已发货、已收货、已完成\n                pay_time: timeCondition\n            }).sum('actual_price') || 0;\n\n            // 2. 订单数 - 已支付订单数量\n            const orderCount = await this.model('order').where({\n                is_delete: 0,\n                order_status: ['IN', [201, 300, 301, 401]],\n                pay_time: timeCondition\n            }).count();\n\n            // 3. 商品访客数 - 商品浏览足迹的去重用户数\n            const visitorCount = await this.model('footprint').where({\n                add_time: timeCondition\n            }).group('user_id').count();\n\n            // 4. 销售件数 - 已支付订单的商品总件数\n            const salesVolume = await this.model('order_goods').alias('og')\n                .join({\n                    table: 'order',\n                    join: 'inner',\n                    as: 'o',\n                    on: ['og.order_id', 'o.id']\n                })\n                .where({\n                    'og.is_delete': 0,\n                    'o.is_delete': 0,\n                    'o.order_status': ['IN', [201, 300, 301, 401]],\n                    'o.pay_time': timeCondition\n                }).sum('og.number') || 0;\n\n            // 5. 支付转化率 = 支付订单数 / 商品访客数 * 100\n            const conversionRate = visitorCount > 0 ? ((orderCount / visitorCount) * 100).toFixed(2) : 0;\n\n            // 6. 客单价 = 支付金额 / 订单数\n            const avgOrderValue = orderCount > 0 ? (paymentAmount / orderCount).toFixed(2) : 0;\n\n            // 获取昨日对比数据\n            let yesterdayCondition = {};\n            if (timeRange === 'today') {\n                const yesterdayStart = todayStart - 86400;\n                const yesterdayEnd = todayStart - 1;\n                yesterdayCondition = ['BETWEEN', yesterdayStart, yesterdayEnd];\n            } else {\n                // 对于其他时间范围，获取相同长度的前一个时间段\n                let prevStartTime = todayStart - 86400;\n                let prevEndTime = todayStart - 1;\n\n                switch (timeRange) {\n                    case 'yesterday':\n                        prevStartTime = todayStart - 86400 * 2;\n                        prevEndTime = todayStart - 86400 - 1;\n                        break;\n                    case '7days':\n                        prevStartTime = todayStart - 86400 * 14;\n                        prevEndTime = todayStart - 86400 * 7 - 1;\n                        break;\n                    case '30days':\n                        prevStartTime = todayStart - 86400 * 60;\n                        prevEndTime = todayStart - 86400 * 30 - 1;\n                        break;\n                }\n                yesterdayCondition = ['BETWEEN', prevStartTime, prevEndTime];\n            }\n\n            // 获取对比期间的数据\n            const prevPaymentAmount = await this.model('order').where({\n                is_delete: 0,\n                order_status: ['IN', [201, 300, 301, 401]],\n                pay_time: yesterdayCondition\n            }).sum('actual_price') || 0;\n\n            const prevOrderCount = await this.model('order').where({\n                is_delete: 0,\n                order_status: ['IN', [201, 300, 301, 401]],\n                pay_time: yesterdayCondition\n            }).count();\n\n            const prevVisitorCount = await this.model('footprint').where({\n                add_time: yesterdayCondition\n            }).group('user_id').count();\n\n            const prevSalesVolume = await this.model('order_goods').alias('og')\n                .join({\n                    table: 'order',\n                    join: 'inner',\n                    as: 'o',\n                    on: ['og.order_id', 'o.id']\n                })\n                .where({\n                    'og.is_delete': 0,\n                    'o.is_delete': 0,\n                    'o.order_status': ['IN', [201, 300, 301, 401]],\n                    'o.pay_time': yesterdayCondition\n                }).sum('og.number') || 0;\n\n            const prevConversionRate = prevVisitorCount > 0 ? ((prevOrderCount / prevVisitorCount) * 100) : 0;\n            const prevAvgOrderValue = prevOrderCount > 0 ? (prevPaymentAmount / prevOrderCount) : 0;\n\n            // 计算变化百分比\n            const calculateChange = (current, previous) => {\n                if (previous === 0) return current > 0 ? 100 : 0;\n                return ((current - previous) / previous * 100);\n            };\n\n            const overview = {\n                paymentAmount: parseFloat(paymentAmount).toFixed(2),\n                paymentAmountChange: calculateChange(paymentAmount, prevPaymentAmount).toFixed(2),\n                orderCount,\n                orderCountChange: calculateChange(orderCount, prevOrderCount).toFixed(2),\n                visitorCount,\n                visitorCountChange: calculateChange(visitorCount, prevVisitorCount).toFixed(2),\n                conversionRate: parseFloat(conversionRate),\n                conversionRateChange: calculateChange(conversionRate, prevConversionRate).toFixed(2),\n                salesVolume: parseInt(salesVolume),\n                salesVolumeChange: calculateChange(salesVolume, prevSalesVolume).toFixed(2),\n                avgOrderValue: parseFloat(avgOrderValue),\n                avgOrderValueChange: calculateChange(avgOrderValue, prevAvgOrderValue).toFixed(2)\n            };\n\n            return this.success(overview);\n        } catch (error) {\n            console.error('获取数据概览失败:', error);\n            return this.fail('获取数据概览失败');\n        }\n    }\n\n    // 获取24小时支付金额图表数据\n    async getPaymentChartAction() {\n        try {\n            const timeRange = this.get('timeRange') || 'today';\n            const startTime = this.get('startTime');\n            const endTime = this.get('endTime');\n\n            let chartData = [];\n            const now = Math.floor(Date.now() / 1000);\n            const todayStart = Math.floor(new Date().setHours(0, 0, 0, 0) / 1000);\n\n            if (timeRange === 'today' || timeRange === 'realtime') {\n                // 24小时数据，按小时分组\n                for (let i = 0; i < 24; i++) {\n                    const hourStart = todayStart + i * 3600;\n                    const hourEnd = hourStart + 3599;\n\n                    const amount = await this.model('order').where({\n                        is_delete: 0,\n                        order_status: ['IN', [201, 300, 301, 401]],\n                        pay_time: ['BETWEEN', hourStart, hourEnd]\n                    }).sum('actual_price') || 0;\n\n                    chartData.push({\n                        time: `${i.toString().padStart(2, '0')}:00`,\n                        amount: parseFloat(amount).toFixed(2),\n                        timestamp: hourStart\n                    });\n                }\n            } else {\n                // 其他时间范围，按天分组\n                let days = 1;\n                let startTimestamp = todayStart;\n\n                switch (timeRange) {\n                    case 'yesterday':\n                        days = 1;\n                        startTimestamp = todayStart - 86400;\n                        break;\n                    case '7days':\n                        days = 7;\n                        startTimestamp = todayStart - 86400 * 7;\n                        break;\n                    case '30days':\n                        days = 30;\n                        startTimestamp = todayStart - 86400 * 30;\n                        break;\n                    case 'custom':\n                        if (startTime && endTime) {\n                            startTimestamp = parseInt(startTime);\n                            days = Math.ceil((parseInt(endTime) - parseInt(startTime)) / 86400);\n                        }\n                        break;\n                }\n\n                for (let i = 0; i < days; i++) {\n                    const dayStart = startTimestamp + i * 86400;\n                    const dayEnd = dayStart + 86399;\n\n                    const amount = await this.model('order').where({\n                        is_delete: 0,\n                        order_status: ['IN', [201, 300, 301, 401]],\n                        pay_time: ['BETWEEN', dayStart, dayEnd]\n                    }).sum('actual_price') || 0;\n\n                    const date = new Date(dayStart * 1000);\n                    chartData.push({\n                        time: `${date.getMonth() + 1}/${date.getDate()}`,\n                        amount: parseFloat(amount).toFixed(2),\n                        timestamp: dayStart\n                    });\n                }\n            }\n\n            return this.success(chartData);\n        } catch (error) {\n            console.error('获取支付图表数据失败:', error);\n            return this.fail('获取支付图表数据失败');\n        }\n    }\n\n\n};\n"]}