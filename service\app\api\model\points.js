function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

module.exports = class extends think.Model {

  get tableName() {
    return 'user_points';
  }

  /**
   * 获取用户积分信息
   */
  getUserPoints(userId) {
    var _this = this;

    return _asyncToGenerator(function* () {
      try {
        let userPoints = yield _this.model('user_points').where({
          user_id: userId
        }).find();

        // 如果用户没有积分记录，创建一个
        if (think.isEmpty(userPoints)) {
          const pointsId = yield _this.model('user_points').add({
            user_id: userId,
            total_points: 0,
            available_points: 0,
            used_points: 0,
            created_at: new Date(),
            updated_at: new Date()
          });

          userPoints = {
            id: pointsId,
            user_id: userId,
            total_points: 0,
            available_points: 0,
            used_points: 0
          };
        }

        return userPoints;
      } catch (error) {
        console.error('获取用户积分失败:', error);
        return null;
      }
    })();
  }

  /**
   * 增加用户积分
   */
  addUserPoints(userId, points, type = 'signin', sourceId = null, description = '') {
    var _this2 = this;

    return _asyncToGenerator(function* () {
      try {
        // 开始事务
        return yield _this2.model('user_points').transaction(_asyncToGenerator(function* () {
          // 1. 获取当前积分
          const userPoints = yield _this2.getUserPoints(userId);
          if (!userPoints) {
            throw new Error('获取用户积分失败');
          }

          // 2. 更新积分
          const newTotalPoints = userPoints.total_points + points;
          const newAvailablePoints = userPoints.available_points + points;

          yield _this2.model('user_points').where({
            user_id: userId
          }).update({
            total_points: newTotalPoints,
            available_points: newAvailablePoints,
            updated_at: new Date()
          });

          // 3. 记录积分日志
          yield _this2.model('points_log').add({
            user_id: userId,
            points_change: points,
            points_type: type,
            source_id: sourceId,
            description: description,
            balance_after: newTotalPoints,
            created_at: new Date()
          });

          return {
            success: true,
            totalPoints: newTotalPoints,
            availablePoints: newAvailablePoints
          };
        }));
      } catch (error) {
        console.error('增加用户积分失败:', error);
        return {
          success: false,
          error: error.message
        };
      }
    })();
  }

  /**
   * 消费用户积分
   */
  consumeUserPoints(userId, points, type = 'consume', sourceId = null, description = '') {
    var _this3 = this;

    return _asyncToGenerator(function* () {
      try {
        // 开始事务
        return yield _this3.model('user_points').transaction(_asyncToGenerator(function* () {
          // 1. 获取当前积分
          const userPoints = yield _this3.getUserPoints(userId);
          if (!userPoints) {
            throw new Error('获取用户积分失败');
          }

          // 2. 检查积分是否足够
          if (userPoints.available_points < points) {
            throw new Error('积分不足');
          }

          // 3. 更新积分
          const newAvailablePoints = userPoints.available_points - points;
          const newUsedPoints = userPoints.used_points + points;

          yield _this3.model('user_points').where({
            user_id: userId
          }).update({
            available_points: newAvailablePoints,
            used_points: newUsedPoints,
            updated_at: new Date()
          });

          // 4. 记录积分日志
          yield _this3.model('points_log').add({
            user_id: userId,
            points_change: -points, // 负数表示消费
            points_type: type,
            source_id: sourceId,
            description: description,
            balance_after: userPoints.total_points, // 总积分不变
            created_at: new Date()
          });

          return {
            success: true,
            totalPoints: userPoints.total_points,
            availablePoints: newAvailablePoints,
            usedPoints: newUsedPoints
          };
        }));
      } catch (error) {
        console.error('消费用户积分失败:', error);
        return {
          success: false,
          error: error.message
        };
      }
    })();
  }

  /**
   * 获取用户积分日志
   */
  getUserPointsLog(userId, page = 1, limit = 20) {
    var _this4 = this;

    return _asyncToGenerator(function* () {
      try {
        const offset = (page - 1) * limit;

        const logs = yield _this4.model('points_log').where({
          user_id: userId
        }).field('points_change, points_type, description, balance_after, created_at').order('created_at DESC').limit(offset, limit).select();

        const total = yield _this4.model('points_log').where({
          user_id: userId
        }).count();

        return {
          logs: logs,
          total: total,
          page: page,
          limit: limit,
          totalPages: Math.ceil(total / limit)
        };
      } catch (error) {
        console.error('获取积分日志失败:', error);
        return null;
      }
    })();
  }

  /**
   * 获取积分统计信息
   */
  getPointsStats(userId) {
    var _this5 = this;

    return _asyncToGenerator(function* () {
      try {
        // 获取用户积分信息
        const userPoints = yield _this5.getUserPoints(userId);

        // 获取本月获得积分
        const currentMonth = _this5.getCurrentMonth();
        const monthEarned = yield _this5.model('points_log').where({
          user_id: userId,
          points_change: ['>', 0],
          created_at: ['LIKE', `${currentMonth}%`]
        }).sum('points_change');

        // 获取本月消费积分
        const monthUsed = yield _this5.model('points_log').where({
          user_id: userId,
          points_change: ['<', 0],
          created_at: ['LIKE', `${currentMonth}%`]
        }).sum('points_change');

        // 获取签到获得的积分
        const signinEarned = yield _this5.model('points_log').where({
          user_id: userId,
          points_type: ['IN', ['signin', 'bonus']]
        }).sum('points_change');

        return {
          totalPoints: userPoints.total_points,
          availablePoints: userPoints.available_points,
          usedPoints: userPoints.used_points,
          monthEarned: monthEarned || 0,
          monthUsed: Math.abs(monthUsed || 0),
          signinEarned: signinEarned || 0
        };
      } catch (error) {
        console.error('获取积分统计失败:', error);
        return null;
      }
    })();
  }

  /**
   * 获取积分排行榜
   */
  getPointsRanking(limit = 10) {
    var _this6 = this;

    return _asyncToGenerator(function* () {
      try {
        const sql = `
        SELECT 
          up.user_id,
          u.nickname,
          u.avatar,
          up.total_points,
          up.available_points
        FROM user_points up
        LEFT JOIN user u ON up.user_id = u.id
        WHERE up.total_points > 0
        ORDER BY up.total_points DESC
        LIMIT ${limit}
      `;

        const rankings = yield _this6.model('user_points').query(sql);
        return rankings;
      } catch (error) {
        console.error('获取积分排行榜失败:', error);
        return [];
      }
    })();
  }

  /**
   * 系统积分统计
   */
  getSystemPointsStats() {
    var _this7 = this;

    return _asyncToGenerator(function* () {
      try {
        // 总积分发放量
        const totalIssued = yield _this7.model('points_log').where({
          points_change: ['>', 0]
        }).sum('points_change');

        // 总积分消费量
        const totalConsumed = yield _this7.model('points_log').where({
          points_change: ['<', 0]
        }).sum('points_change');

        // 当前流通积分
        const totalCirculation = yield _this7.model('user_points').sum('available_points');

        // 今日发放积分
        const today = _this7.getCurrentDate();
        const todayIssued = yield _this7.model('points_log').where({
          points_change: ['>', 0],
          created_at: ['LIKE', `${today}%`]
        }).sum('points_change');

        return {
          totalIssued: totalIssued || 0,
          totalConsumed: Math.abs(totalConsumed || 0),
          totalCirculation: totalCirculation || 0,
          todayIssued: todayIssued || 0
        };
      } catch (error) {
        console.error('获取系统积分统计失败:', error);
        return null;
      }
    })();
  }

  /**
   * 获取当前月份 YYYY-MM
   */
  getCurrentMonth() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  }

  /**
   * 获取当前日期 YYYY-MM-DD
   */
  getCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
};