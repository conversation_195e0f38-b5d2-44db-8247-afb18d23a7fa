{"_from": "lru.min@^1.0.0", "_id": "lru.min@1.1.2", "_inBundle": false, "_integrity": "sha512-Nv9KddBcQSlQopmBHXSsZVY5xsdlZkdH/Iey0BlcBYggMd4two7cZnKOK9vmy3nY0O5RGH99z1PCeTpPqszUYg==", "_location": "/lru.min", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "lru.min@^1.0.0", "name": "lru.min", "escapedName": "lru.min", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/mysql2"], "_resolved": "https://registry.npmjs.org/lru.min/-/lru.min-1.1.2.tgz", "_shasum": "01ce1d72cc50c7faf8bd1f809ebf05d4331021eb", "_spec": "lru.min@^1.0.0", "_where": "D:\\py-ide\\hioshop-miniprogram-master\\node_modules\\mysql2", "author": {"name": "https://github.com/wellwelwel"}, "bugs": {"url": "https://github.com/wellwelwel/lru.min/issues"}, "bundleDependencies": false, "deprecated": false, "description": "🔥 An extremely fast and efficient LRU cache for JavaScript with high compatibility (including Browsers) — 6.8KB.", "devDependencies": {"@babel/core": "^7.26.9", "@babel/preset-env": "^7.26.9", "@biomejs/biome": "^1.9.4", "@types/babel__core": "^7.20.5", "@types/node": "^22.13.10", "esbuild": "^0.25.0", "monocart-coverage-reports": "2.12.1", "packages-update": "^2.0.0", "poku": "^3.0.1", "prettier": "^3.5.3", "terser": "^5.39.0", "tsx": "^4.19.3", "typescript": "^5.8.2"}, "engines": {"bun": ">=1.0.0", "deno": ">=1.30.0", "node": ">=8.0.0"}, "exports": {".": {"import": {"types": "./lib/index.d.ts", "default": "./lib/index.mjs"}, "require": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}}}, "files": ["browser", "lib"], "funding": {"type": "github", "url": "https://github.com/sponsors/wellwelwel"}, "homepage": "https://github.com/wellwelwel/lru.min#readme", "keywords": ["lru", "cache", "caching", "hash", "node", "nodejs", "bun", "deno", "typescript", "browser", "fast", "lru-cache", "quick-lru"], "license": "MIT", "main": "./lib/index.js", "module": "./lib/index.mjs", "name": "lru.min", "repository": {"type": "git", "url": "git+https://github.com/wellwelwel/lru.min.git"}, "scripts": {"benchmark:cjs": "cd benchmark && npm ci && node index.cjs", "benchmark:esm": "cd benchmark && npm ci && node index.mjs", "build": "tsc && npm run build:esm && npm run build:browser", "build:browser": "tsx tools/browserfy.ts", "build:esm": "esbuild src/index.ts --outfile=lib/index.mjs --platform=node --target=node12 --format=esm", "lint": "npx @biomejs/biome lint && prettier --check .", "lint:fix": "npx @biomejs/biome lint --write && prettier --write .github/workflows/*.yml .", "postupdate": "npm run lint:fix", "prebuild": "rm -rf ./browser ./lib", "size": "ls -lh lib/index.mjs | awk '{print $5}'", "test:bun": "bun poku", "test:coverage": "mcr --import tsx --config mcr.config.ts npm run test:node", "test:deno": "deno run -A npm:poku", "test:node": "poku", "update": "pu minor && npm i && npm audit fix"}, "types": "./lib/index.d.ts", "version": "1.1.2"}