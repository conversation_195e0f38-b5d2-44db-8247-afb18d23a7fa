function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

/**
 * 秒杀轮次状态更新定时任务
 * 每分钟执行一次，检查并更新轮次状态
 */

const FlashSaleMultiScheduler = require('../service/flash_sale_multi_scheduler');

module.exports = class extends think.Service {

  /**
   * 执行定时任务
   */
  run() {
    var _this = this;

    return _asyncToGenerator(function* () {
      try {
        console.log('🔄 开始执行秒杀轮次状态更新任务...');

        const scheduler = new FlashSaleMultiScheduler();

        // 更新轮次状态
        const result = yield scheduler.updateRoundStatus(_this.model);

        // 每小时清理一次过期数据（当分钟数为0时）
        const now = new Date();
        if (now.getMinutes() === 0) {
          console.log('🧹 开始清理过期数据...');
          const cleanedCount = yield scheduler.cleanupExpiredData(_this.model, 30);
          console.log(`✅ 清理完成，删除了 ${cleanedCount} 个过期轮次`);
        }

        // 输出统计信息
        const stats = yield scheduler.getActiveRoundsStats(_this.model);
        console.log('📊 当前轮次状态统计:', stats);

        console.log('✅ 秒杀轮次状态更新任务完成');
      } catch (error) {
        console.error('❌ 秒杀轮次状态更新任务失败:', error);
      }
    })();
  }
};