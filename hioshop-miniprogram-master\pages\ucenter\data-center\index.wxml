<view class="container">
  <!-- 数据概览 -->
  <view class="data-overview">
    <view class="overview-header">
      <view class="header-title">数据中心</view>
      <view class="header-subtitle">我的推广数据统计</view>
    </view>
    
    <view class="stats-grid">
      <view class="stats-card">
        <view class="stats-value">{{promotionData.totalOrders || 0}}</view>
        <view class="stats-label">推广订单</view>
      </view>
      <view class="stats-card">
        <view class="stats-value">{{promotionData.totalAmount || '0.00'}}</view>
        <view class="stats-label">推广金额</view>
      </view>
      <view class="stats-card">
        <view class="stats-value">{{promotionData.totalCommission || '0.00'}}</view>
        <view class="stats-label">累计佣金</view>
      </view>
      <view class="stats-card">
        <view class="stats-value">{{promotionData.teamCount || 0}}</view>
        <view class="stats-label">团队人数</view>
      </view>
    </view>
  </view>

  <!-- 时间筛选 -->
  <view class="time-filter">
    <view class="filter-tabs">
      <view class="filter-tab {{timeFilter === 'today' ? 'active' : ''}}" 
            bindtap="changeTimeFilter" data-filter="today">今日</view>
      <view class="filter-tab {{timeFilter === 'week' ? 'active' : ''}}" 
            bindtap="changeTimeFilter" data-filter="week">本周</view>
      <view class="filter-tab {{timeFilter === 'month' ? 'active' : ''}}" 
            bindtap="changeTimeFilter" data-filter="month">本月</view>
      <view class="filter-tab {{timeFilter === 'all' ? 'active' : ''}}" 
            bindtap="changeTimeFilter" data-filter="all">全部</view>
    </view>
  </view>

  <!-- 详细数据 -->
  <view class="data-details">
    <!-- 推广数据 -->
    <view class="detail-section">
      <view class="section-header">
        <view class="section-title">推广数据</view>
        <view class="section-period">{{timePeriodText}}</view>
      </view>
      
      <view class="detail-grid">
        <view class="detail-item">
          <view class="detail-value">{{currentData.orders || 0}}</view>
          <view class="detail-label">新增订单</view>
        </view>
        <view class="detail-item">
          <view class="detail-value">{{currentData.amount || '0.00'}}</view>
          <view class="detail-label">订单金额</view>
        </view>
        <view class="detail-item">
          <view class="detail-value">{{currentData.commission || '0.00'}}</view>
          <view class="detail-label">获得佣金</view>
        </view>
        <view class="detail-item">
          <view class="detail-value">{{currentData.visitors || 0}}</view>
          <view class="detail-label">访问人数</view>
        </view>
      </view>
    </view>

    <!-- 团队数据 -->
    <view class="detail-section">
      <view class="section-header">
        <view class="section-title">团队数据</view>
      </view>
      
      <view class="team-stats">
        <view class="team-item">
          <view class="team-label">直接下级</view>
          <view class="team-value">{{teamData.directCount || 0}}人</view>
        </view>
        <view class="team-item">
          <view class="team-label">团队总人数</view>
          <view class="team-value">{{teamData.totalCount || 0}}人</view>
        </view>
        <view class="team-item">
          <view class="team-label">团队订单</view>
          <view class="team-value">{{teamData.teamOrders || 0}}单</view>
        </view>
        <view class="team-item">
          <view class="team-label">团队佣金</view>
          <view class="team-value">{{teamData.teamCommission || '0.00'}}元</view>
        </view>
      </view>
    </view>

    <!-- 排行榜 -->
    <view class="detail-section">
      <view class="section-header">
        <view class="section-title">推广排行</view>
        <view class="section-more" bindtap="toRankingList">查看更多 ></view>
      </view>
      
      <view class="ranking-list" wx:if="{{rankingList.length > 0}}">
        <view class="ranking-item" wx:for="{{rankingList}}" wx:key="id">
          <view class="ranking-left">
            <view class="ranking-number {{index < 3 ? 'top' : ''}}">{{index + 1}}</view>
            <image class="ranking-avatar" src="{{item.avatar || '/images/default-avatar.png'}}"></image>
            <view class="ranking-info">
              <view class="ranking-name">{{item.nickname || '用户' + item.id}}</view>
              <view class="ranking-desc">推广{{item.orderCount}}单</view>
            </view>
          </view>
          <view class="ranking-right">
            <view class="ranking-commission">{{item.commission}}元</view>
          </view>
        </view>
      </view>
      
      <view class="empty-state" wx:else>
        <view class="empty-text">暂无排行数据</view>
      </view>
    </view>

    <!-- 最近订单 -->
    <view class="detail-section">
      <view class="section-header">
        <view class="section-title">最近推广订单</view>
        <view class="section-more" bindtap="toPromotionOrders">查看全部 ></view>
      </view>
      
      <view class="order-list" wx:if="{{recentOrders.length > 0}}">
        <view class="order-item" wx:for="{{recentOrders}}" wx:key="id">
          <view class="order-left">
            <view class="order-goods">{{item.goodsName}}</view>
            <view class="order-time">{{item.createTime}}</view>
          </view>
          <view class="order-right">
            <view class="order-amount">{{item.orderAmount}}元</view>
            <view class="order-commission">佣金 +{{item.commission}}元</view>
          </view>
        </view>
      </view>
      
      <view class="empty-state" wx:else>
        <view class="empty-text">暂无推广订单</view>
      </view>
    </view>
  </view>
</view>
