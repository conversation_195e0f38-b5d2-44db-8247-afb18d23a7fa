{"version": 3, "sources": ["..\\..\\..\\src\\api\\model\\order.js"], "names": ["_", "require", "module", "exports", "think", "Model", "generateOrderNumber", "date", "Date", "getFullYear", "padStart", "getMonth", "getDay", "getHours", "getMinutes", "getSeconds", "random", "getOrderStatus", "showType", "status", "push", "getOrderHandleOption", "orderId", "handleOption", "cancel", "delete", "pay", "confirm", "cancel_refund", "apply_refund", "refund_detail", "refund_info", "orderInfo", "where", "id", "find", "order_status", "currentTime", "parseInt", "getTime", "confirmTime", "confirm_time", "add_time", "daysDiff", "refundApply", "model", "order_id", "isEmpty", "refund_amount", "refund_reason", "apply_time", "created_at", "getOrderTextCode", "textCode", "close", "delivery", "receive", "success", "countdown", "getOrderStatusText", "statusText", "getOrderAddTime", "setOrderPayTime", "payTime", "update", "pay_time", "orderDeleteById", "is_delete", "checkPayStatus", "info", "pay_status", "select", "_length", "length", "updatePayStatus", "payStatus", "limit", "updateOrderStatus", "orderStatus", "getOrderByOrderSn", "orderSn", "order_sn", "updatePayData", "data", "pay_id", "transaction_id", "time_end"], "mappings": ";;AAAA,MAAMA,IAAIC,QAAQ,QAAR,CAAV;AACAC,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;AACvC;;;;AAIA;AACAC,0BAAsB;AAClB,cAAMC,OAAO,IAAIC,IAAJ,EAAb;AACA,eAAOD,KAAKE,WAAL,KAAqBT,EAAEU,QAAF,CAAWH,KAAKI,QAAL,EAAX,EAA4B,CAA5B,EAA+B,GAA/B,CAArB,GAA2DX,EAAEU,QAAF,CAAWH,KAAKK,MAAL,EAAX,EAA0B,CAA1B,EAA6B,GAA7B,CAA3D,GAA+FZ,EAAEU,QAAF,CAAWH,KAAKM,QAAL,EAAX,EAA4B,CAA5B,EAA+B,GAA/B,CAA/F,GAAqIb,EAAEU,QAAF,CAAWH,KAAKO,UAAL,EAAX,EAA8B,CAA9B,EAAiC,GAAjC,CAArI,GAA6Kd,EAAEU,QAAF,CAAWH,KAAKQ,UAAL,EAAX,EAA8B,CAA9B,EAAiC,GAAjC,CAA7K,GAAqNf,EAAEgB,MAAF,CAAS,MAAT,EAAiB,MAAjB,CAA5N;AACH;AACDC,mBAAeC,QAAf,EAAyB;AACrB,YAAIC,SAAS,EAAb;AACA,YAAID,YAAY,CAAhB,EAAmB;AACf;AACAC,mBAAOC,IAAP,CAAY,GAAZ,EAAiB,GAAjB,EAAsB,GAAtB,EAA2B,GAA3B,EAAgC,GAAhC,EAAqC,GAArC,EAA0C,GAA1C,EAA+C,GAA/C,EAAoD,GAApD,EAAyD,GAAzD,EAA8D,GAA9D;AACA;AACH,SAJD,MAIO,IAAIF,YAAY,CAAhB,EAAmB;AACtB;AACAC,mBAAOC,IAAP,CAAY,GAAZ;AACH,SAHM,MAGA,IAAIF,YAAY,CAAhB,EAAmB;AACtB;AACAC,mBAAOC,IAAP,CAAY,GAAZ,EAAiB,GAAjB;AACH,SAHM,MAGA,IAAIF,YAAY,CAAhB,EAAmB;AACtB;AACAC,mBAAOC,IAAP,CAAY,GAAZ;AACH,SAHM,MAGA,IAAIF,YAAY,CAAhB,EAAmB;AACtB;AACAC,mBAAOC,IAAP,CAAY,GAAZ,EAAiB,GAAjB;AACH,SAHM,MAGA;AACH,mBAAO,IAAP;AACH;AACD,eAAOD,MAAP;AACH;AACD;;;;AAIME,wBAAN,CAA2BC,OAA3B,EAAoC;AAAA;;AAAA;AAChC,kBAAMC,eAAe;AACjBC,wBAAQ,KADS,EACF;AACfC,wBAAQ,KAFS,EAEF;AACfC,qBAAK,KAHY,EAGL;AACZC,yBAAS,KAJQ,EAID;AAChBC,+BAAe,KALE;AAMjBC,8BAAc,KANG,EAMI;AACrBC,+BAAe,KAPE,EAOK;AACtBC,6BAAa,IARI,CAQC;AARD,aAArB;AAUA,kBAAMC,YAAY,MAAM,MAAKC,KAAL,CAAW;AAC/BC,oBAAIZ;AAD2B,aAAX,EAErBa,IAFqB,EAAxB;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAIH,UAAUI,YAAV,KAA2B,GAA3B,IAAkCJ,UAAUI,YAAV,KAA2B,GAAjE,EAAsE;AAClEb,6BAAaC,MAAb,GAAsB,IAAtB;AACAD,6BAAaG,GAAb,GAAmB,IAAnB;AACH;AACD;AACA,gBAAIM,UAAUI,YAAV,KAA2B,GAA3B,IAAkCJ,UAAUI,YAAV,KAA2B,GAAjE,EAAsE;AAClEb,6BAAaE,MAAb,GAAsB,IAAtB;AACH;AACD;AACA,gBAAIO,UAAUI,YAAV,KAA2B,GAA/B,EAAoC;AAChCb,6BAAaM,YAAb,GAA4B,IAA5B;AACH;AACD;AACA,gBAAIG,UAAUI,YAAV,KAA2B,GAA/B,EAAoC;AAChCb,6BAAaK,aAAb,GAA6B,IAA7B;AACH;AACD;AACA,gBAAII,UAAUI,YAAV,KAA2B,GAA/B,EAAoC;AAChCb,6BAAaM,YAAb,GAA4B,IAA5B;AACH;AACD;AACA,gBAAIG,UAAUI,YAAV,KAA2B,GAA/B,EAAoC;AAChCb,6BAAaE,MAAb,GAAsB,IAAtB;AACH;AACD;AACA;AACA,gBAAIO,UAAUI,YAAV,KAA2B,GAA/B,EAAoC;AAChCb,6BAAaI,OAAb,GAAuB,IAAvB;AACAJ,6BAAaM,YAAb,GAA4B,IAA5B;AACH;AACD;AACA,gBAAIG,UAAUI,YAAV,KAA2B,GAA/B,EAAoC;AAChCb,6BAAaE,MAAb,GAAsB,IAAtB;AACA;AACA,sBAAMY,cAAcC,SAAS,IAAI9B,IAAJ,GAAW+B,OAAX,KAAuB,IAAhC,CAApB;AACA,sBAAMC,cAAcR,UAAUS,YAAV,IAA0BT,UAAUU,QAAxD;AACA,sBAAMC,WAAW,CAACN,cAAcG,WAAf,KAA+B,KAAK,EAAL,GAAU,EAAzC,CAAjB;AACA,oBAAIG,YAAY,CAAhB,EAAmB;AACfpB,iCAAaM,YAAb,GAA4B,IAA5B;AACH;AACJ;;AAED;AACA,kBAAMe,cAAc,MAAM,MAAKC,KAAL,CAAW,cAAX,EAA2BZ,KAA3B,CAAiC;AACvDa,0BAAUxB;AAD6C,aAAjC,EAEvBa,IAFuB,EAA1B;;AAIA,gBAAI,CAAC/B,MAAM2C,OAAN,CAAcH,WAAd,CAAL,EAAiC;AAC7B;AACArB,6BAAaM,YAAb,GAA4B,KAA5B;AACAN,6BAAaO,aAAb,GAA6B,IAA7B;AACAP,6BAAaQ,WAAb,GAA2B;AACvBG,wBAAIU,YAAYV,EADO;AAEvBf,4BAAQyB,YAAYzB,MAFG;AAGvB6B,mCAAeJ,YAAYI,aAHJ;AAIvBC,mCAAeL,YAAYK,aAJJ;AAKvBC,gCAAYN,YAAYM,UALD;AAMvBC,gCAAYP,YAAYO;AAND,iBAA3B;AAQH;;AAED,mBAAO5B,YAAP;AA7FgC;AA8FnC;AACK6B,oBAAN,CAAuB9B,OAAvB,EAAgC;AAAA;;AAAA;AAC5B,kBAAM+B,WAAW;AACb3B,qBAAK,KADQ;AAEb4B,uBAAO,KAFM;AAGbC,0BAAU,KAHG;AAIbC,yBAAS,KAJI;AAKbC,yBAAS,KALI;AAMbC,2BAAW;AANE,aAAjB;AAQA,kBAAM1B,YAAY,MAAM,OAAKC,KAAL,CAAW;AAC/BC,oBAAIZ;AAD2B,aAAX,EAErBa,IAFqB,EAAxB;AAGA,gBAAIH,UAAUI,YAAV,KAA2B,GAA/B,EAAoC;AAChCiB,yBAAS3B,GAAT,GAAe,IAAf;AACA2B,yBAASK,SAAT,GAAqB,IAArB;AACH;AACD,gBAAI1B,UAAUI,YAAV,KAA2B,GAA3B,IAAkCJ,UAAUI,YAAV,KAA2B,GAAjE,EAAsE;AAClEiB,yBAASC,KAAT,GAAiB,IAAjB;AACH;AACD,gBAAItB,UAAUI,YAAV,KAA2B,GAA3B,IAAkCJ,UAAUI,YAAV,KAA2B,GAAjE,EAAsE;AAAE;AACpEiB,yBAASE,QAAT,GAAoB,IAApB;AACH;AACD,gBAAIvB,UAAUI,YAAV,KAA2B,GAA/B,EAAoC;AAAE;AAClCiB,yBAASG,OAAT,GAAmB,IAAnB;AACH;AACD,gBAAIxB,UAAUI,YAAV,KAA2B,GAA/B,EAAoC;AAChCiB,yBAASI,OAAT,GAAmB,IAAnB;AACH;AACD,mBAAOJ,QAAP;AA5B4B;AA6B/B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACMM,sBAAN,CAAyBrC,OAAzB,EAAkC;AAAA;;AAAA;AAC9B,kBAAMU,YAAY,MAAM,OAAKC,KAAL,CAAW;AAC/BC,oBAAIZ;AAD2B,aAAX,EAErBa,IAFqB,EAAxB;AAGA,gBAAIyB,aAAa,KAAjB;AACA,oBAAQ5B,UAAUI,YAAlB;AACI,qBAAK,GAAL;AACIwB,iCAAa,KAAb;AACA;AACJ,qBAAK,GAAL;AACIA,iCAAa,MAAb;AACA;AACJ,qBAAK,GAAL;AACIA,iCAAa,MAAb,CADJ,CACyB;AACrB;AACJ,qBAAK,GAAL;AACIA,iCAAa,KAAb;AACA;AACJ,qBAAK,GAAL;AACIA,iCAAa,MAAb,CADJ,CACyB;AACrB;AACJ,qBAAK,GAAL;AACIA,iCAAa,KAAb;AACA;AACJ,qBAAK,GAAL;AACIA,iCAAa,KAAb;AACA;AACJ,qBAAK,GAAL;AACIA,iCAAa,MAAb,CADJ,CACyB;AACrB;AAxBR;AA0BA,mBAAOA,UAAP;AA/B8B;AAgCjC;AACD;AACMC,mBAAN,CAAsBvC,OAAtB,EAA+B;AAAA;;AAAA;AAC3B,kBAAMU,YAAY,MAAM,OAAKC,KAAL,CAAW;AAC/BC,oBAAIZ;AAD2B,aAAX,EAErBa,IAFqB,EAAxB;AAGA,gBAAIO,WAAWV,UAAUU,QAAzB;AACA,mBAAOA,QAAP;AAL2B;AAM9B;AACD;AACMoB,mBAAN,CAAsBxC,OAAtB,EAA+ByC,OAA/B,EAAwC;AAAA;;AAAA;AACpC,kBAAM/B,YAAY,MAAM,OAAKC,KAAL,CAAW;AAC/BC,oBAAIZ;AAD2B,aAAX,EAErB0C,MAFqB,CAEd;AACNC,wBADM;AAENF;AAFM,aAFc,CAAxB;AAMA,mBAAO/B,SAAP;AAPoC;AAQvC;AACD;AACMkC,mBAAN,CAAsB5C,OAAtB,EAA+B;AAAA;;AAAA;AAC3B,mBAAO,OAAKW,KAAL,CAAW;AACdC,oBAAIZ;AADU,aAAX,EAEJ0C,MAFI,CAEG;AACNG,2BAAW;AADL,aAFH,CAAP;AAD2B;AAM9B;AACD;;;;;;AAMMC,kBAAN,CAAqB9C,OAArB,EAA8B;AAAA;;AAAA;AAC1B,gBAAI+C,OAAO,MAAM,OAAKpC,KAAL,CAAW;AACxBC,oBAAIZ,OADoB;AAExBgD,4BAAY;AAFY,aAAX,EAGdC,MAHc,EAAjB;AAIA,gBAAIC,UAAUH,KAAKI,MAAnB;AACA,gBAAID,UAAU,CAAd,EAAiB;AACb,uBAAO,KAAP;AACH,aAFD,MAEO;AACH,uBAAO,IAAP;AACH;AAVyB;AAW7B;AACD;;;;;;AAMME,mBAAN,CAAsBpD,OAAtB,EAA+BqD,YAAY,CAA3C,EAA8C;AAAA;;AAAA;AAC1C,mBAAO,OAAK1C,KAAL,CAAW;AACdC,oBAAIZ;AADU,aAAX,EAEJsD,KAFI,CAEE,CAFF,EAEKZ,MAFL,CAEY;AACfM,4BAAYhC,SAASqC,SAAT;AADG,aAFZ,CAAP;AAD0C;AAM7C;AACD;;;;;;AAMME,qBAAN,CAAwBvD,OAAxB,EAAiCwD,cAAc,CAA/C,EAAkD;AAAA;;AAAA;AAC9C,mBAAO,OAAK7C,KAAL,CAAW;AACdC,oBAAIZ;AADU,aAAX,EAEJsD,KAFI,CAEE,CAFF,EAEKZ,MAFL,CAEY;AACf5B,8BAAcE,SAASwC,WAAT;AADC,aAFZ,CAAP;AAD8C;AAMjD;AACD;;;;;AAKMC,qBAAN,CAAwBC,OAAxB,EAAiC;AAAA;;AAAA;AAC7B,gBAAI5E,MAAM2C,OAAN,CAAciC,OAAd,CAAJ,EAA4B;AACxB,uBAAO,EAAP;AACH;AACD,mBAAO,QAAK/C,KAAL,CAAW;AACdgD,0BAAUD;AADI,aAAX,EAEJ7C,IAFI,EAAP;AAJ6B;AAOhC;AACD;;;;;;AAMM+C,iBAAN,CAAoB5D,OAApB,EAA6B+C,IAA7B,EAAmC;AAAA;;AAAA;AAC/B,gBAAIc,OAAO;AACPb,4BAAY,CADL;AAEPlC,8BAAc,GAFP,EAEa;AACpBgD,wBAAQf,KAAKgB,cAHN;AAIPpB,0BAAUI,KAAKiB;AAJR,aAAX;AAMA,mBAAO,QAAKrD,KAAL,CAAW;AACdC,oBAAIZ;AADU,aAAX,EAEJsD,KAFI,CAEE,CAFF,EAEKZ,MAFL,CAEYmB,IAFZ,CAAP;AAP+B;AAUlC;AA9SsC,CAA3C", "file": "..\\..\\..\\src\\api\\model\\order.js", "sourcesContent": ["const _ = require('lodash');\nmodule.exports = class extends think.Model {\n    /**\n     * 生成订单的编号order_sn\n     * @returns {string}\n     */\n    // TODO 这里应该产生一个唯一的订单，但是实际上这里仍然存在两个订单相同的可能性\n    generateOrderNumber() {\n        const date = new Date();\n        return date.getFullYear() + _.padStart(date.getMonth(), 2, '0') + _.padStart(date.getDay(), 2, '0') + _.padStart(date.getHours(), 2, '0') + _.padStart(date.getMinutes(), 2, '0') + _.padStart(date.getSeconds(), 2, '0') + _.random(100000, 999999);\n    }\n    getOrderStatus(showType) {\n        let status = [];\n        if (showType == 0) {\n            // 全部订单：包含售后完成状态203\n            status.push(101, 102, 103, 201, 202, 203, 300, 301, 302, 303, 401);\n            // TODO 这里会不会效率不高？\n        } else if (showType == 1) {\n            // 待付款订单\n            status.push(101);\n        } else if (showType == 2) {\n            // 待发货订单（包括已付款201和待发货300状态）\n            status.push(201, 300);\n        } else if (showType == 3) {\n            // 待收货订单\n            status.push(301);\n        } else if (showType == 4) {\n            // 待评价订单\n            status.push(302, 303);\n        } else {\n            return null;\n        }\n        return status;\n    }\n    /**\n     * 获取订单可操作的选项\n     * @param orderId\n     */\n    async getOrderHandleOption(orderId) {\n        const handleOption = {\n            cancel: false, // 取消操作\n            delete: false, // 删除操作\n            pay: false, // 支付操作\n            confirm: false, // 确认收货完成订单操作\n            cancel_refund: false,\n            apply_refund: false, // 申请售后操作\n            refund_detail: false, // 售后详情操作\n            refund_info: null // 售后申请信息\n        };\n        const orderInfo = await this.where({\n            id: orderId\n        }).find();\n        // 订单流程：下单成功－》支付订单－》发货－》收货－》评论\n        // 订单相关状态字段设计，采用单个字段表示全部的订单状态\n        // 1xx表示订单取消和删除等状态：  101订单创建成功等待付款、102订单已取消、103订单已取消(自动)\n        // 2xx表示订单支付状态：        201订单已付款，等待发货、202订单取消，退款中、203已退款\n        // 3xx表示订单物流相关状态：     300订单待发货，301订单已发货，302用户确认收货、303系统自动收货\n        // 4xx表示订单完成的状态：      401已收货已评价\n        // 5xx表示订单退换货相关的状态：  501已收货，退款退货 TODO\n        // 如果订单已经取消或是已完成，则可删除和再次购买\n        // if (status == 101) \"未付款\";\n        // if (status == 102) \"已取消\";\n        // if (status == 103) \"已取消(系统)\";\n        // if (status == 201) \"已付款\";\n        // if (status == 300) \"待发货\";\n        // if (status == 301) \"已发货\";\n        // if (status == 302) \"已收货\";\n        // if (status == 303) \"已收货(系统)\";\n        //  TODO 设置一个定时器，自动将有些订单设为完成\n        // 订单刚创建，可以取消订单，可以继续支付\n        if (orderInfo.order_status === 101 || orderInfo.order_status === 801) {\n            handleOption.cancel = true;\n            handleOption.pay = true;\n        }\n        // 如果订单被取消\n        if (orderInfo.order_status === 102 || orderInfo.order_status === 103) {\n            handleOption.delete = true;\n        }\n        // 如果订单已付款，没有发货，则可申请售后\n        if (orderInfo.order_status === 201) {\n            handleOption.apply_refund = true;\n        }\n        // 如果订单申请退款中，没有相关操作\n        if (orderInfo.order_status === 202) {\n            handleOption.cancel_refund = true;\n        }\n        // 如果订单待发货，可以申请售后\n        if (orderInfo.order_status === 300) {\n            handleOption.apply_refund = true;\n        }\n        // 如果订单已经退款，则可删除\n        if (orderInfo.order_status === 203) {\n            handleOption.delete = true;\n        }\n        // 如果订单已经发货，没有收货，则可收货操作和申请售后\n        // 此时不能取消订单\n        if (orderInfo.order_status === 301) {\n            handleOption.confirm = true;\n            handleOption.apply_refund = true;\n        }\n        // 如果订单已完成，可以申请售后（7天内）\n        if (orderInfo.order_status === 401) {\n            handleOption.delete = true;\n            // 检查是否在7天售后期内\n            const currentTime = parseInt(new Date().getTime() / 1000);\n            const confirmTime = orderInfo.confirm_time || orderInfo.add_time;\n            const daysDiff = (currentTime - confirmTime) / (24 * 60 * 60);\n            if (daysDiff <= 7) {\n                handleOption.apply_refund = true;\n            }\n        }\n\n        // 检查是否已有售后申请\n        const refundApply = await this.model('refund_apply').where({\n            order_id: orderId\n        }).find();\n\n        if (!think.isEmpty(refundApply)) {\n            // 如果已有售后申请，则不显示\"申请售后\"，改为显示\"售后详情\"\n            handleOption.apply_refund = false;\n            handleOption.refund_detail = true;\n            handleOption.refund_info = {\n                id: refundApply.id,\n                status: refundApply.status,\n                refund_amount: refundApply.refund_amount,\n                refund_reason: refundApply.refund_reason,\n                apply_time: refundApply.apply_time,\n                created_at: refundApply.created_at\n            };\n        }\n\n        return handleOption;\n    }\n    async getOrderTextCode(orderId) {\n        const textCode = {\n            pay: false,\n            close: false,\n            delivery: false,\n            receive: false,\n            success: false,\n            countdown: false,\n        };\n        const orderInfo = await this.where({\n            id: orderId\n        }).find();\n        if (orderInfo.order_status === 101) {\n            textCode.pay = true;\n            textCode.countdown = true;\n        }\n        if (orderInfo.order_status === 102 || orderInfo.order_status === 103) {\n            textCode.close = true;\n        }\n        if (orderInfo.order_status === 201 || orderInfo.order_status === 300) { //待发货\n            textCode.delivery = true;\n        }\n        if (orderInfo.order_status === 301) { //已发货\n            textCode.receive = true;\n        }\n        if (orderInfo.order_status === 401) {\n            textCode.success = true;\n        }\n        return textCode;\n    }\n    // if (status == 101) \"未付款\";\n    // if (status == 102) \"已取消\";\n    // if (status == 103) \"已取消(系统)\";\n    // if (status == 201) \"已付款\";\n    // if (status == 301) \"已发货\";\n    // if (status == 302) \"已收货\";\n    // if (status == 303) \"已收货(系统)\";\n    // if (status == 401) \"已完成\";\n    async getOrderStatusText(orderId) {\n        const orderInfo = await this.where({\n            id: orderId\n        }).find();\n        let statusText = '待付款';\n        switch (orderInfo.order_status) {\n            case 101:\n                statusText = '待付款';\n                break;\n            case 102:\n                statusText = '交易关闭';\n                break;\n            case 103:\n                statusText = '交易关闭'; //到时间系统自动取消\n                break;\n            case 201:\n                statusText = '待发货';\n                break;\n            case 203:\n                statusText = '售后完成'; //售后完成状态\n                break;\n            case 300:\n                statusText = '待发货';\n                break;\n            case 301:\n                statusText = '已发货';\n                break;\n            case 401:\n                statusText = '交易成功'; //到时间，未收货的系统自动收货、\n                break;\n        }\n        return statusText;\n    }\n    // 返回创建时间\n    async getOrderAddTime(orderId) {\n        const orderInfo = await this.where({\n            id: orderId\n        }).find();\n        let add_time = orderInfo.add_time;\n        return add_time;\n    }\n    // 支付时间\n    async setOrderPayTime(orderId, payTime) {\n        const orderInfo = await this.where({\n            id: orderId\n        }).update({\n            pay_time,\n            payTime\n        });\n        return orderInfo;\n    }\n    // 删除订单，将is_delete置为0\n    async orderDeleteById(orderId) {\n        return this.where({\n            id: orderId\n        }).update({\n            is_delete: 1\n        });\n    }\n    /**\n     * check订单状态\n     * @param orderId\n     * @param payStatus\n     * @returns {Promise.<boolean>}\n     */\n    async checkPayStatus(orderId) {\n        let info = await this.where({\n            id: orderId,\n            pay_status: 2\n        }).select();\n        let _length = info.length;\n        if (_length > 0) {\n            return false;\n        } else {\n            return true;\n        }\n    }\n    /**\n     * 更改订单支付状态\n     * @param orderId\n     * @param payStatus\n     * @returns {Promise.<boolean>}\n     */\n    async updatePayStatus(orderId, payStatus = 0) {\n        return this.where({\n            id: orderId\n        }).limit(1).update({\n            pay_status: parseInt(payStatus)\n        });\n    }\n    /**\n     * 更改订单状态\n     * @param orderId\n     * @param order_Status\n     * @returns {Promise.<boolean>}\n     */\n    async updateOrderStatus(orderId, orderStatus = 0) {\n        return this.where({\n            id: orderId\n        }).limit(1).update({\n            order_status: parseInt(orderStatus)\n        });\n    }\n    /**\n     * 根据订单编号查找订单信息\n     * @param orderSn\n     * @returns {Promise.<Promise|Promise<any>|T|*>}\n     */\n    async getOrderByOrderSn(orderSn) {\n        if (think.isEmpty(orderSn)) {\n            return {};\n        }\n        return this.where({\n            order_sn: orderSn\n        }).find();\n    }\n    /**\n     * 更新订单状态,包括更新库存数据，现在可以开始写了\n     * @param orderId\n     * @param\n     * @returns {Promise.<boolean>}\n     */\n    async updatePayData(orderId, info) {\n        let data = {\n            pay_status: 2,\n            order_status: 201,  // 修改为201（已付款），符合数据库注释中的状态定义\n            pay_id: info.transaction_id,\n            pay_time: info.time_end\n        }\n        return this.where({\n            id: orderId\n        }).limit(1).update(data);\n    }\n};"]}