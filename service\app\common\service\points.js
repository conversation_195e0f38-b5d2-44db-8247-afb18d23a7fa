function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

/**
 * 佣金服务
 * 统一管理用户佣金的增减操作
 */

module.exports = class extends think.Service {

  /**
   * 给用户增加佣金
   * @param {number} userId 用户ID
   * @param {number} commission 佣金金额
   * @param {string} type 佣金类型：promotion,bonus,admin,withdraw
   * @param {string} description 描述
   * @param {number} sourceId 来源ID（可选）
   */
  addCommission(userId, commission, type = 'promotion', description = '', sourceId = null) {
    var _this = this;

    return _asyncToGenerator(function* () {
      try {
        console.log(`=== 给用户 ${userId} 增加 ${commission} 元佣金 ===`);
        console.log('类型:', type, '描述:', description);

        // 验证参数
        if (!userId || commission <= 0) {
          throw new Error('用户ID和佣金金额必须有效');
        }

        // 获取用户当前佣金
        let userCommission = yield _this.model('user_commission').where({ user_id: userId }).find();

        if (think.isEmpty(userCommission)) {
          // 如果用户佣金记录不存在，创建新记录
          userCommission = {
            user_id: userId,
            total_commission: 0,
            available_commission: 0,
            frozen_commission: 0,
            withdrawn_commission: 0
          };
          yield _this.model('user_commission').add(userCommission);
          userCommission = yield _this.model('user_commission').where({ user_id: userId }).find();
        }

        // 计算新的佣金余额
        const newTotalCommission = parseFloat(userCommission.total_commission) + parseFloat(commission);
        const newAvailableCommission = parseFloat(userCommission.available_commission) + parseFloat(commission);

        // 更新用户佣金
        yield _this.model('user_commission').where({ user_id: userId }).update({
          total_commission: newTotalCommission.toFixed(2),
          available_commission: newAvailableCommission.toFixed(2),
          updated_at: new Date()
        });

        // 记录佣金变动日志
        yield _this.model('commission_log').add({
          user_id: userId,
          commission_change: parseFloat(commission).toFixed(2),
          commission_type: type,
          source_id: sourceId,
          description: description,
          balance_after: newAvailableCommission.toFixed(2),
          status: 'completed',
          created_at: new Date()
        });

        console.log(`✅ 佣金增加成功: ${userCommission.available_commission} -> ${newAvailableCommission.toFixed(2)}`);

        return {
          success: true,
          oldBalance: parseFloat(userCommission.available_commission),
          newBalance: parseFloat(newAvailableCommission.toFixed(2)),
          commissionAdded: parseFloat(commission)
        };
      } catch (error) {
        console.error('增加佣金失败:', error);
        throw error;
      }
    })();
  }

  /**
   * 推广员获得推广佣金
   * @param {number} userId 推广员用户ID
   * @param {number} commission 佣金金额
   * @param {number} orderId 订单ID（可选）
   * @param {string} description 描述
   */
  addPromotionCommission(userId, commission, orderId = null, description = '推广佣金') {
    var _this2 = this;

    return _asyncToGenerator(function* () {
      try {
        console.log(`=== 推广员 ${userId} 获得推广佣金 ${commission} 元 ===`);

        // 给用户增加佣金
        const result = yield _this2.addCommission(userId, commission, 'promotion', description, orderId);

        // 更新推广员统计数据
        yield _this2.updatePromoterCommissionStats(userId, commission);

        return result;
      } catch (error) {
        console.error('添加推广佣金失败:', error);
        throw error;
      }
    })();
  }

  /**
   * 多级分销佣金发放（基于分销池配置）
   * @param {object} commissionData 佣金数据
   */
  distributeMultiLevelCommission(commissionData) {
    var _this3 = this;

    return _asyncToGenerator(function* () {
      try {
        const {
          orderId,
          goodsId,
          goodsName,
          orderAmount,
          promoterUserId,
          level1UserId = null,
          level2UserId = null,
          teamLeaderUserId = null
        } = commissionData;

        console.log('=== 开始多级分销佣金发放（基于分销池配置） ===');
        console.log('订单ID:', orderId, '商品ID:', goodsId);

        // 获取分销池佣金配置
        const distributionRates = yield _this3.getDistributionCommissionRates(goodsId);
        console.log('分销池佣金配置:', distributionRates);

        const { personal_rate, level1_rate, level2_rate, team_leader_rate } = distributionRates;

        const results = [];

        // 1. 个人佣金（直接推广员）
        if (promoterUserId && personal_rate > 0) {
          const personalCommission = (orderAmount * personal_rate / 100).toFixed(2);
          const result = yield _this3.addPromotionCommission(promoterUserId, personalCommission, orderId, `推广商品${goodsName}获得个人佣金`);
          results.push({
            userId: promoterUserId,
            level: 'personal',
            commission: personalCommission,
            rate: personal_rate,
            result: result
          });
        }

        // 2. 一级分销佣金（从推广订单记录中获取）
        if (level1UserId && level1_rate > 0) {
          const level1Commission = (orderAmount * level1_rate / 100).toFixed(2);
          const result = yield _this3.addPromotionCommission(level1UserId, level1Commission, orderId, `下级推广商品${goodsName}获得一级佣金`);
          results.push({
            userId: level1UserId,
            level: 'level1',
            commission: level1Commission,
            rate: level1_rate,
            result: result
          });
        }

        // 3. 二级分销佣金（从推广订单记录中获取）
        if (level2UserId && level2_rate > 0) {
          const level2Commission = (orderAmount * level2_rate / 100).toFixed(2);
          const result = yield _this3.addPromotionCommission(level2UserId, level2Commission, orderId, `二级下级推广商品${goodsName}获得二级佣金`);
          results.push({
            userId: level2UserId,
            level: 'level2',
            commission: level2Commission,
            rate: level2_rate,
            result: result
          });
        }

        // 4. 团长佣金
        if (teamLeaderUserId && team_leader_rate > 0) {
          const teamLeaderCommission = (orderAmount * team_leader_rate / 100).toFixed(2);
          const result = yield _this3.addPromotionCommission(teamLeaderUserId, teamLeaderCommission, orderId, `团队推广商品${goodsName}获得团长佣金`);
          results.push({
            userId: teamLeaderUserId,
            level: 'team_leader',
            commission: teamLeaderCommission,
            rate: team_leader_rate,
            result: result
          });
        }

        console.log('✅ 多级分销佣金发放完成（基于分销池配置），共发放', results.length, '笔佣金');
        return results;
      } catch (error) {
        console.error('多级分销佣金发放失败:', error);
        throw error;
      }
    })();
  }

  /**
   * 更新推广员佣金统计
   * @param {number} userId 用户ID
   * @param {number} commission 佣金金额
   */
  updatePromoterCommissionStats(userId, commission) {
    var _this4 = this;

    return _asyncToGenerator(function* () {
      try {
        // 更新推广员表中的佣金统计字段
        const promoter = yield _this4.model('personal_promoters').where({ user_id: userId }).find();

        if (!think.isEmpty(promoter)) {
          const newTotalCommission = parseFloat(promoter.total_commission || 0) + parseFloat(commission);
          const newOrderCount = parseInt(promoter.order_count || 0) + 1;

          yield _this4.model('personal_promoters').where({ user_id: userId }).update({
            total_commission: newTotalCommission.toFixed(2),
            order_count: newOrderCount,
            last_order_time: parseInt(new Date().getTime() / 1000),
            update_time: parseInt(new Date().getTime() / 1000)
          });

          console.log(`推广员 ${userId} 统计更新: 总佣金 ${newTotalCommission.toFixed(2)} 元，订单数 ${newOrderCount}`);
        }
      } catch (error) {
        console.error('更新推广员佣金统计失败:', error);
      }
    })();
  }

  /**
   * 推广员升级奖励佣金
   * @param {number} userId 推广员用户ID
   * @param {number} newLevel 新等级
   * @param {number} bonusCommission 奖励佣金
   */
  addUpgradeBonus(userId, newLevel, bonusCommission) {
    var _this5 = this;

    return _asyncToGenerator(function* () {
      try {
        if (bonusCommission <= 0) {
          console.log('升级奖励佣金为0，跳过');
          return { success: true, commissionAdded: 0 };
        }

        console.log(`=== 推广员 ${userId} 升级到等级 ${newLevel}，获得奖励 ${bonusCommission} 元佣金 ===`);

        const description = `升级到等级${newLevel}奖励`;
        const result = yield _this5.addCommission(userId, bonusCommission, 'bonus', description, newLevel);

        // 更新推广员统计数据
        yield _this5.updatePromoterCommissionStats(userId, bonusCommission);

        return result;
      } catch (error) {
        console.error('添加升级奖励佣金失败:', error);
        throw error;
      }
    })();
  }

  /**
   * 更新推广员统计数据
   * @param {number} userId 用户ID
   * @param {number} points 积分数量
   */
  updatePromoterStats(userId, points) {
    var _this6 = this;

    return _asyncToGenerator(function* () {
      try {
        const currentTime = parseInt(new Date().getTime() / 1000);
        const currentMonth = new Date().getMonth() + 1;
        const currentYear = new Date().getFullYear();

        // 查找推广员记录
        const promoter = yield _this6.model('personal_promoters').where({ user_id: userId }).find();

        if (think.isEmpty(promoter)) {
          console.log('推广员记录不存在，跳过统计更新');
          return;
        }

        // 更新总积分
        const newTotalPoints = parseInt(promoter.total_commission || 0) + points;

        // 检查是否是当月
        const promoterUpdateTime = new Date(promoter.update_time * 1000);
        const isCurrentMonth = promoterUpdateTime.getMonth() + 1 === currentMonth && promoterUpdateTime.getFullYear() === currentYear;

        let newMonthPoints;
        if (isCurrentMonth) {
          newMonthPoints = parseInt(promoter.month_commission || 0) + points;
        } else {
          // 新月份，重置月度积分
          newMonthPoints = points;
        }

        // 更新推广员统计
        yield _this6.model('personal_promoters').where({ user_id: userId }).update({
          total_commission: newTotalPoints,
          month_commission: newMonthPoints,
          update_time: currentTime
        });

        console.log(`✅ 推广员统计更新成功: 总积分=${newTotalPoints}, 月积分=${newMonthPoints}`);
      } catch (error) {
        console.error('更新推广员统计失败:', error);
        throw error;
      }
    })();
  }

  /**
   * 检查并处理推广员等级升级
   * @param {number} userId 用户ID
   */
  checkPromoterLevelUp(userId) {
    var _this7 = this;

    return _asyncToGenerator(function* () {
      try {
        console.log(`=== 检查推广员 ${userId} 等级升级 ===`);

        // 获取推广员信息
        const promoter = yield _this7.model('personal_promoters').where({ user_id: userId }).find();
        if (think.isEmpty(promoter)) {
          console.log('推广员记录不存在');
          return;
        }

        const currentLevel = promoter.level;
        const totalOrders = promoter.total_orders;

        // 获取所有等级配置
        const levels = yield _this7.model('promoter_levels').order('level ASC').select();

        // 找到符合条件的最高等级
        let newLevel = currentLevel;
        let bonusPoints = 0;

        for (let level of levels) {
          if (level.level > currentLevel && totalOrders >= level.min_orders && (level.max_orders === 0 || totalOrders <= level.max_orders)) {
            newLevel = level.level;
            bonusPoints = parseFloat(level.bonus_points) || 0; // 升级奖励佣金
          }
        }

        // 如果等级有提升
        if (newLevel > currentLevel) {
          console.log(`推广员等级提升: ${currentLevel} -> ${newLevel}`);

          // 更新推广员等级
          yield _this7.model('personal_promoters').where({ user_id: userId }).update({
            level: newLevel,
            update_time: parseInt(new Date().getTime() / 1000)
          });

          // 发放升级奖励佣金
          if (bonusPoints > 0) {
            yield _this7.addUpgradeBonus(userId, newLevel, bonusPoints);
          }

          console.log(`✅ 等级升级完成，奖励佣金: ${bonusPoints}元`);
          return { upgraded: true, newLevel, bonusPoints };
        }

        console.log('等级无变化');
        return { upgraded: false, currentLevel };
      } catch (error) {
        console.error('检查等级升级失败:', error);
        throw error;
      }
    })();
  }

  /**
   * 获取用户积分余额
   * @param {number} userId 用户ID
   */
  getUserPoints(userId) {
    var _this8 = this;

    return _asyncToGenerator(function* () {
      try {
        const userPoints = yield _this8.model('user_points').where({ user_id: userId }).find();

        if (think.isEmpty(userPoints)) {
          return {
            total_points: 0,
            available_points: 0,
            used_points: 0
          };
        }

        return {
          total_points: parseInt(userPoints.total_points),
          available_points: parseInt(userPoints.available_points),
          used_points: parseInt(userPoints.used_points)
        };
      } catch (error) {
        console.error('获取用户积分失败:', error);
        throw error;
      }
    })();
  }

  /**
   * 获取商品分销池的佣金配置
   * @param {number} goodsId 商品ID
   */
  getDistributionCommissionRates(goodsId) {
    var _this9 = this;

    return _asyncToGenerator(function* () {
      try {
        // 获取商品分销配置
        const distributionConfig = yield _this9.model('goods_distribution').where({
          goods_id: goodsId,
          is_distributed: 1
        }).find();

        if (think.isEmpty(distributionConfig)) {
          console.log('商品未开启分销，无法获得佣金');
          return null;
        }

        return _this9.formatDistributionRates(distributionConfig);
      } catch (error) {
        console.error('获取分销池佣金配置失败:', error);
        return null;
      }
    })();
  }

  /**
   * 格式化分销池佣金比例
   * @param {object} distributionConfig 分销配置
   */
  formatDistributionRates(distributionConfig) {
    return {
      personal_rate: parseFloat(distributionConfig.personal_rate || 8.00),
      level1_rate: parseFloat(distributionConfig.level1_rate || 3.00),
      level2_rate: parseFloat(distributionConfig.level2_rate || 1.00),
      team_leader_rate: parseFloat(distributionConfig.team_leader_rate || 2.00)
    };
  }

  /**
   * 分享有礼佣金发放（基于分销池配置，支持上级推广员）
   * @param {number} userId 分享者用户ID
   * @param {number} orderId 订单ID
   * @param {number} goodsId 商品ID
   * @param {number} orderAmount 订单金额
   * @param {number} parentUserId 上级推广员用户ID
   * @param {string} shareSource 分享来源
   */
  addShareCommissionWithParent(userId, orderId, goodsId, orderAmount, parentUserId, shareSource = 'miniprogram') {
    var _this10 = this;

    return _asyncToGenerator(function* () {
      try {
        console.log(`=== 分享有礼佣金记录（支持上级推广员） ===`);
        console.log('当前分享者ID:', userId, '订单ID:', orderId, '商品ID:', goodsId, '订单金额:', orderAmount);
        console.log('分享者的上级ID:', parentUserId);
        console.log('注意：佣金基于当前分享者，与购买者的上级关系无关');

        // 1. 获取商品分销池的佣金配置
        const distributionRates = yield _this10.getDistributionCommissionRates(goodsId);

        if (!distributionRates) {
          console.log('商品未开启分销，无法获得佣金');
          return { success: false, message: '商品未开启分销' };
        }

        console.log('分销池佣金配置:', distributionRates);

        // 2. 计算佣金
        const personalCommission = parseFloat((orderAmount * distributionRates.personal_rate / 100).toFixed(2));
        const level1Commission = parentUserId ? parseFloat((orderAmount * distributionRates.level1_rate / 100).toFixed(2)) : 0;

        console.log(`佣金计算: 个人${personalCommission}元, 上级${level1Commission}元`);

        // 3. 记录推广订单，等确认收货后发放
        const orderInfo = yield _this10.model('order').where({ id: orderId }).find();
        const goodsInfo = yield _this10.model('goods').where({ id: goodsId }).find();

        // 记录直接推广员的佣金
        yield _this10.model('promotion_orders').add({
          promoter_id: 0,
          promoter_user_id: userId,
          parent_promoter_user_id: parentUserId,
          buyer_user_id: orderInfo.user_id,
          order_id: orderId,
          order_sn: orderInfo.order_sn,
          goods_id: goodsId,
          goods_name: goodsInfo.name,
          goods_price: goodsInfo.shop_price,
          order_amount: orderAmount,
          commission_rate: distributionRates.personal_rate,
          commission_amount: personalCommission,
          personal_commission: personalCommission,
          level1_commission: level1Commission,
          level2_commission: 0,
          team_leader_commission: 0,
          share_source: shareSource,
          status: 'pending',
          commission_status: 'pending', // 佣金状态：待发放
          is_first_order: 0,
          settle_time: 0,
          withdraw_time: 0,
          refund_time: 0,
          create_time: parseInt(new Date().getTime() / 1000),
          update_time: parseInt(new Date().getTime() / 1000)
        });

        console.log('✅ 分享有礼佣金记录完成，等待确认收货后发放');

        return {
          success: true,
          commission: personalCommission,
          parentCommission: level1Commission,
          message: '分享有礼佣金记录成功'
        };
      } catch (error) {
        console.error('分享有礼佣金记录失败:', error);
        return { success: false, message: error.message };
      }
    })();
  }

  /**
   * 分享有礼佣金发放（基于分销池配置）
   * @param {number} userId 分享者用户ID
   * @param {number} orderId 订单ID
   * @param {number} goodsId 商品ID
   * @param {number} orderAmount 订单金额
   * @param {string} shareSource 分享来源
   */
  addShareCommission(userId, orderId, goodsId, orderAmount, shareSource = 'miniprogram') {
    var _this11 = this;

    return _asyncToGenerator(function* () {
      try {
        console.log(`=== 分享有礼佣金记录（确认收货后发放） ===`);
        console.log('分享者ID:', userId, '订单ID:', orderId, '商品ID:', goodsId, '订单金额:', orderAmount);

        // 1. 获取商品分销池的佣金配置
        const distributionRates = yield _this11.getDistributionCommissionRates(goodsId);

        if (!distributionRates) {
          console.log('商品未开启分销，无法获得佣金');
          return { success: false, message: '商品未开启分销' };
        }

        console.log('分销池佣金配置:', distributionRates);

        // 2. 使用分销池的个人佣金比例
        const commissionRate = distributionRates.personal_rate;
        if (commissionRate <= 0) {
          console.log('分销池个人佣金比例为0，无法获得佣金');
          return { success: false, message: '分销池个人佣金比例为0' };
        }

        // 3. 计算佣金金额
        const commissionAmount = parseFloat((orderAmount * commissionRate / 100).toFixed(2));
        console.log(`分销池佣金比例: ${commissionRate}%, 佣金金额: ${commissionAmount}元`);

        // 4. 不再发放佣金，只记录推广订单，等确认收货后发放

        // 5. 在推广订单表中记录分享佣金（等待确认收货后发放）
        const orderInfo = yield _this11.model('order').where({ id: orderId }).find();
        const goodsInfo = yield _this11.model('goods').where({ id: goodsId }).find();

        yield _this11.model('promotion_orders').add({
          promoter_id: 0, // 分享有礼没有推广员ID
          promoter_user_id: userId,
          buyer_user_id: orderInfo.user_id,
          order_id: orderId,
          order_sn: orderInfo.order_sn,
          goods_id: goodsId,
          goods_name: goodsInfo.name,
          goods_price: goodsInfo.shop_price,
          order_amount: orderAmount,
          commission_rate: commissionRate,
          commission_amount: commissionAmount,
          personal_commission: commissionAmount, // 分享有礼只有个人佣金
          level1_commission: 0.00,
          level2_commission: 0.00,
          team_leader_commission: 0.00,
          share_source: shareSource,
          commission_source: 'share', // 标识为分享有礼佣金
          status: 'pending', // 等待确认收货后发放
          is_first_order: 0,
          settle_time: 0, // 确认收货时更新
          create_time: parseInt(new Date().getTime() / 1000),
          update_time: parseInt(new Date().getTime() / 1000)
        });

        console.log('✅ 分享有礼佣金记录成功，等待确认收货后发放');
        return { success: true, commission: commissionAmount, result: { pending: true } };
      } catch (error) {
        console.error('分享有礼佣金发放失败:', error);
        throw error;
      }
    })();
  }

  /**
   * 通用佣金发放方法（使用统一的佣金服务）
   * @param {number} userId 用户ID
   * @param {number} amount 佣金金额
   * @param {string} type 佣金类型
   * @param {number} sourceId 来源ID
   * @param {string} description 描述
   */
  addCommission(userId, amount, type = 'promotion', sourceId = null, description = '') {
    var _this12 = this;

    return _asyncToGenerator(function* () {
      try {
        console.log(`=== 使用统一佣金服务发放佣金 ===`);
        console.log('用户ID:', userId, '金额:', amount, '类型:', type);

        // 使用统一的佣金服务，而不是直接操作数据库
        const commissionService = _this12.service('commission');

        // 调用佣金服务的发放方法
        const result = yield commissionService.settleCommission(userId, amount, sourceId, // 订单ID
        null, // 推广订单ID，这里暂时为null
        description);

        // 更新推广员统计（如果存在）
        const promoter = yield _this12.model('personal_promoters').where({ user_id: userId }).find();
        if (!think.isEmpty(promoter)) {
          yield _this12.model('personal_promoters').where({ user_id: userId }).increment('total_commission', amount);
          yield _this12.model('personal_promoters').where({ user_id: userId }).increment('month_commission', amount);
        }

        console.log(`✅ 佣金发放成功: 用户${userId} 获得${amount}元佣金`);
        return { success: true, result: result };
      } catch (error) {
        console.error('佣金发放失败:', error);
        throw error;
      }
    })();
  }
};