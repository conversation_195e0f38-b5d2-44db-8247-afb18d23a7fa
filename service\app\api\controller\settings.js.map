{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\settings.js"], "names": ["Base", "require", "module", "exports", "showSettingsAction", "info", "model", "where", "id", "find", "success", "saveAction", "userId", "getLoginUserId", "name", "post", "mobile", "nick<PERSON><PERSON>", "avatar", "name_mobile", "newbu<PERSON>", "<PERSON><PERSON><PERSON>", "from", "nickname", "toString", "data", "update", "userDetailAction", "field", "fail"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACAC,OAAOC,OAAP,GAAiB,cAAcH,IAAd,CAAmB;AAC5BI,oBAAN,GAA2B;AAAA;;AAAA;AACzB,UAAIC,OAAO,MAAM,MAAKC,KAAL,CAAW,eAAX,EACdC,KADc,CACR;AACLC,YAAI;AADC,OADQ,EAIdC,IAJc,EAAjB;AAKA,aAAO,MAAKC,OAAL,CAAaL,IAAb,CAAP;AANyB;AAO1B;AACKM,YAAN,GAAmB;AAAA;;AAAA;AACjB,UAAIC,SAAS,MAAM,OAAKC,cAAL,EAAnB;AACA,UAAIC,OAAO,OAAKC,IAAL,CAAU,MAAV,CAAX;AACA,UAAIC,SAAS,OAAKD,IAAL,CAAU,QAAV,CAAb;AACA,UAAIE,WAAW,OAAKF,IAAL,CAAU,UAAV,CAAf;AACA,UAAIG,SAAS,OAAKH,IAAL,CAAU,QAAV,CAAb;AACA,UAAII,cAAc,CAAlB;AACA,UAAIL,QAAQ,EAAR,IAAcE,UAAU,EAA5B,EAAgC;AAC9BG,sBAAc,CAAd;AACD;AACD,YAAMC,YAAYC,OAAOC,IAAP,CAAYL,QAAZ,CAAlB;AACA,UAAIM,WAAWH,UAAUI,QAAV,CAAmB,QAAnB,CAAf;AACA,UAAIC,OAAO;AACTX,cAAMA,IADG;AAETE,gBAAQA,MAFC;AAGTO,kBAAUA,QAHD;AAITL,gBAAQA,MAJC;AAKTC,qBAAaA;AALJ,OAAX;AAOA,UAAId,OAAO,MAAM,OAAKC,KAAL,CAAW,MAAX,EACdC,KADc,CACR;AACLC,YAAII;AADC,OADQ,EAIdc,MAJc,CAIPD,IAJO,CAAjB;AAKA,aAAO,OAAKf,OAAL,CAAaL,IAAb,CAAP;AAxBiB;AAyBlB;AACKsB,kBAAN,GAAyB;AAAA;;AAAA;AACvB,UAAIf,SAAS,MAAM,OAAKC,cAAL,EAAnB;AACA,UAAID,UAAU,CAAd,EAAiB;AACf,YAAIP,OAAO,MAAM,OAAKC,KAAL,CAAW,MAAX,EACdC,KADc,CACR;AACLC,cAAII;AADC,SADQ,EAIdgB,KAJc,CAIR,gCAJQ,EAKdnB,IALc,EAAjB;AAMAJ,aAAKkB,QAAL,GAAgBF,OAAOC,IAAP,CAAYjB,KAAKkB,QAAjB,EAA2B,QAA3B,EAAqCC,QAArC,EAAhB;AACA,eAAO,OAAKd,OAAL,CAAaL,IAAb,CAAP;AACD,OATD,MAUI;AACF,eAAO,OAAKwB,IAAL,CAAU,GAAV,EAAc,KAAd,CAAP;AACD;AAdsB;AAexB;AAlDiC,CAApC", "file": "..\\..\\..\\src\\api\\controller\\settings.js", "sourcesContent": ["const Base = require(\"./base.js\");\nmodule.exports = class extends Base {\n  async showSettingsAction() {\n    let info = await this.model(\"show_settings\")\n      .where({\n        id: 1,\n      })\n      .find();\n    return this.success(info);\n  }\n  async saveAction() {\n    let userId = await this.getLoginUserId();\n    let name = this.post(\"name\");\n    let mobile = this.post(\"mobile\");\n    let nickName = this.post(\"nickName\");\n    let avatar = this.post(\"avatar\");\n    let name_mobile = 0;\n    if (name != \"\" && mobile != \"\") {\n      name_mobile = 1;\n    }\n    const newbuffer = Buffer.from(nickName);\n    let nickname = newbuffer.toString(\"base64\");\n    let data = {\n      name: name,\n      mobile: mobile,\n      nickname: nickname,\n      avatar: avatar,\n      name_mobile: name_mobile,\n    };\n    let info = await this.model(\"user\")\n      .where({\n        id: userId,\n      })\n      .update(data);\n    return this.success(info);\n  }\n  async userDetailAction() {\n    let userId = await this.getLoginUserId();\n    if (userId != 0) {\n      let info = await this.model(\"user\")\n        .where({\n          id: userId,\n        })\n        .field(\"id,mobile,name,nickname,avatar\")\n        .find();\n      info.nickname = Buffer.from(info.nickname, \"base64\").toString();\n      return this.success(info);\n    }\n    else{\n      return this.fail(100,'未登录')\n    }\n  }\n};\n"]}