-- 修复分销池相关数据库问题
-- 解决字段缺失和表不存在的问题

-- 1. 检查并添加商品表缺失的字段
-- 检查 hiolabs_goods 表是否存在 add_time 字段，如果不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'hiolabs_goods' 
     AND COLUMN_NAME = 'add_time') > 0,
    'SELECT "add_time字段已存在" as result',
    'ALTER TABLE `hiolabs_goods` ADD COLUMN `add_time` int(10) unsigned NOT NULL DEFAULT 0 COMMENT "创建时间"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 创建商品分销配置表（如果不存在）
CREATE TABLE IF NOT EXISTS `hiolabs_goods_distribution` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_id` int(10) unsigned NOT NULL COMMENT '商品ID',
  `is_distributed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已分销 0:未分销 1:已分销',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '佣金比例（百分比）',
  `commission_type` varchar(20) NOT NULL DEFAULT 'default' COMMENT '佣金类型 default:默认规则 custom:自定义',
  `personal_rate` decimal(5,2) DEFAULT '0.00' COMMENT '个人推广佣金比例(%)',
  `level1_rate` decimal(5,2) DEFAULT '0.00' COMMENT '一级分销佣金比例(%)',
  `level2_rate` decimal(5,2) DEFAULT '0.00' COMMENT '二级分销佣金比例(%)',
  `team_leader_rate` decimal(5,2) DEFAULT '0.00' COMMENT '团长额外佣金比例(%)',
  `min_level_required` tinyint(1) DEFAULT '1' COMMENT '最低分销等级要求',
  `max_level_allowed` tinyint(1) DEFAULT '4' COMMENT '最高分销等级限制',
  `category_id` int(10) unsigned DEFAULT '0' COMMENT '商品分类ID',
  `priority` int(10) DEFAULT '0' COMMENT '分销优先级',
  `min_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最低佣金金额',
  `max_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最高佣金金额',
  `distributor_level` tinyint(3) NOT NULL DEFAULT '1' COMMENT '分销商等级要求',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0:禁用 1:启用',
  `start_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '分销开始时间',
  `end_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '分销结束时间',
  `daily_limit` int(10) DEFAULT '0' COMMENT '每日分销限额(0=无限制)',
  `total_limit` int(10) DEFAULT '0' COMMENT '总分销限额(0=无限制)',
  `current_count` int(10) DEFAULT '0' COMMENT '当前分销数量',
  `tags` varchar(255) DEFAULT '' COMMENT '商品标签(JSON格式)',
  `description` text COMMENT '分销说明',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `goods_id` (`goods_id`),
  KEY `is_distributed` (`is_distributed`),
  KEY `commission_rate` (`commission_rate`),
  KEY `is_active` (`is_active`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_level_required` (`min_level_required`),
  KEY `idx_priority` (`priority`),
  KEY `idx_start_end_time` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分销配置表';

-- 3. 创建分销佣金规则模板表
CREATE TABLE IF NOT EXISTS `hiolabs_commission_templates` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_code` varchar(50) NOT NULL COMMENT '模板代码',
  `category_id` int(10) unsigned DEFAULT '0' COMMENT '适用分类ID(0=全部)',
  `price_min` decimal(10,2) DEFAULT '0.00' COMMENT '价格区间最小值',
  `price_max` decimal(10,2) DEFAULT '999999.99' COMMENT '价格区间最大值',
  `personal_rate` decimal(5,2) DEFAULT '0.00' COMMENT '个人推广佣金比例',
  `level1_rate` decimal(5,2) DEFAULT '0.00' COMMENT '一级分销佣金比例',
  `level2_rate` decimal(5,2) DEFAULT '0.00' COMMENT '二级分销佣金比例',
  `team_leader_rate` decimal(5,2) DEFAULT '0.00' COMMENT '团长额外佣金比例',
  `min_level_required` tinyint(1) DEFAULT '1' COMMENT '最低等级要求',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认模板',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `sort_order` int(10) DEFAULT '0' COMMENT '排序',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_code` (`template_code`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_price_range` (`price_min`, `price_max`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销佣金规则模板表';

-- 4. 创建分销员关系表
CREATE TABLE IF NOT EXISTS `hiolabs_distributor_relations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '分销员用户ID',
  `parent_id` int(10) unsigned DEFAULT '0' COMMENT '上级分销员用户ID',
  `level` tinyint(1) DEFAULT '1' COMMENT '在关系链中的层级(1=直接下级)',
  `relation_path` varchar(500) DEFAULT '' COMMENT '关系路径(如: 1,2,3)',
  `team_leader_id` int(10) unsigned DEFAULT '0' COMMENT '所属团长ID',
  `join_time` int(10) unsigned NOT NULL COMMENT '建立关系时间',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '关系是否有效',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_parent` (`user_id`, `parent_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`),
  KEY `idx_team_leader_id` (`team_leader_id`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员关系表';

-- 5. 创建分销佣金记录表
CREATE TABLE IF NOT EXISTS `hiolabs_distribution_commissions` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` int(10) unsigned NOT NULL COMMENT '订单ID',
  `goods_id` int(10) unsigned NOT NULL COMMENT '商品ID',
  `buyer_user_id` int(10) unsigned NOT NULL COMMENT '购买者用户ID',
  `promoter_user_id` int(10) unsigned NOT NULL COMMENT '推广员用户ID',
  `commission_type` enum('personal','level1','level2','team_leader') NOT NULL COMMENT '佣金类型',
  `commission_rate` decimal(5,2) NOT NULL COMMENT '佣金比例',
  `order_amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `commission_amount` decimal(10,2) NOT NULL COMMENT '佣金金额',
  `promoter_level` tinyint(1) NOT NULL COMMENT '推广员等级',
  `status` enum('pending','confirmed','paid','cancelled') DEFAULT 'pending' COMMENT '佣金状态',
  `settle_time` int(10) unsigned DEFAULT '0' COMMENT '结算时间',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_buyer_user_id` (`buyer_user_id`),
  KEY `idx_promoter_user_id` (`promoter_user_id`),
  KEY `idx_commission_type` (`commission_type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销佣金记录表';

-- 6. 插入默认佣金模板（如果不存在）
INSERT IGNORE INTO `hiolabs_commission_templates` 
(`template_name`, `template_code`, `category_id`, `price_min`, `price_max`, `personal_rate`, `level1_rate`, `level2_rate`, `team_leader_rate`, `min_level_required`, `is_default`, `is_active`, `sort_order`, `create_time`, `update_time`) 
VALUES
('基础商品模板', 'basic', 0, 0.00, 100.00, 8.00, 3.00, 1.00, 2.00, 1, 1, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('中档商品模板', 'medium', 0, 100.01, 500.00, 10.00, 4.00, 2.00, 3.00, 2, 0, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('高档商品模板', 'premium', 0, 500.01, 999999.99, 15.00, 6.00, 3.00, 5.00, 3, 0, 1, 3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 7. 更新现有商品的add_time字段（如果为0）
UPDATE `hiolabs_goods` SET `add_time` = UNIX_TIMESTAMP() WHERE `add_time` = 0;

-- 8. 创建一些示例分销配置（可选）
-- 为前10个商品创建示例分销配置
INSERT IGNORE INTO `hiolabs_goods_distribution` 
(`goods_id`, `is_distributed`, `commission_rate`, `personal_rate`, `level1_rate`, `level2_rate`, `team_leader_rate`, `min_level_required`, `add_time`, `update_time`)
SELECT 
    `id` as goods_id,
    1 as is_distributed,
    CASE 
        WHEN `retail_price` <= 100 THEN 8.00
        WHEN `retail_price` <= 500 THEN 10.00
        ELSE 15.00
    END as commission_rate,
    CASE 
        WHEN `retail_price` <= 100 THEN 8.00
        WHEN `retail_price` <= 500 THEN 10.00
        ELSE 15.00
    END as personal_rate,
    CASE 
        WHEN `retail_price` <= 100 THEN 3.00
        WHEN `retail_price` <= 500 THEN 4.00
        ELSE 6.00
    END as level1_rate,
    CASE 
        WHEN `retail_price` <= 100 THEN 1.00
        WHEN `retail_price` <= 500 THEN 2.00
        ELSE 3.00
    END as level2_rate,
    CASE 
        WHEN `retail_price` <= 100 THEN 2.00
        WHEN `retail_price` <= 500 THEN 3.00
        ELSE 5.00
    END as team_leader_rate,
    1 as min_level_required,
    UNIX_TIMESTAMP() as add_time,
    UNIX_TIMESTAMP() as update_time
FROM `hiolabs_goods` 
WHERE `is_delete` = 0 
LIMIT 10;

-- 完成提示
SELECT '分销池数据库修复完成！' as message;
