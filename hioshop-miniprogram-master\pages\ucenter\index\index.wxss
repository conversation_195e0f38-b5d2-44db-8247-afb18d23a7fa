page {
    min-height: 100%;
    background-color: #f5f5f5;
}

.container {
    min-height: 100%;
    overflow-x: hidden;
}

/* 用户信息区域 */
.user-section {
    background: linear-gradient(135deg, #D4A373 0%, #E9C46A 50%, #F4A261 100%);
    padding-top: 40rpx;
    padding-bottom: 40rpx;
    position: relative;
}

.user-content {
    padding: 0 30rpx;
}

/* 顶部功能按钮 */
.top-actions {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 40rpx;
    gap: 30rpx;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
}

.action-icon {
    width: 40rpx;
    height: 40rpx;
}

.action-text {
    color: white;
    font-size: 24rpx;
}

/* 用户信息 */
.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
}

.user-avatar {
    margin-right: 20rpx;
}

.avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 60rpx;
    border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
    flex: 1;
}

.user-id {
    color: white;
    font-size: 36rpx;
    font-weight: normal;
    margin-bottom: 8rpx;
}

.user-level {
    color: rgba(255, 255, 255, 0.8);
    font-size: 28rpx;
}

/* 会员横幅 */
.member-banner {
    background: rgba(255, 229, 217, 0.9);
    border-radius: 16rpx;
    padding: 20rpx 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.member-text {
    display: flex;
    align-items: center;
    gap: 12rpx;
}

.crown-icon {
    width: 32rpx;
    height: 32rpx;
}

.member-text text {
    color: #8B4513;
    font-size: 28rpx;
}

.register-btn {
    display: flex;
    align-items: center;
    gap: 8rpx;
    background: #D4A373;
    border-radius: 20rpx;
    padding: 8rpx 16rpx;
}

.register-btn text {
    color: white;
    font-size: 24rpx;
}

.arrow-icon {
    width: 20rpx;
    height: 20rpx;
}

/* 数据统计区域 - 复制自个人中心.html样式 */
.stats-section {
    background: white;
    margin: 20rpx 30rpx;
    border-radius: 32rpx;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16rpx;
}

.stats-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx 20rpx;
}

.stats-value {
    font-size: 32rpx;
    font-weight: normal;
    color: #333;
    margin-bottom: 8rpx;
}

.stats-label {
    font-size: 24rpx;
    color: #666;
    text-align: center;
}

/* 营销横幅 - 复制自个人中心.html样式 */
.banner-section {
    margin: 20rpx 30rpx;
    border-radius: 32rpx;
    overflow: hidden;
    background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
}

.banner-image {
    width: 100%;
    height: 200rpx;
}

/* 订单区域 */
.order-section {
    background: white;
    margin: 20rpx 30rpx;
    border-radius: 32rpx;
    padding: 30rpx;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
}

.section-title {
    font-size: 32rpx;
    font-weight: normal;
    color: #333;
}

.section-more {
    display: flex;
    align-items: center;
    gap: 8rpx;
}

.section-more text {
    font-size: 26rpx;
    color: #999;
}

.order-icons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10rpx;
}

.order-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12rpx;
    padding: 20rpx 10rpx;
    min-height: 120rpx;
    box-sizing: border-box;
    background: transparent;
    border: none;
    font-size: inherit;
    line-height: inherit;
}

.order-item::after {
    border: none;
}

.order-icon-wrap {
    position: relative;
}

.order-icon {
    width: 48rpx;
    height: 48rpx;
}

.badge {
    position: absolute;
    top: -8rpx;
    right: -8rpx;
    background: #ff4757;
    color: white;
    font-size: 20rpx;
    border-radius: 12rpx;
    padding: 2rpx 8rpx;
    min-width: 20rpx;
    text-align: center;
}

.order-text {
    font-size: 24rpx;
    color: #666;
}





/* 底部公司信息 */
.company {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 40rpx 0 80rpx 0;
    padding: 0 30rpx;
}

.company .c-wrap {
    width: 60%;
    position: relative;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    display: flex;
    justify-content: center;
}

.company .c-wrap .line {
    height: 40rpx;
    width: 100%;
    border-bottom: 2rpx solid #ddd;
    position: absolute;
    top: 20rpx;
}

.company .c-wrap .text {
    background: #f5f5f5;
    color: #999;
    font-size: 28rpx;
    text-align: center;
    padding: 0 20rpx;
    z-index: 2;
}

.company .tip {
    font-size: 24rpx;
    color: #b3b3b3;
    margin-top: 10rpx;
}

/* 样式文件已完成，所有旧样式已清理 */
