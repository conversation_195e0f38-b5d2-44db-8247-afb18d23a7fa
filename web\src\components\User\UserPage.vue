<template>
	<div class="user-management-page">
		<!-- 页面头部 -->
		<div class="page-header">
			<div class="header-content">
				<div class="title-section">
					<h1 class="page-title">
						<i class="el-icon-user"></i>
						用户管理
					</h1>
					<p class="page-subtitle">管理系统用户信息，查看用户详情和推广员状态</p>
				</div>
				<div class="stats-section">
					<div class="stat-card">
						<div class="stat-number">{{ total }}</div>
						<div class="stat-label">总用户数</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 搜索筛选区域 -->
		<div class="filter-section">
			<el-card class="filter-card" shadow="never">
				<div class="filter-content">
					<el-form :inline="true" :model="filterForm" class="filter-form">
						<el-form-item label="用户昵称">
							<el-input
								v-model="filterForm.nickname"
								placeholder="请输入用户昵称"
								prefix-icon="el-icon-search"
								clearable
								style="width: 200px;">
							</el-input>
						</el-form-item>
						<el-form-item>
							<el-button type="primary" icon="el-icon-search" @click="onSubmitFilter">
								搜索
							</el-button>
							<el-button icon="el-icon-refresh" @click="resetFilter">
								重置
							</el-button>
						</el-form-item>
					</el-form>
				</div>
			</el-card>
		</div>

		<!-- 用户列表区域 -->
		<div class="table-section">
			<el-card class="table-card" shadow="never">
				<div class="table-header">
					<h3 class="table-title">用户列表</h3>
					<div class="table-actions">
						<el-button size="small" icon="el-icon-refresh" @click="getList">刷新</el-button>
					</div>
				</div>
				<el-table
					:data="tableData"
					class="modern-table"
					style="width: 100%"
					:header-cell-style="{ background: '#fafafa', color: '#606266', fontWeight: '500' }"
					:row-style="{ height: '60px' }"
					empty-text="暂无用户数据">
					<el-table-column prop="id" label="ID" width="80" align="center">
						<template slot-scope="scope">
							<span class="user-id">#{{ scope.row.id }}</span>
						</template>
					</el-table-column>

					<el-table-column label="用户信息" min-width="200">
						<template slot-scope="scope">
							<div class="user-info">
								<div class="avatar-container">
									<img :src="getAvatarUrl(scope.row.avatar)"
									     alt="用户头像"
									     class="user-avatar"
									     @error="handleImageError">
								</div>
								<div class="user-details">
									<div class="user-name">
										<el-input
											v-model="scope.row.nickname"
											placeholder="用户昵称"
											size="small"
											class="nickname-input"
											@blur="submitNick(scope.$index, scope.row)">
										</el-input>
									</div>
									<div class="user-meta">
										<span class="gender-tag" :class="scope.row.gender == 2 ? 'female' : 'male'">
											<i :class="scope.row.gender == 2 ? 'el-icon-female' : 'el-icon-male'"></i>
											{{ scope.row.gender == 2 ? '女' : '男' }}
										</span>
									</div>
								</div>
							</div>
						</template>
					</el-table-column>

					<el-table-column label="联系方式" width="140">
						<template slot-scope="scope">
							<div class="contact-info">
								<div class="mobile-info">
									<i class="el-icon-phone"></i>
									<span class="mobile-text">{{ scope.row.mobile || '未绑定' }}</span>
								</div>
							</div>
						</template>
					</el-table-column>

					<el-table-column label="身份状态" width="120" align="center">
						<template slot-scope="scope">
							<el-tag
								:type="scope.row.is_distributor ? 'success' : 'info'"
								size="medium"
								class="status-tag">
								<i :class="scope.row.is_distributor ? 'el-icon-star-on' : 'el-icon-user'"></i>
								{{ scope.row.is_distributor ? '推广员' : '普通用户' }}
							</el-tag>
						</template>
					</el-table-column>

					<el-table-column label="注册时间" width="160">
						<template slot-scope="scope">
							<div class="time-info">
								<i class="el-icon-time"></i>
								<span>{{ scope.row.register_time }}</span>
							</div>
						</template>
					</el-table-column>

					<el-table-column label="最近登录" width="160">
						<template slot-scope="scope">
							<div class="time-info">
								<i class="el-icon-clock"></i>
								<span>{{ scope.row.last_login_time }}</span>
							</div>
						</template>
					</el-table-column>

					<el-table-column label="操作" width="120" align="center">
						<template slot-scope="scope">
							<el-button
								size="small"
								type="primary"
								icon="el-icon-edit"
								class="action-btn"
								@click="handleRowEdit(scope.$index, scope.row)">
								编辑
							</el-button>
						</template>
					</el-table-column>
				</el-table>

				<!-- 分页区域 -->
				<div class="pagination-section">
					<el-pagination
						background
						@current-change="handlePageChange"
						:current-page.sync="page"
						:page-size="10"
						layout="total, prev, pager, next, jumper"
						:total="total"
						class="modern-pagination">
					</el-pagination>
				</div>
			</el-card>
		</div>
	</div>
</template>

<script>

export default {
	data() {
		return {
			page: 1,
			total: 0,
			filterForm: {
				name: ''
			},
			tableData: [],
            loginInfo:null,
            username:''
		}
	},
	methods: {
		// 处理头像URL
		getAvatarUrl(avatar) {
			console.log('用户头像URL:', avatar);

			if (!avatar || avatar === undefined || avatar === null || avatar === '') {
				console.log('头像为空，使用默认头像');
				return 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';
			}

			// 如果是完整的URL，直接返回
			if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
				return avatar;
			}

			// 如果是相对路径，补充域名
			if (avatar.startsWith('/')) {
				return `https://ht.rxkjsdj.com${avatar}`;
			}

			// 其他情况使用默认头像
			return 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';
		},

		// 图片加载失败处理
		handleImageError(event) {
			console.log('头像加载失败，使用默认头像');
			event.target.src = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';
		},

        submitNick(index, row){
            this.axios.post('user/updateInfo', { id: row.id,nickname:row.nickname }).then((response) => {

            })
		},
		handlePageChange(val) {
			this.page = val;
			//保存到localStorage
			localStorage.setItem('userPage', this.page)
			this.getList()
		},
		handleRowEdit(index, row) {
			this.$router.push({ name: 'user_add', query: { id: row.id } })
		},
		handleRowDelete(index, row) {

			this.$confirm('确定要删除?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {

				this.axios.post('user/destory', { id: row.id }).then((response) => {
					console.log(response.data)
					if (response.data.errno === 0) {
						this.$message({
							type: 'success',
							message: '删除成功!'
						});

						this.getList();
					}
				})


			});
		},
		onSubmitFilter() {
			this.page = 1
			this.getList()
		},

		// 重置筛选条件
		resetFilter() {
			this.filterForm.nickname = '';
			this.page = 1;
			this.getList();
		},
		getList() {
			this.axios.get('user', {
				params: {
					page: this.page,
                    nickname: this.filterForm.nickname
				}
			}).then((response) => {
                console.log(response.data);
                console.log(response);
                this.tableData = response.data.data.userData.data;
                this.page = response.data.data.userData.currentPage;
                this.total = response.data.data.userData.count;
			})
            if(!this.loginInfo){
                this.loginInfo = JSON.parse(window.localStorage.getItem('userInfo') || null);
                this.username = this.loginInfo.username;
            }
		}
	},
	components: {

	},
	mounted() {
		let thePage = localStorage.getItem('userPage');
        if(thePage == null){
            thePage = 1;
        }
		this.page = Number(thePage);
        console.log(this.page);
		this.getList();
	}
}

</script>

<style scoped>
/* 页面整体布局 */
.user-management-page {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px;
  border-radius: 12px;
  color: white;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.title-section {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title i {
  font-size: 32px;
}

.page-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.stats-section {
  display: flex;
  gap: 20px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.15);
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  backdrop-filter: blur(10px);
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20px;
}

.filter-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.filter-content {
  padding: 10px 0;
}

.filter-form {
  margin: 0;
}

/* 表格区域 */
.table-section {
  margin-bottom: 20px;
}

.table-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0 20px;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 10px;
}

/* 现代化表格样式 */
.modern-table {
  margin-top: 20px;
}

.modern-table ::v-deep .el-table__header-wrapper {
  border-radius: 8px 8px 0 0;
}

.modern-table ::v-deep .el-table__row {
  transition: all 0.3s ease;
}

.modern-table ::v-deep .el-table__row:hover {
  background-color: #f8f9ff !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 用户ID样式 */
.user-id {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 600;
  color: #909399;
  background: #f4f4f5;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

/* 用户信息区域 */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar-container {
  flex-shrink: 0;
}

.user-avatar {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #e4e7ed;
  transition: all 0.3s ease;
}

.user-avatar:hover {
  border-color: #409eff;
  transform: scale(1.1);
}

.user-details {
  flex: 1;
  min-width: 0;
}

.nickname-input {
  margin-bottom: 4px;
}

.nickname-input ::v-deep .el-input__inner {
  border: none;
  background: transparent;
  padding: 4px 8px;
  font-weight: 500;
  color: #303133;
}

.nickname-input ::v-deep .el-input__inner:focus {
  background: #fff;
  border: 1px solid #409eff;
  border-radius: 4px;
}

.user-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.gender-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.gender-tag.male {
  background: #e1f3ff;
  color: #409eff;
}

.gender-tag.female {
  background: #fde2e2;
  color: #f56c6c;
}

/* 联系方式 */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.mobile-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #606266;
  font-size: 14px;
}

.mobile-info i {
  color: #909399;
}

.mobile-text {
  font-family: 'Monaco', 'Menlo', monospace;
}

/* 状态标签 */
.status-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border-radius: 16px;
  font-weight: 500;
}

/* 时间信息 */
.time-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #606266;
  font-size: 13px;
}

.time-info i {
  color: #909399;
}

/* 操作按钮 */
.action-btn {
  border-radius: 6px;
  font-weight: 500;
}

/* 分页区域 */
.pagination-section {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
  margin-top: 20px;
}

.modern-pagination {
  display: flex;
  justify-content: center;
}

.modern-pagination ::v-deep .el-pager li {
  border-radius: 6px;
  margin: 0 2px;
}

.modern-pagination ::v-deep .btn-prev,
.modern-pagination ::v-deep .btn-next {
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-management-page {
    padding: 10px;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

/* 动画效果 */
.filter-card,
.table-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.modern-table ::v-deep .el-loading-mask {
  border-radius: 8px;
}
</style>
