var app = getApp();
var WxParse = require('../../lib/wxParse/wxParse.js');
var util = require('../../utils/util.js');
var timer = require('../../utils/wxTimer.js');
var api = require('../../config/api.js');
const user = require('../../services/user.js');
Page({
    data: {
        id: 0,
        goods: {},
        gallery: [],
        galleryImages:[],
        specificationList: [],
        productList: [],
        cartGoodsCount: 0,
        checkedSpecPrice: 0,
        number: 1,
        checkedSpecText: '',
        tmpSpecText: '请选择规格和数量',
        openAttr: false,
        soldout: false,
        disabled: '',
        alone_text: '单独购买',
        userId: 0,
        shareUserId: 0,
        priceChecked: false,
        goodsNumber: 0,
        loading: 0,
        current: 0,
        showShareDialog:0,
        userInfo:{},
        autoplay:true,
        commissionAmount: '0.00' // 推广佣金金额
    },
    hideDialog: function (e) {
        let that = this;
        that.setData({
            showShareDialog: false,
        });
    },
    shareTo:function(){
        // 检查商品是否支持推广
        if (this.data.goods.is_distributed !== 1) {
            wx.showToast({
                title: '该商品不支持推广',
                icon: 'none',
                duration: 2000
            });
            return false;
        }

        let userInfo = wx.getStorageSync('userInfo');
        if (userInfo == '') {
            util.loginNow();
            return false;
        } else {
            this.setData({
                showShareDialog: !this.data.showShareDialog,
            });
        }
    },
    createShareImage: function () {
        // 检查商品是否支持推广
        if (this.data.goods.is_distributed !== 1) {
            wx.showToast({
                title: '该商品不支持推广',
                icon: 'none',
                duration: 2000
            });
            return false;
        }

        let id = this.data.id;
        let userId = this.data.userId;

        // 记录二维码分享生成
        if (userId > 0) {
            this.recordShareVisitByType(id, userId, 'qrcode');
        }

        wx.navigateTo({
            url: '/pages/share/index?goodsid=' + id + '&userId=' + userId + '&shareType=qrcode'
        })
    },
    previewImage: function (e) {
        let current = e.currentTarget.dataset.src;
        let that = this;
        wx.previewImage({
            current: current, // 当前显示图片的http链接  
            urls: that.data.galleryImages // 需要预览的图片http链接列表  
        })
    },
    bindchange: function(e) {
        let current = e.detail.current;
        this.setData({
            current: current
        })
    },
    inputNumber(event) {
        let number = event.detail.value;
        this.setData({
            number: number
        });
    },
    goIndex: function() {
        wx.switchTab({
            url: '/pages/index/index',
        })
    },
    onShareAppMessage: function(res) {
        // 检查商品是否支持推广
        if (this.data.goods.is_distributed !== 1) {
            wx.showToast({
                title: '该商品不支持推广',
                icon: 'none',
                duration: 2000
            });
            return {
                title: '商品详情',
                path: '/pages/goods/goods?id=' + this.data.id,
                imageUrl: this.data.goods.list_pic_url
            };
        }

        let id = this.data.id;
        let name = this.data.goods.name;
        let image = this.data.goods.list_pic_url;
        let userId = this.data.userId;

        // 记录好友分享访问
        if (userId > 0) {
            this.recordShareVisitByType(id, userId, 'friend');
        }

        return {
            title: name,
            path: '/pages/goods/goods?id=' + id + '&&userId=' + userId + '&&shareType=friend',
            imageUrl: image
        }
    },
    onUnload: function() {},
    handleTap: function(event) { //阻止冒泡 
    },
    getGoodsInfo: function() {
        let that = this;
        console.log('准备请求商品详情，商品ID:', that.data.id, '类型:', typeof that.data.id);
        util.request(api.GoodsDetail, {
            id: that.data.id
        }).then(function(res) {
            console.log('商品详情API响应:', res);
            if (res.errno === 0) {
                let _specificationList = res.data.specificationList;
                // 如果仅仅存在一种货品，那么商品页面初始化时默认checked
                if (_specificationList.valueList.length == 1) {
                    _specificationList.valueList[0].checked = true
                    that.setData({
                        checkedSpecText: '已选择：' + _specificationList.valueList[0].value,
                        tmpSpecText: '已选择：' + _specificationList.valueList[0].value,
                    });
                } else {
                    that.setData({
                        checkedSpecText: '请选择规格和数量'
                    });
                }
                let galleryImages = [];
                for (const item of res.data.gallery) {
                    galleryImages.push(item.img_url);
                }
                // 计算推广佣金金额
                let commissionAmount = '0.00';
                if (res.data.info.is_distributed === 1 && res.data.info.personal_rate > 0) {
                    const price = parseFloat(res.data.info.retail_price || 0);
                    const rate = parseFloat(res.data.info.personal_rate || 0);
                    const commission = (price * rate / 100).toFixed(2);
                    commissionAmount = commission;
                }

                that.setData({
                    goods: res.data.info,
                    goodsNumber: res.data.info.goods_number,
                    gallery: res.data.gallery,
                    specificationList: res.data.specificationList,
                    productList: res.data.productList,
                    checkedSpecPrice: res.data.info.min_retail_price || res.data.info.retail_price,
                    galleryImages: galleryImages,
                    loading:1,
                    commissionAmount: commissionAmount
                });
                setTimeout(() => {
                    WxParse.wxParse('goodsDetail', 'html', res.data.info.goods_desc, that);
                }, 1000);
                // 保存商品图片URL，优先使用https_pic_url，如果没有则使用list_pic_url
                let goodsImageUrl = res.data.info.https_pic_url || res.data.info.list_pic_url;
                console.log('保存商品图片URL:', goodsImageUrl);
                wx.setStorageSync('goodsImage', goodsImageUrl);
            }
            else{
                util.showErrorToast(res.errmsg)
            }
        });
    },
    clickSkuValue: function(event) {
        // goods_specification中的id 要和product中的goods_specification_ids要一样
        let that = this;
        let specNameId = event.currentTarget.dataset.nameId;
        let specValueId = event.currentTarget.dataset.valueId;
        let index = event.currentTarget.dataset.index;
        //判断是否可以点击
        let _specificationList = this.data.specificationList;
        if (_specificationList.specification_id == specNameId) {
            for (let j = 0; j < _specificationList.valueList.length; j++) {
                if (_specificationList.valueList[j].id == specValueId) {
                    //如果已经选中，则反选
                    if (_specificationList.valueList[j].checked) {
                        _specificationList.valueList[j].checked = false;
                    } else {
                        _specificationList.valueList[j].checked = true;
                    }
                } else {
                    _specificationList.valueList[j].checked = false;
                }
            }
        }
        this.setData({
            'specificationList': _specificationList
        });
        //重新计算spec改变后的信息
        this.changeSpecInfo();

        //重新计算哪些值不可以点击
    },
    //获取选中的规格信息
    getCheckedSpecValue: function() {
        let checkedValues = [];
        let _specificationList = this.data.specificationList;
        let _checkedObj = {
            nameId: _specificationList.specification_id,
            valueId: 0,
            valueText: ''
        };
        for (let j = 0; j < _specificationList.valueList.length; j++) {
            if (_specificationList.valueList[j].checked) {
                _checkedObj.valueId = _specificationList.valueList[j].id;
                _checkedObj.valueText = _specificationList.valueList[j].value;
            }
        }
        checkedValues.push(_checkedObj);
        return checkedValues;
    },
    //根据已选的值，计算其它值的状态
    setSpecValueStatus: function() {

    },
    //判断规格是否选择完整
    isCheckedAllSpec: function() {
        return !this.getCheckedSpecValue().some(function(v) {
            if (v.valueId == 0) {
                return true;
            }
        });
    },
    getCheckedSpecKey: function() {
        let checkedValue = this.getCheckedSpecValue().map(function(v) {
            return v.valueId;
        });
        return checkedValue.join('_');
    },
    changeSpecInfo: function() {
        let checkedNameValue = this.getCheckedSpecValue();
        this.setData({
            disabled: '',
            number: 1
        });
        //设置选择的信息
        let checkedValue = checkedNameValue.filter(function(v) {
            if (v.valueId != 0) {
                return true;
            } else {
                return false;
            }
        }).map(function(v) {
            return v.valueText;
        });
        if (checkedValue.length > 0) {
            this.setData({
                tmpSpecText: '已选择：' + checkedValue.join('　'),
                priceChecked: true

            });
        } else {
            this.setData({
                tmpSpecText: '请选择规格和数量',
                priceChecked: false
            });
        }

        if (this.isCheckedAllSpec()) {
            this.setData({
                checkedSpecText: this.data.tmpSpecText
            });

            // 点击规格的按钮后
            // 验证库存
            let checkedProductArray = this.getCheckedProductItem(this.getCheckedSpecKey());
            if (!checkedProductArray || checkedProductArray.length <= 0) {
                this.setData({
                    soldout: true
                });
                // console.error('规格所对应货品不存在');
                wx.showToast({
                    image: '/images/icon/icon_error.png',
                    title: '规格所对应货品不存在',
                });
                return;
            }
            let checkedProduct = checkedProductArray[0];
            if (checkedProduct.goods_number < this.data.number) {
                //找不到对应的product信息，提示没有库存
                this.setData({
                    checkedSpecPrice: checkedProduct.retail_price,
                    goodsNumber: checkedProduct.goods_number,
                    soldout: true
                });
                wx.showToast({
                    image: '/images/icon/icon_error.png',
                    title: '库存不足',
                });
                return false;
            }
            if (checkedProduct.goods_number > 0) {
                this.setData({
                    checkedSpecPrice: checkedProduct.retail_price,
                    goodsNumber: checkedProduct.goods_number,
                    soldout: false
                });

                var checkedSpecPrice = checkedProduct.retail_price;

            } else {
                this.setData({
                    checkedSpecPrice: this.data.goods.min_retail_price || this.data.goods.retail_price,
                    soldout: true
                });
            }
        } else {
            this.setData({
                checkedSpecText: '请选择规格和数量',
                checkedSpecPrice: this.data.goods.min_retail_price || this.data.goods.retail_price,
                soldout: false
            });
        }
    },
    getCheckedProductItem: function(key) {
        return this.data.productList.filter(function(v) {
            if (v.goods_specification_ids == key) {
                return true;
            } else {
                return false;
            }
        });
    },
    onLoad: function(options) {
        let id = 0;
        let shareUserId = 0;
        let shareType = 'other'; // 默认为其他
        var scene = decodeURIComponent(options.scene);
        console.log('原始参数:', options);
        console.log('解码后的scene:', scene);

        if (scene != 'undefined' && scene) {
            // 解析场景值，支持 goodsId=123 格式
            if (scene.includes('goodsId=')) {
                id = scene.split('goodsId=')[1];
                shareType = 'qrcode'; // 扫码进入默认为二维码分享
            } else {
                // 处理二维码场景值：可能是 "商品ID" 或 "商品ID,分享人ID"
                if (scene.includes(',')) {
                    let sceneParams = scene.split(',');
                    id = sceneParams[0];
                    shareUserId = sceneParams[1] || 0;
                    shareType = 'qrcode'; // 扫码进入为二维码分享
                    console.log('从二维码获取分享人ID:', shareUserId);
                } else {
                    id = scene;
                    shareType = 'qrcode'; // 扫码进入为二维码分享
                }
            }
            // 确保ID是数字类型
            id = parseInt(id);
            shareUserId = parseInt(shareUserId);
            console.log('从扫码获取商品ID:', id, '类型:', typeof id);
        } else {
            id = options.id;
            shareUserId = options.userId || 0;
            shareType = options.shareType || 'other'; // 从URL参数获取分享类型
            // 确保ID是数字类型
            id = parseInt(id);
            shareUserId = parseInt(shareUserId);
            console.log('从普通跳转获取商品ID:', id, '分享人ID:', shareUserId, '分享类型:', shareType, '类型:', typeof id);
        }

        // 如果有分享人ID，保存到本地存储用于统计
        if (shareUserId > 0) {
            wx.setStorageSync('shareUserId', shareUserId);
            console.log('保存分享人ID到本地存储:', shareUserId);

            // 延迟记录分享访问统计，确保页面加载完成
            setTimeout(() => {
                console.log('准备记录分享访问，商品ID:', id, '分享人ID:', shareUserId, '分享类型:', shareType);
                this.recordShareVisitByType(id, shareUserId, shareType);
            }, 1000);
        }

        this.setData({
            id: id, // 这个是商品id
            valueId: id,
            shareUserId: shareUserId, // 分享人ID
            shareType: shareType, // 分享类型
        });
    },
    recordShareVisit: function(goodsId, shareUserId) {
        // 记录推广访问统计（默认为其他来源）
        this.recordShareVisitByType(goodsId, shareUserId, 'other');
    },

    recordShareVisitByType: function(goodsId, shareUserId, shareType) {
        // 记录推广访问统计
        console.log('=== 开始记录推广访问 ===');
        console.log('商品ID:', goodsId);
        console.log('推广员用户ID:', shareUserId);
        console.log('分享类型:', shareType);
        console.log('访问时间:', new Date().toLocaleString());

        // 检查参数有效性
        if (!goodsId || !shareUserId || shareUserId <= 0) {
            console.log('参数无效，跳过推广访问记录');
            return;
        }

        // 调用后端接口记录推广访问
        util.request(api.RecordPromotionVisit, {
            goodsId: goodsId,
            promoterUserId: shareUserId,
            shareSource: shareType // 传递具体的分享类型
        }, 'POST').then(function(res) {
            console.log('推广访问记录API响应:', res);
            if (res.errno === 0) {
                console.log('✅ 推广访问记录成功');
            } else {
                console.log('❌ 推广访问记录失败:', res.errmsg);
            }
        }).catch(function(error) {
            console.log('❌ 推广访问记录异常:', error);
        });
    },
    onShow: function() {
        let userInfo = wx.getStorageSync('userInfo');
        let info = wx.getSystemInfoSync();
        let sysHeight = info.windowHeight - 100;
        let userId = userInfo.id;
        if (userId > 0) {
            this.setData({
                userId: userId,
                userInfo: userInfo,
            });
        }
        this.setData({
            priceChecked: false,
            sysHeight: sysHeight,
            autoplay: true
        })
        this.getGoodsInfo();
        this.getCartCount();
    },
    onHide:function(){
        this.setData({
            autoplay:false
        })
    },
    getCartCount: function() {
        let that = this;
        util.request(api.CartGoodsCount).then(function(res) {
            if (res.errno === 0) {
                that.setData({
                    cartGoodsCount: res.data.cartTotal.goodsCount
                });
            }
        });
    },
    onPullDownRefresh: function() {
        wx.showNavigationBarLoading()
        this.getGoodsInfo();
        wx.hideNavigationBarLoading() //完成停止加载
        wx.stopPullDownRefresh() //停止下拉刷新
    },
    openCartPage: function() {
        wx.switchTab({
            url: '/pages/cart/cart',
        });
    },
    goIndexPage: function() {
        wx.switchTab({
            url: '/pages/index/index',
        });
    },
    switchAttrPop: function() {
        if (this.data.openAttr == false) {
            this.setData({
                openAttr: !this.data.openAttr
            });
        }
    },
    closeAttr: function() {
        this.setData({
            openAttr: false,
            alone_text: '单独购买'
        });
    },
    goMarketing: function(e) {
        let that = this;
        that.setData({
            showDialog: !this.data.showDialog
        });
    },
    addToCart: function() {
        // 判断是否登录，如果没有登录，则登录
        util.loginNow();
        var that = this;
        let userInfo = wx.getStorageSync('userInfo');
        let productLength = this.data.productList.length;
        if (userInfo == '') {
            return false;
        }
        if (this.data.openAttr == false && productLength != 1) {
            //打开规格选择窗口
            this.setData({
                openAttr: !that.data.openAttr
            });
            this.setData({
                alone_text: '加入购物车'
            })
        } else {
            //提示选择完整规格
            if (!this.isCheckedAllSpec()) {
                wx.showToast({
                    image: '/images/icon/icon_error.png',
                    title: '请选择规格',
                });
                return false;
            }
            //根据选中的规格，判断是否有对应的sku信息
            let checkedProductArray = this.getCheckedProductItem(this.getCheckedSpecKey());
            if (!checkedProductArray || checkedProductArray.length <= 0) {
                //找不到对应的product信息，提示没有库存
                wx.showToast({
                    image: '/images/icon/icon_error.png',
                    title: '库存不足',
                });
                return false;
            }
            let checkedProduct = checkedProductArray[0];
            //验证库存
            if (checkedProduct.goods_number < this.data.number) {
                //要买的数量比库存多
                wx.showToast({
                    image: '/images/icon/icon_error.png',
                    title: '库存不足',
                });
                return false;
            }
            wx.showLoading({
              title: '',
              mask:true
            })
            util.request(api.CartAdd, {
                    addType: 0,
                    goodsId: this.data.id,
                    number: this.data.number,
                    productId: checkedProduct.id
                }, "POST")
                .then(function(res) {
                    let _res = res;
                    if (_res.errno == 0) {
                        wx.showToast({
                            title: '添加成功',
                        });
                        if (productLength != 1 || that.data.openAttr == true) {
                            that.setData({
                                openAttr: !that.data.openAttr,
                                cartGoodsCount: _res.data.cartTotal.goodsCount
                            });
                        } else {
                            that.setData({
                                cartGoodsCount: _res.data.cartTotal.goodsCount
                            });
                        }
                    } else {
                        wx.showToast({
                            image: '/images/icon/icon_error.png',
                            title: _res.errmsg,
                        });
                    }
                    wx.hideLoading()
                });
        }
    },
    fastToCart: function() {
        // 判断是否登录，如果没有登录，则登录
        util.loginNow();
        let userInfo = wx.getStorageSync('userInfo');
        if (userInfo == '') {
            return false;
        }
        var that = this;
        if (this.data.openAttr === false) {
            //打开规格选择窗口
            this.setData({
                openAttr: !this.data.openAttr
            });
            that.setData({
                alone_text: '加入购物车'
            })
        } else {
            //提示选择完整规格
            if (!this.isCheckedAllSpec()) {
                wx.showToast({
                    image: '/images/icon/icon_error.png',
                    title: '请选择规格',
                });
                return false;
            }
            //根据选中的规格，判断是否有对应的sku信息
            let checkedProductArray = this.getCheckedProductItem(this.getCheckedSpecKey());
            if (!checkedProductArray || checkedProductArray.length <= 0) {
                //找不到对应的product信息，提示没有库存
                wx.showToast({
                    image: '/images/icon/icon_error.png',
                    title: '库存不足',
                });
                return false;
            }
            let checkedProduct = checkedProductArray[0];
            //验证库存
            if (checkedProduct.goods_number < this.data.number) {
                //要买的数量比库存多
                wx.showToast({
                    image: '/images/icon/icon_error.png',
                    title: '库存不足',
                });
                return false;
            }
            //添加到购物车
            wx.showLoading({
                title: '',
                mask:true
              })
            util.request(api.CartAdd, {
                    addType: 1, // 0：正常加入购物车，1:立即购买，2:再来一单
                    goodsId: this.data.id,
                    number: this.data.number,
                    productId: checkedProduct.id,
                }, "POST")
                .then(function(res) {
                    let _res = res;
                    wx.hideLoading()
                    if (_res.errno == 0) {
                        let id = that.data.id;
                        wx.navigateTo({
                            url: '/pages/order-check/index?addtype=1'
                        });
                    } else {
                        wx.showToast({
                            image: '/images/icon/icon_error.png',
                            title: _res.errmsg,
                        });
                    }
                });
        }
    },
    cutNumber: function() {
        this.setData({
            number: (this.data.number - 1 > 1) ? this.data.number - 1 : 1
        });
        this.setData({
            disabled: ''
        });
    },
    addNumber: function() {
        this.setData({
            number: Number(this.data.number) + 1
        });
        let checkedProductArray = this.getCheckedProductItem(this.getCheckedSpecKey());
        let checkedProduct = checkedProductArray;
        var check_number = this.data.number + 1;
        if (checkedProduct.goods_number < check_number) {
            this.setData({
                disabled: true
            });
        }
    }
})