{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\order.js"], "names": ["Base", "require", "moment", "_", "WangDianSync", "module", "exports", "indexAction", "page", "get", "size", "orderSn", "consignee", "logistic_code", "status", "data", "model", "whereCondition", "order_sn", "order_status", "order_type", "excludeRefundStatuses", "shouldExcludeRefund", "some", "includes", "s", "refundOrderIds", "field", "select", "excludeOrderIds", "map", "item", "order_id", "length", "id", "where", "order", "countSelect", "console", "log", "orderData", "find", "goodsList", "is_delete", "goodsCount", "for<PERSON>ach", "v", "number", "user", "user_id", "think", "isEmpty", "nickname", "<PERSON><PERSON><PERSON>", "from", "toString", "e", "message", "name", "mobile", "avatar", "userInfo", "province_name", "province", "getField", "city_name", "city", "district_name", "district", "full_region", "postscript", "add_time", "unix", "format", "pay_time", "order_status_text", "getOrderStatusText", "express", "expressInfo", "shipper_name", "refundApply", "refundInfo", "refund_amount", "refund_reason", "apply_time", "status_text", "getRefundStatusText", "hasRefund", "success", "statusMap", "refundListAction", "refundStatus", "refundType", "processStatus", "refund_type", "refundModel", "alias", "join", "table", "as", "on", "images", "JSON", "parse", "apply_time_text", "order_add_time_text", "order_add_time", "count", "error", "fail", "handleRefundAction", "applyId", "post", "action", "adminMemo", "rejectReason", "currentTime", "parseInt", "Date", "getTime", "updateData", "process_time", "updated_at", "admin_memo", "orderInfo", "pay_status", "WeixinService", "service", "refundNo", "now", "Math", "random", "substr", "toUpperCase", "refundParams", "out_trade_no", "out_refund_no", "total_fee", "actual_price", "refund_fee", "refund_desc", "refundResult", "createRefund", "complete_time", "update", "refund_time", "commissionService", "handleRefundCommission", "commissionError", "error_msg", "refundError", "orderGoods", "wangdianSync", "result", "pushOrderRefund", "return_address", "return_contact", "return_phone", "reject_reason", "newStatus", "getStatusCountAction", "oneMonthAgo", "floor", "statusCounts", "all", "toPay", "toDelivery", "toReceive", "received", "closed", "refundCompleted", "updateServiceMemoAction", "orderId", "flagColor", "priority", "orderExists", "service_flag_color", "service_priority", "service_updated_at", "service_updated_by", "getServiceMemoAction", "getAutoStatusAction", "info", "autoDelivery", "toDeliveryAction", "address", "button_text", "getOrderBtnText", "saveGoodsListAction", "price", "addOrMinus", "changePrice", "Number", "decrement", "order_price", "goods_price", "generateOrderNumber", "increment", "goodsListDeleteAction", "saveAdminMemoAction", "text", "savePrintInfoAction", "print_info", "saveExpressValueInfoAction", "express_value", "saveRemarkInfoAction", "remark", "detailAction", "product_id", "goods_sn", "_nickname", "user_name", "allAddress", "shipping_time", "confirm_time", "dealdone_time", "def", "senderInfo", "receiveInfo", "province_id", "city_id", "district_id", "Name", "Tel", "ProvinceName", "CityName", "ExpAreaName", "Address", "receiver", "sender", "getAllRegionAction", "aData", "type", "bData", "cData", "newData", "children", "bitem", "innerChildren", "citem", "parent_id", "push", "value", "label", "orderpackAction", "orderReceiveAction", "orderPriceAction", "goodsPrice", "freightPrice", "actualPrice", "freight_price", "getOrderExpressAction", "latestExpressInfo", "getLatestOrderExpressByAli", "getPrintTestAction", "printExpress", "getMianExpressAction", "senderOptions", "receiveOptions", "receiverInfo", "expressType", "getMianExpress", "ResultCode", "orderExpressAdd", "rePrintExpressAction", "date", "getFullYear", "padStart", "getMonth", "getDay", "getHours", "getMinutes", "getSeconds", "directPrintExpressAction", "express_type", "code", "MonthCode", "send_time", "ele", "Order", "ShipperCode", "LogisticCode", "region_code", "DestinatioCode", "MarkDestination", "kdInfo", "kdData", "shipper_id", "shipper_code", "add", "goDeliveryAction", "print_status", "shipping_status", "openId", "weixin_openid", "goodsInfo", "goodsName", "goods_name", "shippingTime", "TEMPLATE_ID", "config", "tokenServer", "token", "getAccessToken", "res", "sendMessage", "syncWeixinShippingInfo", "goPrintOnlyAction", "orderDeliveryAction", "method", "deliveryId", "expressName", "deliveryMessage", "logisticsType", "checkExpressAction", "saveAddressAction", "sn", "c<PERSON><PERSON><PERSON>", "addOptions", "storeAction", "isPost", "values", "is_show", "is_new", "changeStatusAction", "destoryAction", "limit", "delete", "getGoodsSpecificationAction", "goods_id", "openid", "shippingConfig", "enabled", "reason", "weixinService", "shippingData", "buildShippingData", "goods_list", "validationError", "errorInfo", "stringify", "currentOrder", "weixin_shipping_sync", "weixin_shipping_sync_error", "db<PERSON><PERSON>r", "uploadShippingInfo", "weixin_shipping_sync_time", "stack", "resyncWeixinShippingAction"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,SAASD,QAAQ,QAAR,CAAf;AACA,MAAME,IAAIF,QAAQ,QAAR,CAAV;AACA;AACA,MAAMG,eAAeH,QAAQ,+BAAR,CAArB;AACAI,OAAOC,OAAP,GAAiB,cAAcN,IAAd,CAAmB;AAChC;;;;AAIMO,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAMC,OAAO,MAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,MAAKD,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,kBAAME,UAAU,MAAKF,GAAL,CAAS,SAAT,KAAuB,EAAvC;AACA,kBAAMG,YAAY,MAAKH,GAAL,CAAS,WAAT,KAAyB,EAA3C;AACA,kBAAMI,gBAAgB,MAAKJ,GAAL,CAAS,eAAT,KAA6B,EAAnD;AACA,kBAAMK,SAAS,MAAKL,GAAL,CAAS,QAAT,KAAsB,EAArC;AACA,gBAAIM,OAAO,EAAX;AACA,kBAAMC,QAAQ,MAAKA,KAAL,CAAW,OAAX,CAAd;AACA,gBAAIH,iBAAiB,EAArB,EAAyB;AACrB,oBAAII,iBAAiB;AACjBC,8BAAU,CAAC,MAAD,EAAU,IAAGP,OAAQ,GAArB,CADO;AAEjBC,+BAAW,CAAC,MAAD,EAAU,IAAGA,SAAU,GAAvB,CAFM;AAGjBO,kCAAc,CAAC,IAAD,EAAOL,MAAP,CAHG;AAIjBM,gCAAY,CAAC,GAAD,EAAM,CAAN;AAJK,iBAArB;;AAOA;AACA,sBAAMC,wBAAwB,CAAC,KAAD,EAAQ,KAAR,EAAe,KAAf,CAA9B,CATqB,CASgC;AACrD,sBAAMC,sBAAsBD,sBAAsBE,IAAtB,CAA2B;AAAA,2BAAKT,OAAOU,QAAP,CAAgBC,CAAhB,CAAL;AAAA,iBAA3B,CAA5B;;AAEA,oBAAIH,mBAAJ,EAAyB;AACrB;AACA,0BAAMI,iBAAiB,MAAM,MAAKV,KAAL,CAAW,cAAX,EAA2BW,KAA3B,CAAiC,UAAjC,EAA6CC,MAA7C,EAA7B;AACA,0BAAMC,kBAAkBH,eAAeI,GAAf,CAAmB;AAAA,+BAAQC,KAAKC,QAAb;AAAA,qBAAnB,CAAxB;;AAEA,wBAAIH,gBAAgBI,MAAhB,GAAyB,CAA7B,EAAgC;AAC5BhB,uCAAeiB,EAAf,GAAoB,CAAC,QAAD,EAAWL,eAAX,CAApB;AACH;AACJ;;AAEDd,uBAAO,MAAMC,MAAMmB,KAAN,CAAYlB,cAAZ,EAA4BmB,KAA5B,CAAkC,CAAC,SAAD,CAAlC,EAA+C5B,IAA/C,CAAoDA,IAApD,EAA0DE,IAA1D,EAAgE2B,WAAhE,EAAb;AACAC,wBAAQC,GAAR,CAAYxB,IAAZ;AACH,aAxBD,MAwBO;AACH,oBAAIyB,YAAY,MAAM,MAAKxB,KAAL,CAAW,eAAX,EAA4BmB,KAA5B,CAAkC;AACpDtB,mCAAeA;AADqC,iBAAlC,EAEnB4B,IAFmB,EAAtB;AAGA,oBAAIT,WAAWQ,UAAUR,QAAzB;AACAjB,uBAAO,MAAMC,MAAMmB,KAAN,CAAY;AACrBD,wBAAIF;AADiB,iBAAZ,EAEVI,KAFU,CAEJ,CAAC,SAAD,CAFI,EAES5B,IAFT,CAEcA,IAFd,EAEoBE,IAFpB,EAE0B2B,WAF1B,EAAb;AAGH;AACD,iBAAK,MAAMN,IAAX,IAAmBhB,KAAKA,IAAxB,EAA8B;AAC1BgB,qBAAKW,SAAL,GAAiB,MAAM,MAAK1B,KAAL,CAAW,aAAX,EAA0BW,KAA1B,CAAgC,oFAAhC,EAAsHQ,KAAtH,CAA4H;AAC/IH,8BAAUD,KAAKG,EADgI;AAE/IS,+BAAW;AAFoI,iBAA5H,EAGpBf,MAHoB,EAAvB;AAIAG,qBAAKa,UAAL,GAAkB,CAAlB;AACAb,qBAAKW,SAAL,CAAeG,OAAf,CAAuB,aAAK;AACxBd,yBAAKa,UAAL,IAAmBE,EAAEC,MAArB;AACH,iBAFD;AAGA,oBAAIC,OAAO,MAAM,MAAKhC,KAAL,CAAW,MAAX,EAAmBmB,KAAnB,CAAyB;AACtCD,wBAAIH,KAAKkB;AAD6B,iBAAzB,EAEdtB,KAFc,CAER,6BAFQ,EAEuBc,IAFvB,EAAjB;AAGA,oBAAI,CAACS,MAAMC,OAAN,CAAcH,IAAd,CAAL,EAA0B;AACtB;AACA,wBAAIA,KAAKI,QAAT,EAAmB;AACf,4BAAI;AACAJ,iCAAKI,QAAL,GAAgBC,OAAOC,IAAP,CAAYN,KAAKI,QAAjB,EAA2B,QAA3B,EAAqCG,QAArC,EAAhB;AACH,yBAFD,CAEE,OAAOC,CAAP,EAAU;AACRlB,oCAAQC,GAAR,CAAY,iBAAZ,EAA+BiB,EAAEC,OAAjC;AACAT,iCAAKI,QAAL,GAAgB,MAAhB;AACH;AACJ,qBAPD,MAOO;AACHJ,6BAAKI,QAAL,GAAgB,OAAhB;AACH;AACJ,iBAZD,MAYO;AACHJ,2BAAO,EAAEI,UAAU,KAAZ,EAAmBM,MAAM,EAAzB,EAA6BC,QAAQ,EAArC,EAAyCC,QAAQ,EAAjD,EAAP;AACH;AACD7B,qBAAK8B,QAAL,GAAgBb,IAAhB;AACA,oBAAIc,gBAAgB,MAAM,MAAK9C,KAAL,CAAW,QAAX,EAAqBmB,KAArB,CAA2B;AACjDD,wBAAIH,KAAKgC;AADwC,iBAA3B,EAEvBC,QAFuB,CAEd,MAFc,EAEN,IAFM,CAA1B;AAGA,oBAAIC,YAAY,MAAM,MAAKjD,KAAL,CAAW,QAAX,EAAqBmB,KAArB,CAA2B;AAC7CD,wBAAIH,KAAKmC;AADoC,iBAA3B,EAEnBF,QAFmB,CAEV,MAFU,EAEF,IAFE,CAAtB;AAGA,oBAAIG,gBAAgB,MAAM,MAAKnD,KAAL,CAAW,QAAX,EAAqBmB,KAArB,CAA2B;AACjDD,wBAAIH,KAAKqC;AADwC,iBAA3B,EAEvBJ,QAFuB,CAEd,MAFc,EAEN,IAFM,CAA1B;AAGAjC,qBAAKsC,WAAL,GAAmBP,gBAAgBG,SAAhB,GAA4BE,aAA/C;AACApC,qBAAKuC,UAAL,GAAkBjB,OAAOC,IAAP,CAAYvB,KAAKuC,UAAjB,EAA6B,QAA7B,EAAuCf,QAAvC,EAAlB;AACAxB,qBAAKwC,QAAL,GAAgBrE,OAAOsE,IAAP,CAAYzC,KAAKwC,QAAjB,EAA2BE,MAA3B,CAAkC,qBAAlC,CAAhB;AACA,oBAAI1C,KAAK2C,QAAL,IAAiB,CAArB,EAAwB;AACpB3C,yBAAK2C,QAAL,GAAgBxE,OAAOsE,IAAP,CAAYzC,KAAK2C,QAAjB,EAA2BD,MAA3B,CAAkC,qBAAlC,CAAhB;AACH,iBAFD,MAEO;AACH1C,yBAAK2C,QAAL,GAAgB,CAAhB;AACH;AACD3C,qBAAK4C,iBAAL,GAAyB,MAAM,MAAK3D,KAAL,CAAW,OAAX,EAAoB4D,kBAApB,CAAuC7C,KAAKG,EAA5C,CAA/B;AACA,oBAAI2C,UAAU,MAAM,MAAK7D,KAAL,CAAW,eAAX,EAA4BmB,KAA5B,CAAkC;AAClDH,8BAAUD,KAAKG;AADmC,iBAAlC,EAEjBO,IAFiB,EAApB;AAGA,oBAAI,CAACS,MAAMC,OAAN,CAAc0B,OAAd,CAAL,EAA6B;AACzB9C,yBAAK+C,WAAL,GAAmBD,QAAQE,YAAR,GAAuBF,QAAQhE,aAAlD;AACH,iBAFD,MAEO;AACHkB,yBAAK+C,WAAL,GAAmB,EAAnB;AACH;;AAED;AACA,oBAAIE,cAAc,MAAM,MAAKhE,KAAL,CAAW,cAAX,EAA2BmB,KAA3B,CAAiC;AACrDH,8BAAUD,KAAKG;AADsC,iBAAjC,EAErBO,IAFqB,EAAxB;;AAIA,oBAAI,CAACS,MAAMC,OAAN,CAAc6B,WAAd,CAAL,EAAiC;AAC7BjD,yBAAKkD,UAAL,GAAkB;AACd/C,4BAAI8C,YAAY9C,EADF;AAEdpB,gCAAQkE,YAAYlE,MAFN;AAGdoE,uCAAeF,YAAYE,aAHb;AAIdC,uCAAeH,YAAYG,aAJb;AAKdC,oCAAYJ,YAAYI,UALV;AAMdC,qCAAa,MAAKC,mBAAL,CAAyBN,YAAYlE,MAArC;AANC,qBAAlB;AAQAiB,yBAAKwD,SAAL,GAAiB,IAAjB;AACH,iBAVD,MAUO;AACHxD,yBAAKkD,UAAL,GAAkB,IAAlB;AACAlD,yBAAKwD,SAAL,GAAiB,KAAjB;AACH;;AAED;AACH;AACD,mBAAO,MAAKC,OAAL,CAAazE,IAAb,CAAP;AAvHgB;AAwHnB;;AAED;;;AAGAuE,wBAAoBxE,MAApB,EAA4B;AACxB,cAAM2E,YAAY;AACd,uBAAW,KADG;AAEd,0BAAc,KAFA;AAGd,wBAAY,KAHE;AAId,wBAAY,KAJE;AAKd,2BAAe,MALD;AAMd,wBAAY,KANE;AAOd,yBAAa;AAPC,SAAlB;AASA,eAAOA,UAAU3E,MAAV,KAAqB,MAA5B;AACH;;AAED;;;AAGM4E,oBAAN,GAAyB;AAAA;;AAAA;AACrB,kBAAMlF,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,OAAKD,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,kBAAME,UAAU,OAAKF,GAAL,CAAS,SAAT,KAAuB,EAAvC;AACA,kBAAMkF,eAAe,OAAKlF,GAAL,CAAS,cAAT,KAA4B,EAAjD;AACA,kBAAMG,YAAY,OAAKH,GAAL,CAAS,WAAT,KAAyB,EAA3C;AACA,kBAAMmF,aAAa,OAAKnF,GAAL,CAAS,YAAT,KAA0B,EAA7C,CANqB,CAM4B;AACjD,kBAAMoF,gBAAgB,OAAKpF,GAAL,CAAS,eAAT,KAA6B,EAAnD,CAPqB,CAOkC;;AAEvD,gBAAI;AACA6B,wBAAQC,GAAR,CAAY,kBAAZ;AACAD,wBAAQC,GAAR,CAAY,KAAZ,EAAmB/B,IAAnB,EAAyB,KAAzB,EAAgCE,IAAhC,EAAsC,MAAtC,EAA8CC,OAA9C,EAAuD,OAAvD,EAAgEgF,YAAhE;;AAEA;AACA,oBAAI1E,iBAAiB,EAArB;AACA,oBAAIN,OAAJ,EAAa;AACTM,mCAAeC,QAAf,GAA0B,CAAC,MAAD,EAAU,IAAGP,OAAQ,GAArB,CAA1B;AACH;AACD,oBAAIgF,YAAJ,EAAkB;AACd1E,mCAAeH,MAAf,GAAwB6E,YAAxB;AACH;AACD,oBAAIC,UAAJ,EAAgB;AACZ3E,mCAAe6E,WAAf,GAA6BF,UAA7B;AACH;;AAED;AACA,oBAAIC,kBAAkB,SAAtB,EAAiC;AAC7B5E,mCAAeH,MAAf,GAAwB,CAAC,IAAD,EAAO,CAAC,SAAD,EAAY,YAAZ,CAAP,CAAxB;AACH,iBAFD,MAEO,IAAI+E,kBAAkB,WAAtB,EAAmC;AACtC5E,mCAAeH,MAAf,GAAwB,CAAC,IAAD,EAAO,CAAC,UAAD,EAAa,WAAb,EAA0B,UAA1B,CAAP,CAAxB;AACH,iBAFM,MAEA;AACH;AACAG,mCAAeH,MAAf,GAAwB,CAAC,QAAD,EAAW,CAAC,WAAD,EAAc,UAAd,EAA0B,UAA1B,CAAX,CAAxB;AACH;;AAED;AACA,sBAAMiF,cAAc,OAAK/E,KAAL,CAAW,cAAX,CAApB;AACA,sBAAMD,OAAO,MAAMgF,YAAYC,KAAZ,CAAkB,IAAlB,EACdC,IADc,CACT;AACFC,2BAAO,OADL;AAEFD,0BAAM,MAFJ;AAGFE,wBAAI,GAHF;AAIFC,wBAAI,CAAC,aAAD,EAAgB,MAAhB;AAJF,iBADS,EAOdjE,KAPc,CAORlB,cAPQ,EAQdkB,KARc,CAQR;AACH,mCAAe,CAAC,MAAD,EAAU,IAAGvB,SAAU,GAAvB;AADZ,iBARQ,EAWde,KAXc,CAWR,kGAXQ,EAYdS,KAZc,CAYR,CAAC,YAAD,CAZQ,EAad5B,IAbc,CAaTA,IAbS,EAaHE,IAbG,EAcd2B,WAdc,EAAnB;;AAgBA;AACA,qBAAK,MAAMN,IAAX,IAAmBhB,KAAKA,IAAxB,EAA8B;AAC1B;AACA,wBAAIiC,OAAO,MAAM,OAAKhC,KAAL,CAAW,MAAX,EAAmBmB,KAAnB,CAAyB;AACtCD,4BAAIH,KAAKkB;AAD6B,qBAAzB,EAEdtB,KAFc,CAER,6BAFQ,EAEuBc,IAFvB,EAAjB;;AAIA,wBAAI,CAACS,MAAMC,OAAN,CAAcH,IAAd,CAAL,EAA0B;AACtB;AACA,4BAAIA,KAAKI,QAAT,EAAmB;AACf,gCAAI;AACAJ,qCAAKI,QAAL,GAAgBC,OAAOC,IAAP,CAAYN,KAAKI,QAAjB,EAA2B,QAA3B,EAAqCG,QAArC,EAAhB;AACH,6BAFD,CAEE,OAAOC,CAAP,EAAU;AACRlB,wCAAQC,GAAR,CAAY,iBAAZ,EAA+BiB,EAAEC,OAAjC;AACAT,qCAAKI,QAAL,GAAgB,MAAhB;AACH;AACJ,yBAPD,MAOO;AACHJ,iCAAKI,QAAL,GAAgB,OAAhB;AACH;AACJ,qBAZD,MAYO;AACHJ,+BAAO,EAAEI,UAAU,KAAZ,EAAmBM,MAAM,EAAzB,EAA6BC,QAAQ,EAArC,EAAyCC,QAAQ,EAAjD,EAAP;AACH;AACD7B,yBAAK8B,QAAL,GAAgBb,IAAhB;;AAEA;AACAjB,yBAAKW,SAAL,GAAiB,MAAM,OAAK1B,KAAL,CAAW,aAAX,EAA0BW,KAA1B,CAAgC,oFAAhC,EAAsHQ,KAAtH,CAA4H;AAC/IH,kCAAUD,KAAKC,QADgI;AAE/IW,mCAAW;AAFoI,qBAA5H,EAGpBf,MAHoB,EAAvB;;AAKA;AACA,wBAAIG,KAAKsE,MAAT,EAAiB;AACb,4BAAI;AACAtE,iCAAKsE,MAAL,GAAcC,KAAKC,KAAL,CAAWxE,KAAKsE,MAAhB,CAAd;AACH,yBAFD,CAEE,OAAO7C,CAAP,EAAU;AACRzB,iCAAKsE,MAAL,GAAc,EAAd;AACH;AACJ,qBAND,MAMO;AACHtE,6BAAKsE,MAAL,GAAc,EAAd;AACH;;AAED;AACAtE,yBAAKyE,eAAL,GAAuBtG,OAAOsE,IAAP,CAAYzC,KAAKqD,UAAjB,EAA6BX,MAA7B,CAAoC,qBAApC,CAAvB;AACA1C,yBAAK0E,mBAAL,GAA2BvG,OAAOsE,IAAP,CAAYzC,KAAK2E,cAAjB,EAAiCjC,MAAjC,CAAwC,qBAAxC,CAA3B;;AAEA;AACA1C,yBAAKsD,WAAL,GAAmB,OAAKC,mBAAL,CAAyBvD,KAAKjB,MAA9B,CAAnB;AACH;;AAEDwB,wBAAQC,GAAR,CAAY,gBAAZ,EAA8BxB,KAAK4F,KAAnC,EAA0C,KAA1C;AACA,uBAAO,OAAKnB,OAAL,CAAazE,IAAb,CAAP;AAEH,aAhGD,CAgGE,OAAO6F,KAAP,EAAc;AACZtE,wBAAQsE,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO,OAAKC,IAAL,CAAU,iBAAiBD,MAAMnD,OAAjC,CAAP;AACH;AA5GoB;AA6GxB;;AAED;;;AAGMqD,sBAAN,GAA2B;AAAA;;AAAA;AACvB,kBAAMC,UAAU,OAAKC,IAAL,CAAU,SAAV,CAAhB;AACA,kBAAMC,SAAS,OAAKD,IAAL,CAAU,QAAV,CAAf,CAFuB,CAEa;AACpC,kBAAME,YAAY,OAAKF,IAAL,CAAU,WAAV,KAA0B,EAA5C;AACA,kBAAMG,eAAe,OAAKH,IAAL,CAAU,cAAV,KAA6B,EAAlD;;AAEA,gBAAI;AACA1E,wBAAQC,GAAR,CAAY,gBAAZ;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBwE,OAArB,EAA8B,KAA9B,EAAqCE,MAArC;;AAEA;AACA,sBAAMjC,cAAc,MAAM,OAAKhE,KAAL,CAAW,cAAX,EAA2BmB,KAA3B,CAAiC;AACvDD,wBAAI6E;AADmD,iBAAjC,EAEvBtE,IAFuB,EAA1B;;AAIA,oBAAIS,MAAMC,OAAN,CAAc6B,WAAd,CAAJ,EAAgC;AAC5B,2BAAO,OAAK6B,IAAL,CAAU,SAAV,CAAP;AACH;;AAED;AACA,oBAAI7B,YAAYlE,MAAZ,KAAuB,WAA3B,EAAwC;AACpC,2BAAO,OAAK+F,IAAL,CAAU,cAAV,CAAP;AACH;;AAED,sBAAMO,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACA,oBAAIC,aAAa;AACbC,kCAAcL,WADD;AAEbM,gCAAY,IAAIJ,IAAJ;AAFC,iBAAjB;;AAKA,wBAAQL,MAAR;AACI,yBAAK,SAAL;AACI,4BAAIjC,YAAYlE,MAAZ,KAAuB,SAA3B,EAAsC;AAClC,mCAAO,OAAK+F,IAAL,CAAU,gBAAV,CAAP;AACH;AACDW,mCAAWG,UAAX,GAAwBT,SAAxB;;AAEA;AACA,4BAAIlC,YAAYc,WAAZ,KAA4B,aAAhC,EAA+C;AAC3C;AACAxD,oCAAQC,GAAR,CAAY,mBAAZ;;AAEA;AACA,kCAAMqF,YAAY,MAAM,OAAK5G,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B,EAAED,IAAI8C,YAAYhD,QAAlB,EAA1B,EAAwDS,IAAxD,EAAxB;AACA,gCAAIS,MAAMC,OAAN,CAAcyE,SAAd,CAAJ,EAA8B;AAC1B,uCAAO,OAAKf,IAAL,CAAU,SAAV,CAAP;AACH;;AAED;AACA,gCAAIe,UAAUC,UAAV,KAAyB,CAA7B,EAAgC;AAC5B,uCAAO,OAAKhB,IAAL,CAAU,YAAV,CAAP;AACH;;AAED;AACA,gCAAI;AACAvE,wCAAQC,GAAR,CAAY,mBAAZ;AACA,sCAAMuF,gBAAgB,OAAKC,OAAL,CAAa,QAAb,EAAuB,KAAvB,CAAtB;;AAEA;AACA,sCAAMC,WAAW,OAAOV,KAAKW,GAAL,EAAP,GAAoBC,KAAKC,MAAL,GAAc5E,QAAd,CAAuB,EAAvB,EAA2B6E,MAA3B,CAAkC,CAAlC,EAAqC,CAArC,EAAwCC,WAAxC,EAArC;;AAEA,sCAAMC,eAAe;AACjBC,kDAAcX,UAAU1G,QADP,EACoC;AACrDsH,mDAAeR,QAFE,EAEmC;AACpDS,+CAAWpB,SAASO,UAAUc,YAAV,GAAyB,GAAlC,CAHM,EAGmC;AACpDC,gDAAYtB,SAASrC,YAAYE,aAAZ,GAA4B,GAArC,CAJK,EAIsC;AACvD0D,iDAAc,QAAO5D,YAAYG,aAAc;AAL9B,iCAArB;;AAQA7C,wCAAQC,GAAR,CAAY,SAAZ,EAAuB+F,YAAvB;AACA,sCAAMO,eAAe,MAAMf,cAAcgB,YAAd,CAA2BR,YAA3B,CAA3B;;AAEA,oCAAIO,aAAarD,OAAjB,EAA0B;AACtBlD,4CAAQC,GAAR,CAAY,WAAZ,EAAyBsG,YAAzB;;AAEA;AACArB,+CAAW1G,MAAX,GAAoB,WAApB;AACA0G,+CAAWuB,aAAX,GAA2B3B,WAA3B;AACAI,+CAAWG,UAAX,GAAyB,GAAET,SAAU,iBAAgB2B,aAAaL,aAAc,GAAhF;;AAEA;AACA,0CAAM,OAAKxH,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5BD,4CAAI8C,YAAYhD;AADY,qCAA1B,EAEHgH,MAFG,CAEI;AACN7H,sDAAc,GADR,EACa;AACnB8H,qDAAa7B,WAFP;AAGNM,oDAAY,IAAIJ,IAAJ;AAHN,qCAFJ,CAAN;;AAQA;AACA,wCAAI;AACA,8CAAM4B,oBAAoB,OAAKnB,OAAL,CAAa,YAAb,EAA2B,QAA3B,CAA1B;AACA,8CAAMmB,kBAAkBC,sBAAlB,CAAyCnE,YAAYhD,QAArD,CAAN;AACAM,gDAAQC,GAAR,CAAY,YAAZ;AACH,qCAJD,CAIE,OAAO6G,eAAP,EAAwB;AACtB9G,gDAAQsE,KAAR,CAAc,aAAd,EAA6BwC,eAA7B;AACA;AACH;AAEJ,iCA3BD,MA2BO;AACH9G,4CAAQsE,KAAR,CAAc,WAAd,EAA2BiC,YAA3B;AACA,2CAAO,OAAKhC,IAAL,CAAW,UAASgC,aAAaQ,SAAb,IAA0B,MAAO,EAArD,CAAP;AACH;AAEJ,6BAlDD,CAkDE,OAAOC,WAAP,EAAoB;AAClBhH,wCAAQsE,KAAR,CAAc,gBAAd,EAAgC0C,WAAhC;AACA,uCAAO,OAAKzC,IAAL,CAAW,UAASyC,YAAYD,SAAZ,IAAyBC,YAAY7F,OAArC,IAAgD,MAAO,EAA3E,CAAP;AACH;;AAED;AACA,gCAAI;AACAnB,wCAAQC,GAAR,CAAa,kCAAiCyC,YAAY9D,QAAS,EAAnE;;AAEA;AACA,sCAAMqI,aAAa,MAAM,OAAKvI,KAAL,CAAW,aAAX,EAA0BmB,KAA1B,CAAgC,EAAEH,UAAUgD,YAAYhD,QAAxB,EAAhC,EAAoEJ,MAApE,EAAzB;;AAEA,oCAAI,CAACsB,MAAMC,OAAN,CAAcoG,UAAd,CAAL,EAAgC;AAC5B,0CAAMC,eAAe,IAAIpJ,YAAJ,QAArB;AACA,0CAAMqJ,SAAS,MAAMD,aAAaE,eAAb,CAA6B9B,SAA7B,EAAwC2B,UAAxC,CAArB;;AAEA,wCAAIE,OAAOjE,OAAX,EAAoB;AAChBlD,gDAAQC,GAAR,CAAa,qBAAoByC,YAAY9D,QAAS,EAAtD;AACH,qCAFD,MAEO;AACHoB,gDAAQsE,KAAR,CAAe,qBAAoB5B,YAAY9D,QAAS,SAAQuI,OAAOhG,OAAQ,EAA/E;AACH;AACJ,iCATD,MASO;AACHnB,4CAAQsE,KAAR,CAAe,8BAA6B5B,YAAY9D,QAAS,EAAjE;AACH;AACJ,6BAlBD,CAkBE,OAAO0F,KAAP,EAAc;AACZtE,wCAAQsE,KAAR,CAAc,sBAAd,EAAsCA,KAAtC;AACA;AACH;AACJ,yBA9FD,MA8FO,IAAI5B,YAAYc,WAAZ,KAA4B,eAAhC,EAAiD;AACpD;AACA0B,uCAAW1G,MAAX,GAAoB,aAApB;AACA;AACA0G,uCAAWmC,cAAX,GAA4BzC,aAAa,aAAzC;AACAM,uCAAWoC,cAAX,GAA4B,MAA5B;AACApC,uCAAWqC,YAAX,GAA0B,cAA1B;AACH;AACD;;AAEJ,yBAAK,QAAL;AACI,4BAAI7E,YAAYlE,MAAZ,KAAuB,SAA3B,EAAsC;AAClC,mCAAO,OAAK+F,IAAL,CAAU,gBAAV,CAAP;AACH;AACDW,mCAAW1G,MAAX,GAAoB,UAApB;AACA0G,mCAAWsC,aAAX,GAA2B3C,YAA3B;AACA;;AAEJ,yBAAK,gBAAL;AACI,4BAAInC,YAAYlE,MAAZ,KAAuB,UAA3B,EAAuC;AACnC,mCAAO,OAAK+F,IAAL,CAAU,kBAAV,CAAP;AACH;;AAED;AACAvE,gCAAQC,GAAR,CAAY,sBAAZ;;AAEA;AACA,8BAAMqF,YAAY,MAAM,OAAK5G,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B,EAAED,IAAI8C,YAAYhD,QAAlB,EAA1B,EAAwDS,IAAxD,EAAxB;AACA,4BAAIS,MAAMC,OAAN,CAAcyE,SAAd,CAAJ,EAA8B;AAC1B,mCAAO,OAAKf,IAAL,CAAU,SAAV,CAAP;AACH;;AAED;AACA,4BAAIe,UAAUC,UAAV,KAAyB,CAA7B,EAAgC;AAC5B,mCAAO,OAAKhB,IAAL,CAAU,YAAV,CAAP;AACH;;AAED;AACA,4BAAI;AACAvE,oCAAQC,GAAR,CAAY,yBAAZ;AACA,kCAAMuF,gBAAgB,OAAKC,OAAL,CAAa,QAAb,EAAuB,KAAvB,CAAtB;;AAEA;AACA,kCAAMC,WAAW,OAAOV,KAAKW,GAAL,EAAP,GAAoBC,KAAKC,MAAL,GAAc5E,QAAd,CAAuB,EAAvB,EAA2B6E,MAA3B,CAAkC,CAAlC,EAAqC,CAArC,EAAwCC,WAAxC,EAArC;;AAEA,kCAAMC,eAAe;AACjBC,8CAAcX,UAAU1G,QADP,EACoC;AACrDsH,+CAAeR,QAFE,EAEmC;AACpDS,2CAAWpB,SAASO,UAAUc,YAAV,GAAyB,GAAlC,CAHM,EAGmC;AACpDC,4CAAYtB,SAASrC,YAAYE,aAAZ,GAA4B,GAArC,CAJK,EAIsC;AACvD0D,6CAAc,QAAO5D,YAAYG,aAAc;AAL9B,6BAArB;;AAQA7C,oCAAQC,GAAR,CAAY,SAAZ,EAAuB+F,YAAvB;AACA,kCAAMO,eAAe,MAAMf,cAAcgB,YAAd,CAA2BR,YAA3B,CAA3B;;AAEA,gCAAIO,aAAarD,OAAjB,EAA0B;AACtBlD,wCAAQC,GAAR,CAAY,WAAZ,EAAyBsG,YAAzB;;AAEA;AACArB,2CAAW1G,MAAX,GAAoB,WAApB;AACA0G,2CAAWuB,aAAX,GAA2B3B,WAA3B;AACAI,2CAAWG,UAAX,GAAyB,GAAET,aAAa,WAAY,iBAAgB2B,aAAaL,aAAc,GAA/F;;AAEA;AACA,sCAAM,OAAKxH,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5BD,wCAAI8C,YAAYhD;AADY,iCAA1B,EAEHgH,MAFG,CAEI;AACN7H,kDAAc,GADR,EACa;AACnB8H,iDAAa7B,WAFP;AAGNM,gDAAY,IAAIJ,IAAJ;AAHN,iCAFJ,CAAN;;AAQA;AACA,oCAAI;AACA,0CAAM4B,oBAAoB,OAAKnB,OAAL,CAAa,YAAb,EAA2B,QAA3B,CAA1B;AACA,0CAAMmB,kBAAkBC,sBAAlB,CAAyCnE,YAAYhD,QAArD,CAAN;AACAM,4CAAQC,GAAR,CAAY,YAAZ;AACH,iCAJD,CAIE,OAAO6G,eAAP,EAAwB;AACtB9G,4CAAQsE,KAAR,CAAc,aAAd,EAA6BwC,eAA7B;AACA;AACH;AAEJ,6BA3BD,MA2BO;AACH9G,wCAAQsE,KAAR,CAAc,WAAd,EAA2BiC,YAA3B;AACA,uCAAO,OAAKhC,IAAL,CAAW,UAASgC,aAAaQ,SAAb,IAA0B,MAAO,EAArD,CAAP;AACH;AAEJ,yBAlDD,CAkDE,OAAOC,WAAP,EAAoB;AAClBhH,oCAAQsE,KAAR,CAAc,gBAAd,EAAgC0C,WAAhC;AACA,mCAAO,OAAKzC,IAAL,CAAW,UAASyC,YAAYD,SAAZ,IAAyBC,YAAY7F,OAArC,IAAgD,MAAO,EAA3E,CAAP;AACH;;AAED;AACA,4BAAI;AACAnB,oCAAQC,GAAR,CAAa,mCAAkCyC,YAAY9D,QAAS,EAApE;;AAEA;AACA,kCAAMqI,aAAa,MAAM,OAAKvI,KAAL,CAAW,aAAX,EAA0BmB,KAA1B,CAAgC,EAAEH,UAAUgD,YAAYhD,QAAxB,EAAhC,EAAoEJ,MAApE,EAAzB;;AAEA,gCAAI,CAACsB,MAAMC,OAAN,CAAcoG,UAAd,CAAL,EAAgC;AAC5B,sCAAMC,eAAe,IAAIpJ,YAAJ,QAArB;AACA,sCAAMqJ,SAAS,MAAMD,aAAaE,eAAb,CAA6B9B,SAA7B,EAAwC2B,UAAxC,CAArB;;AAEA,oCAAIE,OAAOjE,OAAX,EAAoB;AAChBlD,4CAAQC,GAAR,CAAa,qBAAoByC,YAAY9D,QAAS,EAAtD;AACH,iCAFD,MAEO;AACHoB,4CAAQsE,KAAR,CAAe,qBAAoB5B,YAAY9D,QAAS,SAAQuI,OAAOhG,OAAQ,EAA/E;AACH;AACJ,6BATD,MASO;AACHnB,wCAAQsE,KAAR,CAAe,8BAA6B5B,YAAY9D,QAAS,EAAjE;AACH;AACJ,yBAlBD,CAkBE,OAAO0F,KAAP,EAAc;AACZtE,oCAAQsE,KAAR,CAAc,sBAAd,EAAsCA,KAAtC;AACA;AACH;AACD;;AAEJ,yBAAK,UAAL;AACI;AACAY,mCAAW1G,MAAX,GAAoB,WAApB;AACA0G,mCAAWuB,aAAX,GAA2B3B,WAA3B;AACA;;AAEJ,yBAAK,YAAL;AACI,4BAAIpC,YAAYlE,MAAZ,KAAuB,SAA3B,EAAsC;AAClC,mCAAO,OAAK+F,IAAL,CAAU,mBAAV,CAAP;AACH;AACDW,mCAAW1G,MAAX,GAAoB,YAApB;AACA0G,mCAAWG,UAAX,GAAwBT,SAAxB;AACA;;AAEJ;AACI,+BAAO,OAAKL,IAAL,CAAU,SAAV,CAAP;AA3OR;;AA8OA;AACA,sBAAM,OAAK7F,KAAL,CAAW,cAAX,EAA2BmB,KAA3B,CAAiC;AACnCD,wBAAI6E;AAD+B,iBAAjC,EAEHiC,MAFG,CAEIxB,UAFJ,CAAN;;AAIAlF,wBAAQC,GAAR,CAAY,YAAZ;AACA,uBAAO,OAAKiD,OAAL,CAAa;AAChB/B,6BAAS,UADO;AAEhBsG,+BAAWvC,WAAW1G;AAFN,iBAAb,CAAP;AAKH,aAjRD,CAiRE,OAAO8F,KAAP,EAAc;AACZtE,wBAAQsE,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,OAAKC,IAAL,CAAU,eAAeD,MAAMnD,OAA/B,CAAP;AACH;AA1RsB;AA2R1B;;AAED;AACMuG,wBAAN,GAA6B;AAAA;;AAAA;AACzB,gBAAI;AACA;AACA,sBAAMC,cAAc/B,KAAKgC,KAAL,CAAW5C,KAAKW,GAAL,KAAa,IAAxB,IAAiC,KAAK,EAAL,GAAU,EAAV,GAAe,EAApE;;AAEA,sBAAMjH,QAAQ,OAAKA,KAAL,CAAW,OAAX,CAAd;;AAEA;AACA,sBAAMmJ,eAAe,EAArB;;AAEA;AACAA,6BAAaC,GAAb,GAAmB,MAAMpJ,MAAMmB,KAAN,CAAY;AACjCoC,8BAAU,CAAC,GAAD,EAAM0F,WAAN,CADuB;AAEjC7I,gCAAY,CAAC,GAAD,EAAM,CAAN,CAFqB;AAGjCuB,+BAAW;AAHsB,iBAAZ,EAItBgE,KAJsB,EAAzB;;AAMA;AACAwD,6BAAaE,KAAb,GAAqB,MAAMrJ,MAAMmB,KAAN,CAAY;AACnCoC,8BAAU,CAAC,GAAD,EAAM0F,WAAN,CADyB;AAEnC9I,kCAAc,CAAC,IAAD,EAAO,SAAP,CAFqB;AAGnCC,gCAAY,CAAC,GAAD,EAAM,CAAN,CAHuB;AAInCuB,+BAAW;AAJwB,iBAAZ,EAKxBgE,KALwB,EAA3B;;AAOA;AACAwD,6BAAaG,UAAb,GAA0B,MAAMtJ,MAAMmB,KAAN,CAAY;AACxCoC,8BAAU,CAAC,GAAD,EAAM0F,WAAN,CAD8B;AAExC9I,kCAAc,GAF0B;AAGxCC,gCAAY,CAAC,GAAD,EAAM,CAAN,CAH4B;AAIxCuB,+BAAW;AAJ6B,iBAAZ,EAK7BgE,KAL6B,EAAhC;;AAOA;AACAwD,6BAAaI,SAAb,GAAyB,MAAMvJ,MAAMmB,KAAN,CAAY;AACvCoC,8BAAU,CAAC,GAAD,EAAM0F,WAAN,CAD6B;AAEvC9I,kCAAc,GAFyB;AAGvCC,gCAAY,CAAC,GAAD,EAAM,CAAN,CAH2B;AAIvCuB,+BAAW;AAJ4B,iBAAZ,EAK5BgE,KAL4B,EAA/B;;AAOA;AACAwD,6BAAaK,QAAb,GAAwB,MAAMxJ,MAAMmB,KAAN,CAAY;AACtCoC,8BAAU,CAAC,GAAD,EAAM0F,WAAN,CAD4B;AAEtC9I,kCAAc,GAFwB;AAGtCC,gCAAY,CAAC,GAAD,EAAM,CAAN,CAH0B;AAItCuB,+BAAW;AAJ2B,iBAAZ,EAK3BgE,KAL2B,EAA9B;;AAOA;AACAwD,6BAAaM,MAAb,GAAsB,MAAMzJ,MAAMmB,KAAN,CAAY;AACpCoC,8BAAU,CAAC,GAAD,EAAM0F,WAAN,CAD0B;AAEpC9I,kCAAc,CAAC,IAAD,EAAO,aAAP,CAFsB;AAGpCC,gCAAY,CAAC,GAAD,EAAM,CAAN,CAHwB;AAIpCuB,+BAAW;AAJyB,iBAAZ,EAKzBgE,KALyB,EAA5B;;AAOA;AACAwD,6BAAaO,eAAb,GAA+B,MAAM1J,MAAMmB,KAAN,CAAY;AAC7CoC,8BAAU,CAAC,GAAD,EAAM0F,WAAN,CADmC;AAE7C9I,kCAAc,GAF+B;AAG7CC,gCAAY,CAAC,GAAD,EAAM,CAAN,CAHiC;AAI7CuB,+BAAW;AAJkC,iBAAZ,EAKlCgE,KALkC,EAArC;;AAOA,uBAAO,OAAKnB,OAAL,CAAa2E,YAAb,CAAP;AACH,aAjED,CAiEE,OAAOvD,KAAP,EAAc;AACZtE,wBAAQsE,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO,OAAKC,IAAL,CAAU,UAAV,CAAP;AACH;AArEwB;AAsE5B;;AAED;AACM8D,2BAAN,GAAgC;AAAA;;AAAA;AAC5B,gBAAI;AACA,sBAAMC,UAAU,OAAK5D,IAAL,CAAU,UAAV,CAAhB;AACA,sBAAME,YAAY,OAAKF,IAAL,CAAU,YAAV,KAA2B,EAA7C;AACA,sBAAM6D,YAAY,OAAK7D,IAAL,CAAU,oBAAV,KAAmC,IAArD;AACA,sBAAM8D,WAAW,OAAK9D,IAAL,CAAU,kBAAV,KAAiC,CAAlD;;AAEA,oBAAI,CAAC4D,OAAL,EAAc;AACV,2BAAO,OAAK/D,IAAL,CAAU,UAAV,CAAP;AACH;;AAED;AACA,sBAAMkE,cAAc,MAAM,OAAK/J,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B,EAAED,IAAI0I,OAAN,EAA1B,EAA2CnI,IAA3C,EAA1B;AACA,oBAAIS,MAAMC,OAAN,CAAc4H,WAAd,CAAJ,EAAgC;AAC5B,2BAAO,OAAKlE,IAAL,CAAU,OAAV,CAAP;AACH;;AAED;AACA,sBAAM,OAAK7F,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B,EAAED,IAAI0I,OAAN,EAA1B,EAA2C5B,MAA3C,CAAkD;AACpDrB,gCAAYT,SADwC;AAEpD8D,wCAAoBH,SAFgC;AAGpDI,sCAAkB5D,SAASyD,QAAT,CAHkC;AAIpDI,wCAAoBhD,KAAKgC,KAAL,CAAW5C,KAAKW,GAAL,KAAa,IAAxB,CAJgC;AAKpDkD,wCAAoB,OALgC,CAKxB;AALwB,iBAAlD,CAAN;;AAQA,uBAAO,OAAK3F,OAAL,CAAa,UAAb,CAAP;AACH,aA1BD,CA0BE,OAAOoB,KAAP,EAAc;AACZtE,wBAAQsE,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,OAAKC,IAAL,CAAU,UAAV,CAAP;AACH;AA9B2B;AA+B/B;;AAED;AACMuE,wBAAN,GAA6B;AAAA;;AAAA;AACzB,gBAAI;AACA,sBAAMR,UAAU,OAAKnK,GAAL,CAAS,UAAT,CAAhB;;AAEA,oBAAI,CAACmK,OAAL,EAAc;AACV,2BAAO,OAAK/D,IAAL,CAAU,UAAV,CAAP;AACH;;AAED,sBAAMe,YAAY,MAAM,OAAK5G,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B,EAAED,IAAI0I,OAAN,EAA1B,EACnBjJ,KADmB,CACb,kGADa,EAEnBc,IAFmB,EAAxB;;AAIA,oBAAIS,MAAMC,OAAN,CAAcyE,SAAd,CAAJ,EAA8B;AAC1B,2BAAO,OAAKf,IAAL,CAAU,OAAV,CAAP;AACH;;AAED;AACA,oBAAIe,UAAUsD,kBAAd,EAAkC;AAC9BtD,8BAAUsD,kBAAV,GAA+BhL,OAAOsE,IAAP,CAAYoD,UAAUsD,kBAAtB,EAA0CzG,MAA1C,CAAiD,qBAAjD,CAA/B;AACH;;AAED,uBAAO,OAAKe,OAAL,CAAaoC,SAAb,CAAP;AACH,aArBD,CAqBE,OAAOhB,KAAP,EAAc;AACZtE,wBAAQsE,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,OAAKC,IAAL,CAAU,UAAV,CAAP;AACH;AAzBwB;AA0B5B;;AAEKwE,uBAAN,GAA4B;AAAA;;AAAA;AACxB,gBAAIvK,SAAS,MAAM,OAAKE,KAAL,CAAW,UAAX,EAAuBmB,KAAvB,CAA6B;AAC5CD,oBAAI;AADwC,aAA7B,EAEhBP,KAFgB,CAEV,cAFU,EAEMc,IAFN,EAAnB;AAGA,gBAAI6I,OAAOxK,OAAOyK,YAAlB;AACA,mBAAO,OAAK/F,OAAL,CAAa8F,IAAb,CAAP;AALwB;AAM3B;AACKE,oBAAN,GAAyB;AAAA;;AAAA;AACrB,kBAAMhL,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,OAAKD,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,kBAAMK,SAAS,OAAKL,GAAL,CAAS,QAAT,KAAsB,EAArC;AACA,kBAAMO,QAAQ,OAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMD,OAAO,MAAMC,MAAMmB,KAAN,CAAY;AAC3BhB,8BAAcL;AADa,aAAZ,EAEhBsB,KAFgB,CAEV,CAAC,SAAD,CAFU,EAEG5B,IAFH,CAEQA,IAFR,EAEcE,IAFd,EAEoB2B,WAFpB,EAAnB;AAGA,iBAAK,MAAMN,IAAX,IAAmBhB,KAAKA,IAAxB,EAA8B;AAC1BgB,qBAAKW,SAAL,GAAiB,MAAM,OAAK1B,KAAL,CAAW,aAAX,EAA0BW,KAA1B,CAAgC,0EAAhC,EAA4GQ,KAA5G,CAAkH;AACrIH,8BAAUD,KAAKG;AADsH,iBAAlH,EAEpBN,MAFoB,EAAvB;AAGAG,qBAAKa,UAAL,GAAkB,CAAlB;AACAb,qBAAKW,SAAL,CAAeG,OAAf,CAAuB,aAAK;AACxBd,yBAAKa,UAAL,IAAmBE,EAAEC,MAArB;AACH,iBAFD;AAGA,oBAAIe,gBAAgB,MAAM,OAAK9C,KAAL,CAAW,QAAX,EAAqBmB,KAArB,CAA2B;AACjDD,wBAAIH,KAAKgC;AADwC,iBAA3B,EAEvBC,QAFuB,CAEd,MAFc,EAEN,IAFM,CAA1B;AAGA,oBAAIC,YAAY,MAAM,OAAKjD,KAAL,CAAW,QAAX,EAAqBmB,KAArB,CAA2B;AAC7CD,wBAAIH,KAAKmC;AADoC,iBAA3B,EAEnBF,QAFmB,CAEV,MAFU,EAEF,IAFE,CAAtB;AAGA,oBAAIG,gBAAgB,MAAM,OAAKnD,KAAL,CAAW,QAAX,EAAqBmB,KAArB,CAA2B;AACjDD,wBAAIH,KAAKqC;AADwC,iBAA3B,EAEvBJ,QAFuB,CAEd,MAFc,EAEN,IAFM,CAA1B;AAGAjC,qBAAK0J,OAAL,GAAe3H,gBAAgBG,SAAhB,GAA4BE,aAA5B,GAA4CpC,KAAK0J,OAAhE;AACA1J,qBAAKuC,UAAL,GAAkBjB,OAAOC,IAAP,CAAYvB,KAAKuC,UAAjB,EAA6B,QAA7B,EAAuCf,QAAvC,EAAlB;AACAxB,qBAAKwC,QAAL,GAAgBrE,OAAOsE,IAAP,CAAYzC,KAAKwC,QAAjB,EAA2BE,MAA3B,CAAkC,qBAAlC,CAAhB;AACA1C,qBAAK4C,iBAAL,GAAyB,MAAM,OAAK3D,KAAL,CAAW,OAAX,EAAoB4D,kBAApB,CAAuC7C,KAAKG,EAA5C,CAA/B;AACAH,qBAAK2J,WAAL,GAAmB,MAAM,OAAK1K,KAAL,CAAW,OAAX,EAAoB2K,eAApB,CAAoC5J,KAAKG,EAAzC,CAAzB;AACH;AACD,mBAAO,OAAKsD,OAAL,CAAazE,IAAb,CAAP;AA/BqB;AAgCxB;AACK6K,uBAAN,GAA4B;AAAA;;AAAA;AACxB;AACA,gBAAI1J,KAAK,OAAK8E,IAAL,CAAU,IAAV,CAAT;AACA,gBAAIhF,WAAW,OAAKgF,IAAL,CAAU,UAAV,CAAf;AACA,gBAAIjE,SAAS,OAAKiE,IAAL,CAAU,QAAV,CAAb;AACA,gBAAI6E,QAAQ,OAAK7E,IAAL,CAAU,cAAV,CAAZ;AACA,gBAAI8E,aAAa,OAAK9E,IAAL,CAAU,YAAV,CAAjB;AACA,gBAAI+E,cAAcC,OAAOjJ,MAAP,IAAiBiJ,OAAOH,KAAP,CAAnC;AACAvJ,oBAAQC,GAAR,CAAYP,QAAZ;AACAM,oBAAQC,GAAR,CAAYwJ,WAAZ;AACA,gBAAID,cAAc,CAAlB,EAAqB;AACjB,sBAAM,OAAK9K,KAAL,CAAW,aAAX,EAA0BmB,KAA1B,CAAgC;AAClCD,wBAAIA;AAD8B,iBAAhC,EAEH+J,SAFG,CAEO,QAFP,EAEiBlJ,MAFjB,CAAN;AAGA,sBAAM,OAAK/B,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5BD,wBAAIF;AADwB,iBAA1B,EAEHiK,SAFG,CAEO;AACTvD,kCAAcqD,WADL;AAETG,iCAAaH,WAFJ;AAGTI,iCAAaJ;AAHJ,iBAFP,CAAN;AAOA,oBAAI7K,WAAW,OAAKF,KAAL,CAAW,OAAX,EAAoBoL,mBAApB,EAAf;AACA,sBAAM,OAAKpL,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5BD,wBAAIF;AADwB,iBAA1B,EAEHgH,MAFG,CAEI;AACN9H,8BAAUA;AADJ,iBAFJ,CAAN;AAKA,uBAAO,OAAKsE,OAAL,CAAatE,QAAb,CAAP;AACH,aAlBD,MAkBO,IAAI4K,cAAc,CAAlB,EAAqB;AACxB,sBAAM,OAAK9K,KAAL,CAAW,aAAX,EAA0BmB,KAA1B,CAAgC;AAClCD,wBAAIA;AAD8B,iBAAhC,EAEHmK,SAFG,CAEO,QAFP,EAEiBtJ,MAFjB,CAAN;AAGA,sBAAM,OAAK/B,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5BD,wBAAIF;AADwB,iBAA1B,EAEHqK,SAFG,CAEO;AACT3D,kCAAcqD,WADL;AAETG,iCAAaH,WAFJ;AAGTI,iCAAaJ;AAHJ,iBAFP,CAAN;AAOA,oBAAI7K,WAAW,OAAKF,KAAL,CAAW,OAAX,EAAoBoL,mBAApB,EAAf;AACA,sBAAM,OAAKpL,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5BD,wBAAIF;AADwB,iBAA1B,EAEHgH,MAFG,CAEI;AACN9H,8BAAUA;AADJ,iBAFJ,CAAN;AAKA,uBAAO,OAAKsE,OAAL,CAAatE,QAAb,CAAP;AACH;AA9CuB;AA+C3B;AACKoL,yBAAN,GAA8B;AAAA;;AAAA;AAC1BhK,oBAAQC,GAAR,CAAY,QAAKyE,IAAL,CAAU,IAAV,CAAZ;AACA,gBAAI9E,KAAK,QAAK8E,IAAL,CAAU,IAAV,CAAT;AACA,gBAAIhF,WAAW,QAAKgF,IAAL,CAAU,UAAV,CAAf;AACA,gBAAIjE,SAAS,QAAKiE,IAAL,CAAU,QAAV,CAAb;AACA,gBAAI6E,QAAQ,QAAK7E,IAAL,CAAU,cAAV,CAAZ;AACA,gBAAI8E,aAAa,QAAK9E,IAAL,CAAU,YAAV,CAAjB;AACA,gBAAI+E,cAAcC,OAAOjJ,MAAP,IAAiBiJ,OAAOH,KAAP,CAAnC;AACAvJ,oBAAQC,GAAR,CAAYP,QAAZ;AACAM,oBAAQC,GAAR,CAAYwJ,WAAZ;AACA,kBAAM,QAAK/K,KAAL,CAAW,aAAX,EAA0BmB,KAA1B,CAAgC;AAClCD,oBAAIA;AAD8B,aAAhC,EAEH8G,MAFG,CAEI;AACNrG,2BAAW;AADL,aAFJ,CAAN;AAKA,kBAAM,QAAK3B,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5BD,oBAAIF;AADwB,aAA1B,EAEHiK,SAFG,CAEO;AACTvD,8BAAcqD,WADL;AAETG,6BAAaH,WAFJ;AAGTI,6BAAaJ;AAHJ,aAFP,CAAN;AAOA,gBAAI7K,WAAW,QAAKF,KAAL,CAAW,OAAX,EAAoBoL,mBAApB,EAAf;AACA,kBAAM,QAAKpL,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5BD,oBAAIF;AADwB,aAA1B,EAEHgH,MAFG,CAEI;AACN9H,0BAAUA;AADJ,aAFJ,CAAN;AAKA,mBAAO,QAAKsE,OAAL,CAAatE,QAAb,CAAP;AA5B0B;AA6B7B;AACKqL,uBAAN,GAA4B;AAAA;;AAAA;AACxB,kBAAMrK,KAAK,QAAK8E,IAAL,CAAU,IAAV,CAAX;AACA,kBAAMwF,OAAO,QAAKxF,IAAL,CAAU,MAAV,CAAb;AACA,kBAAMhG,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,gBAAIsK,OAAO;AACP3D,4BAAY6E;AADL,aAAX;AAGA,gBAAIzL,OAAO,MAAMC,MAAMmB,KAAN,CAAY;AACzBD,oBAAIA;AADqB,aAAZ,EAEd8G,MAFc,CAEPsC,IAFO,CAAjB;AAGA,mBAAO,QAAK9F,OAAL,CAAazE,IAAb,CAAP;AAVwB;AAW3B;AACK0L,uBAAN,GAA4B;AAAA;;AAAA;AACxB,kBAAMvK,KAAK,QAAK8E,IAAL,CAAU,IAAV,CAAX;AACA,kBAAM0F,aAAa,QAAK1F,IAAL,CAAU,YAAV,CAAnB;AACA,kBAAMhG,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,gBAAIsK,OAAO;AACPoB,4BAAYA;AADL,aAAX;AAGA,gBAAI3L,OAAO,MAAMC,MAAMmB,KAAN,CAAY;AACzBD,oBAAIA;AADqB,aAAZ,EAEd8G,MAFc,CAEPsC,IAFO,CAAjB;AAGA,mBAAO,QAAK9F,OAAL,CAAazE,IAAb,CAAP;AAVwB;AAW3B;AACK4L,8BAAN,GAAmC;AAAA;;AAAA;AAC/B,kBAAMzK,KAAK,QAAK8E,IAAL,CAAU,IAAV,CAAX;AACA,kBAAM4F,gBAAgB,QAAK5F,IAAL,CAAU,eAAV,CAAtB;AACA,kBAAMhG,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,gBAAIsK,OAAO;AACPsB,+BAAeA;AADR,aAAX;AAGA,gBAAI7L,OAAO,MAAMC,MAAMmB,KAAN,CAAY;AACzBD,oBAAIA;AADqB,aAAZ,EAEd8G,MAFc,CAEPsC,IAFO,CAAjB;AAGA,mBAAO,QAAK9F,OAAL,CAAazE,IAAb,CAAP;AAV+B;AAWlC;AACK8L,wBAAN,GAA6B;AAAA;;AAAA;AACzB,kBAAM3K,KAAK,QAAK8E,IAAL,CAAU,IAAV,CAAX;AACA,kBAAM8F,SAAS,QAAK9F,IAAL,CAAU,QAAV,CAAf;AACA,kBAAMhG,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,gBAAIsK,OAAO;AACPwB,wBAAQA;AADD,aAAX;AAGA,gBAAI/L,OAAO,MAAMC,MAAMmB,KAAN,CAAY;AACzBD,oBAAIA;AADqB,aAAZ,EAEd8G,MAFc,CAEPsC,IAFO,CAAjB;AAGA,mBAAO,QAAK9F,OAAL,CAAazE,IAAb,CAAP;AAVyB;AAW5B;AACKgM,gBAAN,GAAqB;AAAA;;AAAA;AACjB,kBAAM7K,KAAK,QAAKzB,GAAL,CAAS,SAAT,CAAX;AACA,kBAAMO,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,gBAAID,OAAO,MAAMC,MAAMmB,KAAN,CAAY;AACzBD,oBAAIA;AADqB,aAAZ,EAEdO,IAFc,EAAjB;AAGA1B,iBAAK2B,SAAL,GAAiB,MAAM,QAAK1B,KAAL,CAAW,aAAX,EAA0BW,KAA1B,CAAgC,2GAAhC,EAA6IQ,KAA7I,CAAmJ;AACtKH,0BAAUjB,KAAKmB,EADuJ;AAEtKS,2BAAW;AAF2J,aAAnJ,EAGpBf,MAHoB,EAAvB;AAIAb,iBAAK6B,UAAL,GAAkB,CAAlB;AACA7B,iBAAK2B,SAAL,CAAeG,OAAf,CAAuB,aAAK;AACxB9B,qBAAK6B,UAAL,IAAmBE,EAAEC,MAArB;AACH,aAFD;AAGA,iBAAK,MAAMhB,IAAX,IAAmBhB,KAAK2B,SAAxB,EAAmC;AAC/B,oBAAI4I,OAAO,MAAM,QAAKtK,KAAL,CAAW,SAAX,EAAsBmB,KAAtB,CAA4B;AACzCD,wBAAIH,KAAKiL;AADgC,iBAA5B,EAEdrL,KAFc,CAER,UAFQ,EAEIc,IAFJ,EAAjB;AAGAV,qBAAKkL,QAAL,GAAgB3B,KAAK2B,QAArB;AACH;AACD3K,oBAAQC,GAAR,CAAYxB,KAAK2B,SAAjB;AACA,gBAAImB,WAAW,MAAM,QAAK7C,KAAL,CAAW,MAAX,EAAmBmB,KAAnB,CAAyB;AAC1CD,oBAAInB,KAAKkC;AADiC,aAAzB,EAElBR,IAFkB,EAArB;AAGA,gBAAIyK,YAAY7J,OAAOC,IAAP,CAAYO,SAAST,QAArB,EAA+B,QAA/B,EAAyCG,QAAzC,EAAhB;AACAxC,iBAAKoM,SAAL,GAAiBD,SAAjB;AACAnM,iBAAK6C,MAAL,GAAcC,SAASD,MAAvB;AACA,gBAAIE,gBAAgB,MAAM,QAAK9C,KAAL,CAAW,QAAX,EAAqBmB,KAArB,CAA2B;AACjDD,oBAAInB,KAAKgD;AADwC,aAA3B,EAEvBC,QAFuB,CAEd,MAFc,EAEN,IAFM,CAA1B;AAGA,gBAAIC,YAAY,MAAM,QAAKjD,KAAL,CAAW,QAAX,EAAqBmB,KAArB,CAA2B;AAC7CD,oBAAInB,KAAKmD;AADoC,aAA3B,EAEnBF,QAFmB,CAEV,MAFU,EAEF,IAFE,CAAtB;AAGA,gBAAIG,gBAAgB,MAAM,QAAKnD,KAAL,CAAW,QAAX,EAAqBmB,KAArB,CAA2B;AACjDD,oBAAInB,KAAKqD;AADwC,aAA3B,EAEvBJ,QAFuB,CAEd,MAFc,EAEN,IAFM,CAA1B;AAGAjD,iBAAKsD,WAAL,GAAmBP,gBAAgBG,SAAhB,GAA4BE,aAA/C;AACApD,iBAAKuD,UAAL,GAAkBjB,OAAOC,IAAP,CAAYvC,KAAKuD,UAAjB,EAA6B,QAA7B,EAAuCf,QAAvC,EAAlB;AACAxC,iBAAK4D,iBAAL,GAAyB,MAAM,QAAK3D,KAAL,CAAW,OAAX,EAAoB4D,kBAApB,CAAuC7D,KAAKmB,EAA5C,CAA/B;AACAnB,iBAAKwD,QAAL,GAAgBrE,OAAOsE,IAAP,CAAYzD,KAAKwD,QAAjB,EAA2BE,MAA3B,CAAkC,qBAAlC,CAAhB;AACA1D,iBAAKqM,UAAL,GAAkBrM,KAAKsD,WAAL,GAAmBtD,KAAK0K,OAA1C;AACA,gBAAI1K,KAAK2D,QAAL,IAAiB,CAArB,EAAwB;AACpB3D,qBAAK2D,QAAL,GAAgBxE,OAAOsE,IAAP,CAAYzD,KAAK2D,QAAjB,EAA2BD,MAA3B,CAAkC,qBAAlC,CAAhB;AACH;AACD,gBAAI1D,KAAKsM,aAAL,IAAsB,CAA1B,EAA6B;AACzBtM,qBAAKsM,aAAL,GAAqBnN,OAAOsE,IAAP,CAAYzD,KAAKsM,aAAjB,EAAgC5I,MAAhC,CAAuC,qBAAvC,CAArB;AACH;AACD,gBAAI1D,KAAKuM,YAAL,IAAqB,CAAzB,EAA4B;AACxBvM,qBAAKuM,YAAL,GAAoBpN,OAAOsE,IAAP,CAAYzD,KAAKuM,YAAjB,EAA+B7I,MAA/B,CAAsC,qBAAtC,CAApB;AACH;AACD,gBAAI1D,KAAKwM,aAAL,IAAsB,CAA1B,EAA6B;AACzBxM,qBAAKwM,aAAL,GAAqBrN,OAAOsE,IAAP,CAAYzD,KAAKwM,aAAjB,EAAgC9I,MAAhC,CAAuC,qBAAvC,CAArB;AACH;AACD,gBAAI+I,MAAM,MAAM,QAAKxM,KAAL,CAAW,UAAX,EAAuBmB,KAAvB,CAA6B;AACzCD,oBAAI;AADqC,aAA7B,EAEbO,IAFa,EAAhB;AAGA,gBAAIgL,aAAa,EAAjB;AACA,gBAAIC,cAAc,EAAlB;AACAA,0BAAc;AACVhK,sBAAM3C,KAAKH,SADD;AAEV+C,wBAAQ5C,KAAK4C,MAFH;AAGVI,0BAAUD,aAHA;AAIV6J,6BAAa5M,KAAKgD,QAJR;AAKVG,sBAAMD,SALI;AAMV2J,yBAAS7M,KAAKmD,IANJ;AAOVE,0BAAUD,aAPA;AAQV0J,6BAAa9M,KAAKqD,QARR;AASVqH,yBAAS1K,KAAK0K;AATJ,aAAd;AAWAgC,yBAAa;AACT/J,sBAAM8J,IAAIM,IADD;AAETnK,wBAAQ6J,IAAIO,GAFH;AAGThK,0BAAUyJ,IAAIQ,YAHL;AAIT9J,sBAAMsJ,IAAIS,QAJD;AAKT7J,0BAAUoJ,IAAIU,WALL;AAMTP,6BAAaH,IAAIG,WANR;AAOTC,yBAASJ,IAAII,OAPJ;AAQTC,6BAAaL,IAAIK,WARR;AASTpC,yBAAS+B,IAAIW;AATJ,aAAb;AAWA,mBAAO,QAAK3I,OAAL,CAAa;AAChBoC,2BAAW7G,IADK;AAEhBqN,0BAAUV,WAFM;AAGhBW,wBAAQZ;AAHQ,aAAb,CAAP;AAhFiB;AAqFpB;AACKa,sBAAN,GAA2B;AAAA;;AAAA;AAAE;AACzB,kBAAMtN,QAAQ,QAAKA,KAAL,CAAW,QAAX,CAAd;AACA,kBAAMuN,QAAQ,MAAMvN,MAAMmB,KAAN,CAAY;AAC5BqM,sBAAM;AADsB,aAAZ,EAEjB5M,MAFiB,EAApB;AAGA,kBAAM6M,QAAQ,MAAMzN,MAAMmB,KAAN,CAAY;AAC5BqM,sBAAM;AADsB,aAAZ,EAEjB5M,MAFiB,EAApB;AAGA,kBAAM8M,QAAQ,MAAM1N,MAAMmB,KAAN,CAAY;AAC5BqM,sBAAM;AADsB,aAAZ,EAEjB5M,MAFiB,EAApB;AAGA,gBAAI+M,UAAU,EAAd;AACA,iBAAK,MAAM5M,IAAX,IAAmBwM,KAAnB,EAA0B;AACtB,oBAAIK,WAAW,EAAf;AACA,qBAAK,MAAMC,KAAX,IAAoBJ,KAApB,EAA2B;AACvB,wBAAIK,gBAAgB,EAApB;AACA,yBAAK,MAAMC,KAAX,IAAoBL,KAApB,EAA2B;AACvB,4BAAIK,MAAMC,SAAN,IAAmBH,MAAM3M,EAA7B,EAAiC;AAC7B4M,0CAAcG,IAAd,CAAmB;AACfC,uCAAOH,MAAM7M,EADE;AAEfiN,uCAAOJ,MAAMrL;AAFE,6BAAnB;AAIH;AACJ;AACD,wBAAImL,MAAMG,SAAN,IAAmBjN,KAAKG,EAA5B,EAAgC;AAC5B0M,iCAASK,IAAT,CAAc;AACVC,mCAAOL,MAAM3M,EADH;AAEViN,mCAAON,MAAMnL,IAFH;AAGVkL,sCAAUE;AAHA,yBAAd;AAKH;AACJ;AACDH,wBAAQM,IAAR,CAAa;AACTC,2BAAOnN,KAAKG,EADH;AAETiN,2BAAOpN,KAAK2B,IAFH;AAGTkL,8BAAUA;AAHD,iBAAb;AAKH;AACD,mBAAO,QAAKpJ,OAAL,CAAamJ,OAAb,CAAP;AAtCuB;AAuC1B;AACKS,mBAAN,GAAwB;AAAA;;AAAA;AACpB,kBAAMlN,KAAK,QAAKzB,GAAL,CAAS,SAAT,CAAX;AACA,kBAAMO,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMD,OAAO,MAAMC,MAAMmB,KAAN,CAAY;AAC3BD,oBAAIA;AADuB,aAAZ,EAEhB8G,MAFgB,CAET;AACN7H,8BAAc;AADR,aAFS,CAAnB;AAHoB;AAQvB;AACKkO,sBAAN,GAA2B;AAAA;;AAAA;AACvB,kBAAMnN,KAAK,QAAKzB,GAAL,CAAS,SAAT,CAAX;AACA,gBAAI2G,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAAlB;AACA,kBAAMvG,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMD,OAAO,MAAMC,MAAMmB,KAAN,CAAY;AAC3BD,oBAAIA;AADuB,aAAZ,EAEhB8G,MAFgB,CAET;AACN7H,8BAAc,GADR;AAENkM,+BAAejG;AAFT,aAFS,CAAnB;AAJuB;AAU1B;AACKkI,oBAAN,GAAyB;AAAA;;AAAA;AACrB,kBAAMpN,KAAK,QAAKzB,GAAL,CAAS,SAAT,CAAX;AACA,kBAAM8O,aAAa,QAAK9O,GAAL,CAAS,YAAT,CAAnB;AACA,kBAAM+O,eAAe,QAAK/O,GAAL,CAAS,cAAT,CAArB;AACA,kBAAMgP,cAAc,QAAKhP,GAAL,CAAS,aAAT,CAApB;AACA,kBAAMO,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMD,OAAO,MAAMC,MAAMmB,KAAN,CAAY;AAC3BD,oBAAIA;AADuB,aAAZ,EAEhBO,IAFgB,EAAnB;AAGA,gBAAIkM,UAAU;AACVjG,8BAAc+G,WADJ;AAEVC,+BAAeF,YAFL;AAGVrD,6BAAaoD,UAHH;AAIVrO,0BAAUF,MAAMoL,mBAAN;AAJA,aAAd;AAMA,kBAAMpL,MAAMmB,KAAN,CAAY;AACdD,oBAAIA;AADU,aAAZ,EAEH8G,MAFG,CAEI2F,OAFJ,CAAN;AAfqB;AAkBxB;AACKgB,yBAAN,GAA8B;AAAA;;AAAA;AAC1B,kBAAM/E,UAAU,QAAK5D,IAAL,CAAU,SAAV,CAAhB;AACA,kBAAM4I,oBAAoB,MAAM,QAAK5O,KAAL,CAAW,eAAX,EAA4B6O,0BAA5B,CAAuDjF,OAAvD,CAAhC;AACA,mBAAO,QAAKpF,OAAL,CAAaoK,iBAAb,CAAP;AAH0B;AAI7B;AACKE,sBAAN,GAA2B;AAAA;;AAAA;AACvB,kBAAMF,oBAAoB,MAAM,QAAK5O,KAAL,CAAW,eAAX,EAA4B+O,YAA5B,EAAhC;AACA,mBAAO,QAAKvK,OAAL,CAAaoK,iBAAb,CAAP;AAFuB;AAG1B;AACKI,wBAAN,GAA6B;AAAA;;AAAA;AACzB,kBAAMpF,UAAU,QAAK5D,IAAL,CAAU,SAAV,CAAhB;AACA,kBAAMqH,SAAS,QAAKrH,IAAL,CAAU,QAAV,CAAf;AACA,kBAAMoH,WAAW,QAAKpH,IAAL,CAAU,UAAV,CAAjB;AACA1E,oBAAQC,GAAR,CAAYqI,OAAZ;AACAtI,oBAAQC,GAAR,CAAY8L,MAAZ;AACA/L,oBAAQC,GAAR,CAAY6L,QAAZ;AACA,gBAAI6B,gBAAgB5B,OAAO4B,aAA3B;AACA,gBAAIC,iBAAiB9B,SAAS8B,cAA9B;AACA,gBAAIzC,aAAa;AACbK,sBAAMO,OAAO3K,IADA;AAEbqK,qBAAKM,OAAO1K,MAFC;AAGbqK,8BAAc,MAAM,QAAKhN,KAAL,CAAW,QAAX,EAAqBmB,KAArB,CAA2B;AAC3CD,wBAAI+N,cAAc,CAAd;AADuC,iBAA3B,EAEjBjM,QAFiB,CAER,MAFQ,EAEA,IAFA,CAHP;AAMbiK,0BAAU,MAAM,QAAKjN,KAAL,CAAW,QAAX,EAAqBmB,KAArB,CAA2B;AACvCD,wBAAI+N,cAAc,CAAd;AADmC,iBAA3B,EAEbjM,QAFa,CAEJ,MAFI,EAEI,IAFJ,CANH;AASbkK,6BAAa,MAAM,QAAKlN,KAAL,CAAW,QAAX,EAAqBmB,KAArB,CAA2B;AAC1CD,wBAAI+N,cAAc,CAAd;AADsC,iBAA3B,EAEhBjM,QAFgB,CAEP,MAFO,EAEC,IAFD,CATN;AAYbmK,yBAASE,OAAO5C;AAZH,aAAjB;AAcA,gBAAI0E,eAAe;AACfrC,sBAAMM,SAAS1K,IADA;AAEfqK,qBAAKK,SAASzK,MAFC;AAGfqK,8BAAc,MAAM,QAAKhN,KAAL,CAAW,QAAX,EAAqBmB,KAArB,CAA2B;AAC3CD,wBAAIgO,eAAe,CAAf;AADuC,iBAA3B,EAEjBlM,QAFiB,CAER,MAFQ,EAEA,IAFA,CAHL;AAMfiK,0BAAU,MAAM,QAAKjN,KAAL,CAAW,QAAX,EAAqBmB,KAArB,CAA2B;AACvCD,wBAAIgO,eAAe,CAAf;AADmC,iBAA3B,EAEblM,QAFa,CAEJ,MAFI,EAEI,IAFJ,CAND;AASfkK,6BAAa,MAAM,QAAKlN,KAAL,CAAW,QAAX,EAAqBmB,KAArB,CAA2B;AAC1CD,wBAAIgO,eAAe,CAAf;AADsC,iBAA3B,EAEhBlM,QAFgB,CAEP,MAFO,EAEC,IAFD,CATJ;AAYfmK,yBAASC,SAAS3C;AAZH,aAAnB;AAcA;AACA,kBAAM2E,cAAc,QAAKpJ,IAAL,CAAU,aAAV,CAApB;AACA,kBAAM4I,oBAAoB,MAAM,QAAK5O,KAAL,CAAW,eAAX,EAA4BqP,cAA5B,CAA2CzF,OAA3C,EAAoD6C,UAApD,EAAgE0C,YAAhE,EAA8EC,WAA9E,CAAhC;AACA9N,oBAAQC,GAAR,CAAY,uCAAZ;AACAD,oBAAQC,GAAR,CAAYqN,iBAAZ;AACA,gBAAIA,kBAAkBU,UAAlB,IAAgC,GAApC,EAAyC;AACrC;AACA,wBAAKC,eAAL,CAAqBX,iBAArB,EAAwChF,OAAxC;AACH;AACD,mBAAO,QAAKpF,OAAL,CAAa;AAChBoK,mCAAmBA,iBADH;AAEhBvB,wBAAQZ,UAFQ;AAGhBW,0BAAU+B;AAHM,aAAb,CAAP;AA9CyB;AAmD5B;AACKK,wBAAN,GAA6B;AAAA;;AAAA;AACzB,kBAAMC,OAAO,IAAInJ,IAAJ,EAAb;AACA,gBAAIsD,UAAU,QAAKnK,GAAL,CAAS,SAAT,CAAd;AACA,gBAAIS,WAAWuP,KAAKC,WAAL,KAAqBvQ,EAAEwQ,QAAF,CAAWF,KAAKG,QAAL,EAAX,EAA4B,CAA5B,EAA+B,GAA/B,CAArB,GAA2DzQ,EAAEwQ,QAAF,CAAWF,KAAKI,MAAL,EAAX,EAA0B,CAA1B,EAA6B,GAA7B,CAA3D,GAA+F1Q,EAAEwQ,QAAF,CAAWF,KAAKK,QAAL,EAAX,EAA4B,CAA5B,EAA+B,GAA/B,CAA/F,GAAqI3Q,EAAEwQ,QAAF,CAAWF,KAAKM,UAAL,EAAX,EAA8B,CAA9B,EAAiC,GAAjC,CAArI,GAA6K5Q,EAAEwQ,QAAF,CAAWF,KAAKO,UAAL,EAAX,EAA8B,CAA9B,EAAiC,GAAjC,CAA7K,GAAqN7Q,EAAEgI,MAAF,CAAS,MAAT,EAAiB,MAAjB,CAApO;AACA,gBAAImD,OAAO,MAAM,QAAKtK,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AACvCD,oBAAI0I;AADmC,aAA1B,EAEd5B,MAFc,CAEP;AACN9H,0BAAUA;AADJ,aAFO,CAAjB;AAKA,mBAAO,QAAKsE,OAAL,CAAa8F,IAAb,CAAP;AATyB;AAU5B;AACK2F,4BAAN,GAAiC;AAAA;;AAAA;AAC7B,gBAAIrG,UAAU,QAAKnK,GAAL,CAAS,SAAT,CAAd;AACA,gBAAIoE,UAAU,MAAM,QAAK7D,KAAL,CAAW,eAAX,EAA4BmB,KAA5B,CAAkC;AAClDH,0BAAU4I;AADwC,aAAlC,EAEjBnI,IAFiB,EAApB;AAGA,gBAAI6I,OAAO,EAAX;AACA,gBAAIzG,QAAQqM,YAAR,GAAuB,CAA3B,EAA8B;AAC1B5F,uBAAO,MAAM,QAAKtK,KAAL,CAAW,SAAX,EAAsBmB,KAAtB,CAA4B;AACrCgP,0BAAM;AAD+B,iBAA5B,EAEV1O,IAFU,EAAb;AAGH,aAJD,MAIO;AACH6I,uBAAO,MAAM,QAAKtK,KAAL,CAAW,SAAX,EAAsBmB,KAAtB,CAA4B;AACrCgP,0BAAM;AAD+B,iBAA5B,EAEV1O,IAFU,EAAb;AAGH;AACDoC,oBAAQuM,SAAR,GAAoB9F,KAAK8F,SAAzB;AACAvM,oBAAQwM,SAAR,GAAoBnR,OAAOsE,IAAP,CAAYK,QAAQN,QAApB,EAA8BE,MAA9B,CAAqC,YAArC,CAApB;AACA,mBAAO,QAAKe,OAAL,CAAaX,OAAb,CAAP;AAjB6B;AAkBhC;AACK0L,mBAAN,CAAsBe,GAAtB,EAA2B1G,OAA3B,EAAoC;AAAA;;AAAA;AAChC,gBAAIxD,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAAlB;AACA,gBAAI+D,OAAO,MAAM,QAAKtK,KAAL,CAAW,eAAX,EAA4BmB,KAA5B,CAAkC;AAC/CH,0BAAU4I;AADqC,aAAlC,EAEdnI,IAFc,EAAjB;AAGA,gBAAIS,MAAMC,OAAN,CAAcmI,IAAd,CAAJ,EAAyB;AACrB,oBAAI1D,YAAY0J,IAAIC,KAApB;AACA,oBAAIC,cAAc5J,UAAU4J,WAA5B;AACA,oBAAI3Q,gBAAgB+G,UAAU6J,YAA9B;AACA,oBAAIrB,cAAckB,IAAIlB,WAAtB;AACA,oBAAIsB,cAAc9J,UAAU+J,cAA5B;AACA,oBAAIvB,eAAe,CAAnB,EAAsB;AAClBsB,kCAAc9J,UAAUgK,eAAxB;AACH;AACD,sBAAM5Q,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,oBAAI6Q,SAAS,MAAM,QAAK7Q,KAAL,CAAW,SAAX,EAAsBmB,KAAtB,CAA4B;AAC3CgP,0BAAMK;AADqC,iBAA5B,EAEhB/O,IAFgB,EAAnB;AAGA,oBAAIqP,SAAS;AACT9P,8BAAU4I,OADD;AAETmH,gCAAYF,OAAO3P,EAFV;AAGT6C,kCAAc8M,OAAOnO,IAHZ;AAITsO,kCAAcR,WAJL;AAKT3Q,mCAAeA,aALN;AAMT6Q,iCAAaA,WANJ;AAOTR,kCAAcd,WAPL;AAQT7L,8BAAU6C;AARD,iBAAb;AAUA,sBAAM,QAAKpG,KAAL,CAAW,eAAX,EAA4BiR,GAA5B,CAAgCH,MAAhC,CAAN;AACH,aAxBD,MAwBO;AACH,oBAAIlK,YAAY0J,IAAIC,KAApB;AACA,sBAAM,QAAKvQ,KAAL,CAAW,eAAX,EAA4BmB,KAA5B,CAAkC;AACpCH,8BAAU4I;AAD0B,iBAAlC,EAEH5B,MAFG,CAEI;AACNnI,mCAAe+G,UAAU6J;AADnB,iBAFJ,CAAN;AAKH;AACD;AArCgC;AAsCnC;AACD;AACMS,oBAAN,GAAyB;AAAA;;AAAA;AACrB,gBAAItH,UAAU,QAAK5D,IAAL,CAAU,UAAV,CAAd;AACA,gBAAII,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAAlB;AACA,gBAAIC,aAAa;AACbrG,8BAAc,GADD;AAEbgR,8BAAc,CAFD;AAGbC,iCAAiB,CAHJ;AAIb/E,+BAAejG;AAJF,aAAjB;AAMA,gBAAIrG,OAAO,MAAM,QAAKC,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AACvCD,oBAAI0I;AADmC,aAA1B,EAEd5B,MAFc,CAEPxB,UAFO,CAAjB;;AAIA;AACA,gBAAII,YAAY,MAAM,QAAK5G,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5CD,oBAAI0I;AADwC,aAA1B,EAEnBnI,IAFmB,EAAtB;AAGA,gBAAIO,OAAO,MAAM,QAAKhC,KAAL,CAAW,MAAX,EAAmBmB,KAAnB,CAAyB;AACtCD,oBAAI0F,UAAU3E;AADwB,aAAzB,EAEdR,IAFc,EAAjB;AAGA,gBAAI4P,SAASrP,KAAKsP,aAAlB;;AAEA;AACA,gBAAIC,YAAY,MAAM,QAAKvR,KAAL,CAAW,aAAX,EAA0BmB,KAA1B,CAAgC;AAClDH,0BAAU4I;AADwC,aAAhC,EAEnBjJ,KAFmB,CAEb,mBAFa,EAEQC,MAFR,EAAtB;AAGA,gBAAIiD,UAAU,MAAM,QAAK7D,KAAL,CAAW,eAAX,EAA4BmB,KAA5B,CAAkC;AAClDH,0BAAU4I;AADwC,aAAlC,EAEjBnI,IAFiB,EAApB;;AAIA;AACA,gBAAI+P,YAAY,EAAhB;AACA,gBAAID,UAAUtQ,MAAV,IAAoB,CAAxB,EAA2B;AACvBuQ,4BAAYD,UAAU,CAAV,EAAaE,UAAzB;AACH,aAFD,MAEO;AACHD,4BAAYD,UAAU,CAAV,EAAaE,UAAb,GAA0B,GAA1B,GAAgCF,UAAUtQ,MAA1C,GAAmD,KAA/D;AACH;;AAED;AACA,gBAAIyQ,eAAexS,OAAOsE,IAAP,CAAY4C,WAAZ,EAAyB3C,MAAzB,CAAgC,qBAAhC,CAAnB;;AAEA;AACA,gBAAIkO,cAAczP,MAAM0P,MAAN,CAAa,uBAAb,CAAlB;AACA,gBAAInP,UAAU;AACV,0BAAU4O,MADA;AAEV,+BAAeM,WAFL;AAGV,wBAAQ,4BAHE;AAIV,qCAAoB,QAJV;AAKV,wBAAO,OALG;AAMV,wBAAQ;AACN,8BAAU;AACN,iCAASH;AADH,qBADJ;AAIN,6BAAS;AACL,iCAASE;AADJ,qBAJH;AAON,6BAAS;AACL,iCAAS7N,QAAQE;AADZ,qBAPH;AAUN,yCAAqB;AACjB,iCAASF,QAAQhE;AADA,qBAVf;AAaN,8BAAU;AACN,iCAAS;AADH;AAbJ;AANE,aAAd;;AAyBA,kBAAMgS,cAAc3P,MAAM6E,OAAN,CAAc,QAAd,EAAwB,KAAxB,CAApB;AACA,kBAAM+K,QAAQ,MAAMD,YAAYE,cAAZ,EAApB;AACA,kBAAMC,MAAM,MAAMH,YAAYI,WAAZ,CAAwBH,KAAxB,EAA8BrP,OAA9B,CAAlB;;AAEA;AACA,kBAAM,QAAKyP,sBAAL,CAA4BtL,SAA5B,EAAuC/C,OAAvC,EAAgD0N,SAAhD,EAA2DF,MAA3D,EAAmE,CAAnE,CAAN;;AAEA,mBAAO,QAAK7M,OAAL,EAAP;AA3EqB;AA4ExB;AACK2N,qBAAN,GAA0B;AAAA;;AAAA;AACtB,gBAAIvI,UAAU,QAAK5D,IAAL,CAAU,UAAV,CAAd;AACA,gBAAIQ,aAAa;AACb2K,8BAAc;AADD,aAAjB;AAGA,gBAAIpR,OAAO,MAAM,QAAKC,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AACvCD,oBAAI0I;AADmC,aAA1B,EAEd5B,MAFc,CAEPxB,UAFO,CAAjB;AAGA,mBAAO,QAAKhC,OAAL,CAAazE,IAAb,CAAP;AARsB;AASzB;AACKqS,uBAAN,GAA4B;AAAA;;AAAA;AAAE;AAC1B,kBAAMxI,UAAU,QAAKnK,GAAL,CAAS,SAAT,CAAhB;AACA,kBAAM4S,SAAS,QAAK5S,GAAL,CAAS,QAAT,CAAf;AACA,kBAAM6S,aAAa,QAAK7S,GAAL,CAAS,SAAT,KAAuB,CAA1C;AACA,kBAAMI,gBAAgB,QAAKJ,GAAL,CAAS,eAAT,KAA6B,CAAnD;AACA,gBAAI2G,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAAlB;AACA,gBAAIgM,cAAc,EAAlB;AACA,gBAAIzO,cAAc,IAAlB;;AAEA,gBAAIuO,UAAU,CAAd,EAAiB;AAAE;AACf,oBAAI/B,MAAM,MAAM,QAAKtQ,KAAL,CAAW,eAAX,EAA4BmB,KAA5B,CAAkC;AAC9CH,8BAAU4I;AADoC,iBAAlC,EAEbnI,IAFa,EAAhB;AAGA,oBAAIS,MAAMC,OAAN,CAAcmO,GAAd,CAAJ,EAAwB;AACpB,wBAAIO,SAAS,MAAM,QAAK7Q,KAAL,CAAW,SAAX,EAAsBmB,KAAtB,CAA4B;AAC3CD,4BAAIoR;AADuC,qBAA5B,EAEhB7Q,IAFgB,EAAnB;AAGA8Q,kCAAc1B,OAAOnO,IAArB;AACA,wBAAIoO,SAAS;AACT9P,kCAAU4I,OADD;AAETmH,oCAAYuB,UAFH;AAGTvO,sCAAc8M,OAAOnO,IAHZ;AAITsO,sCAAcH,OAAOV,IAJZ;AAKTtQ,uCAAeA,aALN;AAMT0D,kCAAU6C;AAND,qBAAb;AAQA,0BAAM,QAAKpG,KAAL,CAAW,eAAX,EAA4BiR,GAA5B,CAAgCH,MAAhC,CAAN;AACAhN,kCAAcgN,MAAd;AACA,wBAAItK,aAAa;AACbrG,sCAAc,GADD;AAEbiR,yCAAiB,CAFJ;AAGb/E,uCAAejG;AAHF,qBAAjB;AAKA,0BAAM,QAAKpG,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5BD,4BAAI0I;AADwB,qBAA1B,EAEH5B,MAFG,CAEIxB,UAFJ,CAAN;AAGH,iBAvBD,MAuBO;AACH,wBAAIqK,SAAS,MAAM,QAAK7Q,KAAL,CAAW,SAAX,EAAsBmB,KAAtB,CAA4B;AAC3CD,4BAAIoR;AADuC,qBAA5B,EAEhB7Q,IAFgB,EAAnB;AAGA8Q,kCAAc1B,OAAOnO,IAArB;AACA,wBAAIoO,SAAS;AACT9P,kCAAU4I,OADD;AAETmH,oCAAYuB,UAFH;AAGTvO,sCAAc8M,OAAOnO,IAHZ;AAITsO,sCAAcH,OAAOV,IAJZ;AAKTtQ,uCAAeA,aALN;AAMT0D,kCAAU6C;AAND,qBAAb;AAQA,0BAAM,QAAKpG,KAAL,CAAW,eAAX,EAA4BmB,KAA5B,CAAkC;AACpCH,kCAAU4I;AAD0B,qBAAlC,EAEH5B,MAFG,CAEI8I,MAFJ,CAAN;AAGAhN,kCAAcgN,MAAd;AACH;AACJ,aA7CD,MA6CO,IAAIuB,UAAU,CAAd,EAAiB;AAAE;AACtB,oBAAI7L,aAAa;AACbrG,kCAAc,GADD;AAEbkM,mCAAejG;AAFF,iBAAjB;AAIA,sBAAM,QAAKpG,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5BD,wBAAI0I;AADwB,iBAA1B,EAEH5B,MAFG,CAEIxB,UAFJ,CAAN;AAGA+L,8BAAc,KAAd;AACH;;AAED;AACA,kBAAM,QAAKC,eAAL,CAAqBH,MAArB,EAA4BzI,OAA5B,EAAoC2I,WAApC,EAAgD1S,aAAhD,CAAN;;AAEA;AACA,gBAAI+G,YAAY,MAAM,QAAK5G,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5CD,oBAAI0I;AADwC,aAA1B,EAEnBnI,IAFmB,EAAtB;AAGA,gBAAIO,OAAO,MAAM,QAAKhC,KAAL,CAAW,MAAX,EAAmBmB,KAAnB,CAAyB;AACtCD,oBAAI0F,UAAU3E;AADwB,aAAzB,EAEdR,IAFc,EAAjB;AAGA,gBAAI8P,YAAY,MAAM,QAAKvR,KAAL,CAAW,aAAX,EAA0BmB,KAA1B,CAAgC;AAClDH,0BAAU4I;AADwC,aAAhC,EAEnBjJ,KAFmB,CAEb,mBAFa,EAEQC,MAFR,EAAtB;;AAIA;AACA,gBAAI6R,gBAAgBJ,UAAU,CAAV,GAAc,CAAd,GAAmBA,UAAU,CAAV,GAAc,CAAd,GAAkB,CAAzD;;AAEA;AACA,kBAAM,QAAKH,sBAAL,CAA4BtL,SAA5B,EAAuC9C,WAAvC,EAAoDyN,SAApD,EAA+DvP,KAAKsP,aAApE,EAAmFmB,aAAnF,CAAN;;AAEA,mBAAO,QAAKjO,OAAL,EAAP;AArFwB;AAsF3B;AACEgO,mBAAN,CAAsBH,MAAtB,EAA6BzI,OAA7B,EAAqC2I,WAArC,EAAiD1S,aAAjD,EAA+D;AAAA;;AAAA;AAC9D,gBAAI+G,YAAY,MAAM,QAAK5G,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5CD,oBAAI0I;AADwC,aAA1B,EAEnBjJ,KAFmB,CAEb,SAFa,EAEFc,IAFE,EAAtB;AAGA,gBAAIO,OAAO,MAAM,QAAKhC,KAAL,CAAW,MAAX,EAAmBmB,KAAnB,CAAyB;AACtCD,oBAAI0F,UAAU3E;AADwB,aAAzB,EAEdtB,KAFc,CAER,eAFQ,EAESc,IAFT,EAAjB;AAGA,gBAAI4P,SAASrP,KAAKsP,aAAlB;AACA;AACA;AACA;AACA;AACA;AACA,gBAAIC,YAAY,MAAM,QAAKvR,KAAL,CAAW,aAAX,EAA0BmB,KAA1B,CAAgC;AAClDH,0BAAU4I;AADwC,aAAhC,EAEnBjJ,KAFmB,CAEb,YAFa,EAECC,MAFD,EAAtB;AAGA;AACA,gBAAI4Q,YAAY,EAAhB;AACA,gBAAID,UAAUtQ,MAAV,IAAoB,CAAxB,EAA2B;AACvBuQ,4BAAYD,UAAU,CAAV,EAAaE,UAAzB;AACH,aAFD,MAEO;AACHD,4BAAYD,UAAU,CAAV,EAAaE,UAAb,GAA0B,GAA1B,GAAgCF,UAAUtQ,MAA1C,GAAmD,KAA/D;AACH;AACD;AACA,gBAAImF,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAAlB;AACA,gBAAImL,eAAexS,OAAOsE,IAAP,CAAY4C,WAAZ,EAAyB3C,MAAzB,CAAgC,qBAAhC,CAAnB;AACA;AACA;AACA,gBAAIkO,cAAczP,MAAM0P,MAAN,CAAa,uBAAb,CAAlB;AACA,gBAAInP,UAAU;AACV,0BAAU4O,MADA;AAEV,+BAAeM,WAFL;AAGV,wBAAQ,4BAHE;AAIV,qCAAoB,QAJV;AAKV,wBAAO,OALG;AAMV,wBAAQ;AACN,8BAAU;AACN,iCAASH;AADH,qBADJ;AAIN,6BAAS;AACL,iCAASE;AADJ,qBAJH;AAON,6BAAS;AACL,iCAASa;AADJ,qBAPH;AAUN,yCAAqB;AACjB,iCAAS1S;AADQ,qBAVf;AAaN,8BAAU;AACN,iCAAS;AADH;AAbJ;AANE,aAAd;AAwBA,kBAAMgS,cAAc3P,MAAM6E,OAAN,CAAc,QAAd,EAAwB,KAAxB,CAApB;AACA,kBAAM+K,QAAQ,MAAMD,YAAYE,cAAZ,EAApB;AACA,kBAAMC,MAAM,MAAMH,YAAYI,WAAZ,CAAwBH,KAAxB,EAA8BrP,OAA9B,CAAlB;AAvD8D;AAwD9D;AACQiQ,sBAAN,GAA2B;AAAA;;AAAA;AACvB,kBAAMxR,KAAK,QAAKzB,GAAL,CAAS,SAAT,CAAX;AACA,gBAAI6K,OAAO,MAAM,QAAKtK,KAAL,CAAW,eAAX,EAA4BmB,KAA5B,CAAkC;AAC/CH,0BAAUE;AADqC,aAAlC,EAEdO,IAFc,EAAjB;AAGA,gBAAI,CAACS,MAAMC,OAAN,CAAcmI,IAAd,CAAL,EAA0B;AACtB,uBAAO,QAAK9F,OAAL,CAAa8F,IAAb,CAAP;AACH,aAFD,MAEO;AACH,uBAAO,QAAKzE,IAAL,CAAU,GAAV,EAAe,KAAf,CAAP;AACH;AATsB;AAU1B;AACK8M,qBAAN,GAA0B;AAAA;;AAAA;AACtB,kBAAMC,KAAK,QAAK5M,IAAL,CAAU,UAAV,CAAX;AACA,kBAAMtD,OAAO,QAAKsD,IAAL,CAAU,MAAV,CAAb;AACA,kBAAMrD,SAAS,QAAKqD,IAAL,CAAU,QAAV,CAAf;AACA,kBAAM6M,WAAW,QAAK7M,IAAL,CAAU,UAAV,CAAjB;AACA,kBAAM8M,aAAa,QAAK9M,IAAL,CAAU,YAAV,CAAnB;AACA,kBAAMjD,WAAW+P,WAAW,CAAX,CAAjB;AACA,kBAAM5P,OAAO4P,WAAW,CAAX,CAAb;AACA,kBAAM1P,WAAW0P,WAAW,CAAX,CAAjB;AACA,gBAAIxI,OAAO;AACP1K,2BAAW8C,IADJ;AAEPC,wBAAQA,MAFD;AAGP8H,yBAASoI,QAHF;AAIP9P,0BAAUA,QAJH;AAKPG,sBAAMA,IALC;AAMPE,0BAAUA;AANH,aAAX;AAQA,kBAAMpD,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMD,OAAO,MAAMC,MAAMmB,KAAN,CAAY;AAC3BjB,0BAAU0S;AADiB,aAAZ,EAEhB5K,MAFgB,CAETsC,IAFS,CAAnB;AAGA,mBAAO,QAAK9F,OAAL,CAAazE,IAAb,CAAP;AArBsB;AAsBzB;AACKgT,eAAN,GAAoB;AAAA;;AAAA;AAChB,gBAAI,CAAC,QAAKC,MAAV,EAAkB;AACd,uBAAO,KAAP;AACH;AACD,kBAAMC,SAAS,QAAKjN,IAAL,EAAf;AACA,kBAAM9E,KAAK,QAAK8E,IAAL,CAAU,IAAV,CAAX;AACA,kBAAMhG,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACAiT,mBAAOC,OAAP,GAAiBD,OAAOC,OAAP,GAAiB,CAAjB,GAAqB,CAAtC;AACAD,mBAAOE,MAAP,GAAgBF,OAAOE,MAAP,GAAgB,CAAhB,GAAoB,CAApC;AACA,gBAAIjS,KAAK,CAAT,EAAY;AACR,sBAAMlB,MAAMmB,KAAN,CAAY;AACdD,wBAAIA;AADU,iBAAZ,EAEH8G,MAFG,CAEIiL,MAFJ,CAAN;AAGH,aAJD,MAIO;AACH,uBAAOA,OAAO/R,EAAd;AACA,sBAAMlB,MAAMiR,GAAN,CAAUgC,MAAV,CAAN;AACH;AACD,mBAAO,QAAKzO,OAAL,CAAayO,MAAb,CAAP;AAjBgB;AAkBnB;AACKG,sBAAN,GAA2B;AAAA;;AAAA;AACvB,kBAAMzT,UAAU,QAAKqG,IAAL,CAAU,SAAV,CAAhB;AACA,kBAAMkI,QAAQ,QAAKlI,IAAL,CAAU,QAAV,CAAd;AACA,kBAAMsE,OAAO,MAAM,QAAKtK,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AACzCjB,0BAAUP;AAD+B,aAA1B,EAEhBqI,MAFgB,CAET;AACN7H,8BAAc+N;AADR,aAFS,CAAnB;AAKA,mBAAO,QAAK1J,OAAL,CAAa8F,IAAb,CAAP;AARuB;AAS1B;AACK+I,iBAAN,GAAsB;AAAA;;AAAA;AAClB,kBAAMnS,KAAK,QAAK8E,IAAL,CAAU,IAAV,CAAX;AACA,kBAAM,QAAKhG,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5BD,oBAAIA;AADwB,aAA1B,EAEHoS,KAFG,CAEG,CAFH,EAEMC,MAFN,EAAN;AAGA;AACA,kBAAM,QAAKvT,KAAL,CAAW,aAAX,EAA0BmB,KAA1B,CAAgC;AAClCH,0BAAUE;AADwB,aAAhC,EAEHqS,MAFG,EAAN;AAGA;AACA,mBAAO,QAAK/O,OAAL,EAAP;AAVkB;AAWrB;AACKgP,+BAAN,GAAoC;AAAA;;AAAA;AAChC,kBAAMC,WAAW,QAAKzN,IAAL,CAAU,UAAV,CAAjB;AACA,gBAAIjG,OAAO,MAAM,QAAKC,KAAL,CAAW,qBAAX,EAAkCmB,KAAlC,CAAwC;AACrDsS,0BAAUA,QAD2C;AAErD9R,2BAAW;AAF0C,aAAxC,EAGdhB,KAHc,CAGR,UAHQ,EAGIC,MAHJ,EAAjB;AAIA,mBAAO,QAAK4D,OAAL,CAAazE,IAAb,CAAP;AANgC;AAOnC;;AAED;;;;;;;;AAQMmS,0BAAN,CAA6BtL,SAA7B,EAAwC9C,WAAxC,EAAqDyN,SAArD,EAAgEmC,MAAhE,EAAwEjB,gBAAgB,CAAxF,EAA2F;AAAA;;AAAA;AACvF,gBAAI;AACA;AACA,sBAAMkB,iBAAiBzR,MAAM0P,MAAN,CAAa,4BAAb,CAAvB;AACA,oBAAI,CAAC+B,cAAD,IAAmB,CAACA,eAAeC,OAAvC,EAAgD;AAC5CtS,4BAAQC,GAAR,CAAY,kBAAZ;AACA,2BAAO,EAAEiD,SAAS,KAAX,EAAkBqP,QAAQ,UAA1B,EAAP;AACH;;AAEDvS,wBAAQC,GAAR,CAAY,sBAAZ;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBqF,UAAU1F,EAA/B,EAAmC,MAAnC,EAA2C0F,UAAU1G,QAArD;AACAoB,wBAAQC,GAAR,CAAY,OAAZ,EAAqBkR,aAArB,EAAoC,OAApC,EAA6C3O,cAAc,GAAd,GAAoB,GAAjE;;AAEA;AACA,oBAAI,CAAC4P,MAAL,EAAa;AACTpS,4BAAQsE,KAAR,CAAc,qBAAd;AACA,2BAAO,EAAEpB,SAAS,KAAX,EAAkBqP,QAAQ,gBAA1B,EAAP;AACH;;AAED,oBAAI,CAACjN,SAAD,IAAc,CAACA,UAAU1G,QAA7B,EAAuC;AACnCoB,4BAAQsE,KAAR,CAAc,kBAAd;AACA,2BAAO,EAAEpB,SAAS,KAAX,EAAkBqP,QAAQ,eAA1B,EAAP;AACH;;AAED,sBAAMC,gBAAgB5R,MAAM6E,OAAN,CAAc,QAAd,EAAwB,KAAxB,CAAtB;;AAEA;AACA,oBAAIgN,YAAJ;AACA,oBAAI;AACAA,mCAAeD,cAAcE,iBAAd,CACX;AACI9T,kCAAU0G,UAAU1G,QADxB;AAEI+T,oCAAY1C;AAFhB,qBADW,EAKXzN,WALW,EAMX4P,MANW,EAOXjB,aAPW,CAAf;AASH,iBAVD,CAUE,OAAOyB,eAAP,EAAwB;AACtB5S,4BAAQsE,KAAR,CAAc,WAAd,EAA2BsO,gBAAgBzR,OAA3C;;AAEA;AACA,wBAAI;AACA,8BAAM0R,YAAY7O,KAAK8O,SAAL,CAAe;AAC7B5G,kCAAM,kBADuB;AAE7B/K,qCAASyR,gBAAgBzR;AAFI,yBAAf,CAAlB;;AAKA;AACA,8BAAM4R,eAAe,MAAM,QAAKrU,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AACjDD,gCAAI0F,UAAU1F;AADmC,yBAA1B,EAExBP,KAFwB,CAElB,sBAFkB,EAEMc,IAFN,EAA3B;;AAIA;AACA,4BAAIS,MAAMC,OAAN,CAAckS,YAAd,KAA+BA,aAAaC,oBAAb,KAAsC,CAAzE,EAA4E;AACxE,kCAAM,QAAKtU,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5BD,oCAAI0F,UAAU1F;AADc,6BAA1B,EAEH8G,MAFG,CAEI;AACNsM,sDAAsB,CADhB;AAENC,4DAA4BJ;AAFtB,6BAFJ,CAAN;AAMA7S,oCAAQC,GAAR,CAAY,cAAZ;AACH,yBARD,MAQO;AACHD,oCAAQC,GAAR,CAAY,eAAZ;AACH;AACJ,qBAvBD,CAuBE,OAAOiT,OAAP,EAAgB;AACdlT,gCAAQsE,KAAR,CAAc,gBAAd,EAAgC4O,QAAQ/R,OAAxC;AACH;;AAED,2BAAO,EAAE+B,SAAS,KAAX,EAAkBqP,QAAQ,mBAA1B,EAA+CjO,OAAOsO,gBAAgBzR,OAAtE,EAAP;AACH;;AAEDnB,wBAAQC,GAAR,CAAY,WAAZ,EAAyB+D,KAAK8O,SAAL,CAAeL,YAAf,EAA6B,IAA7B,EAAmC,CAAnC,CAAzB;;AAEA;AACA,sBAAMtL,SAAS,MAAMqL,cAAcW,kBAAd,CAAiCV,YAAjC,CAArB;;AAEA,oBAAItL,OAAOjE,OAAX,EAAoB;AAChBlD,4BAAQC,GAAR,CAAY,YAAZ;;AAEA;AACA,wBAAI;AACA;AACA,8BAAM8S,eAAe,MAAM,QAAKrU,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AACjDD,gCAAI0F,UAAU1F;AADmC,yBAA1B,EAExBP,KAFwB,CAElB,gDAFkB,EAEgCc,IAFhC,EAA3B;;AAIA;AACA,4BAAIS,MAAMC,OAAN,CAAckS,YAAd,KAA+BA,aAAaC,oBAAb,KAAsC,CAAzE,EAA4E;AACxE,kCAAM,QAAKtU,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5BD,oCAAI0F,UAAU1F;AADc,6BAA1B,EAEH8G,MAFG,CAEI;AACNsM,sDAAsB,CADhB;AAENI,2DAA2BrO,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAFrB;AAGNgO,4DAA4B;AAHtB,6BAFJ,CAAN;AAOAjT,oCAAQC,GAAR,CAAY,YAAZ;AACH,yBATD,MASO;AACHD,oCAAQC,GAAR,CAAY,aAAZ;AACH;AACJ,qBAnBD,CAmBE,OAAOiT,OAAP,EAAgB;AACdlT,gCAAQsE,KAAR,CAAc,cAAd,EAA8B4O,QAAQ/R,OAAtC;AACA;AACH;;AAED,2BAAO,EAAE+B,SAAS,IAAX,EAAP;AAEH,iBA9BD,MA8BO;AACHlD,4BAAQsE,KAAR,CAAc,aAAd,EAA6B6C,OAAO7C,KAApC;;AAEA;AACA,wBAAI;AACA,8BAAM,QAAK5F,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5BD,gCAAI0F,UAAU1F;AADc,yBAA1B,EAEH8G,MAFG,CAEI;AACNsM,kDAAsB,CADhB;AAENC,wDAA4BjP,KAAK8O,SAAL,CAAe3L,OAAO7C,KAAtB;AAFtB,yBAFJ,CAAN;AAMH,qBAPD,CAOE,OAAO4O,OAAP,EAAgB;AACdlT,gCAAQsE,KAAR,CAAc,cAAd,EAA8B4O,QAAQ/R,OAAtC;AACH;;AAED,2BAAO,EAAE+B,SAAS,KAAX,EAAkBqP,QAAQ,WAA1B,EAAuCjO,OAAO6C,OAAO7C,KAArD,EAAP;AACH;AAEJ,aA5HD,CA4HE,OAAOA,KAAP,EAAc;AACZtE,wBAAQsE,KAAR,CAAc,aAAd,EAA6BA,KAA7B;;AAEA;AACA,oBAAI;AACA;AACA,0BAAMyO,eAAe,MAAM,QAAKrU,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AACjDD,4BAAI0F,UAAU1F;AADmC,qBAA1B,EAExBP,KAFwB,CAElB,sBAFkB,EAEMc,IAFN,EAA3B;;AAIA,0BAAM0S,YAAY7O,KAAK8O,SAAL,CAAe;AAC7B5G,8BAAM,WADuB;AAE7B/K,iCAASmD,MAAMnD,OAFc;AAG7BkS,+BAAO/O,MAAM+O;AAHgB,qBAAf,CAAlB;;AAMA;AACA,wBAAIzS,MAAMC,OAAN,CAAckS,YAAd,KAA+BA,aAAaC,oBAAb,KAAsC,CAAzE,EAA4E;AACxE,8BAAM,QAAKtU,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5BD,gCAAI0F,UAAU1F;AADc,yBAA1B,EAEH8G,MAFG,CAEI;AACNsM,kDAAsB,CADhB;AAENC,wDAA4BJ;AAFtB,yBAFJ,CAAN;AAMA7S,gCAAQC,GAAR,CAAY,YAAZ;AACH,qBARD,MAQO;AACHD,gCAAQC,GAAR,CAAY,aAAZ;AACH;AACJ,iBAxBD,CAwBE,OAAOiT,OAAP,EAAgB;AACdlT,4BAAQsE,KAAR,CAAc,aAAd,EAA6B4O,QAAQ/R,OAArC;AACH;;AAED,uBAAO,EAAE+B,SAAS,KAAX,EAAkBqP,QAAQ,WAA1B,EAAuCjO,OAAOA,MAAMnD,OAApD,EAAP;AACH;AA9JsF;AA+J1F;;AAED;;;AAGMmS,8BAAN,GAAmC;AAAA;;AAAA;AAC/B,kBAAMhL,UAAU,QAAK5D,IAAL,CAAU,SAAV,CAAhB;;AAEA,gBAAI;AACA;AACA,oBAAIY,YAAY,MAAM,QAAK5G,KAAL,CAAW,OAAX,EAAoBmB,KAApB,CAA0B;AAC5CD,wBAAI0I;AADwC,iBAA1B,EAEnBnI,IAFmB,EAAtB;;AAIA,oBAAIS,MAAMC,OAAN,CAAcyE,SAAd,CAAJ,EAA8B;AAC1B,2BAAO,QAAKf,IAAL,CAAU,OAAV,CAAP;AACH;;AAED,oBAAIe,UAAUzG,YAAV,KAA2B,GAA/B,EAAoC;AAChC,2BAAO,QAAK0F,IAAL,CAAU,YAAV,CAAP;AACH;;AAED;AACA,oBAAI7D,OAAO,MAAM,QAAKhC,KAAL,CAAW,MAAX,EAAmBmB,KAAnB,CAAyB;AACtCD,wBAAI0F,UAAU3E;AADwB,iBAAzB,EAEdR,IAFc,EAAjB;;AAIA;AACA,oBAAIqC,cAAc,MAAM,QAAK9D,KAAL,CAAW,eAAX,EAA4BmB,KAA5B,CAAkC;AACtDH,8BAAU4I;AAD4C,iBAAlC,EAErBnI,IAFqB,EAAxB;;AAIA;AACA,oBAAI8P,YAAY,MAAM,QAAKvR,KAAL,CAAW,aAAX,EAA0BmB,KAA1B,CAAgC;AAClDH,8BAAU4I;AADwC,iBAAhC,EAEnBjJ,KAFmB,CAEb,mBAFa,EAEQC,MAFR,EAAtB;;AAIA;AACA,oBAAI6R,gBAAgBvQ,MAAMC,OAAN,CAAc2B,WAAd,IAA6B,CAA7B,GAAiC,CAArD,CA9BA,CA8BwD;;AAExD;AACA,sBAAM,QAAKoO,sBAAL,CAA4BtL,SAA5B,EAAuC9C,WAAvC,EAAoDyN,SAApD,EAA+DvP,KAAKsP,aAApE,EAAmFmB,aAAnF,CAAN;;AAEA,uBAAO,QAAKjO,OAAL,CAAa,QAAb,CAAP;AAEH,aArCD,CAqCE,OAAOoB,KAAP,EAAc;AACZtE,wBAAQsE,KAAR,CAAc,eAAd,EAA+BA,KAA/B;AACA,uBAAO,QAAKC,IAAL,CAAU,QAAV,CAAP;AACH;AA3C8B;AA4ClC;AA1oD+B,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\order.js", "sourcesContent": ["const Base = require('./base.js');\nconst moment = require('moment');\nconst _ = require('lodash');\n// const Jushuitan = require('jushuitan');\nconst WangDianSync = require('../../common/wangdian_sync.js');\nmodule.exports = class extends Base {\n    /**\n     * index action\n     * @return {Promise} []\n     */\n    async indexAction() {\n        const page = this.get('page') || 1;\n        const size = this.get('size') || 10;\n        const orderSn = this.get('orderSn') || '';\n        const consignee = this.get('consignee') || '';\n        const logistic_code = this.get('logistic_code') || '';\n        const status = this.get('status') || '';\n        let data = {}\n        const model = this.model('order');\n        if (logistic_code == '') {\n            let whereCondition = {\n                order_sn: ['like', `%${orderSn}%`],\n                consignee: ['like', `%${consignee}%`],\n                order_status: ['IN', status],\n                order_type: ['<', 7],\n            };\n\n            // 特定状态下排除有售后申请的订单（保留全部订单和已关闭的显示）\n            const excludeRefundStatuses = ['300', '301', '401']; // 待发货、待收货、已收货\n            const shouldExcludeRefund = excludeRefundStatuses.some(s => status.includes(s));\n\n            if (shouldExcludeRefund) {\n                // 获取有售后申请的订单ID列表\n                const refundOrderIds = await this.model('refund_apply').field('order_id').select();\n                const excludeOrderIds = refundOrderIds.map(item => item.order_id);\n\n                if (excludeOrderIds.length > 0) {\n                    whereCondition.id = ['NOT IN', excludeOrderIds];\n                }\n            }\n\n            data = await model.where(whereCondition).order(['id DESC']).page(page, size).countSelect();\n            console.log(data);\n        } else {\n            let orderData = await this.model('order_express').where({\n                logistic_code: logistic_code\n            }).find();\n            let order_id = orderData.order_id;\n            data = await model.where({\n                id: order_id\n            }).order(['id DESC']).page(page, size).countSelect();\n        }\n        for (const item of data.data) {\n            item.goodsList = await this.model('order_goods').field('goods_name,goods_aka,list_pic_url,number,goods_specifition_name_value,retail_price').where({\n                order_id: item.id,\n                is_delete: 0\n            }).select();\n            item.goodsCount = 0;\n            item.goodsList.forEach(v => {\n                item.goodsCount += v.number;\n            });\n            let user = await this.model('user').where({\n                id: item.user_id\n            }).field('nickname,name,mobile,avatar').find();\n            if (!think.isEmpty(user)) {\n                // 确保nickname存在且不为空再进行base64解码\n                if (user.nickname) {\n                    try {\n                        user.nickname = Buffer.from(user.nickname, 'base64').toString();\n                    } catch (e) {\n                        console.log('用户昵称base64解码失败:', e.message);\n                        user.nickname = '解码失败';\n                    }\n                } else {\n                    user.nickname = '未设置昵称';\n                }\n            } else {\n                user = { nickname: '已删除', name: '', mobile: '', avatar: '' };\n            }\n            item.userInfo = user;\n            let province_name = await this.model('region').where({\n                id: item.province\n            }).getField('name', true);\n            let city_name = await this.model('region').where({\n                id: item.city\n            }).getField('name', true);\n            let district_name = await this.model('region').where({\n                id: item.district\n            }).getField('name', true);\n            item.full_region = province_name + city_name + district_name;\n            item.postscript = Buffer.from(item.postscript, 'base64').toString();\n            item.add_time = moment.unix(item.add_time).format('YYYY-MM-DD HH:mm:ss');\n            if (item.pay_time != 0) {\n                item.pay_time = moment.unix(item.pay_time).format('YYYY-MM-DD HH:mm:ss');\n            } else {\n                item.pay_time = 0;\n            }\n            item.order_status_text = await this.model('order').getOrderStatusText(item.id);\n            let express = await this.model('order_express').where({\n                order_id: item.id\n            }).find();\n            if (!think.isEmpty(express)) {\n                item.expressInfo = express.shipper_name + express.logistic_code;\n            } else {\n                item.expressInfo = ''\n            }\n\n            // 获取售后申请信息\n            let refundApply = await this.model('refund_apply').where({\n                order_id: item.id\n            }).find();\n\n            if (!think.isEmpty(refundApply)) {\n                item.refundInfo = {\n                    id: refundApply.id,\n                    status: refundApply.status,\n                    refund_amount: refundApply.refund_amount,\n                    refund_reason: refundApply.refund_reason,\n                    apply_time: refundApply.apply_time,\n                    status_text: this.getRefundStatusText(refundApply.status)\n                };\n                item.hasRefund = true;\n            } else {\n                item.refundInfo = null;\n                item.hasRefund = false;\n            }\n\n            // item.button_text = await this.model('order').getOrderBtnText(item.id);\n        }\n        return this.success(data);\n    }\n\n    /**\n     * 获取售后状态文本\n     */\n    getRefundStatusText(status) {\n        const statusMap = {\n            'pending': '待处理',\n            'processing': '处理中',\n            'approved': '已同意',\n            'rejected': '已拒绝',\n            'wait_return': '等待退货',\n            'returned': '已退货',\n            'completed': '已完成'\n        };\n        return statusMap[status] || '未知状态';\n    }\n\n    /**\n     * 获取售后订单列表\n     */\n    async refundListAction() {\n        const page = this.get('page') || 1;\n        const size = this.get('size') || 10;\n        const orderSn = this.get('orderSn') || '';\n        const refundStatus = this.get('refundStatus') || '';\n        const consignee = this.get('consignee') || '';\n        const refundType = this.get('refundType') || ''; // 退款类型筛选\n        const processStatus = this.get('processStatus') || ''; // 处理状态筛选：pending,processing为待处理，approved,completed为已处理\n\n        try {\n            console.log('=== 获取售后订单列表 ===');\n            console.log('页码:', page, '大小:', size, '订单号:', orderSn, '售后状态:', refundStatus);\n\n            // 构建查询条件\n            let whereCondition = {};\n            if (orderSn) {\n                whereCondition.order_sn = ['like', `%${orderSn}%`];\n            }\n            if (refundStatus) {\n                whereCondition.status = refundStatus;\n            }\n            if (refundType) {\n                whereCondition.refund_type = refundType;\n            }\n\n            // 处理状态筛选\n            if (processStatus === 'pending') {\n                whereCondition.status = ['IN', ['pending', 'processing']];\n            } else if (processStatus === 'processed') {\n                whereCondition.status = ['IN', ['rejected', 'completed', 'approved']];\n            } else {\n                // 默认情况下，排除已完成、已拒绝和已同意的售后申请\n                whereCondition.status = ['NOT IN', ['completed', 'rejected', 'approved']];\n            }\n\n            // 查询售后申请，关联订单信息\n            const refundModel = this.model('refund_apply');\n            const data = await refundModel.alias('ra')\n                .join({\n                    table: 'order',\n                    join: 'left',\n                    as: 'o',\n                    on: ['ra.order_id', 'o.id']\n                })\n                .where(whereCondition)\n                .where({\n                    'o.consignee': ['like', `%${consignee}%`]\n                })\n                .field('ra.*, o.order_sn, o.consignee, o.mobile, o.actual_price, o.add_time as order_add_time, o.user_id')\n                .order(['ra.id DESC'])\n                .page(page, size)\n                .countSelect();\n\n            // 处理每个售后申请的详细信息\n            for (const item of data.data) {\n                // 获取用户信息\n                let user = await this.model('user').where({\n                    id: item.user_id\n                }).field('nickname,name,mobile,avatar').find();\n\n                if (!think.isEmpty(user)) {\n                    // 确保nickname存在且不为空再进行base64解码\n                    if (user.nickname) {\n                        try {\n                            user.nickname = Buffer.from(user.nickname, 'base64').toString();\n                        } catch (e) {\n                            console.log('用户昵称base64解码失败:', e.message);\n                            user.nickname = '解码失败';\n                        }\n                    } else {\n                        user.nickname = '未设置昵称';\n                    }\n                } else {\n                    user = { nickname: '已删除', name: '', mobile: '', avatar: '' };\n                }\n                item.userInfo = user;\n\n                // 获取订单商品信息\n                item.goodsList = await this.model('order_goods').field('goods_name,goods_aka,list_pic_url,number,goods_specifition_name_value,retail_price').where({\n                    order_id: item.order_id,\n                    is_delete: 0\n                }).select();\n\n                // 处理图片字段\n                if (item.images) {\n                    try {\n                        item.images = JSON.parse(item.images);\n                    } catch (e) {\n                        item.images = [];\n                    }\n                } else {\n                    item.images = [];\n                }\n\n                // 格式化时间\n                item.apply_time_text = moment.unix(item.apply_time).format('YYYY-MM-DD HH:mm:ss');\n                item.order_add_time_text = moment.unix(item.order_add_time).format('YYYY-MM-DD HH:mm:ss');\n\n                // 添加状态文本\n                item.status_text = this.getRefundStatusText(item.status);\n            }\n\n            console.log('✅ 售后订单列表获取成功，共', data.count, '条记录');\n            return this.success(data);\n\n        } catch (error) {\n            console.error('获取售后订单列表失败:', error);\n            return this.fail('获取售后订单列表失败: ' + error.message);\n        }\n    }\n\n    /**\n     * 处理售后申请\n     */\n    async handleRefundAction() {\n        const applyId = this.post('applyId');\n        const action = this.post('action'); // approve, reject, complete\n        const adminMemo = this.post('adminMemo') || '';\n        const rejectReason = this.post('rejectReason') || '';\n\n        try {\n            console.log('=== 处理售后申请 ===');\n            console.log('申请ID:', applyId, '操作:', action);\n\n            // 获取售后申请信息\n            const refundApply = await this.model('refund_apply').where({\n                id: applyId\n            }).find();\n\n            if (think.isEmpty(refundApply)) {\n                return this.fail('售后申请不存在');\n            }\n\n            // 检查当前状态是否允许操作\n            if (refundApply.status === 'completed') {\n                return this.fail('售后已完成，无法再次操作');\n            }\n\n            const currentTime = parseInt(new Date().getTime() / 1000);\n            let updateData = {\n                process_time: currentTime,\n                updated_at: new Date()\n            };\n\n            switch (action) {\n                case 'approve':\n                    if (refundApply.status !== 'pending') {\n                        return this.fail('只有待处理状态的申请可以同意');\n                    }\n                    updateData.admin_memo = adminMemo;\n\n                    // 根据退款类型采用不同处理流程\n                    if (refundApply.refund_type === 'refund_only') {\n                        // 仅退款：需要调用微信退款API\n                        console.log('=== 开始处理仅退款申请 ===');\n\n                        // 获取订单信息\n                        const orderInfo = await this.model('order').where({ id: refundApply.order_id }).find();\n                        if (think.isEmpty(orderInfo)) {\n                            return this.fail('订单信息不存在');\n                        }\n\n                        // 检查订单是否已支付\n                        if (orderInfo.pay_status !== 2) {\n                            return this.fail('订单未支付，无法退款');\n                        }\n\n                        // 调用微信退款API\n                        try {\n                            console.log('=== 调用微信退款API ===');\n                            const WeixinService = this.service('weixin', 'api');\n\n                            // 生成退款单号\n                            const refundNo = 'RF' + Date.now() + Math.random().toString(36).substr(2, 4).toUpperCase();\n\n                            const refundParams = {\n                                out_trade_no: orderInfo.order_sn,                    // 原订单号\n                                out_refund_no: refundNo,                            // 退款单号\n                                total_fee: parseInt(orderInfo.actual_price * 100),  // 原订单金额（分）\n                                refund_fee: parseInt(refundApply.refund_amount * 100), // 退款金额（分）\n                                refund_desc: `售后退款：${refundApply.refund_reason}`\n                            };\n\n                            console.log('微信退款参数:', refundParams);\n                            const refundResult = await WeixinService.createRefund(refundParams);\n\n                            if (refundResult.success) {\n                                console.log('✅ 微信退款成功:', refundResult);\n\n                                // 退款成功，更新状态\n                                updateData.status = 'completed';\n                                updateData.complete_time = currentTime;\n                                updateData.admin_memo = `${adminMemo} [微信退款成功，退款单号：${refundResult.out_refund_no}]`;\n\n                                // 将订单状态更新为售后完成状态\n                                await this.model('order').where({\n                                    id: refundApply.order_id\n                                }).update({\n                                    order_status: 203, // 售后完成状态\n                                    refund_time: currentTime,\n                                    updated_at: new Date()\n                                });\n\n                                // 处理佣金扣除\n                                try {\n                                    const commissionService = this.service('commission', 'common');\n                                    await commissionService.handleRefundCommission(refundApply.order_id);\n                                    console.log('✅ 佣金扣除处理完成');\n                                } catch (commissionError) {\n                                    console.error('❌ 佣金扣除处理失败:', commissionError);\n                                    // 不影响主流程，继续执行\n                                }\n\n                            } else {\n                                console.error('❌ 微信退款失败:', refundResult);\n                                return this.fail(`微信退款失败：${refundResult.error_msg || '未知错误'}`);\n                            }\n\n                        } catch (refundError) {\n                            console.error('❌ 调用微信退款API异常:', refundError);\n                            return this.fail(`退款处理失败：${refundError.error_msg || refundError.message || '系统异常'}`);\n                        }\n\n                        // 推送退款状态到旺店通ERP\n                        try {\n                            console.log(`[旺店通同步] 仅退款申请已同意，开始推送退款状态到旺店通: ${refundApply.order_sn}`);\n\n                            // 获取商品信息\n                            const orderGoods = await this.model('order_goods').where({ order_id: refundApply.order_id }).select();\n\n                            if (!think.isEmpty(orderGoods)) {\n                                const wangdianSync = new WangDianSync(this);\n                                const result = await wangdianSync.pushOrderRefund(orderInfo, orderGoods);\n\n                                if (result.success) {\n                                    console.log(`[旺店通同步] 退款状态推送成功: ${refundApply.order_sn}`);\n                                } else {\n                                    console.error(`[旺店通同步] 退款状态推送失败: ${refundApply.order_sn}, 错误: ${result.message}`);\n                                }\n                            } else {\n                                console.error(`[旺店通同步] 获取商品信息失败，无法推送退款状态: ${refundApply.order_sn}`);\n                            }\n                        } catch (error) {\n                            console.error('[旺店通同步] 推送退款状态时发生异常:', error);\n                            // 不抛出异常，避免影响主要的退款流程\n                        }\n                    } else if (refundApply.refund_type === 'return_refund') {\n                        // 退货退款：等待用户退货\n                        updateData.status = 'wait_return';\n                        // 设置退货地址信息\n                        updateData.return_address = adminMemo || '请联系客服获取退货地址';\n                        updateData.return_contact = '客服中心';\n                        updateData.return_phone = '************';\n                    }\n                    break;\n\n                case 'reject':\n                    if (refundApply.status !== 'pending') {\n                        return this.fail('只有待处理状态的申请可以拒绝');\n                    }\n                    updateData.status = 'rejected';\n                    updateData.reject_reason = rejectReason;\n                    break;\n\n                case 'confirm_return':\n                    if (refundApply.status !== 'returned') {\n                        return this.fail('只有已退货状态的申请可以确认收货');\n                    }\n\n                    // 确认收货后，需要调用微信退款API\n                    console.log('=== 开始处理退货退款确认收货 ===');\n\n                    // 获取订单信息\n                    const orderInfo = await this.model('order').where({ id: refundApply.order_id }).find();\n                    if (think.isEmpty(orderInfo)) {\n                        return this.fail('订单信息不存在');\n                    }\n\n                    // 检查订单是否已支付\n                    if (orderInfo.pay_status !== 2) {\n                        return this.fail('订单未支付，无法退款');\n                    }\n\n                    // 调用微信退款API\n                    try {\n                        console.log('=== 调用微信退款API（退货退款） ===');\n                        const WeixinService = this.service('weixin', 'api');\n\n                        // 生成退款单号\n                        const refundNo = 'RF' + Date.now() + Math.random().toString(36).substr(2, 4).toUpperCase();\n\n                        const refundParams = {\n                            out_trade_no: orderInfo.order_sn,                    // 原订单号\n                            out_refund_no: refundNo,                            // 退款单号\n                            total_fee: parseInt(orderInfo.actual_price * 100),  // 原订单金额（分）\n                            refund_fee: parseInt(refundApply.refund_amount * 100), // 退款金额（分）\n                            refund_desc: `退货退款：${refundApply.refund_reason}`\n                        };\n\n                        console.log('微信退款参数:', refundParams);\n                        const refundResult = await WeixinService.createRefund(refundParams);\n\n                        if (refundResult.success) {\n                            console.log('✅ 微信退款成功:', refundResult);\n\n                            // 退款成功，更新状态\n                            updateData.status = 'completed';\n                            updateData.complete_time = currentTime;\n                            updateData.admin_memo = `${adminMemo || '已确认收到退货商品'} [微信退款成功，退款单号：${refundResult.out_refund_no}]`;\n\n                            // 确认收货后，将订单状态更新为售后完成状态\n                            await this.model('order').where({\n                                id: refundApply.order_id\n                            }).update({\n                                order_status: 203, // 售后完成状态\n                                refund_time: currentTime,\n                                updated_at: new Date()\n                            });\n\n                            // 处理佣金扣除\n                            try {\n                                const commissionService = this.service('commission', 'common');\n                                await commissionService.handleRefundCommission(refundApply.order_id);\n                                console.log('✅ 佣金扣除处理完成');\n                            } catch (commissionError) {\n                                console.error('❌ 佣金扣除处理失败:', commissionError);\n                                // 不影响主流程，继续执行\n                            }\n\n                        } else {\n                            console.error('❌ 微信退款失败:', refundResult);\n                            return this.fail(`微信退款失败：${refundResult.error_msg || '未知错误'}`);\n                        }\n\n                    } catch (refundError) {\n                        console.error('❌ 调用微信退款API异常:', refundError);\n                        return this.fail(`退款处理失败：${refundError.error_msg || refundError.message || '系统异常'}`);\n                    }\n\n                    // 推送退款状态到旺店通ERP\n                    try {\n                        console.log(`[旺店通同步] 退货退款已确认收货，开始推送退款状态到旺店通: ${refundApply.order_sn}`);\n\n                        // 获取商品信息\n                        const orderGoods = await this.model('order_goods').where({ order_id: refundApply.order_id }).select();\n\n                        if (!think.isEmpty(orderGoods)) {\n                            const wangdianSync = new WangDianSync(this);\n                            const result = await wangdianSync.pushOrderRefund(orderInfo, orderGoods);\n\n                            if (result.success) {\n                                console.log(`[旺店通同步] 退款状态推送成功: ${refundApply.order_sn}`);\n                            } else {\n                                console.error(`[旺店通同步] 退款状态推送失败: ${refundApply.order_sn}, 错误: ${result.message}`);\n                            }\n                        } else {\n                            console.error(`[旺店通同步] 获取商品信息失败，无法推送退款状态: ${refundApply.order_sn}`);\n                        }\n                    } catch (error) {\n                        console.error('[旺店通同步] 推送退款状态时发生异常:', error);\n                        // 不抛出异常，避免影响主要的退款流程\n                    }\n                    break;\n\n                case 'complete':\n                    // 这个动作现在主要用于仅退款的直接完成，或者其他特殊情况\n                    updateData.status = 'completed';\n                    updateData.complete_time = currentTime;\n                    break;\n\n                case 'processing':\n                    if (refundApply.status !== 'pending') {\n                        return this.fail('只有待处理状态的申请可以设为处理中');\n                    }\n                    updateData.status = 'processing';\n                    updateData.admin_memo = adminMemo;\n                    break;\n\n                default:\n                    return this.fail('无效的操作类型');\n            }\n\n            // 更新售后申请状态\n            await this.model('refund_apply').where({\n                id: applyId\n            }).update(updateData);\n\n            console.log('✅ 售后申请处理成功');\n            return this.success({\n                message: '售后申请处理成功',\n                newStatus: updateData.status\n            });\n\n        } catch (error) {\n            console.error('处理售后申请失败:', error);\n            return this.fail('处理售后申请失败: ' + error.message);\n        }\n    }\n\n    // 获取最近一个月各状态订单数量统计\n    async getStatusCountAction() {\n        try {\n            // 计算一个月前的时间戳\n            const oneMonthAgo = Math.floor(Date.now() / 1000) - (30 * 24 * 60 * 60);\n\n            const model = this.model('order');\n\n            // 获取各状态订单数量\n            const statusCounts = {};\n\n            // 全部订单（最近一个月）\n            statusCounts.all = await model.where({\n                add_time: ['>', oneMonthAgo],\n                order_type: ['<', 7],\n                is_delete: 0\n            }).count();\n\n            // 待付款订单 (101, 801)\n            statusCounts.toPay = await model.where({\n                add_time: ['>', oneMonthAgo],\n                order_status: ['IN', '101,801'],\n                order_type: ['<', 7],\n                is_delete: 0\n            }).count();\n\n            // 待发货订单 (300)\n            statusCounts.toDelivery = await model.where({\n                add_time: ['>', oneMonthAgo],\n                order_status: 300,\n                order_type: ['<', 7],\n                is_delete: 0\n            }).count();\n\n            // 待收货订单 (301)\n            statusCounts.toReceive = await model.where({\n                add_time: ['>', oneMonthAgo],\n                order_status: 301,\n                order_type: ['<', 7],\n                is_delete: 0\n            }).count();\n\n            // 已收货订单 (401)\n            statusCounts.received = await model.where({\n                add_time: ['>', oneMonthAgo],\n                order_status: 401,\n                order_type: ['<', 7],\n                is_delete: 0\n            }).count();\n\n            // 已关闭订单 (102, 103, 203) - 包含售后完成的订单\n            statusCounts.closed = await model.where({\n                add_time: ['>', oneMonthAgo],\n                order_status: ['IN', '102,103,203'],\n                order_type: ['<', 7],\n                is_delete: 0\n            }).count();\n\n            // 售后完成订单单独统计\n            statusCounts.refundCompleted = await model.where({\n                add_time: ['>', oneMonthAgo],\n                order_status: 203,\n                order_type: ['<', 7],\n                is_delete: 0\n            }).count();\n\n            return this.success(statusCounts);\n        } catch (error) {\n            console.error('获取订单状态统计失败:', error);\n            return this.fail('获取统计数据失败');\n        }\n    }\n\n    // 更新客服备注和旗子标识\n    async updateServiceMemoAction() {\n        try {\n            const orderId = this.post('order_id');\n            const adminMemo = this.post('admin_memo') || '';\n            const flagColor = this.post('service_flag_color') || null;\n            const priority = this.post('service_priority') || 0;\n\n            if (!orderId) {\n                return this.fail('订单ID不能为空');\n            }\n\n            // 检查订单是否存在\n            const orderExists = await this.model('order').where({ id: orderId }).find();\n            if (think.isEmpty(orderExists)) {\n                return this.fail('订单不存在');\n            }\n\n            // 更新订单的客服备注信息\n            await this.model('order').where({ id: orderId }).update({\n                admin_memo: adminMemo,\n                service_flag_color: flagColor,\n                service_priority: parseInt(priority),\n                service_updated_at: Math.floor(Date.now() / 1000),\n                service_updated_by: 'admin' // 这里可以从session中获取当前管理员信息\n            });\n\n            return this.success('客服备注更新成功');\n        } catch (error) {\n            console.error('更新客服备注失败:', error);\n            return this.fail('更新客服备注失败');\n        }\n    }\n\n    // 获取客服备注信息\n    async getServiceMemoAction() {\n        try {\n            const orderId = this.get('order_id');\n\n            if (!orderId) {\n                return this.fail('订单ID不能为空');\n            }\n\n            const orderInfo = await this.model('order').where({ id: orderId })\n                .field('id,order_sn,admin_memo,service_flag_color,service_priority,service_updated_at,service_updated_by')\n                .find();\n\n            if (think.isEmpty(orderInfo)) {\n                return this.fail('订单不存在');\n            }\n\n            // 格式化更新时间\n            if (orderInfo.service_updated_at) {\n                orderInfo.service_updated_at = moment.unix(orderInfo.service_updated_at).format('YYYY-MM-DD HH:mm:ss');\n            }\n\n            return this.success(orderInfo);\n        } catch (error) {\n            console.error('获取客服备注失败:', error);\n            return this.fail('获取客服备注失败');\n        }\n    }\n\n    async getAutoStatusAction() {\n        let status = await this.model('settings').where({\n            id: 1\n        }).field('autoDelivery').find();\n        let info = status.autoDelivery;\n        return this.success(info);\n    }\n    async toDeliveryAction() {\n        const page = this.get('page') || 1;\n        const size = this.get('size') || 10;\n        const status = this.get('status') || '';\n        const model = this.model('order');\n        const data = await model.where({\n            order_status: status,\n        }).order(['id DESC']).page(page, size).countSelect();\n        for (const item of data.data) {\n            item.goodsList = await this.model('order_goods').field('goods_name,list_pic_url,number,goods_specifition_name_value,retail_price').where({\n                order_id: item.id\n            }).select();\n            item.goodsCount = 0;\n            item.goodsList.forEach(v => {\n                item.goodsCount += v.number;\n            });\n            let province_name = await this.model('region').where({\n                id: item.province\n            }).getField('name', true);\n            let city_name = await this.model('region').where({\n                id: item.city\n            }).getField('name', true);\n            let district_name = await this.model('region').where({\n                id: item.district\n            }).getField('name', true);\n            item.address = province_name + city_name + district_name + item.address;\n            item.postscript = Buffer.from(item.postscript, 'base64').toString();\n            item.add_time = moment.unix(item.add_time).format('YYYY-MM-DD HH:mm:ss');\n            item.order_status_text = await this.model('order').getOrderStatusText(item.id);\n            item.button_text = await this.model('order').getOrderBtnText(item.id);\n        }\n        return this.success(data);\n    }\n    async saveGoodsListAction() {\n        // console.log(typeof(data));\n        let id = this.post('id');\n        let order_id = this.post('order_id');\n        let number = this.post('number');\n        let price = this.post('retail_price');\n        let addOrMinus = this.post('addOrMinus');\n        let changePrice = Number(number) * Number(price);\n        console.log(order_id);\n        console.log(changePrice);\n        if (addOrMinus == 0) {\n            await this.model('order_goods').where({\n                id: id\n            }).decrement('number', number);\n            await this.model('order').where({\n                id: order_id\n            }).decrement({\n                actual_price: changePrice,\n                order_price: changePrice,\n                goods_price: changePrice\n            });\n            let order_sn = this.model('order').generateOrderNumber();\n            await this.model('order').where({\n                id: order_id\n            }).update({\n                order_sn: order_sn\n            });\n            return this.success(order_sn);\n        } else if (addOrMinus == 1) {\n            await this.model('order_goods').where({\n                id: id\n            }).increment('number', number);\n            await this.model('order').where({\n                id: order_id\n            }).increment({\n                actual_price: changePrice,\n                order_price: changePrice,\n                goods_price: changePrice\n            });\n            let order_sn = this.model('order').generateOrderNumber();\n            await this.model('order').where({\n                id: order_id\n            }).update({\n                order_sn: order_sn\n            });\n            return this.success(order_sn);\n        }\n    }\n    async goodsListDeleteAction() {\n        console.log(this.post('id'));\n        let id = this.post('id');\n        let order_id = this.post('order_id');\n        let number = this.post('number');\n        let price = this.post('retail_price');\n        let addOrMinus = this.post('addOrMinus');\n        let changePrice = Number(number) * Number(price);\n        console.log(order_id);\n        console.log(changePrice);\n        await this.model('order_goods').where({\n            id: id\n        }).update({\n            is_delete: 1\n        });\n        await this.model('order').where({\n            id: order_id\n        }).decrement({\n            actual_price: changePrice,\n            order_price: changePrice,\n            goods_price: changePrice\n        });\n        let order_sn = this.model('order').generateOrderNumber();\n        await this.model('order').where({\n            id: order_id\n        }).update({\n            order_sn: order_sn\n        });\n        return this.success(order_sn);\n    }\n    async saveAdminMemoAction() {\n        const id = this.post('id');\n        const text = this.post('text');\n        const model = this.model('order');\n        let info = {\n            admin_memo: text\n        }\n        let data = await model.where({\n            id: id\n        }).update(info);\n        return this.success(data);\n    }\n    async savePrintInfoAction() {\n        const id = this.post('id');\n        const print_info = this.post('print_info');\n        const model = this.model('order');\n        let info = {\n            print_info: print_info\n        };\n        let data = await model.where({\n            id: id\n        }).update(info);\n        return this.success(data);\n    }\n    async saveExpressValueInfoAction() {\n        const id = this.post('id');\n        const express_value = this.post('express_value');\n        const model = this.model('order');\n        let info = {\n            express_value: express_value\n        };\n        let data = await model.where({\n            id: id\n        }).update(info);\n        return this.success(data);\n    }\n    async saveRemarkInfoAction() {\n        const id = this.post('id');\n        const remark = this.post('remark');\n        const model = this.model('order');\n        let info = {\n            remark: remark\n        };\n        let data = await model.where({\n            id: id\n        }).update(info);\n        return this.success(data);\n    }\n    async detailAction() {\n        const id = this.get('orderId');\n        const model = this.model('order');\n        let data = await model.where({\n            id: id\n        }).find();\n        data.goodsList = await this.model('order_goods').field('id,product_id,goods_name,goods_aka,list_pic_url,number,goods_specifition_name_value,retail_price,goods_id').where({\n            order_id: data.id,\n            is_delete: 0\n        }).select();\n        data.goodsCount = 0;\n        data.goodsList.forEach(v => {\n            data.goodsCount += v.number;\n        });\n        for (const item of data.goodsList) {\n            let info = await this.model('product').where({\n                id: item.product_id\n            }).field('goods_sn').find();\n            item.goods_sn = info.goods_sn;\n        }\n        console.log(data.goodsList);\n        let userInfo = await this.model('user').where({\n            id: data.user_id\n        }).find();\n        let _nickname = Buffer.from(userInfo.nickname, 'base64').toString();\n        data.user_name = _nickname;\n        data.avatar = userInfo.avatar;\n        let province_name = await this.model('region').where({\n            id: data.province\n        }).getField('name', true);\n        let city_name = await this.model('region').where({\n            id: data.city\n        }).getField('name', true);\n        let district_name = await this.model('region').where({\n            id: data.district\n        }).getField('name', true);\n        data.full_region = province_name + city_name + district_name;\n        data.postscript = Buffer.from(data.postscript, 'base64').toString();\n        data.order_status_text = await this.model('order').getOrderStatusText(data.id);\n        data.add_time = moment.unix(data.add_time).format('YYYY-MM-DD HH:mm:ss');\n        data.allAddress = data.full_region + data.address;\n        if (data.pay_time != 0) {\n            data.pay_time = moment.unix(data.pay_time).format('YYYY-MM-DD HH:mm:ss');\n        }\n        if (data.shipping_time != 0) {\n            data.shipping_time = moment.unix(data.shipping_time).format('YYYY-MM-DD HH:mm:ss');\n        }\n        if (data.confirm_time != 0) {\n            data.confirm_time = moment.unix(data.confirm_time).format('YYYY-MM-DD HH:mm:ss');\n        }\n        if (data.dealdone_time != 0) {\n            data.dealdone_time = moment.unix(data.dealdone_time).format('YYYY-MM-DD HH:mm:ss');\n        }\n        let def = await this.model('settings').where({\n            id: 1\n        }).find();\n        let senderInfo = {}\n        let receiveInfo = {}\n        receiveInfo = {\n            name: data.consignee,\n            mobile: data.mobile,\n            province: province_name,\n            province_id: data.province,\n            city: city_name,\n            city_id: data.city,\n            district: district_name,\n            district_id: data.district,\n            address: data.address\n        }\n        senderInfo = {\n            name: def.Name,\n            mobile: def.Tel,\n            province: def.ProvinceName,\n            city: def.CityName,\n            district: def.ExpAreaName,\n            province_id: def.province_id,\n            city_id: def.city_id,\n            district_id: def.district_id,\n            address: def.Address,\n        }\n        return this.success({\n            orderInfo: data,\n            receiver: receiveInfo,\n            sender: senderInfo\n        });\n    }\n    async getAllRegionAction() { // 我写的算法\n        const model = this.model('region');\n        const aData = await model.where({\n            type: 1\n        }).select();\n        const bData = await model.where({\n            type: 2\n        }).select();\n        const cData = await model.where({\n            type: 3\n        }).select();\n        let newData = [];\n        for (const item of aData) {\n            let children = [];\n            for (const bitem of bData) {\n                let innerChildren = [];\n                for (const citem of cData) {\n                    if (citem.parent_id == bitem.id) {\n                        innerChildren.push({\n                            value: citem.id,\n                            label: citem.name\n                        })\n                    }\n                }\n                if (bitem.parent_id == item.id) {\n                    children.push({\n                        value: bitem.id,\n                        label: bitem.name,\n                        children: innerChildren\n                    })\n                }\n            }\n            newData.push({\n                value: item.id,\n                label: item.name,\n                children: children\n            });\n        }\n        return this.success(newData);\n    }\n    async orderpackAction() {\n        const id = this.get('orderId');\n        const model = this.model('order');\n        const data = await model.where({\n            id: id\n        }).update({\n            order_status: 300\n        });\n    }\n    async orderReceiveAction() {\n        const id = this.get('orderId');\n        let currentTime = parseInt(new Date().getTime() / 1000);\n        const model = this.model('order');\n        const data = await model.where({\n            id: id\n        }).update({\n            order_status: 302,\n            shipping_time: currentTime\n        });\n    }\n    async orderPriceAction() {\n        const id = this.get('orderId');\n        const goodsPrice = this.get('goodsPrice');\n        const freightPrice = this.get('freightPrice');\n        const actualPrice = this.get('actualPrice');\n        const model = this.model('order');\n        const data = await model.where({\n            id: id\n        }).find();\n        let newData = {\n            actual_price: actualPrice,\n            freight_price: freightPrice,\n            goods_price: goodsPrice,\n            order_sn: model.generateOrderNumber()\n        }\n        await model.where({\n            id: id\n        }).update(newData);\n    }\n    async getOrderExpressAction() {\n        const orderId = this.post('orderId');\n        const latestExpressInfo = await this.model('order_express').getLatestOrderExpressByAli(orderId);\n        return this.success(latestExpressInfo);\n    }\n    async getPrintTestAction() {\n        const latestExpressInfo = await this.model('order_express').printExpress();\n        return this.success(latestExpressInfo);\n    }\n    async getMianExpressAction() {\n        const orderId = this.post('orderId');\n        const sender = this.post('sender');\n        const receiver = this.post('receiver');\n        console.log(orderId);\n        console.log(sender);\n        console.log(receiver);\n        let senderOptions = sender.senderOptions;\n        let receiveOptions = receiver.receiveOptions;\n        let senderInfo = {\n            Name: sender.name,\n            Tel: sender.mobile,\n            ProvinceName: await this.model('region').where({\n                id: senderOptions[0]\n            }).getField('name', true),\n            CityName: await this.model('region').where({\n                id: senderOptions[1]\n            }).getField('name', true),\n            ExpAreaName: await this.model('region').where({\n                id: senderOptions[2]\n            }).getField('name', true),\n            Address: sender.address\n        };\n        let receiverInfo = {\n            Name: receiver.name,\n            Tel: receiver.mobile,\n            ProvinceName: await this.model('region').where({\n                id: receiveOptions[0]\n            }).getField('name', true),\n            CityName: await this.model('region').where({\n                id: receiveOptions[1]\n            }).getField('name', true),\n            ExpAreaName: await this.model('region').where({\n                id: receiveOptions[2]\n            }).getField('name', true),\n            Address: receiver.address\n        };\n        // 每次重新生成一次订单号，这样，不会出现已经下过单的情况了。\n        const expressType = this.post('expressType');\n        const latestExpressInfo = await this.model('order_express').getMianExpress(orderId, senderInfo, receiverInfo, expressType);\n        console.log('lastExpressInfo++++++++++++++++++++++');\n        console.log(latestExpressInfo);\n        if (latestExpressInfo.ResultCode == 100) {\n            // 获取快递单号成功，然后存入order_express中\n            this.orderExpressAdd(latestExpressInfo, orderId)\n        }\n        return this.success({\n            latestExpressInfo: latestExpressInfo,\n            sender: senderInfo,\n            receiver: receiverInfo\n        });\n    }\n    async rePrintExpressAction() {\n        const date = new Date();\n        let orderId = this.get('orderId')\n        let order_sn = date.getFullYear() + _.padStart(date.getMonth(), 2, '0') + _.padStart(date.getDay(), 2, '0') + _.padStart(date.getHours(), 2, '0') + _.padStart(date.getMinutes(), 2, '0') + _.padStart(date.getSeconds(), 2, '0') + _.random(100000, 999999);\n        let info = await this.model('order').where({\n            id: orderId\n        }).update({\n            order_sn: order_sn\n        });\n        return this.success(info);\n    }\n    async directPrintExpressAction() {\n        let orderId = this.get('orderId')\n        let express = await this.model('order_express').where({\n            order_id: orderId\n        }).find();\n        let info = {};\n        if (express.express_type < 4) {\n            info = await this.model('shipper').where({\n                code: 'SF'\n            }).find();\n        } else {\n            info = await this.model('shipper').where({\n                code: 'YTO'\n            }).find();\n        }\n        express.MonthCode = info.MonthCode;\n        express.send_time = moment.unix(express.add_time).format('YYYY-MM-DD');\n        return this.success(express);\n    }\n    async orderExpressAdd(ele, orderId) {\n        let currentTime = parseInt(new Date().getTime() / 1000);\n        let info = await this.model('order_express').where({\n            order_id: orderId\n        }).find();\n        if (think.isEmpty(info)) {\n            let orderInfo = ele.Order;\n            let ShipperCode = orderInfo.ShipperCode;\n            let logistic_code = orderInfo.LogisticCode;\n            let expressType = ele.expressType;\n            let region_code = orderInfo.DestinatioCode;\n            if (expressType == 4) {\n                region_code = orderInfo.MarkDestination;\n            }\n            const model = this.model('order');\n            let kdInfo = await this.model('shipper').where({\n                code: ShipperCode\n            }).find();\n            let kdData = {\n                order_id: orderId,\n                shipper_id: kdInfo.id,\n                shipper_name: kdInfo.name,\n                shipper_code: ShipperCode,\n                logistic_code: logistic_code,\n                region_code: region_code,\n                express_type: expressType,\n                add_time: currentTime\n            };\n            await this.model('order_express').add(kdData);\n        } else {\n            let orderInfo = ele.Order;\n            await this.model('order_express').where({\n                order_id: orderId\n            }).update({\n                logistic_code: orderInfo.LogisticCode\n            });\n        }\n        // 如果生成快递单号了。然后又最后没有使用，又去生成快递单号，那么应该重新生成下订单号，用新订单号去生成快递单号，然后update掉旧的order_express\n    }\n    // 点击打印并发货按钮后，就将订单的状态改成已发货\n    async goDeliveryAction() {\n        let orderId = this.post('order_id');\n        let currentTime = parseInt(new Date().getTime() / 1000);\n        let updateData = {\n            order_status: 301,\n            print_status: 1,\n            shipping_status: 1,\n            shipping_time: currentTime\n        };\n        let data = await this.model('order').where({\n            id: orderId\n        }).update(updateData);\n\n        // 获取订单详细信息\n        let orderInfo = await this.model('order').where({\n            id: orderId\n        }).find();\n        let user = await this.model('user').where({\n            id: orderInfo.user_id\n        }).find();\n        let openId = user.weixin_openid;\n\n        // 获取商品信息\n        let goodsInfo = await this.model('order_goods').where({\n            order_id: orderId\n        }).field('goods_name,number').select();\n        let express = await this.model('order_express').where({\n            order_id: orderId\n        }).find();\n\n        // 物品名称\n        let goodsName = '';\n        if (goodsInfo.length == 1) {\n            goodsName = goodsInfo[0].goods_name\n        } else {\n            goodsName = goodsInfo[0].goods_name + '等' + goodsInfo.length + '件商品'\n        }\n\n        // 支付时间\n        let shippingTime = moment.unix(currentTime).format('YYYY-MM-DD HH:mm:ss');\n\n        // 发送订阅消息\n        let TEMPLATE_ID = think.config('templateId.deliveryId');\n        let message = {\n            \"touser\": openId,\n            \"template_id\": TEMPLATE_ID,\n            \"page\": '/pages/ucenter/index/index',\n            \"miniprogram_state\":\"formal\",\n            \"lang\":\"zh_CN\",\n            \"data\": {\n              \"thing7\": {\n                  \"value\": goodsName\n              },\n              \"date2\": {\n                  \"value\": shippingTime\n              },\n              \"name3\": {\n                  \"value\": express.shipper_name\n              },\n              \"character_string4\": {\n                  \"value\": express.logistic_code\n              } ,\n              \"thing9\": {\n                  \"value\": '签收前请检查包裹！'\n              }\n          }\n        }\n\n        const tokenServer = think.service('weixin', 'api');\n        const token = await tokenServer.getAccessToken();\n        const res = await tokenServer.sendMessage(token,message);\n\n        // 调用微信订单发货管理接口\n        await this.syncWeixinShippingInfo(orderInfo, express, goodsInfo, openId, 1);\n\n        return this.success();\n    }\n    async goPrintOnlyAction() {\n        let orderId = this.post('order_id');\n        let updateData = {\n            print_status: 1\n        };\n        let data = await this.model('order').where({\n            id: orderId\n        }).update(updateData);\n        return this.success(data);\n    }\n    async orderDeliveryAction() { // 发货api\n        const orderId = this.get('orderId');\n        const method = this.get('method');\n        const deliveryId = this.get('shipper') || 0;\n        const logistic_code = this.get('logistic_code') || 0;\n        let currentTime = parseInt(new Date().getTime() / 1000);\n        let expressName = '';\n        let expressInfo = null;\n\n        if (method == 2) { // 快递发货\n            let ele = await this.model('order_express').where({\n                order_id: orderId\n            }).find();\n            if (think.isEmpty(ele)) {\n                let kdInfo = await this.model('shipper').where({\n                    id: deliveryId\n                }).find();\n                expressName = kdInfo.name;\n                let kdData = {\n                    order_id: orderId,\n                    shipper_id: deliveryId,\n                    shipper_name: kdInfo.name,\n                    shipper_code: kdInfo.code,\n                    logistic_code: logistic_code,\n                    add_time: currentTime\n                };\n                await this.model('order_express').add(kdData);\n                expressInfo = kdData;\n                let updateData = {\n                    order_status: 301,\n                    shipping_status: 1,\n                    shipping_time: currentTime\n                };\n                await this.model('order').where({\n                    id: orderId\n                }).update(updateData);\n            } else {\n                let kdInfo = await this.model('shipper').where({\n                    id: deliveryId\n                }).find();\n                expressName = kdInfo.name;\n                let kdData = {\n                    order_id: orderId,\n                    shipper_id: deliveryId,\n                    shipper_name: kdInfo.name,\n                    shipper_code: kdInfo.code,\n                    logistic_code: logistic_code,\n                    add_time: currentTime\n                }\n                await this.model('order_express').where({\n                    order_id: orderId\n                }).update(kdData);\n                expressInfo = kdData;\n            }\n        } else if (method == 3) { // 自提\n            let updateData = {\n                order_status: 301,\n                shipping_time: currentTime\n            };\n            await this.model('order').where({\n                id: orderId\n            }).update(updateData);\n            expressName = '自提件';\n        }\n\n        // 发送订阅消息\n        await this.deliveryMessage(method,orderId,expressName,logistic_code);\n\n        // 获取订单信息并同步微信发货接口\n        let orderInfo = await this.model('order').where({\n            id: orderId\n        }).find();\n        let user = await this.model('user').where({\n            id: orderInfo.user_id\n        }).find();\n        let goodsInfo = await this.model('order_goods').where({\n            order_id: orderId\n        }).field('goods_name,number').select();\n\n        // 确定物流类型：1-快递 2-同城 3-虚拟 4-自提\n        let logisticsType = method == 2 ? 1 : (method == 3 ? 4 : 1);\n\n        // 调用微信订单发货管理接口\n        await this.syncWeixinShippingInfo(orderInfo, expressInfo, goodsInfo, user.weixin_openid, logisticsType);\n\n        return this.success();\n    }\n\tasync deliveryMessage(method,orderId,expressName,logistic_code){\n\t\tlet orderInfo = await this.model('order').where({\n\t\t    id: orderId\n\t\t}).field('user_id').find();\n\t\tlet user = await this.model('user').where({\n\t\t    id: orderInfo.user_id\n\t\t}).field('weixin_openid').find();\n\t\tlet openId = user.weixin_openid;\n\t\t// 物品名称\n\t\t// 快递单号\n\t\t// 快递公司\n\t\t// 发货时间\n\t\t// 温馨提示\n\t\tlet goodsInfo = await this.model('order_goods').where({\n\t\t    order_id: orderId\n\t\t}).field('goods_name').select();\n\t\t// 物品名称\n\t\tlet goodsName = '';\n\t\tif (goodsInfo.length == 1) {\n\t\t    goodsName = goodsInfo[0].goods_name\n\t\t} else {\n\t\t    goodsName = goodsInfo[0].goods_name + '等' + goodsInfo.length + '件商品'\n\t\t}\n\t\t// 支付时间\n\t\tlet currentTime = parseInt(new Date().getTime() / 1000);\n\t\tlet shippingTime = moment.unix(currentTime).format('YYYY-MM-DD HH:mm:ss');\n\t\t// 订单金额\n\t\t// 订阅消息 请先在微信小程序的官方后台设置好订阅消息模板，然后根据自己的data的字段信息，设置好data\n\t\tlet TEMPLATE_ID = think.config('templateId.deliveryId');\n\t\tlet message = {\n\t\t    \"touser\": openId,\n\t\t    \"template_id\": TEMPLATE_ID,\n\t\t    \"page\": '/pages/ucenter/index/index',\n\t\t    \"miniprogram_state\":\"formal\",\n\t\t    \"lang\":\"zh_CN\",\n\t\t    \"data\": {\n\t\t      \"thing7\": {\n\t\t          \"value\": goodsName\n\t\t      },\n\t\t      \"date2\": {\n\t\t          \"value\": shippingTime\n\t\t      },\n\t\t      \"name3\": {\n\t\t          \"value\": expressName\n\t\t      },\n\t\t      \"character_string4\": {\n\t\t          \"value\": logistic_code\n\t\t      } ,\n\t\t      \"thing9\": {\n\t\t          \"value\": '签收前请检查包裹！'\n\t\t      }\n\t\t  }\n\t\t}\n\t\tconst tokenServer = think.service('weixin', 'api');\n\t\tconst token = await tokenServer.getAccessToken();\n\t\tconst res = await tokenServer.sendMessage(token,message);\n\t}\n    async checkExpressAction() {\n        const id = this.get('orderId');\n        let info = await this.model('order_express').where({\n            order_id: id\n        }).find();\n        if (!think.isEmpty(info)) {\n            return this.success(info);\n        } else {\n            return this.fail(100, '没找到');\n        }\n    }\n    async saveAddressAction() {\n        const sn = this.post('order_sn');\n        const name = this.post('name');\n        const mobile = this.post('mobile');\n        const cAddress = this.post('cAddress');\n        const addOptions = this.post('addOptions');\n        const province = addOptions[0];\n        const city = addOptions[1];\n        const district = addOptions[2];\n        let info = {\n            consignee: name,\n            mobile: mobile,\n            address: cAddress,\n            province: province,\n            city: city,\n            district: district\n        }\n        const model = this.model('order');\n        const data = await model.where({\n            order_sn: sn\n        }).update(info);\n        return this.success(data);\n    }\n    async storeAction() {\n        if (!this.isPost) {\n            return false;\n        }\n        const values = this.post();\n        const id = this.post('id');\n        const model = this.model('order');\n        values.is_show = values.is_show ? 1 : 0;\n        values.is_new = values.is_new ? 1 : 0;\n        if (id > 0) {\n            await model.where({\n                id: id\n            }).update(values);\n        } else {\n            delete values.id;\n            await model.add(values);\n        }\n        return this.success(values);\n    }\n    async changeStatusAction() {\n        const orderSn = this.post('orderSn');\n        const value = this.post('status');\n        const info = await this.model('order').where({\n            order_sn: orderSn\n        }).update({\n            order_status: value\n        });\n        return this.success(info);\n    }\n    async destoryAction() {\n        const id = this.post('id');\n        await this.model('order').where({\n            id: id\n        }).limit(1).delete();\n        // 删除订单商品\n        await this.model('order_goods').where({\n            order_id: id\n        }).delete();\n        // TODO 事务，验证订单是否可删除（只有失效的订单才可以删除）\n        return this.success();\n    }\n    async getGoodsSpecificationAction() {\n        const goods_id = this.post('goods_id');\n        let data = await this.model('goods_specification').where({\n            goods_id: goods_id,\n            is_delete: 0\n        }).field('id,value').select();\n        return this.success(data);\n    }\n\n    /**\n     * 同步微信订单发货信息\n     * @param {Object} orderInfo 订单信息\n     * @param {Object} expressInfo 快递信息\n     * @param {Array} goodsInfo 商品信息\n     * @param {string} openid 用户openid\n     * @param {number} logisticsType 物流类型\n     */\n    async syncWeixinShippingInfo(orderInfo, expressInfo, goodsInfo, openid, logisticsType = 1) {\n        try {\n            // 检查是否启用订单发货管理\n            const shippingConfig = think.config('weixin.shipping_management');\n            if (!shippingConfig || !shippingConfig.enabled) {\n                console.log('订单发货管理功能未启用，跳过同步');\n                return { success: false, reason: 'disabled' };\n            }\n\n            console.log('=== 开始同步微信订单发货信息 ===');\n            console.log('订单ID:', orderInfo.id, '订单号:', orderInfo.order_sn);\n            console.log('物流类型:', logisticsType, '快递信息:', expressInfo ? '有' : '无');\n\n            // 数据验证\n            if (!openid) {\n                console.error('用户openid为空，跳过微信发货同步');\n                return { success: false, reason: 'invalid_openid' };\n            }\n\n            if (!orderInfo || !orderInfo.order_sn) {\n                console.error('订单信息不完整，跳过微信发货同步');\n                return { success: false, reason: 'invalid_order' };\n            }\n\n            const weixinService = think.service('weixin', 'api');\n\n            // 构建发货数据（包含数据验证）\n            let shippingData;\n            try {\n                shippingData = weixinService.buildShippingData(\n                    {\n                        order_sn: orderInfo.order_sn,\n                        goods_list: goodsInfo\n                    },\n                    expressInfo,\n                    openid,\n                    logisticsType\n                );\n            } catch (validationError) {\n                console.error('发货数据验证失败:', validationError.message);\n\n                // 记录验证失败日志\n                try {\n                    const errorInfo = JSON.stringify({\n                        type: 'validation_error',\n                        message: validationError.message\n                    });\n\n                    // 先查询当前状态，避免重复更新\n                    const currentOrder = await this.model('order').where({\n                        id: orderInfo.id\n                    }).field('weixin_shipping_sync').find();\n\n                    // 只有状态不同时才更新\n                    if (think.isEmpty(currentOrder) || currentOrder.weixin_shipping_sync !== 0) {\n                        await this.model('order').where({\n                            id: orderInfo.id\n                        }).update({\n                            weixin_shipping_sync: 0,\n                            weixin_shipping_sync_error: errorInfo\n                        });\n                        console.log('数据库验证失败状态已更新');\n                    } else {\n                        console.log('数据库验证失败状态无需更新');\n                    }\n                } catch (dbError) {\n                    console.error('更新数据库验证失败状态失败:', dbError.message);\n                }\n\n                return { success: false, reason: 'validation_failed', error: validationError.message };\n            }\n\n            console.log('发货数据构建完成:', JSON.stringify(shippingData, null, 2));\n\n            // 调用微信发货接口\n            const result = await weixinService.uploadShippingInfo(shippingData);\n\n            if (result.success) {\n                console.log('微信发货信息同步成功');\n\n                // 记录同步成功日志到数据库\n                try {\n                    // 先查询当前状态，避免重复更新\n                    const currentOrder = await this.model('order').where({\n                        id: orderInfo.id\n                    }).field('weixin_shipping_sync,weixin_shipping_sync_time').find();\n\n                    // 只有状态不同时才更新\n                    if (think.isEmpty(currentOrder) || currentOrder.weixin_shipping_sync !== 1) {\n                        await this.model('order').where({\n                            id: orderInfo.id\n                        }).update({\n                            weixin_shipping_sync: 1,\n                            weixin_shipping_sync_time: parseInt(new Date().getTime() / 1000),\n                            weixin_shipping_sync_error: null\n                        });\n                        console.log('数据库同步状态已更新');\n                    } else {\n                        console.log('数据库同步状态无需更新');\n                    }\n                } catch (dbError) {\n                    console.error('更新数据库同步状态失败:', dbError.message);\n                    // 数据库更新失败不影响接口调用成功的结果\n                }\n\n                return { success: true };\n\n            } else {\n                console.error('微信发货信息同步失败:', result.error);\n\n                // 记录失败日志\n                try {\n                    await this.model('order').where({\n                        id: orderInfo.id\n                    }).update({\n                        weixin_shipping_sync: 0,\n                        weixin_shipping_sync_error: JSON.stringify(result.error)\n                    });\n                } catch (dbError) {\n                    console.error('更新数据库失败状态失败:', dbError.message);\n                }\n\n                return { success: false, reason: 'api_error', error: result.error };\n            }\n\n        } catch (error) {\n            console.error('微信发货信息同步异常:', error);\n\n            // 记录异常日志\n            try {\n                // 先查询当前状态，避免重复更新\n                const currentOrder = await this.model('order').where({\n                    id: orderInfo.id\n                }).field('weixin_shipping_sync').find();\n\n                const errorInfo = JSON.stringify({\n                    type: 'exception',\n                    message: error.message,\n                    stack: error.stack\n                });\n\n                // 只有状态不同时才更新\n                if (think.isEmpty(currentOrder) || currentOrder.weixin_shipping_sync !== 0) {\n                    await this.model('order').where({\n                        id: orderInfo.id\n                    }).update({\n                        weixin_shipping_sync: 0,\n                        weixin_shipping_sync_error: errorInfo\n                    });\n                    console.log('数据库异常状态已更新');\n                } else {\n                    console.log('数据库异常状态无需更新');\n                }\n            } catch (dbError) {\n                console.error('记录同步异常日志失败:', dbError.message);\n            }\n\n            return { success: false, reason: 'exception', error: error.message };\n        }\n    }\n\n    /**\n     * 重新同步微信发货信息（手动重试接口）\n     */\n    async resyncWeixinShippingAction() {\n        const orderId = this.post('orderId');\n\n        try {\n            // 获取订单信息\n            let orderInfo = await this.model('order').where({\n                id: orderId\n            }).find();\n\n            if (think.isEmpty(orderInfo)) {\n                return this.fail('订单不存在');\n            }\n\n            if (orderInfo.order_status !== 301) {\n                return this.fail('订单未发货，无法同步');\n            }\n\n            // 获取用户信息\n            let user = await this.model('user').where({\n                id: orderInfo.user_id\n            }).find();\n\n            // 获取快递信息\n            let expressInfo = await this.model('order_express').where({\n                order_id: orderId\n            }).find();\n\n            // 获取商品信息\n            let goodsInfo = await this.model('order_goods').where({\n                order_id: orderId\n            }).field('goods_name,number').select();\n\n            // 确定物流类型\n            let logisticsType = think.isEmpty(expressInfo) ? 4 : 1; // 有快递信息为快递，否则为自提\n\n            // 重新同步\n            await this.syncWeixinShippingInfo(orderInfo, expressInfo, goodsInfo, user.weixin_openid, logisticsType);\n\n            return this.success('重新同步完成');\n\n        } catch (error) {\n            console.error('重新同步微信发货信息失败:', error);\n            return this.fail('重新同步失败');\n        }\n    }\n};"]}