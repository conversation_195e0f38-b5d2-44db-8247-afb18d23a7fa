{"version": 3, "sources": ["..\\..\\..\\src\\common\\model\\flash_sale_rounds.js"], "names": ["module", "exports", "think", "Model", "getRoundInfo", "roundId", "round", "where", "id", "find", "isEmpty", "campaignModel", "model", "campaign", "campaign_id", "goodsModel", "goods", "goods_id", "canPurchase", "quantity", "reason", "status", "remainingStock", "stock", "sold_count", "decreaseStock", "result", "query", "tablePrefix", "affectedRows", "Error", "error", "console", "getActiveRounds", "now", "Date", "nowStr", "toISOString", "slice", "replace", "start_time", "end_time", "select", "getUpcomingRounds", "minutes", "future", "getTime", "futureStr", "order", "updateStatus", "update", "endExpiredRounds"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;;AAEzC;;;AAGMC,cAAN,CAAmBC,OAAnB,EAA4B;AAAA;;AAAA;AAC1B,YAAMC,QAAQ,MAAM,MAAKC,KAAL,CAAW,EAAEC,IAAIH,OAAN,EAAX,EAA4BI,IAA5B,EAApB;AACA,UAAIP,MAAMQ,OAAN,CAAcJ,KAAd,CAAJ,EAA0B;AACxB,eAAO,IAAP;AACD;;AAED;AACA,YAAMK,gBAAgB,MAAKC,KAAL,CAAW,sBAAX,CAAtB;AACA,YAAMC,WAAW,MAAMF,cAAcJ,KAAd,CAAoB,EAAEC,IAAIF,MAAMQ,WAAZ,EAApB,EAA+CL,IAA/C,EAAvB;;AAEA;AACA,YAAMM,aAAa,MAAKH,KAAL,CAAW,OAAX,CAAnB;AACA,YAAMI,QAAQ,MAAMD,WAAWR,KAAX,CAAiB,EAAEC,IAAIK,SAASI,QAAf,EAAjB,EAA4CR,IAA5C,EAApB;;AAEA,+BACKH,KADL;AAEEO,kBAAUA,QAFZ;AAGEG,eAAOA;AAHT;AAd0B;AAmB3B;;AAED;;;AAGME,aAAN,CAAkBb,OAAlB,EAA2Bc,WAAW,CAAtC,EAAyC;AAAA;;AAAA;AACvC,YAAMb,QAAQ,MAAM,OAAKC,KAAL,CAAW,EAAEC,IAAIH,OAAN,EAAX,EAA4BI,IAA5B,EAApB;AACA,UAAIP,MAAMQ,OAAN,CAAcJ,KAAd,CAAJ,EAA0B;AACxB,eAAO,EAAEY,aAAa,KAAf,EAAsBE,QAAQ,OAA9B,EAAP;AACD;;AAED,UAAId,MAAMe,MAAN,KAAiB,QAArB,EAA+B;AAC7B,eAAO,EAAEH,aAAa,KAAf,EAAsBE,QAAQ,WAA9B,EAAP;AACD;;AAED,YAAME,iBAAiBhB,MAAMiB,KAAN,GAAcjB,MAAMkB,UAA3C;AACA,UAAIF,iBAAiBH,QAArB,EAA+B;AAC7B,eAAO,EAAED,aAAa,KAAf,EAAsBE,QAAQ,MAA9B,EAAP;AACD;;AAED,aAAO,EAAEF,aAAa,IAAf,EAAqBI,cAArB,EAAP;AAfuC;AAgBxC;;AAED;;;AAGMG,eAAN,CAAoBpB,OAApB,EAA6Bc,QAA7B,EAAuC;AAAA;;AAAA;AACrC,UAAI;AACF;AACA,cAAMO,SAAS,MAAM,OAAKC,KAAL,CAAY;iBACtB,OAAKC,WAAY;wCACMT,QAAS;qBAC5Bd,OAAQ;sCACSc,QAAS;;OAJpB,CAArB;;AAQA,YAAIO,OAAOG,YAAP,KAAwB,CAA5B,EAA+B;AAC7B,gBAAM,IAAIC,KAAJ,CAAU,aAAV,CAAN;AACD;;AAED,eAAO,IAAP;AACD,OAfD,CAeE,OAAOC,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,QAAd,EAAwBA,KAAxB;AACA,cAAMA,KAAN;AACD;AAnBoC;AAoBtC;;AAED;;;AAGME,iBAAN,GAAwB;AAAA;;AAAA;AACtB,YAAMC,MAAM,IAAIC,IAAJ,EAAZ;AACA,YAAMC,SAASF,IAAIG,WAAJ,GAAkBC,KAAlB,CAAwB,CAAxB,EAA2B,EAA3B,EAA+BC,OAA/B,CAAuC,GAAvC,EAA4C,GAA5C,CAAf;;AAEA,aAAO,MAAM,OAAKhC,KAAL,CAAW;AACtBiC,oBAAY,CAAC,IAAD,EAAOJ,MAAP,CADU;AAEtBK,kBAAU,CAAC,GAAD,EAAML,MAAN,CAFY;AAGtBf,gBAAQ;AAHc,OAAX,EAIVqB,MAJU,EAAb;AAJsB;AASvB;;AAED;;;AAGMC,mBAAN,CAAwBC,UAAU,EAAlC,EAAsC;AAAA;;AAAA;AACpC,YAAMV,MAAM,IAAIC,IAAJ,EAAZ;AACA,YAAMU,SAAS,IAAIV,IAAJ,CAASD,IAAIY,OAAJ,KAAgBF,UAAU,KAAnC,CAAf;;AAEA,YAAMR,SAASF,IAAIG,WAAJ,GAAkBC,KAAlB,CAAwB,CAAxB,EAA2B,EAA3B,EAA+BC,OAA/B,CAAuC,GAAvC,EAA4C,GAA5C,CAAf;AACA,YAAMQ,YAAYF,OAAOR,WAAP,GAAqBC,KAArB,CAA2B,CAA3B,EAA8B,EAA9B,EAAkCC,OAAlC,CAA0C,GAA1C,EAA+C,GAA/C,CAAlB;;AAEA,aAAO,MAAM,OAAKhC,KAAL,CAAW;AACtBiC,oBAAY,CAAC,SAAD,EAAY,CAACJ,MAAD,EAASW,SAAT,CAAZ,CADU;AAEtB1B,gBAAQ;AAFc,OAAX,EAGV2B,KAHU,CAGJ,gBAHI,EAGcN,MAHd,EAAb;AAPoC;AAWrC;;AAED;;;AAGMO,cAAN,CAAmB5C,OAAnB,EAA4BgB,MAA5B,EAAoC;AAAA;;AAAA;AAClC,aAAO,MAAM,OAAKd,KAAL,CAAW,EAAEC,IAAIH,OAAN,EAAX,EAA4B6C,MAA5B,CAAmC,EAAE7B,MAAF,EAAnC,CAAb;AADkC;AAEnC;;AAED;;;AAGM8B,kBAAN,GAAyB;AAAA;;AAAA;AACvB,YAAMjB,MAAM,IAAIC,IAAJ,EAAZ;AACA,YAAMC,SAASF,IAAIG,WAAJ,GAAkBC,KAAlB,CAAwB,CAAxB,EAA2B,EAA3B,EAA+BC,OAA/B,CAAuC,GAAvC,EAA4C,GAA5C,CAAf;;AAEA,aAAO,MAAM,OAAKhC,KAAL,CAAW;AACtBkC,kBAAU,CAAC,GAAD,EAAML,MAAN,CADY;AAEtBf,gBAAQ,CAAC,IAAD,EAAO,CAAC,UAAD,EAAa,QAAb,CAAP;AAFc,OAAX,EAGV6B,MAHU,CAGH,EAAE7B,QAAQ,OAAV,EAHG,CAAb;AAJuB;AAQxB;AAxHwC,CAA3C", "file": "..\\..\\..\\src\\common\\model\\flash_sale_rounds.js", "sourcesContent": ["module.exports = class extends think.Model {\n\n  /**\n   * 获取轮次详细信息\n   */\n  async getRoundInfo(roundId) {\n    const round = await this.where({ id: roundId }).find();\n    if (think.isEmpty(round)) {\n      return null;\n    }\n    \n    // 获取活动信息\n    const campaignModel = this.model('flash_sale_campaigns');\n    const campaign = await campaignModel.where({ id: round.campaign_id }).find();\n    \n    // 获取商品信息\n    const goodsModel = this.model('goods');\n    const goods = await goodsModel.where({ id: campaign.goods_id }).find();\n    \n    return {\n      ...round,\n      campaign: campaign,\n      goods: goods\n    };\n  }\n  \n  /**\n   * 检查轮次是否可以购买\n   */\n  async canPurchase(roundId, quantity = 1) {\n    const round = await this.where({ id: roundId }).find();\n    if (think.isEmpty(round)) {\n      return { canPurchase: false, reason: '轮次不存在' };\n    }\n    \n    if (round.status !== 'active') {\n      return { canPurchase: false, reason: '轮次未开始或已结束' };\n    }\n    \n    const remainingStock = round.stock - round.sold_count;\n    if (remainingStock < quantity) {\n      return { canPurchase: false, reason: '库存不足' };\n    }\n    \n    return { canPurchase: true, remainingStock };\n  }\n  \n  /**\n   * 原子性减库存操作\n   */\n  async decreaseStock(roundId, quantity) {\n    try {\n      // 使用数据库级别的原子操作\n      const result = await this.query(`\n        UPDATE ${this.tablePrefix}flash_sale_rounds \n        SET sold_count = sold_count + ${quantity}\n        WHERE id = ${roundId} \n        AND (stock - sold_count) >= ${quantity}\n        AND status = 'active'\n      `);\n      \n      if (result.affectedRows === 0) {\n        throw new Error('库存不足或轮次状态异常');\n      }\n      \n      return true;\n    } catch (error) {\n      console.error('减库存失败:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * 获取当前活跃的轮次\n   */\n  async getActiveRounds() {\n    const now = new Date();\n    const nowStr = now.toISOString().slice(0, 19).replace('T', ' ');\n    \n    return await this.where({\n      start_time: ['<=', nowStr],\n      end_time: ['>', nowStr],\n      status: 'active'\n    }).select();\n  }\n  \n  /**\n   * 获取即将开始的轮次\n   */\n  async getUpcomingRounds(minutes = 30) {\n    const now = new Date();\n    const future = new Date(now.getTime() + minutes * 60000);\n    \n    const nowStr = now.toISOString().slice(0, 19).replace('T', ' ');\n    const futureStr = future.toISOString().slice(0, 19).replace('T', ' ');\n    \n    return await this.where({\n      start_time: ['BETWEEN', [nowStr, futureStr]],\n      status: 'upcoming'\n    }).order('start_time ASC').select();\n  }\n  \n  /**\n   * 更新轮次状态\n   */\n  async updateStatus(roundId, status) {\n    return await this.where({ id: roundId }).update({ status });\n  }\n  \n  /**\n   * 批量更新过期轮次状态\n   */\n  async endExpiredRounds() {\n    const now = new Date();\n    const nowStr = now.toISOString().slice(0, 19).replace('T', ' ');\n    \n    return await this.where({\n      end_time: ['<', nowStr],\n      status: ['IN', ['upcoming', 'active']]\n    }).update({ status: 'ended' });\n  }\n};\n"]}