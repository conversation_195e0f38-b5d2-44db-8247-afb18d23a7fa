{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\shipper.js"], "names": ["Base", "require", "module", "exports", "indexAction", "model", "info", "where", "enabled", "select", "set", "id", "find", "data", "success", "listAction", "page", "get", "size", "name", "order", "countSelect", "enabledStatusAction", "status", "sale", "update", "updateSortAction", "post", "sort", "sort_order", "infoAction", "storeAction", "isPost", "values", "add", "destoryAction", "limit", "delete", "freightAction", "is_delete", "freightDestroyAction", "template_id", "getareadataAction", "all", "type", "field", "freightdetailAction", "area", "item", "free_by_money", "freeByMoney", "free_by_number", "freeByNumber", "areaData", "split", "getField", "areaName", "join", "defaultData", "freight", "saveTableAction", "def", "idInfo", "push", "length", "deleData", "is_default", "ele", "group_id", "dbTable", "val", "start", "start_fee", "add_fee", "arr", "e", "think", "isEmpty", "substring", "groupId", "areaArr", "upData", "tempData", "package_price", "freight_type", "addTableAction", "temp_id", "exceptareaAction", "exceptAreaDeleteAction", "except_area_id", "exceptAreaDetailAction", "saveExceptAreaAction", "table", "content", "addExceptAreaAction"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;;AAEAC,OAAOC,OAAP,GAAiB,cAAcH,IAAd,CAAmB;AACnC;;;;AAIMI,YAAN,GAAoB;AAAA;;AAAA;AACnB,SAAMC,QAAQ,MAAKA,KAAL,CAAW,SAAX,CAAd;AACA,SAAMC,OAAO,MAAMD,MAAME,KAAN,CAAY;AAC9BC,aAAS;AADqB,IAAZ,EAEhBC,MAFgB,EAAnB;AAGA,SAAMC,MAAM,MAAM,MAAKL,KAAL,CAAW,UAAX,EAAuBE,KAAvB,CAA6B;AAC9CI,QAAI;AAD0C,IAA7B,EAEfC,IAFe,EAAlB;AAGA,OAAIC,OAAO;AACVP,UAAMA,IADI;AAEVI,SAAKA;AAFK,IAAX;AAIA,UAAO,MAAKI,OAAL,CAAaD,IAAb,CAAP;AAZmB;AAanB;;AAEKE,WAAN,GAAmB;AAAA;;AAAA;AAClB,SAAMC,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,SAAMC,OAAO,OAAKD,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,SAAME,OAAO,OAAKF,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,SAAMZ,QAAQ,OAAKA,KAAL,CAAW,SAAX,CAAd;AACA,SAAMQ,OAAO,MAAMR,MAAME,KAAN,CAAY;AAC9B,iBAAa,CAAC,MAAD,EAAU,IAAGY,IAAK,GAAlB;AADiB,IAAZ,EAEhBC,KAFgB,CAEV,gBAFU,EAEQJ,IAFR,CAEaA,IAFb,EAEmBE,IAFnB,EAEyBG,WAFzB,EAAnB;AAGA,UAAO,OAAKP,OAAL,CAAaD,IAAb,CAAP;AARkB;AASlB;;AAEKS,oBAAN,GAA4B;AAAA;;AAAA;AAC3B,SAAMX,KAAK,OAAKM,GAAL,CAAS,IAAT,CAAX;AACA,SAAMM,SAAS,OAAKN,GAAL,CAAS,QAAT,CAAf;AACA,OAAIO,OAAO,CAAX;AACA,OAAID,UAAU,MAAd,EAAsB;AACrBC,WAAO,CAAP;AACA;AACD,SAAMnB,QAAQ,OAAKA,KAAL,CAAW,SAAX,CAAd;AACA,SAAMA,MAAME,KAAN,CAAY;AACjBI,QAAIA;AADa,IAAZ,EAEHc,MAFG,CAEI;AACTjB,aAASgB;AADA,IAFJ,CAAN;AAKA,UAAO,OAAKV,OAAL,EAAP;AAb2B;AAc3B;;AAEKY,iBAAN,GAAyB;AAAA;;AAAA;AACxB,SAAMf,KAAK,OAAKgB,IAAL,CAAU,IAAV,CAAX;AACA,SAAMC,OAAO,OAAKD,IAAL,CAAU,MAAV,CAAb;AACA,SAAMtB,QAAQ,OAAKA,KAAL,CAAW,SAAX,CAAd;AACA,SAAMQ,OAAO,MAAMR,MAAME,KAAN,CAAY;AAC9BI,QAAIA;AAD0B,IAAZ,EAEhBc,MAFgB,CAET;AACTI,gBAAYD;AADH,IAFS,CAAnB;AAKA,UAAO,OAAKd,OAAL,CAAaD,IAAb,CAAP;AATwB;AAUxB;;AAEKiB,WAAN,GAAmB;AAAA;;AAAA;AAClB,SAAMnB,KAAK,OAAKM,GAAL,CAAS,IAAT,CAAX;AACA,SAAMZ,QAAQ,OAAKA,KAAL,CAAW,SAAX,CAAd;AACA,SAAMQ,OAAO,MAAMR,MAAME,KAAN,CAAY;AAC9BI,QAAIA;AAD0B,IAAZ,EAEhBC,IAFgB,EAAnB;AAGA,UAAO,OAAKE,OAAL,CAAaD,IAAb,CAAP;AANkB;AAOlB;;AAEKkB,YAAN,GAAoB;AAAA;;AAAA;AACnB,OAAI,CAAC,OAAKC,MAAV,EAAkB;AACjB,WAAO,KAAP;AACA;AACD,SAAMC,SAAS,OAAKN,IAAL,EAAf;AACA,SAAMhB,KAAK,OAAKgB,IAAL,CAAU,IAAV,CAAX;;AAEA,SAAMtB,QAAQ,OAAKA,KAAL,CAAW,SAAX,CAAd;AACA,OAAIM,KAAK,CAAT,EAAY;AACX,UAAMN,MAAME,KAAN,CAAY;AACjBI,SAAIA;AADa,KAAZ,EAEHc,MAFG,CAEIQ,MAFJ,CAAN;AAGA,IAJD,MAIO;AACN,WAAOA,OAAOtB,EAAd;AACA,UAAMN,MAAM6B,GAAN,CAAUD,MAAV,CAAN;AACA;AACD,UAAO,OAAKnB,OAAL,CAAamB,MAAb,CAAP;AAhBmB;AAiBnB;;AAGKE,cAAN,GAAsB;AAAA;;AAAA;AACrB,SAAMxB,KAAK,OAAKgB,IAAL,CAAU,IAAV,CAAX;AACA,SAAM,OAAKtB,KAAL,CAAW,SAAX,EAAsBE,KAAtB,CAA4B;AACjCI,QAAIA;AAD6B,IAA5B,EAEHyB,KAFG,CAEG,CAFH,EAEMC,MAFN,EAAN;AAGA,UAAO,OAAKvB,OAAL,EAAP;AALqB;AAMrB;;AAEKwB,cAAN,GAAsB;AAAA;;AAAA;AACrB,SAAMjC,QAAQ,OAAKA,KAAL,CAAW,kBAAX,CAAd;AACA,SAAMQ,OAAO,MAAMR,MAAME,KAAN,CAAY;AAC9BgC,eAAW;AADmB,IAAZ,EAEhB9B,MAFgB,EAAnB;AAGA,UAAO,OAAKK,OAAL,CAAaD,IAAb,CAAP;AALqB;AAMrB;;AAEK2B,qBAAN,GAA6B;AAAA;;AAAA;AAC5B,SAAM7B,KAAK,OAAKgB,IAAL,CAAU,IAAV,CAAX;AACA;AACA,SAAM,OAAKtB,KAAL,CAAW,kBAAX,EAA+BE,KAA/B,CAAqC;AAC1CI,QAAIA;AADsC,IAArC,EAEHc,MAFG,CAEI;AACTc,eAAW;AADF,IAFJ,CAAN;;AAMA;AACA,SAAM,OAAKlC,KAAL,CAAW,wBAAX,EAAqCE,KAArC,CAA2C;AAChDkC,iBAAa9B;AADmC,IAA3C,EAEHc,MAFG,CAEI;AACTc,eAAW;AADF,IAFJ,CAAN;;AAMA;AACA,SAAM,OAAKlC,KAAL,CAAW,yBAAX,EAAsCE,KAAtC,CAA4C;AACjDkC,iBAAa9B;AADoC,IAA5C,EAEHc,MAFG,CAEI;AACTc,eAAW;AADF,IAFJ,CAAN;;AAMA,UAAO,OAAKzB,OAAL,EAAP;AAvB4B;AAwB5B;;AAEK4B,kBAAN,GAA0B;AAAA;;AAAA;AACzB,OAAIC,MAAM,MAAM,QAAKtC,KAAL,CAAW,QAAX,EAAqBE,KAArB,CAA2B;AAC1CqC,UAAM;AADoC,IAA3B,EAEbC,KAFa,CAEP,SAFO,EAEIpC,MAFJ,EAAhB;AAGA,UAAO,QAAKK,OAAL,CAAa6B,GAAb,CAAP;AAJyB;AAKzB;;AAGKG,oBAAN,GAA4B;AAAA;;AAAA;AAC3B,OAAInC,KAAK,QAAKgB,IAAL,CAAU,IAAV,CAAT;;AAEA,SAAMtB,QAAQ,QAAKA,KAAL,CAAW,wBAAX,CAAd;AACA,OAAIQ,OAAO,MAAMR,MAAME,KAAN,CAAY;AAC5BkC,iBAAa9B,EADe;AAE5B4B,eAAW,CAFiB;AAG5BQ,UAAM,CAAC,IAAD,EAAO,CAAP;AAHsB,IAAZ,EAIdtC,MAJc,EAAjB;;AAMA,QAAK,MAAMuC,IAAX,IAAmBnC,IAAnB,EAAyB;AACxB,QAAIkC,OAAOC,KAAKD,IAAhB;AACA,QAAIC,KAAKC,aAAL,GAAqB,CAAzB,EAA4B;AAC3BD,UAAKE,WAAL,GAAmB,KAAnB;AACA;AACD,QAAIF,KAAKG,cAAL,GAAsB,CAA1B,EAA6B;AAC5BH,UAAKI,YAAL,GAAoB,KAApB;AACA;AACD,QAAIC,WAAWN,KAAKO,KAAL,CAAW,GAAX,CAAf;AACA,QAAIhD,OAAO,MAAM,QAAKD,KAAL,CAAW,QAAX,EAAqBE,KAArB,CAA2B;AAC3CI,SAAI,CAAC,IAAD,EAAO0C,QAAP;AADuC,KAA3B,EAEdE,QAFc,CAEL,MAFK,CAAjB;AAGAP,SAAKQ,QAAL,GAAgBlD,KAAKmD,IAAL,CAAU,GAAV,CAAhB;AACA;;AAED,OAAIC,cAAc,MAAMrD,MAAME,KAAN,CAAY;AACnCkC,iBAAa9B,EADsB;AAEnCoC,UAAM,CAF6B;AAGnCR,eAAW;AAHwB,IAAZ,EAIrB9B,MAJqB,EAAxB;;AAMA,OAAIkD,UAAU,MAAM,QAAKtD,KAAL,CAAW,kBAAX,EAA+BE,KAA/B,CAAqC;AACxDI,QAAIA;AADoD,IAArC,EAEjBC,IAFiB,EAApB;;AAIA,OAAIN,OAAO;AACVqD,aAASA,OADC;AAEV9C,UAAMA,IAFI;AAGV6C,iBAAaA;AAHH,IAAX;;AAMA,UAAO,QAAK5C,OAAL,CAAaR,IAAb,CAAP;AAzC2B;AA0C3B;;AAGKsD,gBAAN,GAAwB;AAAA;;AAAA;AACvB,OAAI/C,OAAO,QAAKc,IAAL,CAAU,OAAV,CAAX;AACA,OAAIkC,MAAM,QAAKlC,IAAL,CAAU,aAAV,CAAV;AACA,OAAIrB,OAAO,QAAKqB,IAAL,CAAU,MAAV,CAAX;AACA,OAAImC,SAAS,EAAb,CAJuB,CAIN;AACjB,QAAK,MAAMd,IAAX,IAAmBnC,IAAnB,EAAyB;AACxB,QAAImC,KAAKrC,EAAL,GAAU,CAAd,EAAiB;AAChBmD,YAAOC,IAAP,CAAYf,KAAKrC,EAAjB;AACA;AACD;;AAED,OAAImD,OAAOE,MAAP,IAAiB,CAArB,EAAwB;AACvB,QAAIC,WAAW,MAAM,QAAK5D,KAAL,CAAW,wBAAX,EAAqCE,KAArC,CAA2C;AAC/DI,SAAI,CAAC,OAAD,EAAUmD,MAAV,CAD2D;AAE/DrB,kBAAanC,KAAKK,EAF6C;AAG/DuD,iBAAY,CAHmD;AAI/D3B,gBAAW;AAJoD,KAA3C,EAKlBgB,QALkB,CAKT,IALS,CAArB;;AAOA,SAAK,MAAMY,GAAX,IAAkBF,QAAlB,EAA4B;AAC3B,WAAM,QAAK5D,KAAL,CAAW,yBAAX,EAAsCE,KAAtC,CAA4C;AACjDkC,mBAAanC,KAAKK,EAD+B;AAEjDyD,gBAAUD,GAFuC;AAGjD5B,iBAAW;AAHsC,MAA5C,EAIHd,MAJG,CAII;AACTc,iBAAW;AADF,MAJJ,CAAN;AAOA;;AAED,QAAI8B,UAAU,MAAM,QAAKhE,KAAL,CAAW,wBAAX,EAAqCE,KAArC,CAA2C;AAC9DI,SAAI,CAAC,OAAD,EAAUmD,MAAV,CAD0D;AAE9DrB,kBAAanC,KAAKK,EAF4C;AAG9DuD,iBAAY,CAHkD;AAI9D3B,gBAAW;AAJmD,KAA3C,EAKjBd,MALiB,CAKV;AACTc,gBAAW;AADF,KALU,CAApB;;AASA,SAAK,MAAMS,IAAX,IAAmBnC,IAAnB,EAAyB;AACxB,SAAIF,KAAKqC,KAAKrC,EAAd,CADwB,CACN;AAClB,SAAIA,KAAK,CAAT,EAAY;;AAEX,UAAI8B,cAAcnC,KAAKK,EAAvB;;AAEA,UAAI2D,MAAM;AACTvB,aAAMC,KAAKD,IADF;AAETwB,cAAOvB,KAAKuB,KAFH;AAGTC,kBAAWxB,KAAKwB,SAHP;AAITtC,YAAKc,KAAKd,GAJD;AAKTuC,gBAASzB,KAAKyB,OALL;AAMTxB,sBAAeD,KAAKC,aANX;AAOTE,uBAAgBH,KAAKG;AAPZ,OAAV;AASA,YAAM,QAAK9C,KAAL,CAAW,wBAAX,EAAqCE,KAArC,CAA2C;AAChDI,WAAIA,EAD4C;AAEhD8B,oBAAaA,WAFmC;AAGhDF,kBAAW;AAHqC,OAA3C,EAIHd,MAJG,CAII6C,GAJJ,CAAN;;AAMA;;;AAGA,UAAIvB,OAAOC,KAAKD,IAAhB;AACA,UAAI2B,MAAM3B,KAAKO,KAAL,CAAW,GAAX,CAAV;;AAEA,YAAM,QAAKjD,KAAL,CAAW,yBAAX,EAAsCE,KAAtC,CAA4C;AACjDwC,aAAM,CAAC,OAAD,EAAU2B,GAAV,CAD2C;AAEjDjC,oBAAaA,WAFoC;AAGjD2B,iBAAUzD;AAHuC,OAA5C,EAIHc,MAJG,CAII;AACTc,kBAAW;AADF,OAJJ,CAAN;AAOA,WAAK,MAAMS,IAAX,IAAmB0B,GAAnB,EAAwB;AACvB,WAAIC,IAAI,MAAM,QAAKtE,KAAL,CAAW,yBAAX,EAAsCE,KAAtC,CAA4C;AACzDkC,qBAAaA,WAD4C;AAEzDM,cAAMC,IAFmD;AAGzDoB,kBAAUzD;AAH+C,QAA5C,EAIXC,IAJW,EAAd;AAKA,WAAIgE,MAAMC,OAAN,CAAcF,CAAd,CAAJ,EAAsB;AACrB,cAAM,QAAKtE,KAAL,CAAW,yBAAX,EAAsC6B,GAAtC,CAA0C;AAC/CO,sBAAaA,WADkC;AAE/C2B,mBAAUzD,EAFqC;AAG/CoC,eAAMC;AAHyC,SAA1C,CAAN;AAKA;AACD;AACD,MA9CD,MA8CO;AACN,UAAIP,cAAcnC,KAAKK,EAAvB;AACA,UAAIoC,OAAOC,KAAKD,IAAL,CAAU+B,SAAV,CAAoB,CAApB,CAAX;AACA,UAAIR,MAAM;AACTvB,aAAMA,IADG;AAETwB,cAAOvB,KAAKuB,KAFH;AAGTC,kBAAWxB,KAAKwB,SAHP;AAITtC,YAAKc,KAAKd,GAJD;AAKTuC,gBAASzB,KAAKyB,OALL;AAMThC,oBAAaA,WANJ;AAOTQ,sBAAeD,KAAKC,aAPX;AAQTE,uBAAgBH,KAAKG;AARZ,OAAV;AAUA,UAAI4B,UAAU,MAAM,QAAK1E,KAAL,CAAW,wBAAX,EAAqC6B,GAArC,CAAyCoC,GAAzC,CAApB;AACA,UAAIU,UAAUjC,KAAKO,KAAL,CAAW,GAAX,CAAd;AACA,WAAK,MAAMN,IAAX,IAAmBgC,OAAnB,EAA4B;AAC3B,aAAM,QAAK3E,KAAL,CAAW,yBAAX,EAAsC6B,GAAtC,CAA0C;AAC/CO,qBAAaA,WADkC;AAE/C2B,kBAAUW,OAFqC;AAG/ChC,cAAMC;AAHyC,QAA1C,CAAN;AAKA;AACD;AACD;AACD,IAnGD,MAmGO;AACN;AACA,QAAIqB,UAAU,MAAM,QAAKhE,KAAL,CAAW,wBAAX,EAAqCE,KAArC,CAA2C;AAC9DkC,kBAAanC,KAAKK,EAD4C;AAE9DuD,iBAAY,CAFkD;AAG9D3B,gBAAW;AAHmD,KAA3C,EAIjBd,MAJiB,CAIV;AACTc,gBAAW;AADF,KAJU,CAApB;AAOA;;AAEA,QAAI1B,KAAKmD,MAAL,IAAe,CAAnB,EAAsB;AACrB,UAAK,MAAMhB,IAAX,IAAmBnC,IAAnB,EAAyB;AACxB,UAAIkC,OAAOC,KAAKD,IAAL,CAAU+B,SAAV,CAAoB,CAApB,CAAX;AACA,UAAIrC,cAAcnC,KAAKK,EAAvB;AACA,UAAI2D,MAAM;AACTvB,aAAMA,IADG;AAETwB,cAAOvB,KAAKuB,KAFH;AAGTC,kBAAWxB,KAAKwB,SAHP;AAITtC,YAAKc,KAAKd,GAJD;AAKTuC,gBAASzB,KAAKyB,OALL;AAMThC,oBAAaA,WANJ;AAOTQ,sBAAeD,KAAKC,aAPX;AAQTE,uBAAgBH,KAAKG;AARZ,OAAV;AAUA,UAAI4B,UAAU,MAAM,QAAK1E,KAAL,CAAW,wBAAX,EAAqC6B,GAArC,CAAyCoC,GAAzC,CAApB;AACA;AACA,UAAIU,UAAUjC,KAAKO,KAAL,CAAW,GAAX,CAAd;AACA,WAAK,MAAMN,IAAX,IAAmBgC,OAAnB,EAA4B;AAC3B,aAAM,QAAK3E,KAAL,CAAW,yBAAX,EAAsC6B,GAAtC,CAA0C;AAC/CO,qBAAaA,WADkC;AAE/C2B,kBAAUW,OAFqC;AAG/ChC,cAAMC;AAHyC,QAA1C,CAAN;AAKA;AAED;AACD;AACD;;AAED,OAAIiC,SAAS;AACZV,WAAOV,IAAI,CAAJ,EAAOU,KADF;AAEZC,eAAWX,IAAI,CAAJ,EAAOW,SAFN;AAGZtC,SAAK2B,IAAI,CAAJ,EAAO3B,GAHA;AAIZuC,aAASZ,IAAI,CAAJ,EAAOY,OAJJ;AAKZxB,mBAAeY,IAAI,CAAJ,EAAOZ,aALV;AAMZE,oBAAgBU,IAAI,CAAJ,EAAOV;AANX,IAAb;;AASA,SAAM,QAAK9C,KAAL,CAAW,wBAAX,EAAqCE,KAArC,CAA2C;AAChDI,QAAIkD,IAAI,CAAJ,EAAOlD,EADqC;AAEhD8B,iBAAanC,KAAKK,EAF8B;AAGhDuD,gBAAY;AAHoC,IAA3C,EAIHzC,MAJG,CAIIwD,MAJJ,CAAN;;AAMA;AACA;AACA;AACA;;;AAGA,OAAIC,WAAW;AACd/D,UAAMb,KAAKa,IADG;AAEdgE,mBAAe7E,KAAK6E,aAFN;AAGdC,kBAAc9E,KAAK8E;AAHL,IAAf;;AAMA,SAAM,QAAK/E,KAAL,CAAW,kBAAX,EAA+BE,KAA/B,CAAqC;AAC1CI,QAAIL,KAAKK;AADiC,IAArC,EAEHc,MAFG,CAEIyD,QAFJ,CAAN;AAGA,UAAO,QAAKpE,OAAL,EAAP;AApLuB;AAqLvB;;AAEKuE,eAAN,GAAuB;AAAA;;AAAA;AACtB,OAAI/E,OAAO,QAAKqB,IAAL,CAAU,MAAV,CAAX;AACA,OAAId,OAAO,QAAKc,IAAL,CAAU,OAAV,CAAX;AACA,OAAIkC,MAAM,QAAKlC,IAAL,CAAU,aAAV,CAAV;AACA;AACA,OAAI2D,UAAU,MAAM,QAAKjF,KAAL,CAAW,kBAAX,EAA+B6B,GAA/B,CAAmC5B,IAAnC,CAApB;;AAEA,OAAIgF,UAAU,CAAd,EAAiB;AAChB,QAAIL,SAAS;AACZV,YAAOV,IAAI,CAAJ,EAAOU,KADF;AAEZC,gBAAWX,IAAI,CAAJ,EAAOW,SAFN;AAGZtC,UAAK2B,IAAI,CAAJ,EAAO3B,GAHA;AAIZuC,cAASZ,IAAI,CAAJ,EAAOY,OAJJ;AAKZxB,oBAAeY,IAAI,CAAJ,EAAOZ,aALV;AAMZE,qBAAgBU,IAAI,CAAJ,EAAOV,cANX;AAOZV,kBAAa6C,OAPD;AAQZpB,iBAAY;AARA,KAAb;;AAWA,QAAIa,UAAU,MAAM,QAAK1E,KAAL,CAAW,wBAAX,EAAqC6B,GAArC,CAAyC+C,MAAzC,CAApB;AACA,QAAIF,UAAU,CAAd,EAAiB;AAChB,WAAM,QAAK1E,KAAL,CAAW,yBAAX,EAAsC6B,GAAtC,CAA0C;AAC/CO,mBAAa6C,OADkC;AAE/ClB,gBAAUW,OAFqC;AAG/ChC,YAAM;AAHyC,MAA1C,CAAN;AAKA;;AAED,QAAIlC,KAAKmD,MAAL,GAAc,CAAlB,EAAqB;AACpB,UAAK,MAAMhB,IAAX,IAAmBnC,IAAnB,EAAyB;AACxB,UAAIkC,OAAOC,KAAKD,IAAL,CAAU+B,SAAV,CAAoB,CAApB,CAAX;AACA,UAAIrC,cAAc6C,OAAlB;AACA,UAAIhF,OAAO;AACVyC,aAAMA,IADI;AAEVwB,cAAOvB,KAAKuB,KAFF;AAGVC,kBAAWxB,KAAKwB,SAHN;AAIVtC,YAAKc,KAAKd,GAJA;AAKVuC,gBAASzB,KAAKyB,OALJ;AAMVhC,oBAAa6C,OANH;AAOVrC,sBAAeD,KAAKC,aAPV;AAQVE,uBAAgBH,KAAKG;AARX,OAAX;AAUA,UAAI4B,UAAU,MAAM,QAAK1E,KAAL,CAAW,wBAAX,EAAqC6B,GAArC,CAAyC5B,IAAzC,CAApB;AACA,UAAI0E,UAAUjC,KAAKO,KAAL,CAAW,GAAX,CAAd;AACA,WAAK,MAAMN,IAAX,IAAmBgC,OAAnB,EAA4B;AAC3B,aAAM,QAAK3E,KAAL,CAAW,yBAAX,EAAsC6B,GAAtC,CAA0C;AAC/CO,qBAAaA,WADkC;AAE/C2B,kBAAUW,OAFqC;AAG/ChC,cAAMC;AAHyC,QAA1C,CAAN;AAKA;AACD;AACD;AACD;;AAED,UAAO,QAAKlC,OAAL,EAAP;AAvDsB;AAwDtB;;AAEKyE,iBAAN,GAAyB;AAAA;;AAAA;AACxB,SAAMlF,QAAQ,QAAKA,KAAL,CAAW,aAAX,CAAd;AACA,SAAMQ,OAAO,MAAMR,MAAME,KAAN,CAAY;AAC9BgC,eAAW;AADmB,IAAZ,EAEhB9B,MAFgB,EAAnB;;AAIA,QAAK,MAAMuC,IAAX,IAAmBnC,IAAnB,EAAyB;AACxB,QAAIkC,OAAOC,KAAKD,IAAhB;AACA,QAAIM,WAAWN,KAAKO,KAAL,CAAW,GAAX,CAAf;AACA,QAAIhD,OAAO,MAAM,QAAKD,KAAL,CAAW,QAAX,EAAqBE,KAArB,CAA2B;AAC3CI,SAAI,CAAC,IAAD,EAAO0C,QAAP;AADuC,KAA3B,EAEdE,QAFc,CAEL,MAFK,CAAjB;AAGAP,SAAKQ,QAAL,GAAgBlD,KAAKmD,IAAL,CAAU,GAAV,CAAhB;AACA;;AAED,UAAO,QAAK3C,OAAL,CAAaD,IAAb,CAAP;AAfwB;AAgBxB;;AAEK2E,uBAAN,GAA+B;AAAA;;AAAA;AAC9B,SAAM7E,KAAK,QAAKgB,IAAL,CAAU,IAAV,CAAX;AACA,SAAM,QAAKtB,KAAL,CAAW,aAAX,EAA0BE,KAA1B,CAAgC;AACrCI,QAAIA;AADiC,IAAhC,EAEHyB,KAFG,CAEG,CAFH,EAEMX,MAFN,CAEa;AAClBc,eAAW;AADO,IAFb,CAAN;AAKA,SAAM,QAAKlC,KAAL,CAAW,oBAAX,EAAiCE,KAAjC,CAAuC;AAC5CkF,oBAAgB9E;AAD4B,IAAvC,EAEHc,MAFG,CAEI;AACTc,eAAW;AADF,IAFJ,CAAN;AAKA,UAAO,QAAKzB,OAAL,EAAP;AAZ8B;AAa9B;;AAGK4E,uBAAN,GAA+B;AAAA;;AAAA;AAC9B,OAAI/E,KAAK,QAAKgB,IAAL,CAAU,IAAV,CAAT;AACA,SAAMtB,QAAQ,QAAKA,KAAL,CAAW,aAAX,CAAd;AACA,OAAIQ,OAAO,MAAMR,MAAME,KAAN,CAAY;AAC5BI,QAAIA,EADwB;AAE5B4B,eAAW;AAFiB,IAAZ,EAGd3B,IAHc,EAAjB;AAIA;AACA,OAAImC,OAAOlC,KAAKkC,IAAhB;AACA,OAAIM,WAAWN,KAAKO,KAAL,CAAW,GAAX,CAAf;AACA,OAAIhD,OAAO,MAAM,QAAKD,KAAL,CAAW,QAAX,EAAqBE,KAArB,CAA2B;AAC3CI,QAAI,CAAC,IAAD,EAAO0C,QAAP;AADuC,IAA3B,EAEdE,QAFc,CAEL,MAFK,CAAjB;AAGA1C,QAAK2C,QAAL,GAAgBlD,KAAKmD,IAAL,CAAU,GAAV,CAAhB;AACA,UAAO,QAAK3C,OAAL,CAAaD,IAAb,CAAP;AAd8B;AAe9B;;AAEK8E,qBAAN,GAA6B;AAAA;;AAAA;AAC5B,OAAIC,QAAQ,QAAKjE,IAAL,CAAU,OAAV,CAAZ;AACA,OAAIrB,OAAO,QAAKqB,IAAL,CAAU,MAAV,CAAX;AACA,OAAId,OAAO;AACVkC,UAAM6C,MAAM,CAAN,EAAS7C,IADL;AAEV8C,aAASvF,KAAKuF;AAFJ,IAAX;AAIA,SAAM,QAAKxF,KAAL,CAAW,aAAX,EAA0BE,KAA1B,CAAgC;AACrCI,QAAIL,KAAKK;AAD4B,IAAhC,EAEHc,MAFG,CAEIZ,IAFJ,CAAN;AAGA,OAAIkC,OAAO6C,MAAM,CAAN,EAAS7C,IAApB;AACA,OAAI2B,MAAM3B,KAAKO,KAAL,CAAW,GAAX,CAAV;AACA,SAAM,QAAKjD,KAAL,CAAW,oBAAX,EAAiCE,KAAjC,CAAuC;AAC5CwC,UAAM,CAAC,OAAD,EAAU2B,GAAV,CADsC;AAE5Ce,oBAAgBnF,KAAKK,EAFuB;AAG5C4B,eAAW;AAHiC,IAAvC,EAIHd,MAJG,CAII;AACTc,eAAW;AADF,IAJJ,CAAN;AAOA,QAAK,MAAMS,IAAX,IAAmB0B,GAAnB,EAAwB;AACvB,QAAIC,IAAI,MAAM,QAAKtE,KAAL,CAAW,oBAAX,EAAiCE,KAAjC,CAAuC;AACpDkF,qBAAgBnF,KAAKK,EAD+B;AAEpDoC,WAAMC,IAF8C;AAGpDT,gBAAW;AAHyC,KAAvC,EAIX3B,IAJW,EAAd;AAKA,QAAIgE,MAAMC,OAAN,CAAcF,CAAd,CAAJ,EAAsB;AACrB,WAAM,QAAKtE,KAAL,CAAW,oBAAX,EAAiC6B,GAAjC,CAAqC;AAC1CuD,sBAAgBnF,KAAKK,EADqB;AAE1CoC,YAAMC;AAFoC,MAArC,CAAN;AAIA;AACD;AACD,UAAO,QAAKlC,OAAL,EAAP;AAhC4B;AAiC5B;;AAEKgF,oBAAN,GAA4B;AAAA;;AAAA;AAC3B,OAAIF,QAAQ,QAAKjE,IAAL,CAAU,OAAV,CAAZ;AACA,OAAIrB,OAAO,QAAKqB,IAAL,CAAU,MAAV,CAAX;AACA,OAAId,OAAO;AACVkC,UAAM6C,MAAM,CAAN,EAAS7C,IAAT,CAAc+B,SAAd,CAAwB,CAAxB,CADI;AAEVe,aAASvF,KAAKuF;AAFJ,IAAX;AAIA,OAAIlF,KAAK,MAAM,QAAKN,KAAL,CAAW,aAAX,EAA0B6B,GAA1B,CAA8BrB,IAA9B,CAAf;AACA,OAAIkC,OAAO6C,MAAM,CAAN,EAAS7C,IAAT,CAAc+B,SAAd,CAAwB,CAAxB,CAAX;AACA,OAAIJ,MAAM3B,KAAKO,KAAL,CAAW,GAAX,CAAV;AACA,QAAK,MAAMN,IAAX,IAAmB0B,GAAnB,EAAwB;AACvB,UAAM,QAAKrE,KAAL,CAAW,oBAAX,EAAiC6B,GAAjC,CAAqC;AAC1CuD,qBAAgB9E,EAD0B;AAE1CoC,WAAMC;AAFoC,KAArC,CAAN;AAIA;AACD,UAAO,QAAKlC,OAAL,EAAP;AAhB2B;AAiB3B;AA/gBkC,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\shipper.js", "sourcesContent": ["const Base = require('./base.js');\r\n\r\nmodule.exports = class extends Base {\r\n\t/**\r\n\t * index action\r\n\t * @return {Promise} []\r\n\t */\r\n\tasync indexAction() {\r\n\t\tconst model = this.model('shipper');\r\n\t\tconst info = await model.where({\r\n\t\t\tenabled: 1\r\n\t\t}).select();\r\n\t\tconst set = await this.model('settings').where({\r\n\t\t\tid: 1\r\n\t\t}).find();\r\n\t\tlet data = {\r\n\t\t\tinfo: info,\r\n\t\t\tset: set\r\n\t\t}\r\n\t\treturn this.success(data);\r\n\t}\r\n\r\n\tasync listAction() {\r\n\t\tconst page = this.get('page') || 1;\r\n\t\tconst size = this.get('size') || 10;\r\n\t\tconst name = this.get('name') || '';\r\n\t\tconst model = this.model('shipper');\r\n\t\tconst data = await model.where({\r\n\t\t\t'name|code': ['like', `%${name}%`]\r\n\t\t}).order('sort_order ASC').page(page, size).countSelect();\r\n\t\treturn this.success(data);\r\n\t}\r\n\r\n\tasync enabledStatusAction() {\r\n\t\tconst id = this.get('id');\r\n\t\tconst status = this.get('status');\r\n\t\tlet sale = 0;\r\n\t\tif (status == 'true') {\r\n\t\t\tsale = 1;\r\n\t\t}\r\n\t\tconst model = this.model('shipper');\r\n\t\tawait model.where({\r\n\t\t\tid: id\r\n\t\t}).update({\r\n\t\t\tenabled: sale\r\n\t\t});\r\n\t\treturn this.success();\r\n\t}\r\n\r\n\tasync updateSortAction() {\r\n\t\tconst id = this.post('id');\r\n\t\tconst sort = this.post('sort');\r\n\t\tconst model = this.model('shipper');\r\n\t\tconst data = await model.where({\r\n\t\t\tid: id\r\n\t\t}).update({\r\n\t\t\tsort_order: sort\r\n\t\t});\r\n\t\treturn this.success(data);\r\n\t}\r\n\r\n\tasync infoAction() {\r\n\t\tconst id = this.get('id');\r\n\t\tconst model = this.model('shipper');\r\n\t\tconst data = await model.where({\r\n\t\t\tid: id\r\n\t\t}).find();\r\n\t\treturn this.success(data);\r\n\t}\r\n\r\n\tasync storeAction() {\r\n\t\tif (!this.isPost) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\tconst values = this.post();\r\n\t\tconst id = this.post('id');\r\n\r\n\t\tconst model = this.model('shipper');\r\n\t\tif (id > 0) {\r\n\t\t\tawait model.where({\r\n\t\t\t\tid: id\r\n\t\t\t}).update(values);\r\n\t\t} else {\r\n\t\t\tdelete values.id;\r\n\t\t\tawait model.add(values);\r\n\t\t}\r\n\t\treturn this.success(values);\r\n\t}\r\n\r\n\r\n\tasync destoryAction() {\r\n\t\tconst id = this.post('id');\r\n\t\tawait this.model('shipper').where({\r\n\t\t\tid: id\r\n\t\t}).limit(1).delete();\r\n\t\treturn this.success();\r\n\t}\r\n\r\n\tasync freightAction() {\r\n\t\tconst model = this.model('freight_template');\r\n\t\tconst data = await model.where({\r\n\t\t\tis_delete: 0\r\n\t\t}).select();\r\n\t\treturn this.success(data);\r\n\t}\r\n\r\n\tasync freightDestroyAction() {\r\n\t\tconst id = this.post('id');\r\n\t\t// 软删除快递模板\r\n\t\tawait this.model('freight_template').where({\r\n\t\t\tid: id\r\n\t\t}).update({\r\n\t\t\tis_delete: 1\r\n\t\t});\r\n\r\n\t\t// 同时软删除相关的模板组数据\r\n\t\tawait this.model('freight_template_group').where({\r\n\t\t\ttemplate_id: id\r\n\t\t}).update({\r\n\t\t\tis_delete: 1\r\n\t\t});\r\n\r\n\t\t// 软删除相关的模板详情数据\r\n\t\tawait this.model('freight_template_detail').where({\r\n\t\t\ttemplate_id: id\r\n\t\t}).update({\r\n\t\t\tis_delete: 1\r\n\t\t});\r\n\r\n\t\treturn this.success();\r\n\t}\r\n\r\n\tasync getareadataAction() {\r\n\t\tlet all = await this.model('region').where({\r\n\t\t\ttype: 1\r\n\t\t}).field('id,name').select();\r\n\t\treturn this.success(all);\r\n\t}\r\n\r\n\r\n\tasync freightdetailAction() {\r\n\t\tlet id = this.post('id');\r\n\r\n\t\tconst model = this.model('freight_template_group');\r\n\t\tlet data = await model.where({\r\n\t\t\ttemplate_id: id,\r\n\t\t\tis_delete: 0,\r\n\t\t\tarea: ['<>', 0]\r\n\t\t}).select();\r\n\r\n\t\tfor (const item of data) {\r\n\t\t\tlet area = item.area;\r\n\t\t\tif (item.free_by_money > 0) {\r\n\t\t\t\titem.freeByMoney = false\r\n\t\t\t}\r\n\t\t\tif (item.free_by_number > 0) {\r\n\t\t\t\titem.freeByNumber = false\r\n\t\t\t}\r\n\t\t\tlet areaData = area.split(',');\r\n\t\t\tlet info = await this.model('region').where({\r\n\t\t\t\tid: ['IN', areaData]\r\n\t\t\t}).getField('name');\r\n\t\t\titem.areaName = info.join(',');\r\n\t\t}\r\n\r\n\t\tlet defaultData = await model.where({\r\n\t\t\ttemplate_id: id,\r\n\t\t\tarea: 0,\r\n\t\t\tis_delete: 0\r\n\t\t}).select();\r\n\r\n\t\tlet freight = await this.model('freight_template').where({\r\n\t\t\tid: id\r\n\t\t}).find();\r\n\r\n\t\tlet info = {\r\n\t\t\tfreight: freight,\r\n\t\t\tdata: data,\r\n\t\t\tdefaultData: defaultData\r\n\t\t};\r\n\r\n\t\treturn this.success(info);\r\n\t}\r\n\r\n\r\n\tasync saveTableAction() {\r\n\t\tlet data = this.post('table');\r\n\t\tlet def = this.post('defaultData');\r\n\t\tlet info = this.post('info');\r\n\t\tlet idInfo = []; // 是已存在的id。如果大于零，则去循环。等于零，则先将已存在的data删除，然后判断，1，data的length > 0.则，说明有新的数据\r\n\t\tfor (const item of data) {\r\n\t\t\tif (item.id > 0) {\r\n\t\t\t\tidInfo.push(item.id);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (idInfo.length != 0) {\r\n\t\t\tlet deleData = await this.model('freight_template_group').where({\r\n\t\t\t\tid: ['NOTIN', idInfo],\r\n\t\t\t\ttemplate_id: info.id,\r\n\t\t\t\tis_default: 0,\r\n\t\t\t\tis_delete: 0\r\n\t\t\t}).getField('id');\r\n\r\n\t\t\tfor (const ele of deleData) {\r\n\t\t\t\tawait this.model('freight_template_detail').where({\r\n\t\t\t\t\ttemplate_id: info.id,\r\n\t\t\t\t\tgroup_id: ele,\r\n\t\t\t\t\tis_delete: 0\r\n\t\t\t\t}).update({\r\n\t\t\t\t\tis_delete: 1\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\tlet dbTable = await this.model('freight_template_group').where({\r\n\t\t\t\tid: ['NOTIN', idInfo],\r\n\t\t\t\ttemplate_id: info.id,\r\n\t\t\t\tis_default: 0,\r\n\t\t\t\tis_delete: 0\r\n\t\t\t}).update({\r\n\t\t\t\tis_delete: 1\r\n\t\t\t});\r\n\r\n\t\t\tfor (const item of data) {\r\n\t\t\t\tlet id = item.id; // 这个是group_id\r\n\t\t\t\tif (id > 0) {\r\n\r\n\t\t\t\t\tlet template_id = info.id;\r\n\r\n\t\t\t\t\tlet val = {\r\n\t\t\t\t\t\tarea: item.area,\r\n\t\t\t\t\t\tstart: item.start,\r\n\t\t\t\t\t\tstart_fee: item.start_fee,\r\n\t\t\t\t\t\tadd: item.add,\r\n\t\t\t\t\t\tadd_fee: item.add_fee,\r\n\t\t\t\t\t\tfree_by_money: item.free_by_money,\r\n\t\t\t\t\t\tfree_by_number: item.free_by_number\r\n\t\t\t\t\t};\r\n\t\t\t\t\tawait this.model('freight_template_group').where({\r\n\t\t\t\t\t\tid: id,\r\n\t\t\t\t\t\ttemplate_id: template_id,\r\n\t\t\t\t\t\tis_delete: 0\r\n\t\t\t\t\t}).update(val);\r\n\r\n\t\t\t\t\t// 这里要根据area去notin更新\r\n\r\n\r\n\t\t\t\t\tlet area = item.area;\r\n\t\t\t\t\tlet arr = area.split(',');\r\n\r\n\t\t\t\t\tawait this.model('freight_template_detail').where({\r\n\t\t\t\t\t\tarea: ['NOTIN', arr],\r\n\t\t\t\t\t\ttemplate_id: template_id,\r\n\t\t\t\t\t\tgroup_id: id\r\n\t\t\t\t\t}).update({\r\n\t\t\t\t\t\tis_delete: 1\r\n\t\t\t\t\t});\r\n\t\t\t\t\tfor (const item of arr) {\r\n\t\t\t\t\t\tlet e = await this.model('freight_template_detail').where({\r\n\t\t\t\t\t\t\ttemplate_id: template_id,\r\n\t\t\t\t\t\t\tarea: item,\r\n\t\t\t\t\t\t\tgroup_id: id\r\n\t\t\t\t\t\t}).find();\r\n\t\t\t\t\t\tif (think.isEmpty(e)) {\r\n\t\t\t\t\t\t\tawait this.model('freight_template_detail').add({\r\n\t\t\t\t\t\t\t\ttemplate_id: template_id,\r\n\t\t\t\t\t\t\t\tgroup_id: id,\r\n\t\t\t\t\t\t\t\tarea: item\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tlet template_id = info.id;\r\n\t\t\t\t\tlet area = item.area.substring(2);\r\n\t\t\t\t\tlet val = {\r\n\t\t\t\t\t\tarea: area,\r\n\t\t\t\t\t\tstart: item.start,\r\n\t\t\t\t\t\tstart_fee: item.start_fee,\r\n\t\t\t\t\t\tadd: item.add,\r\n\t\t\t\t\t\tadd_fee: item.add_fee,\r\n\t\t\t\t\t\ttemplate_id: template_id,\r\n\t\t\t\t\t\tfree_by_money: item.free_by_money,\r\n\t\t\t\t\t\tfree_by_number: item.free_by_number\r\n\t\t\t\t\t};\r\n\t\t\t\t\tlet groupId = await this.model('freight_template_group').add(val);\r\n\t\t\t\t\tlet areaArr = area.split(',');\r\n\t\t\t\t\tfor (const item of areaArr) {\r\n\t\t\t\t\t\tawait this.model('freight_template_detail').add({\r\n\t\t\t\t\t\t\ttemplate_id: template_id,\r\n\t\t\t\t\t\t\tgroup_id: groupId,\r\n\t\t\t\t\t\t\tarea: item\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// 这里前台将table全删除了，所以要将原先的数据都删除\r\n\t\t\tlet dbTable = await this.model('freight_template_group').where({\r\n\t\t\t\ttemplate_id: info.id,\r\n\t\t\t\tis_default: 0,\r\n\t\t\t\tis_delete: 0\r\n\t\t\t}).update({\r\n\t\t\t\tis_delete: 1\r\n\t\t\t});\r\n\t\t\t// 将detail表也要删除！！！\r\n\r\n\t\t\tif (data.length != 0) {\r\n\t\t\t\tfor (const item of data) {\r\n\t\t\t\t\tlet area = item.area.substring(2);\r\n\t\t\t\t\tlet template_id = info.id;\r\n\t\t\t\t\tlet val = {\r\n\t\t\t\t\t\tarea: area,\r\n\t\t\t\t\t\tstart: item.start,\r\n\t\t\t\t\t\tstart_fee: item.start_fee,\r\n\t\t\t\t\t\tadd: item.add,\r\n\t\t\t\t\t\tadd_fee: item.add_fee,\r\n\t\t\t\t\t\ttemplate_id: template_id,\r\n\t\t\t\t\t\tfree_by_money: item.free_by_money,\r\n\t\t\t\t\t\tfree_by_number: item.free_by_number\r\n\t\t\t\t\t};\r\n\t\t\t\t\tlet groupId = await this.model('freight_template_group').add(val);\r\n\t\t\t\t\t//根据area 去循环一下另一张detail表\r\n\t\t\t\t\tlet areaArr = area.split(',');\r\n\t\t\t\t\tfor (const item of areaArr) {\r\n\t\t\t\t\t\tawait this.model('freight_template_detail').add({\r\n\t\t\t\t\t\t\ttemplate_id: template_id,\r\n\t\t\t\t\t\t\tgroup_id: groupId,\r\n\t\t\t\t\t\t\tarea: item\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet upData = {\r\n\t\t\tstart: def[0].start,\r\n\t\t\tstart_fee: def[0].start_fee,\r\n\t\t\tadd: def[0].add,\r\n\t\t\tadd_fee: def[0].add_fee,\r\n\t\t\tfree_by_money: def[0].free_by_money,\r\n\t\t\tfree_by_number: def[0].free_by_number\r\n\t\t};\r\n\r\n\t\tawait this.model('freight_template_group').where({\r\n\t\t\tid: def[0].id,\r\n\t\t\ttemplate_id: info.id,\r\n\t\t\tis_default: 1\r\n\t\t}).update(upData);\r\n\r\n\t\t// await this.model('freight_template_detail').where({\r\n\t\t//     group_id: def[0].id,\r\n\t\t//     template_id: info.id,\r\n\t\t// }).update(upData);\r\n\r\n\r\n\t\tlet tempData = {\r\n\t\t\tname: info.name,\r\n\t\t\tpackage_price: info.package_price,\r\n\t\t\tfreight_type: info.freight_type\r\n\t\t};\r\n\r\n\t\tawait this.model('freight_template').where({\r\n\t\t\tid: info.id\r\n\t\t}).update(tempData);\r\n\t\treturn this.success();\r\n\t}\r\n\r\n\tasync addTableAction() {\r\n\t\tlet info = this.post('info');\r\n\t\tlet data = this.post('table');\r\n\t\tlet def = this.post('defaultData');\r\n\t\t// return false;\r\n\t\tlet temp_id = await this.model('freight_template').add(info);\r\n\r\n\t\tif (temp_id > 0) {\r\n\t\t\tlet upData = {\r\n\t\t\t\tstart: def[0].start,\r\n\t\t\t\tstart_fee: def[0].start_fee,\r\n\t\t\t\tadd: def[0].add,\r\n\t\t\t\tadd_fee: def[0].add_fee,\r\n\t\t\t\tfree_by_money: def[0].free_by_money,\r\n\t\t\t\tfree_by_number: def[0].free_by_number,\r\n\t\t\t\ttemplate_id: temp_id,\r\n\t\t\t\tis_default: 1\r\n\t\t\t};\r\n\r\n\t\t\tlet groupId = await this.model('freight_template_group').add(upData);\r\n\t\t\tif (groupId > 0) {\r\n\t\t\t\tawait this.model('freight_template_detail').add({\r\n\t\t\t\t\ttemplate_id: temp_id,\r\n\t\t\t\t\tgroup_id: groupId,\r\n\t\t\t\t\tarea: 0\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\tif (data.length > 0) {\r\n\t\t\t\tfor (const item of data) {\r\n\t\t\t\t\tlet area = item.area.substring(2);\r\n\t\t\t\t\tlet template_id = temp_id;\r\n\t\t\t\t\tlet info = {\r\n\t\t\t\t\t\tarea: area,\r\n\t\t\t\t\t\tstart: item.start,\r\n\t\t\t\t\t\tstart_fee: item.start_fee,\r\n\t\t\t\t\t\tadd: item.add,\r\n\t\t\t\t\t\tadd_fee: item.add_fee,\r\n\t\t\t\t\t\ttemplate_id: temp_id,\r\n\t\t\t\t\t\tfree_by_money: item.free_by_money,\r\n\t\t\t\t\t\tfree_by_number: item.free_by_number\r\n\t\t\t\t\t};\r\n\t\t\t\t\tlet groupId = await this.model('freight_template_group').add(info);\r\n\t\t\t\t\tlet areaArr = area.split(',');\r\n\t\t\t\t\tfor (const item of areaArr) {\r\n\t\t\t\t\t\tawait this.model('freight_template_detail').add({\r\n\t\t\t\t\t\t\ttemplate_id: template_id,\r\n\t\t\t\t\t\t\tgroup_id: groupId,\r\n\t\t\t\t\t\t\tarea: item\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn this.success();\r\n\t}\r\n\r\n\tasync exceptareaAction() {\r\n\t\tconst model = this.model('except_area');\r\n\t\tconst data = await model.where({\r\n\t\t\tis_delete: 0\r\n\t\t}).select();\r\n\r\n\t\tfor (const item of data) {\r\n\t\t\tlet area = item.area;\r\n\t\t\tlet areaData = area.split(',');\r\n\t\t\tlet info = await this.model('region').where({\r\n\t\t\t\tid: ['IN', areaData]\r\n\t\t\t}).getField('name');\r\n\t\t\titem.areaName = info.join(',');\r\n\t\t}\r\n\r\n\t\treturn this.success(data);\r\n\t}\r\n\r\n\tasync exceptAreaDeleteAction() {\r\n\t\tconst id = this.post('id');\r\n\t\tawait this.model('except_area').where({\r\n\t\t\tid: id\r\n\t\t}).limit(1).update({\r\n\t\t\tis_delete: 1\r\n\t\t});\r\n\t\tawait this.model('except_area_detail').where({\r\n\t\t\texcept_area_id: id\r\n\t\t}).update({\r\n\t\t\tis_delete: 1\r\n\t\t});\r\n\t\treturn this.success();\r\n\t}\r\n\r\n\r\n\tasync exceptAreaDetailAction() {\r\n\t\tlet id = this.post('id');\r\n\t\tconst model = this.model('except_area');\r\n\t\tlet data = await model.where({\r\n\t\t\tid: id,\r\n\t\t\tis_delete: 0,\r\n\t\t}).find();\r\n\t\t// let areaData = {}\r\n\t\tlet area = data.area;\r\n\t\tlet areaData = area.split(',');\r\n\t\tlet info = await this.model('region').where({\r\n\t\t\tid: ['IN', areaData]\r\n\t\t}).getField('name');\r\n\t\tdata.areaName = info.join(',');\r\n\t\treturn this.success(data);\r\n\t}\r\n\r\n\tasync saveExceptAreaAction() {\r\n\t\tlet table = this.post('table');\r\n\t\tlet info = this.post('info');\r\n\t\tlet data = {\r\n\t\t\tarea: table[0].area,\r\n\t\t\tcontent: info.content\r\n\t\t};\r\n\t\tawait this.model('except_area').where({\r\n\t\t\tid: info.id\r\n\t\t}).update(data);\r\n\t\tlet area = table[0].area;\r\n\t\tlet arr = area.split(',');\r\n\t\tawait this.model('except_area_detail').where({\r\n\t\t\tarea: ['NOTIN', arr],\r\n\t\t\texcept_area_id: info.id,\r\n\t\t\tis_delete: 0\r\n\t\t}).update({\r\n\t\t\tis_delete: 1\r\n\t\t});\r\n\t\tfor (const item of arr) {\r\n\t\t\tlet e = await this.model('except_area_detail').where({\r\n\t\t\t\texcept_area_id: info.id,\r\n\t\t\t\tarea: item,\r\n\t\t\t\tis_delete: 0\r\n\t\t\t}).find();\r\n\t\t\tif (think.isEmpty(e)) {\r\n\t\t\t\tawait this.model('except_area_detail').add({\r\n\t\t\t\t\texcept_area_id: info.id,\r\n\t\t\t\t\tarea: item\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn this.success();\r\n\t}\r\n\r\n\tasync addExceptAreaAction() {\r\n\t\tlet table = this.post('table');\r\n\t\tlet info = this.post('info');\r\n\t\tlet data = {\r\n\t\t\tarea: table[0].area.substring(2),\r\n\t\t\tcontent: info.content\r\n\t\t};\r\n\t\tlet id = await this.model('except_area').add(data);\r\n\t\tlet area = table[0].area.substring(2);\r\n\t\tlet arr = area.split(',');\r\n\t\tfor (const item of arr) {\r\n\t\t\tawait this.model('except_area_detail').add({\r\n\t\t\t\texcept_area_id: id,\r\n\t\t\t\tarea: item\r\n\t\t\t});\r\n\t\t}\r\n\t\treturn this.success();\r\n\t}\r\n};\r\n"]}