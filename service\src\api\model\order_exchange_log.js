module.exports = class extends think.Model {
  
  /**
   * 获取用户的兑换记录
   * @param {number} userId 用户ID
   * @param {number} limit 限制数量
   * @returns {array} 兑换记录列表
   */
  async getUserExchangeRecords(userId, limit = 20) {
    try {
      const records = await this.where({
        user_id: userId
      }).order('created_at DESC').limit(limit).select();
      
      return records;
    } catch (error) {
      console.error('获取用户兑换记录失败:', error);
      return [];
    }
  }

  /**
   * 检查订单号是否已被兑换
   * @param {string} orderNumber 订单号
   * @param {number} userId 用户ID（可选）
   * @returns {boolean} 是否已兑换
   */
  async isOrderExchanged(orderNumber, userId = null) {
    try {
      const where = { order_number: orderNumber };
      if (userId) {
        where.user_id = userId;
      }
      
      const record = await this.where(where).find();
      return !think.isEmpty(record);
    } catch (error) {
      console.error('检查订单兑换状态失败:', error);
      return false;
    }
  }

  /**
   * 获取用户今日兑换次数
   * @param {number} userId 用户ID
   * @returns {number} 今日兑换次数
   */
  async getTodayExchangeCount(userId) {
    try {
      const today = new Date().toISOString().split('T')[0];
      const count = await this.where({
        user_id: userId,
        created_at: ['LIKE', `${today}%`]
      }).count();
      
      return count;
    } catch (error) {
      console.error('获取今日兑换次数失败:', error);
      return 0;
    }
  }

  /**
   * 获取兑换统计数据
   * @param {string} timeRange 时间范围 (today, week, month)
   * @returns {object} 统计数据
   */
  async getExchangeStats(timeRange = 'today') {
    try {
      let whereCondition = {};
      const now = new Date();
      
      switch (timeRange) {
        case 'today':
          const today = now.toISOString().split('T')[0];
          whereCondition.created_at = ['LIKE', `${today}%`];
          break;
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          whereCondition.created_at = ['>=', weekAgo.toISOString()];
          break;
        case 'month':
          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          whereCondition.created_at = ['>=', monthAgo.toISOString()];
          break;
      }
      
      // 总兑换次数
      const totalExchanges = await this.where(whereCondition).count();
      
      // 成功兑换次数
      const successExchanges = await this.where({
        ...whereCondition,
        status: 'success'
      }).count();
      
      // 总兑换积分
      const totalPoints = await this.where({
        ...whereCondition,
        status: 'success'
      }).sum('exchange_points');
      
      // 新增用户数（首次兑换的用户）
      const newUsers = await this.query(`
        SELECT COUNT(DISTINCT user_id) as count 
        FROM (
          SELECT user_id, MIN(created_at) as first_exchange 
          FROM order_exchange_log 
          WHERE status = 'success'
          GROUP BY user_id
        ) as first_exchanges 
        WHERE first_exchange >= ?
      `, [whereCondition.created_at ? whereCondition.created_at[1] : '1970-01-01']);
      
      return {
        totalExchanges: totalExchanges || 0,
        successExchanges: successExchanges || 0,
        totalPoints: totalPoints || 0,
        newUsers: newUsers[0] ? newUsers[0].count : 0
      };
    } catch (error) {
      console.error('获取兑换统计失败:', error);
      return {
        totalExchanges: 0,
        successExchanges: 0,
        totalPoints: 0,
        newUsers: 0
      };
    }
  }

  /**
   * 创建兑换记录
   * @param {object} data 兑换数据
   * @returns {number} 记录ID
   */
  async createExchangeRecord(data) {
    try {
      const currentTime = new Date();
      const recordData = {
        user_id: data.userId,
        order_number: data.orderNumber,
        order_platform: data.platform,
        order_amount: data.amount,
        exchange_points: data.points,
        exchange_rate: data.rate,
        status: data.status || 'success',
        fail_reason: data.failReason || '',
        created_at: currentTime,
        updated_at: currentTime
      };
      
      const recordId = await this.add(recordData);
      return recordId;
    } catch (error) {
      console.error('创建兑换记录失败:', error);
      throw error;
    }
  }

  /**
   * 更新兑换记录状态
   * @param {number} recordId 记录ID
   * @param {string} status 新状态
   * @param {string} failReason 失败原因（可选）
   * @returns {boolean} 是否成功
   */
  async updateExchangeStatus(recordId, status, failReason = '') {
    try {
      const updateData = {
        status: status,
        updated_at: new Date()
      };
      
      if (failReason) {
        updateData.fail_reason = failReason;
      }
      
      const result = await this.where({ id: recordId }).update(updateData);
      return result > 0;
    } catch (error) {
      console.error('更新兑换记录状态失败:', error);
      return false;
    }
  }
};
