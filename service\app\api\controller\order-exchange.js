function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

const Base = require('./base.js');

module.exports = class extends Base {

  /**
   * 获取兑换配置
   * GET /api/order-exchange/config
   */
  configAction() {
    var _this = this;

    return _asyncToGenerator(function* () {
      console.log('=== 获取兑换配置请求 ===');

      try {
        // 这里可以从数据库获取配置，暂时返回默认配置
        const config = {
          enabled: true,
          dailyLimit: 3,
          minOrderAmount: 50,
          exchangeRate: 0.5, // 1元=0.5积分
          maxPoints: 1000,
          supportedPlatforms: ['taobao', 'tmall', 'jd', 'pdd']
        };

        return _this.success(config);
      } catch (error) {
        console.error('获取兑换配置失败:', error);
        return _this.fail(500, '获取配置失败');
      }
    })();
  }

  /**
   * 执行订单兑换
   * POST /api/order-exchange/exchange
   */
  exchangeAction() {
    var _this2 = this;

    return _asyncToGenerator(function* () {
      console.log('=== 订单兑换请求开始 ===');

      const orderNumber = _this2.post('orderNumber');
      const openid = _this2.post('openid');

      console.log('订单号:', orderNumber);
      console.log('用户openid:', openid);

      if (!orderNumber || !openid) {
        return _this2.fail(400, '订单号和用户信息不能为空');
      }

      try {
        // 1. 验证用户是否存在
        const user = yield _this2.model('user').where({
          weixin_openid: openid
        }).find();

        if (think.isEmpty(user)) {
          return _this2.fail(404, '用户不存在，请先完成登录');
        }

        const userId = user.id;
        console.log('用户ID:', userId, '昵称:', user.nickname);

        // 2. 检查订单号是否已经兑换过
        const existingExchange = yield _this2.model('order_exchange_log').where({
          order_number: orderNumber,
          user_id: userId
        }).find();

        if (!think.isEmpty(existingExchange)) {
          return _this2.fail(400, '该订单号已经兑换过了');
        }

        // 3. 检查今日兑换次数限制
        const today = new Date().toISOString().split('T')[0];
        const todayExchangeCount = yield _this2.model('order_exchange_log').where({
          user_id: userId,
          created_at: ['LIKE', `${today}%`]
        }).count();

        if (todayExchangeCount >= 3) {
          return _this2.fail(400, '今日兑换次数已达上限');
        }

        // 4. 模拟订单验证（实际应该调用第三方API验证）
        const orderInfo = yield _this2.validateOrder(orderNumber);
        if (!orderInfo.valid) {
          return _this2.fail(400, orderInfo.message || '订单验证失败');
        }

        // 5. 计算积分
        const exchangeRate = 0.5; // 1元=0.5积分
        const earnedPoints = Math.floor(orderInfo.amount * exchangeRate);

        if (earnedPoints <= 0) {
          return _this2.fail(400, '订单金额不足，无法兑换积分');
        }

        // 6. 开始事务处理
        const currentTime = new Date();

        // 记录兑换日志
        const exchangeLogId = yield _this2.model('order_exchange_log').add({
          user_id: userId,
          order_number: orderNumber,
          order_platform: orderInfo.platform,
          order_amount: orderInfo.amount,
          exchange_points: earnedPoints,
          exchange_rate: exchangeRate,
          status: 'success',
          created_at: currentTime,
          updated_at: currentTime
        });

        // 积分系统已移除，但保持兑换记录
        console.log('兑换成功（积分系统已移除），原本会获得积分:', earnedPoints);

        return _this2.success({
          success: true,
          earnedPoints: earnedPoints,
          orderAmount: orderInfo.amount,
          exchangeRate: exchangeRate,
          message: `兑换成功！获得${earnedPoints}积分`
        });
      } catch (error) {
        console.error('订单兑换失败:', error);
        return _this2.fail(500, '兑换失败，请重试');
      }
    })();
  }

  /**
   * 获取兑换记录
   * GET /api/order-exchange/records
   */
  recordsAction() {
    var _this3 = this;

    return _asyncToGenerator(function* () {
      console.log('=== 获取兑换记录请求 ===');

      const openid = _this3.get('openid');

      if (!openid) {
        return _this3.fail(400, '用户信息不能为空');
      }

      try {
        // 获取用户信息
        const user = yield _this3.model('user').where({
          weixin_openid: openid
        }).find();

        if (think.isEmpty(user)) {
          return _this3.fail(404, '用户不存在');
        }

        // 获取兑换记录
        const records = yield _this3.model('order_exchange_log').where({
          user_id: user.id
        }).order('created_at DESC').limit(20).select();

        // 格式化记录
        const formattedRecords = records.map(function (record) {
          return {
            id: record.id,
            platform: _this3.getPlatformName(record.order_platform),
            orderNumber: record.order_number,
            date: record.created_at.split(' ')[0],
            amount: record.order_amount,
            points: record.exchange_points,
            status: record.status
          };
        });

        return _this3.success(formattedRecords);
      } catch (error) {
        console.error('获取兑换记录失败:', error);
        return _this3.fail(500, '获取记录失败');
      }
    })();
  }

  /**
   * 验证订单（模拟）
   * @param {string} orderNumber 订单号
   * @returns {object} 验证结果
   */
  validateOrder(orderNumber) {
    return _asyncToGenerator(function* () {
      // 这里应该调用第三方API验证订单
      // 暂时使用模拟逻辑

      console.log('验证订单:', orderNumber);

      // 模拟不同平台的订单号格式
      let platform = 'unknown';
      let amount = 0;

      if (orderNumber.startsWith('TB') || orderNumber.startsWith('tb')) {
        platform = 'taobao';
        amount = 99.00;
      } else if (orderNumber.startsWith('TM') || orderNumber.startsWith('tm')) {
        platform = 'tmall';
        amount = 158.00;
      } else if (orderNumber.startsWith('JD') || orderNumber.startsWith('jd')) {
        platform = 'jd';
        amount = 299.00;
      } else if (orderNumber.startsWith('PDD') || orderNumber.startsWith('pdd')) {
        platform = 'pdd';
        amount = 45.00;
      } else {
        // 通用订单号，随机金额
        platform = 'other';
        amount = Math.floor(Math.random() * 200) + 50;
      }

      // 模拟验证失败的情况
      if (orderNumber.includes('invalid') || orderNumber.includes('error')) {
        return {
          valid: false,
          message: '订单不存在或已失效'
        };
      }

      if (amount < 50) {
        return {
          valid: false,
          message: '订单金额低于最低兑换标准（50元）'
        };
      }

      return {
        valid: true,
        platform: platform,
        amount: amount
      };
    })();
  }

  /**
   * 获取平台名称
   * @param {string} platform 平台代码
   * @returns {string} 平台名称
   */
  getPlatformName(platform) {
    const platformNames = {
      'taobao': '淘宝',
      'tmall': '天猫',
      'jd': '京东',
      'pdd': '拼多多',
      'douyin': '抖音',
      'other': '其他'
    };

    return platformNames[platform] || '未知';
  }
};