<view class="container">
  <!-- 状态信息 -->
  <view class="status-section">
    <view class="status-icon" style="background-color: {{statusColor[refundInfo.status]}}">
      <text class="icon">{{refundInfo.status === 'completed' ? '✓' : '●'}}</text>
    </view>
    <view class="status-info">
      <view class="status-title">{{statusText[refundInfo.status] || '未知状态'}}</view>
      <view class="status-desc" wx:if="{{refundInfo.status === 'pending'}}">
        您的售后申请已提交，我们会尽快处理
      </view>
      <view class="status-desc" wx:elif="{{refundInfo.status === 'processing'}}">
        售后申请正在处理中，请耐心等待
      </view>
      <view class="status-desc" wx:elif="{{refundInfo.status === 'approved'}}">
        <view wx:if="{{refundInfo.refund_type === 'refund_only'}}">
          仅退款申请已通过，退款将在1-3个工作日内到账
        </view>
        <view wx:else>
          退货退款申请已通过，请查看退货地址信息
        </view>
      </view>
      <view class="status-desc" wx:elif="{{refundInfo.status === 'wait_return'}}">
        请将商品寄回指定地址，并填写物流信息
      </view>
      <view class="status-desc" wx:elif="{{refundInfo.status === 'returned'}}">
        您已寄回商品，我们正在确认收货
      </view>
      <view class="status-desc" wx:elif="{{refundInfo.status === 'rejected'}}">
        售后申请已被拒绝，如有疑问请联系客服
      </view>
      <view class="status-desc" wx:elif="{{refundInfo.status === 'completed'}}">
        售后已完成，感谢您的理解与支持
      </view>
    </view>
  </view>

  <!-- 申请信息 -->
  <view class="info-section">
    <view class="section-title">申请信息</view>
    <view class="info-item">
      <view class="info-label">申请编号</view>
      <view class="info-value">{{refundInfo.id}}</view>
    </view>
    <view class="info-item">
      <view class="info-label">申请时间</view>
      <view class="info-value">{{refundInfo.apply_time_text}}</view>
    </view>
    <view class="info-item">
      <view class="info-label">退款类型</view>
      <view class="info-value">{{refundInfo.refund_type === 'refund_only' ? '仅退款' : '退货退款'}}</view>
    </view>
    <view class="info-item">
      <view class="info-label">退款金额</view>
      <view class="info-value price">¥{{refundInfo.refund_amount}}</view>
    </view>
    <view class="info-item">
      <view class="info-label">退款原因</view>
      <view class="info-value">{{refundInfo.refund_reason}}</view>
    </view>
    <view class="info-item" wx:if="{{refundInfo.refund_desc}}">
      <view class="info-label">退款说明</view>
      <view class="info-value">{{refundInfo.refund_desc}}</view>
    </view>
  </view>

  <!-- 退货地址信息 -->
  <view class="info-section" wx:if="{{refundInfo.refund_type === 'return_refund' && (refundInfo.status === 'wait_return' || refundInfo.status === 'returned')}}">
    <view class="section-title">退货地址</view>
    <view class="return-address">
      <view class="address-item">
        <view class="address-label">收货人：</view>
        <view class="address-value">{{refundInfo.return_contact || '客服中心'}}</view>
      </view>
      <view class="address-item">
        <view class="address-label">联系电话：</view>
        <view class="address-value">{{refundInfo.return_phone || '************'}}</view>
      </view>
      <view class="address-item">
        <view class="address-label">退货地址：</view>
        <view class="address-value">{{refundInfo.return_address || '请联系客服获取退货地址'}}</view>
      </view>
    </view>
  </view>

  <!-- 物流信息填写/显示 -->
  <view class="info-section" wx:if="{{refundInfo.refund_type === 'return_refund' && refundInfo.status === 'wait_return'}}">
    <view class="section-title">物流信息</view>
    <view class="logistics-form">
      <view class="form-item">
        <view class="form-label">物流公司</view>
        <input class="form-input" placeholder="请输入物流公司" value="{{logisticsCompany}}" bindinput="onLogisticsCompanyInput" />
      </view>
      <view class="form-item">
        <view class="form-label">物流单号</view>
        <input class="form-input" placeholder="请输入物流单号" value="{{logisticsNo}}" bindinput="onLogisticsNoInput" />
      </view>
      <button class="submit-logistics-btn" bindtap="submitLogistics">提交物流信息</button>
    </view>
  </view>

  <!-- 已提交的物流信息 -->
  <view class="info-section" wx:if="{{refundInfo.refund_type === 'return_refund' && refundInfo.status === 'returned'}}">
    <view class="section-title">物流信息</view>
    <view class="info-item">
      <view class="info-label">物流公司</view>
      <view class="info-value">{{refundInfo.user_logistics_company}}</view>
    </view>
    <view class="info-item">
      <view class="info-label">物流单号</view>
      <view class="info-value">{{refundInfo.user_logistics_no}}</view>
    </view>
    <view class="info-item">
      <view class="info-label">寄出时间</view>
      <view class="info-value">{{refundInfo.user_return_time_text}}</view>
    </view>
  </view>

  <!-- 凭证图片 -->
  <view class="info-section" wx:if="{{refundInfo.images && refundInfo.images.length > 0}}">
    <view class="section-title">凭证图片</view>
    <view class="image-list">
      <image 
        class="evidence-image" 
        wx:for="{{refundInfo.images}}" 
        wx:key="index"
        src="{{item}}" 
        mode="aspectFill"
        bindtap="previewImage"
        data-src="{{item}}">
      </image>
    </view>
  </view>

  <!-- 订单信息 -->
  <view class="info-section">
    <view class="section-title">相关订单</view>
    <view class="order-info">
      <view class="order-sn">订单号：{{orderInfo.order_sn}}</view>
      <view class="goods-list">
        <view class="goods-item" wx:for="{{orderGoods}}" wx:key="id">
          <image class="goods-image" src="{{item.list_pic_url}}" mode="aspectFill"></image>
          <view class="goods-info">
            <view class="goods-name">{{item.goods_name}}</view>
            <view class="goods-spec" wx:if="{{item.goods_specifition_name_value}}">{{item.goods_specifition_name_value}}</view>
            <view class="goods-price">¥{{item.retail_price}} × {{item.number}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 管理员备注 -->
  <view class="info-section" wx:if="{{refundInfo.admin_memo || refundInfo.reject_reason}}">
    <view class="section-title">处理备注</view>
    <view class="memo-content">
      {{refundInfo.admin_memo || refundInfo.reject_reason}}
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <button class="contact-btn" bindtap="contactService">
      联系客服
    </button>
    <button 
      class="cancel-btn" 
      wx:if="{{refundInfo.status === 'pending'}}"
      bindtap="cancelRefund">
      撤销申请
    </button>
  </view>
</view>
