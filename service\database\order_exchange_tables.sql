-- 订单兑换日志表
CREATE TABLE IF NOT EXISTS `order_exchange_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `order_number` varchar(100) NOT NULL COMMENT '订单号',
  `order_platform` varchar(50) NOT NULL COMMENT '订单平台（taobao, tmall, jd, pdd等）',
  `order_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单金额',
  `exchange_points` int(11) NOT NULL DEFAULT '0' COMMENT '兑换获得的积分',
  `exchange_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '兑换比例（1元=?积分）',
  `status` enum('success','failed','pending') NOT NULL DEFAULT 'pending' COMMENT '兑换状态',
  `fail_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_number` (`order_number`),
  KEY `idx_created_at` (`created_at`),
  UNIQUE KEY `uk_user_order` (`user_id`, `order_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单兑换日志表';

-- 用户积分表（如果不存在）
CREATE TABLE IF NOT EXISTS `user_points` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `total_points` int(11) NOT NULL DEFAULT '0' COMMENT '总积分',
  `available_points` int(11) NOT NULL DEFAULT '0' COMMENT '可用积分',
  `used_points` int(11) NOT NULL DEFAULT '0' COMMENT '已使用积分',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户积分表';

-- 积分变动日志表（如果不存在）
CREATE TABLE IF NOT EXISTS `points_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `points_change` int(11) NOT NULL COMMENT '积分变动（正数为增加，负数为减少）',
  `points_type` varchar(50) NOT NULL COMMENT '积分类型（signin, order_exchange, consume等）',
  `source_id` int(11) DEFAULT NULL COMMENT '来源ID（如兑换记录ID、订单ID等）',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_points_type` (`points_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分变动日志表';

-- 订单兑换配置表
CREATE TABLE IF NOT EXISTS `order_exchange_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `daily_limit` int(11) NOT NULL DEFAULT '3' COMMENT '每日兑换限制次数',
  `min_order_amount` decimal(10,2) NOT NULL DEFAULT '50.00' COMMENT '最低订单金额',
  `exchange_rate` decimal(5,2) NOT NULL DEFAULT '0.50' COMMENT '兑换比例（1元=?积分）',
  `max_points` int(11) NOT NULL DEFAULT '1000' COMMENT '单次最高兑换积分',
  `supported_platforms` text COMMENT '支持的平台（JSON格式）',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单兑换配置表';

-- 插入默认配置
INSERT INTO `order_exchange_config` (`enabled`, `daily_limit`, `min_order_amount`, `exchange_rate`, `max_points`, `supported_platforms`, `created_at`, `updated_at`) 
VALUES (1, 3, 50.00, 0.50, 1000, '["taobao","tmall","jd","pdd"]', NOW(), NOW())
ON DUPLICATE KEY UPDATE `updated_at` = NOW();
