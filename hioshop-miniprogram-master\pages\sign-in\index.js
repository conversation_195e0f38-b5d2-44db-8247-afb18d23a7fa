const app = getApp();
const util = require('../../utils/util.js');
const api = require('../../config/api.js');

Page({
  data: {
    // 签到状态
    consecutiveDays: 0,
    totalPoints: 0,
    todayStatusText: '今日尚未签到，别忘记哦！',
    todaySignedClass: '',
    
    // 日历相关
    currentYear: 0,
    currentMonth: 0,
    currentMonthText: '',
    calendarDays: [],
    
    // 签到记录
    signedDates: [],
    
    // 弹窗
    showSuccessModal: false,
    earnedPoints: 0
  },

  onLoad: function() {
    this.initCalendar();
    this.loadSignData();
  },

  onShow: function() {
    this.loadSignData();
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');

    if (!token || !userInfo) {
      wx.showModal({
        title: '需要登录',
        content: '请先登录后再使用签到功能',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return false;
    }

    return true;
  },

  // 初始化日历
  initCalendar: function() {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth();
    
    this.setData({
      currentYear: year,
      currentMonth: month
    });
    
    this.renderCalendar(year, month);
  },

  // 渲染日历
  renderCalendar: function(year, month) {
    const today = new Date();
    const currentDate = today.getDate();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();
    
    // 设置月份标题
    const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', 
                       '7月', '8月', '9月', '10月', '11月', '12月'];
    const monthText = `${year}年 ${monthNames[month]}`;
    
    // 获取当月第一天是星期几 (0=周日, 1=周一, ..., 6=周六)
    const firstDay = new Date(year, month, 1).getDay();
    const adjustedFirstDay = firstDay === 0 ? 7 : firstDay; // 将周日(0)转为7
    
    // 获取当月天数
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    
    const calendarDays = [];
    
    // 添加空白格子
    for (let i = 1; i < adjustedFirstDay; i++) {
      calendarDays.push({
        day: '',
        date: '',
        className: 'empty',
        isSigned: false
      });
    }
    
    // 添加日期格子
    for (let day = 1; day <= daysInMonth; day++) {
      const dateString = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      const isSigned = this.data.signedDates.includes(dateString);
      const isToday = day === currentDate && month === currentMonth && year === currentYear;
      const isFuture = new Date(dateString) > today;
      
      let className = 'calendar-day';
      if (isToday) {
        className += ' today';
        if (!isSigned) {
          className += ' clickable can-sign';
        }
      } else if (isSigned) {
        className += ' signed';
      } else if (isFuture) {
        className += ' future';
      }
      
      const canSign = isToday && !isSigned;

      calendarDays.push({
        day: day,
        date: dateString,
        className: className,
        isSigned: isSigned,
        isToday: isToday,
        canSign: canSign
      });
    }
    
    this.setData({
      currentMonthText: monthText,
      calendarDays: calendarDays
    });
  },

  // 加载签到数据
  loadSignData: function() {
    const that = this;

    // 显示加载中
    wx.showLoading({
      title: '加载中...'
    });

    // 获取本地存储的openid和用户信息
    const openid = wx.getStorageSync('openid');
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');

    console.log('签到数据检查:');
    console.log('- openid:', openid);
    console.log('- userInfo:', userInfo);
    console.log('- token:', token);

    if (!openid) {
      wx.hideLoading();
      wx.showToast({
        title: '请先完成授权登录',
        icon: 'none'
      });
      return;
    }

    // 调用API获取签到数据，传递openid参数
    util.request(api.SignInData, { openid: openid }, 'GET').then(function(res) {
      wx.hideLoading();

      console.log('签到数据API响应:', res);

      if (res.errno === 0) {
        const data = res.data;
        console.log('签到数据解析:', data);

        that.setData({
          consecutiveDays: data.consecutiveDays || 0,
          totalPoints: data.totalPoints || 0,
          signedDates: data.signedDates || [],
          todayStatusText: data.todaySigned ? '今日已签到，明天继续来哦！' : '今日尚未签到，别忘记哦！',
          todaySignedClass: data.todaySigned ? 'signed' : ''
        });

        // 重新渲染日历
        that.renderCalendar(that.data.currentYear, that.data.currentMonth);
        console.log('签到数据加载成功');
        console.log('已签到日期:', data.signedDates);
        console.log('今日是否已签到:', data.todaySigned);
      } else {
        console.log('获取签到数据失败:', res.errmsg || res.errno);
        wx.showToast({
          title: res.errmsg || '获取数据失败',
          icon: 'none'
        });
      }
    }).catch(function(err) {
      wx.hideLoading();
      console.log('签到数据请求异常:', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },



  // 日期点击事件
  onDayTap: function(e) {
    const date = e.currentTarget.dataset.date;
    const index = e.currentTarget.dataset.index;
    const dayData = this.data.calendarDays[index];

    console.log('点击日期:', date);
    console.log('日期数据:', dayData);
    console.log('是否可签到:', dayData ? dayData.canSign : 'dayData为空');

    if (dayData && dayData.canSign) {
      console.log('开始执行签到...');
      this.performSignIn(date, index);
    } else {
      console.log('不能签到，原因:', dayData ? '不是今天或已签到' : '日期数据为空');
      if (dayData && dayData.isToday && dayData.isSigned) {
        wx.showToast({
          title: '今日已签到',
          icon: 'none'
        });
      } else if (dayData && !dayData.isToday) {
        wx.showToast({
          title: '只能签到今天',
          icon: 'none'
        });
      }
    }
  },

  // 执行签到
  performSignIn: function(date, index) {
    const that = this;

    // 获取本地存储的openid
    const openid = wx.getStorageSync('openid');
    console.log('签到使用的openid:', openid);

    if (!openid) {
      wx.showToast({
        title: '请先完成授权登录',
        icon: 'none'
      });
      return;
    }

    // 显示加载中
    wx.showLoading({
      title: '签到中...'
    });

    // 调用签到API，传递openid和日期
    util.request(api.SignIn, { openid: openid, date: date }, 'POST').then(function(res) {
      wx.hideLoading();
      
      if (res.errno === 0) {
        const data = res.data;
        that.handleSignInSuccess(data, index);
      } else {
        console.log('签到失败:', res.errmsg);
        wx.showToast({
          title: res.errmsg || '签到失败',
          icon: 'none'
        });
      }
    }).catch(function(err) {
      wx.hideLoading();
      console.log('签到API调用失败:', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // 处理签到成功
  handleSignInSuccess: function(data, index) {
    // 更新日历显示
    const calendarDays = this.data.calendarDays;
    calendarDays[index].isSigned = true;
    calendarDays[index].canSign = false;
    calendarDays[index].className = calendarDays[index].className.replace('clickable', '').replace('can-sign', '') + ' signed signing';
    
    // 更新签到数据
    this.setData({
      calendarDays: calendarDays,
      consecutiveDays: data.consecutiveDays,
      totalPoints: data.totalPoints,
      todayStatusText: '今日已签到，明天继续来哦！',
      todaySignedClass: 'signed',
      earnedPoints: data.earnedPoints,
      showSuccessModal: true
    });
    
    // 移除动画类
    setTimeout(() => {
      const updatedDays = this.data.calendarDays;
      updatedDays[index].className = updatedDays[index].className.replace(' signing', '');
      this.setData({
        calendarDays: updatedDays
      });
    }, 800);
  },



  // 上一月
  prevMonth: function() {
    let year = this.data.currentYear;
    let month = this.data.currentMonth - 1;
    
    if (month < 0) {
      month = 11;
      year--;
    }
    
    this.setData({
      currentYear: year,
      currentMonth: month
    });
    
    this.renderCalendar(year, month);
  },

  // 下一月
  nextMonth: function() {
    let year = this.data.currentYear;
    let month = this.data.currentMonth + 1;
    
    if (month > 11) {
      month = 0;
      year++;
    }
    
    this.setData({
      currentYear: year,
      currentMonth: month
    });
    
    this.renderCalendar(year, month);
  },

  // 关闭成功弹窗
  closeSuccessModal: function() {
    this.setData({
      showSuccessModal: false
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    console.log('下拉刷新签到数据');
    this.loadSignData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  }
});
