const mysql = require('mysql2');

// 数据库连接配置
const connection = mysql.createConnection({
  host: '127.0.0.1',
  port: 3306,
  user: 'root',
  password: '19841020',
  database: 'hiolabsDB'
});

// 创建二维码登录token表的SQL
const createTableSQL = `
CREATE TABLE IF NOT EXISTS \`hiolabs_qr_login_tokens\` (
  \`id\` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  \`token\` varchar(64) NOT NULL COMMENT '二维码token',
  \`status\` enum('pending','success','expired') DEFAULT 'pending' COMMENT '状态：pending等待扫码，success登录成功，expired已过期',
  \`user_id\` int(11) DEFAULT NULL COMMENT '扫码用户ID',
  \`user_type\` enum('promoter','distributor') DEFAULT NULL COMMENT '用户类型：promoter推广员，distributor分销商',
  \`create_time\` int(11) NOT NULL COMMENT '创建时间戳',
  \`expire_time\` int(11) NOT NULL COMMENT '过期时间戳',
  \`scan_time\` int(11) DEFAULT NULL COMMENT '扫码时间戳',
  \`login_time\` int(11) DEFAULT NULL COMMENT '登录时间戳',
  PRIMARY KEY (\`id\`),
  UNIQUE KEY \`token\` (\`token\`),
  KEY \`status\` (\`status\`),
  KEY \`expire_time\` (\`expire_time\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='二维码登录token表';
`;

// 连接数据库并创建表
connection.connect((err) => {
  if (err) {
    console.error('数据库连接失败:', err);
    return;
  }
  
  console.log('数据库连接成功');
  
  // 执行创建表的SQL
  connection.query(createTableSQL, (err, results) => {
    if (err) {
      console.error('创建表失败:', err);
    } else {
      console.log('二维码登录表创建成功');
    }
    
    // 关闭连接
    connection.end();
  });
});
