function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

const Base = require('./base.js');

module.exports = class extends Base {

    /**
     * 记录推广访问
     * POST /api/promotion/recordVisit
     */
    recordVisitAction() {
        var _this = this;

        return _asyncToGenerator(function* () {
            try {
                const goodsId = _this.post('goodsId');
                const promoterUserId = _this.post('promoterUserId');
                const shareSource = _this.post('shareSource') || 'miniprogram';

                console.log('=== 记录推广访问 ===');
                console.log('商品ID:', goodsId);
                console.log('推广员用户ID:', promoterUserId);
                console.log('分享来源:', shareSource);

                if (!goodsId || !promoterUserId) {
                    return _this.fail(400, '参数不完整');
                }

                // 获取当前用户ID（如果已登录）
                let visitorUserId = 0;
                try {
                    visitorUserId = yield _this.getLoginUserId();
                } catch (error) {
                    // 用户未登录，visitorUserId 为 0
                    console.log('用户未登录，记录匿名访问');
                }

                // 移除自购限制，允许推广员访问自己的分享
                console.log('记录推广访问，访问者ID:', visitorUserId, '推广员ID:', promoterUserId);

                const currentTime = parseInt(new Date().getTime() / 1000);

                // 首先获取或创建推广员记录
                let promoter = yield _this.model('hiolabs_personal_promoters').where({
                    user_id: promoterUserId
                }).find();

                if (think.isEmpty(promoter)) {
                    // 如果推广员不存在，创建推广员记录
                    const promoterData = {
                        user_id: promoterUserId,
                        level: 1,
                        status: 1,
                        first_share_time: currentTime,
                        create_time: currentTime,
                        update_time: currentTime
                    };
                    const promoterId = yield _this.model('hiolabs_personal_promoters').add(promoterData);
                    promoter = Object.assign({ id: promoterId }, promoterData);
                    console.log('创建新推广员记录:', promoterId);
                }

                // 检查是否已经记录过（24小时内同一用户访问同一推广员的同一商品）
                const yesterday = currentTime - 86400;
                let existingRecord = null;

                if (visitorUserId > 0) {
                    existingRecord = yield _this.model('hiolabs_promotion_visits').where({
                        promoter_user_id: promoterUserId,
                        visitor_user_id: visitorUserId,
                        goods_id: goodsId,
                        visit_time: ['>', yesterday]
                    }).find();
                } else {
                    // 对于未登录用户，通过IP地址判断
                    const ipAddress = _this.ctx.ip || '';
                    existingRecord = yield _this.model('hiolabs_promotion_visits').where({
                        promoter_user_id: promoterUserId,
                        goods_id: goodsId,
                        ip_address: ipAddress,
                        visitor_user_id: 0,
                        visit_time: ['>', yesterday]
                    }).find();
                }

                if (!think.isEmpty(existingRecord)) {
                    console.log('24小时内已记录过访问，不重复记录');
                    return _this.success({ message: '访问记录成功' });
                }

                // 检查是否是新访客（24小时内未访问过该推广员的任何商品）
                const isNewVisitor = visitorUserId > 0 ? think.isEmpty((yield _this.model('hiolabs_promotion_visits').where({
                    promoter_user_id: promoterUserId,
                    visitor_user_id: visitorUserId,
                    visit_time: ['>', yesterday]
                }).find())) : 1;

                // 创建推广访问记录
                const visitRecord = {
                    promoter_id: promoter.id,
                    promoter_user_id: promoterUserId,
                    visitor_user_id: visitorUserId,
                    goods_id: goodsId,
                    share_source: shareSource,
                    visit_time: currentTime,
                    ip_address: _this.ctx.ip || '',
                    user_agent: _this.ctx.header['user-agent'] || '',
                    is_new_visitor: isNewVisitor ? 1 : 0
                };

                const recordId = yield _this.model('hiolabs_promotion_visits').add(visitRecord);

                if (recordId) {
                    console.log('推广访问记录成功，记录ID:', recordId);

                    // 更新推广员统计数据
                    yield _this.updatePromoterStats(promoter.id, 'visit');

                    return _this.success({
                        message: '访问记录成功',
                        recordId: recordId
                    });
                } else {
                    return _this.fail(500, '记录访问失败');
                }
            } catch (error) {
                console.error('记录推广访问失败:', error);
                return _this.fail(500, '记录访问失败');
            }
        })();
    }

    /**
     * 更新推广员统计数据
     * @param {number} promoterId 推广员ID
     * @param {string} type 统计类型：visit, order
     */
    updatePromoterStats(promoterId, type = 'visit') {
        var _this2 = this;

        return _asyncToGenerator(function* () {
            try {
                const currentTime = parseInt(new Date().getTime() / 1000);

                // 更新推广员统计数据
                let updateData = { update_time: currentTime };

                if (type === 'visit') {
                    updateData.total_views = ['exp', 'total_views + 1'];
                    updateData.month_views = ['exp', 'month_views + 1'];
                } else if (type === 'order') {
                    updateData.total_orders = ['exp', 'total_orders + 1'];
                    updateData.month_orders = ['exp', 'month_orders + 1'];
                }

                yield _this2.model('hiolabs_personal_promoters').where({
                    id: promoterId
                }).update(updateData);

                console.log('推广员统计更新成功');
            } catch (error) {
                console.error('更新推广员统计失败:', error);
            }
        })();
    }

    /**
     * 获取推广记录列表
     * GET /api/promotion/records
     */
    recordsAction() {
        var _this3 = this;

        return _asyncToGenerator(function* () {
            try {
                const userId = yield _this3.getLoginUserId();
                const page = _this3.get('page') || 1;
                const limit = _this3.get('limit') || 20;

                // 获取用户的推广记录
                const records = yield _this3.model('hiolabs_promotion_visits').alias('pv').join({
                    table: 'hiolabs_goods',
                    join: 'left',
                    as: 'g',
                    on: ['pv.goods_id', 'g.id']
                }).join({
                    table: 'user',
                    join: 'left',
                    as: 'u',
                    on: ['pv.visitor_user_id', 'u.id']
                }).where({
                    'pv.promoter_user_id': userId
                }).field('pv.*, g.name as goods_name, g.list_pic_url, u.nickname as visitor_nickname').order('pv.visit_time DESC').page(page, limit).countSelect();

                return _this3.success({
                    data: records.data,
                    total: records.count,
                    page: parseInt(page),
                    limit: parseInt(limit)
                });
            } catch (error) {
                console.error('获取推广记录失败:', error);
                return _this3.fail(500, '获取推广记录失败');
            }
        })();
    }

    /**
     * 获取推广统计数据
     * GET /api/promotion/stats
     */
    statsAction() {
        var _this4 = this;

        return _asyncToGenerator(function* () {
            try {
                const userId = yield _this4.getLoginUserId();

                // 获取推广员信息
                const promoter = yield _this4.model('hiolabs_personal_promoters').where({
                    user_id: userId
                }).find();

                if (think.isEmpty(promoter)) {
                    return _this4.success({
                        total: {
                            visits: 0,
                            orders: 0,
                            commission: 0
                        },
                        today: {
                            visits: 0,
                            orders: 0,
                            commission: 0
                        }
                    });
                }

                // 获取今日统计数据
                const todayStart = parseInt(new Date().setHours(0, 0, 0, 0) / 1000);
                const todayVisits = yield _this4.model('hiolabs_promotion_visits').where({
                    promoter_user_id: userId,
                    visit_time: ['>=', todayStart]
                }).count();

                const todayOrders = yield _this4.model('hiolabs_promotion_orders').where({
                    promoter_user_id: userId,
                    create_time: ['>=', todayStart]
                }).count();

                const todayCommission = yield _this4.model('hiolabs_promotion_orders').where({
                    promoter_user_id: userId,
                    create_time: ['>=', todayStart]
                }).sum('commission_amount');

                return _this4.success({
                    total: {
                        visits: promoter.total_views || 0,
                        orders: promoter.total_orders || 0,
                        commission: parseFloat(promoter.total_commission) || 0
                    },
                    today: {
                        visits: todayVisits,
                        orders: todayOrders,
                        commission: parseFloat(todayCommission) || 0
                    }
                });
            } catch (error) {
                console.error('获取推广统计失败:', error);
                return _this4.fail(500, '获取统计数据失败');
            }
        })();
    }

    /**
     * 处理推广成交，发放佣金奖励（基于分销池配置）
     * POST /api/promotion/recordOrder
     */
    recordOrderAction() {
        var _this5 = this;

        return _asyncToGenerator(function* () {
            try {
                const orderId = _this5.post('orderId');
                const promoterUserId = _this5.post('promoterUserId');
                const goodsId = _this5.post('goodsId');
                const orderAmount = _this5.post('orderAmount') || 0;
                const shareSource = _this5.post('shareSource') || 'miniprogram';

                console.log('=== 处理推广成交（分享有礼佣金） ===');
                console.log('订单ID:', orderId);
                console.log('推广员用户ID:', promoterUserId);
                console.log('商品ID:', goodsId);
                console.log('订单金额:', orderAmount);
                console.log('分享来源:', shareSource);

                if (!orderId || !promoterUserId || !goodsId) {
                    return _this5.fail(400, '参数不完整');
                }

                // 检查推广员是否存在
                const promoter = yield _this5.model('hiolabs_personal_promoters').where({ user_id: promoterUserId, status: 1 }).find();

                if (think.isEmpty(promoter)) {
                    console.log('推广员不存在或已禁用');
                    return _this5.fail(400, '推广员不存在或已禁用');
                }

                // 使用佣金服务发放分享有礼佣金
                const commissionService = _this5.service('points');
                const result = yield commissionService.addShareCommission(promoterUserId, orderId, goodsId, orderAmount, shareSource);

                if (result.success) {
                    // 更新推广员成交统计
                    yield _this5.updatePromoterOrderStats(promoterUserId);

                    // 检查等级升级
                    yield commissionService.checkPromoterLevelUp(promoterUserId);

                    console.log('✅ 分享有礼佣金发放完成');
                    return _this5.success({
                        message: '分享有礼佣金发放成功',
                        commission: result.commission,
                        commissionDetails: result.result
                    });
                } else {
                    console.log('❌ 分享有礼佣金发放失败:', result.message);
                    return _this5.fail(400, result.message || '佣金发放失败');
                }
            } catch (error) {
                console.error('处理推广成交失败:', error);
                return _this5.fail(500, '处理推广成交失败');
            }
        })();
    }

    /**
     * 更新推广员成交统计
     */
    updatePromoterOrderStats(promoterUserId) {
        var _this6 = this;

        return _asyncToGenerator(function* () {
            try {
                const currentTime = parseInt(new Date().getTime() / 1000);
                const currentMonth = new Date().getMonth() + 1;
                const currentYear = new Date().getFullYear();

                const promoter = yield _this6.model('hiolabs_personal_promoters').where({ user_id: promoterUserId }).find();

                if (think.isEmpty(promoter)) {
                    return;
                }

                // 更新总成交次数
                const newTotalOrders = parseInt(promoter.total_orders || 0) + 1;

                // 检查是否是当月
                const promoterUpdateTime = new Date(promoter.update_time * 1000);
                const isCurrentMonth = promoterUpdateTime.getMonth() + 1 === currentMonth && promoterUpdateTime.getFullYear() === currentYear;

                let newMonthOrders;
                if (isCurrentMonth) {
                    newMonthOrders = parseInt(promoter.month_orders || 0) + 1;
                } else {
                    // 新月份，重置月度统计
                    newMonthOrders = 1;
                }

                // 更新推广员统计
                yield _this6.model('hiolabs_personal_promoters').where({ user_id: promoterUserId }).update({
                    total_orders: newTotalOrders,
                    month_orders: newMonthOrders,
                    update_time: currentTime
                });

                console.log(`✅ 推广员成交统计更新: 总成交=${newTotalOrders}, 月成交=${newMonthOrders}`);
            } catch (error) {
                console.error('更新推广员成交统计失败:', error);
                throw error;
            }
        })();
    }

    /**
     * 获取分享记录列表（区分浏览和下单）
     * GET /api/promotion/shareRecords
     */
    shareRecordsAction() {
        var _this7 = this;

        return _asyncToGenerator(function* () {
            try {
                const userId = yield _this7.getLoginUserId();
                const page = parseInt(_this7.get('page') || 1);
                const pageSize = parseInt(_this7.get('pageSize') || 20);
                const startDate = _this7.get('startDate') || '';
                const endDate = _this7.get('endDate') || '';
                const actionType = _this7.get('actionType') || ''; // all, browsed, ordered

                console.log('=== 获取分享记录 ===');
                console.log('推广员ID:', userId, '页码:', page, '筛选类型:', actionType);

                // 构建查询条件
                let whereCondition = `v.promoter_user_id = ${userId}`;

                // 时间筛选
                if (startDate && endDate) {
                    const startTime = parseInt(new Date(startDate).getTime() / 1000);
                    const endTime = parseInt(new Date(endDate).getTime() / 1000);
                    whereCondition += ` AND v.visit_time BETWEEN ${startTime} AND ${endTime}`;
                }

                // 构建主查询SQL
                let sql = `
                SELECT
                    v.id,
                    v.promoter_user_id,
                    v.visitor_user_id,
                    v.goods_id,
                    v.visit_time,
                    v.share_source,
                    v.ip_address,
                    v.is_new_visitor,
                    CASE
                        WHEN o.id IS NOT NULL THEN 'ordered'
                        ELSE 'browsed'
                    END as user_action,
                    o.id as order_record_id,
                    o.order_id,
                    o.order_amount,
                    o.commission_amount,
                    o.status as order_status,
                    g.name as goods_name,
                    g.list_pic_url as goods_image,
                    u.nickname as visitor_nickname
                FROM hiolabs_promotion_visits v
                LEFT JOIN hiolabs_promotion_orders o
                    ON v.visitor_user_id = o.buyer_user_id
                    AND v.goods_id = o.goods_id
                    AND v.promoter_user_id = o.promoter_user_id
                LEFT JOIN hiolabs_goods g ON v.goods_id = g.id
                LEFT JOIN hiolabs_user u ON v.visitor_user_id = u.id
                WHERE ${whereCondition}
            `;

                // 根据行为类型筛选
                if (actionType === 'browsed') {
                    sql += ` HAVING user_action = 'browsed'`;
                } else if (actionType === 'ordered') {
                    sql += ` HAVING user_action = 'ordered'`;
                }

                sql += ` ORDER BY v.visit_time DESC`;

                // 分页查询
                const offset = (page - 1) * pageSize;
                const limitSql = sql + ` LIMIT ${offset}, ${pageSize}`;

                // 执行查询
                const records = yield _this7.model().query(limitSql);

                // 查询总数
                const countSql = `SELECT COUNT(*) as total FROM (${sql}) as temp`;
                const countResult = yield _this7.model().query(countSql);
                const total = countResult[0].total;

                // 格式化数据
                const formattedRecords = records.map(function (record) {
                    return {
                        id: record.id,
                        visitorUserId: record.visitor_user_id,
                        visitorNickname: record.visitor_nickname || '匿名用户',
                        goodsId: record.goods_id,
                        goodsName: record.goods_name,
                        goodsImage: record.goods_image,
                        shareSource: record.share_source,
                        visitTime: record.visit_time,
                        visitTimeText: _this7.formatTime(record.visit_time),
                        ipAddress: record.ip_address,
                        isNewVisitor: record.is_new_visitor === 1,
                        userAction: record.user_action,
                        userActionText: record.user_action === 'ordered' ? '已下单' : '仅浏览',
                        orderInfo: record.user_action === 'ordered' ? {
                            orderId: record.order_id,
                            orderAmount: parseFloat(record.order_amount || 0),
                            commissionAmount: parseFloat(record.commission_amount || 0),
                            orderStatus: record.order_status
                        } : null
                    };
                });

                // 统计数据
                const statsResult = yield _this7.getShareRecordsStats(userId, startDate, endDate);

                console.log(`✅ 查询完成: 共${total}条记录`);

                return _this7.success({
                    records: formattedRecords,
                    pagination: {
                        page: page,
                        pageSize: pageSize,
                        total: total,
                        totalPages: Math.ceil(total / pageSize)
                    },
                    stats: statsResult
                });
            } catch (error) {
                console.error('获取分享记录失败:', error);
                return _this7.fail('获取分享记录失败: ' + error.message);
            }
        })();
    }

    /**
     * 获取分享记录统计数据
     */
    getShareRecordsStats(userId, startDate = '', endDate = '') {
        var _this8 = this;

        return _asyncToGenerator(function* () {
            try {
                let whereCondition = `v.promoter_user_id = ${userId}`;

                // 时间筛选
                if (startDate && endDate) {
                    const startTime = parseInt(new Date(startDate).getTime() / 1000);
                    const endTime = parseInt(new Date(endDate).getTime() / 1000);
                    whereCondition += ` AND v.visit_time BETWEEN ${startTime} AND ${endTime}`;
                }

                const statsSql = `
                SELECT
                    COUNT(v.id) as total_visits,
                    COUNT(DISTINCT v.visitor_user_id) as unique_visitors,
                    COUNT(o.id) as total_orders,
                    COALESCE(SUM(o.order_amount), 0) as total_order_amount,
                    COALESCE(SUM(o.commission_amount), 0) as total_commission,
                    ROUND(COUNT(o.id) * 100.0 / COUNT(v.id), 2) as conversion_rate
                FROM hiolabs_promotion_visits v
                LEFT JOIN hiolabs_promotion_orders o
                    ON v.visitor_user_id = o.buyer_user_id
                    AND v.goods_id = o.goods_id
                    AND v.promoter_user_id = o.promoter_user_id
                WHERE ${whereCondition}
            `;

                const statsResult = yield _this8.model().query(statsSql);
                const stats = statsResult[0];

                return {
                    totalVisits: parseInt(stats.total_visits), // 总访问次数
                    uniqueVisitors: parseInt(stats.unique_visitors), // 独立访客数
                    totalOrders: parseInt(stats.total_orders), // 总下单数
                    browsedOnly: parseInt(stats.total_visits) - parseInt(stats.total_orders), // 仅浏览数
                    totalOrderAmount: parseFloat(stats.total_order_amount), // 总订单金额
                    totalCommission: parseFloat(stats.total_commission), // 总佣金
                    conversionRate: parseFloat(stats.conversion_rate) // 转化率
                };
            } catch (error) {
                console.error('获取统计数据失败:', error);
                return {
                    totalVisits: 0,
                    uniqueVisitors: 0,
                    totalOrders: 0,
                    browsedOnly: 0,
                    totalOrderAmount: 0,
                    totalCommission: 0,
                    conversionRate: 0
                };
            }
        })();
    }

    /**
     * 获取推广数据中心信息
     * GET /api/promotion/dataCenter
     */
    dataCenterAction() {
        var _this9 = this;

        return _asyncToGenerator(function* () {
            try {
                const userId = yield _this9.getLoginUserId();
                const timeFilter = _this9.get('timeFilter') || 'month'; // today, week, month, all

                console.log('=== 获取推广数据中心信息 ===');
                console.log('用户ID:', userId, '时间筛选:', timeFilter);

                // 获取时间范围
                const timeRange = _this9.getTimeRange(timeFilter);

                // 并行获取各种数据
                const [promotionData, currentData, teamData, rankingList, recentOrders] = yield Promise.all([_this9.getPromotionOverview(userId), _this9.getCurrentPeriodData(userId, timeRange), _this9.getTeamData(userId), _this9.getRankingData(userId), _this9.getRecentOrders(userId)]);

                console.log('✅ 数据中心信息获取完成');

                return _this9.success({
                    promotionData,
                    currentData,
                    teamData,
                    rankingList,
                    recentOrders,
                    timeFilter,
                    timePeriodText: _this9.getTimePeriodText(timeFilter)
                });
            } catch (error) {
                console.error('获取推广数据中心信息失败:', error);
                return _this9.fail('获取数据失败: ' + error.message);
            }
        })();
    }

    /**
     * 获取推广总览数据
     */
    getPromotionOverview(userId) {
        var _this10 = this;

        return _asyncToGenerator(function* () {
            try {
                // 获取用户佣金信息
                const commissionInfo = yield _this10.model('hiolabs_user_commission').where({
                    user_id: userId
                }).find();

                // 获取推广订单统计
                const orderStats = yield _this10.model('hiolabs_promotion_orders').where({
                    promoter_user_id: userId,
                    status: ['!=', 'cancelled']
                }).field('COUNT(*) as total_orders, SUM(order_amount) as total_amount, SUM(commission_amount) as total_commission').find();

                // 获取团队人数（直接下级 + 间接下级）
                const teamCount = yield _this10.getTeamMemberCount(userId);

                return {
                    totalOrders: parseInt(orderStats.total_orders || 0),
                    totalAmount: parseFloat(orderStats.total_amount || 0).toFixed(2),
                    totalCommission: parseFloat(commissionInfo.total_commission || 0).toFixed(2),
                    teamCount: teamCount
                };
            } catch (error) {
                console.error('获取推广总览数据失败:', error);
                return {
                    totalOrders: 0,
                    totalAmount: '0.00',
                    totalCommission: '0.00',
                    teamCount: 0
                };
            }
        })();
    }

    /**
     * 获取当前时间段数据
     */
    getCurrentPeriodData(userId, timeRange) {
        var _this11 = this;

        return _asyncToGenerator(function* () {
            try {
                const whereCondition = {
                    promoter_user_id: userId,
                    status: ['!=', 'cancelled']
                };

                if (timeRange.start && timeRange.end) {
                    whereCondition.create_time = ['between', [timeRange.start, timeRange.end]];
                }

                // 获取订单数据
                const orderStats = yield _this11.model('hiolabs_promotion_orders').where(whereCondition).field('COUNT(*) as orders, SUM(order_amount) as amount, SUM(commission_amount) as commission').find();

                // 获取访问数据
                const visitStats = yield _this11.model('hiolabs_promotion_visits').where({
                    promoter_user_id: userId,
                    visit_time: timeRange.start && timeRange.end ? ['between', [timeRange.start, timeRange.end]] : ['>', 0]
                }).field('COUNT(DISTINCT visitor_user_id) as visitors').find();

                return {
                    orders: parseInt(orderStats.orders || 0),
                    amount: parseFloat(orderStats.amount || 0).toFixed(2),
                    commission: parseFloat(orderStats.commission || 0).toFixed(2),
                    visitors: parseInt(visitStats.visitors || 0)
                };
            } catch (error) {
                console.error('获取当前时间段数据失败:', error);
                return {
                    orders: 0,
                    amount: '0.00',
                    commission: '0.00',
                    visitors: 0
                };
            }
        })();
    }

    /**
     * 获取团队数据
     */
    getTeamData(userId) {
        var _this12 = this;

        return _asyncToGenerator(function* () {
            try {
                // 获取直接下级数量
                const directCount = yield _this12.model('hiolabs_promotion_orders').where({
                    parent_promoter_user_id: userId
                }).field('COUNT(DISTINCT promoter_user_id) as count').find();

                // 获取团队总人数（包括间接下级）
                const totalCount = yield _this12.getTeamMemberCount(userId);

                // 获取团队订单数据
                const teamOrderStats = yield _this12.getTeamOrderStats(userId);

                return {
                    directCount: parseInt(directCount.count || 0),
                    totalCount: totalCount,
                    teamOrders: teamOrderStats.orders,
                    teamCommission: teamOrderStats.commission
                };
            } catch (error) {
                console.error('获取团队数据失败:', error);
                return {
                    directCount: 0,
                    totalCount: 0,
                    teamOrders: 0,
                    teamCommission: '0.00'
                };
            }
        })();
    }

    /**
     * 获取排行榜数据
     */
    getRankingData(userId) {
        var _this13 = this;

        return _asyncToGenerator(function* () {
            try {
                // 获取本月推广排行榜（前5名）
                const currentMonth = _this13.getCurrentMonthRange();

                const rankingList = yield _this13.model('hiolabs_promotion_orders').alias('po').join({
                    table: 'hiolabs_user',
                    join: 'left',
                    as: 'u',
                    on: ['po.promoter_user_id', 'u.id']
                }).where({
                    'po.status': ['!=', 'cancelled'],
                    'po.create_time': ['between', [currentMonth.start, currentMonth.end]]
                }).field('po.promoter_user_id as user_id, u.nickname, u.avatar, COUNT(*) as order_count, SUM(po.commission_amount) as commission').group('po.promoter_user_id').order('commission DESC').limit(5).select();

                return rankingList.map(function (item) {
                    return {
                        id: item.user_id,
                        nickname: _this13.decodeNickname(item.nickname) || `用户${item.user_id}`,
                        avatar: item.avatar || '',
                        orderCount: parseInt(item.order_count),
                        commission: parseFloat(item.commission || 0).toFixed(2)
                    };
                });
            } catch (error) {
                console.error('获取排行榜数据失败:', error);
                return [];
            }
        })();
    }

    /**
     * 获取最近订单
     */
    getRecentOrders(userId) {
        var _this14 = this;

        return _asyncToGenerator(function* () {
            try {
                const recentOrders = yield _this14.model('hiolabs_promotion_orders').where({
                    promoter_user_id: userId,
                    status: ['!=', 'cancelled']
                }).field('id, goods_name, order_amount, commission_amount, create_time').order('create_time DESC').limit(5).select();

                return recentOrders.map(function (order) {
                    return {
                        id: order.id,
                        goodsName: order.goods_name,
                        orderAmount: parseFloat(order.order_amount).toFixed(2),
                        commission: parseFloat(order.commission_amount).toFixed(2),
                        createTime: _this14.formatTime(order.create_time)
                    };
                });
            } catch (error) {
                console.error('获取最近订单失败:', error);
                return [];
            }
        })();
    }

    /**
     * 格式化时间戳
     */
    formatTime(timestamp) {
        const date = new Date(timestamp * 1000);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hour = String(date.getHours()).padStart(2, '0');
        const minute = String(date.getMinutes()).padStart(2, '0');

        return `${year}-${month}-${day} ${hour}:${minute}`;
    }

    /**
     * 获取时间范围
     */
    getTimeRange(timeFilter) {
        const now = new Date();
        let start, end;

        switch (timeFilter) {
            case 'today':
                start = parseInt(new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime() / 1000);
                end = parseInt(new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59).getTime() / 1000);
                break;
            case 'week':
                const weekStart = new Date(now);
                weekStart.setDate(now.getDate() - now.getDay());
                weekStart.setHours(0, 0, 0, 0);
                start = parseInt(weekStart.getTime() / 1000);
                end = parseInt(now.getTime() / 1000);
                break;
            case 'month':
                start = parseInt(new Date(now.getFullYear(), now.getMonth(), 1).getTime() / 1000);
                end = parseInt(now.getTime() / 1000);
                break;
            case 'all':
            default:
                start = null;
                end = null;
                break;
        }

        return { start, end };
    }

    /**
     * 获取当前月份范围
     */
    getCurrentMonthRange() {
        const now = new Date();
        const start = parseInt(new Date(now.getFullYear(), now.getMonth(), 1).getTime() / 1000);
        const end = parseInt(new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59).getTime() / 1000);
        return { start, end };
    }

    /**
     * 获取时间段文本
     */
    getTimePeriodText(timeFilter) {
        const textMap = {
            'today': '今日',
            'week': '本周',
            'month': '本月',
            'all': '全部'
        };
        return textMap[timeFilter] || '本月';
    }

    /**
     * 获取团队成员数量（递归计算）
     */
    getTeamMemberCount(userId) {
        var _this15 = this;

        return _asyncToGenerator(function* () {
            try {
                // 获取直接下级
                const directMembers = yield _this15.model('hiolabs_promotion_orders').where({
                    parent_promoter_user_id: userId
                }).field('DISTINCT promoter_user_id as user_id').select();

                let totalCount = directMembers.length;

                // 递归获取间接下级
                for (let member of directMembers) {
                    const subCount = yield _this15.getTeamMemberCount(member.user_id);
                    totalCount += subCount;
                }

                return totalCount;
            } catch (error) {
                console.error('获取团队成员数量失败:', error);
                return 0;
            }
        })();
    }

    /**
     * 获取团队订单统计
     */
    getTeamOrderStats(userId) {
        var _this16 = this;

        return _asyncToGenerator(function* () {
            try {
                // 获取所有团队成员ID
                const teamMemberIds = yield _this16.getAllTeamMemberIds(userId);

                if (teamMemberIds.length === 0) {
                    return { orders: 0, commission: '0.00' };
                }

                // 获取团队订单统计
                const stats = yield _this16.model('hiolabs_promotion_orders').where({
                    promoter_user_id: ['IN', teamMemberIds],
                    status: ['!=', 'cancelled']
                }).field('COUNT(*) as orders, SUM(commission_amount) as commission').find();

                return {
                    orders: parseInt(stats.orders || 0),
                    commission: parseFloat(stats.commission || 0).toFixed(2)
                };
            } catch (error) {
                console.error('获取团队订单统计失败:', error);
                return { orders: 0, commission: '0.00' };
            }
        })();
    }

    /**
     * 获取所有团队成员ID（递归）
     */
    getAllTeamMemberIds(userId, visited = new Set()) {
        var _this17 = this;

        return _asyncToGenerator(function* () {
            if (visited.has(userId)) {
                return [];
            }
            visited.add(userId);

            try {
                // 获取直接下级
                const directMembers = yield _this17.model('hiolabs_promotion_orders').where({
                    parent_promoter_user_id: userId
                }).field('DISTINCT promoter_user_id as user_id').select();

                let allMemberIds = directMembers.map(function (m) {
                    return m.user_id;
                });

                // 递归获取间接下级
                for (let member of directMembers) {
                    const subMemberIds = yield _this17.getAllTeamMemberIds(member.user_id, visited);
                    allMemberIds = allMemberIds.concat(subMemberIds);
                }

                return allMemberIds;
            } catch (error) {
                console.error('获取团队成员ID失败:', error);
                return [];
            }
        })();
    }

    /**
     * 解码昵称（base64）
     */
    decodeNickname(nickname) {
        if (!nickname) return null;
        try {
            return Buffer.from(nickname, 'base64').toString('utf8');
        } catch (error) {
            return nickname;
        }
    }
};