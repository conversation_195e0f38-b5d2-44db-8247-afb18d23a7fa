<template>
  <div class="flash-sale-multi-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>限时秒杀管理（多商品轮次）</h2>
      <button @click="openCreateModal" class="btn btn-primary">
        创建新轮次
      </button>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <h3>总轮次</h3>
        <p class="stat-number">{{ statistics.totalRounds || 0 }}</p>
      </div>
      <div class="stat-card">
        <h3>进行中</h3>
        <p class="stat-number active">{{ statistics.activeRounds || 0 }}</p>
      </div>
      <div class="stat-card">
        <h3>即将开始</h3>
        <p class="stat-number upcoming">{{ statistics.upcomingRounds || 0 }}</p>
      </div>
      <div class="stat-card">
        <h3>总销售额</h3>
        <p class="stat-number">¥{{ (statistics.totalSales || 0).toFixed(2) }}</p>
      </div>
    </div>

    <!-- 当前轮次 -->
    <div class="current-rounds" v-if="currentRounds.current && currentRounds.current.length > 0">
      <h3>当前进行中的轮次</h3>
      <div class="round-list">
        <div v-for="round in currentRounds.current" :key="round.id" class="round-item active">
          <div class="round-info">
            <h4>{{ round.round_name }} (轮次 #{{ round.round_number }})</h4>
            <p>商品数量: {{ round.goods_count }}</p>
            <p>剩余时间: {{ formatTime(round.countdown) }}</p>
          </div>
          <div class="goods-preview">
            <div v-for="goods in round.goods_list.slice(0, 3)" :key="goods.id" class="goods-item">
              <img :src="goods.goods_image" :alt="goods.goods_name" />
              <span>{{ goods.goods_name }}</span>
              <span class="price">¥{{ goods.flash_price }}</span>
            </div>
            <span v-if="round.goods_list.length > 3" class="more-goods">
              +{{ round.goods_list.length - 3 }}个商品
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 轮次列表 -->
    <div class="rounds-table">
      <h3>轮次列表</h3>
      <table class="table">
        <thead>
          <tr>
            <th>轮次编号</th>
            <th>轮次名称</th>
            <th>商品数量</th>
            <th>总库存</th>
            <th>已售出</th>
            <th>开始时间</th>
            <th>结束时间</th>
            <th>状态</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="round in roundsList.data" :key="round.id">
            <td>#{{ round.round_number }}</td>
            <td>{{ round.round_name }}</td>
            <td>{{ round.goods_count }}</td>
            <td>{{ round.total_stock }}</td>
            <td>{{ round.total_sold }}</td>
            <td>{{ formatDateTime(round.start_time) }}</td>
            <td>{{ formatDateTime(round.end_time) }}</td>
            <td>
              <span :class="'status-' + round.status">
                {{ getStatusText(round.status) }}
              </span>
            </td>
            <td>
              <div class="round-actions">
                <button @click="viewRoundDetails(round)" class="btn btn-info btn-sm">查看详情</button>
                <button
                  v-if="round.status === 'upcoming' || round.status === 'active'"
                  @click="closeRound(round)"
                  class="btn btn-warning btn-sm"
                >
                  关闭轮次
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 创建轮次模态框 -->
    <div v-if="showAddModal" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>创建新轮次</h3>
          <button @click="closeModal" class="close-btn">&times;</button>
        </div>
        
        <div class="modal-body">
          <!-- 整点秒杀时间设置 -->
          <div class="hourly-flash-settings">
            <div class="setting-header">
              <h4>整点秒杀设置</h4>
              <p class="setting-description">每小时整点开始，持续40分钟，24小时不间断</p>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>活动开始日期 *</label>
                <input
                  v-model="newRound.start_date"
                  type="date"
                  class="form-control"
                  required
                />
              </div>
              <div class="form-group">
                <label>活动结束日期 *</label>
                <input
                  v-model="newRound.end_date"
                  type="date"
                  class="form-control"
                  :min="newRound.start_date"
                  required
                />
              </div>
            </div>

            <!-- 自动生成的轮次名称预览 -->
            <div v-if="generatedRoundName" class="round-name-preview">
              <h5>轮次名称：</h5>
              <div class="name-display">{{ generatedRoundName }}</div>
            </div>

            <!-- 时段预览 -->
            <div v-if="hourlySlotPreview.length > 0" class="slot-preview">
              <h5>将生成以下秒杀时段：</h5>
              <div class="slot-list-container">
                <div class="slot-list">
                  <div v-for="(slot, index) in hourlySlotPreview.slice(0, 10)" :key="index" class="slot-item">
                    <span class="slot-number">第{{ index + 1 }}场</span>
                    <span class="slot-time">{{ formatSlotTime(slot.start) }} - {{ formatSlotTime(slot.end) }}</span>
                    <span class="slot-duration">40分钟</span>
                    <span v-if="isSlotInPast(slot.start)" class="slot-status past">已过期</span>
                    <span v-else-if="isSlotActive(slot.start, slot.end)" class="slot-status active">进行中</span>
                    <span v-else class="slot-status upcoming">待开始</span>
                  </div>
                  <div v-if="hourlySlotPreview.length > 10" class="more-slots">
                    ... 还有 {{ hourlySlotPreview.length - 10 }} 个时段
                  </div>
                </div>
              </div>
              <div class="slot-summary">
                <span>共 {{ hourlySlotPreview.length }} 个时段</span>
                <span class="valid-slots">（有效时段：{{ validSlotsCount }} 个）</span>
              </div>
            </div>
          </div>

          <!-- 商品选择 -->
          <div class="form-group">
            <label>选择商品 * (点击商品卡片进行选择)</label>

            <!-- 加载状态 -->
            <div v-if="loadingGoods" class="loading-state">
              <i class="loading-icon">⏳</i>
              <span>正在加载商品列表...</span>
            </div>

            <!-- 商品列表 -->
            <div v-else-if="goodsList && goodsList.length > 0" class="goods-selection-grid">
              <div
                v-for="goods in goodsList"
                :key="goods.id"
                class="goods-card"
                :class="{
                  'selected': isGoodsSelected(goods.id),
                  'disabled': !goods.can_select
                }"
                @click="toggleGoods(goods)"
              >
                <!-- 商品基本信息 -->
                <div class="goods-card-header">
                  <div class="goods-image-container">
                    <img :src="goods.list_pic_url" :alt="goods.name" class="goods-card-image" />
                    <div v-if="isGoodsSelected(goods.id)" class="selected-badge">
                      <i class="checkmark">✓</i>
                    </div>
                    <div v-if="!goods.can_select" class="disabled-overlay">
                      <span>已参与其他秒杀</span>
                    </div>
                  </div>
                  <div class="goods-card-info">
                    <h4 class="goods-name">{{ goods.name }}</h4>
                    <p class="original-price">原价: ¥{{ goods.retail_price }}</p>
                    <div v-if="!goods.can_select" class="warning-text">
                      <i class="warning-icon">⚠</i>
                      <span>已参与其他秒杀活动</span>
                    </div>
                  </div>
                </div>

                <!-- 秒杀设置面板 -->
                <div v-if="isGoodsSelected(goods.id)" class="goods-settings-panel">
                  <div class="settings-title">秒杀设置</div>
                  <div class="settings-grid">
                    <div class="setting-item full-width">
                      <label>折扣设置</label>
                      <div class="discount-setting">
                        <div class="discount-input-group">
                          <input
                            v-model.number="getSelectedGoods(goods.id).discount_rate"
                            type="number"
                            step="1"
                            min="10"
                            max="90"
                            class="discount-input"
                            @input="updateFlashPriceByDiscount(goods.id)"
                            @click.stop
                          />
                          <span class="discount-unit">% OFF</span>
                        </div>
                        <div class="price-preview">
                          原价: ¥{{ parseFloat(goods.retail_price).toFixed(2) }} →
                          秒杀价: ¥{{ getSelectedGoods(goods.id).flash_price }}
                        </div>
                        <div class="price-range-hint" v-if="goods.price_range">
                          商品价格区间: {{ goods.price_range }}
                        </div>
                      </div>
                    </div>

                    <div class="setting-item">
                      <label>秒杀库存</label>
                      <input
                        v-model.number="getSelectedGoods(goods.id).stock"
                        type="number"
                        min="1"
                        max="9999"
                        class="stock-input"
                        @click.stop
                      />
                    </div>

                    <div class="setting-item">
                      <label>限购数量</label>
                      <input
                        v-model.number="getSelectedGoods(goods.id).limit_quantity"
                        type="number"
                        min="1"
                        max="99"
                        class="limit-input"
                        @click.stop
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态显示 -->
            <div v-else class="empty-state">
              <div class="empty-icon">📦</div>
              <p class="empty-text">暂无可选商品</p>
              <p class="empty-hint">请确保有上架的商品，且未参与其他秒杀活动</p>
              <button @click="loadGoodsList" class="btn btn-secondary btn-sm">重新加载</button>
            </div>

            <!-- 选择提示 -->
            <div class="selection-hint">
              <p v-if="newRound.goods_list.length === 0" class="hint-text">
                <i class="info-icon">ℹ</i>
                请点击商品卡片选择参与秒杀的商品，可以选择多个商品
              </p>
              <p v-else class="selected-count">
                已选择 <strong>{{ newRound.goods_list.length }}</strong> 个商品
              </p>
            </div>
          </div>

          <!-- 已选商品汇总 -->
          <div v-if="newRound.goods_list.length > 0" class="selected-summary">
            <h4>已选择 {{ newRound.goods_list.length }} 个商品</h4>
            <div class="summary-list">
              <div v-for="goods in newRound.goods_list" :key="goods.goods_id" class="summary-item">
                <span>{{ goods.goods_name }}</span>
                <span>¥{{ goods.flash_price }} ({{ calculateDiscountRate(goods.original_price, goods.flash_price) }}% OFF)</span>
                <span>库存: {{ goods.stock }}</span>
                <button @click="removeGoods(goods.goods_id)" class="remove-btn">移除</button>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button @click="closeModal" class="btn btn-secondary">取消</button>
          <button
            @click="createRound"
            class="btn btn-primary"
            :disabled="!canCreateRound || isCreating"
          >
            {{ isCreating ? `正在创建... (${creationProgress.current}/${creationProgress.total})` : '创建整点秒杀轮次' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 轮次详情模态框 -->
    <div v-if="showDetailModal" class="modal-overlay" @click="showDetailModal = false">
      <div class="modal-content large" @click.stop>
        <div class="modal-header">
          <h3>轮次详情 - {{ selectedRound.round_name }}</h3>
          <button @click="showDetailModal = false" class="close-btn">&times;</button>
        </div>
        
        <div class="modal-body">
          <div class="round-details">
            <div class="detail-section">
              <h4>基本信息</h4>
              <p>轮次编号: #{{ selectedRound.round_number }}</p>
              <p>开始时间: {{ formatDateTime(selectedRound.start_time) }}</p>
              <p>结束时间: {{ formatDateTime(selectedRound.end_time) }}</p>
              <p>状态: {{ getStatusText(selectedRound.status) }}</p>
            </div>
            
            <div class="detail-section">
              <h4>商品列表</h4>
              <table class="table">
                <thead>
                  <tr>
                    <th>商品</th>
                    <th>原价</th>
                    <th>秒杀价</th>
                    <th>折扣</th>
                    <th>库存</th>
                    <th>已售</th>
                    <th>限购</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="goods in selectedRound.goods_list" :key="goods.id">
                    <td>
                      <div class="goods-cell">
                        <img :src="goods.goods_image" :alt="goods.goods_name" />
                        <span>{{ goods.goods_name }}</span>
                      </div>
                    </td>
                    <td>¥{{ goods.original_price }}</td>
                    <td>¥{{ goods.flash_price }}</td>
                    <td>{{ goods.discount_rate }}%</td>
                    <td>{{ goods.stock }}</td>
                    <td>{{ goods.sold_count }}</td>
                    <td>{{ goods.limit_quantity }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FlashSaleMultiPage',
  data() {
    return {
      statistics: {},
      currentRounds: { current: [], upcoming: [] },
      roundsList: { data: [], count: 0 },
      goodsList: [],
      loadingGoods: false,
      showAddModal: false,
      showDetailModal: false,
      selectedRound: {},
      isCreating: false,
      newRound: {
        start_date: '',
        end_date: '',
        goods_list: []
      },
      refreshTimer: null,
      creationProgress: {
        current: 0,
        total: 0
      }
    };
  },
  
  computed: {
    canCreateRound() {
      const hasStartDate = this.newRound.start_date;
      const hasEndDate = this.newRound.end_date;
      const hasGoods = this.newRound.goods_list.length > 0;
      const goodsValid = this.newRound.goods_list.every(g => g.flash_price > 0 && g.stock > 0);
      const dateValid = hasStartDate && hasEndDate && new Date(this.newRound.start_date) <= new Date(this.newRound.end_date);

      console.log('canCreateRound检查:', {
        hasStartDate,
        hasEndDate,
        hasGoods,
        goodsValid,
        dateValid,
        goodsList: this.newRound.goods_list
      });

      return hasStartDate && hasEndDate && hasGoods && goodsValid && dateValid;
    },

    // 自动生成轮次名称
    generatedRoundName() {
      if (!this.newRound.start_date || !this.newRound.end_date) {
        return '';
      }

      const startDate = new Date(this.newRound.start_date);
      const endDate = new Date(this.newRound.end_date);

      if (startDate.getTime() === endDate.getTime()) {
        // 单日活动
        const dateStr = startDate.toLocaleDateString('zh-CN', {
          month: 'long',
          day: 'numeric'
        });
        return `${dateStr}整点秒杀`;
      } else {
        // 多日活动
        const startStr = startDate.toLocaleDateString('zh-CN', {
          month: 'long',
          day: 'numeric'
        });
        const endStr = endDate.toLocaleDateString('zh-CN', {
          month: 'long',
          day: 'numeric'
        });
        return `${startStr}至${endStr}整点秒杀`;
      }
    },

    // 整点秒杀时段预览（24小时全天候）
    hourlySlotPreview() {
      if (!this.newRound.start_date || !this.newRound.end_date) {
        return [];
      }

      const slots = [];
      const startDate = new Date(this.newRound.start_date);
      const endDate = new Date(this.newRound.end_date);

      // 设置结束日期为当天的23:59:59
      endDate.setHours(23, 59, 59, 999);

      let currentDate = new Date(startDate);
      currentDate.setHours(0, 0, 0, 0); // 从00:00开始

      while (currentDate <= endDate) {
        for (let hour = 0; hour < 24; hour++) {
          const slotStart = new Date(currentDate);
          slotStart.setHours(hour, 0, 0, 0);

          const slotEnd = new Date(currentDate);
          slotEnd.setHours(hour, 40, 0, 0);

          // 检查是否超出结束日期
          if (slotStart > endDate) break;

          slots.push({
            start: slotStart.toISOString().slice(0, 19).replace('T', ' '),
            end: slotEnd.toISOString().slice(0, 19).replace('T', ' '),
            startTime: slotStart,
            endTime: slotEnd
          });
        }

        // 移动到下一天
        currentDate.setDate(currentDate.getDate() + 1);
      }

      return slots;
    },

    // 有效时段数量（未过期的时段）
    validSlotsCount() {
      const now = new Date();
      return this.hourlySlotPreview.filter(slot => new Date(slot.start) > now).length;
    }
  },

  mounted() {
    console.log('FlashSaleMultiPage组件已挂载');
    console.log('初始showAddModal值:', this.showAddModal);
    this.loadData();
    this.startAutoRefresh();
  },

  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
  },

  methods: {
    async loadData() {
      await Promise.all([
        this.loadStatistics(),
        this.loadCurrentRounds(),
        this.loadRoundsList(),
        this.loadGoodsList()
      ]);
    },

    async loadStatistics() {
      try {
        const response = await this.axios.get('flashsalemulti/statistics');
        if (response.data.errno === 0) {
          this.statistics = response.data.data;
        }
      } catch (error) {
        console.error('加载统计数据失败:', error);
      }
    },

    async loadCurrentRounds() {
      try {
        const response = await this.axios.get('flashsalemulti/current');
        if (response.data.errno === 0) {
          this.currentRounds = response.data.data;
        }
      } catch (error) {
        console.error('加载当前轮次失败:', error);
      }
    },

    async loadRoundsList() {
      try {
        const response = await this.axios.get('flashsalemulti/list');
        if (response.data.errno === 0) {
          this.roundsList = response.data.data;
        }
      } catch (error) {
        console.error('加载轮次列表失败:', error);
      }
    },

    async loadGoodsList() {
      try {
        this.loadingGoods = true;
        console.log('开始加载商品列表...');

        const response = await this.axios.get('flashsalemulti/goods');
        console.log('商品列表API响应:', response.data);

        if (response.data.errno === 0) {
          this.goodsList = response.data.data || [];
          console.log('商品列表加载成功，数量:', this.goodsList.length);

          if (this.goodsList.length === 0) {
            this.$message.warning('暂无可选商品，请确保有上架的商品且未参与其他秒杀活动');
          }
        } else {
          console.error('API返回错误:', response.data.errmsg);
          this.$message.error(response.data.errmsg || '加载商品列表失败');
          this.goodsList = [];
        }
      } catch (error) {
        console.error('加载商品列表失败:', error);
        this.$message.error('网络错误，请检查服务器连接');
        this.goodsList = [];
      } finally {
        this.loadingGoods = false;
      }
    },

    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        this.loadCurrentRounds();
        this.loadStatistics();
      }, 30000); // 30秒刷新一次
    },

    isGoodsSelected(goodsId) {
      return this.newRound.goods_list.some(g => g.goods_id === goodsId);
    },

    getSelectedGoods(goodsId) {
      return this.newRound.goods_list.find(g => g.goods_id === goodsId);
    },

    toggleGoods(goods) {
      if (!goods.can_select) return;

      if (this.isGoodsSelected(goods.id)) {
        this.removeGoods(goods.id);
      } else {
        const originalPrice = parseFloat(goods.retail_price) || 0;
        const defaultDiscount = 20; // 默认20%折扣
        const flashPrice = Math.round(originalPrice * (100 - defaultDiscount) / 100 * 100) / 100;

        this.newRound.goods_list.push({
          goods_id: goods.id,
          goods_name: goods.name,
          original_price: originalPrice,
          flash_price: flashPrice,
          discount_rate: defaultDiscount,
          stock: 100,
          limit_quantity: 1
        });
      }
    },

    removeGoods(goodsId) {
      const index = this.newRound.goods_list.findIndex(g => g.goods_id === goodsId);
      if (index > -1) {
        this.newRound.goods_list.splice(index, 1);
      }
    },

    calculateDiscountRate(originalPrice, flashPrice) {
      if (!originalPrice || originalPrice <= 0 || !flashPrice || flashPrice <= 0) return 0;
      const rate = Math.round((1 - flashPrice / originalPrice) * 100);
      return isNaN(rate) ? 0 : rate;
    },

    updateFlashPriceByDiscount(goodsId) {
      const selectedGoods = this.getSelectedGoods(goodsId);
      if (selectedGoods && selectedGoods.original_price > 0) {
        const discountRate = selectedGoods.discount_rate || 0;
        const flashPrice = Math.round(selectedGoods.original_price * (100 - discountRate) / 100 * 100) / 100;
        selectedGoods.flash_price = flashPrice;
      }
    },

    getCurrentDateTime() {
      const now = new Date();
      now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
      return now.toISOString().slice(0, 16);
    },

    getCurrentDate() {
      const now = new Date();
      return now.toISOString().slice(0, 10);
    },

    openCreateModal() {
      console.log('点击创建新轮次按钮');
      // 设置默认日期为今天
      const today = new Date().toISOString().slice(0, 10);
      this.newRound.start_date = today;
      this.newRound.end_date = today;
      this.showAddModal = true;
      console.log('showAddModal设置为:', this.showAddModal);
    },

    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return '';
      const date = new Date(dateTimeStr);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    formatSlotTime(dateTimeStr) {
      if (!dateTimeStr) return '';
      const date = new Date(dateTimeStr);
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    isSlotInPast(startTime) {
      const now = new Date();
      const slotStart = new Date(startTime);
      return slotStart < now;
    },

    isSlotActive(startTime, endTime) {
      const now = new Date();
      const slotStart = new Date(startTime);
      const slotEnd = new Date(endTime);
      return now >= slotStart && now <= slotEnd;
    },

    async createRound() {
      if (!this.canCreateRound) {
        this.$message.error('请完善轮次信息');
        return;
      }

      // 生成整点秒杀时段数据
      const hourlySlots = this.hourlySlotPreview;
      const now = new Date();

      // 过滤掉已过期的时段
      const validSlots = hourlySlots.filter(slot => new Date(slot.start) > now);

      if (validSlots.length === 0) {
        this.$message.error('所选时间段内没有有效的秒杀时段');
        return;
      }

      // 如果轮次数量过多，询问用户确认
      if (validSlots.length > 50) {
        const confirmed = confirm(`将要创建${validSlots.length}个轮次，这可能需要较长时间。是否继续？`);
        if (!confirmed) {
          return;
        }
      }

      try {
        this.isCreating = true;
        this.creationProgress.current = 0;
        this.creationProgress.total = validSlots.length;

        let createdCount = 0;
        let failedCount = 0;
        const failedReasons = [];

        // 批量处理，每次处理10个轮次
        const batchSize = 10;
        for (let batchStart = 0; batchStart < validSlots.length; batchStart += batchSize) {
          const batchEnd = Math.min(batchStart + batchSize, validSlots.length);
          const batch = validSlots.slice(batchStart, batchEnd);

          // 并行创建当前批次的轮次
          const batchPromises = batch.map(async (slot, batchIndex) => {
            const globalIndex = batchStart + batchIndex;

            try {
              const roundData = {
                round_name: `${this.generatedRoundName} - 第${globalIndex + 1}场`,
                start_time: slot.start,
                end_time: slot.end,
                is_hourly_flash: true,
                slot_index: globalIndex + 1,
                total_slots: validSlots.length,
                goods_list: this.newRound.goods_list.map(goods => ({
                  goods_id: goods.goods_id,
                  goods_name: goods.goods_name,
                  goods_image: goods.goods_image,
                  original_price: goods.original_price,
                  flash_price: goods.flash_price,
                  stock: goods.stock,
                  discount_rate: goods.discount_rate
                }))
              };

              const response = await this.axios.post('flashsalemulti/create', roundData);

              if (response.data.errno === 0) {
                return { success: true, index: globalIndex + 1 };
              } else {
                return { success: false, index: globalIndex + 1, error: response.data.errmsg };
              }
            } catch (error) {
              return { success: false, index: globalIndex + 1, error: error.message };
            }
          });

          // 等待当前批次完成
          const batchResults = await Promise.all(batchPromises);

          // 统计结果
          batchResults.forEach(result => {
            this.creationProgress.current++;
            if (result.success) {
              createdCount++;
            } else {
              failedCount++;
              if (failedReasons.length < 5) { // 只记录前5个错误
                failedReasons.push(`第${result.index}场: ${result.error}`);
              }
            }
          });

          // 短暂延迟，避免服务器压力过大
          if (batchEnd < validSlots.length) {
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        }

        // 显示结果
        if (createdCount > 0) {
          let message = `成功创建${createdCount}个整点秒杀轮次`;
          if (failedCount > 0) {
            message += `，${failedCount}个失败`;
            if (failedReasons.length > 0) {
              console.warn('创建失败的轮次:', failedReasons);
              message += `\n主要错误: ${failedReasons[0]}`;
            }
          }
          this.$message.success(message);
          this.closeModal();
          this.loadData();
        } else {
          let errorMessage = '所有轮次创建失败';
          if (failedReasons.length > 0) {
            errorMessage += `\n错误信息: ${failedReasons[0]}`;
          }
          this.$message.error(errorMessage);
        }

      } catch (error) {
        console.error('创建整点秒杀轮次失败:', error);
        this.$message.error('创建过程中发生错误: ' + error.message);
      } finally {
        this.isCreating = false;
        this.creationProgress.current = 0;
        this.creationProgress.total = 0;
      }
    },

    closeModal() {
      this.showAddModal = false;
      this.newRound = {
        start_date: '',
        end_date: '',
        goods_list: []
      };
    },

    viewRoundDetails(round) {
      this.selectedRound = round;
      this.showDetailModal = true;
    },

    async closeRound(round) {
      if (!confirm(`确定要关闭轮次"${round.round_name}"吗？关闭后轮次将立即结束。`)) {
        return;
      }

      try {
        this.$message.info('正在关闭轮次...');
        const response = await this.axios.post('flashsalemulti/close', {
          round_id: round.id
        });

        if (response.data.errno === 0) {
          this.$message.success('轮次已关闭');
          this.loadData(); // 重新加载数据
        } else {
          this.$message.error(response.data.errmsg || '关闭失败');
        }
      } catch (error) {
        console.error('关闭轮次失败:', error);
        this.$message.error('关闭失败');
      }
    },



    formatTime(seconds) {
      if (!seconds || seconds <= 0) return '00:00:00';
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    },

    formatDateTime(dateTime) {
      if (!dateTime) return '';
      return new Date(dateTime).toLocaleString('zh-CN');
    },

    getStatusText(status) {
      const statusMap = {
        'upcoming': '即将开始',
        'active': '进行中',
        'ended': '已结束'
      };
      return statusMap[status] || status;
    }
  }
};
</script>

<style scoped>
.flash-sale-multi-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.round-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.round-actions .btn {
  margin: 0;
}

.btn-info {
  background-color: #17a2b8;
  border-color: #17a2b8;
  color: white;
}

.btn-info:hover {
  background-color: #138496;
  border-color: #117a8b;
}

.btn-warning {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background-color: #e0a800;
  border-color: #d39e00;
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
  border-color: #bd2130;
}

/* 整点秒杀设置样式 */
.hourly-flash-settings {
  margin-bottom: 20px;
}

.setting-header {
  margin-bottom: 15px;
}

.setting-header h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.setting-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.time-range-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.time-separator {
  color: #666;
  font-weight: bold;
}

.slot-preview {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
  border: 1px solid #e9ecef;
}

.slot-preview h5 {
  margin: 0 0 10px 0;
  color: #333;
}

.slot-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.slot-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 8px 12px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.slot-number {
  font-weight: bold;
  color: #007bff;
  min-width: 60px;
}

.slot-time {
  flex: 1;
  color: #333;
}

.slot-duration {
  color: #28a745;
  font-size: 12px;
  font-weight: bold;
}

.slot-summary {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #dee2e6;
  text-align: center;
  color: #666;
  font-weight: bold;
}

.valid-slots {
  color: #28a745;
  margin-left: 10px;
}

.round-name-preview {
  margin-bottom: 20px;
}

.round-name-preview h5 {
  margin-bottom: 8px;
  color: #333;
  font-weight: bold;
}

.name-display {
  padding: 10px 15px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 16px;
  font-weight: bold;
  color: #007bff;
}

.slot-list-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.slot-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
}

.slot-status.past {
  background-color: #f8d7da;
  color: #721c24;
}

.slot-status.active {
  background-color: #d4edda;
  color: #155724;
}

.slot-status.upcoming {
  background-color: #d1ecf1;
  color: #0c5460;
}

.more-slots {
  padding: 10px;
  text-align: center;
  color: #666;
  font-style: italic;
  background-color: #f8f9fa;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-card h3 {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
  color: #333;
}

.stat-number.active {
  color: #28a745;
}

.stat-number.upcoming {
  color: #ffc107;
}

/* 当前轮次 */
.current-rounds {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.round-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.round-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
}

.round-item.active {
  border-color: #28a745;
  background-color: #f8fff9;
}

.round-info h4 {
  margin: 0 0 5px 0;
  color: #333;
}

.round-info p {
  margin: 2px 0;
  color: #666;
  font-size: 14px;
}

.goods-preview {
  display: flex;
  align-items: center;
  gap: 10px;
}

.goods-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 80px;
}

.goods-item img {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 4px;
}

.goods-item span {
  font-size: 12px;
  color: #666;
}

.goods-item .price {
  color: #e74c3c;
  font-weight: bold;
}

.more-goods {
  color: #007bff;
  font-size: 12px;
}

/* 表格 */
.rounds-table {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.status-upcoming {
  color: #ffc107;
  font-weight: bold;
}

.status-active {
  color: #28a745;
  font-weight: bold;
}

.status-ended {
  color: #6c757d;
  font-weight: bold;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-content.large {
  max-width: 1000px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #e9ecef;
}

/* 表单 */
.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-row .form-group {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
  color: #333;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-control.small {
  width: 80px;
}

.form-control:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 商品选择网格 */
.goods-selection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  max-height: 500px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background-color: #f8f9fa;
}

/* 商品卡片 */
.goods-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.goods-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  transform: translateY(-2px);
}

.goods-card.selected {
  border-color: #28a745;
  background-color: #f8fff9;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
}

.goods-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #f5f5f5;
  border-color: #dee2e6;
}

.goods-card.disabled:hover {
  transform: none;
  box-shadow: none;
  border-color: #dee2e6;
}

/* 商品卡片头部 */
.goods-card-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}

.goods-image-container {
  position: relative;
  margin-right: 15px;
  flex-shrink: 0;
}

.goods-card-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.selected-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 24px;
  height: 24px;
  background-color: #28a745;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.checkmark {
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.disabled-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* 商品信息 */
.goods-card-info {
  flex: 1;
}

.goods-name {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.original-price {
  margin: 0 0 5px 0;
  color: #666;
  font-size: 14px;
}

.warning-text {
  display: flex;
  align-items: center;
  color: #dc3545;
  font-size: 12px;
  font-weight: bold;
}

.warning-icon {
  margin-right: 4px;
  font-size: 14px;
}

/* 商品设置面板 */
.goods-settings-panel {
  border-top: 1px solid #e9ecef;
  padding-top: 15px;
  margin-top: 15px;
}

.settings-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.settings-title::before {
  content: "⚙";
  margin-right: 6px;
  color: #007bff;
}

.settings-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.setting-item {
  display: flex;
  flex-direction: column;
}

.setting-item.full-width {
  grid-column: 1 / -1;
}

.setting-item label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  font-weight: 500;
}

/* 价格输入组 */
.price-input-group {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  background: white;
}

.currency {
  background-color: #f8f9fa;
  padding: 6px 8px;
  border-right: 1px solid #ddd;
  font-size: 14px;
  color: #666;
}

.price-input {
  border: none;
  padding: 6px 8px;
  font-size: 14px;
  flex: 1;
  outline: none;
}

.price-input:focus {
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.discount-display {
  margin-top: 4px;
  font-size: 12px;
  color: #e74c3c;
  font-weight: bold;
  text-align: center;
  background-color: #fff5f5;
  padding: 2px 6px;
  border-radius: 12px;
  border: 1px solid #fecaca;
}

/* 库存和限购输入 */
.stock-input,
.limit-input {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 6px 8px;
  font-size: 14px;
  outline: none;
  background: white;
}

.stock-input:focus,
.limit-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 折扣设置样式 */
.discount-setting {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.discount-input-group {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.discount-input {
  width: 80px;
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
  font-weight: 600;
}

.discount-unit {
  margin-left: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #dc3545;
}

.price-preview {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.price-range-hint {
  font-size: 12px;
  color: #28a745;
  font-style: italic;
}

/* 选择提示 */
.selection-hint {
  margin-top: 15px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.hint-text {
  margin: 0;
  color: #666;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.info-icon {
  margin-right: 8px;
  color: #007bff;
  font-size: 16px;
}

.selected-count {
  margin: 0;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.selected-count strong {
  color: #28a745;
  font-size: 16px;
}

/* 已选商品汇总 */
.selected-summary {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 20px;
}

.selected-summary h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.summary-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: white;
  border-radius: 4px;
  font-size: 14px;
}

.remove-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

/* 轮次详情 */
.round-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 5px;
}

.detail-section p {
  margin: 5px 0;
  color: #666;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  color: #6c757d;
  font-size: 16px;
}

.loading-state .loading-icon {
  margin-right: 10px;
  font-size: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  text-align: center;
}

.empty-state .empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-state .empty-text {
  font-size: 18px;
  font-weight: 500;
  color: #495057;
  margin: 0 0 8px 0;
}

.empty-state .empty-hint {
  font-size: 14px;
  color: #6c757d;
  margin: 0 0 20px 0;
  line-height: 1.5;
}

.empty-state .btn-sm {
  padding: 6px 16px;
  font-size: 14px;
}

.goods-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.goods-cell img {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
}

.goods-cell span {
  font-size: 14px;
}
</style>
