const Base = require('./base.js');
const crypto = require('crypto');

module.exports = class extends Base {
    async loginAction() {
        const username = this.post('username');
        const password = this.post('password');

        // 正常的数据库验证流程
        const admin = await this.model('admin').where({
            username: username
        }).find();

        if (think.isEmpty(admin)) {
            console.log('用户不存在:', username);
            return this.fail(401, '用户名或密码不正确!');
        }

        console.log('输入密码加密后:', think.md5(password + '' + admin.password_salt));
        console.log('数据库中的密码:', admin.password);

        if (think.md5(password + '' + admin.password_salt) !== admin.password) {
            console.log('密码不匹配');
            return this.fail(400, '用户名或密码不正确!!');
        }
        // 更新登录信息
        await this.model('admin').where({
            id: admin.id
        }).update({
            last_login_time: parseInt(Date.now() / 1000),
            last_login_ip: this.ctx.ip
        });
        const TokenSerivce = this.service('token', 'admin');
        let sessionData = {}
        sessionData.user_id = admin.id
        const sessionKey = await TokenSerivce.create(sessionData);
        if (think.isEmpty(sessionKey)) {
            return this.fail('登录失败');
        }
        const userInfo = {
            id: admin.id,
            username: admin.username,
            name:admin.name
        };
        return this.success({
            token: sessionKey,
            userInfo: userInfo
        });
    }

    // 生成扫码登录二维码
    async generateQrCodeAction() {
        try {
            // 生成随机token
            const qrToken = crypto.randomBytes(32).toString('hex');
            const currentTime = parseInt(Date.now() / 1000);
            const expireTime = currentTime + 300; // 5分钟过期

            // 存储到数据库或缓存中
            // 这里简单存储到一个临时表或使用内存存储
            const qrData = {
                token: qrToken,
                status: 'pending', // pending, success, expired
                create_time: currentTime,
                expire_time: expireTime,
                user_id: null
            };

            // 存储二维码数据
            await this.model().query(`
                INSERT INTO hiolabs_qr_login_tokens (token, status, create_time, expire_time)
                VALUES ('${qrToken}', 'pending', ${currentTime}, ${expireTime})
            `);

            // 生成二维码内容
            const qrContent = `hioshop://login?token=${qrToken}`;

            return this.success({
                qrToken: qrToken,
                qrContent: qrContent,
                expireTime: expireTime
            });
        } catch (error) {
            console.error('生成二维码失败:', error);
            return this.fail('生成二维码失败');
        }
    }

    // 检查二维码登录状态
    async checkQrStatusAction() {
        try {
            const qrToken = this.get('token');
            if (!qrToken) {
                return this.fail('缺少token参数');
            }

            const currentTime = parseInt(Date.now() / 1000);

            // 查询二维码状态
            const qrData = await this.model().query(`
                SELECT * FROM hiolabs_qr_login_tokens WHERE token = '${qrToken}' LIMIT 1
            `);
            const qrRecord = qrData && qrData.length > 0 ? qrData[0] : null;

            if (!qrRecord) {
                return this.fail('无效的二维码');
            }

            // 检查是否过期
            if (currentTime > qrRecord.expire_time) {
                await this.model().query(`
                    UPDATE hiolabs_qr_login_tokens SET status = 'expired' WHERE token = '${qrToken}'
                `);
                return this.fail('二维码已过期');
            }

            if (qrRecord.status === 'success' && qrRecord.user_id) {
                // 生成管理后台token
                const TokenService = this.service('token', 'admin');
                const sessionData = {
                    user_id: qrRecord.user_id,
                    user_type: qrRecord.user_type // 'promoter' 或 'distributor'
                };
                const adminToken = await TokenService.create(sessionData);

                // 获取用户信息
                const userInfo = await this.getUserInfo(qrRecord.user_id, qrRecord.user_type);

                return this.success({
                    status: 'success',
                    token: adminToken,
                    userInfo: userInfo
                });
            }

            return this.success({
                status: qrRecord.status
            });
        } catch (error) {
            console.error('检查二维码状态失败:', error);
            return this.fail('检查状态失败');
        }
    }

    // 获取用户信息
    async getUserInfo(userId, userType) {
        try {
            const user = await this.model('user').where({ id: userId }).find();
            if (think.isEmpty(user)) {
                return null;
            }

            let roleInfo = {};
            if (userType === 'promoter') {
                const promoter = await this.model('personal_promoters').where({
                    user_id: userId,
                    status: 1
                }).find();
                roleInfo = {
                    role: 'promoter',
                    level: promoter ? promoter.level : 1
                };
            } else if (userType === 'distributor') {
                const distributor = await this.model('distributors').where({
                    user_id: userId,
                    is_active: 1,
                    audit_status: 1
                }).find();
                roleInfo = {
                    role: 'distributor',
                    level: distributor ? distributor.level_id : 1
                };
            }

            return {
                id: user.id,
                username: user.mobile || user.nickname,
                name: user.nickname,
                avatar: user.avatar,
                ...roleInfo
            };
        } catch (error) {
            console.error('获取用户信息失败:', error);
            return null;
        }
    }
};