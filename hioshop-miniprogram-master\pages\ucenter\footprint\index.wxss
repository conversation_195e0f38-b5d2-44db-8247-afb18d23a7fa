page {
    background: #fafafa;
    min-height: 100%;
}

.container {
    background: #fafafa;
    min-height: 100%;
    border-top: 1rpx solid #f4f4f4;
}

.sold-img {
    position: absolute;
    left: 0;
    top: 0;
    width: 320rpx;
    height: 320rpx;
    display: flex;
    justify-content: center;
    align-items: center;
}

.soldout {
    height: 180rpx;
    width: 180rpx;
}

.print-goods {
    width: 100%;
    height: auto;
    overflow: hidden;
}

.print-goods .item {
    float: left;
    background: #fff;
    /* width: 375rpx; */
    width: 50%;
    height: auto;
    overflow: hidden;
    text-align: center;
    padding: 20rpx 24rpx 16rpx 24rpx;
    box-sizing: border-box;
    position: relative;
}

.print-goods .left {
    border-right: 1px solid #f4f4f4;
    border-bottom: 1px solid #f4f4f4;
}

.print-goods .right {
    border-bottom: 1px solid #f4f4f4;
}

.print-goods .item .navi {
    width: auto;
    height: auto;
    display: block;
}

.print-goods .item .img {
    width: 320rpx;
    height: 320rpx;
    position: relative;
}

.print-goods .item .name {
    display: block;
    width: 100%;
    line-height: 40rpx;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    font-size: 30rpx;
    color: #233445;
    margin: 8rpx 0 16rpx 0;
}

.print-goods .item .price-text {
    width: 100%;
    height: 36rpx;
    text-align: center;
    margin-bottom: 20rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: none;
}

.price-text .origin-price {
    margin-right: 10rpx;
    color: #999;
    font-size: 26rpx;
    text-decoration: line-through;
    line-height: 36rpx;
}

.price-text .retail-price {
    font-size: 30rpx;
    color: #ff3456;
    line-height: 36rpx;
}

.tag-join-number {
    position: absolute;
    top: 20rpx;
    left: 20rpx;
    /* border: 1rpx solid #ff3456; */
    text-align: center;
    padding: 8rpx 8rpx;
    font-size: 22rpx;
    line-height: 28rpx;
    height: 28rpx;
    color: #233445;
    font-weight: bold;
    width: 80rpx;
    border-radius: 6rpx;
    background: #fe9;
}

.tag-kill-number {
    position: absolute;
    top: 20rpx;
    left: 20rpx;
    /* border: 1rpx solid #ff3456; */
    text-align: center;
    padding: 8rpx 8rpx;
    font-size: 24rpx;
    line-height: 28rpx;
    height: 28rpx;
    color: #fff;
    width: 80rpx;
    border-radius: 6rpx;
    background: -webkit-linear-gradient(left, #fdbb43, #f96); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(right, #fdbb43, #f96); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(right, #fdbb43, #f96); /* Firefox 3.6 - 15 */
    background: linear-gradient(to right, #ff3456, #f96); /* 标准的语法（必须放在最后） */
}

.no-print {
    width: 100%;
    position: absolute;
    bottom: 0;
    top: 88rpx;
    left: 0;
    right: 0;
    text-align: center;
    padding-top: 203rpx;
}

.no-print-img {
    width: 100rpx;
    height: 100rpx;
    margin-bottom: 30rpx;
}

.no-print .text {
    font-size: 30rpx;
    color: #999;
    text-align: center;
    margin-bottom: 30rpx;
}

.to-index-btn {
    color: #fff;
    background: linear-gradient(to right, #ff3456, #ff347d);
    border-radius: 10rpx;
    width: 300rpx;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    font-size: 28rpx;
    margin: 0 auto;
}

.no-more-goods {
    display: none;
    height: 100rpx;
    width: 100%;
    text-align: center;
    font-size: 28rpx;
    margin: 50rpx 0;
    color: #999;
}

.cancel-print {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    width: 46rpx;
    height: 46rpx;
    z-index: 999;
}

.hidden {
    display: none;
}

.show {
    display: block;
}

.day-item {
    width: 100%;
    /* height: auto; */
    margin-bottom: 20rpx;
    min-height: 100rpx;
    float: left;
}

.day-hd {
    width: 100%;
    height: 100rpx;
    line-height: 100rpx;
    color: #666;
    text-align: center;
    font-size: 30rpx;
}

.goods-info {
    /* width: 220rpx; */
}

.goods-info .goods-title {
    font-size: 28rpx;
    color: #222;
    margin-bottom: 6rpx;
    /* height: 64rpx; */
    display: -webkit-box;
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    text-align: left;
    /* text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden; */
}

.goods-info .goods-intro {
    font-size: 24rpx;
    color: #888;
    margin-bottom: 6rpx;
    /* height: 64rpx; */
    display: -webkit-box;
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    text-align: left;
}

.goods-info .price-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.goods-info .price-container .l {
    display: flex;
    flex-direction: column;
}

.goods-info .price-container .r {
    width: 46rpx;
    height: 46rpx;
}

.goods-info .price-container .r .cart-img {
    width: 46rpx;
    height: 46rpx;
}

.goods-info .price-container .l .h {
    font-size: 22rpx;
    height: 22rpx;
    color: #000;
    text-align: left;
}

.goods-info .price-container .l .b {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.goods-info .price-container .l .no-level {
    font-size: 26rpx;
    color: #e00000;
}

.goods-info .price-container .l .b .price-t {
    font-size: 28rpx;
    color: #e00000;
    margin-right: 6rpx;
}

.goods-info .price-container .l .b .price-tag {
    border: 1rpx solid #e00000;
    color: #e00000;
    background: #ffeaea;
    border-radius: 100rpx;
    padding: 0 6rpx;
    font-size: 14rpx;
    text-align: center;
    line-height: 22rpx;
    height: 22rpx;
}

.goods-info .price-container .retail-price {
    font-size: 18rpx;
    color: #000;
    margin-bottom: 4rpx;
    /* font-weight: bold; */
}
