const Base = require('./base.js');

module.exports = class extends Base {

  /**
   * 获取本地时间字符串，兼容Windows和Linux
   * @returns {string} 格式: YYYY-MM-DD HH:mm:ss
   */
  getLocalDateTime() {
    const date = new Date();

    // 使用本地时间，不使用UTC
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }
  /**
   * 获取可领取的优惠券列表
   */
  async availableAction() {
    try {
      // 获取用户ID（如果已登录）
      let userId = null;
      try {
        userId = await this.getLoginUserId();
      } catch (error) {
        console.log('用户未登录，显示默认状态');
      }

      // 获取当前有效的优惠券 - 无论是否登录都显示
      // 使用本地时间，兼容Windows和Linux
      const now = this.getLocalDateTime();
      console.log('=== 优惠券查询调试信息 ===');
      console.log('当前时间:', now);
      console.log('用户ID:', userId);

      const whereCondition = {
        status: 'active',
        start_time: ['<=', now],
        end_time: ['>=', now],
        is_delete: 0
      };
      console.log('查询条件:', whereCondition);

      // 先查询所有优惠券，用于调试
      const allCoupons = await this.model('coupons').where({ is_delete: 0 }).select();
      console.log('allCoupons查询结果类型:', typeof allCoupons);
      console.log('allCoupons是否为数组:', Array.isArray(allCoupons));
      console.log('allCoupons原始数据:', allCoupons);

      if (Array.isArray(allCoupons)) {
        console.log('数据库中所有优惠券:', allCoupons.map(c => ({
          id: c.id,
          name: c.name,
          type: c.type,
          status: c.status,
          start_time: c.start_time,
          end_time: c.end_time,
          is_delete: c.is_delete,
          start_time_check: c.start_time <= now,
          end_time_check: c.end_time >= now,
          status_check: c.status === 'active'
        })));
      }

      let coupons = await this.model('coupons').where(whereCondition).order('CASE WHEN type = "newuser" THEN 0 ELSE 1 END, id ASC').select();
      console.log('coupons查询结果类型:', typeof coupons);
      console.log('coupons是否为数组:', Array.isArray(coupons));
      console.log('coupons原始数据:', coupons);

      // 确保coupons是数组
      if (!Array.isArray(coupons)) {
        console.error('coupons查询结果不是数组，设置为空数组');
        coupons = [];
      }

      console.log('查询到的有效优惠券数量:', coupons.length);
      if (coupons.length > 0) {
        console.log('有效优惠券详情:', coupons.map(c => ({
          id: c.id,
          name: c.name,
          type: c.type,
          status: c.status,
          start_time: c.start_time,
          end_time: c.end_time,
          is_delete: c.is_delete
        })));
      }

      // 为每个优惠券检查用户领取状态
      let result = [];
      if (Array.isArray(coupons) && coupons.length > 0) {
        for (let coupon of coupons) {
          let canReceive = true;
          let reason = '';
          let receivedCount = 0;

          if (userId) {
            // 用户已登录，检查领取状态

            // 检查用户对该优惠券的领取数量
            receivedCount = await this.model('user_coupons').where({
              user_id: userId,
              coupon_id: coupon.id
            }).count();

            // 检查是否已达到领取上限
            if (receivedCount >= coupon.per_user_limit) {
              canReceive = false;
              reason = '已领取';
            }

            // 检查新人券特殊限制
            if (canReceive && coupon.type === 'newuser') {
              const hasNewUserCoupon = await this.model('user_coupons').alias('uc')
                .join('hiolabs_coupons c ON uc.coupon_id = c.id')
                .where({
                  'uc.user_id': userId,
                  'c.type': 'newuser'
                }).count();

              if (hasNewUserCoupon > 0) {
                canReceive = false;
                reason = '已领取新人券';
              }
            }
          }

          result.push({
            id: coupon.id,
            name: coupon.name,
            code: coupon.code,
            type: coupon.type,
            discount_type: coupon.discount_type,
            discount_value: coupon.discount_value,
            min_amount: coupon.min_amount || 0,
            max_discount: coupon.max_discount || 0,
            total_quantity: coupon.total_quantity,
            per_user_limit: coupon.per_user_limit,
            description: coupon.description || '',
            start_time: coupon.start_time,
            end_time: coupon.end_time,
            canReceive: canReceive,
            reason: reason,
            receivedCount: receivedCount
          });
        }
      }

      return this.success(result);
    } catch (error) {
      think.logger.error('获取可领取优惠券失败:', error);
      return this.fail('获取优惠券失败');
    }
  }

  /**
   * 领取优惠券
   */
  async receiveAction() {
    try {
      const userId = await this.getLoginUserId();
      if (!userId) {
        return this.fail('请先登录');
      }

      // 检查用户是否完善了个人信息，与订单有礼、签到功能保持一致
      console.log('=== 开始查询用户信息 ===');
      console.log('查询用户ID:', userId, 'type:', typeof userId);

      const userInfo = await this.model('user').where({ id: userId }).find();
      console.log('查询到的用户信息:', userInfo);

      if (think.isEmpty(userInfo)) {
        console.log('用户不存在');
        return this.fail('用户不存在');
      }

      // 解码昵称（数据库中是Base64编码存储的）
      let decodedNickname = '';
      if (userInfo.nickname) {
        try {
          decodedNickname = Buffer.from(userInfo.nickname, 'base64').toString();
        } catch (error) {
          console.log('昵称解码失败，使用原始值:', userInfo.nickname);
          decodedNickname = userInfo.nickname;
        }
      }

      console.log('=== 用户权限验证 ===');
      console.log('用户ID:', userId);
      console.log('原始昵称:', userInfo.nickname);
      console.log('解码昵称:', decodedNickname);
      console.log('is_new_user:', userInfo.is_new_user);

      // 验证用户信息完整性（根据昵称判断，不使用is_authorized字段）

      console.log('=== 权限验证详情 ===');
      console.log('nickname存在:', !!userInfo.nickname);
      console.log('decodedNickname:', decodedNickname);
      console.log('is_new_user:', userInfo.is_new_user);

      // 检查用户是否已完成授权（基于昵称判断）
      if (!userInfo.nickname ||
          decodedNickname === '点我登录' ||
          decodedNickname === '点击登录' ||
          decodedNickname === '微信用户') {
        console.log('用户权限验证失败，原因: 个人信息不完整');
        return this.fail('请先完善个人信息（头像和昵称）后再领取优惠券');
      }

      console.log('用户权限验证通过');

      const { couponId } = this.post();
      console.log('=== 接收到的参数 ===');
      console.log('couponId:', couponId, 'type:', typeof couponId);

      if (!couponId) {
        return this.fail('参数错误');
      }

      console.log('=== 查询优惠券信息 ===');
      const coupon = await this.model('coupons').where({
        id: couponId,
        status: 'active',
        is_delete: 0
      }).find();
      console.log('查询到的优惠券:', coupon);

      if (think.isEmpty(coupon)) {
        return this.fail('优惠券不存在或已失效');
      }

      // 检查时间有效性
      const now = new Date();
      if (new Date(coupon.start_time) > now || new Date(coupon.end_time) < now) {
        return this.fail('优惠券不在有效期内');
      }

      // 检查新人券限制（每人只能领取一张新人券）
      if (coupon.type === 'newuser') {
        console.log('=== 检查新人券资格 ===');

        // 检查用户是否已领取过任何新人券
        const hasNewUserCoupon = await this.model('user_coupons').alias('uc')
          .join('hiolabs_coupons c ON uc.coupon_id = c.id')
          .where({
            'uc.user_id': userId,
            'c.type': 'newuser'
          }).count();

        console.log('用户已领取的新人券数量:', hasNewUserCoupon);

        if (hasNewUserCoupon > 0) {
          console.log('用户已领取过新人券，不符合新人券条件');
          return this.fail('您已领取过新人券，每人仅限领取一张');
        }
        console.log('用户未领取过新人券，符合新人券条件');
      }

      // 检查用户领取数量限制
      console.log('=== 检查用户领取数量限制 ===');
      console.log('查询条件 - user_id:', userId, 'coupon_id:', couponId);
      const receivedCount = await this.model('user_coupons').where({
        user_id: userId,
        coupon_id: couponId
      }).count();
      console.log('用户已领取数量:', receivedCount);

      if (receivedCount >= coupon.per_user_limit) {
        return this.fail('您已达到该优惠券的领取上限');
      }

      // 检查总量限制
      if (coupon.total_quantity > 0) {
        const totalReceived = await this.model('user_coupons').where({
          coupon_id: couponId
        }).count();

        if (totalReceived >= coupon.total_quantity) {
          return this.fail('优惠券已被领完');
        }
      }

      // 生成用户优惠券
      const couponCode = this.generateCouponCode();
      const expireAt = coupon.valid_days ?
        new Date(Date.now() + coupon.valid_days * 24 * 60 * 60 * 1000) :
        new Date(coupon.end_time);

      console.log('=== 准备插入用户优惠券数据 ===');
      console.log('用户ID:', userId);
      console.log('优惠券ID:', couponId);
      console.log('优惠券代码:', couponCode);
      console.log('过期时间:', expireAt);
      console.log('优惠券信息:', coupon);

      await this.model('user_coupons').add({
        user_id: userId,
        coupon_id: couponId,
        coupon_code: couponCode,
        expire_at: expireAt,
        source: 'manual'
      });

      return this.success('领取成功');
    } catch (error) {
      think.logger.error('领取优惠券失败:', error);
      console.error('=== 优惠券领取详细错误信息 ===');
      console.error('错误消息:', error.message);
      console.error('错误堆栈:', error.stack);
      console.error('错误代码:', error.code);
      console.error('SQL错误:', error.sql);
      console.error('================================');
      return this.fail('领取失败，请稍后重试');
    }
  }

  /**
   * 获取用户的优惠券列表
   */
  async myAction() {
    try {
      const userId = await this.getLoginUserId();
      if (!userId) {
        return this.fail('请先登录');
      }

      const { status = 'unused' } = this.get();

      const userCoupons = await this.model('user_coupons').alias('uc')
        .join('hiolabs_coupons c ON uc.coupon_id = c.id')
        .where({
          'uc.user_id': userId,
          'uc.status': status,
          'c.is_delete': 0
        })
        .field('uc.*, c.name, c.type, c.discount_type, c.discount_value, c.min_amount, c.max_discount, c.description')
        .order('uc.received_at DESC')
        .select();

      // 检查过期状态
      const now = new Date();
      for (let userCoupon of userCoupons) {
        if (userCoupon.status === 'unused' && new Date(userCoupon.expire_at) < now) {
          // 更新为过期状态
          await this.model('user_coupons').where({id: userCoupon.id}).update({
            status: 'expired'
          });
          userCoupon.status = 'expired';
        }
      }

      // 按状态分组
      const result = {
        unused: [],
        used: [],
        expired: []
      };

      userCoupons.forEach(coupon => {
        if (result[coupon.status]) {
          result[coupon.status].push(coupon);
        }
      });

      return this.success(result);
    } catch (error) {
      think.logger.error('获取用户优惠券失败:', error);
      return this.fail('获取优惠券失败');
    }
  }

  /**
   * 获取订单可用的优惠券
   */
  async availableForOrderAction() {
    try {
      const userId = await this.getLoginUserId();
      if (!userId) {
        return this.fail('请先登录');
      }

      const { amount } = this.get();
      if (!amount || amount <= 0) {
        return this.fail('订单金额参数错误');
      }

      const userCoupons = await this.model('user_coupons').alias('uc')
        .join('hiolabs_coupons c ON uc.coupon_id = c.id')
        .where({
          'uc.user_id': userId,
          'uc.status': 'unused',
          'uc.expire_at': ['>=', this.getLocalDateTime()],
          'c.min_amount': ['<=', amount],
          'c.is_delete': 0
        })
        .field('uc.*, c.name, c.type, c.discount_type, c.discount_value, c.min_amount, c.max_discount, c.description')
        .order('c.discount_value DESC')
        .select();

      // 计算每个优惠券的优惠金额
      userCoupons.forEach(coupon => {
        coupon.calculatedDiscount = this.calculateDiscount(coupon, amount);
      });

      return this.success(userCoupons);
    } catch (error) {
      think.logger.error('获取订单可用优惠券失败:', error);
      return this.fail('获取可用优惠券失败');
    }
  }

  /**
   * 自动选择最优优惠券
   */
  async autoSelectBestAction() {
    try {
      const userId = await this.getLoginUserId();
      if (!userId) {
        return this.fail('请先登录');
      }

      const { amount } = this.post();
      if (!amount || amount <= 0) {
        return this.fail('订单金额参数错误');
      }

      const availableCoupons = await this.getAvailableCouponsForAmount(userId, amount);

      let bestCoupon = null;
      let maxDiscount = 0;

      for (let coupon of availableCoupons) {
        const discount = this.calculateDiscount(coupon, amount);
        if (discount > maxDiscount) {
          maxDiscount = discount;
          bestCoupon = coupon;
        }
      }

      return this.success({
        coupon: bestCoupon,
        discount: maxDiscount,
        finalAmount: amount - maxDiscount
      });
    } catch (error) {
      think.logger.error('自动选择优惠券失败:', error);
      return this.fail('自动选择失败');
    }
  }

  /**
   * 计算优惠金额
   */
  calculateDiscount(coupon, amount) {
    if (coupon.discount_type === 'fixed') {
      return Math.min(coupon.discount_value, amount);
    } else {
      const discount = amount * (coupon.discount_value / 100);
      return Math.min(discount, coupon.max_discount || discount);
    }
  }

  /**
   * 获取指定金额可用的优惠券
   */
  async getAvailableCouponsForAmount(userId, amount) {
    return await this.model('user_coupons').alias('uc')
      .join('hiolabs_coupons c ON uc.coupon_id = c.id')
      .where({
        'uc.user_id': userId,
        'uc.status': 'unused',
        'uc.expire_at': ['>=', this.getLocalDateTime()],
        'c.min_amount': ['<=', amount],
        'c.is_delete': 0
      })
      .field('uc.*, c.*')
      .select();
  }

  /**
   * 管理端 - 获取优惠券列表
   */
  async indexAction() {
    try {
      const { page = 1, size = 20, type, status, keyword } = this.get();

      let where = { is_delete: 0 };

      if (type) {
        where.type = type;
      }

      if (status) {
        where.status = status;
      }

      if (keyword) {
        where._complex = {
          name: ['LIKE', `%${keyword}%`],
          code: ['LIKE', `%${keyword}%`],
          _logic: 'OR'
        };
      }

      const coupons = await this.model('coupons')
        .where(where)
        .order('created_at DESC')
        .page(page, size)
        .countSelect();

      // 为每个优惠券添加统计信息
      for (let coupon of coupons.data) {
        coupon.receivedCount = await this.model('user_coupons').where({
          coupon_id: coupon.id
        }).count();

        coupon.usedCount = await this.model('user_coupons').where({
          coupon_id: coupon.id,
          status: 'used'
        }).count();
      }

      return this.success(coupons);
    } catch (error) {
      think.logger.error('获取优惠券列表失败:', error);
      return this.fail('获取列表失败');
    }
  }

  /**
   * 管理端 - 创建优惠券
   */
  async addAction() {
    try {
      const data = this.post();

      // 验证必填字段
      if (!data.name || !data.type || !data.discount_type || !data.discount_value) {
        return this.fail('请填写完整的优惠券信息');
      }

      // 验证时间
      if (!data.start_time || !data.end_time) {
        return this.fail('请设置有效期');
      }

      if (new Date(data.start_time) >= new Date(data.end_time)) {
        return this.fail('开始时间必须早于结束时间');
      }

      // 生成代码（如果没有提供）
      if (!data.code) {
        data.code = this.generateCouponCode();
      } else {
        // 检查代码是否已存在
        const existingCoupon = await this.model('coupons').where({
          code: data.code,
          is_delete: 0
        }).find();

        if (!think.isEmpty(existingCoupon)) {
          return this.fail('优惠券代码已存在');
        }
      }

      // 设置默认值
      data.status = data.status || 'active';
      data.total_quantity = data.total_quantity || -1;
      data.per_user_limit = data.per_user_limit || 1;
      data.auto_distribute = data.auto_distribute ? 1 : 0;

      const couponId = await this.model('coupons').add(data);

      return this.success({ id: couponId, message: '创建成功' });
    } catch (error) {
      think.logger.error('创建优惠券失败:', error);
      return this.fail('创建失败');
    }
  }

  /**
   * 管理端 - 切换优惠券状态
   */
  async toggleStatusAction() {
    try {
      const { id, status } = this.post();

      if (!id || !status) {
        return this.fail('参数错误');
      }

      if (!['active', 'disabled'].includes(status)) {
        return this.fail('状态参数错误');
      }

      const coupon = await this.model('coupons').where({
        id: id,
        is_delete: 0
      }).find();

      if (think.isEmpty(coupon)) {
        return this.fail('优惠券不存在');
      }

      await this.model('coupons').where({ id: id }).update({
        status: status,
        updated_at: new Date()
      });

      return this.success('状态更新成功');
    } catch (error) {
      think.logger.error('切换优惠券状态失败:', error);
      return this.fail('操作失败');
    }
  }

  /**
   * 管理端 - 删除优惠券
   */
  async deleteAction() {
    try {
      const { id } = this.post();

      if (!id) {
        return this.fail('参数错误');
      }

      const coupon = await this.model('coupons').where({
        id: id,
        is_delete: 0
      }).find();

      if (think.isEmpty(coupon)) {
        return this.fail('优惠券不存在');
      }

      // 检查是否有用户已领取
      const receivedCount = await this.model('user_coupons').where({
        coupon_id: id
      }).count();

      if (receivedCount > 0) {
        return this.fail('该优惠券已有用户领取，无法删除');
      }

      await this.model('coupons').where({ id: id }).update({
        is_delete: 1,
        updated_at: new Date()
      });

      return this.success('删除成功');
    } catch (error) {
      think.logger.error('删除优惠券失败:', error);
      return this.fail('删除失败');
    }
  }

  /**
   * 管理端 - 获取统计数据
   */
  async statisticsAction() {
    try {
      const total = await this.model('coupons').where({ is_delete: 0 }).count();
      const active = await this.model('coupons').where({
        is_delete: 0,
        status: 'active'
      }).count();
      const received = await this.model('user_coupons').count();
      const used = await this.model('user_coupons').where({
        status: 'used'
      }).count();

      return this.success({
        total,
        active,
        received,
        used
      });
    } catch (error) {
      think.logger.error('获取统计数据失败:', error);
      return this.fail('获取统计失败');
    }
  }

  /**
   * 生成优惠券码
   */
  generateCouponCode() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `CPN${timestamp}${random}`.toUpperCase();
  }

  /**
   * 获取过期时间字段名（兼容不同的数据库表结构）
   */
  async getExpireFieldName() {
    try {
      // 尝试查询一条记录来确定字段名
      const testRecord = await this.model('user_coupons').limit(1).find();
      if (testRecord && testRecord.hasOwnProperty('expire_at')) {
        return 'expire_at';
      } else if (testRecord && testRecord.hasOwnProperty('expired_at')) {
        return 'expired_at';
      } else {
        // 默认使用 expire_at
        return 'expire_at';
      }
    } catch (error) {
      console.log('检测过期字段名失败，使用默认值 expire_at:', error.message);
      return 'expire_at';
    }
  }
};
