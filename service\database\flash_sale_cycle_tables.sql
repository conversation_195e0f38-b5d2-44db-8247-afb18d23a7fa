-- 循环秒杀功能数据表设计
-- 每5分钟一场的高频秒杀系统

-- 删除旧的表（如果存在）
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS `hiolabs_flash_sale_orders`;
DROP TABLE IF EXISTS `hiolabs_flash_sale_user_records`;
DROP TABLE IF EXISTS `hiolabs_flash_sale_rounds`;
DROP TABLE IF EXISTS `hiolabs_flash_sales`;
DROP TABLE IF EXISTS `hiolabs_flash_sale_time_slots`;
DROP TABLE IF EXISTS `hiolabs_flash_sale_campaigns`;
SET FOREIGN_KEY_CHECKS = 1;

-- 1. 秒杀活动配置表（主表）
CREATE TABLE IF NOT EXISTS `hiolabs_flash_sale_campaigns` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '活动配置ID',
  `name` varchar(100) NOT NULL COMMENT '活动名称',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `flash_price` decimal(10,2) NOT NULL COMMENT '秒杀价格',
  `original_price` decimal(10,2) NOT NULL COMMENT '原价',
  `total_stock` int(11) NOT NULL COMMENT '总库存',
  `stock_per_round` int(11) NOT NULL COMMENT '每轮库存',
  `limit_quantity` int(11) DEFAULT 1 COMMENT '限购数量',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date NOT NULL COMMENT '结束日期',
  `daily_start_time` time NOT NULL DEFAULT '09:00:00' COMMENT '每日开始时间',
  `daily_end_time` time NOT NULL DEFAULT '22:00:00' COMMENT '每日结束时间',
  `round_duration` int(11) NOT NULL DEFAULT 300 COMMENT '每轮时长（秒，默认5分钟）',
  `break_duration` int(11) NOT NULL DEFAULT 0 COMMENT '轮次间隔（秒，默认无间隔）',
  `status` enum('draft','active','paused','ended') DEFAULT 'draft' COMMENT '状态：草稿，进行中，暂停，已结束',
  `auto_start` tinyint(1) DEFAULT 1 COMMENT '是否自动开始',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_status_date` (`status`, `start_date`, `end_date`),
  KEY `idx_date_time` (`start_date`, `end_date`, `daily_start_time`, `daily_end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='秒杀活动配置表';

-- 2. 秒杀轮次表（自动生成的具体时间段）
CREATE TABLE IF NOT EXISTS `hiolabs_flash_sale_rounds` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '轮次ID',
  `campaign_id` int(11) NOT NULL COMMENT '活动配置ID',
  `round_number` int(11) NOT NULL COMMENT '轮次编号（从1开始）',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `stock` int(11) NOT NULL COMMENT '本轮库存',
  `sold_count` int(11) DEFAULT 0 COMMENT '已售数量',
  `status` enum('upcoming','active','ended','cancelled') DEFAULT 'upcoming' COMMENT '状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_campaign_round` (`campaign_id`, `round_number`),
  KEY `idx_campaign_id` (`campaign_id`),
  KEY `idx_time_status` (`start_time`, `end_time`, `status`),
  KEY `idx_status_time` (`status`, `start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='秒杀轮次表';

-- 3. 秒杀订单记录表（保持原有结构，但关联到轮次）
CREATE TABLE IF NOT EXISTS `hiolabs_flash_sale_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `campaign_id` int(11) NOT NULL COMMENT '活动配置ID',
  `round_id` int(11) NOT NULL COMMENT '轮次ID',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `quantity` int(11) NOT NULL COMMENT '购买数量',
  `flash_price` decimal(10,2) NOT NULL COMMENT '秒杀价格',
  `total_amount` decimal(10,2) NOT NULL COMMENT '总金额',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_campaign_id` (`campaign_id`),
  KEY `idx_round_id` (`round_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='秒杀订单记录表';

-- 4. 用户参与记录表（防止重复参与）
CREATE TABLE IF NOT EXISTS `hiolabs_flash_sale_user_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `campaign_id` int(11) NOT NULL COMMENT '活动配置ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `total_purchased` int(11) DEFAULT 0 COMMENT '总购买数量',
  `last_purchase_time` datetime DEFAULT NULL COMMENT '最后购买时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_campaign_user` (`campaign_id`, `user_id`),
  KEY `idx_campaign_id` (`campaign_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户参与记录表';



-- 创建索引以提高查询性能
CREATE INDEX idx_rounds_active ON hiolabs_flash_sale_rounds (status, start_time, end_time);
CREATE INDEX idx_campaigns_active ON hiolabs_flash_sale_campaigns (status, start_date, end_date);
CREATE INDEX idx_orders_time ON hiolabs_flash_sale_orders (created_at);

-- 插入示例数据
INSERT INTO `hiolabs_flash_sale_campaigns` (
  `name`, `goods_id`, `flash_price`, `original_price`, `total_stock`, `stock_per_round`,
  `limit_quantity`, `start_date`, `end_date`, `daily_start_time`, `daily_end_time`,
  `round_duration`, `break_duration`, `status`
) VALUES 
('iPhone 15 Pro 限时秒杀', 1, 6999.00, 8999.00, 100, 5, 1, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 7 DAY), '09:00:00', '22:00:00', 300, 0, 'active'),
('小米14 Ultra 疯狂秒杀', 2, 4999.00, 5999.00, 200, 10, 2, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 3 DAY), '10:00:00', '21:00:00', 300, 60, 'active');

SELECT '✅ 循环秒杀表结构创建完成！' as result;
