/**index.wxss**/

.container {
    background-color: #fff;
    min-height: 100%;
    align-items: stretch;
    overflow-x: hidden;
    position: relative;
}

.loading {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.loading .img {
    width: 90rpx;
    height: 90rpx;
}

.loading .text {
    margin-top: 20rpx;
    width: 100%;
    font-size: 24rpx;
    color: #999;
    text-align: center;
}

.qrcode-img {
    width: 600rpx;
    height: 600rpx;
}

.contact-wrap {
    position: fixed;
    z-index: 9;
    right: 30rpx;
    bottom: 60rpx;
    box-shadow: 2rpx 4rpx 10rpx #f1f1f1;
    color: #999;
    border-radius: 10rpx;
}

.contact-wrap .contact-btn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 130rpx;
    opacity: 0.9;
    background: #fff;
}

.contact-wrap .icon-mask {
    background: #000;
    opacity: 0.9;
}

.contact-wrap .contact-btn:after {
    border: none;
}

.contact-wrap .contact-btn .icon {
    width: 50rpx;
    height: 50rpx;
    margin: 0 auto;
    margin-top: 20rpx;
}

.contact-wrap .contact-btn .text {
    font-size: 22rpx;
    text-align: center;
}

.black-mask {
    width: 100%;
    height: 2000rpx;
    position: fixed;
    top: 0;
    left: 0;
    background: #333;
    opacity: 0.5;
    z-index: 99;
}

.mask {
    width: 100%;
    height: 1000rpx;
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
}

/* 现代化搜索框样式 - 参考main.html设计 */
.search-bar {
    position: fixed;
    top: 0;
    width: 100%;
    padding: 24rpx 32rpx;
    background: white;
    box-sizing: border-box;
    z-index: 100;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-container {
    display: flex;
    align-items: center;
    background: #f5f5f5;
    border-radius: 50rpx;
    padding: 20rpx 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.search-container:active {
    transform: scale(0.98);
    background: #eeeeee;
}

.search-icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 16rpx;
    opacity: 0.6;
}

.search-placeholder {
    flex: 1;
    color: #999;
    font-size: 28rpx;
    line-height: 1;
}

.scan-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60rpx;
    height: 60rpx;
    margin-left: 16rpx;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.scan-icon:active {
    transform: scale(0.9);
    background: rgba(0, 0, 0, 0.1);
}

.scan-text {
    font-size: 32rpx;
    line-height: 1;
}

.banner-wrap {
    width: 100%;
    height: 750rpx;
    display: flex;
    justify-content: center;
    margin-top: 120rpx; /* 为固定搜索栏留出空间 */
}

.banner {
    width: 750rpx;
    height: 750rpx;
}

.banner image {
    width: 100%;
    /* height: 417rpx; */
    height: 750rpx;
}



/* 分类图标区域样式 - 4列布局 */
.catalog-wrap {
    margin: 20rpx 0;
    width: 100%;
    padding: 0 30rpx;
    box-sizing: border-box;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30rpx;
    padding: 20rpx 0;
}

.catalog-wrap .icon-navi {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.catalog-wrap .icon-img {
    height: 80rpx;
    width: 80rpx;
    margin-bottom: 10rpx;
}

.catalog-wrap .icon-text {
    width: 100%;
    text-align: center;
    font-size: 24rpx;
    color: #333;
}

/* 优惠券专区样式 */
.coupon-zone {
    margin: 30rpx 20rpx;
}

.coupon-section {
    background: white;
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    overflow: hidden; /* 确保内容不会溢出 */
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
}

.section-title {
    display: flex;
    align-items: center;
}

/* 移除图标样式 */

.title-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
}

.section-more {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #999;
}

.arrow-right {
    font-size: 28rpx;
    margin-left: 8rpx;
    color: #ccc;
}

.coupon-scroll {
    white-space: nowrap;
    /* 隐藏滚动条 - 真机兼容版本 */
    overflow-x: auto;
    overflow-y: hidden;
    position: relative;
}

/* 强制隐藏滚动条 - 多种方法组合 */
.coupon-scroll::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
}

.coupon-scroll::-webkit-scrollbar-track {
    display: none !important;
}

.coupon-scroll::-webkit-scrollbar-thumb {
    display: none !important;
}

.coupon-scroll {
    -ms-overflow-style: none !important;  /* IE和Edge */
    scrollbar-width: none !important;     /* Firefox */
}

/* 真机专用：通过遮罩隐藏滚动条 */
.coupon-scroll::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20rpx;
    background: white;
    pointer-events: none;
    z-index: 10;
}

/* 备用方案：使用view实现横向滚动（无滚动条） */
.coupon-scroll-alternative {
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
}

.coupon-scroll-alternative::-webkit-scrollbar {
    display: none !important;
}

.coupon-scroll-alternative {
    -ms-overflow-style: none !important;
    scrollbar-width: none !important;
}

.coupon-list {
    display: flex;
    gap: 30rpx;
}

/* 统一的优惠券卡片基础样式 */
.coupon-card {
    flex-shrink: 0;
    width: 410rpx;
    height: 186rpx; /* 增加15%：162rpx × 1.15 = 186rpx */
    position: relative;
    border-radius: 14rpx;
    overflow: hidden;
}

/* 新人专享券样式 - 强制刷新 */
.newuser-coupon {
    box-shadow: 0 8rpx 25rpx rgba(255, 77, 79, 0.15) !important;
}

.coupon-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 77, 79, 0.05) 0%, rgba(255, 77, 79, 0.1) 100%);
    backdrop-filter: blur(20rpx);
}

/* 圆形缺口 */
.circle-left {
    position: absolute;
    left: -24rpx;
    top: 50%;
    width: 48rpx;
    height: 48rpx;
    background: #f8f9fa;
    border-radius: 50%;
    transform: translateY(-50%);
    filter: blur(4rpx);
    opacity: 0.8;
}

.circle-right {
    position: absolute;
    right: -24rpx;
    top: 50%;
    width: 48rpx;
    height: 48rpx;
    background: #f8f9fa;
    border-radius: 50%;
    transform: translateY(-50%);
    filter: blur(4rpx);
    opacity: 0.8;
}

.coupon-content {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    z-index: 2;
}

/* 左侧金额区域 */
.amount-section {
    width: 112rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-right: 2rpx dashed rgba(255, 77, 79, 0.3);
}

.amount-text {
    text-align: center;
    color: #FF4D4F;
}

.currency {
    font-size: 28rpx;
    font-weight: bold;
}

.amount {
    font-size: 60rpx;
    font-weight: bold;
    line-height: 1;
}

/* 分割线 */
.divider-line {
    width: 2rpx;
    height: 100%;
    background: rgba(255, 77, 79, 0.3);
}

/* 右侧信息区域 */
.info-section {
    flex: 1;
    padding: 24rpx 20rpx 16rpx 20rpx; /* 增加上下内边距以适应新高度 */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}

.coupon-title {
    font-size: 24rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 12rpx; /* 增加间距 */
}

.coupon-condition {
    font-size: 20rpx;
    color: rgba(255, 77, 79, 0.6);
    margin-bottom: 20rpx; /* 增加间距 */
}

.receive-button {
    background: #FF4D4F;
    color: white;
    border: none;
    border-radius: 4.4rpx;
    padding: 7.4rpx 32rpx;
    font-size: 22rpx;
    font-weight: bold;
    box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
    transition: all 0.3s ease;
    margin-bottom: 10rpx;
    margin-left: 100rpx;
}

.receive-button:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.4);
}

.receive-button.disabled {
    background: #ccc;
    color: #999;
    box-shadow: none;
}

/* 移除满减券样式，统一使用新人券样式 */

/* 强制统一所有优惠券样式 - 解决缓存问题 */
.coupon-card,
.newuser-coupon,
.discount-coupon {
    box-shadow: 0 8rpx 25rpx rgba(255, 77, 79, 0.15) !important;
}

/* 强制统一背景 - 覆盖所有可能的背景样式 */
.coupon-card .coupon-bg,
.coupon-card .coupon-pattern,
.coupon-card .gradient-overlay,
.newuser-coupon .coupon-bg,
.newuser-coupon .coupon-pattern,
.newuser-coupon .gradient-overlay,
.discount-coupon .coupon-bg,
.discount-coupon .coupon-pattern,
.discount-coupon .gradient-overlay {
    background: linear-gradient(135deg, rgba(255, 77, 79, 0.05) 0%, rgba(255, 77, 79, 0.1) 100%) !important;
    backdrop-filter: blur(20rpx) !important;
}

/* 确保优惠券卡片本身没有背景色 */
.coupon-card,
.newuser-coupon,
.discount-coupon {
    background: transparent !important;
}

/* 强制统一按钮样式 */
.coupon-card button,
.receive-button,
.receive-button-white {
    background: #FF4D4F !important;
    color: white !important;
    border: none !important;
    border-radius: 4.4rpx !important;
    padding: 7.4rpx 32rpx !important;
    font-size: 22rpx !important;
    font-weight: bold !important;
    box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3) !important;
    margin-bottom: 10rpx !important;
    margin-left: 100rpx !important;
}

/* 强制统一文字颜色 */
.coupon-card .amount-text {
    color: #FF4D4F !important;
}

.coupon-card .coupon-title {
    color: #333 !important;
}

.coupon-card .coupon-condition {
    color: rgba(255, 77, 79, 0.6) !important;
}

/* 强制统一分割线 */
.coupon-card .divider-line,
.coupon-card .divider-line-white {
    background: rgba(255, 77, 79, 0.3) !important;
}

/* 新品推荐区域样式 - 参考main.html设计 */
.new-products-section {
    margin: 40rpx 30rpx;
}

/* 分类商品网格布局 - 与新品推荐完全一致 */
.goods-container .category-products-grid {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 20rpx !important;
    padding: 0 !important;
    background: transparent !important;
    margin: 0 !important;
    border-radius: 0 !important;
    width: 100% !important;
    box-sizing: border-box !important;
    clear: both !important;
}

/* 分类商品卡片样式 - 与新品推荐完全一致 */
.category-products-grid .product-card-category {
    background: white !important;
    border-radius: 16rpx !important;
    overflow: hidden !important;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08) !important;
    transition: all 0.3s ease !important;
    width: auto !important;
    margin: 0 !important;
    float: none !important;
    display: block !important;
}

.category-products-grid .product-card-category:active {
    transform: translateY(-4rpx) !important;
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.12) !important;
}

.category-products-grid .product-image-container {
    position: relative !important;
    width: 100% !important;
    aspect-ratio: 1 !important;
    overflow: hidden !important;
}

.category-products-grid .product-image-category {
    width: 100% !important;
    height: 100% !important;
    background: #f5f5f5 !important;
}

.category-products-grid .new-badge-tag {
    position: absolute !important;
    top: 12rpx !important;
    left: 0 !important;
    background: linear-gradient(135deg, #10b981, #059669) !important;
    color: white !important;
    font-size: 20rpx !important;
    padding: 6rpx 16rpx !important;
    border-radius: 0 20rpx 20rpx 0 !important;
    z-index: 2 !important;
}

.category-products-grid .sold-overlay {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.5) !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    z-index: 3 !important;
}

.category-products-grid .soldout-icon {
    width: 80rpx !important;
    height: 80rpx !important;
}

/* 分类商品信息区域 - 与新品推荐完全一致 */
.category-products-grid .product-info-category {
    padding: 20rpx !important;
}

.category-products-grid .product-title-category {
    font-size: 26rpx !important;
    color: #333 !important;
    margin-bottom: 12rpx !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    line-height: 1.4 !important;
    height: 72rpx !important;
}

.category-products-grid .product-price-row {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 12rpx !important;
}

.category-products-grid .product-price-category {
    font-size: 28rpx !important;
    font-weight: 500 !important;
    color: #ff3456 !important;
}

.category-products-grid .add-cart-btn-category {
    width: 48rpx !important;
    height: 48rpx !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
}

.category-products-grid .add-cart-btn-category:active {
    transform: scale(0.9) !important;
    box-shadow: 0 2rpx 4rpx rgba(239, 68, 68, 0.4) !important;
}

.category-products-grid .cart-icon-category {
    width: 42rpx !important;
    height: 42rpx !important;
    filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%) !important;
}

.category-products-grid .product-meta {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

.category-products-grid .sold-count {
    font-size: 22rpx !important;
    color: #999 !important;
}

.category-products-grid .rating {
    display: flex !important;
    align-items: center !important;
    gap: 4rpx !important;
}

.category-products-grid .star-icon {
    width: 20rpx !important;
    height: 20rpx !important;
}

.category-products-grid .rating-text {
    font-size: 22rpx !important;
    color: #999 !important;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
}

.section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
}

.section-more {
    display: flex;
    align-items: center;
    gap: 8rpx;
}

.section-more text {
    font-size: 24rpx;
    color: #999;
}

.arrow-icon {
    width: 20rpx;
    height: 20rpx;
    opacity: 0.6;
}

.new-products-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
}

.product-card-new {
    background: white;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.product-card-new:active {
    transform: translateY(-4rpx);
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.12);
}

.product-image-container {
    position: relative;
    width: 100%;
    aspect-ratio: 1;
    overflow: hidden;
}

.product-image-new {
    width: 100%;
    height: 100%;
    background: #f5f5f5;
}

.new-badge-tag {
    position: absolute;
    top: 12rpx;
    left: 0;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    font-size: 20rpx;
    padding: 6rpx 16rpx;
    border-radius: 0 20rpx 20rpx 0;
    z-index: 2;
}

.product-info-new {
    padding: 20rpx;
}

.product-title-new {
    font-size: 26rpx;
    color: #333;
    margin-bottom: 12rpx;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
    height: 72rpx;
}

.product-price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12rpx;
}

.product-price-new {
    font-size: 28rpx;
    font-weight: 500;
    color: #ff3456;
}

.add-cart-btn-new {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.add-cart-btn-new:active {
    transform: scale(0.9);
    box-shadow: 0 2rpx 4rpx rgba(239, 68, 68, 0.4);
}

.cart-icon-new {
    width: 42rpx;
    height: 42rpx;
    filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%);
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sold-count {
    font-size: 22rpx;
    color: #999;
}

.rating {
    display: flex;
    align-items: center;
    gap: 4rpx;
}

.star-icon {
    width: 20rpx;
    height: 20rpx;
}

.rating-text {
    font-size: 22rpx;
    color: #999;
}

.no-goods-mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 220rpx;
    height: 220rpx;
    background: #000;
    opacity: 0.3;
}

/* 信息容器 */



.list-wrap .new-box {
    width: 210rpx;
    /* height: 330rpx; */
    float: left;
    margin: 0 30rpx 20rpx 0;
    background: white;  /* 添加白色背景 */
    border-radius: 12rpx;  /* 添加圆角 */
    overflow: hidden;  /* 确保内容不超出圆角 */
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);  /* 添加轻微阴影 */
    transition: all 0.3s ease;  /* 添加过渡动画 */
}

.new-box .sold-img {
    position: absolute;
    left: 0;
    top: 0;
    width: 210rpx;
    height: 210rpx;
    display: flex;
    justify-content: center;
    align-items: center;
}

.new-box .soldout {
    height: 120rpx;
    width: 120rpx;
}

.new-buy .list-wrap,
.goods-container .list-wrap {
    display: block;
    width: 100%;
    padding: 26rpx 30rpx 10rpx 30rpx;
    height: auto;
    box-sizing: border-box;
}

.list-wrap .no-margin {
    margin-right: 0;
}

.new-box .navi-url {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: 8rpx;  /* 添加内边距 */
}

/* 商品卡片点击效果 */
.new-box:active {
    transform: translateY(-2rpx);  /* 轻微上移 */
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);  /* 增强阴影 */
}

.new-box .navi-url .image {
    width: 210rpx;
    height: 210rpx;
    border-radius: 12rpx;  /* 增加圆角 */
    background: #f9f9f9;
}

.new-box .navi-url .box {
    height: 210rpx;
    width: 210rpx;
    position: relative;
    margin-bottom: 10rpx;
    border-radius: 12rpx;  /* 增加圆角与图片保持一致 */
    overflow: hidden;  /* 确保子元素不会超出圆角边界 */
    /* border: 1rpx solid #eee; */
    /* box-sizing: border-box; */
}

.new-box .navi-url .box .new-tag {
    height: 36rpx;
    width: 60rpx;
    background: #ca2a1d;
    position: absolute;
    top: 20rpx;
    left: 0;
    line-height: 36rpx;
    text-align: center;
    font-size: 18rpx;
    color: #fff;
    border-radius: 0 40rpx 40rpx 0;
}

.new-box .navi-url .goods-info {
    padding: 8rpx 4rpx;  /* 为商品信息区域添加内边距 */
}

.new-box .navi-url .goods-info .goods-title {
    font-size: 24rpx;
    color: #222;
    margin-bottom: 6rpx;
    display: -webkit-box;
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
}

.new-box .navi-url .goods-info .goods-intro {
    font-size: 16rpx;
    color: #888;
    margin-bottom: 6rpx;
    /* height: 64rpx; */
    display: -webkit-box;
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
}

.new-box .navi-url .goods-info .price-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.new-box .navi-url .goods-info .price-container .l {
    display: flex;
    flex-direction: column;
}

.new-box .navi-url .goods-info .price-container .r {
    width: 46rpx;
    height: 46rpx;
}

.new-box .navi-url .goods-info .price-container .r .cart-img {
    width: 46rpx;
    height: 46rpx;
}

.new-box .navi-url .goods-info .price-container .l .h {
    font-size: 28rpx;
    font-weight: 500;
    color: #ff3456;
}

.new-box .navi-url .goods-info .price-container .l .b {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
}

.new-box .navi-url .goods-info .price-container .retail-price {
    font-size: 18rpx;
    color: #000;
    margin-bottom: 4rpx;
    /* font-weight: bold; */
}

.goods-container .topic-container {
    width: 100%;
    margin-bottom: 40rpx;
}

.topic-container .banner-container {
    margin: 20rpx 30rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.banner-container .banner-image-wrap {
    width: 100%;
}

.banner-container image {
    width: 100%;
    /* height: 300rpx; */
    border-radius: 10rpx;
}

.banner-container .text {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    text-align: center;
    font-size: 30rpx;
    font-weight: 500;
    z-index: 9;
    color: #fff;
}

.banner-container .bg {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: #000;
    border-radius: 10rpx;
    opacity: 0.2;
}

.category-title {
    padding: 0 30rpx;
    margin: 20rpx 0;
}

.category-title .title {
    width: 100%;
    height: 100rpx;
    border-radius: 8rpx;
    background: #f9f9f9;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.category-title .title .text {
    font-size: 30rpx;
    color: #333;
    font-weight: 500;
    height: 60rpx;
    line-height: 60rpx;
}

.category-title .title .line {
    height: 5rpx;
    width: 30rpx;
    border-radius: 100rpx;
    background: #ff3456;
}

.clearfix:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
}

/* 没有更多  */

.no-more-goods {
    text-align: center;
    font-size: 28rpx;
    margin: 20rpx 0 30rpx 0;
    color: #999;
}