<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>微信小程序商城后台 - 订单管理</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#4f46e5", secondary: "#6366f1" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
      font-family: "Inter", "PingFang SC", "Microsoft YaHei", sans-serif;
      overflow-x: hidden;
      }
      .sidebar {
      width: 240px;
      min-width: 240px;
      transition: all 0.3s;
      }
      .sidebar.collapsed {
      width: 80px;
      min-width: 80px;
      }
      .main-content {
      transition: all 0.3s;
      }
      .sidebar.collapsed + .main-content {
      margin-left: 80px;
      }
      .tab-active {
      color: #4f46e5;
      border-bottom: 2px solid #4f46e5;
      }
      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
      }
      .custom-switch {
      position: relative;
      display: inline-block;
      width: 40px;
      height: 20px;
      }
      .custom-switch input {
      opacity: 0;
      width: 0;
      height: 0;
      }
      .switch-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #e5e7eb;
      transition: .4s;
      border-radius: 20px;
      }
      .switch-slider:before {
      position: absolute;
      content: "";
      height: 16px;
      width: 16px;
      left: 2px;
      bottom: 2px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
      }
      input:checked + .switch-slider {
      background-color: #4f46e5;
      }
      input:checked + .switch-slider:before {
      transform: translateX(20px);
      }
      .date-range-picker {
      position: relative;
      }
      .date-range-picker input {
      padding-right: 30px;
      }
      .date-range-picker i {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      pointer-events: none;
      }
      .table-container {
      overflow-x: auto;
      }
      table {
      min-width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      }
      th, td {
      white-space: nowrap;
      padding: 12px 16px;
      text-align: left;
      }
      tbody tr {
      cursor: pointer;
      transition: all 0.2s;
      }
      tbody tr:hover {
      background-color: #f9fafb;
      }
      .pagination-button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      border-radius: 8px;
      transition: all 0.2s;
      }
      .pagination-button:hover {
      background-color: #f3f4f6;
      }
      .pagination-button.active {
      background-color: #4f46e5;
      color: white;
      }
    </style>
  </head>
  <body class="bg-gray-50 text-gray-800">
    <!-- 侧边栏 -->
    <div class="flex h-screen">
      <div
        id="sidebar"
        class="sidebar bg-white border-r h-full fixed left-0 top-0 overflow-y-auto z-10 shadow-sm"
      >
        <div class="p-4 flex items-center justify-between border-b">
          <div class="font-['Pacifico'] text-xl text-gray-800">logo</div>
          <button
            id="toggle-sidebar"
            class="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
          >
            <i class="ri-menu-fold-line ri-lg"></i>
          </button>
        </div>
        <div class="mt-2">
          <ul class="space-y-1">
            <li class="mx-2">
              <a
                href="#"
                class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group"
              >
                <div
                  class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-primary"
                >
                  <i class="ri-home-line"></i>
                </div>
                <span class="ml-3 menu-text text-sm">首页</span>
              </a>
            </li>
            <li class="mx-2">
              <a
                href="#"
                class="flex items-center px-3 py-2 bg-primary/5 text-primary rounded-lg group"
              >
                <div class="w-6 h-6 flex items-center justify-center">
                  <i class="ri-file-list-line"></i>
                </div>
                <span class="ml-3 menu-text text-sm font-medium">订单列表</span>
              </a>
            </li>
            <li class="mx-2">
              <div
                class="px-3 py-2 hover:bg-gray-100 rounded-lg cursor-pointer submenu-toggle group"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div
                      class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600"
                    >
                      <i class="ri-shopping-bag-line"></i>
                    </div>
                    <span class="ml-3 menu-text text-sm text-gray-600"
                      >商品管理</span
                    >
                  </div>
                  <i
                    class="ri-arrow-down-s-line submenu-arrow text-gray-400"
                  ></i>
                </div>
              </div>
              <ul class="submenu pl-9 mt-1 hidden">
                <li>
                  <a
                    href="#"
                    class="block px-3 py-2 text-sm text-gray-500 hover:text-primary hover:bg-gray-100 rounded-lg"
                    >商品列表</a
                  >
                </li>
                <li>
                  <a
                    href="#"
                    class="block px-3 py-2 text-sm text-gray-500 hover:text-primary hover:bg-gray-100 rounded-lg"
                    >商品设置</a
                  >
                </li>
              </ul>
            </li>
            <li class="mx-2">
              <a
                href="#"
                class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group"
              >
                <div
                  class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600"
                >
                  <i class="ri-shopping-cart-line"></i>
                </div>
                <span class="ml-3 menu-text text-sm">购物车</span>
              </a>
            </li>
            <li class="mx-2">
              <a
                href="#"
                class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group"
              >
                <div
                  class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600"
                >
                  <i class="ri-user-line"></i>
                </div>
                <span class="ml-3 menu-text text-sm">用户列表</span>
              </a>
            </li>
            <li class="mx-2">
              <div
                class="px-3 py-2 hover:bg-gray-100 rounded-lg cursor-pointer submenu-toggle group"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div
                      class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600"
                    >
                      <i class="ri-store-line"></i>
                    </div>
                    <span class="ml-3 menu-text text-sm text-gray-600"
                      >店铺设置</span
                    >
                  </div>
                  <i
                    class="ri-arrow-down-s-line submenu-arrow text-gray-400"
                  ></i>
                </div>
              </div>
              <ul class="submenu pl-9 mt-1 hidden">
                <li class="py-2 hover:text-primary">
                  <a href="#" class="block">显示设置</a>
                </li>
                <li class="py-2 hover:text-primary">
                  <a href="#" class="block">广告列表</a>
                </li>
                <li class="py-2 hover:text-primary">
                  <a href="#" class="block">公告管理</a>
                </li>
                <li class="py-2 hover:text-primary">
                  <a href="#" class="block">运费模板</a>
                </li>
                <li class="py-2 hover:text-primary">
                  <a href="#" class="block">快递设置</a>
                </li>
                <li class="py-2 hover:text-primary">
                  <a href="#" class="block">管理员</a>
                </li>
              </ul>
            </li>
            <li class="mx-2">
              <a
                href="#"
                class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group"
              >
                <div
                  class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600"
                >
                  <i class="ri-bar-chart-line"></i>
                </div>
                <span class="ml-3 menu-text text-sm">数据统计</span>
              </a>
            </li>
            <li class="mx-2 mt-8">
              <a
                href="#"
                class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group"
              >
                <div
                  class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600"
                >
                  <i class="ri-logout-box-line"></i>
                </div>
                <span class="ml-3 menu-text text-sm">退出</span>
              </a>
            </li>
          </ul>
        </div>
      </div>
      <!-- 主内容区 -->
      <div id="main-content" class="main-content flex-1 ml-60 p-6 min-h-screen">
        <!-- 面包屑导航 -->
        <div class="flex items-center text-sm text-gray-500 mb-4">
          <a href="#" class="hover:text-primary">首页</a>
          <span class="mx-2">/</span>
          <span class="text-gray-700">订单管理</span>
        </div>
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div class="bg-white rounded shadow p-5">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-500 text-sm">今日订单</p>
                <h3 class="text-2xl font-bold mt-1">128</h3>
                <p class="text-green-500 text-sm mt-1">
                  +12.5% <span class="text-gray-500">较昨日</span>
                </p>
              </div>
              <div
                class="w-12 h-12 flex items-center justify-center bg-blue-100 text-primary rounded-full"
              >
                <i class="ri-file-list-line ri-xl"></i>
              </div>
            </div>
          </div>
          <div class="bg-white rounded shadow p-5">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-500 text-sm">待处理订单</p>
                <h3 class="text-2xl font-bold mt-1">42</h3>
                <p class="text-red-500 text-sm mt-1">
                  +8.3% <span class="text-gray-500">较昨日</span>
                </p>
              </div>
              <div
                class="w-12 h-12 flex items-center justify-center bg-yellow-100 text-yellow-500 rounded-full"
              >
                <i class="ri-time-line ri-xl"></i>
              </div>
            </div>
          </div>
          <div class="bg-white rounded shadow p-5">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-500 text-sm">今日销售额</p>
                <h3 class="text-2xl font-bold mt-1">¥15,842</h3>
                <p class="text-green-500 text-sm mt-1">
                  +16.2% <span class="text-gray-500">较昨日</span>
                </p>
              </div>
              <div
                class="w-12 h-12 flex items-center justify-center bg-green-100 text-green-500 rounded-full"
              >
                <i class="ri-money-cny-circle-line ri-xl"></i>
              </div>
            </div>
          </div>
          <div class="bg-white rounded shadow p-5">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-500 text-sm">本月销售额</p>
                <h3 class="text-2xl font-bold mt-1">¥253,692</h3>
                <p class="text-green-500 text-sm mt-1">
                  +8.7% <span class="text-gray-500">较上月</span>
                </p>
              </div>
              <div
                class="w-12 h-12 flex items-center justify-center bg-purple-100 text-purple-500 rounded-full"
              >
                <i class="ri-line-chart-line ri-xl"></i>
              </div>
            </div>
          </div>
        </div>
        <!-- 销售趋势图 -->
        <div class="bg-white rounded shadow mb-6">
          <div class="p-4 border-b">
            <h2 class="text-lg font-medium">销售趋势</h2>
          </div>
          <div class="p-4">
            <div id="sales-chart" style="width: 100%; height: 300px;"></div>
          </div>
        </div>
        <!-- 订单管理标题 -->
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-2xl font-bold">订单管理</h1>
          <button
            class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-button flex items-center whitespace-nowrap"
          >
            <i class="ri-add-line mr-1"></i> 新增订单
          </button>
        </div>
        <!-- 订单状态标签 -->
        <div class="bg-white rounded shadow mb-6">
          <div class="flex border-b overflow-x-auto">
            <button class="tab-active px-6 py-4 font-medium whitespace-nowrap">
              全部订单
              <span
                class="ml-1 bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full"
                >1,284</span
              >
            </button>
            <button
              class="px-6 py-4 text-gray-500 hover:text-gray-700 whitespace-nowrap"
            >
              待付款
              <span
                class="ml-1 bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full"
                >36</span
              >
            </button>
            <button
              class="px-6 py-4 text-gray-500 hover:text-gray-700 whitespace-nowrap"
            >
              待发货
              <span
                class="ml-1 bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full"
                >42</span
              >
            </button>
            <button
              class="px-6 py-4 text-gray-500 hover:text-gray-700 whitespace-nowrap"
            >
              待收货
              <span
                class="ml-1 bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full"
                >18</span
              >
            </button>
            <button
              class="px-6 py-4 text-gray-500 hover:text-gray-700 whitespace-nowrap"
            >
              已收货
              <span
                class="ml-1 bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full"
                >1,158</span
              >
            </button>
            <button
              class="px-6 py-4 text-gray-500 hover:text-gray-700 whitespace-nowrap"
            >
              已关闭
              <span
                class="ml-1 bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full"
                >30</span
              >
            </button>
          </div>
          <!-- 筛选工具栏 -->
          <div class="p-4 flex flex-wrap gap-4 items-center border-b">
            <div class="relative">
              <input
                type="text"
                placeholder="搜索订单号/商品/用户"
                class="pl-10 pr-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary w-64 text-sm"
              />
              <div
                class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center text-gray-400"
              >
                <i class="ri-search-line"></i>
              </div>
            </div>
            <div class="date-range-picker">
              <input
                type="text"
                placeholder="开始日期"
                class="px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary w-36 text-sm"
              />
              <i class="ri-calendar-line text-gray-400"></i>
            </div>
            <div class="date-range-picker">
              <input
                type="text"
                placeholder="结束日期"
                class="px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary w-36 text-sm"
              />
              <i class="ri-calendar-line text-gray-400"></i>
            </div>
            <div class="relative">
              <button
                class="flex items-center justify-between px-4 py-2 border rounded bg-white hover:bg-gray-50 w-40 text-sm whitespace-nowrap"
              >
                <span>订单状态</span>
                <i class="ri-arrow-down-s-line"></i>
              </button>
            </div>
            <button
              class="bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-button text-gray-700 flex items-center whitespace-nowrap"
            >
              <i class="ri-filter-line mr-1"></i> 筛选
            </button>
            <button
              class="bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-button text-gray-700 flex items-center ml-auto whitespace-nowrap"
            >
              <i class="ri-download-line mr-1"></i> 导出
            </button>
          </div>
          <!-- 订单列表 -->
          <div class="table-container">
            <table class="w-full">
              <thead>
                <tr class="bg-gray-50 text-gray-500 text-xs">
                  <th class="py-3 px-4 font-medium">
                    <div class="flex items-center">
                      <input
                        type="checkbox"
                        class="mr-2 w-4 h-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      商品信息
                    </div>
                  </th>
                  <th class="py-3 px-4 font-medium">订单号</th>
                  <th class="py-3 px-4 font-medium">下单时间</th>
                  <th class="py-3 px-4 font-medium">付款时间</th>
                  <th class="py-3 px-4 font-medium">商品价格</th>
                  <th class="py-3 px-4 font-medium">下单账号</th>
                  <th class="py-3 px-4 font-medium">收货地址</th>
                  <th class="py-3 px-4 font-medium">订单状态</th>
                  <th class="py-3 px-4 font-medium">操作</th>
                </tr>
              </thead>
              <tbody class="divide-y">
                <tr class="hover:bg-gray-50 cursor-pointer">
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <input
                        type="checkbox"
                        class="mr-2 w-4 h-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <div class="flex">
                        <img
                          src="https://readdy.ai/api/search-image?query=minimalist%20product%20photo%20of%20a%20white%20ceramic%20coffee%20mug%20on%20a%20clean%20white%20background%2C%20professional%20e-commerce%20style%2C%20high%20quality%20product%20photography&width=80&height=80&seq=1&orientation=squarish"
                          alt="商品图片"
                          class="w-16 h-16 object-cover rounded mr-3"
                        />
                        <div>
                          <p class="font-medium">简约陶瓷咖啡杯</p>
                          <p class="text-gray-500 text-sm">规格：300ml 白色</p>
                          <p class="text-gray-500 text-sm">数量：2件</p>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4 text-sm">WX20250604152311</td>
                  <td class="py-4 px-4 text-sm">2025-06-04 15:23</td>
                  <td class="py-4 px-4 text-sm">2025-06-04 15:25</td>
                  <td class="py-4 px-4 font-medium">¥128.00</td>
                  <td class="py-4 px-4 text-sm">微信用户_7a9b</td>
                  <td class="py-4 px-4 text-sm">北京市朝阳区</td>
                  <td class="py-4 px-4">
                    <span
                      class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full"
                      >待发货</span
                    >
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button
                        class="text-primary hover:text-primary/80 text-sm whitespace-nowrap"
                      >
                        详情
                      </button>
                      <button
                        class="text-primary hover:text-primary/80 text-sm whitespace-nowrap"
                      >
                        发货
                      </button>
                    </div>
                  </td>
                </tr>
                <tr class="hover:bg-gray-50 cursor-pointer">
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <input
                        type="checkbox"
                        class="mr-2 w-4 h-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <div class="flex">
                        <img
                          src="https://readdy.ai/api/search-image?query=minimalist%20product%20photo%20of%20a%20black%20leather%20wallet%20on%20a%20clean%20white%20background%2C%20professional%20e-commerce%20style%2C%20high%20quality%20product%20photography&width=80&height=80&seq=2&orientation=squarish"
                          alt="商品图片"
                          class="w-16 h-16 object-cover rounded mr-3"
                        />
                        <div>
                          <p class="font-medium">真皮短款钱包</p>
                          <p class="text-gray-500 text-sm">规格：黑色</p>
                          <p class="text-gray-500 text-sm">数量：1件</p>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4 text-sm">WX20250604143512</td>
                  <td class="py-4 px-4 text-sm">2025-06-04 14:35</td>
                  <td class="py-4 px-4 text-sm">2025-06-04 14:36</td>
                  <td class="py-4 px-4 font-medium">¥299.00</td>
                  <td class="py-4 px-4 text-sm">陈文杰</td>
                  <td class="py-4 px-4 text-sm">上海市浦东新区</td>
                  <td class="py-4 px-4">
                    <span
                      class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full"
                      >待发货</span
                    >
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button
                        class="text-primary hover:text-primary/80 text-sm whitespace-nowrap"
                      >
                        详情
                      </button>
                      <button
                        class="text-primary hover:text-primary/80 text-sm whitespace-nowrap"
                      >
                        发货
                      </button>
                    </div>
                  </td>
                </tr>
                <tr class="hover:bg-gray-50 cursor-pointer">
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <input
                        type="checkbox"
                        class="mr-2 w-4 h-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <div class="flex">
                        <img
                          src="https://readdy.ai/api/search-image?query=minimalist%20product%20photo%20of%20wireless%20earbuds%20with%20charging%20case%20on%20a%20clean%20white%20background%2C%20professional%20e-commerce%20style%2C%20high%20quality%20product%20photography&width=80&height=80&seq=3&orientation=squarish"
                          alt="商品图片"
                          class="w-16 h-16 object-cover rounded mr-3"
                        />
                        <div>
                          <p class="font-medium">无线蓝牙耳机</p>
                          <p class="text-gray-500 text-sm">规格：白色</p>
                          <p class="text-gray-500 text-sm">数量：1件</p>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4 text-sm">WX20250604132145</td>
                  <td class="py-4 px-4 text-sm">2025-06-04 13:21</td>
                  <td class="py-4 px-4 text-sm">2025-06-04 13:22</td>
                  <td class="py-4 px-4 font-medium">¥499.00</td>
                  <td class="py-4 px-4 text-sm">林小雨</td>
                  <td class="py-4 px-4 text-sm">广州市天河区</td>
                  <td class="py-4 px-4">
                    <span
                      class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                      >待收货</span
                    >
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button
                        class="text-primary hover:text-primary/80 text-sm whitespace-nowrap"
                      >
                        详情
                      </button>
                      <button
                        class="text-gray-500 hover:text-gray-700 text-sm whitespace-nowrap"
                      >
                        物流
                      </button>
                    </div>
                  </td>
                </tr>
                <tr class="hover:bg-gray-50 cursor-pointer">
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <input
                        type="checkbox"
                        class="mr-2 w-4 h-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <div class="flex">
                        <img
                          src="https://readdy.ai/api/search-image?query=minimalist%20product%20photo%20of%20a%20small%20potted%20succulent%20plant%20on%20a%20clean%20white%20background%2C%20professional%20e-commerce%20style%2C%20high%20quality%20product%20photography&width=80&height=80&seq=4&orientation=squarish"
                          alt="商品图片"
                          class="w-16 h-16 object-cover rounded mr-3"
                        />
                        <div>
                          <p class="font-medium">多肉植物盆栽套装</p>
                          <p class="text-gray-500 text-sm">规格：3盆装</p>
                          <p class="text-gray-500 text-sm">数量：1件</p>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4 text-sm">WX20250604112233</td>
                  <td class="py-4 px-4 text-sm">2025-06-04 11:22</td>
                  <td class="py-4 px-4 text-sm">-</td>
                  <td class="py-4 px-4 font-medium">¥89.90</td>
                  <td class="py-4 px-4 text-sm">张思琪</td>
                  <td class="py-4 px-4 text-sm">成都市武侯区</td>
                  <td class="py-4 px-4">
                    <span
                      class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full"
                      >待付款</span
                    >
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button
                        class="text-primary hover:text-primary/80 text-sm whitespace-nowrap"
                      >
                        详情
                      </button>
                      <button
                        class="text-red-500 hover:text-red-700 text-sm whitespace-nowrap"
                      >
                        关闭
                      </button>
                    </div>
                  </td>
                </tr>
                <tr class="hover:bg-gray-50 cursor-pointer">
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <input
                        type="checkbox"
                        class="mr-2 w-4 h-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <div class="flex">
                        <img
                          src="https://readdy.ai/api/search-image?query=minimalist%20product%20photo%20of%20a%20stainless%20steel%20water%20bottle%20on%20a%20clean%20white%20background%2C%20professional%20e-commerce%20style%2C%20high%20quality%20product%20photography&width=80&height=80&seq=5&orientation=squarish"
                          alt="商品图片"
                          class="w-16 h-16 object-cover rounded mr-3"
                        />
                        <div>
                          <p class="font-medium">不锈钢保温杯</p>
                          <p class="text-gray-500 text-sm">规格：500ml 银色</p>
                          <p class="text-gray-500 text-sm">数量：2件</p>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4 text-sm">WX20250604103045</td>
                  <td class="py-4 px-4 text-sm">2025-06-04 10:30</td>
                  <td class="py-4 px-4 text-sm">2025-06-04 10:32</td>
                  <td class="py-4 px-4 font-medium">¥158.00</td>
                  <td class="py-4 px-4 text-sm">王建国</td>
                  <td class="py-4 px-4 text-sm">深圳市南山区</td>
                  <td class="py-4 px-4">
                    <span
                      class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full"
                      >已收货</span
                    >
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button
                        class="text-primary hover:text-primary/80 text-sm whitespace-nowrap"
                      >
                        详情
                      </button>
                      <button
                        class="text-gray-500 hover:text-gray-700 text-sm whitespace-nowrap"
                      >
                        评价
                      </button>
                    </div>
                  </td>
                </tr>
                <tr class="hover:bg-gray-50 cursor-pointer">
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <input
                        type="checkbox"
                        class="mr-2 w-4 h-4 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <div class="flex">
                        <img
                          src="https://readdy.ai/api/search-image?query=minimalist%20product%20photo%20of%20a%20smartphone%20case%20on%20a%20clean%20white%20background%2C%20professional%20e-commerce%20style%2C%20high%20quality%20product%20photography&width=80&height=80&seq=6&orientation=squarish"
                          alt="商品图片"
                          class="w-16 h-16 object-cover rounded mr-3"
                        />
                        <div>
                          <p class="font-medium">手机保护壳</p>
                          <p class="text-gray-500 text-sm">
                            规格：iPhone 15 Pro 透明
                          </p>
                          <p class="text-gray-500 text-sm">数量：1件</p>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4 text-sm">WX20250604093512</td>
                  <td class="py-4 px-4 text-sm">2025-06-04 09:35</td>
                  <td class="py-4 px-4 text-sm">2025-06-04 09:36</td>
                  <td class="py-4 px-4 font-medium">¥79.00</td>
                  <td class="py-4 px-4 text-sm">刘明辉</td>
                  <td class="py-4 px-4 text-sm">杭州市西湖区</td>
                  <td class="py-4 px-4">
                    <span
                      class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full"
                      >已关闭</span
                    >
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button
                        class="text-primary hover:text-primary/80 text-sm whitespace-nowrap"
                      >
                        详情
                      </button>
                      <button
                        class="text-gray-500 hover:text-gray-700 text-sm whitespace-nowrap"
                      >
                        删除
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <!-- 分页 -->
          <div class="p-4 flex items-center justify-between border-t">
            <div class="text-sm text-gray-500">显示 1 至 6 条，共 1,284 条</div>
            <div class="flex items-center space-x-2">
              <button class="pagination-button" disabled>
                <i class="ri-arrow-left-s-line"></i>
              </button>
              <button class="pagination-button active">1</button>
              <button class="pagination-button">2</button>
              <button class="pagination-button">3</button>
              <button class="pagination-button">4</button>
              <button class="pagination-button">5</button>
              <button class="pagination-button">
                <i class="ri-more-line"></i>
              </button>
              <button class="pagination-button">
                <i class="ri-arrow-right-s-line"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 订单详情模态框 -->
    <div
      id="order-detail-modal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden"
    >
      <div
        class="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto"
      >
        <div class="flex justify-between items-center p-4 border-b">
          <h3 class="text-lg font-medium">订单详情</h3>
          <button id="close-modal" class="text-gray-500 hover:text-gray-700">
            <i class="ri-close-line ri-lg"></i>
          </button>
        </div>
        <div class="p-6">
          <div class="mb-6">
            <h4 class="text-base font-medium mb-3">订单信息</h4>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-500">订单编号：</span>
                <span>WX20250604152311</span>
              </div>
              <div>
                <span class="text-gray-500">订单状态：</span>
                <span class="text-yellow-600">待发货</span>
              </div>
              <div>
                <span class="text-gray-500">下单时间：</span>
                <span>2025-06-04 15:23</span>
              </div>
              <div>
                <span class="text-gray-500">付款时间：</span>
                <span>2025-06-04 15:25</span>
              </div>
              <div>
                <span class="text-gray-500">支付方式：</span>
                <span>微信支付</span>
              </div>
              <div>
                <span class="text-gray-500">订单来源：</span>
                <span>微信小程序</span>
              </div>
            </div>
          </div>
          <div class="mb-6">
            <h4 class="text-base font-medium mb-3">收货信息</h4>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-500">收货人：</span>
                <span>张晓明</span>
              </div>
              <div>
                <span class="text-gray-500">联系电话：</span>
                <span>138****5678</span>
              </div>
              <div class="col-span-2">
                <span class="text-gray-500">收货地址：</span>
                <span>北京市朝阳区建国路88号现代城5号楼2单元801</span>
              </div>
            </div>
          </div>
          <div class="mb-6">
            <h4 class="text-base font-medium mb-3">商品信息</h4>
            <div class="border rounded overflow-hidden">
              <table class="w-full text-sm">
                <thead>
                  <tr class="bg-gray-50">
                    <th class="py-3 px-4 text-left font-medium">商品</th>
                    <th class="py-3 px-4 text-center font-medium">单价</th>
                    <th class="py-3 px-4 text-center font-medium">数量</th>
                    <th class="py-3 px-4 text-right font-medium">小计</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="border-t">
                    <td class="py-3 px-4">
                      <div class="flex items-center">
                        <img
                          src="https://readdy.ai/api/search-image?query=minimalist%20product%20photo%20of%20a%20white%20ceramic%20coffee%20mug%20on%20a%20clean%20white%20background%2C%20professional%20e-commerce%20style%2C%20high%20quality%20product%20photography&width=60&height=60&seq=1&orientation=squarish"
                          alt="商品图片"
                          class="w-12 h-12 object-cover rounded mr-3"
                        />
                        <div>
                          <p class="font-medium">简约陶瓷咖啡杯</p>
                          <p class="text-gray-500">规格：300ml 白色</p>
                        </div>
                      </div>
                    </td>
                    <td class="py-3 px-4 text-center">¥64.00</td>
                    <td class="py-3 px-4 text-center">2</td>
                    <td class="py-3 px-4 text-right font-medium">¥128.00</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="mb-6">
            <h4 class="text-base font-medium mb-3">费用信息</h4>
            <div class="text-sm text-right space-y-2">
              <div>
                <span class="text-gray-500">商品金额：</span>
                <span>¥128.00</span>
              </div>
              <div>
                <span class="text-gray-500">运费：</span>
                <span>¥0.00</span>
              </div>
              <div>
                <span class="text-gray-500">优惠金额：</span>
                <span>-¥0.00</span>
              </div>
              <div class="text-lg font-medium">
                <span>实付金额：</span>
                <span class="text-primary">¥128.00</span>
              </div>
            </div>
          </div>
          <div class="flex justify-end space-x-3 mt-6">
            <button
              class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 whitespace-nowrap"
            >
              打印订单
            </button>
            <button
              class="px-4 py-2 bg-primary hover:bg-primary/90 text-white rounded-button whitespace-nowrap"
            >
              确认发货
            </button>
          </div>
        </div>
      </div>
    </div>
    <script id="sidebar-toggle-script">
      document.addEventListener("DOMContentLoaded", function () {
        const sidebar = document.getElementById("sidebar");
        const mainContent = document.getElementById("main-content");
        const toggleButton = document.getElementById("toggle-sidebar");
        const menuTexts = document.querySelectorAll(".menu-text");
        const submenuToggles = document.querySelectorAll(".submenu-toggle");
        const submenus = document.querySelectorAll(".submenu");
        const submenuArrows = document.querySelectorAll(".submenu-arrow");
        toggleButton.addEventListener("click", function () {
          sidebar.classList.toggle("collapsed");
          if (sidebar.classList.contains("collapsed")) {
            menuTexts.forEach((text) => (text.style.display = "none"));
            submenuArrows.forEach((arrow) => (arrow.style.display = "none"));
            submenus.forEach((menu) => menu.classList.add("hidden"));
            toggleButton.innerHTML = '<i class="ri-menu-unfold-line ri-lg"></i>';
            mainContent.style.marginLeft = "80px";
          } else {
            menuTexts.forEach((text) => (text.style.display = "block"));
            submenuArrows.forEach((arrow) => (arrow.style.display = "block"));
            toggleButton.innerHTML = '<i class="ri-menu-fold-line ri-lg"></i>';
            mainContent.style.marginLeft = "240px";
          }
        });
        submenuToggles.forEach((toggle, index) => {
          toggle.addEventListener("click", function () {
            const submenu = submenus[index];
            const arrow = submenuArrows[index];
            submenu.classList.toggle("hidden");
            if (submenu.classList.contains("hidden")) {
              arrow.classList.remove("transform", "rotate-180");
            } else {
              arrow.classList.add("transform", "rotate-180");
            }
          });
        });
      });
    </script>
    <script id="tab-switcher-script">
      document.addEventListener("DOMContentLoaded", function () {
        const tabs = document.querySelectorAll(
          ".bg-white.rounded.shadow.mb-6 > .flex.border-b > button",
        );
        tabs.forEach((tab) => {
          tab.addEventListener("click", function () {
            // 移除所有标签的激活状态
            tabs.forEach((t) => {
              t.classList.remove("tab-active");
              t.classList.add("text-gray-500", "hover:text-gray-700");
            });
            // 添加当前标签的激活状态
            this.classList.add("tab-active");
            this.classList.remove("text-gray-500", "hover:text-gray-700");
          });
        });
      });
    </script>
    <script id="order-detail-script">
      document.addEventListener("DOMContentLoaded", function () {
        const orderRows = document.querySelectorAll("tbody tr");
        const orderDetailModal = document.getElementById("order-detail-modal");
        const closeModalButton = document.getElementById("close-modal");
        const detailButtons = document.querySelectorAll("button.text-primary");
        function openOrderDetail() {
          orderDetailModal.classList.remove("hidden");
          document.body.style.overflow = "hidden";
        }
        function closeOrderDetail() {
          orderDetailModal.classList.add("hidden");
          document.body.style.overflow = "auto";
        }
        orderRows.forEach((row) => {
          row.addEventListener("click", function (e) {
            // 避免点击复选框和按钮时触发
            if (
              !e.target.closest('input[type="checkbox"]') &&
              !e.target.closest("button")
            ) {
              openOrderDetail();
            }
          });
        });
        detailButtons.forEach((button) => {
          button.addEventListener("click", openOrderDetail);
        });
        closeModalButton.addEventListener("click", closeOrderDetail);
        // 点击模态框外部关闭
        orderDetailModal.addEventListener("click", function (e) {
          if (e.target === orderDetailModal) {
            closeOrderDetail();
          }
        });
        // ESC键关闭
        document.addEventListener("keydown", function (e) {
          if (e.key === "Escape" && !orderDetailModal.classList.contains("hidden")) {
            closeOrderDetail();
          }
        });
      });
    </script>
    <script id="sales-chart-script">
      document.addEventListener("DOMContentLoaded", function () {
        const chartDom = document.getElementById("sales-chart");
        const myChart = echarts.init(chartDom);
        const option = {
          animation: false,
          tooltip: {
            trigger: "axis",
            backgroundColor: "rgba(255, 255, 255, 0.8)",
            borderColor: "#e5e7eb",
            borderWidth: 1,
            textStyle: {
              color: "#1f2937",
            },
          },
          legend: {
            data: ["订单数", "销售额"],
            right: 10,
            textStyle: {
              color: "#1f2937",
            },
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            top: "60px",
            containLabel: true,
          },
          xAxis: {
            type: "category",
            boundaryGap: false,
            data: [
              "5月29日",
              "5月30日",
              "5月31日",
              "6月1日",
              "6月2日",
              "6月3日",
              "6月4日",
            ],
            axisLine: {
              lineStyle: {
                color: "#e5e7eb",
              },
            },
            axisLabel: {
              color: "#6b7280",
            },
          },
          yAxis: [
            {
              type: "value",
              name: "订单数",
              position: "left",
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#57b5e7",
                },
              },
              axisLabel: {
                color: "#6b7280",
                formatter: "{value} 单",
              },
              splitLine: {
                lineStyle: {
                  color: "#f3f4f6",
                },
              },
            },
            {
              type: "value",
              name: "销售额",
              position: "right",
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#fc8d62",
                },
              },
              axisLabel: {
                color: "#6b7280",
                formatter: "¥{value}",
              },
              splitLine: {
                show: false,
              },
            },
          ],
          series: [
            {
              name: "订单数",
              type: "line",
              smooth: true,
              data: [120, 132, 101, 134, 90, 180, 128],
              itemStyle: {
                color: "#57b5e7",
              },
              lineStyle: {
                width: 3,
              },
              showSymbol: false,
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(87, 181, 231, 0.2)",
                    },
                    {
                      offset: 1,
                      color: "rgba(87, 181, 231, 0.05)",
                    },
                  ],
                },
              },
            },
            {
              name: "销售额",
              type: "line",
              smooth: true,
              yAxisIndex: 1,
              data: [12000, 13200, 10100, 13400, 9000, 18000, 15842],
              itemStyle: {
                color: "#fc8d62",
              },
              lineStyle: {
                width: 3,
              },
              showSymbol: false,
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(252, 141, 98, 0.2)",
                    },
                    {
                      offset: 1,
                      color: "rgba(252, 141, 98, 0.05)",
                    },
                  ],
                },
              },
            },
          ],
        };
        myChart.setOption(option);
        window.addEventListener("resize", function () {
          myChart.resize();
        });
      });
    </script>
  </body>
</html>
