<view class="container">
  <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
    <image class="avatar" src="{{root + avatarUrl}}"></image>
  </button>
  <view class='edit-container'>
    <view class="a-item">
      <image class="icon" src="/images/icon/nick.png"></image>
      <input type="nickname" class="a-input" bindinput="bindinputNickName" bindblur="bindinputNickName" value="{{nickName}}" placeholder="请输入昵称" />
    </view>
    <view class="a-item">
      <image class="icon" src="/images/icon/receiver.png"></image>
      <input class='a-input' bindinput="bindinputName" bindblur="bindinputName" placeholder='请输入姓名' value="{{name}}"></input>
    </view>
    <view class="a-item">
      <image class="icon" src="/images/icon/mobile.png"></image>
      <input class='a-input' bindinput="mobilechange" bindblur="mobilechange" value="{{mobile}}" placeholder='请输入手机'></input>
    </view>
  </view>
  <view class='wrap-btn'>
    <view class="btn-wrap active" bindtap='saveInfo'>保存</view>
  </view>
</view>