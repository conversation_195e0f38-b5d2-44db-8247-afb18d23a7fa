function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

/**
 * 多商品秒杀轮次状态调度服务
 * 负责自动更新轮次状态：upcoming -> active -> ended
 */

module.exports = class FlashSaleMultiScheduler {

  /**
   * 获取当前本地时间字符串（YYYY-MM-DD HH:mm:ss格式）
   * 解决时区问题
   */
  getCurrentLocalTimeString() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  /**
   * 更新所有轮次状态
   * @param {Object} model 数据模型
   */
  updateRoundStatus(model) {
    var _this = this;

    return _asyncToGenerator(function* () {
      try {
        const roundModel = model('flash_sale_rounds');
        const nowStr = _this.getCurrentLocalTimeString();

        console.log(`[${nowStr}] 开始检查轮次状态...`);

        // 调试：查看所有upcoming状态的轮次
        const allUpcoming = yield roundModel.where({
          status: 'upcoming'
        }).select();

        if (allUpcoming.length > 0) {
          console.log('📋 当前所有upcoming轮次:');
          allUpcoming.forEach(function (r) {
            console.log(`  - 轮次${r.round_number}: ${r.start_time} ~ ${r.end_time} (当前时间: ${nowStr})`);
            console.log(`    时间比较: start_time <= nowStr ? ${r.start_time <= nowStr}`);
          });
        }

        // 1. 将到达开始时间的upcoming轮次设为active
        const upcomingToActive = yield roundModel.where({
          start_time: ['<=', nowStr],
          status: 'upcoming'
        }).select();

        if (upcomingToActive.length > 0) {
          yield roundModel.where({
            start_time: ['<=', nowStr],
            status: 'upcoming'
          }).update({ status: 'active' });

          console.log(`✅ 启动了 ${upcomingToActive.length} 个轮次:`, upcomingToActive.map(function (r) {
            return `轮次${r.round_number}(${r.round_name})`;
          }).join(', '));
        }

        // 2. 将超过结束时间的active轮次设为ended
        const activeToEnded = yield roundModel.where({
          end_time: ['<', nowStr],
          status: 'active'
        }).select();

        if (activeToEnded.length > 0) {
          yield roundModel.where({
            end_time: ['<', nowStr],
            status: 'active'
          }).update({ status: 'ended' });

          console.log(`⏰ 结束了 ${activeToEnded.length} 个轮次:`, activeToEnded.map(function (r) {
            return `轮次${r.round_number}(${r.round_name})`;
          }).join(', '));
        }

        // 3. 将超过结束时间的upcoming轮次也设为ended（防止遗漏）
        const upcomingToEnded = yield roundModel.where({
          end_time: ['<', nowStr],
          status: 'upcoming'
        }).select();

        if (upcomingToEnded.length > 0) {
          yield roundModel.where({
            end_time: ['<', nowStr],
            status: 'upcoming'
          }).update({ status: 'ended' });

          console.log(`⚠️ 直接结束了 ${upcomingToEnded.length} 个未启动的过期轮次:`, upcomingToEnded.map(function (r) {
            return `轮次${r.round_number}(${r.round_name})`;
          }).join(', '));
        }

        const totalUpdated = upcomingToActive.length + activeToEnded.length + upcomingToEnded.length;
        if (totalUpdated === 0) {
          console.log('📊 所有轮次状态正常，无需更新');
        }

        return {
          activated: upcomingToActive.length,
          ended: activeToEnded.length + upcomingToEnded.length,
          total: totalUpdated
        };
      } catch (error) {
        console.error('❌ 更新轮次状态失败:', error);
        throw error;
      }
    })();
  }

  /**
   * 获取当前活跃轮次统计
   * @param {Object} model 数据模型
   */
  getActiveRoundsStats(model) {
    return _asyncToGenerator(function* () {
      try {
        const roundModel = model('flash_sale_rounds');

        const stats = yield roundModel.field('status, COUNT(*) as count').group('status').select();

        const result = {
          upcoming: 0,
          active: 0,
          ended: 0,
          cancelled: 0
        };

        stats.forEach(function (stat) {
          result[stat.status] = stat.count;
        });

        return result;
      } catch (error) {
        console.error('获取轮次统计失败:', error);
        throw error;
      }
    })();
  }

  /**
   * 清理过期数据
   * @param {Object} model 数据模型
   * @param {number} daysToKeep 保留天数
   */
  cleanupExpiredData(model, daysToKeep = 30) {
    return _asyncToGenerator(function* () {
      try {
        const roundModel = model('flash_sale_rounds');
        const roundGoodsModel = model('flash_sale_round_goods');

        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
        const cutoffStr = cutoffDate.toISOString().slice(0, 19).replace('T', ' ');

        // 获取要删除的轮次
        const expiredRounds = yield roundModel.where({
          end_time: ['<', cutoffStr],
          status: 'ended'
        }).field('id, round_name').select();

        if (expiredRounds.length > 0) {
          const roundIds = expiredRounds.map(function (r) {
            return r.id;
          });

          // 删除轮次商品
          yield roundGoodsModel.where({
            round_id: ['IN', roundIds]
          }).delete();

          // 删除轮次
          yield roundModel.where({
            id: ['IN', roundIds]
          }).delete();

          console.log(`🗑️ 清理了 ${expiredRounds.length} 个过期轮次 (${daysToKeep}天前):`, expiredRounds.map(function (r) {
            return r.round_name;
          }).join(', '));
        }

        return expiredRounds.length;
      } catch (error) {
        console.error('清理过期数据失败:', error);
        throw error;
      }
    })();
  }
};
//# sourceMappingURL=flash_sale_multi_scheduler.js.map