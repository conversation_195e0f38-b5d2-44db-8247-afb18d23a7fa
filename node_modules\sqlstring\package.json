{"_from": "sqlstring@^2.3.2", "_id": "sqlstring@2.3.3", "_inBundle": false, "_integrity": "sha512-qC9iz2FlN7DQl3+wjwn3802RTyjCx7sDvfQEXchwa6CWOx07/WVfh91gBmQ9fahw8snwGEWU3xGzOt4tFyHLxg==", "_location": "/sqlstring", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "sqlstring@^2.3.2", "name": "sqlstring", "escapedName": "sqlstring", "rawSpec": "^2.3.2", "saveSpec": null, "fetchSpec": "^2.3.2"}, "_requiredBy": ["/mysql2"], "_resolved": "https://registry.npmjs.org/sqlstring/-/sqlstring-2.3.3.tgz", "_shasum": "2ddc21f03bce2c387ed60680e739922c65751d0c", "_spec": "sqlstring@^2.3.2", "_where": "D:\\py-ide\\hioshop-miniprogram-master\\node_modules\\mysql2", "bugs": {"url": "https://github.com/mysqljs/sqlstring/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "fengmk2", "email": "<EMAIL>", "url": "http://fengmk2.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "Simple SQL escape and format for MySQL", "devDependencies": {"beautify-benchmark": "0.2.4", "benchmark": "2.1.4", "eslint": "7.32.0", "eslint-plugin-markdown": "2.2.1", "nyc": "15.1.0", "urun": "0.0.8", "utest": "0.0.8"}, "engines": {"node": ">= 0.6"}, "files": ["lib/", "HISTORY.md", "LICENSE", "README.md", "index.js"], "homepage": "https://github.com/mysqljs/sqlstring#readme", "keywords": ["sqlstring", "sql", "escape", "sql escape"], "license": "MIT", "name": "sqlstring", "repository": {"type": "git", "url": "git+https://github.com/mysqljs/sqlstring.git"}, "scripts": {"bench": "node benchmark/index.js", "lint": "eslint .", "test": "node test/run.js", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "2.3.3"}