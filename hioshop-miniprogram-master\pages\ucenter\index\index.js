var util = require('../../../utils/util.js');
var api = require('../../../config/api.js');
var user = require('../../../services/user.js');
var auth = require('../../../utils/auth.js');

// TODO 订单显示数量在图标上

const app = getApp()

Page({
  data: {
    userInfo: {},
    hasUserInfo: false,
    status: {},
    root: api.ApiRoot,
    is_new: 0,
    // 新增数据字段
    commissionInfo: {
      availableCommission: '0.00',
      frozenCommission: '0.00',
      totalCommission: '0.00'
    },
    userPoints: 0,
    couponCount: 0,
    orderCount: 0
  },
  goProfile: function (e) {
    // 检查是否已完整授权
    if (!auth.isFullyAuthorized()) {
      wx.showModal({
        title: '需要登录',
        content: '访问个人设置需要先完善个人信息，是否立即设置？',
        confirmText: '立即设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/app-auth/index?from=ucenter'
            });
          }
        }
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/ucenter/settings/index',
    });
  },
  toOrderListTap: function (event) {
    // 检查是否已完整授权
    if (!auth.isFullyAuthorized()) {
      wx.showModal({
        title: '需要登录',
        content: '查看订单需要先完善个人信息，是否立即设置？',
        confirmText: '立即设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/app-auth/index?from=ucenter'
            });
          }
        }
      });
      return;
    }

    let showType = event.currentTarget.dataset.index;
    wx.setStorageSync('showType', showType);
    wx.navigateTo({
      url: '/pages/ucenter/order-list/index?showType=' + showType,
    });
  },
  toAddressList: function (e) {
    // 检查是否已完整授权
    if (!auth.isFullyAuthorized()) {
      wx.showModal({
        title: '需要登录',
        content: '管理收货地址需要先完善个人信息，是否立即设置？',
        confirmText: '立即设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/app-auth/index?from=ucenter'
            });
          }
        }
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/ucenter/address/index?type=0',
    });
  },
  toAbout: function () {
    wx.navigateTo({
      url: '/pages/ucenter/about/index',
    });
  },
  toFootprint: function (e) {
    // 检查是否已完整授权
    if (!auth.isFullyAuthorized()) {
      wx.showModal({
        title: '需要登录',
        content: '查看浏览足迹需要先完善个人信息，是否立即设置？',
        confirmText: '立即设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/app-auth/index?from=ucenter'
            });
          }
        }
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/ucenter/footprint/index',
    });
  },
  goAuth: function (e) {
    // 检查是否已登录
    let userInfo = wx.getStorageSync('userInfo');
    if (userInfo != '') {
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      });
      return;
    }

    // 未登录时，跳转到授权页面
    wx.navigateTo({
      url: '/pages/app-auth/index',
    });
  },
  postLogin(code) {
    let that = this;
    util.request(api.AuthLoginByWeixin, {
      code: code
    }, 'POST').then(function (res) {
      if (res.errno === 0) {
        let userInfo = res.data.userInfo;
        that.setData({
          is_new: res.data.is_new,
          userInfo: userInfo,
          hasUserInfo: true
        })
        wx.setStorageSync('token', res.data.token);
        wx.setStorageSync('userInfo', userInfo);
        app.globalData.token = res.data.token;
      }
    });
  },
  onLoad: function (options) {
    // 检查页面访问权限
    if (!auth.isFullyAuthorized()) {
      // 显示基础内容，然后提示用户是否要完善信息
      this.showBasicContent();

      // 延迟显示授权提示，让用户先看到页面内容
      setTimeout(() => {
        this.startQuickAuth();
      }, 1000);
      return;
    }

    // 权限检查通过，执行正常的页面初始化逻辑
    let userInfo = wx.getStorageSync('userInfo');
    if (userInfo != '') {
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      });
    }
  },
  onShow: function () {
    // 检查页面访问权限
    if (!auth.isFullyAuthorized()) {
      // 显示基础内容，不强制弹出授权提示
      this.showBasicContent();
      wx.removeStorageSync('categoryId');
      return;
    }

    // 权限检查通过，执行正常的页面显示逻辑
    this.getOrderInfo();
    this.getSettingsDetail();
    this.getCommissionInfo();
    this.getUserStats();
    wx.removeStorageSync('categoryId');
  },
  getSettingsDetail() {
    let that = this;
    util.request(api.SettingsDetail).then(function (res) {
      if (res.errno === 0) {
        let userInfo = res.data;
        // wx.setStorageSync('userInfo', userInfo);
        that.setData({
          userInfo: userInfo,
          hasUserInfo: true
        });
      }
    });
  },
  onPullDownRefresh: function () {
    wx.showNavigationBarLoading()
    this.getOrderInfo();
    wx.hideNavigationBarLoading() //完成停止加载
    wx.stopPullDownRefresh() //停止下拉刷新
  },
  getOrderInfo: function (e) {
    let that = this;
    util.request(api.OrderCountInfo).then(function (res) {
      if (res.errno === 0) {
        let status = res.data;
        that.setData({
          status: status
        });
      }
    });
  },

  // 获取佣金信息
  getCommissionInfo: function() {
    let that = this;
    util.request(api.CommissionInfo).then(function (res) {
      if (res.errno === 0) {
        that.setData({
          commissionInfo: res.data.commissionInfo || {
            availableCommission: '0.00',
            frozenCommission: '0.00',
            totalCommission: '0.00'
          }
        });
      }
    }).catch(function(err) {
      console.log('获取佣金信息失败:', err);
    });
  },

  // 获取用户统计数据
  getUserStats: function() {
    let that = this;

    // 获取积分信息
    util.request(api.UserPoints).then(function (res) {
      if (res.errno === 0) {
        that.setData({
          userPoints: res.data.points || 0
        });
      }
    }).catch(function(err) {
      console.log('获取积分信息失败:', err);
    });

    // 获取优惠券数量
    util.request(api.CouponMy).then(function (res) {
      if (res.errno === 0) {
        // 计算所有状态的优惠券总数
        let totalCount = 0;
        if (res.data) {
          totalCount = (res.data.unused ? res.data.unused.length : 0) +
                      (res.data.used ? res.data.used.length : 0) +
                      (res.data.expired ? res.data.expired.length : 0);
        }
        that.setData({
          couponCount: totalCount
        });
      }
    }).catch(function(err) {
      console.log('获取优惠券信息失败:', err);
    });

    // 获取订单数量（作为数据统计）
    util.request(api.OrderCountInfo).then(function (res) {
      if (res.errno === 0) {
        let totalOrders = 0;
        if (res.data) {
          totalOrders = (res.data.toPay || 0) + (res.data.toDelivery || 0) +
                       (res.data.toReceive || 0) + (res.data.toComment || 0);
        }
        that.setData({
          orderCount: totalOrders
        });
      }
    }).catch(function(err) {
      console.log('获取订单统计失败:', err);
    });
  },

  // 一键快速授权
  startQuickAuth: function() {
    console.log('=== 开始一键授权 ===');

    const that = this;

    // 显示授权提示
    wx.showModal({
      title: '完善个人信息',
      content: '为了更好的使用体验，请设置您的头像和昵称',
      confirmText: '立即设置',
      cancelText: '暂不设置',
      showCancel: true,
      success: (modalRes) => {
        if (modalRes.confirm) {
          // 用户同意，显示加载提示并跳转
          wx.showLoading({
            title: '正在跳转...'
          });

          setTimeout(() => {
            wx.hideLoading();
            wx.navigateTo({
              url: '/pages/app-auth/index?from=ucenter'
            });
          }, 500);
        } else if (modalRes.cancel) {
          // 用户取消，显示基础页面内容
          console.log('用户选择暂不设置个人信息');
          that.showBasicContent();
        }
      }
    });
  },

  // 显示基础页面内容（用户选择不授权时）
  showBasicContent: function() {
    console.log('显示基础页面内容');

    // 设置默认的用户信息显示
    this.setData({
      userInfo: {
        nickname: '点击登录',
        avatar: '/images/icon/default_avatar_big.jpg'
      },
      hasUserInfo: false
    });

    // 显示提示信息
    wx.showToast({
      title: '您可以随时在此页面完善信息',
      icon: 'none',
      duration: 2000
    });

    // 可以加载一些不需要登录的基础数据
    // 比如公共的商品信息、公告等
  },

  // 提交快速授权
  // 签到功能
  signIn: function() {
    console.log('点击签到');

    // 检查是否已完整授权
    if (!auth.isFullyAuthorized()) {
      wx.showModal({
        title: '需要登录',
        content: '使用签到功能需要先完善个人信息，是否立即设置？',
        confirmText: '立即设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/app-auth/index?from=ucenter'
            });
          }
        }
      });
      return;
    }

    // 已授权，跳转到签到页面
    wx.navigateTo({
      url: '/pages/sign-in/index'
    });
  },

  // 显示会员码
  showMemberCode: function() {
    // 检查是否已完整授权
    if (!auth.isFullyAuthorized()) {
      wx.showModal({
        title: '需要登录',
        content: '查看会员码需要先完善个人信息，是否立即设置？',
        confirmText: '立即设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/app-auth/index?from=ucenter'
            });
          }
        }
      });
      return;
    }

    // 跳转到会员码页面
    wx.navigateTo({
      url: '/pages/member-code/index'
    });
  },



  // 跳转到订单有礼页面
  toOrderExchange: function() {
    wx.navigateTo({
      url: '/pages/order-exchange/index'
    });
  },

  // 跳转到我的优惠券
  toMyCoupons: function() {
    // 检查是否已完整授权
    if (!auth.isFullyAuthorized()) {
      wx.showModal({
        title: '需要登录',
        content: '查看优惠券需要先完善个人信息，是否立即设置？',
        confirmText: '立即设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/app-auth/index?from=ucenter'
            });
          }
        }
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/ucenter/coupons/index'
    });
  },

  // 跳转到佣金页面
  toCommission: function() {
    // 检查是否已完整授权
    if (!auth.isFullyAuthorized()) {
      wx.showModal({
        title: '需要登录',
        content: '查看佣金需要先完善个人信息，是否立即设置？',
        confirmText: '立即设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/app-auth/index?from=ucenter'
            });
          }
        }
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/ucenter/commission/index'
    });
  },

  // 跳转到积分页面
  toPoints: function() {
    // 检查是否已完整授权
    if (!auth.isFullyAuthorized()) {
      wx.showModal({
        title: '需要登录',
        content: '查看积分需要先完善个人信息，是否立即设置？',
        confirmText: '立即设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/app-auth/index?from=ucenter'
            });
          }
        }
      });
      return;
    }

    // 跳转到积分页面（如果有的话）
    wx.showToast({
      title: '积分功能开发中',
      icon: 'none'
    });
  },

  // 跳转到数据中心
  toDataCenter: function() {
    // 检查是否已完整授权
    if (!auth.isFullyAuthorized()) {
      wx.showModal({
        title: '需要登录',
        content: '查看数据需要先完善个人信息，是否立即设置？',
        confirmText: '立即设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/app-auth/index?from=ucenter'
            });
          }
        }
      });
      return;
    }

    // 跳转到数据中心页面
    wx.navigateTo({
      url: '/pages/ucenter/data-center/index'
    });
  },

  submitQuickAuth: function(loginParams) {
    console.log('提交快速授权:', loginParams);

    const that = this;

    // 显示加载
    wx.showLoading({
      title: '正在登录...'
    });

    // 发送登录请求
    util.request(api.AuthLoginByWeixin, loginParams, 'POST').then(function(res) {
      wx.hideLoading();

      if (res.errno === 0) {
        console.log('登录成功:', res.data);

        // 保存用户信息
        const userInfo = res.data.userInfo;
        wx.setStorageSync('userInfo', userInfo);
        wx.setStorageSync('token', res.data.token);
        wx.setStorageSync('openid', res.data.openid);
        app.globalData.userInfo = userInfo;
        app.globalData.token = res.data.token;
        app.globalData.openid = res.data.openid;

        // 更新页面数据
        that.setData({
          userInfo: userInfo,
          hasUserInfo: true
        });

        // 显示成功提示
        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500
        });

        // 继续加载页面数据
        setTimeout(() => {
          that.getOrderInfo();
          that.getSettingsDetail();
        }, 500);

      } else {
        console.log('登录失败:', res.errmsg);
        wx.showToast({
          title: res.errmsg || '登录失败',
          icon: 'none'
        });
      }
    }).catch(function(err) {
      wx.hideLoading();
      console.log('登录请求失败:', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // 跳转到推广员介绍页面
  registerMember: function() {
    wx.navigateTo({
      url: '/pages/ucenter/promoter-guide/index'
    });
  },

  // 跳转到地址管理
  toAddressList: function() {
    // 检查是否已完整授权
    if (!auth.isFullyAuthorized()) {
      wx.showModal({
        title: '需要登录',
        content: '查看地址需要先完善个人信息，是否立即设置？',
        confirmText: '立即设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/app-auth/index?from=ucenter'
            });
          }
        }
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/ucenter/address/index'
    });
  },

  // 跳转到我的足迹
  toFootprint: function() {
    // 检查是否已完整授权
    if (!auth.isFullyAuthorized()) {
      wx.showModal({
        title: '需要登录',
        content: '查看足迹需要先完善个人信息，是否立即设置？',
        confirmText: '立即设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/app-auth/index?from=ucenter'
            });
          }
        }
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/ucenter/footprint/index'
    });
  }
})