/**
 * 支付相关服务
 */
const util = require('../utils/util.js');
const api = require('../config/api.js');

// 判断是否为IP环境（本地开发）
function isIPEnvironment(url) {
  const ipPattern = /^https?:\/\/(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|localhost|127\.0\.0\.1)/;
  return ipPattern.test(url);
}
function payOrder(orderId) {
    return new Promise(function(resolve, reject) {
        util.request(api.PayPrepayId, {
            orderId: orderId
        }).then((res) => {
            if (res.errno === 0) {
                const payParam = res.data;

                // 根据环境自动选择支付方式
                const isTestEnv = isIPEnvironment(api.ApiRoot);

                if (isTestEnv) {
                    // IP环境：直接返回成功（测试支付接口已处理状态更新）
                    console.log('本地开发环境，跳过真实支付');
                    resolve(res);
                } else {
                    // 域名环境：调用真实微信支付
                    console.log('生产环境，调用真实微信支付');
                    wx.requestPayment({
                        'timeStamp': payParam.timeStamp,
                        'nonceStr': payParam.nonceStr,
                        'package': payParam.package,
                        'signType': payParam.signType,
                        'paySign': payParam.paySign,
                        'success': function(res) {
                            resolve(res);
                        },
                        'fail': function(res) {
                            reject(res);
                        },
                        'complete': function(res) {
                            reject(res);
                        }
                    });
                }
            } else {
                reject(res);
            }
        });
    });
}
module.exports = {
    payOrder
};