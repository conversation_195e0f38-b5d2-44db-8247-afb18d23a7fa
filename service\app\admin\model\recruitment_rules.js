function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

module.exports = class extends think.Model {
    /**
     * 获取招募规则
     * @returns {Promise.<*>}
     */
    getRecruitmentRules() {
        var _this = this;

        return _asyncToGenerator(function* () {
            const rules = yield _this.find();

            if (rules) {
                // 解析JSON字段
                if (rules.conditions) {
                    try {
                        rules.conditions = JSON.parse(rules.conditions);
                    } catch (e) {
                        rules.conditions = {};
                    }
                }

                // 转换字段名
                return {
                    joinCondition: rules.join_condition,
                    conditions: rules.conditions || {},
                    applicationMethod: rules.application_method,
                    requireApplicationInfo: rules.require_application_info === 1,
                    auditMethod: rules.audit_method,
                    addTime: rules.add_time,
                    updateTime: rules.update_time
                };
            }

            return null;
        })();
    }

    /**
     * 保存招募规则
     * @param rulesData
     * @returns {Promise.<*>}
     */
    saveRecruitmentRules(rulesData) {
        var _this2 = this;

        return _asyncToGenerator(function* () {
            const currentTime = parseInt(new Date().getTime() / 1000);

            const saveData = {
                join_condition: rulesData.joinCondition,
                conditions: JSON.stringify(rulesData.conditions || {}),
                application_method: rulesData.applicationMethod,
                require_application_info: rulesData.requireApplicationInfo ? 1 : 0,
                audit_method: rulesData.auditMethod,
                update_time: currentTime
            };

            // 检查是否已存在规则
            const existingRules = yield _this2.find();

            if (existingRules && existingRules.id) {
                // 更新现有规则
                return yield _this2.where({ id: existingRules.id }).update(saveData);
            } else {
                // 创建新规则
                saveData.add_time = currentTime;
                return yield _this2.add(saveData);
            }
        })();
    }

    /**
     * 检查用户是否满足招募条件
     * @param userId
     * @returns {Promise.<*>}
     */
    checkUserConditions(userId) {
        var _this3 = this;

        return _asyncToGenerator(function* () {
            const rules = yield _this3.getRecruitmentRules();

            if (!rules || rules.joinCondition === 'no_conditions') {
                return {
                    canJoin: true,
                    message: '无条件加入'
                };
            }

            const conditions = rules.conditions || {};
            const checkResults = {
                canJoin: true,
                conditions: [],
                failedConditions: []
            };

            // 检查自购金额条件
            if (conditions.requireAmount) {
                const userOrders = yield _this3.model('order').where({
                    user_id: userId,
                    order_status: ['>=', 300] // 已支付订单
                }).sum('actual_price');

                const totalAmount = parseFloat(userOrders || 0);
                const requiredAmount = parseFloat(conditions.minAmount || 0);

                if (totalAmount >= requiredAmount) {
                    checkResults.conditions.push({
                        type: 'amount',
                        required: requiredAmount,
                        current: totalAmount,
                        passed: true
                    });
                } else {
                    checkResults.conditions.push({
                        type: 'amount',
                        required: requiredAmount,
                        current: totalAmount,
                        passed: false
                    });
                    checkResults.failedConditions.push('自购金额不足');
                    checkResults.canJoin = false;
                }
            }

            // 检查消费笔数条件
            if (conditions.requireOrders) {
                const orderCount = yield _this3.model('order').where({
                    user_id: userId,
                    order_status: ['>=', 300] // 已支付订单
                }).count();

                const requiredOrders = parseInt(conditions.minOrders || 0);

                if (orderCount >= requiredOrders) {
                    checkResults.conditions.push({
                        type: 'orders',
                        required: requiredOrders,
                        current: orderCount,
                        passed: true
                    });
                } else {
                    checkResults.conditions.push({
                        type: 'orders',
                        required: requiredOrders,
                        current: orderCount,
                        passed: false
                    });
                    checkResults.failedConditions.push('消费笔数不足');
                    checkResults.canJoin = false;
                }
            }

            // 检查指定商品购买条件
            if (conditions.requirePurchase) {
                // 这里可以添加指定商品购买检查逻辑
                // 暂时设为通过
                checkResults.conditions.push({
                    type: 'purchase',
                    required: '指定商品',
                    current: '已购买',
                    passed: true
                });
            }

            return checkResults;
        })();
    }

    /**
     * 获取默认招募规则
     * @returns {Object}
     */
    getDefaultRules() {
        return {
            joinCondition: 'with_conditions',
            conditions: {
                requirePurchase: false,
                requireAmount: true,
                minAmount: 99.00,
                requireOrders: false,
                minOrders: 1
            },
            applicationMethod: 'manual_apply',
            requireApplicationInfo: false,
            auditMethod: 'auto_audit'
        };
    }

    /**
     * 验证招募规则数据
     * @param rulesData
     * @returns {Object}
     */
    validateRules(rulesData) {
        const errors = [];

        if (!rulesData.joinCondition) {
            errors.push('请选择加入条件类型');
        }

        if (!rulesData.applicationMethod) {
            errors.push('请选择申请方式');
        }

        if (!rulesData.auditMethod) {
            errors.push('请选择审核方式');
        }

        // 如果是有条件加入，验证条件设置
        if (rulesData.joinCondition === 'with_conditions') {
            const conditions = rulesData.conditions || {};
            const hasCondition = conditions.requirePurchase || conditions.requireAmount || conditions.requireOrders;

            if (!hasCondition) {
                errors.push('请至少设置一个加入条件');
            }

            if (conditions.requireAmount) {
                if (!conditions.minAmount || conditions.minAmount < 0.01) {
                    errors.push('自购金额最低设置为0.01元');
                }
            }

            if (conditions.requireOrders) {
                if (!conditions.minOrders || conditions.minOrders < 1) {
                    errors.push('消费笔数最低设置为1笔');
                }
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
};