module.exports = class extends think.Model {

  /**
   * 获取轮次详细信息
   */
  async getRoundInfo(roundId) {
    const round = await this.where({ id: roundId }).find();
    if (think.isEmpty(round)) {
      return null;
    }
    
    // 获取活动信息
    const campaignModel = this.model('flash_sale_campaigns');
    const campaign = await campaignModel.where({ id: round.campaign_id }).find();
    
    // 获取商品信息
    const goodsModel = this.model('goods');
    const goods = await goodsModel.where({ id: campaign.goods_id }).find();
    
    return {
      ...round,
      campaign: campaign,
      goods: goods
    };
  }
  
  /**
   * 检查轮次是否可以购买
   */
  async canPurchase(roundId, quantity = 1) {
    const round = await this.where({ id: roundId }).find();
    if (think.isEmpty(round)) {
      return { canPurchase: false, reason: '轮次不存在' };
    }
    
    if (round.status !== 'active') {
      return { canPurchase: false, reason: '轮次未开始或已结束' };
    }
    
    const remainingStock = round.stock - round.sold_count;
    if (remainingStock < quantity) {
      return { canPurchase: false, reason: '库存不足' };
    }
    
    return { canPurchase: true, remainingStock };
  }
  
  /**
   * 原子性减库存操作
   */
  async decreaseStock(roundId, quantity) {
    try {
      // 使用数据库级别的原子操作
      const result = await this.query(`
        UPDATE ${this.tablePrefix}flash_sale_rounds 
        SET sold_count = sold_count + ${quantity}
        WHERE id = ${roundId} 
        AND (stock - sold_count) >= ${quantity}
        AND status = 'active'
      `);
      
      if (result.affectedRows === 0) {
        throw new Error('库存不足或轮次状态异常');
      }
      
      return true;
    } catch (error) {
      console.error('减库存失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取当前活跃的轮次
   */
  async getActiveRounds() {
    const now = new Date();
    const nowStr = now.toISOString().slice(0, 19).replace('T', ' ');
    
    return await this.where({
      start_time: ['<=', nowStr],
      end_time: ['>', nowStr],
      status: 'active'
    }).select();
  }
  
  /**
   * 获取即将开始的轮次
   */
  async getUpcomingRounds(minutes = 30) {
    const now = new Date();
    const future = new Date(now.getTime() + minutes * 60000);
    
    const nowStr = now.toISOString().slice(0, 19).replace('T', ' ');
    const futureStr = future.toISOString().slice(0, 19).replace('T', ' ');
    
    return await this.where({
      start_time: ['BETWEEN', [nowStr, futureStr]],
      status: 'upcoming'
    }).order('start_time ASC').select();
  }
  
  /**
   * 更新轮次状态
   */
  async updateStatus(roundId, status) {
    return await this.where({ id: roundId }).update({ status });
  }
  
  /**
   * 批量更新过期轮次状态
   */
  async endExpiredRounds() {
    const now = new Date();
    const nowStr = now.toISOString().slice(0, 19).replace('T', ' ');
    
    return await this.where({
      end_time: ['<', nowStr],
      status: ['IN', ['upcoming', 'active']]
    }).update({ status: 'ended' });
  }
};
