{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\base.js"], "names": ["module", "exports", "think", "Controller", "__before", "token", "ctx", "header", "console", "log", "tokenSerivce", "service", "userId", "getUserId", "controller", "undefined", "fail"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,UAApB,CAA+B;AACxCC,UAAN,GAAiB;AAAA;;AAAA;AACf;AACAF,YAAMG,KAAN,GAAc,MAAKC,GAAL,CAASC,MAAT,CAAgB,iBAAhB,KAAsC,MAAKD,GAAL,CAASC,MAAT,CAAgB,iBAAhB,CAAtC,IAA4E,EAA1F;AACAC,cAAQC,GAAR,CAAY,mBAAZ;AACAD,cAAQC,GAAR,CAAY,QAAZ,EAAsBP,MAAMG,KAA5B;;AAEA,YAAMK,eAAeR,MAAMS,OAAN,CAAc,OAAd,EAAuB,OAAvB,CAArB;AACAT,YAAMU,MAAN,GAAe,MAAMF,aAAaG,SAAb,EAArB;;AAEAL,cAAQC,GAAR,CAAY,OAAZ,EAAqBP,MAAMU,MAA3B;;AAEA;AACA,UAAI,MAAKN,GAAL,CAASQ,UAAT,IAAuB,MAA3B,EAAmC;AACjC,YAAIZ,MAAMU,MAAN,IAAgB,CAAhB,IAAqBV,MAAMU,MAAN,IAAgBG,SAAzC,EAAoD;AAClDP,kBAAQC,GAAR,CAAY,iBAAZ;AACA,iBAAO,MAAKO,IAAL,CAAU,GAAV,EAAe,MAAf,CAAP;AACD;AACF;AACDR,cAAQC,GAAR,CAAY,WAAZ;AAlBe;AAmBhB;AApB6C,CAAhD", "file": "..\\..\\..\\src\\admin\\controller\\base.js", "sourcesContent": ["module.exports = class extends think.Controller {\n  async __before() {\n    // 根据token值获取用户id\n    think.token = this.ctx.header['x-hioshop-token'] || this.ctx.header['X-Hioshop-Token'] || '';\n    console.log('=== Token验证开始 ===');\n    console.log('Token:', think.token);\n\n    const tokenSerivce = think.service('token', 'admin');\n    think.userId = await tokenSerivce.getUserId();\n\n    console.log('用户ID:', think.userId);\n\n    // 只允许登录操作\n    if (this.ctx.controller != 'auth') {\n      if (think.userId <= 0 || think.userId == undefined) {\n        console.log('Token验证失败，用户未登录');\n        return this.fail(401, '请先登录');\n      }\n    }\n    console.log('Token验证通过');\n  }\n};"]}