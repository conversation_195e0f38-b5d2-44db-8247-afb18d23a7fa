{"version": 3, "sources": ["..\\..\\..\\src\\api\\service\\qiniu.js"], "names": ["qiniu", "require", "module", "exports", "think", "Service", "getQiniuToken", "accessKey", "config", "secret<PERSON>ey", "bucket", "domain", "mac", "auth", "digest", "<PERSON>", "currentTime", "parseInt", "Date", "getTime", "key", "uuid", "options", "scope", "deadline", "save<PERSON><PERSON>", "putPolicy", "rs", "PutPolicy", "uploadToken", "data"], "mappings": ";;AAAA,MAAMA,QAAQC,QAAQ,OAAR,CAAd;AACAC,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,OAApB,CAA4B;AACnCC,iBAAN,GAAsB;AAAA;AAClB,gBAAIC,YAAYH,MAAMI,MAAN,CAAa,kBAAb,CAAhB;AACA,gBAAIC,YAAYL,MAAMI,MAAN,CAAa,kBAAb,CAAhB;AACA,gBAAIE,SAAUN,MAAMI,MAAN,CAAa,cAAb,CAAd;AACA,gBAAIG,SAAUP,MAAMI,MAAN,CAAa,cAAb,CAAd;AACA,gBAAII,MAAM,IAAIZ,MAAMa,IAAN,CAAWC,MAAX,CAAkBC,GAAtB,CAA0BR,SAA1B,EAAqCE,SAArC,CAAV;AACA,gBAAIO,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,IAAwC,GAA1D;AACA,gBAAIC,MAAMhB,MAAMiB,IAAN,CAAW,EAAX,CAAV;AACA,gBAAIC,UAAU;AACVC,uBAAMb,MADI;AAEVc,0BAASR,WAFC;AAGVS,yBAAQL;AAHE,aAAd;AAKA,gBAAIM,YAAY,IAAI1B,MAAM2B,EAAN,CAASC,SAAb,CAAuBN,OAAvB,CAAhB;AACA,gBAAIO,cAAYH,UAAUG,WAAV,CAAsBjB,GAAtB,CAAhB;AACA,gBAAIkB,OAAO;AACPD,6BAAYA,WADL;AAEPlB,wBAAOA;AAFA,aAAX;AAIA,mBAAOmB,IAAP;AAnBkB;AAoBrB;AArBwC,CAA7C", "file": "..\\..\\..\\src\\api\\service\\qiniu.js", "sourcesContent": ["const qiniu = require('qiniu');\nmodule.exports = class extends think.Service {\n    async getQiniuToken() {\n        let accessKey = think.config('qiniu.access_key');\n        let secretKey = think.config('qiniu.secret_key');\n        let bucket =  think.config('qiniu.bucket');\n        let domain =  think.config('qiniu.domain');\n        let mac = new qiniu.auth.digest.Mac(accessKey, secretKey);\n        let currentTime = parseInt(new Date().getTime() / 1000) + 600;\n        let key = think.uuid(32);\n        let options = {\n            scope:bucket,\n            deadline:currentTime,\n            saveKey:key\n        };\n        let putPolicy = new qiniu.rs.PutPolicy(options);\n        let uploadToken=putPolicy.uploadToken(mac);\n        let data = {\n            uploadToken:uploadToken,\n            domain:domain\n        };\n        return data;\n    }\n};\n"]}