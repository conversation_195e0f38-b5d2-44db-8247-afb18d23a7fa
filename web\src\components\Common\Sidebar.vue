<template>
  <div
    id="sidebar"
    :class="['sidebar', 'bg-white', 'border-r', 'h-full', 'fixed', 'left-0', 'top-0', 'overflow-y-auto', 'z-10', 'shadow-sm', { 'collapsed': isCollapsed }]"
  >
    <div class="p-4 flex items-center justify-between border-b">
      <div class="font-['Pacifico'] text-xl text-gray-800">logo</div>
      <button
        @click="toggleSidebar"
        class="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
      >
        <i :class="isCollapsed ? 'ri-menu-unfold-line ri-lg' : 'ri-menu-fold-line ri-lg'"></i>
      </button>
    </div>
    <div class="mt-2">
      <ul class="space-y-1">
        <li class="mx-2">
          <router-link
            to="/dashboard/welcome"
            class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group"
            :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/welcome' }"
          >
            <div
              class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-primary"
              :class="{ 'text-primary': $route.path === '/dashboard/welcome' }"
            >
              <i class="ri-home-line"></i>
            </div>
            <span class="ml-3 menu-text text-sm" :class="{ 'font-medium': $route.path === '/dashboard/welcome' }">首页</span>
          </router-link>
        </li>
        <li class="mx-2">
          <router-link
            to="/dashboard/data-overview"
            class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group"
            :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/data-overview' }"
          >
            <div
              class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-primary"
              :class="{ 'text-primary': $route.path === '/dashboard/data-overview' }"
            >
              <i class="ri-bar-chart-line"></i>
            </div>
            <span class="ml-3 menu-text text-sm" :class="{ 'font-medium': $route.path === '/dashboard/data-overview' }">数据概览</span>
          </router-link>
        </li>
        <li class="mx-2">
          <router-link
            to="/dashboard/order"
            class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group"
            :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/order' }"
          >
            <div
              class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-primary"
              :class="{ 'text-primary': $route.path === '/dashboard/order' }"
            >
              <i class="ri-file-list-line"></i>
            </div>
            <span class="ml-3 menu-text text-sm" :class="{ 'font-medium': $route.path === '/dashboard/order' }">订单列表</span>
          </router-link>
        </li>
        <li class="mx-2">
          <div
            @click="toggleSubmenu('goods')"
            class="px-3 py-2 hover:bg-gray-100 rounded-lg cursor-pointer submenu-toggle group"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div
                  class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600"
                >
                  <i class="ri-shopping-bag-line"></i>
                </div>
                <span class="ml-3 menu-text text-sm text-gray-600">商品管理</span>
              </div>
              <i
                :class="['ri-arrow-down-s-line', 'submenu-arrow', 'text-gray-400', { 'transform rotate-180': openSubmenus.goods }]"
              ></i>
            </div>
          </div>
          <ul :class="['submenu', 'pl-9', 'mt-1', { 'hidden': !openSubmenus.goods }]">
            <li>
              <router-link
                to="/dashboard/goods"
                class="block px-3 py-2 text-sm text-gray-500 hover:text-primary hover:bg-gray-100 rounded-lg"
                :class="{ 'text-primary bg-gray-100': $route.path === '/dashboard/goods' }"
              >商品列表</router-link>
            </li>
            <li>
              <router-link
                to="/dashboard/nature"
                class="block px-3 py-2 text-sm text-gray-500 hover:text-primary hover:bg-gray-100 rounded-lg"
                :class="{ 'text-primary bg-gray-100': $route.path === '/dashboard/nature' }"
              >商品设置</router-link>
            </li>
          </ul>
        </li>
        <li class="mx-2">
          <router-link
            to="/dashboard/shopcart"
            class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group"
            :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/shopcart' }"
          >
            <div
              class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600"
              :class="{ 'text-primary': $route.path === '/dashboard/shopcart' }"
            >
              <i class="ri-shopping-cart-line"></i>
            </div>
            <span class="ml-3 menu-text text-sm" :class="{ 'font-medium': $route.path === '/dashboard/shopcart' }">购物车</span>
          </router-link>
        </li>
        <li class="mx-2">
          <router-link
            to="/dashboard/user"
            class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group"
            :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/user' }"
          >
            <div
              class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600"
              :class="{ 'text-primary': $route.path === '/dashboard/user' }"
            >
              <i class="ri-user-line"></i>
            </div>
            <span class="ml-3 menu-text text-sm" :class="{ 'font-medium': $route.path === '/dashboard/user' }">用户列表</span>
          </router-link>
        </li>

        <!-- 商品分销池 - 一级菜单 -->
        <li class="mx-2">
          <router-link
            to="/dashboard/promotion/product-pool"
            class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group"
            :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/promotion/product-pool' }"
          >
            <div
              class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600"
              :class="{ 'text-primary': $route.path === '/dashboard/promotion/product-pool' }"
            >
              <i class="ri-store-line"></i>
            </div>
            <span class="ml-3 menu-text text-sm" :class="{ 'font-medium': $route.path === '/dashboard/promotion/product-pool' }">商品分销池</span>
          </router-link>
        </li>

        <!-- 团队分销子菜单 -->
        <li class="mx-2">
          <div
            @click="togglePromotionMenu"
            class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group cursor-pointer"
            :class="{ 'bg-primary/5 text-primary': isPromotionMenuOpen || ($route.path.includes('/dashboard/promotion') && $route.path !== '/dashboard/promotion/product-pool') }"
          >
            <div
              class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600"
              :class="{ 'text-primary': isPromotionMenuOpen || ($route.path.includes('/dashboard/promotion') && $route.path !== '/dashboard/promotion/product-pool') }"
            >
              <i class="ri-share-line"></i>
            </div>
            <span class="ml-3 menu-text text-sm flex-1" :class="{ 'font-medium': isPromotionMenuOpen || ($route.path.includes('/dashboard/promotion') && $route.path !== '/dashboard/promotion/product-pool') }">团队分销</span>
            <div class="menu-text">
              <i class="ri-arrow-down-s-line transition-transform duration-200" :class="{ 'rotate-180': isPromotionMenuOpen }"></i>
            </div>
          </div>

          <!-- 团队分销子菜单项 -->
          <div v-show="isPromotionMenuOpen" class="ml-6 mt-1 space-y-1">
            <router-link
              to="/dashboard/promotion/member-management"
              class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm"
              :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/promotion/member-management' }"
            >
              <span class="menu-text">分销员管理</span>
            </router-link>

            <router-link
              to="/dashboard/promotion/data-overview"
              class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm"
              :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/promotion/data-overview' }"
            >
              <span class="menu-text">数据概览</span>
            </router-link>

            <router-link
              to="/dashboard/promotion/member-list"
              class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm"
              :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/promotion/member-list' }"
            >
              <span class="menu-text">分销员列表</span>
            </router-link>

            <router-link
              to="/dashboard/promotion/mode-settings"
              class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm"
              :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/promotion/mode-settings' }"
            >
              <span class="menu-text">分销模式</span>
            </router-link>

            <router-link
              to="/dashboard/promotion/review-status"
              class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm"
              :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/promotion/review-status' }"
            >
              <span class="menu-text">审核状态</span>
            </router-link>

            <router-link
              to="/dashboard/promotion/commission-settings"
              class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm"
              :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/promotion/commission-settings' }"
            >
              <span class="menu-text">分销佣金</span>
            </router-link>

            <router-link
              to="/dashboard/promotion/recruitment-rules"
              class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm"
              :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/promotion/recruitment-rules' }"
            >
              <span class="menu-text">招募设置</span>
            </router-link>




          </div>
        </li>

        <!-- 个人推广子菜单 -->
        <li class="mx-2">
          <div
            @click="togglePersonalPromotionMenu"
            class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group cursor-pointer"
            :class="{ 'bg-primary/5 text-primary': isPersonalPromotionMenuOpen || $route.path.includes('/dashboard/personal-promotion') }"
          >
            <div
              class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600"
              :class="{ 'text-primary': isPersonalPromotionMenuOpen || $route.path.includes('/dashboard/personal-promotion') }"
            >
              <i class="ri-user-star-line"></i>
            </div>
            <span class="ml-3 menu-text text-sm flex-1" :class="{ 'font-medium': isPersonalPromotionMenuOpen || $route.path.includes('/dashboard/personal-promotion') }">个人推广</span>
            <div class="menu-text">
              <i class="ri-arrow-down-s-line transition-transform duration-200" :class="{ 'rotate-180': isPersonalPromotionMenuOpen }"></i>
            </div>
          </div>

          <!-- 个人推广子菜单项 -->
          <div v-show="isPersonalPromotionMenuOpen" class="ml-6 mt-1 space-y-1">
            <router-link
              to="/dashboard/personal-promotion/promoter-list"
              class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm"
              :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/personal-promotion/promoter-list' }"
            >
              <span class="menu-text">推广员列表</span>
            </router-link>

            <router-link
              to="/dashboard/personal-promotion/data-overview"
              class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm"
              :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/personal-promotion/data-overview' }"
            >
              <span class="menu-text">数据概览</span>
            </router-link>

            <router-link
              to="/dashboard/personal-promotion/commission"
              class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm"
              :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/personal-promotion/commission' }"
            >
              <span class="menu-text">推广佣金</span>
            </router-link>



            <router-link
              to="/dashboard/personal-promotion/share-records"
              class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm"
              :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/personal-promotion/share-records' }"
            >
              <span class="menu-text">分享记录</span>
            </router-link>
          </div>
        </li>

        <!-- 营销工具子菜单 -->
        <li class="mx-2">
          <div
            @click="toggleMarketingMenu"
            class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group cursor-pointer"
            :class="{ 'bg-primary/5 text-primary': isMarketingMenuOpen || $route.path.includes('/dashboard/marketing') }"
          >
            <div
              class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600"
              :class="{ 'text-primary': isMarketingMenuOpen || $route.path.includes('/dashboard/marketing') }"
            >
              <i class="ri-megaphone-line"></i>
            </div>
            <span class="ml-3 menu-text text-sm flex-1" :class="{ 'font-medium': isMarketingMenuOpen || $route.path.includes('/dashboard/marketing') }">营销工具</span>
            <div class="menu-text">
              <i class="ri-arrow-down-s-line transition-transform duration-200" :class="{ 'rotate-180': isMarketingMenuOpen }"></i>
            </div>
          </div>

          <!-- 营销工具子菜单项 -->
          <div v-show="isMarketingMenuOpen" class="ml-6 mt-1 space-y-1">
            <router-link
              to="/dashboard/marketing/order-gifts"
              class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm"
              :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/marketing/order-gifts' }"
            >
              <span class="menu-text">订单有礼</span>
            </router-link>

            <router-link
              to="/dashboard/marketing/coupons"
              class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm"
              :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/marketing/coupons' }"
            >
              <span class="menu-text">优惠券</span>
            </router-link>

            <router-link
              to="/dashboard/marketing/flash-sale"
              class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm"
              :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/marketing/flash-sale' }"
            >
              <span class="menu-text">限时秒杀</span>
            </router-link>

            <router-link
              to="/dashboard/marketing/group-buy"
              class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg text-sm"
              :class="{ 'bg-primary/5 text-primary': $route.path === '/dashboard/marketing/group-buy' }"
            >
              <span class="menu-text">团购活动</span>
            </router-link>
          </div>
        </li>
        <li class="mx-2">
          <div
            @click="toggleSubmenu('settings')"
            class="px-3 py-2 hover:bg-gray-100 rounded-lg cursor-pointer submenu-toggle group"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div
                  class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600"
                >
                  <i class="ri-store-line"></i>
                </div>
                <span class="ml-3 menu-text text-sm text-gray-600">店铺设置</span>
              </div>
              <i
                :class="['ri-arrow-down-s-line', 'submenu-arrow', 'text-gray-400', { 'transform rotate-180': openSubmenus.settings }]"
              ></i>
            </div>
          </div>
          <ul :class="['submenu', 'pl-9', 'mt-1', { 'hidden': !openSubmenus.settings }]">
            <li>
              <router-link
                to="/dashboard/settings/showset"
                class="block px-3 py-2 text-sm text-gray-500 hover:text-primary hover:bg-gray-100 rounded-lg"
                :class="{ 'text-primary bg-gray-100': $route.path === '/dashboard/settings/showset' }"
              >显示设置</router-link>
            </li>
            <li>
              <router-link
                to="/dashboard/ad"
                class="block px-3 py-2 text-sm text-gray-500 hover:text-primary hover:bg-gray-100 rounded-lg"
                :class="{ 'text-primary bg-gray-100': $route.path === '/dashboard/ad' }"
              >广告列表</router-link>
            </li>
            <li>
              <router-link
                to="/dashboard/freight"
                class="block px-3 py-2 text-sm text-gray-500 hover:text-primary hover:bg-gray-100 rounded-lg"
                :class="{ 'text-primary bg-gray-100': $route.path === '/dashboard/freight' }"
              >运费模板</router-link>
            </li>
            <li>
              <router-link
                to="/dashboard/shipper"
                class="block px-3 py-2 text-sm text-gray-500 hover:text-primary hover:bg-gray-100 rounded-lg"
                :class="{ 'text-primary bg-gray-100': $route.path === '/dashboard/shipper' }"
              >快递设置</router-link>
            </li>
            <li>
              <router-link
                to="/dashboard/admin"
                class="block px-3 py-2 text-sm text-gray-500 hover:text-primary hover:bg-gray-100 rounded-lg"
                :class="{ 'text-primary bg-gray-100': $route.path === '/dashboard/admin' }"
              >管理员</router-link>
            </li>
          </ul>
        </li>

        <li class="mx-2 mt-8">
          <a
            href="#"
            @click="logout"
            class="flex items-center px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg group"
          >
            <div
              class="w-6 h-6 flex items-center justify-center text-gray-400 group-hover:text-gray-600"
            >
              <i class="ri-logout-box-line"></i>
            </div>
            <span class="ml-3 menu-text text-sm">退出</span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentPagePath: "/dashboard",
      loginInfo: null,
      isCollapsed: false,
      openSubmenus: {
        goods: false,
        settings: false
      },
      isPromotionMenuOpen: false,
      isPersonalPromotionMenuOpen: false,
      isMarketingMenuOpen: false
    };
  },
  methods: {
    toggleSidebar() {
      this.isCollapsed = !this.isCollapsed;
      // 触发父组件更新布局
      this.$emit('sidebar-toggle', this.isCollapsed);
    },
    toggleSubmenu(submenuName) {
      if (!this.isCollapsed) {
        this.openSubmenus[submenuName] = !this.openSubmenus[submenuName];
      }
    },
    togglePromotionMenu() {
      if (!this.isCollapsed) {
        this.isPromotionMenuOpen = !this.isPromotionMenuOpen;
      }
    },
    togglePersonalPromotionMenu() {
      if (!this.isCollapsed) {
        this.isPersonalPromotionMenuOpen = !this.isPersonalPromotionMenuOpen;
      }
    },
    toggleMarketingMenu() {
      if (!this.isCollapsed) {
        this.isMarketingMenuOpen = !this.isMarketingMenuOpen;
      }
    },
    updateMenuState() {
      // 如果当前路由是团队分销相关页面（但不是商品分销池），自动展开团队分销菜单
      if (this.$route.path.includes('/dashboard/promotion/') && this.$route.path !== '/dashboard/promotion/product-pool') {
        this.isPromotionMenuOpen = true;
      }
      // 如果当前路由是个人推广相关页面，自动展开个人推广菜单
      if (this.$route.path.includes('/dashboard/personal-promotion/')) {
        this.isPersonalPromotionMenuOpen = true;
      }
      // 如果当前路由是营销工具相关页面，自动展开营销工具菜单
      if (this.$route.path.includes('/dashboard/marketing/')) {
        this.isMarketingMenuOpen = true;
      }
    },
    handleOpen(key, keyPath) {
      console.log(key, keyPath);
    },
    handleClose(key, keyPath) {
      console.log(key, keyPath);
    },
    logout() {
      // 直接退出，不显示确认弹窗
      localStorage.clear();
      this.$router.replace({ name: "login" });
    },
    checkLogin() {
      this.axios.get("index/checkLogin").then((response) => {
        console.log(response.data);
        if (response.data.errno === 401) {
          localStorage.clear();
          this.$router.replace({ name: "login" });
        }
      }).catch((error) => {
        // axios拦截器会自动处理401错误
        console.log('checkLogin error:', error);
      });
    },
  },
  watch: {
    isCollapsed(newVal) {
      if (newVal) {
        // 折叠时关闭所有子菜单
        this.openSubmenus.goods = false;
        this.openSubmenus.settings = false;
        this.isPromotionMenuOpen = false;
        this.isPersonalPromotionMenuOpen = false;
        this.isMarketingMenuOpen = false;
      }
    },
    '$route'() {
      this.updateMenuState();
    }
  },
  mounted() {
    console.log(this.$route.path);
    this.checkLogin();
    if (!this.loginInfo) {
      this.loginInfo = JSON.parse(
        window.localStorage.getItem("userInfo") || null
      );
    }

    // 根据当前路由自动展开对应的菜单
    this.updateMenuState();
  },
};
</script>
<style>
/* 配置Tailwind CSS主题色 */
:root {
  --primary: #4f46e5;
  --secondary: #6366f1;
}

.sidebar {
  width: 240px;
  min-width: 240px;
  transition: all 0.3s;
}

.sidebar.collapsed {
  width: 80px;
  min-width: 80px;
}

.sidebar.collapsed .menu-text {
  display: none;
}

.sidebar.collapsed .submenu-arrow {
  display: none;
}

.sidebar.collapsed .submenu {
  display: none !important;
}

/* Tailwind CSS 自定义类 */
.text-primary {
  color: var(--primary);
}

.bg-primary\/5 {
  background-color: rgba(79, 70, 229, 0.05);
}

.hover\:text-primary:hover {
  color: var(--primary);
}

.group-hover\:text-primary {
  color: #9ca3af;
}

.group:hover .group-hover\:text-primary {
  color: var(--primary);
}

/* Remix Icons 样式修复 */
:where([class^="ri-"])::before {
  content: "\f3c2";
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    transform: translateX(-100%);
  }

  .sidebar.show {
    transform: translateX(0);
  }
}
</style>
