function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

module.exports = class extends think.Model {

  /**
   * 获取表名
   */
  get tableName() {
    return 'hiolabs_flash_sales';
  }

  /**
   * 获取活动状态的秒杀商品
   */
  getActiveFlashSales() {
    var _this = this;

    return _asyncToGenerator(function* () {
      const now = new Date();
      return yield _this.where({
        status: 'active',
        start_time: ['<=', now],
        end_time: ['>=', now]
      }).select();
    })();
  }

  /**
   * 获取即将开始的秒杀商品
   */
  getUpcomingFlashSales() {
    var _this2 = this;

    return _asyncToGenerator(function* () {
      const now = new Date();
      return yield _this2.where({
        status: 'upcoming',
        start_time: ['>', now]
      }).order('start_time ASC').select();
    })();
  }

  /**
   * 获取已结束的秒杀商品
   */
  getEndedFlashSales() {
    var _this3 = this;

    return _asyncToGenerator(function* () {
      const now = new Date();
      return yield _this3.where({
        end_time: ['<', now]
      }).select();
    })();
  }

  /**
   * 更新秒杀状态
   */
  updateStatus() {
    var _this4 = this;

    return _asyncToGenerator(function* () {
      const now = new Date();

      // 将已过期的活动标记为已结束
      yield _this4.where({
        status: ['IN', ['upcoming', 'active']],
        end_time: ['<', now]
      }).update({
        status: 'ended'
      });

      // 将到时间的活动标记为进行中
      yield _this4.where({
        status: 'upcoming',
        start_time: ['<=', now],
        end_time: ['>=', now]
      }).update({
        status: 'active'
      });
    })();
  }

  /**
   * 减少库存
   */
  decreaseStock(flashSaleId, quantity) {
    var _this5 = this;

    return _asyncToGenerator(function* () {
      return yield _this5.where({
        id: flashSaleId,
        stock: ['>=', quantity]
      }).increment('sold_count', quantity).decrement('stock', quantity);
    })();
  }

  /**
   * 检查库存是否充足
   */
  checkStock(flashSaleId, quantity) {
    var _this6 = this;

    return _asyncToGenerator(function* () {
      const flashSale = yield _this6.where({
        id: flashSaleId
      }).find();

      if (think.isEmpty(flashSale)) {
        return false;
      }

      return flashSale.stock >= quantity;
    })();
  }
};