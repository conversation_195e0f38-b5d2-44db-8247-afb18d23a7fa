{"version": 3, "sources": ["..\\..\\..\\src\\api\\model\\order_exchange_log.js"], "names": ["module", "exports", "think", "Model", "getUserExchangeRecords", "userId", "limit", "records", "where", "user_id", "order", "select", "error", "console", "isOrderExchanged", "orderNumber", "order_number", "record", "find", "isEmpty", "getTodayExchangeCount", "today", "Date", "toISOString", "split", "count", "created_at", "getExchangeStats", "timeRange", "whereCondition", "now", "weekAgo", "getTime", "monthAgo", "totalExchanges", "successExchanges", "status", "totalPoints", "sum", "newUsers", "query", "createExchangeRecord", "data", "currentTime", "recordData", "order_platform", "platform", "order_amount", "amount", "exchange_points", "points", "exchange_rate", "rate", "fail_reason", "failReason", "updated_at", "recordId", "add", "updateExchangeStatus", "updateData", "result", "id", "update"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,KAApB,CAA0B;;AAEzC;;;;;;AAMMC,wBAAN,CAA6BC,MAA7B,EAAqCC,QAAQ,EAA7C,EAAiD;AAAA;;AAAA;AAC/C,UAAI;AACF,cAAMC,UAAU,MAAM,MAAKC,KAAL,CAAW;AAC/BC,mBAASJ;AADsB,SAAX,EAEnBK,KAFmB,CAEb,iBAFa,EAEMJ,KAFN,CAEYA,KAFZ,EAEmBK,MAFnB,EAAtB;;AAIA,eAAOJ,OAAP;AACD,OAND,CAME,OAAOK,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,eAAO,EAAP;AACD;AAV8C;AAWhD;;AAED;;;;;;AAMME,kBAAN,CAAuBC,WAAvB,EAAoCV,SAAS,IAA7C,EAAmD;AAAA;;AAAA;AACjD,UAAI;AACF,cAAMG,QAAQ,EAAEQ,cAAcD,WAAhB,EAAd;AACA,YAAIV,MAAJ,EAAY;AACVG,gBAAMC,OAAN,GAAgBJ,MAAhB;AACD;;AAED,cAAMY,SAAS,MAAM,OAAKT,KAAL,CAAWA,KAAX,EAAkBU,IAAlB,EAArB;AACA,eAAO,CAAChB,MAAMiB,OAAN,CAAcF,MAAd,CAAR;AACD,OARD,CAQE,OAAOL,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,eAAO,KAAP;AACD;AAZgD;AAalD;;AAED;;;;;AAKMQ,uBAAN,CAA4Bf,MAA5B,EAAoC;AAAA;;AAAA;AAClC,UAAI;AACF,cAAMgB,QAAQ,IAAIC,IAAJ,GAAWC,WAAX,GAAyBC,KAAzB,CAA+B,GAA/B,EAAoC,CAApC,CAAd;AACA,cAAMC,QAAQ,MAAM,OAAKjB,KAAL,CAAW;AAC7BC,mBAASJ,MADoB;AAE7BqB,sBAAY,CAAC,MAAD,EAAU,GAAEL,KAAM,GAAlB;AAFiB,SAAX,EAGjBI,KAHiB,EAApB;;AAKA,eAAOA,KAAP;AACD,OARD,CAQE,OAAOb,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,eAAO,CAAP;AACD;AAZiC;AAanC;;AAED;;;;;AAKMe,kBAAN,CAAuBC,YAAY,OAAnC,EAA4C;AAAA;;AAAA;AAC1C,UAAI;AACF,YAAIC,iBAAiB,EAArB;AACA,cAAMC,MAAM,IAAIR,IAAJ,EAAZ;;AAEA,gBAAQM,SAAR;AACE,eAAK,OAAL;AACE,kBAAMP,QAAQS,IAAIP,WAAJ,GAAkBC,KAAlB,CAAwB,GAAxB,EAA6B,CAA7B,CAAd;AACAK,2BAAeH,UAAf,GAA4B,CAAC,MAAD,EAAU,GAAEL,KAAM,GAAlB,CAA5B;AACA;AACF,eAAK,MAAL;AACE,kBAAMU,UAAU,IAAIT,IAAJ,CAASQ,IAAIE,OAAJ,KAAgB,IAAI,EAAJ,GAAS,EAAT,GAAc,EAAd,GAAmB,IAA5C,CAAhB;AACAH,2BAAeH,UAAf,GAA4B,CAAC,IAAD,EAAOK,QAAQR,WAAR,EAAP,CAA5B;AACA;AACF,eAAK,OAAL;AACE,kBAAMU,WAAW,IAAIX,IAAJ,CAASQ,IAAIE,OAAJ,KAAgB,KAAK,EAAL,GAAU,EAAV,GAAe,EAAf,GAAoB,IAA7C,CAAjB;AACAH,2BAAeH,UAAf,GAA4B,CAAC,IAAD,EAAOO,SAASV,WAAT,EAAP,CAA5B;AACA;AAZJ;;AAeA;AACA,cAAMW,iBAAiB,MAAM,OAAK1B,KAAL,CAAWqB,cAAX,EAA2BJ,KAA3B,EAA7B;;AAEA;AACA,cAAMU,mBAAmB,MAAM,OAAK3B,KAAL,mBAC1BqB,cAD0B;AAE7BO,kBAAQ;AAFqB,YAG5BX,KAH4B,EAA/B;;AAKA;AACA,cAAMY,cAAc,MAAM,OAAK7B,KAAL,mBACrBqB,cADqB;AAExBO,kBAAQ;AAFgB,YAGvBE,GAHuB,CAGnB,iBAHmB,CAA1B;;AAKA;AACA,cAAMC,WAAW,MAAM,OAAKC,KAAL,CAAY;;;;;;;;;OAAZ,EASpB,CAACX,eAAeH,UAAf,GAA4BG,eAAeH,UAAf,CAA0B,CAA1B,CAA5B,GAA2D,YAA5D,CAToB,CAAvB;;AAWA,eAAO;AACLQ,0BAAgBA,kBAAkB,CAD7B;AAELC,4BAAkBA,oBAAoB,CAFjC;AAGLE,uBAAaA,eAAe,CAHvB;AAILE,oBAAUA,SAAS,CAAT,IAAcA,SAAS,CAAT,EAAYd,KAA1B,GAAkC;AAJvC,SAAP;AAMD,OApDD,CAoDE,OAAOb,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,eAAO;AACLsB,0BAAgB,CADX;AAELC,4BAAkB,CAFb;AAGLE,uBAAa,CAHR;AAILE,oBAAU;AAJL,SAAP;AAMD;AA7DyC;AA8D3C;;AAED;;;;;AAKME,sBAAN,CAA2BC,IAA3B,EAAiC;AAAA;;AAAA;AAC/B,UAAI;AACF,cAAMC,cAAc,IAAIrB,IAAJ,EAApB;AACA,cAAMsB,aAAa;AACjBnC,mBAASiC,KAAKrC,MADG;AAEjBW,wBAAc0B,KAAK3B,WAFF;AAGjB8B,0BAAgBH,KAAKI,QAHJ;AAIjBC,wBAAcL,KAAKM,MAJF;AAKjBC,2BAAiBP,KAAKQ,MALL;AAMjBC,yBAAeT,KAAKU,IANH;AAOjBhB,kBAAQM,KAAKN,MAAL,IAAe,SAPN;AAQjBiB,uBAAaX,KAAKY,UAAL,IAAmB,EARf;AASjB5B,sBAAYiB,WATK;AAUjBY,sBAAYZ;AAVK,SAAnB;;AAaA,cAAMa,WAAW,MAAM,OAAKC,GAAL,CAASb,UAAT,CAAvB;AACA,eAAOY,QAAP;AACD,OAjBD,CAiBE,OAAO5C,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,cAAMA,KAAN;AACD;AArB8B;AAsBhC;;AAED;;;;;;;AAOM8C,sBAAN,CAA2BF,QAA3B,EAAqCpB,MAArC,EAA6CkB,aAAa,EAA1D,EAA8D;AAAA;;AAAA;AAC5D,UAAI;AACF,cAAMK,aAAa;AACjBvB,kBAAQA,MADS;AAEjBmB,sBAAY,IAAIjC,IAAJ;AAFK,SAAnB;;AAKA,YAAIgC,UAAJ,EAAgB;AACdK,qBAAWN,WAAX,GAAyBC,UAAzB;AACD;;AAED,cAAMM,SAAS,MAAM,OAAKpD,KAAL,CAAW,EAAEqD,IAAIL,QAAN,EAAX,EAA6BM,MAA7B,CAAoCH,UAApC,CAArB;AACA,eAAOC,SAAS,CAAhB;AACD,OAZD,CAYE,OAAOhD,KAAP,EAAc;AACdC,gBAAQD,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,eAAO,KAAP;AACD;AAhB2D;AAiB7D;AAxLwC,CAA3C", "file": "..\\..\\..\\src\\api\\model\\order_exchange_log.js", "sourcesContent": ["module.exports = class extends think.Model {\n  \n  /**\n   * 获取用户的兑换记录\n   * @param {number} userId 用户ID\n   * @param {number} limit 限制数量\n   * @returns {array} 兑换记录列表\n   */\n  async getUserExchangeRecords(userId, limit = 20) {\n    try {\n      const records = await this.where({\n        user_id: userId\n      }).order('created_at DESC').limit(limit).select();\n      \n      return records;\n    } catch (error) {\n      console.error('获取用户兑换记录失败:', error);\n      return [];\n    }\n  }\n\n  /**\n   * 检查订单号是否已被兑换\n   * @param {string} orderNumber 订单号\n   * @param {number} userId 用户ID（可选）\n   * @returns {boolean} 是否已兑换\n   */\n  async isOrderExchanged(orderNumber, userId = null) {\n    try {\n      const where = { order_number: orderNumber };\n      if (userId) {\n        where.user_id = userId;\n      }\n      \n      const record = await this.where(where).find();\n      return !think.isEmpty(record);\n    } catch (error) {\n      console.error('检查订单兑换状态失败:', error);\n      return false;\n    }\n  }\n\n  /**\n   * 获取用户今日兑换次数\n   * @param {number} userId 用户ID\n   * @returns {number} 今日兑换次数\n   */\n  async getTodayExchangeCount(userId) {\n    try {\n      const today = new Date().toISOString().split('T')[0];\n      const count = await this.where({\n        user_id: userId,\n        created_at: ['LIKE', `${today}%`]\n      }).count();\n      \n      return count;\n    } catch (error) {\n      console.error('获取今日兑换次数失败:', error);\n      return 0;\n    }\n  }\n\n  /**\n   * 获取兑换统计数据\n   * @param {string} timeRange 时间范围 (today, week, month)\n   * @returns {object} 统计数据\n   */\n  async getExchangeStats(timeRange = 'today') {\n    try {\n      let whereCondition = {};\n      const now = new Date();\n      \n      switch (timeRange) {\n        case 'today':\n          const today = now.toISOString().split('T')[0];\n          whereCondition.created_at = ['LIKE', `${today}%`];\n          break;\n        case 'week':\n          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n          whereCondition.created_at = ['>=', weekAgo.toISOString()];\n          break;\n        case 'month':\n          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n          whereCondition.created_at = ['>=', monthAgo.toISOString()];\n          break;\n      }\n      \n      // 总兑换次数\n      const totalExchanges = await this.where(whereCondition).count();\n      \n      // 成功兑换次数\n      const successExchanges = await this.where({\n        ...whereCondition,\n        status: 'success'\n      }).count();\n      \n      // 总兑换积分\n      const totalPoints = await this.where({\n        ...whereCondition,\n        status: 'success'\n      }).sum('exchange_points');\n      \n      // 新增用户数（首次兑换的用户）\n      const newUsers = await this.query(`\n        SELECT COUNT(DISTINCT user_id) as count \n        FROM (\n          SELECT user_id, MIN(created_at) as first_exchange \n          FROM order_exchange_log \n          WHERE status = 'success'\n          GROUP BY user_id\n        ) as first_exchanges \n        WHERE first_exchange >= ?\n      `, [whereCondition.created_at ? whereCondition.created_at[1] : '1970-01-01']);\n      \n      return {\n        totalExchanges: totalExchanges || 0,\n        successExchanges: successExchanges || 0,\n        totalPoints: totalPoints || 0,\n        newUsers: newUsers[0] ? newUsers[0].count : 0\n      };\n    } catch (error) {\n      console.error('获取兑换统计失败:', error);\n      return {\n        totalExchanges: 0,\n        successExchanges: 0,\n        totalPoints: 0,\n        newUsers: 0\n      };\n    }\n  }\n\n  /**\n   * 创建兑换记录\n   * @param {object} data 兑换数据\n   * @returns {number} 记录ID\n   */\n  async createExchangeRecord(data) {\n    try {\n      const currentTime = new Date();\n      const recordData = {\n        user_id: data.userId,\n        order_number: data.orderNumber,\n        order_platform: data.platform,\n        order_amount: data.amount,\n        exchange_points: data.points,\n        exchange_rate: data.rate,\n        status: data.status || 'success',\n        fail_reason: data.failReason || '',\n        created_at: currentTime,\n        updated_at: currentTime\n      };\n      \n      const recordId = await this.add(recordData);\n      return recordId;\n    } catch (error) {\n      console.error('创建兑换记录失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 更新兑换记录状态\n   * @param {number} recordId 记录ID\n   * @param {string} status 新状态\n   * @param {string} failReason 失败原因（可选）\n   * @returns {boolean} 是否成功\n   */\n  async updateExchangeStatus(recordId, status, failReason = '') {\n    try {\n      const updateData = {\n        status: status,\n        updated_at: new Date()\n      };\n      \n      if (failReason) {\n        updateData.fail_reason = failReason;\n      }\n      \n      const result = await this.where({ id: recordId }).update(updateData);\n      return result > 0;\n    } catch (error) {\n      console.error('更新兑换记录状态失败:', error);\n      return false;\n    }\n  }\n};\n"]}