{"version": 3, "sources": ["..\\..\\..\\src\\admin\\service\\qiniu.js"], "names": ["qiniu", "require", "module", "exports", "think", "Service", "getQiniuToken", "accessKey", "config", "secret<PERSON>ey", "bucket", "domain", "mac", "auth", "digest", "<PERSON>", "currentTime", "parseInt", "Date", "getTime", "options", "scope", "deadline", "putPolicy", "rs", "PutPolicy", "uploadToken", "data"], "mappings": ";;AAAA,MAAMA,QAAQC,QAAQ,OAAR,CAAd;AACAC,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,OAApB,CAA4B;AACnCC,iBAAN,GAAsB;AAAA;AAClB,gBAAIC,YAAYH,MAAMI,MAAN,CAAa,kBAAb,CAAhB;AACA,gBAAIC,YAAYL,MAAMI,MAAN,CAAa,kBAAb,CAAhB;AACA,gBAAIE,SAASN,MAAMI,MAAN,CAAa,cAAb,CAAb;AACA,gBAAIG,SAASP,MAAMI,MAAN,CAAa,cAAb,CAAb;AACA,gBAAII,MAAM,IAAIZ,MAAMa,IAAN,CAAWC,MAAX,CAAkBC,GAAtB,CAA0BR,SAA1B,EAAqCE,SAArC,CAAV;AACA,gBAAIO,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,IAAwC,GAA1D;AACA,gBAAIC,UAAU;AACVC,uBAAOX,MADG;AAEVY,0BAAUN;AACV;AAHU,aAAd;AAKA,gBAAIO,YAAY,IAAIvB,MAAMwB,EAAN,CAASC,SAAb,CAAuBL,OAAvB,CAAhB;AACA,gBAAIM,cAAcH,UAAUG,WAAV,CAAsBd,GAAtB,CAAlB;AACA,gBAAIe,OAAO;AACPD,6BAAaA,WADN;AAEPf,wBAAQA;AAFD,aAAX;AAIA,mBAAOgB,IAAP;AAlBkB;AAmBrB;AApBwC,CAA7C", "file": "..\\..\\..\\src\\admin\\service\\qiniu.js", "sourcesContent": ["const qiniu = require('qiniu');\nmodule.exports = class extends think.Service {\n    async getQiniuToken() {\n        let accessKey = think.config('qiniu.access_key');\n        let secretKey = think.config('qiniu.secret_key');\n        let bucket = think.config('qiniu.bucket');\n        let domain = think.config('qiniu.domain');\n        let mac = new qiniu.auth.digest.Mac(accessKey, secretKey);\n        let currentTime = parseInt(new Date().getTime() / 1000) + 600;\n        let options = {\n            scope: bucket,\n            deadline: currentTime\n            // 移除 saveKey，让前端控制文件名\n        };\n        let putPolicy = new qiniu.rs.PutPolicy(options);\n        let uploadToken = putPolicy.uploadToken(mac);\n        let data = {\n            uploadToken: uploadToken,\n            domain: domain\n        };\n        return data;\n    }\n};"]}