{"version": 3, "sources": ["..\\..\\..\\src\\common\\service\\flashsale_scheduler.js"], "names": ["moment", "require", "module", "exports", "FlashSaleScheduler", "generateRounds", "campaign", "model", "console", "log", "name", "roundModel", "startDate", "start_date", "endDate", "end_date", "totalDays", "diff", "roundNumber", "rounds", "day", "currentDate", "add", "dayStart", "set", "hour", "daily_start_time", "minute", "second", "millisecond", "dayEnd", "daily_end_time", "format", "currentTime", "isBefore", "roundStart", "roundEnd", "round_duration", "isAfter", "round", "campaign_id", "id", "round_number", "start_time", "end_time", "stock", "stock_per_round", "sold_count", "status", "push", "break_duration", "length", "where", "delete", "error", "getCurrentRounds", "campaignModel", "now", "activeRounds", "select", "update", "find", "getUpcomingRounds", "minutes", "futureTime", "upcomingRounds", "order", "countdown", "endExpiredRounds", "result", "checkAndUpdateStock", "roundId", "quantity", "Error", "remainingStock", "increment"], "mappings": ";;AAAA;;;;;AAKA,MAAMA,SAASC,QAAQ,QAAR,CAAf;;AAEAC,OAAOC,OAAP,GAAiB,MAAMC,kBAAN,CAAyB;;AAExC;;;;;AAKMC,gBAAN,CAAqBC,QAArB,EAA+BC,KAA/B,EAAsC;AAAA;AACpC,UAAI;AACFC,gBAAQC,GAAR,CAAa,WAAUH,SAASI,IAAK,WAArC;;AAEA,cAAMC,aAAaJ,MAAM,mBAAN,CAAnB;;AAEA;AACA,cAAMK,YAAYZ,OAAOM,SAASO,UAAhB,CAAlB;AACA,cAAMC,UAAUd,OAAOM,SAASS,QAAhB,CAAhB;AACA,cAAMC,YAAYF,QAAQG,IAAR,CAAaL,SAAb,EAAwB,MAAxB,IAAkC,CAApD;;AAEAJ,gBAAQC,GAAR,CAAa,QAAOO,SAAU,IAA9B;;AAEA,YAAIE,cAAc,CAAlB;AACA,cAAMC,SAAS,EAAf;;AAEA;AACA,aAAK,IAAIC,MAAM,CAAf,EAAkBA,MAAMJ,SAAxB,EAAmCI,KAAnC,EAA0C;AACxC,gBAAMC,cAAcrB,OAAOY,SAAP,EAAkBU,GAAlB,CAAsBF,GAAtB,EAA2B,MAA3B,CAApB;;AAEA;AACA,gBAAMG,WAAWvB,OAAOqB,WAAP,EAAoBG,GAApB,CAAwB;AACvCC,kBAAMzB,OAAOM,SAASoB,gBAAhB,EAAkC,UAAlC,EAA8CD,IAA9C,EADiC;AAEvCE,oBAAQ3B,OAAOM,SAASoB,gBAAhB,EAAkC,UAAlC,EAA8CC,MAA9C,EAF+B;AAGvCC,oBAAQ,CAH+B;AAIvCC,yBAAa;AAJ0B,WAAxB,CAAjB;;AAOA,gBAAMC,SAAS9B,OAAOqB,WAAP,EAAoBG,GAApB,CAAwB;AACrCC,kBAAMzB,OAAOM,SAASyB,cAAhB,EAAgC,UAAhC,EAA4CN,IAA5C,EAD+B;AAErCE,oBAAQ3B,OAAOM,SAASyB,cAAhB,EAAgC,UAAhC,EAA4CJ,MAA5C,EAF6B;AAGrCC,oBAAQ,CAH6B;AAIrCC,yBAAa;AAJwB,WAAxB,CAAf;;AAOArB,kBAAQC,GAAR,CAAa,MAAKY,YAAYW,MAAZ,CAAmB,YAAnB,CAAiC,MAAnD;;AAEA;AACA,cAAIC,cAAcjC,OAAOuB,QAAP,CAAlB;;AAEA,iBAAOU,YAAYC,QAAZ,CAAqBJ,MAArB,CAAP,EAAqC;AACnC,kBAAMK,aAAanC,OAAOiC,WAAP,CAAnB;AACA,kBAAMG,WAAWpC,OAAOiC,WAAP,EAAoBX,GAApB,CAAwBhB,SAAS+B,cAAjC,EAAiD,SAAjD,CAAjB;;AAEA;AACA,gBAAID,SAASE,OAAT,CAAiBR,MAAjB,CAAJ,EAA8B;AAC5B;AACD;;AAED,kBAAMS,QAAQ;AACZC,2BAAalC,SAASmC,EADV;AAEZC,4BAAcxB,WAFF;AAGZyB,0BAAYR,WAAWH,MAAX,CAAkB,qBAAlB,CAHA;AAIZY,wBAAUR,SAASJ,MAAT,CAAgB,qBAAhB,CAJE;AAKZa,qBAAOvC,SAASwC,eALJ;AAMZC,0BAAY,CANA;AAOZC,sBAAQb,WAAWG,OAAX,CAAmBtC,QAAnB,IAA+B,UAA/B,GAA4C;AAPxC,aAAd;;AAUAmB,mBAAO8B,IAAP,CAAYV,KAAZ;AACArB;;AAEA;AACAe,wBAAYX,GAAZ,CAAgBhB,SAAS+B,cAAT,GAA0B/B,SAAS4C,cAAnD,EAAmE,SAAnE;AACD;AACF;;AAED1C,gBAAQC,GAAR,CAAa,QAAOU,OAAOgC,MAAO,MAAlC;;AAEA;AACA,YAAIhC,OAAOgC,MAAP,GAAgB,CAApB,EAAuB;AACrB;AACA,gBAAMxC,WAAWyC,KAAX,CAAiB,EAAEZ,aAAalC,SAASmC,EAAxB,EAAjB,EAA+CY,MAA/C,EAAN;;AAEA;AACA,eAAK,MAAMd,KAAX,IAAoBpB,MAApB,EAA4B;AAC1B,kBAAMR,WAAWW,GAAX,CAAeiB,KAAf,CAAN;AACD;;AAED/B,kBAAQC,GAAR,CAAa,UAASU,OAAOgC,MAAO,MAApC;AACD;;AAED,eAAOhC,OAAOgC,MAAd;AAED,OAnFD,CAmFE,OAAOG,KAAP,EAAc;AACd9C,gBAAQ8C,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,cAAMA,KAAN;AACD;AAvFmC;AAwFrC;;AAED;;;;AAIMC,kBAAN,CAAuBhD,KAAvB,EAA8B;AAAA;AAC5B,UAAI;AACF,cAAMI,aAAaJ,MAAM,mBAAN,CAAnB;AACA,cAAMiD,gBAAgBjD,MAAM,sBAAN,CAAtB;;AAEA,cAAMkD,MAAMzD,SAASgC,MAAT,CAAgB,qBAAhB,CAAZ;;AAEA;AACA,cAAM0B,eAAe,MAAM/C,WAAWyC,KAAX,CAAiB;AAC1CT,sBAAY,CAAC,IAAD,EAAOc,GAAP,CAD8B;AAE1Cb,oBAAU,CAAC,GAAD,EAAMa,GAAN,CAFgC;AAG1CT,kBAAQ;AAHkC,SAAjB,EAIxBW,MAJwB,EAA3B;;AAMA;AACA,aAAK,MAAMpB,KAAX,IAAoBmB,YAApB,EAAkC;AAChC,gBAAM/C,WAAWyC,KAAX,CAAiB,EAAEX,IAAIF,MAAME,EAAZ,EAAjB,EAAmCmB,MAAnC,CAA0C,EAAEZ,QAAQ,QAAV,EAA1C,CAAN;AACAT,gBAAMS,MAAN,GAAe,QAAf;AACD;;AAED;AACA,cAAM7B,SAAS,EAAf;AACA,aAAK,MAAMoB,KAAX,IAAoBmB,YAApB,EAAkC;AAChC,gBAAMpD,WAAW,MAAMkD,cAAcJ,KAAd,CAAoB,EAAEX,IAAIF,MAAMC,WAAZ,EAApB,EAA+CqB,IAA/C,EAAvB;AACA,cAAIvD,YAAYA,SAAS0C,MAAT,KAAoB,QAApC,EAA8C;AAC5C7B,mBAAO8B,IAAP,mBACKV,KADL;AAEEjC,wBAAUA;AAFZ;AAID;AACF;;AAED,eAAOa,MAAP;AAED,OAjCD,CAiCE,OAAOmC,KAAP,EAAc;AACd9C,gBAAQ8C,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,cAAMA,KAAN;AACD;AArC2B;AAsC7B;;AAED;;;;;AAKMQ,mBAAN,CAAwBvD,KAAxB,EAA+BwD,UAAU,EAAzC,EAA6C;AAAA;AAC3C,UAAI;AACF,cAAMpD,aAAaJ,MAAM,mBAAN,CAAnB;AACA,cAAMiD,gBAAgBjD,MAAM,sBAAN,CAAtB;;AAEA,cAAMkD,MAAMzD,QAAZ;AACA,cAAMgE,aAAahE,SAASsB,GAAT,CAAayC,OAAb,EAAsB,SAAtB,CAAnB;;AAEA,cAAME,iBAAiB,MAAMtD,WAAWyC,KAAX,CAAiB;AAC5CT,sBAAY,CAAC,SAAD,EAAY,CAACc,IAAIzB,MAAJ,CAAW,qBAAX,CAAD,EAAoCgC,WAAWhC,MAAX,CAAkB,qBAAlB,CAApC,CAAZ,CADgC;AAE5CgB,kBAAQ;AAFoC,SAAjB,EAG1BkB,KAH0B,CAGpB,gBAHoB,EAGFP,MAHE,EAA7B;;AAKA;AACA,cAAMxC,SAAS,EAAf;AACA,aAAK,MAAMoB,KAAX,IAAoB0B,cAApB,EAAoC;AAClC,gBAAM3D,WAAW,MAAMkD,cAAcJ,KAAd,CAAoB,EAAEX,IAAIF,MAAMC,WAAZ,EAApB,EAA+CqB,IAA/C,EAAvB;AACA,cAAIvD,YAAYA,SAAS0C,MAAT,KAAoB,QAApC,EAA8C;AAC5C7B,mBAAO8B,IAAP,mBACKV,KADL;AAEEjC,wBAAUA,QAFZ;AAGE6D,yBAAWnE,OAAOuC,MAAMI,UAAb,EAAyB1B,IAAzB,CAA8BwC,GAA9B,EAAmC,SAAnC;AAHb;AAKD;AACF;;AAED,eAAOtC,MAAP;AAED,OA3BD,CA2BE,OAAOmC,KAAP,EAAc;AACd9C,gBAAQ8C,KAAR,CAAc,cAAd,EAA8BA,KAA9B;AACA,cAAMA,KAAN;AACD;AA/B0C;AAgC5C;;AAED;;;;AAIMc,kBAAN,CAAuB7D,KAAvB,EAA8B;AAAA;AAC5B,UAAI;AACF,cAAMI,aAAaJ,MAAM,mBAAN,CAAnB;AACA,cAAMkD,MAAMzD,SAASgC,MAAT,CAAgB,qBAAhB,CAAZ;;AAEA,cAAMqC,SAAS,MAAM1D,WAAWyC,KAAX,CAAiB;AACpCR,oBAAU,CAAC,GAAD,EAAMa,GAAN,CAD0B;AAEpCT,kBAAQ,CAAC,IAAD,EAAO,CAAC,UAAD,EAAa,QAAb,CAAP;AAF4B,SAAjB,EAGlBY,MAHkB,CAGX,EAAEZ,QAAQ,OAAV,EAHW,CAArB;;AAKAxC,gBAAQC,GAAR,CAAa,OAAM4D,MAAO,QAA1B;AACA,eAAOA,MAAP;AAED,OAZD,CAYE,OAAOf,KAAP,EAAc;AACd9C,gBAAQ8C,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,cAAMA,KAAN;AACD;AAhB2B;AAiB7B;;AAED;;;;;;AAMMgB,qBAAN,CAA0BC,OAA1B,EAAmCC,QAAnC,EAA6CjE,KAA7C,EAAoD;AAAA;AAClD,UAAI;AACF,cAAMI,aAAaJ,MAAM,mBAAN,CAAnB;;AAEA;AACA,cAAMgC,QAAQ,MAAM5B,WAAWyC,KAAX,CAAiB,EAAEX,IAAI8B,OAAN,EAAjB,EAAkCV,IAAlC,EAApB;AACA,YAAI,CAACtB,KAAL,EAAY;AACV,gBAAM,IAAIkC,KAAJ,CAAU,OAAV,CAAN;AACD;;AAED;AACA,cAAMC,iBAAiBnC,MAAMM,KAAN,GAAcN,MAAMQ,UAA3C;AACA,YAAI2B,iBAAiBF,QAArB,EAA+B;AAC7B,gBAAM,IAAIC,KAAJ,CAAU,MAAV,CAAN;AACD;;AAED;AACA,cAAMJ,SAAS,MAAM1D,WAAWyC,KAAX,CAAiB,EAAEX,IAAI8B,OAAN,EAAjB,EAAkCI,SAAlC,CAA4C,YAA5C,EAA0DH,QAA1D,CAArB;;AAEA,eAAOH,MAAP;AAED,OApBD,CAoBE,OAAOf,KAAP,EAAc;AACd9C,gBAAQ8C,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,cAAMA,KAAN;AACD;AAxBiD;AAyBnD;AA1OuC,CAA1C", "file": "..\\..\\..\\src\\common\\service\\flashsale_scheduler.js", "sourcesContent": ["/**\n * 秒杀轮次自动生成和调度服务\n * 负责生成每5分钟的秒杀轮次\n */\n\nconst moment = require('moment');\n\nmodule.exports = class FlashSaleScheduler {\n  \n  /**\n   * 为指定活动生成轮次\n   * @param {Object} campaign 活动配置\n   * @param {Object} model 数据模型\n   */\n  async generateRounds(campaign, model) {\n    try {\n      console.log(`=== 为活动 ${campaign.name} 生成轮次 ===`);\n      \n      const roundModel = model('flash_sale_rounds');\n      \n      // 计算总天数\n      const startDate = moment(campaign.start_date);\n      const endDate = moment(campaign.end_date);\n      const totalDays = endDate.diff(startDate, 'days') + 1;\n      \n      console.log(`活动周期：${totalDays} 天`);\n      \n      let roundNumber = 1;\n      const rounds = [];\n      \n      // 遍历每一天\n      for (let day = 0; day < totalDays; day++) {\n        const currentDate = moment(startDate).add(day, 'days');\n        \n        // 构建当天的开始和结束时间\n        const dayStart = moment(currentDate).set({\n          hour: moment(campaign.daily_start_time, 'HH:mm:ss').hour(),\n          minute: moment(campaign.daily_start_time, 'HH:mm:ss').minute(),\n          second: 0,\n          millisecond: 0\n        });\n        \n        const dayEnd = moment(currentDate).set({\n          hour: moment(campaign.daily_end_time, 'HH:mm:ss').hour(),\n          minute: moment(campaign.daily_end_time, 'HH:mm:ss').minute(),\n          second: 0,\n          millisecond: 0\n        });\n        \n        console.log(`生成 ${currentDate.format('YYYY-MM-DD')} 的轮次`);\n        \n        // 在当天时间范围内生成轮次\n        let currentTime = moment(dayStart);\n        \n        while (currentTime.isBefore(dayEnd)) {\n          const roundStart = moment(currentTime);\n          const roundEnd = moment(currentTime).add(campaign.round_duration, 'seconds');\n          \n          // 如果轮次结束时间超过当天结束时间，则截止到当天结束\n          if (roundEnd.isAfter(dayEnd)) {\n            break;\n          }\n          \n          const round = {\n            campaign_id: campaign.id,\n            round_number: roundNumber,\n            start_time: roundStart.format('YYYY-MM-DD HH:mm:ss'),\n            end_time: roundEnd.format('YYYY-MM-DD HH:mm:ss'),\n            stock: campaign.stock_per_round,\n            sold_count: 0,\n            status: roundStart.isAfter(moment()) ? 'upcoming' : 'ended'\n          };\n          \n          rounds.push(round);\n          roundNumber++;\n          \n          // 移动到下一个轮次开始时间\n          currentTime.add(campaign.round_duration + campaign.break_duration, 'seconds');\n        }\n      }\n      \n      console.log(`总共生成 ${rounds.length} 个轮次`);\n      \n      // 批量插入轮次\n      if (rounds.length > 0) {\n        // 先删除该活动的现有轮次\n        await roundModel.where({ campaign_id: campaign.id }).delete();\n        \n        // 批量插入新轮次\n        for (const round of rounds) {\n          await roundModel.add(round);\n        }\n        \n        console.log(`✅ 成功生成 ${rounds.length} 个轮次`);\n      }\n      \n      return rounds.length;\n      \n    } catch (error) {\n      console.error('生成轮次失败:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * 获取当前进行中的轮次\n   * @param {Object} model 数据模型\n   */\n  async getCurrentRounds(model) {\n    try {\n      const roundModel = model('flash_sale_rounds');\n      const campaignModel = model('flash_sale_campaigns');\n      \n      const now = moment().format('YYYY-MM-DD HH:mm:ss');\n      \n      // 获取当前时间的活跃轮次\n      const activeRounds = await roundModel.where({\n        start_time: ['<=', now],\n        end_time: ['>', now],\n        status: 'upcoming'\n      }).select();\n      \n      // 更新状态为active\n      for (const round of activeRounds) {\n        await roundModel.where({ id: round.id }).update({ status: 'active' });\n        round.status = 'active';\n      }\n      \n      // 获取活动信息\n      const rounds = [];\n      for (const round of activeRounds) {\n        const campaign = await campaignModel.where({ id: round.campaign_id }).find();\n        if (campaign && campaign.status === 'active') {\n          rounds.push({\n            ...round,\n            campaign: campaign\n          });\n        }\n      }\n      \n      return rounds;\n      \n    } catch (error) {\n      console.error('获取当前轮次失败:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * 获取即将开始的轮次\n   * @param {Object} model 数据模型\n   * @param {number} minutes 未来多少分钟内\n   */\n  async getUpcomingRounds(model, minutes = 30) {\n    try {\n      const roundModel = model('flash_sale_rounds');\n      const campaignModel = model('flash_sale_campaigns');\n      \n      const now = moment();\n      const futureTime = moment().add(minutes, 'minutes');\n      \n      const upcomingRounds = await roundModel.where({\n        start_time: ['BETWEEN', [now.format('YYYY-MM-DD HH:mm:ss'), futureTime.format('YYYY-MM-DD HH:mm:ss')]],\n        status: 'upcoming'\n      }).order('start_time ASC').select();\n      \n      // 获取活动信息\n      const rounds = [];\n      for (const round of upcomingRounds) {\n        const campaign = await campaignModel.where({ id: round.campaign_id }).find();\n        if (campaign && campaign.status === 'active') {\n          rounds.push({\n            ...round,\n            campaign: campaign,\n            countdown: moment(round.start_time).diff(now, 'seconds')\n          });\n        }\n      }\n      \n      return rounds;\n      \n    } catch (error) {\n      console.error('获取即将开始的轮次失败:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * 结束过期的轮次\n   * @param {Object} model 数据模型\n   */\n  async endExpiredRounds(model) {\n    try {\n      const roundModel = model('flash_sale_rounds');\n      const now = moment().format('YYYY-MM-DD HH:mm:ss');\n      \n      const result = await roundModel.where({\n        end_time: ['<', now],\n        status: ['IN', ['upcoming', 'active']]\n      }).update({ status: 'ended' });\n      \n      console.log(`结束了 ${result} 个过期轮次`);\n      return result;\n      \n    } catch (error) {\n      console.error('结束过期轮次失败:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * 检查并更新库存\n   * @param {number} roundId 轮次ID\n   * @param {number} quantity 购买数量\n   * @param {Object} model 数据模型\n   */\n  async checkAndUpdateStock(roundId, quantity, model) {\n    try {\n      const roundModel = model('flash_sale_rounds');\n      \n      // 获取轮次信息\n      const round = await roundModel.where({ id: roundId }).find();\n      if (!round) {\n        throw new Error('轮次不存在');\n      }\n      \n      // 检查库存\n      const remainingStock = round.stock - round.sold_count;\n      if (remainingStock < quantity) {\n        throw new Error('库存不足');\n      }\n      \n      // 更新已售数量\n      const result = await roundModel.where({ id: roundId }).increment('sold_count', quantity);\n      \n      return result;\n      \n    } catch (error) {\n      console.error('更新库存失败:', error);\n      throw error;\n    }\n  }\n};\n"]}