{"version": 3, "sources": ["..\\..\\..\\src\\api\\controller\\redirect.js"], "names": ["Base", "require", "rp", "module", "exports", "generateUrlLinkAction", "orderNumber", "get", "source", "utm", "userId", "queryParams", "push", "encodeURIComponent", "query", "join", "path", "console", "log", "urlLink", "generateWechatUrlLink", "success", "error", "fail", "message", "accessToken", "getAccessToken", "url", "requestData", "env_version", "is_expire", "response", "method", "uri", "body", "json", "<PERSON><PERSON><PERSON>", "url_link", "Error", "errmsg", "appid", "think", "config", "secret", "cache<PERSON>ey", "cache", "access_token", "testAction", "html", "header", "directAction", "redirect", "scheme", "orderExchangeAction", "autoJump", "env", "<PERSON><PERSON><PERSON><PERSON>", "generateRedirectHTML", "title", "generateErrorHTML", "params"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACA,MAAMC,KAAKD,QAAQ,iBAAR,CAAX;;AAEAE,OAAOC,OAAP,GAAiB,cAAcJ,IAAd,CAAmB;;AAEhC;;;;AAIMK,yBAAN,GAA8B;AAAA;;AAAA;AAC1B,gBAAI;AACA,sBAAMC,cAAc,MAAKC,GAAL,CAAS,aAAT,KAA2B,EAA/C;AACA,sBAAMC,SAAS,MAAKD,GAAL,CAAS,QAAT,KAAsB,KAArC;AACA,sBAAME,MAAM,MAAKF,GAAL,CAAS,KAAT,KAAmB,EAA/B;AACA,sBAAMG,SAAS,MAAKH,GAAL,CAAS,QAAT,KAAsB,EAArC;;AAEA;AACA,sBAAMI,cAAc,EAApB;AACA,oBAAIL,WAAJ,EAAiBK,YAAYC,IAAZ,CAAkB,eAAcC,mBAAmBP,WAAnB,CAAgC,EAAhE;AACjB,oBAAIE,MAAJ,EAAYG,YAAYC,IAAZ,CAAkB,UAASC,mBAAmBL,MAAnB,CAA2B,EAAtD;AACZ,oBAAIC,GAAJ,EAASE,YAAYC,IAAZ,CAAkB,OAAMC,mBAAmBJ,GAAnB,CAAwB,EAAhD;AACT,oBAAIC,MAAJ,EAAYC,YAAYC,IAAZ,CAAkB,UAASC,mBAAmBH,MAAnB,CAA2B,EAAtD;;AAEZ,sBAAMI,QAAQH,YAAYI,IAAZ,CAAiB,GAAjB,CAAd;AACA,sBAAMC,OAAQ,6BAA4BF,QAAQ,MAAMA,KAAd,GAAsB,EAAG,EAAnE;;AAEAG,wBAAQC,GAAR,CAAY,gBAAZ,EAA8BF,IAA9B;;AAEA;AACA,sBAAMG,UAAU,MAAM,MAAKC,qBAAL,CAA2BJ,IAA3B,CAAtB;;AAEA,uBAAO,MAAKK,OAAL,CAAa;AAChBF,6BAASA,OADO;AAEhBH,0BAAMA,IAFU;AAGhBF,2BAAOA;AAHS,iBAAb,CAAP;AAMH,aA3BD,CA2BE,OAAOQ,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,eAAd,EAA+BA,KAA/B;AACA,uBAAO,MAAKC,IAAL,CAAU,GAAV,EAAe,aAAaD,MAAME,OAAlC,CAAP;AACH;AA/ByB;AAgC7B;;AAED;;;AAGMJ,yBAAN,CAA4BJ,IAA5B,EAAkC;AAAA;;AAAA;AAC9B,gBAAI;AACA;AACA,sBAAMS,cAAc,MAAM,OAAKC,cAAL,EAA1B;;AAEA;AACA,sBAAMC,MAAO,+DAA8DF,WAAY,EAAvF;;AAEA,sBAAMG,cAAc;AAChBZ,0BAAMA,IADU;AAEhBF,2BAAO,EAFS;AAGhBe,iCAAa,SAHG,EAGQ;AACxBC,+BAAW;AAJK,iBAApB;;AAOAb,wBAAQC,GAAR,CAAY,oBAAZ,EAAkCU,WAAlC;;AAEA,sBAAMG,WAAW,MAAM7B,GAAG;AACtB8B,4BAAQ,MADc;AAEtBC,yBAAKN,GAFiB;AAGtBO,0BAAMN,WAHgB;AAItBO,0BAAM;AAJgB,iBAAH,CAAvB;;AAOAlB,wBAAQC,GAAR,CAAY,UAAZ,EAAwBa,QAAxB;;AAEA,oBAAIA,SAASK,OAAT,KAAqB,CAAzB,EAA4B;AACxB,2BAAOL,SAASM,QAAhB;AACH,iBAFD,MAEO;AACH,0BAAM,IAAIC,KAAJ,CAAW,YAAWP,SAASK,OAAQ,MAAKL,SAASQ,MAAO,EAA5D,CAAN;AACH;AAEJ,aA/BD,CA+BE,OAAOjB,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,YAAd,EAA4BA,KAA5B;AACA,sBAAMA,KAAN;AACH;AAnC6B;AAoCjC;;AAED;;;AAGMI,kBAAN,GAAuB;AAAA;AACnB,gBAAI;AACA,sBAAMc,QAAQC,MAAMC,MAAN,CAAa,cAAb,CAAd;AACA,sBAAMC,SAASF,MAAMC,MAAN,CAAa,eAAb,CAAf;;AAEA,oBAAI,CAACF,KAAD,IAAU,CAACG,MAAf,EAAuB;AACnB,0BAAM,IAAIL,KAAJ,CAAU,yBAAV,CAAN;AACH;;AAED;AACA,sBAAMM,WAAW,qBAAjB;AACA,oBAAInB,cAAc,MAAMgB,MAAMI,KAAN,CAAYD,QAAZ,CAAxB;;AAEA,oBAAI,CAACnB,WAAL,EAAkB;AACd;AACA,0BAAME,MAAO,8EAA6Ea,KAAM,WAAUG,MAAO,EAAjH;;AAEA,0BAAMZ,WAAW,MAAM7B,GAAG;AACtB8B,gCAAQ,KADc;AAEtBC,6BAAKN,GAFiB;AAGtBQ,8BAAM;AAHgB,qBAAH,CAAvB;;AAMA,wBAAIJ,SAASe,YAAb,EAA2B;AACvBrB,sCAAcM,SAASe,YAAvB;AACA;AACA,8BAAML,MAAMI,KAAN,CAAYD,QAAZ,EAAsBnB,WAAtB,EAAmC,IAAnC,CAAN;AACAR,gCAAQC,GAAR,CAAY,oBAAZ;AACH,qBALD,MAKO;AACH,8BAAM,IAAIoB,KAAJ,CAAW,qBAAoBP,SAASK,OAAQ,MAAKL,SAASQ,MAAO,EAArE,CAAN;AACH;AACJ,iBAlBD,MAkBO;AACHtB,4BAAQC,GAAR,CAAY,mBAAZ;AACH;;AAED,uBAAOO,WAAP;AAEH,aApCD,CAoCE,OAAOH,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,mBAAd,EAAmCA,KAAnC;AACA,sBAAMA,KAAN;AACH;AAxCkB;AAyCtB;;AAED;;;;AAIMyB,cAAN,GAAmB;AAAA;;AAAA;AACf,kBAAMC,OAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAAd;;AA8HA,mBAAKC,MAAL,CAAY,cAAZ,EAA4B,0BAA5B;AACA,mBAAKf,IAAL,GAAYc,IAAZ;AAhIe;AAiIlB;AACD;;;AAGMD,cAAN,GAAmB;AAAA;;AAAA;AACf,kBAAMC,OAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAAd;;AA2FA,mBAAKC,MAAL,CAAY,cAAZ,EAA4B,0BAA5B;AACA,mBAAKf,IAAL,GAAYc,IAAZ;AA7Fe;AA8FlB;;AAED;;;;AAIME,gBAAN,GAAqB;AAAA;;AAAA;AACjB,gBAAI;AACAjC,wBAAQC,GAAR,CAAY,gBAAZ;;AAEA;AACA,oBAAIC,UAAU,IAAd;AACA,oBAAI;AACA,0BAAMH,OAAO,4BAAb;AACAG,8BAAU,MAAM,OAAKC,qBAAL,CAA2BJ,IAA3B,CAAhB;AACAC,4BAAQC,GAAR,CAAY,qBAAZ,EAAmCC,OAAnC;;AAEA;AACA,2BAAKgC,QAAL,CAAchC,OAAd;AACA;AAEH,iBATD,CASE,OAAOG,KAAP,EAAc;AACZL,4BAAQK,KAAR,CAAc,4BAAd,EAA4CA,KAA5C;AACH;;AAED;AACA,sBAAMkB,QAAQC,MAAMC,MAAN,CAAa,cAAb,CAAd;AACA,sBAAMU,SAAU,+BAA8BZ,KAAM,sDAApD;;AAEAvB,wBAAQC,GAAR,CAAY,kBAAZ,EAAgCkC,MAAhC;AACA,uBAAKD,QAAL,CAAcC,MAAd;AAEH,aAzBD,CAyBE,OAAO9B,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,uBAAO,OAAKC,IAAL,CAAU,GAAV,EAAe,WAAWD,MAAME,OAAhC,CAAP;AACH;AA7BgB;AA8BpB;;AAED;;;AAGM6B,uBAAN,GAA4B;AAAA;;AAAA;AACxB,gBAAI;AACA;AACA,sBAAM/C,cAAc,OAAKC,GAAL,CAAS,aAAT,KAA2B,EAA/C;AACA,sBAAMC,SAAS,OAAKD,GAAL,CAAS,QAAT,KAAsB,KAArC;AACA,sBAAME,MAAM,OAAKF,GAAL,CAAS,KAAT,KAAmB,EAA/B;AACA,sBAAMG,SAAS,OAAKH,GAAL,CAAS,QAAT,KAAsB,EAArC;AACA,sBAAM+C,WAAW,OAAK/C,GAAL,CAAS,UAAT,KAAwB,OAAzC,CANA,CAMkD;;AAElD;AACA,sBAAMiC,QAAQC,MAAMC,MAAN,CAAa,cAAb,CAAd;AACA,sBAAMa,MAAM,OAAKhD,GAAL,CAAS,KAAT,KAAmB,SAA/B,CAVA,CAU0C;;AAE1C;AACA,sBAAMI,cAAc,EAApB;AACA,oBAAIL,WAAJ,EAAiBK,YAAYC,IAAZ,CAAkB,eAAcC,mBAAmBP,WAAnB,CAAgC,EAAhE;AACjB,oBAAIE,MAAJ,EAAYG,YAAYC,IAAZ,CAAkB,UAASC,mBAAmBL,MAAnB,CAA2B,EAAtD;AACZ,oBAAIC,GAAJ,EAASE,YAAYC,IAAZ,CAAkB,OAAMC,mBAAmBJ,GAAnB,CAAwB,EAAhD;AACT,oBAAIC,MAAJ,EAAYC,YAAYC,IAAZ,CAAkB,UAASC,mBAAmBH,MAAnB,CAA2B,EAAtD;;AAEZ,sBAAMI,QAAQH,YAAYI,IAAZ,CAAiB,GAAjB,CAAd;AACA,sBAAMyC,eAAe3C,mBAAmBC,KAAnB,CAArB;;AAEA;AACA,oBAAIK,UAAU,IAAd;AACA,oBAAI;AACA,0BAAMH,OAAQ,6BAA4BF,QAAQ,MAAMA,KAAd,GAAsB,EAAG,EAAnE;AACAK,8BAAU,MAAM,OAAKC,qBAAL,CAA2BJ,IAA3B,CAAhB;AACAC,4BAAQC,GAAR,CAAY,eAAZ,EAA6BC,OAA7B;AACH,iBAJD,CAIE,OAAOG,KAAP,EAAc;AACZL,4BAAQK,KAAR,CAAc,gCAAd,EAAgDA,KAAhD;AACH;;AAED;AACA,sBAAM8B,SAAU,+BAA8BZ,KAAM,0CAAyCgB,YAAa,sBAA1G;;AAEA;AACA,sBAAMR,OAAO,OAAKS,oBAAL,CAA0B;AACnCC,2BAAO,YAD4B;AAEnCpD,+BAFmC;AAGnCa,6BAASA,WAAWiC,MAHe,EAGP;AAC5BA,0BAJmC;AAKnCZ,yBALmC;AAMnC1B,2BAAO0C,YAN4B;AAOnCF;AAPmC,iBAA1B,CAAb;;AAUA,uBAAKL,MAAL,CAAY,cAAZ,EAA4B,0BAA5B;AACA,uBAAKf,IAAL,GAAYc,IAAZ;AAEH,aAjDD,CAiDE,OAAO1B,KAAP,EAAc;AACZL,wBAAQK,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAKY,IAAL,GAAY,OAAKyB,iBAAL,EAAZ;AACH;AArDuB;AAsD3B;;AAED;;;AAGAF,yBAAqBG,MAArB,EAA6B;AACzB,cAAM,EAAEF,KAAF,EAASpD,WAAT,EAAsBa,OAAtB,EAA+BiC,MAA/B,EAAuCZ,KAAvC,EAA8C1B,KAA9C,EAAqDwC,QAArD,KAAkEM,MAAxE;;AAEA,eAAQ;;;;;aAKHF,KAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAiGTpD,cAAe,gCAA+BA,WAAY,QAA1D,GAAoE,EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA4BlDa,OAAQ;8BACTiC,MAAO;;;;;;;;;;;;;;;;;;gCAkBLE,QAAS;;;;;;;;;QArJjC;AA+JH;;AAED;;;AAGAK,wBAAoB;AAChB,eAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAAR;AAiCH;AAnpB+B,CAApC", "file": "..\\..\\..\\src\\api\\controller\\redirect.js", "sourcesContent": ["const Base = require('./base.js');\nconst rp = require('request-promise');\n\nmodule.exports = class extends Base {\n\n    /**\n     * 生成URL Link\n     * GET /api/redirect/generate-url-link\n     */\n    async generateUrlLinkAction() {\n        try {\n            const orderNumber = this.get('orderNumber') || '';\n            const source = this.get('source') || 'web';\n            const utm = this.get('utm') || '';\n            const userId = this.get('userId') || '';\n\n            // 构建小程序路径和参数\n            const queryParams = [];\n            if (orderNumber) queryParams.push(`orderNumber=${encodeURIComponent(orderNumber)}`);\n            if (source) queryParams.push(`source=${encodeURIComponent(source)}`);\n            if (utm) queryParams.push(`utm=${encodeURIComponent(utm)}`);\n            if (userId) queryParams.push(`userId=${encodeURIComponent(userId)}`);\n\n            const query = queryParams.join('&');\n            const path = `pages/order-exchange/index${query ? '?' + query : ''}`;\n\n            console.log('生成URL Link，路径:', path);\n\n            // 调用微信API生成URL Link\n            const urlLink = await this.generateWechatUrlLink(path);\n\n            return this.success({\n                urlLink: urlLink,\n                path: path,\n                query: query\n            });\n\n        } catch (error) {\n            console.error('生成URL Link失败:', error);\n            return this.fail(500, '生成链接失败: ' + error.message);\n        }\n    }\n\n    /**\n     * 调用微信API生成URL Link\n     */\n    async generateWechatUrlLink(path) {\n        try {\n            // 获取access_token\n            const accessToken = await this.getAccessToken();\n\n            // 调用微信API生成URL Link\n            const url = `https://api.weixin.qq.com/wxa/generate_urllink?access_token=${accessToken}`;\n\n            const requestData = {\n                path: path,\n                query: '',\n                env_version: 'develop', // develop-开发版, trial-体验版, release-正式版\n                is_expire: false\n            };\n\n            console.log('调用微信API生成URL Link:', requestData);\n\n            const response = await rp({\n                method: 'POST',\n                uri: url,\n                body: requestData,\n                json: true\n            });\n\n            console.log('微信API响应:', response);\n\n            if (response.errcode === 0) {\n                return response.url_link;\n            } else {\n                throw new Error(`微信API错误: ${response.errcode} - ${response.errmsg}`);\n            }\n\n        } catch (error) {\n            console.error('调用微信API失败:', error);\n            throw error;\n        }\n    }\n\n    /**\n     * 获取微信access_token\n     */\n    async getAccessToken() {\n        try {\n            const appid = think.config('weixin.appid');\n            const secret = think.config('weixin.secret');\n\n            if (!appid || !secret) {\n                throw new Error('微信配置不完整，请检查appid和secret');\n            }\n\n            // 检查缓存中是否有有效的access_token\n            const cacheKey = 'wechat_access_token';\n            let accessToken = await think.cache(cacheKey);\n\n            if (!accessToken) {\n                // 从微信API获取新的access_token\n                const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appid}&secret=${secret}`;\n\n                const response = await rp({\n                    method: 'GET',\n                    uri: url,\n                    json: true\n                });\n\n                if (response.access_token) {\n                    accessToken = response.access_token;\n                    // 缓存access_token，有效期7000秒（微信官方是7200秒，提前200秒刷新）\n                    await think.cache(cacheKey, accessToken, 7000);\n                    console.log('获取新的access_token成功');\n                } else {\n                    throw new Error(`获取access_token失败: ${response.errcode} - ${response.errmsg}`);\n                }\n            } else {\n                console.log('使用缓存的access_token');\n            }\n\n            return accessToken;\n\n        } catch (error) {\n            console.error('获取access_token失败:', error);\n            throw error;\n        }\n    }\n\n    /**\n     * 测试页面 - 用于微信开发者工具测试\n     * GET /api/redirect/test\n     */\n    async testAction() {\n        const html = `<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>URL Link 测试页面</title>\n    <style>\n        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }\n        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n        h1 { color: #333; text-align: center; margin-bottom: 30px; }\n        .form-group { margin-bottom: 20px; }\n        label { display: block; margin-bottom: 5px; font-weight: bold; color: #555; }\n        input, select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; }\n        button { background: #007bff; color: white; padding: 12px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 10px 5px; }\n        button:hover { background: #0056b3; }\n        .result { margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff; }\n        .link { word-break: break-all; color: #007bff; text-decoration: none; }\n        .link:hover { text-decoration: underline; }\n        .error { background: #f8d7da; border-left-color: #dc3545; color: #721c24; }\n        .success { background: #d4edda; border-left-color: #28a745; color: #155724; }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <h1>🔗 URL Link 测试工具</h1>\n\n        <div class=\"form-group\">\n            <label for=\"orderNumber\">订单号:</label>\n            <input type=\"text\" id=\"orderNumber\" placeholder=\"例如: TB202412301234\" value=\"TB202412301234\">\n        </div>\n\n        <div class=\"form-group\">\n            <label for=\"source\">来源:</label>\n            <select id=\"source\">\n                <option value=\"test\">测试</option>\n                <option value=\"wechat_message\">微信消息</option>\n                <option value=\"qr_code\">二维码</option>\n                <option value=\"sms\">短信</option>\n            </select>\n        </div>\n\n        <div class=\"form-group\">\n            <label for=\"utm\">UTM参数:</label>\n            <input type=\"text\" id=\"utm\" placeholder=\"例如: campaign1\" value=\"test_campaign\">\n        </div>\n\n        <div class=\"form-group\">\n            <label for=\"autoJump\">自动跳转:</label>\n            <select id=\"autoJump\">\n                <option value=\"false\">否</option>\n                <option value=\"true\">是</option>\n            </select>\n        </div>\n\n        <button onclick=\"generateUrlLink()\">生成 URL Link</button>\n        <button onclick=\"generateRedirectPage()\">生成重定向页面</button>\n        <button onclick=\"testDirectJump()\">直接测试跳转</button>\n\n        <div id=\"result\"></div>\n    </div>\n\n    <script>\n        // 生成URL Link\n        async function generateUrlLink() {\n            const params = getParams();\n            const url = '/api/redirect/generate-url-link?' + new URLSearchParams(params).toString();\n\n            try {\n                const response = await fetch(url);\n                const data = await response.json();\n\n                if (data.errno === 0) {\n                    showResult('success', '✅ URL Link 生成成功',\n                        '<strong>URL Link:</strong><br>' +\n                        '<a href=\"' + data.data.urlLink + '\" class=\"link\" target=\"_blank\">' + data.data.urlLink + '</a><br><br>' +\n                        '<strong>小程序路径:</strong> ' + data.data.path + '<br>' +\n                        '<strong>查询参数:</strong> ' + data.data.query\n                    );\n                } else {\n                    showResult('error', '❌ 生成失败', data.errmsg);\n                }\n            } catch (error) {\n                showResult('error', '❌ 请求失败', error.message);\n            }\n        }\n\n        // 生成重定向页面\n        function generateRedirectPage() {\n            const params = getParams();\n            const url = '/api/redirect/order-exchange?' + new URLSearchParams(params).toString();\n\n            showResult('success', '🔗 重定向页面链接',\n                '<a href=\"' + url + '\" class=\"link\" target=\"_blank\">' + url + '</a><br><br>' +\n                '<small>点击链接在新窗口中打开重定向页面</small>'\n            );\n        }\n\n        // 直接测试跳转\n        function testDirectJump() {\n            const params = getParams();\n            params.autoJump = 'true';\n            const url = '/api/redirect/order-exchange?' + new URLSearchParams(params).toString();\n\n            window.open(url, '_blank');\n        }\n\n        // 获取表单参数\n        function getParams() {\n            return {\n                orderNumber: document.getElementById('orderNumber').value,\n                source: document.getElementById('source').value,\n                utm: document.getElementById('utm').value,\n                autoJump: document.getElementById('autoJump').value\n            };\n        }\n\n        // 显示结果\n        function showResult(type, title, content) {\n            const resultDiv = document.getElementById('result');\n            resultDiv.className = 'result ' + type;\n            resultDiv.innerHTML = '<strong>' + title + '</strong><br><br>' + content;\n        }\n    </script>\n</body>\n</html>`;\n\n        this.header('Content-Type', 'text/html; charset=utf-8');\n        this.body = html;\n    }\n    /**\n     * 简单的订单兑换跳转测试页面\n     */\n    async testAction() {\n        const html = `<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>订单兑换测试</title>\n    <style>\n        body {\n            font-family: Arial, sans-serif;\n            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);\n            min-height: 100vh;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            margin: 0;\n            padding: 20px;\n        }\n        .container {\n            background: white;\n            border-radius: 20px;\n            padding: 40px;\n            text-align: center;\n            box-shadow: 0 10px 30px rgba(0,0,0,0.2);\n            max-width: 400px;\n            width: 100%;\n        }\n        .logo {\n            font-size: 60px;\n            margin-bottom: 20px;\n        }\n        h1 {\n            color: #333;\n            margin-bottom: 10px;\n        }\n        p {\n            color: #666;\n            margin-bottom: 30px;\n            line-height: 1.5;\n        }\n        .btn {\n            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);\n            color: white;\n            border: none;\n            padding: 15px 30px;\n            border-radius: 50px;\n            font-size: 18px;\n            font-weight: bold;\n            cursor: pointer;\n            width: 100%;\n            transition: all 0.3s ease;\n        }\n        .btn:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 5px 15px rgba(0,0,0,0.2);\n        }\n        .tips {\n            margin-top: 20px;\n            padding: 15px;\n            background: #f8f9fa;\n            border-radius: 10px;\n            font-size: 14px;\n            color: #666;\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"logo\">🎁</div>\n        <h1>订单兑换测试</h1>\n        <p>点击下方按钮跳转到小程序订单兑换页面</p>\n\n        <button class=\"btn\" onclick=\"jumpToMiniProgram()\">打开小程序</button>\n\n        <div class=\"tips\">\n            <strong>测试说明：</strong><br>\n            点击按钮会尝试打开小程序的订单兑换页面\n        </div>\n    </div>\n\n    <script>\n        function jumpToMiniProgram() {\n            // 固定的小程序跳转链接\n            const scheme = 'weixin://dl/business/?appid=wx919ca2ec612e6ecb&path=pages/order-exchange/index&env_version=release';\n\n            // 直接跳转\n            window.location.href = scheme;\n        }\n    </script>\n</body>\n</html>`;\n\n        this.header('Content-Type', 'text/html; charset=utf-8');\n        this.body = html;\n    }\n\n    /**\n     * 直接跳转到小程序（无中间页面，无参数）\n     * GET /api/redirect/direct\n     */\n    async directAction() {\n        try {\n            console.log('直接跳转到小程序订单有礼页面');\n\n            // 生成URL Link（优先使用）\n            let urlLink = null;\n            try {\n                const path = 'pages/order-exchange/index';\n                urlLink = await this.generateWechatUrlLink(path);\n                console.log('生成URL Link成功，直接重定向:', urlLink);\n\n                // 直接重定向到URL Link\n                this.redirect(urlLink);\n                return;\n\n            } catch (error) {\n                console.error('生成URL Link失败，使用URL Scheme:', error);\n            }\n\n            // 如果URL Link失败，使用URL Scheme\n            const appid = think.config('weixin.appid');\n            const scheme = `weixin://dl/business/?appid=${appid}&path=pages/order-exchange/index&env_version=develop`;\n\n            console.log('使用URL Scheme重定向:', scheme);\n            this.redirect(scheme);\n\n        } catch (error) {\n            console.error('直接跳转失败:', error);\n            return this.fail(500, '跳转失败: ' + error.message);\n        }\n    }\n\n    /**\n     * 订单兑换页面跳转（保留原有功能）\n     */\n    async orderExchangeAction() {\n        try {\n            // 获取参数\n            const orderNumber = this.get('orderNumber') || '';\n            const source = this.get('source') || 'web';\n            const utm = this.get('utm') || '';\n            const userId = this.get('userId') || '';\n            const autoJump = this.get('autoJump') || 'false'; // 是否自动跳转\n            \n            // 获取小程序配置\n            const appid = think.config('weixin.appid');\n            const env = this.get('env') || 'release'; // 支持开发环境\n\n            // 构建小程序跳转参数\n            const queryParams = [];\n            if (orderNumber) queryParams.push(`orderNumber=${encodeURIComponent(orderNumber)}`);\n            if (source) queryParams.push(`source=${encodeURIComponent(source)}`);\n            if (utm) queryParams.push(`utm=${encodeURIComponent(utm)}`);\n            if (userId) queryParams.push(`userId=${encodeURIComponent(userId)}`);\n            \n            const query = queryParams.join('&');\n            const encodedQuery = encodeURIComponent(query);\n            \n            // 生成URL Link（优先使用）\n            let urlLink = null;\n            try {\n                const path = `pages/order-exchange/index${query ? '?' + query : ''}`;\n                urlLink = await this.generateWechatUrlLink(path);\n                console.log('生成URL Link成功:', urlLink);\n            } catch (error) {\n                console.error('生成URL Link失败，使用URL Scheme作为备选:', error);\n            }\n\n            // 生成URL Scheme作为备选\n            const scheme = `weixin://dl/business/?appid=${appid}&path=pages/order-exchange/index&query=${encodedQuery}&env_version=develop`;\n\n            // 返回HTML页面\n            const html = this.generateRedirectHTML({\n                title: '订单有礼 - 美汐缘',\n                orderNumber,\n                urlLink: urlLink || scheme, // 优先使用URL Link\n                scheme,\n                appid,\n                query: encodedQuery,\n                autoJump\n            });\n            \n            this.header('Content-Type', 'text/html; charset=utf-8');\n            this.body = html;\n            \n        } catch (error) {\n            console.error('生成跳转页面错误:', error);\n            this.body = this.generateErrorHTML();\n        }\n    }\n\n    /**\n     * 生成跳转HTML页面\n     */\n    generateRedirectHTML(params) {\n        const { title, orderNumber, urlLink, scheme, appid, query, autoJump } = params;\n        \n        return `<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>${title}</title>\n    <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body {\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);\n            min-height: 100vh;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            padding: 20px;\n        }\n        .container {\n            background: white;\n            border-radius: 20px;\n            padding: 40px 30px;\n            text-align: center;\n            box-shadow: 0 20px 60px rgba(0,0,0,0.1);\n            max-width: 400px;\n            width: 100%;\n        }\n        .logo {\n            font-size: 60px;\n            margin-bottom: 20px;\n        }\n        h1 {\n            color: #333;\n            font-size: 28px;\n            margin-bottom: 10px;\n            font-weight: 700;\n        }\n        .subtitle {\n            color: #666;\n            font-size: 16px;\n            margin-bottom: 30px;\n            line-height: 1.5;\n        }\n        .order-info {\n            background: #f8f9fa;\n            padding: 15px;\n            border-radius: 10px;\n            margin-bottom: 30px;\n            font-size: 14px;\n            color: #333;\n        }\n        .btn {\n            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);\n            color: white;\n            border: none;\n            padding: 16px 40px;\n            border-radius: 50px;\n            font-size: 18px;\n            font-weight: 600;\n            cursor: pointer;\n            width: 100%;\n            margin-bottom: 15px;\n            transition: all 0.3s ease;\n        }\n        .btn:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 15px 30px rgba(255, 107, 107, 0.3);\n        }\n        .tips {\n            margin-top: 25px;\n            padding: 20px;\n            background: #f8f9fa;\n            border-radius: 16px;\n            font-size: 14px;\n            color: #666;\n            line-height: 1.6;\n            text-align: left;\n        }\n        .loading {\n            display: none;\n            margin: 20px 0;\n        }\n        .spinner {\n            width: 40px;\n            height: 40px;\n            border: 3px solid #f3f3f3;\n            border-top: 3px solid #ff6b6b;\n            border-radius: 50%;\n            animation: spin 1s linear infinite;\n            margin: 0 auto 10px;\n        }\n        @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"logo\">🎁</div>\n        <h1>订单有礼</h1>\n        <p class=\"subtitle\">点击下方按钮打开小程序<br>开始您的订单兑换之旅</p>\n        \n        ${orderNumber ? `<div class=\"order-info\">订单号: ${orderNumber}</div>` : ''}\n        \n        <button class=\"btn\" onclick=\"openMiniProgram()\">立即打开小程序</button>\n        \n        <div class=\"loading\" id=\"loading\">\n            <div class=\"spinner\"></div>\n            <p>正在跳转到小程序...</p>\n        </div>\n        \n        <div class=\"tips\">\n            <strong>💡 使用说明</strong><br>\n            • 点击按钮直接跳转到小程序<br>\n            • 如无法跳转，请确保在微信中打开<br>\n            • 支持输入订单号兑换积分\n        </div>\n    </div>\n\n    <script>\n        // 检测是否在微信中\n        function isWechat() {\n            return /micromessenger/i.test(navigator.userAgent);\n        }\n\n        // 打开小程序\n        function openMiniProgram() {\n            const loading = document.getElementById('loading');\n            loading.style.display = 'block';\n\n            const urlLink = '${urlLink}';\n            const scheme = '${scheme}';\n\n            if (isWechat()) {\n                // 优先使用URL Link，如果不存在则使用URL Scheme\n                const jumpUrl = urlLink && urlLink !== 'undefined' ? urlLink : scheme;\n                console.log('跳转链接:', jumpUrl);\n                window.location.href = jumpUrl;\n            } else {\n                // 不在微信中，提示用户\n                loading.style.display = 'none';\n                alert('请在微信中打开此链接');\n            }\n        }\n\n        // 页面加载完成后自动尝试跳转\n        window.onload = function() {\n            console.log('页面加载完成');\n\n            const autoJump = '${autoJump}';\n\n            if (autoJump === 'true' && isWechat()) {\n                // 自动跳转（延迟1秒让用户看到页面）\n                setTimeout(openMiniProgram, 1000);\n            }\n        };\n    </script>\n</body>\n</html>`;\n    }\n\n    /**\n     * 生成错误页面\n     */\n    generateErrorHTML() {\n        return `<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>页面错误</title>\n    <style>\n        body {\n            font-family: Arial, sans-serif;\n            text-align: center;\n            padding: 50px;\n            background: #f5f5f5;\n        }\n        .error-container {\n            background: white;\n            padding: 40px;\n            border-radius: 10px;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n            max-width: 400px;\n            margin: 0 auto;\n        }\n        h1 { color: #e74c3c; }\n        p { color: #666; margin: 20px 0; }\n    </style>\n</head>\n<body>\n    <div class=\"error-container\">\n        <h1>😔 页面加载失败</h1>\n        <p>抱歉，页面暂时无法访问</p>\n        <p>请稍后重试或联系客服</p>\n    </div>\n</body>\n</html>`;\n    }\n};\n"]}