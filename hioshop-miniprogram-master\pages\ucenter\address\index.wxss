page {
    background: #fafafa;
    height: 100%;
}

.container {
    display: flex;
    align-items: stretch;
    overflow-x: hidden;
}

.has-info {
    display: flex;
    flex-direction: column;
    padding: 30rpx;
}

.has-info .info-item {
    padding: 30rpx 0;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background: #fff;
    border-radius: 10rpx;
    box-shadow: 2rpx 8rpx 18rpx #f1f1f1;
    margin-bottom: 24rpx;
    position: relative;
}

.has-info .info-item .edit-wrap{
    padding: 30rpx;
    position: absolute;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.has-info .info-item .edit-wrap .img{
    width: 42rpx;
    height: 42rpx;
}

.has-info .info-item .selected {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx 0 30rpx 30rpx;
}

.has-info .info-item .selected .img {
    width: 40rpx;
    height: 40rpx;
}

.has-info .info-item .info-wrap{
    display: flex;
    flex-direction: column;
    padding-left: 30rpx;
}

.has-info .info-item .info-wrap .addr {
    display: flex;
    flex-direction: column;
    margin-bottom: 10rpx;
}

.has-info .info-item .info-wrap .addr .top {
    font-size: 30rpx;
    font-weight: 500;
    margin-bottom: 4rpx;
}

.has-info .info-item .info-wrap .addr .text {
    font-size: 24rpx;
    color: #777;
}

.has-info .info-item .info-wrap .addr .img-wrap {
    width: 44rpx;
    height: 44rpx;
    margin-right: 10rpx;
}

.has-info .info-item .info-wrap .name {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.has-info .info-item .info-wrap .name .img-wrap {
    width: 44rpx;
    height: 44rpx;
    margin-right: 10rpx;
}

.has-info .info-item .info-wrap .name .img {
    width: 42rpx;
    height: 42rpx;
}

.has-info .info-item .info-wrap .name .default {
    font-size: 22rpx;
    color: #ff3456;
    background: #faedef;
    text-align: center;
    padding: 2rpx 10rpx;
    border-radius: 4rpx;
}

.has-info .info-item .info-wrap .name .text {
    font-size: 26rpx;
    margin-right: 10rpx;
}

.has-info .info-item .id-wrap {
    display: flex;
    flex-direction: column;
}

.has-info .info-item .id-wrap .ids {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 26rpx;
    color: #5d6777;
    margin-bottom: 8rpx;
}

.has-info .info-item .id-wrap .ids .id-name {
    margin-right: 20rpx;
}

.has-info .info-item .id-wrap .no-ids {
    font-size: 26rpx;
    color: #ffbb12;
}

.has-info .add-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;
    border-radius: 10rpx;
    box-shadow: 2rpx 8rpx 18rpx #f1f1f1;
    margin-bottom: 24rpx;
    padding: 30rpx 0;
}

.has-info .add-btn .icon-wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40rpx;
    height: 40rpx;
    margin-right: 8rpx;
}

.has-info .add-btn .icon-wrap .img {
    width: 40rpx;
    height: 40rpx;
}

.has-info .add-btn .text {
    font-size: 28rpx;
    color: #ffc310;
}

.no-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 300rpx;
}

.no-info .img {
    width: 100rpx;
    height: 100rpx;
    margin-bottom: 30rpx;
}

.no-info .text {
    font-size: 30rpx;
    color: #999;
    text-align: center;
    margin-bottom: 30rpx;
}

.no-info .add-btn {
    color: #192841;
    /* background: #ff3456; */
    background: linear-gradient(to right, #f8dd66, #fae277);
    border-radius: 100px;
    width: 400rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 28rpx;
    margin: 0 auto;
}



.btn-wrap {
    position: fixed;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.btn-wrap .btn {
    width: 100%;
    background: linear-gradient(to right, #ff3456, #ff347d);
    color: #fff;
    font-size: 28rpx;
    text-align: center;
    height: 110rpx;
    line-height: 100rpx;
}