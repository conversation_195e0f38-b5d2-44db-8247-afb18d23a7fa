const Base = require('./base.js');
const moment = require('moment');
module.exports = class extends Base {
    async indexAction() {
        const model = this.model('goods');
        const goodsList = await model.select();
        return this.success(goodsList);
    }
    /**
     * 商品详情页数据
     * @returns {Promise.<Promise|PreventPromise|void>}
     */
    async detailAction() {
        const goodsId = this.get('id');
		const userId = await this.getLoginUserId();
        const model = this.model('goods');
        let info = await model.where({
            id: goodsId,
			is_delete:0
        }).find();
		if(think.isEmpty(info)){
			return this.fail('该商品不存在或已下架');
		}
        const gallery = await this.model('goods_gallery').where({
            goods_id: goodsId,
            is_delete: 0,
        }).order('sort_order').limit(6).select();
        await this.model('footprint').addFootprint(userId, goodsId);
        let productList = await model.getProductList(goodsId);
        let goodsNumber = 0;
        for (const item of productList) {
            if (item.goods_number > 0) {
                goodsNumber = goodsNumber + item.goods_number;
            }
        }
        let specificationList = await model.getSpecificationList(goodsId);
        info.goods_number = goodsNumber;

        // 获取商品销量数据
        // 如果商品表中没有sell_volume字段，从订单表中统计
        if (!info.sell_volume) {
            try {
                const orderModel = this.model('order_goods');
                const sellVolumeResult = await orderModel
                    .where({
                        goods_id: goodsId
                    })
                    .sum('number');
                info.sell_volume = sellVolumeResult || 0;
            } catch (error) {
                console.log('获取销量失败，使用默认值:', error);
                info.sell_volume = 0;
            }
        }

        // 获取商品分销状态
        try {
            const distributionConfig = await this.model('goods_distribution')
                .where({ goods_id: goodsId })
                .find();

            if (distributionConfig) {
                info.is_distributed = distributionConfig.is_distributed;
                info.commission_rate = distributionConfig.commission_rate || 0;
                info.personal_rate = distributionConfig.personal_rate || 0;
                info.level1_rate = distributionConfig.level1_rate || 0;
                info.level2_rate = distributionConfig.level2_rate || 0;
                info.team_leader_rate = distributionConfig.team_leader_rate || 0;
            } else {
                info.is_distributed = null;
                info.commission_rate = 0;
                info.personal_rate = 0;
                info.level1_rate = 0;
                info.level2_rate = 0;
                info.team_leader_rate = 0;
            }
        } catch (error) {
            console.log('获取分销配置失败，使用默认值:', error);
            info.is_distributed = null;
            info.commission_rate = 0;
            info.personal_rate = 0;
            info.level1_rate = 0;
            info.level2_rate = 0;
            info.team_leader_rate = 0;
        }

        return this.success({
            info: info,
            gallery: gallery,
            specificationList: specificationList,
            productList: productList
        });
    }
    async goodsShareAction() {
        const goodsId = this.get('id');
        const info = await this.model('goods').where({
            id: goodsId
        }).field('name,retail_price').find();
        return this.success(info);
    }
    /**
     * 获取商品列表
     * @returns {Promise.<*>}
     */
    async listAction() {
		const userId = await this.getLoginUserId();
        const keyword = this.get('keyword');
        const sort = this.get('sort');
        const order = this.get('order');
        const sales = this.get('sales');
        const model = this.model('goods');
        const whereMap = {
            is_on_sale: 1,
            is_delete: 0,
        };
        if (!think.isEmpty(keyword)) {
            whereMap.name = ['like', `%${keyword}%`];
            // 添加到搜索历史
            await this.model('search_history').add({
                keyword: keyword,
                user_id: userId,
                add_time: parseInt(new Date().getTime() / 1000)
            });
            //    TODO 之后要做个判断，这个词在搜索记录中的次数，如果大于某个值，则将他存入keyword
        }
        // 排序
        let orderMap = {};
        if (sort === 'price') {
            // 按价格
            orderMap = {
                retail_price: order
            };
        } else if (sort === 'sales') {
            // 按价格
            orderMap = {
                sell_volume: sales
            };
        } else {
            // 按商品添加时间
            orderMap = {
                sort_order: 'asc'
            };
        }
        const goodsData = await model.where(whereMap).order(orderMap).select();
        return this.success(goodsData);
    }
    /**
     * 在售的商品总数
     * @returns {Promise.<Promise|PreventPromise|void>}
     */
    async countAction() {
        const goodsCount = await this.model('goods').where({
            is_delete: 0,
            is_on_sale: 1
        }).count('id');
        return this.success({
            goodsCount: goodsCount
        });
    }
};