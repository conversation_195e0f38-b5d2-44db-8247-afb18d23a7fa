{"version": 3, "sources": ["..\\..\\src\\common\\wangdian_sync.js"], "names": ["crypto", "require", "request", "wangdianConfig", "WangDianSync", "constructor", "ctx", "config", "current", "model", "name", "think", "generateSign", "params", "appsecret", "signParams", "key", "sortedParams", "Object", "keys", "sort", "map", "formattedParts", "value", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "byteLength", "valueStr", "String", "valueLength", "keyLengthStr", "toString", "padStart", "valueLengthStr", "formattedPart", "push", "paramString", "length", "slice", "join", "signString", "sign", "createHash", "update", "digest", "toLowerCase", "callApi", "endpoint", "businessParams", "enabled", "console", "log", "success", "message", "url", "baseUrl", "includes", "requestParams", "sid", "appkey", "timestamp", "Math", "floor", "Date", "now", "JSON", "stringify", "response", "method", "form", "json", "timeout", "error", "transformOrderData", "orderInfo", "orderGoods", "userInfo", "currentTime", "timeStr", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "buyerNick", "user_id", "nickname", "defaultNickname", "from", "wangdianOrder", "tid", "order_sn", "platform_id", "platformId", "trade_status", "pay_status", "delivery_term", "trade_time", "pay_time", "buyer_nick", "buyer_email", "receiver_name", "consignee", "receiver_province", "province", "receiver_city", "city", "receiver_district", "district", "receiver_address", "address", "receiver_mobile", "mobile", "receiver_telno", "pay_method", "logistics_type", "invoice_kind", "invoice_title", "invoice_content", "buyer_message", "postscript", "seller_memo", "remark_flag", "seller_flag", "post_amount", "parseFloat", "freight_price", "cod_amount", "ext_cod_fee", "other_amount", "paid", "actual_price", "warehouse_no", "warehouseNo", "order_list", "item", "index", "oid", "num", "parseInt", "number", "price", "retail_price", "status", "refund_status", "goods_id", "spec_id", "product_id", "goods_no", "goods_sn", "spec_no", "goods_name", "spec_name", "goods_specifition_name_value", "adjust_amount", "discount", "share_discount", "commission", "remark", "cid", "pushOrder", "where", "id", "find", "shop_no", "shopNo", "switch", "trade_list", "replace", "match", "charCodeAt", "substr", "result", "code", "new_count", "chg_count", "pushOrderRefund", "transformOrderDataForRefund", "tradeTimeStr", "payTimeStr", "payTime", "module", "exports"], "mappings": ";;AAAA;;;;AAIA,MAAMA,SAASC,QAAQ,QAAR,CAAf;AACA,MAAMC,UAAUD,QAAQ,iBAAR,CAAhB;AACA,MAAME,iBAAiBF,QAAQ,uBAAR,CAAvB;;AAEA,MAAMG,YAAN,CAAmB;AACfC,gBAAYC,MAAM,IAAlB,EAAwB;AACpB,aAAKC,MAAL,GAAcJ,eAAeA,eAAeK,OAA9B,CAAd;AACA,aAAKF,GAAL,GAAWA,GAAX;AACH;;AAED;;;;;AAKAG,UAAMC,IAAN,EAAY;AACR,YAAI,KAAKJ,GAAL,IAAY,KAAKA,GAAL,CAASG,KAAzB,EAAgC;AAC5B,mBAAO,KAAKH,GAAL,CAASG,KAAT,CAAeC,IAAf,CAAP;AACH;AACD;AACA,eAAOC,MAAMF,KAAN,CAAYC,IAAZ,CAAP;AACH;;AAED;;;;;;AAMAE,iBAAaC,MAAb,EAAqBC,SAArB,EAAgC;AAC5B;AACA,cAAMC,aAAa,EAAnB;AACA,aAAK,MAAMC,GAAX,IAAkBH,MAAlB,EAA0B;AACtB,gBAAIG,QAAQ,MAAZ,EAAoB;AAChBD,2BAAWC,GAAX,IAAkBH,OAAOG,GAAP,CAAlB;AACH;AACJ;;AAED;AACA,cAAMC,eAAeC,OAAOC,IAAP,CAAYJ,UAAZ,EAAwBK,IAAxB,GAA+BC,GAA/B,CAAmCL,OAAO,CAACA,GAAD,EAAMD,WAAWC,GAAX,CAAN,CAA1C,CAArB;;AAEA;AACA,cAAMM,iBAAiB,EAAvB;AACA,aAAK,MAAM,CAACN,GAAD,EAAMO,KAAN,CAAX,IAA2BN,YAA3B,EAAyC;AACrC;AACA,kBAAMO,YAAYC,OAAOC,UAAP,CAAkBV,GAAlB,EAAuB,MAAvB,CAAlB;AACA,kBAAMW,WAAWC,OAAOL,KAAP,CAAjB;AACA,kBAAMM,cAAcJ,OAAOC,UAAP,CAAkBC,QAAlB,EAA4B,MAA5B,CAApB;;AAEA;AACA,kBAAMG,eAAeN,UAAUO,QAAV,GAAqBC,QAArB,CAA8B,CAA9B,EAAiC,GAAjC,CAArB;AACA,kBAAMC,iBAAiBJ,eAAe,IAAf,GACnBA,YAAYE,QAAZ,GAAuBC,QAAvB,CAAgC,CAAhC,EAAmC,GAAnC,CADmB,GAEnBH,YAAYE,QAAZ,EAFJ;AAGA,kBAAMG,gBAAiB,GAAEJ,YAAa,IAAGd,GAAI,IAAGiB,cAAe,IAAGN,QAAS,EAA3E;AACAL,2BAAea,IAAf,CAAoBD,aAApB;AACH;;AAED;AACA,YAAIE,WAAJ;AACA,YAAId,eAAee,MAAf,GAAwB,CAA5B,EAA+B;AAC3BD,0BAAcd,eAAegB,KAAf,CAAqB,CAArB,EAAwB,CAAC,CAAzB,EAA4BC,IAA5B,CAAiC,GAAjC,IAAwC,GAAxC,GAA8CjB,eAAeA,eAAee,MAAf,GAAwB,CAAvC,CAA5D;AACH,SAFD,MAEO;AACHD,0BAAcd,eAAe,CAAf,KAAqB,EAAnC;AACH;;AAED;AACA,cAAMkB,aAAaJ,cAActB,SAAjC;AACA,cAAM2B,OAAOzC,OAAO0C,UAAP,CAAkB,KAAlB,EAAyBC,MAAzB,CAAgCH,UAAhC,EAA4C,MAA5C,EAAoDI,MAApD,CAA2D,KAA3D,EAAkEC,WAAlE,EAAb;;AAEA,eAAOJ,IAAP;AACH;;AAED;;;;;;AAMMK,WAAN,CAAcC,QAAd,EAAwBC,cAAxB,EAAwC;AAAA;;AAAA;AACpC,gBAAI,CAAC,MAAKzC,MAAL,CAAY0C,OAAjB,EAA0B;AACtBC,wBAAQC,GAAR,CAAY,uBAAZ;AACA,uBAAO,EAAEC,SAAS,KAAX,EAAkBC,SAAS,OAA3B,EAAP;AACH;;AAED;AACA,kBAAMC,MAAM,MAAK/C,MAAL,CAAYgD,OAAZ,CAAoBC,QAApB,CAA6B,gBAA7B,IACR,MAAKjD,MAAL,CAAYgD,OADJ,GAEP,GAAE,MAAKhD,MAAL,CAAYgD,OAAQ,IAAGR,QAAS,EAFvC;;AAIA;AACA,kBAAMU;AACFC,qBAAK,MAAKnD,MAAL,CAAYmD,GADf;AAEFC,wBAAQ,MAAKpD,MAAL,CAAYoD,MAFlB;AAGFC,2BAAWC,KAAKC,KAAL,CAAWC,KAAKC,GAAL,KAAa,IAAxB,EAA8BjC,QAA9B;AAHT,eAICiB,cAJD,CAAN;;AAOA;AACAS,0BAAchB,IAAd,GAAqB,MAAK7B,YAAL,CAAkB6C,aAAlB,EAAiC,MAAKlD,MAAL,CAAYO,SAA7C,CAArB;;AAEAoC,oBAAQC,GAAR,CAAa,kBAAiBG,GAAI,EAAlC;AACAJ,oBAAQC,GAAR,CAAa,eAAb,EAA6Bc,KAAKC,SAAL,CAAeT,aAAf,EAA8B,IAA9B,EAAoC,CAApC,CAA7B;;AAEA,gBAAI;AACA,sBAAMU,WAAW,MAAMjE,QAAQ;AAC3BkE,4BAAQ,MADmB;AAE3Bd,yBAAKA,GAFsB;AAG3Be,0BAAMZ,aAHqB;AAI3Ba,0BAAM,IAJqB;AAK3BC,6BAAS;AALkB,iBAAR,CAAvB;;AAQArB,wBAAQC,GAAR,CAAa,gBAAb,EAA8Bc,KAAKC,SAAL,CAAeC,QAAf,EAAyB,IAAzB,EAA+B,CAA/B,CAA9B;AACA,uBAAOA,QAAP;AACH,aAXD,CAWE,OAAOK,KAAP,EAAc;AACZtB,wBAAQsB,KAAR,CAAe,kBAAf,EAAkCA,MAAMnB,OAAxC;AACA,sBAAMmB,KAAN;AACH;AAvCmC;AAwCvC;;AAED;;;;;;;AAOAC,uBAAmBC,SAAnB,EAA8BC,UAA9B,EAA0CC,WAAW,IAArD,EAA2D;AACvD,cAAMC,cAAc,IAAId,IAAJ,EAApB;AACA;AACA,cAAMe,UAAUD,YAAYE,WAAZ,KAA4B,GAA5B,GACZnD,OAAOiD,YAAYG,QAAZ,KAAyB,CAAhC,EAAmChD,QAAnC,CAA4C,CAA5C,EAA+C,GAA/C,CADY,GAC0C,GAD1C,GAEZJ,OAAOiD,YAAYI,OAAZ,EAAP,EAA8BjD,QAA9B,CAAuC,CAAvC,EAA0C,GAA1C,CAFY,GAEqC,GAFrC,GAGZJ,OAAOiD,YAAYK,QAAZ,EAAP,EAA+BlD,QAA/B,CAAwC,CAAxC,EAA2C,GAA3C,CAHY,GAGsC,GAHtC,GAIZJ,OAAOiD,YAAYM,UAAZ,EAAP,EAAiCnD,QAAjC,CAA0C,CAA1C,EAA6C,GAA7C,CAJY,GAIwC,GAJxC,GAKZJ,OAAOiD,YAAYO,UAAZ,EAAP,EAAiCpD,QAAjC,CAA0C,CAA1C,EAA6C,GAA7C,CALJ;;AAOA;AACA,YAAIqD,YAAY,UAAUX,UAAUY,OAApC,CAXuD,CAWV;AAC7C,YAAIV,YAAYA,SAASW,QAAzB,EAAmC;AAC/B;AACAF,wBAAYT,SAASW,QAArB;AACH,SAHD,MAGO;AACH;AACA,kBAAMC,kBAAkB/D,OAAOgE,IAAP,CAAY,MAAZ,EAAoB1D,QAApB,CAA6B,QAA7B,CAAxB;AACAsD,wBAAYG,eAAZ;AACH;;AAED,cAAME,gBAAgB;AAClBC,iBAAKjB,UAAUkB,QADG;AAElBC,yBAAa,KAAKtF,MAAL,CAAYuF,UAFP;AAGlBC,0BAAc,EAHI,EAGC;AACnBC,wBAAY,CAJM,EAIC;AACnBC,2BAAe,CALG,EAKC;AACnBC,wBAAYpB,OANM;AAOlBqB,sBAAUrB,OAPQ;AAQlBsB,wBAAYf,SARM;AASlBgB,yBAAa,EATK;AAUlBC,2BAAe5B,UAAU6B,SAVP;AAWlBC,+BAAmB9B,UAAU+B,QAXX;AAYlBC,2BAAehC,UAAUiC,IAZP;AAalBC,+BAAmBlC,UAAUmC,QAbX;AAclBC,8BAAkBpC,UAAUqC,OAdV;AAelBC,6BAAiBtC,UAAUuC,MAfT;AAgBlBC,4BAAgB,EAhBE;AAiBlBC,wBAAY,CAjBM,EAiBC;AACnBC,4BAAgB,CAlBE;AAmBlBC,0BAAc,CAnBI;AAoBlBC,2BAAe,EApBG;AAqBlBC,6BAAiB,EArBC;AAsBlBC,2BAAe9C,UAAU+C,UAAV,IAAwB,EAtBrB;AAuBlBC,yBAAc,QAAOhD,UAAUkB,QAAS,EAvBtB;AAwBlB+B,yBAAa,CAxBK;AAyBlBC,yBAAa,CAzBK;AA0BlBC,yBAAaC,WAAWpD,UAAUqD,aAAV,IAA2B,CAAtC,CA1BK;AA2BlBC,wBAAY,CA3BM;AA4BlBC,yBAAa,CA5BK;AA6BlBC,0BAAc,CA7BI;AA8BlBC,kBAAML,WAAWpD,UAAU0D,YAArB,CA9BY;AA+BlBC,0BAAc,KAAK9H,MAAL,CAAY+H,WA/BR;AAgClBC,wBAAY5D,WAAWtD,GAAX,CAAe,CAACmH,IAAD,EAAOC,KAAP,MAAkB;AACzCC,qBAAM,GAAEhE,UAAUkB,QAAS,IAAG6C,QAAQ,CAAE,EADC;AAEzCE,qBAAKC,SAASJ,KAAKK,MAAd,CAFoC;AAGzCC,uBAAOhB,WAAWU,KAAKO,YAAhB,CAHkC;AAIzCC,wBAAQ,EAJiC,EAI1B;AACfC,+BAAe,CAL0B;AAMzCC,0BAAUV,KAAKU,QAN0B;AAOzCC,yBAASX,KAAKY,UAP2B;AAQzCC,0BAAUb,KAAKc,QAAL,IAAiBd,KAAKU,QAAL,CAAcnH,QAAd,EARc;AASzCwH,yBAASf,KAAKY,UAAL,CAAgBrH,QAAhB,EATgC;AAUzCyH,4BAAYhB,KAAKgB,UAVwB;AAWzCC,2BAAWjB,KAAKkB,4BAAL,IAAqC,MAXP;AAYzCC,+BAAe,CAZ0B;AAazCC,0BAAU,CAb+B;AAczCC,gCAAgB,CAdyB;AAezCC,4BAAY,CAf6B;AAgBzCC,wBAAQ,EAhBiC;AAiBzCC,qBAAK;AAjBoC,aAAlB,CAAf;AAhCM,SAAtB;;AAqDA,eAAOtE,aAAP;AACH;;AAED;;;;;;AAMMuE,aAAN,CAAgBvF,SAAhB,EAA2BC,UAA3B,EAAuC;AAAA;;AAAA;AACnC,gBAAI;AACAzB,wBAAQC,GAAR,CAAa,mBAAkBuB,UAAUkB,QAAS,EAAlD;;AAEA;AACA,oBAAIhB,WAAW,IAAf;AACA,oBAAI;AACAA,+BAAW,MAAM,OAAKnE,KAAL,CAAW,MAAX,EAAmByJ,KAAnB,CAAyB,EAAEC,IAAIzF,UAAUY,OAAhB,EAAzB,EAAoD8E,IAApD,EAAjB;AACAlH,4BAAQC,GAAR,CAAa,0BAAyBuB,UAAUY,OAAQ,EAAxD;AACH,iBAHD,CAGE,OAAOd,KAAP,EAAc;AACZtB,4BAAQC,GAAR,CAAa,qBAAoBqB,MAAMnB,OAAQ,EAA/C;AACH;;AAED;AACA,sBAAMqC,gBAAgB,OAAKjB,kBAAL,CAAwBC,SAAxB,EAAmCC,UAAnC,EAA+CC,QAA/C,CAAtB;;AAEA;AACA,sBAAM/D,SAAS;AACXwJ,6BAAS,OAAK9J,MAAL,CAAY+J,MADV;AAEXC,4BAAQ,GAFG,EAEG;AACdC,gCAAYvG,KAAKC,SAAL,CAAe,CAACwB,aAAD,CAAf,EAAgC,IAAhC,EAAsC,CAAtC,EAAyC+E,OAAzC,CAAiD,kBAAjD,EAAqE,UAASC,KAAT,EAAgB;AAC7F,+BAAO,QAAQ,CAAC,SAASA,MAAMC,UAAN,CAAiB,CAAjB,EAAoB5I,QAApB,CAA6B,EAA7B,CAAV,EAA4C6I,MAA5C,CAAmD,CAAC,CAApD,CAAf;AACH,qBAFW;AAHD,iBAAf;;AAQA;AACA,sBAAMC,SAAS,MAAM,OAAK/H,OAAL,CAAa,gBAAb,EAA+BjC,MAA/B,CAArB;;AAEA;AACA,oBAAIgK,UAAUA,OAAOC,IAAP,KAAgB,CAA9B,EAAiC;AAC7B5H,4BAAQC,GAAR,CAAa,mBAAkBuB,UAAUkB,QAAS,EAAlD;AACA1C,4BAAQC,GAAR,CAAa,kBAAiB0H,OAAOE,SAAP,IAAoB,CAAE,EAApD;AACA7H,4BAAQC,GAAR,CAAa,kBAAiB0H,OAAOG,SAAP,IAAoB,CAAE,EAApD;AACA,2BAAO,EAAE5H,SAAS,IAAX,EAAiBC,SAAS,QAA1B,EAAoCwH,MAApC,EAAP;AACH,iBALD,MAKO;AACH,0BAAMxH,UAAUwH,SAASA,OAAOxH,OAAhB,GAA0B,MAA1C;AACAH,4BAAQsB,KAAR,CAAe,mBAAkBE,UAAUkB,QAAS,SAAQvC,OAAQ,EAApE;AACA,2BAAO,EAAED,SAAS,KAAX,EAAkBC,OAAlB,EAA2BwH,MAA3B,EAAP;AACH;AACJ,aAtCD,CAsCE,OAAOrG,KAAP,EAAc;AACZtB,wBAAQsB,KAAR,CAAe,mBAAkBE,UAAUkB,QAAS,EAApD,EAAuDpB,KAAvD;AACA,uBAAO,EAAEpB,SAAS,KAAX,EAAkBC,SAASmB,MAAMnB,OAAjC,EAA0CmB,KAA1C,EAAP;AACH;AA1CkC;AA2CtC;;AAED;;;;;;AAMMyG,mBAAN,CAAsBvG,SAAtB,EAAiCC,UAAjC,EAA6C;AAAA;;AAAA;AACzC,gBAAI;AACAzB,wBAAQC,GAAR,CAAa,uBAAsBuB,UAAUkB,QAAS,EAAtD;;AAEA;AACA,oBAAIhB,WAAW,IAAf;AACA,oBAAI;AACAA,+BAAW,MAAM,OAAKnE,KAAL,CAAW,MAAX,EAAmByJ,KAAnB,CAAyB,EAAEC,IAAIzF,UAAUY,OAAhB,EAAzB,EAAoD8E,IAApD,EAAjB;AACAlH,4BAAQC,GAAR,CAAa,0BAAyBuB,UAAUY,OAAQ,EAAxD;AACH,iBAHD,CAGE,OAAOd,KAAP,EAAc;AACZtB,4BAAQC,GAAR,CAAa,qBAAoBqB,MAAMnB,OAAQ,EAA/C;AACH;;AAED;AACA,sBAAMqC,gBAAgB,OAAKwF,2BAAL,CAAiCxG,SAAjC,EAA4CC,UAA5C,EAAwDC,QAAxD,CAAtB;;AAEA;AACA,sBAAM/D,SAAS;AACXwJ,6BAAS,OAAK9J,MAAL,CAAY+J,MADV;AAEXC,4BAAQ,GAFG,EAEG;AACdC,gCAAYvG,KAAKC,SAAL,CAAe,CAACwB,aAAD,CAAf,EAAgC,IAAhC,EAAsC,CAAtC,EAAyC+E,OAAzC,CAAiD,kBAAjD,EAAqE,UAASC,KAAT,EAAgB;AAC7F,+BAAO,QAAQ,CAAC,SAASA,MAAMC,UAAN,CAAiB,CAAjB,EAAoB5I,QAApB,CAA6B,EAA7B,CAAV,EAA4C6I,MAA5C,CAAmD,CAAC,CAApD,CAAf;AACH,qBAFW;AAHD,iBAAf;;AAQA;AACA,sBAAMC,SAAS,MAAM,OAAK/H,OAAL,CAAa,gBAAb,EAA+BjC,MAA/B,CAArB;;AAEA;AACA,oBAAIgK,UAAUA,OAAOC,IAAP,KAAgB,CAA9B,EAAiC;AAC7B5H,4BAAQC,GAAR,CAAa,uBAAsBuB,UAAUkB,QAAS,EAAtD;AACA1C,4BAAQC,GAAR,CAAa,kBAAiB0H,OAAOE,SAAP,IAAoB,CAAE,EAApD;AACA7H,4BAAQC,GAAR,CAAa,kBAAiB0H,OAAOG,SAAP,IAAoB,CAAE,EAApD;AACA,2BAAO,EAAE5H,SAAS,IAAX,EAAiBC,SAAS,YAA1B,EAAwCwH,MAAxC,EAAP;AACH,iBALD,MAKO;AACH,0BAAMxH,UAAUwH,SAASA,OAAOxH,OAAhB,GAA0B,MAA1C;AACAH,4BAAQsB,KAAR,CAAe,uBAAsBE,UAAUkB,QAAS,SAAQvC,OAAQ,EAAxE;AACA,2BAAO,EAAED,SAAS,KAAX,EAAkBC,OAAlB,EAA2BwH,MAA3B,EAAP;AACH;AACJ,aAtCD,CAsCE,OAAOrG,KAAP,EAAc;AACZtB,wBAAQsB,KAAR,CAAe,uBAAsBE,UAAUkB,QAAS,EAAxD,EAA2DpB,KAA3D;AACA,uBAAO,EAAEpB,SAAS,KAAX,EAAkBC,SAASmB,MAAMnB,OAAjC,EAA0CmB,KAA1C,EAAP;AACH;AA1CwC;AA2C5C;;AAED;;;;;;;AAOA0G,gCAA4BxG,SAA5B,EAAuCC,UAAvC,EAAmDC,WAAW,IAA9D,EAAoE;AAChE;AACA,YAAIuG,eAAe,EAAnB;AACA,YAAIC,aAAa,EAAjB;;AAEA,YAAI1G,UAAUyB,QAAd,EAAwB;AACpB;AACA,kBAAMkF,UAAU,IAAItH,IAAJ,CAASW,UAAUyB,QAAV,GAAqB,IAA9B,CAAhB,CAFoB,CAEiC;AACrDgF,2BAAeE,QAAQtG,WAAR,KAAwB,GAAxB,GACXnD,OAAOyJ,QAAQrG,QAAR,KAAqB,CAA5B,EAA+BhD,QAA/B,CAAwC,CAAxC,EAA2C,GAA3C,CADW,GACuC,GADvC,GAEXJ,OAAOyJ,QAAQpG,OAAR,EAAP,EAA0BjD,QAA1B,CAAmC,CAAnC,EAAsC,GAAtC,CAFW,GAEkC,GAFlC,GAGXJ,OAAOyJ,QAAQnG,QAAR,EAAP,EAA2BlD,QAA3B,CAAoC,CAApC,EAAuC,GAAvC,CAHW,GAGmC,GAHnC,GAIXJ,OAAOyJ,QAAQlG,UAAR,EAAP,EAA6BnD,QAA7B,CAAsC,CAAtC,EAAyC,GAAzC,CAJW,GAIqC,GAJrC,GAKXJ,OAAOyJ,QAAQjG,UAAR,EAAP,EAA6BpD,QAA7B,CAAsC,CAAtC,EAAyC,GAAzC,CALJ;AAMAoJ,yBAAaD,YAAb;AACH,SAVD,MAUO;AACH;AACA,kBAAMtG,cAAc,IAAId,IAAJ,EAApB;AACAoH,2BAAetG,YAAYE,WAAZ,KAA4B,GAA5B,GACXnD,OAAOiD,YAAYG,QAAZ,KAAyB,CAAhC,EAAmChD,QAAnC,CAA4C,CAA5C,EAA+C,GAA/C,CADW,GAC2C,GAD3C,GAEXJ,OAAOiD,YAAYI,OAAZ,EAAP,EAA8BjD,QAA9B,CAAuC,CAAvC,EAA0C,GAA1C,CAFW,GAEsC,GAFtC,GAGXJ,OAAOiD,YAAYK,QAAZ,EAAP,EAA+BlD,QAA/B,CAAwC,CAAxC,EAA2C,GAA3C,CAHW,GAGuC,GAHvC,GAIXJ,OAAOiD,YAAYM,UAAZ,EAAP,EAAiCnD,QAAjC,CAA0C,CAA1C,EAA6C,GAA7C,CAJW,GAIyC,GAJzC,GAKXJ,OAAOiD,YAAYO,UAAZ,EAAP,EAAiCpD,QAAjC,CAA0C,CAA1C,EAA6C,GAA7C,CALJ;AAMAoJ,yBAAaD,YAAb;AACH;;AAED;AACA,YAAI9F,YAAY,UAAUX,UAAUY,OAApC,CA5BgE,CA4BnB;AAC7C,YAAIV,YAAYA,SAASW,QAAzB,EAAmC;AAC/B;AACAF,wBAAYT,SAASW,QAArB;AACH,SAHD,MAGO;AACH;AACA,kBAAMC,kBAAkB/D,OAAOgE,IAAP,CAAY,MAAZ,EAAoB1D,QAApB,CAA6B,QAA7B,CAAxB;AACAsD,wBAAYG,eAAZ;AACH;;AAED,cAAME,gBAAgB;AAClBC,iBAAKjB,UAAUkB,QADG;AAElBC,yBAAa,KAAKtF,MAAL,CAAYuF,UAFP;AAGlBC,0BAAc,EAHI,EAGC;AACnBC,wBAAY,CAJM,EAIC;AACnBC,2BAAe,CALG,EAKC;AACnBC,wBAAYiF,YANM,EAMQ;AAC1BhF,sBAAUiF,UAPQ,EAOQ;AAC1BhF,wBAAYf,SARM;AASlBgB,yBAAa,EATK;AAUlBC,2BAAe5B,UAAU6B,SAVP;AAWlBC,+BAAmB9B,UAAU+B,QAXX;AAYlBC,2BAAehC,UAAUiC,IAZP;AAalBC,+BAAmBlC,UAAUmC,QAbX;AAclBC,8BAAkBpC,UAAUqC,OAdV;AAelBC,6BAAiBtC,UAAUuC,MAfT;AAgBlBC,4BAAgB,EAhBE;AAiBlBC,wBAAY,CAjBM,EAiBC;AACnBC,4BAAgB,CAlBE;AAmBlBC,0BAAc,CAnBI;AAoBlBC,2BAAe,EApBG;AAqBlBC,6BAAiB,EArBC;AAsBlBC,2BAAe9C,UAAU+C,UAAV,IAAwB,EAtBrB;AAuBlBC,yBAAc,QAAOhD,UAAUkB,QAAS,MAvBtB;AAwBlB+B,yBAAa,CAxBK;AAyBlBC,yBAAa,CAzBK;AA0BlBC,yBAAaC,WAAWpD,UAAUqD,aAAV,IAA2B,CAAtC,CA1BK;AA2BlBC,wBAAY,CA3BM;AA4BlBC,yBAAa,CA5BK;AA6BlBC,0BAAc,CA7BI;AA8BlBC,kBAAML,WAAWpD,UAAU0D,YAArB,CA9BY,EA8BwB;AAC1CC,0BAAc,KAAK9H,MAAL,CAAY+H,WA/BR;AAgClBC,wBAAY5D,WAAWtD,GAAX,CAAe,CAACmH,IAAD,EAAOC,KAAP,MAAkB;AACzCC,qBAAM,GAAEhE,UAAUkB,QAAS,IAAG6C,QAAQ,CAAE,EADC;AAEzCE,qBAAKC,SAASJ,KAAKK,MAAd,CAFoC;AAGzCC,uBAAOhB,WAAWU,KAAKO,YAAhB,CAHkC;AAIzCC,wBAAQ,EAJiC,EAI1B;AACfC,+BAAe,CAL0B,EAKvB;AAClBC,0BAAUV,KAAKU,QAN0B;AAOzCC,yBAASX,KAAKY,UAP2B;AAQzCC,0BAAUb,KAAKc,QAAL,IAAiBd,KAAKU,QAAL,CAAcnH,QAAd,EARc;AASzCwH,yBAASf,KAAKY,UAAL,CAAgBrH,QAAhB,EATgC;AAUzCyH,4BAAYhB,KAAKgB,UAVwB;AAWzCC,2BAAWjB,KAAKkB,4BAAL,IAAqC,MAXP;AAYzCC,+BAAe,CAZ0B;AAazCC,0BAAU,CAb+B;AAczCC,gCAAgB,CAdyB;AAezCC,4BAAY,CAf6B;AAgBzCC,wBAAQ,EAhBiC;AAiBzCC,qBAAK;AAjBoC,aAAlB,CAAf;AAhCM,SAAtB;;AAqDA,eAAOtE,aAAP;AACH;AAlZc;;AAqZnB4F,OAAOC,OAAP,GAAiBnL,YAAjB", "file": "..\\..\\src\\common\\wangdian_sync.js", "sourcesContent": ["/**\n * 旺店通ERP同步服务\n */\n\nconst crypto = require('crypto');\nconst request = require('request-promise');\nconst wangdianConfig = require('../config/wangdian.js');\n\nclass WangDianSync {\n    constructor(ctx = null) {\n        this.config = wangdianConfig[wangdianConfig.current];\n        this.ctx = ctx;\n    }\n\n    /**\n     * 获取模型实例\n     * @param {String} name 模型名称\n     * @returns {Object} 模型实例\n     */\n    model(name) {\n        if (this.ctx && this.ctx.model) {\n            return this.ctx.model(name);\n        }\n        // 如果没有上下文，使用think.model\n        return think.model(name);\n    }\n\n    /**\n     * 生成旺店通API签名\n     * @param {Object} params 参数对象\n     * @param {String} appsecret 密钥\n     * @returns {String} 签名\n     */\n    generateSign(params, appsecret) {\n        // 移除sign参数（如果存在）\n        const signParams = {};\n        for (const key in params) {\n            if (key !== 'sign') {\n                signParams[key] = params[key];\n            }\n        }\n\n        // 按key进行字典序排序\n        const sortedParams = Object.keys(signParams).sort().map(key => [key, signParams[key]]);\n\n        // 按照旺店通格式处理每个参数\n        const formattedParts = [];\n        for (const [key, value] of sortedParams) {\n            // 计算键名和值的长度\n            const keyLength = Buffer.byteLength(key, 'utf8');\n            const valueStr = String(value);\n            const valueLength = Buffer.byteLength(valueStr, 'utf8');\n\n            // 格式化：键长度-键名:值长度-值\n            const keyLengthStr = keyLength.toString().padStart(2, '0');\n            const valueLengthStr = valueLength <= 9999 ? \n                valueLength.toString().padStart(4, '0') : \n                valueLength.toString();\n            const formattedPart = `${keyLengthStr}-${key}:${valueLengthStr}-${valueStr}`;\n            formattedParts.push(formattedPart);\n        }\n\n        // 用分号连接，最后一个参数不加分号\n        let paramString;\n        if (formattedParts.length > 1) {\n            paramString = formattedParts.slice(0, -1).join(';') + ';' + formattedParts[formattedParts.length - 1];\n        } else {\n            paramString = formattedParts[0] || '';\n        }\n\n        // 拼接appsecret并MD5加密\n        const signString = paramString + appsecret;\n        const sign = crypto.createHash('md5').update(signString, 'utf8').digest('hex').toLowerCase();\n\n        return sign;\n    }\n\n    /**\n     * 调用旺店通API\n     * @param {String} endpoint API端点\n     * @param {Object} businessParams 业务参数\n     * @returns {Promise} API响应\n     */\n    async callApi(endpoint, businessParams) {\n        if (!this.config.enabled) {\n            console.log('[旺店通同步] 功能已禁用，跳过API调用');\n            return { success: false, message: '功能已禁用' };\n        }\n\n        // 如果baseUrl已包含完整路径，直接使用；否则拼接endpoint\n        const url = this.config.baseUrl.includes('trade_push.php') ?\n            this.config.baseUrl :\n            `${this.config.baseUrl}/${endpoint}`;\n        \n        // 添加基础参数\n        const requestParams = {\n            sid: this.config.sid,\n            appkey: this.config.appkey,\n            timestamp: Math.floor(Date.now() / 1000).toString(),\n            ...businessParams\n        };\n\n        // 生成签名\n        requestParams.sign = this.generateSign(requestParams, this.config.appsecret);\n\n        console.log(`[旺店通同步] 请求URL: ${url}`);\n        console.log(`[旺店通同步] 请求参数:`, JSON.stringify(requestParams, null, 2));\n\n        try {\n            const response = await request({\n                method: 'POST',\n                url: url,\n                form: requestParams,\n                json: true,\n                timeout: 30000\n            });\n\n            console.log(`[旺店通同步] API响应:`, JSON.stringify(response, null, 2));\n            return response;\n        } catch (error) {\n            console.error(`[旺店通同步] API调用失败:`, error.message);\n            throw error;\n        }\n    }\n\n    /**\n     * 转换订单数据为旺店通格式\n     * @param {Object} orderInfo 订单信息\n     * @param {Array} orderGoods 订单商品列表\n     * @param {Object} userInfo 用户信息（包含昵称）\n     * @returns {Object} 旺店通订单格式\n     */\n    transformOrderData(orderInfo, orderGoods, userInfo = null) {\n        const currentTime = new Date();\n        // 使用系统本地时间，而不是UTC时间\n        const timeStr = currentTime.getFullYear() + '-' +\n            String(currentTime.getMonth() + 1).padStart(2, '0') + '-' +\n            String(currentTime.getDate()).padStart(2, '0') + ' ' +\n            String(currentTime.getHours()).padStart(2, '0') + ':' +\n            String(currentTime.getMinutes()).padStart(2, '0') + ':' +\n            String(currentTime.getSeconds()).padStart(2, '0');\n\n        // 处理买家昵称：使用小程序客户昵称base64编码，如果没有昵称则使用\"微信用户\"的base64\n        let buyerNick = 'user_' + orderInfo.user_id; // 默认值\n        if (userInfo && userInfo.nickname) {\n            // 用户昵称已经是base64编码，直接使用\n            buyerNick = userInfo.nickname;\n        } else {\n            // 没有昵称，使用\"微信用户\"的base64编码\n            const defaultNickname = Buffer.from('微信用户').toString('base64');\n            buyerNick = defaultNickname;\n        }\n\n        const wangdianOrder = {\n            tid: orderInfo.order_sn,\n            platform_id: this.config.platformId,\n            trade_status: 30,  // 已付款待发货\n            pay_status: 2,     // 已付款\n            delivery_term: 1,  // 款到发货\n            trade_time: timeStr,\n            pay_time: timeStr,\n            buyer_nick: buyerNick,\n            buyer_email: '',\n            receiver_name: orderInfo.consignee,\n            receiver_province: orderInfo.province,\n            receiver_city: orderInfo.city,\n            receiver_district: orderInfo.district,\n            receiver_address: orderInfo.address,\n            receiver_mobile: orderInfo.mobile,\n            receiver_telno: '',\n            pay_method: 1,     // 在线支付\n            logistics_type: 1,\n            invoice_kind: 0,\n            invoice_title: '',\n            invoice_content: '',\n            buyer_message: orderInfo.postscript || '',\n            seller_memo: `商城订单-${orderInfo.order_sn}`,\n            remark_flag: 0,\n            seller_flag: 0,\n            post_amount: parseFloat(orderInfo.freight_price || 0),\n            cod_amount: 0,\n            ext_cod_fee: 0,\n            other_amount: 0,\n            paid: parseFloat(orderInfo.actual_price),\n            warehouse_no: this.config.warehouseNo,\n            order_list: orderGoods.map((item, index) => ({\n                oid: `${orderInfo.order_sn}_${index + 1}`,\n                num: parseInt(item.number),\n                price: parseFloat(item.retail_price),\n                status: 30,    // 已付款待发货\n                refund_status: 0,\n                goods_id: item.goods_id,\n                spec_id: item.product_id,\n                goods_no: item.goods_sn || item.goods_id.toString(),\n                spec_no: item.product_id.toString(),\n                goods_name: item.goods_name,\n                spec_name: item.goods_specifition_name_value || '默认规格',\n                adjust_amount: 0,\n                discount: 0,\n                share_discount: 0,\n                commission: 0,\n                remark: '',\n                cid: ''\n            }))\n        };\n\n        return wangdianOrder;\n    }\n\n    /**\n     * 推送订单到旺店通\n     * @param {Object} orderInfo 订单信息\n     * @param {Array} orderGoods 订单商品列表\n     * @returns {Promise} 推送结果\n     */\n    async pushOrder(orderInfo, orderGoods) {\n        try {\n            console.log(`[旺店通同步] 开始推送订单: ${orderInfo.order_sn}`);\n\n            // 获取用户信息（包含昵称）\n            let userInfo = null;\n            try {\n                userInfo = await this.model('user').where({ id: orderInfo.user_id }).find();\n                console.log(`[旺店通同步] 获取用户信息成功，用户ID: ${orderInfo.user_id}`);\n            } catch (error) {\n                console.log(`[旺店通同步] 获取用户信息失败: ${error.message}`);\n            }\n\n            // 转换订单数据\n            const wangdianOrder = this.transformOrderData(orderInfo, orderGoods, userInfo);\n\n            // 准备API参数\n            const params = {\n                shop_no: this.config.shopNo,\n                switch: '0',  // 0表示推送订单\n                trade_list: JSON.stringify([wangdianOrder], null, 0).replace(/[\\u0080-\\uFFFF]/g, function(match) {\n                    return '\\\\u' + ('0000' + match.charCodeAt(0).toString(16)).substr(-4);\n                })\n            };\n\n            // 调用API\n            const result = await this.callApi('trade_push.php', params);\n\n            // 判断结果\n            if (result && result.code === 0) {\n                console.log(`[旺店通同步] 订单推送成功: ${orderInfo.order_sn}`);\n                console.log(`[旺店通同步] 新增订单数: ${result.new_count || 0}`);\n                console.log(`[旺店通同步] 更新订单数: ${result.chg_count || 0}`);\n                return { success: true, message: '订单推送成功', result };\n            } else {\n                const message = result ? result.message : '未知错误';\n                console.error(`[旺店通同步] 订单推送失败: ${orderInfo.order_sn}, 错误: ${message}`);\n                return { success: false, message, result };\n            }\n        } catch (error) {\n            console.error(`[旺店通同步] 订单推送异常: ${orderInfo.order_sn}`, error);\n            return { success: false, message: error.message, error };\n        }\n    }\n\n    /**\n     * 推送订单退款状态到旺店通\n     * @param {Object} orderInfo 订单信息\n     * @param {Array} orderGoods 订单商品列表\n     * @returns {Promise} 推送结果\n     */\n    async pushOrderRefund(orderInfo, orderGoods) {\n        try {\n            console.log(`[旺店通同步] 开始推送订单退款状态: ${orderInfo.order_sn}`);\n\n            // 获取用户信息（包含昵称）\n            let userInfo = null;\n            try {\n                userInfo = await this.model('user').where({ id: orderInfo.user_id }).find();\n                console.log(`[旺店通同步] 获取用户信息成功，用户ID: ${orderInfo.user_id}`);\n            } catch (error) {\n                console.log(`[旺店通同步] 获取用户信息失败: ${error.message}`);\n            }\n\n            // 转换订单数据为退款状态\n            const wangdianOrder = this.transformOrderDataForRefund(orderInfo, orderGoods, userInfo);\n\n            // 准备API参数\n            const params = {\n                shop_no: this.config.shopNo,\n                switch: '0',  // 0表示推送订单（包括状态更新）\n                trade_list: JSON.stringify([wangdianOrder], null, 0).replace(/[\\u0080-\\uFFFF]/g, function(match) {\n                    return '\\\\u' + ('0000' + match.charCodeAt(0).toString(16)).substr(-4);\n                })\n            };\n\n            // 调用API\n            const result = await this.callApi('trade_push.php', params);\n\n            // 判断结果\n            if (result && result.code === 0) {\n                console.log(`[旺店通同步] 订单退款状态推送成功: ${orderInfo.order_sn}`);\n                console.log(`[旺店通同步] 新增订单数: ${result.new_count || 0}`);\n                console.log(`[旺店通同步] 更新订单数: ${result.chg_count || 0}`);\n                return { success: true, message: '订单退款状态推送成功', result };\n            } else {\n                const message = result ? result.message : '未知错误';\n                console.error(`[旺店通同步] 订单退款状态推送失败: ${orderInfo.order_sn}, 错误: ${message}`);\n                return { success: false, message, result };\n            }\n        } catch (error) {\n            console.error(`[旺店通同步] 订单退款状态推送异常: ${orderInfo.order_sn}`, error);\n            return { success: false, message: error.message, error };\n        }\n    }\n\n    /**\n     * 转换订单数据为旺店通退款格式\n     * @param {Object} orderInfo 订单信息\n     * @param {Array} orderGoods 订单商品列表\n     * @param {Object} userInfo 用户信息（包含昵称）\n     * @returns {Object} 旺店通退款订单格式\n     */\n    transformOrderDataForRefund(orderInfo, orderGoods, userInfo = null) {\n        // 使用原订单的支付时间，保持时间不变\n        let tradeTimeStr = '';\n        let payTimeStr = '';\n\n        if (orderInfo.pay_time) {\n            // 如果有支付时间，转换为旺店通格式\n            const payTime = new Date(orderInfo.pay_time * 1000); // 假设pay_time是时间戳\n            tradeTimeStr = payTime.getFullYear() + '-' +\n                String(payTime.getMonth() + 1).padStart(2, '0') + '-' +\n                String(payTime.getDate()).padStart(2, '0') + ' ' +\n                String(payTime.getHours()).padStart(2, '0') + ':' +\n                String(payTime.getMinutes()).padStart(2, '0') + ':' +\n                String(payTime.getSeconds()).padStart(2, '0');\n            payTimeStr = tradeTimeStr;\n        } else {\n            // 如果没有支付时间，使用当前时间\n            const currentTime = new Date();\n            tradeTimeStr = currentTime.getFullYear() + '-' +\n                String(currentTime.getMonth() + 1).padStart(2, '0') + '-' +\n                String(currentTime.getDate()).padStart(2, '0') + ' ' +\n                String(currentTime.getHours()).padStart(2, '0') + ':' +\n                String(currentTime.getMinutes()).padStart(2, '0') + ':' +\n                String(currentTime.getSeconds()).padStart(2, '0');\n            payTimeStr = tradeTimeStr;\n        }\n\n        // 处理买家昵称：使用小程序客户昵称base64编码，如果没有昵称则使用\"微信用户\"的base64\n        let buyerNick = 'user_' + orderInfo.user_id; // 默认值\n        if (userInfo && userInfo.nickname) {\n            // 用户昵称已经是base64编码，直接使用\n            buyerNick = userInfo.nickname;\n        } else {\n            // 没有昵称，使用\"微信用户\"的base64编码\n            const defaultNickname = Buffer.from('微信用户').toString('base64');\n            buyerNick = defaultNickname;\n        }\n\n        const wangdianOrder = {\n            tid: orderInfo.order_sn,\n            platform_id: this.config.platformId,\n            trade_status: 80,  // 已退款\n            pay_status: 2,     // 保持已付款状态（因为曾经付过款）\n            delivery_term: 1,  // 款到发货\n            trade_time: tradeTimeStr, // 保持原有时间不变\n            pay_time: payTimeStr,     // 保持原有时间不变\n            buyer_nick: buyerNick,\n            buyer_email: '',\n            receiver_name: orderInfo.consignee,\n            receiver_province: orderInfo.province,\n            receiver_city: orderInfo.city,\n            receiver_district: orderInfo.district,\n            receiver_address: orderInfo.address,\n            receiver_mobile: orderInfo.mobile,\n            receiver_telno: '',\n            pay_method: 1,     // 在线支付\n            logistics_type: 1,\n            invoice_kind: 0,\n            invoice_title: '',\n            invoice_content: '',\n            buyer_message: orderInfo.postscript || '',\n            seller_memo: `商城订单-${orderInfo.order_sn}-已退款`,\n            remark_flag: 0,\n            seller_flag: 0,\n            post_amount: parseFloat(orderInfo.freight_price || 0),\n            cod_amount: 0,\n            ext_cod_fee: 0,\n            other_amount: 0,\n            paid: parseFloat(orderInfo.actual_price), // 保持原金额不变\n            warehouse_no: this.config.warehouseNo,\n            order_list: orderGoods.map((item, index) => ({\n                oid: `${orderInfo.order_sn}_${index + 1}`,\n                num: parseInt(item.number),\n                price: parseFloat(item.retail_price),\n                status: 80,    // 已退款\n                refund_status: 5, // 已退款完成\n                goods_id: item.goods_id,\n                spec_id: item.product_id,\n                goods_no: item.goods_sn || item.goods_id.toString(),\n                spec_no: item.product_id.toString(),\n                goods_name: item.goods_name,\n                spec_name: item.goods_specifition_name_value || '默认规格',\n                adjust_amount: 0,\n                discount: 0,\n                share_discount: 0,\n                commission: 0,\n                remark: '',\n                cid: ''\n            }))\n        };\n\n        return wangdianOrder;\n    }\n}\n\nmodule.exports = WangDianSync;\n"]}