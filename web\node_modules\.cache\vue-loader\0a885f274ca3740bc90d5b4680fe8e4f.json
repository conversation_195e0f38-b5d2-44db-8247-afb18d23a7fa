{"remainingRequest": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Test\\FlashSaleApiTest.vue?vue&type=style&index=0&id=1418e71c&scoped=true&lang=css&", "dependencies": [{"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\src\\components\\Test\\FlashSaleApiTest.vue", "mtime": 1753712463451}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\py-ide\\hioshop-miniprogram-master\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmFwaS10ZXN0LWNvbnRhaW5lciB7CiAgcGFkZGluZzogMjBweDsKICBtYXgtd2lkdGg6IDEyMDBweDsKICBtYXJnaW46IDAgYXV0bzsKfQoKLnRlc3Qtc2VjdGlvbiB7CiAgbWFyZ2luLWJvdHRvbTogMzBweDsKICBwYWRkaW5nOiAyMHB4OwogIGJvcmRlcjogMXB4IHNvbGlkICNkZGQ7CiAgYm9yZGVyLXJhZGl1czogOHB4Owp9CgouZGVidWctc2VjdGlvbiB7CiAgbWFyZ2luLWJvdHRvbTogMzBweDsKICBwYWRkaW5nOiAyMHB4OwogIGJvcmRlcjogMXB4IHNvbGlkICNlNmEyM2M7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIGJhY2tncm91bmQtY29sb3I6ICNmZGY2ZWM7Cn0KCi50ZXN0LWJ1dHRvbnMgewogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi50ZXN0LWJ1dHRvbnMgLmVsLWJ1dHRvbiB7CiAgbWFyZ2luLXJpZ2h0OiAxMHB4OwogIG1hcmdpbi1ib3R0b206IDEwcHg7Cn0KCi5lcnJvci1tZXNzYWdlIHsKICBtYXJnaW4tdG9wOiAyMHB4OwogIHBhZGRpbmc6IDE1cHg7CiAgYmFja2dyb3VuZC1jb2xvcjogI2ZlZjBmMDsKICBib3JkZXI6IDFweCBzb2xpZCAjZmJjNGM0OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBjb2xvcjogI2Y1NmM2YzsKfQoKLnRlc3QtcmVzdWx0IHsKICBtYXJnaW4tdG9wOiAyMHB4OwogIHBhZGRpbmc6IDE1cHg7CiAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjlmZjsKICBib3JkZXI6IDFweCBzb2xpZCAjYzRlMWZmOwogIGJvcmRlci1yYWRpdXM6IDRweDsKfQoKcHJlIHsKICB3aGl0ZS1zcGFjZTogcHJlLXdyYXA7CiAgd29yZC13cmFwOiBicmVhay13b3JkOwogIG1heC1oZWlnaHQ6IDQwMHB4OwogIG92ZXJmbG93LXk6IGF1dG87CiAgZm9udC1mYW1pbHk6ICdDb3VyaWVyIE5ldycsIG1vbm9zcGFjZTsKICBmb250LXNpemU6IDEycHg7CiAgbGluZS1oZWlnaHQ6IDEuNDsKfQoKaDIgewogIGNvbG9yOiAjMzAzMTMzOwogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCmgzIHsKICBjb2xvcjogIzYwNjI2NjsKICBtYXJnaW4tYm90dG9tOiAxNXB4Owp9CgpoNCB7CiAgY29sb3I6ICM5MDkzOTk7CiAgbWFyZ2luLWJvdHRvbTogMTBweDsKfQo="}, {"version": 3, "sources": ["FlashSaleApiTest.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "FlashSaleApiTest.vue", "sourceRoot": "src/components/Test", "sourcesContent": ["<template>\n  <div class=\"api-test-container\">\n    <h2>秒杀API测试</h2>\n    \n    <div class=\"test-section\">\n      <h3>API测试结果</h3>\n      <div class=\"test-buttons\">\n        <el-button @click=\"testTimeSlots\" type=\"primary\">测试时段列表API</el-button>\n        <el-button @click=\"testStatistics\" type=\"success\">测试统计数据API</el-button>\n        <el-button @click=\"testProducts\" type=\"info\">测试商品列表API</el-button>\n      </div>\n      \n      <div v-if=\"errorMessage\" class=\"error-message\">\n        <h4>错误信息:</h4>\n        <pre>{{ errorMessage }}</pre>\n      </div>\n      \n      <div v-if=\"testResult\" class=\"test-result\">\n        <h4>测试结果:</h4>\n        <pre>{{ testResult }}</pre>\n      </div>\n    </div>\n    \n    <div class=\"debug-section\">\n      <h3>调试信息</h3>\n      <p><strong>当前Token:</strong> {{ currentToken || '无' }}</p>\n      <p><strong>API Base URL:</strong> {{ baseURL }}</p>\n      <p><strong>完整URL示例:</strong> {{ baseURL }}flash-sale/time-slots</p>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FlashSaleApiTest',\n  data() {\n    return {\n      testResult: '',\n      errorMessage: '',\n      currentToken: '',\n      baseURL: ''\n    }\n  },\n  \n  mounted() {\n    this.currentToken = localStorage.getItem('token') || '';\n    this.baseURL = this.axios.defaults.baseURL || '';\n  },\n  \n  methods: {\n    async testTimeSlots() {\n      try {\n        this.errorMessage = '';\n        this.testResult = '正在测试时段列表API...';\n        \n        const response = await this.axios.get('flash-sale/time-slots');\n        \n        this.testResult = JSON.stringify(response.data, null, 2);\n      } catch (error) {\n        this.errorMessage = `时段列表API测试失败: ${error.message}`;\n        this.testResult = JSON.stringify(error.response?.data || error, null, 2);\n      }\n    },\n    \n    async testStatistics() {\n      try {\n        this.errorMessage = '';\n        this.testResult = '正在测试统计数据API...';\n        \n        const response = await this.axios.get('flash-sale/statistics');\n        \n        this.testResult = JSON.stringify(response.data, null, 2);\n      } catch (error) {\n        this.errorMessage = `统计数据API测试失败: ${error.message}`;\n        this.testResult = JSON.stringify(error.response?.data || error, null, 2);\n      }\n    },\n    \n    async testProducts() {\n      try {\n        this.errorMessage = '';\n        this.testResult = '正在测试商品列表API...';\n        \n        const response = await this.axios.get('flash-sale/products');\n        \n        this.testResult = JSON.stringify(response.data, null, 2);\n      } catch (error) {\n        this.errorMessage = `商品列表API测试失败: ${error.message}`;\n        this.testResult = JSON.stringify(error.response?.data || error, null, 2);\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.api-test-container {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.test-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n}\n\n.debug-section {\n  margin-bottom: 30px;\n  padding: 20px;\n  border: 1px solid #e6a23c;\n  border-radius: 8px;\n  background-color: #fdf6ec;\n}\n\n.test-buttons {\n  margin-bottom: 20px;\n}\n\n.test-buttons .el-button {\n  margin-right: 10px;\n  margin-bottom: 10px;\n}\n\n.error-message {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #fef0f0;\n  border: 1px solid #fbc4c4;\n  border-radius: 4px;\n  color: #f56c6c;\n}\n\n.test-result {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f0f9ff;\n  border: 1px solid #c4e1ff;\n  border-radius: 4px;\n}\n\npre {\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  max-height: 400px;\n  overflow-y: auto;\n  font-family: 'Courier New', monospace;\n  font-size: 12px;\n  line-height: 1.4;\n}\n\nh2 {\n  color: #303133;\n  margin-bottom: 20px;\n}\n\nh3 {\n  color: #606266;\n  margin-bottom: 15px;\n}\n\nh4 {\n  color: #909399;\n  margin-bottom: 10px;\n}\n</style>\n"]}]}