function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

module.exports = class extends think.Model {

  /**
   * 获取用户的兑换记录
   * @param {number} userId 用户ID
   * @param {number} limit 限制数量
   * @returns {array} 兑换记录列表
   */
  getUserExchangeRecords(userId, limit = 20) {
    var _this = this;

    return _asyncToGenerator(function* () {
      try {
        const records = yield _this.where({
          user_id: userId
        }).order('created_at DESC').limit(limit).select();

        return records;
      } catch (error) {
        console.error('获取用户兑换记录失败:', error);
        return [];
      }
    })();
  }

  /**
   * 检查订单号是否已被兑换
   * @param {string} orderNumber 订单号
   * @param {number} userId 用户ID（可选）
   * @returns {boolean} 是否已兑换
   */
  isOrderExchanged(orderNumber, userId = null) {
    var _this2 = this;

    return _asyncToGenerator(function* () {
      try {
        const where = { order_number: orderNumber };
        if (userId) {
          where.user_id = userId;
        }

        const record = yield _this2.where(where).find();
        return !think.isEmpty(record);
      } catch (error) {
        console.error('检查订单兑换状态失败:', error);
        return false;
      }
    })();
  }

  /**
   * 获取用户今日兑换次数
   * @param {number} userId 用户ID
   * @returns {number} 今日兑换次数
   */
  getTodayExchangeCount(userId) {
    var _this3 = this;

    return _asyncToGenerator(function* () {
      try {
        const today = new Date().toISOString().split('T')[0];
        const count = yield _this3.where({
          user_id: userId,
          created_at: ['LIKE', `${today}%`]
        }).count();

        return count;
      } catch (error) {
        console.error('获取今日兑换次数失败:', error);
        return 0;
      }
    })();
  }

  /**
   * 获取兑换统计数据
   * @param {string} timeRange 时间范围 (today, week, month)
   * @returns {object} 统计数据
   */
  getExchangeStats(timeRange = 'today') {
    var _this4 = this;

    return _asyncToGenerator(function* () {
      try {
        let whereCondition = {};
        const now = new Date();

        switch (timeRange) {
          case 'today':
            const today = now.toISOString().split('T')[0];
            whereCondition.created_at = ['LIKE', `${today}%`];
            break;
          case 'week':
            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            whereCondition.created_at = ['>=', weekAgo.toISOString()];
            break;
          case 'month':
            const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            whereCondition.created_at = ['>=', monthAgo.toISOString()];
            break;
        }

        // 总兑换次数
        const totalExchanges = yield _this4.where(whereCondition).count();

        // 成功兑换次数
        const successExchanges = yield _this4.where(Object.assign({}, whereCondition, {
          status: 'success'
        })).count();

        // 总兑换积分
        const totalPoints = yield _this4.where(Object.assign({}, whereCondition, {
          status: 'success'
        })).sum('exchange_points');

        // 新增用户数（首次兑换的用户）
        const newUsers = yield _this4.query(`
        SELECT COUNT(DISTINCT user_id) as count 
        FROM (
          SELECT user_id, MIN(created_at) as first_exchange 
          FROM order_exchange_log 
          WHERE status = 'success'
          GROUP BY user_id
        ) as first_exchanges 
        WHERE first_exchange >= ?
      `, [whereCondition.created_at ? whereCondition.created_at[1] : '1970-01-01']);

        return {
          totalExchanges: totalExchanges || 0,
          successExchanges: successExchanges || 0,
          totalPoints: totalPoints || 0,
          newUsers: newUsers[0] ? newUsers[0].count : 0
        };
      } catch (error) {
        console.error('获取兑换统计失败:', error);
        return {
          totalExchanges: 0,
          successExchanges: 0,
          totalPoints: 0,
          newUsers: 0
        };
      }
    })();
  }

  /**
   * 创建兑换记录
   * @param {object} data 兑换数据
   * @returns {number} 记录ID
   */
  createExchangeRecord(data) {
    var _this5 = this;

    return _asyncToGenerator(function* () {
      try {
        const currentTime = new Date();
        const recordData = {
          user_id: data.userId,
          order_number: data.orderNumber,
          order_platform: data.platform,
          order_amount: data.amount,
          exchange_points: data.points,
          exchange_rate: data.rate,
          status: data.status || 'success',
          fail_reason: data.failReason || '',
          created_at: currentTime,
          updated_at: currentTime
        };

        const recordId = yield _this5.add(recordData);
        return recordId;
      } catch (error) {
        console.error('创建兑换记录失败:', error);
        throw error;
      }
    })();
  }

  /**
   * 更新兑换记录状态
   * @param {number} recordId 记录ID
   * @param {string} status 新状态
   * @param {string} failReason 失败原因（可选）
   * @returns {boolean} 是否成功
   */
  updateExchangeStatus(recordId, status, failReason = '') {
    var _this6 = this;

    return _asyncToGenerator(function* () {
      try {
        const updateData = {
          status: status,
          updated_at: new Date()
        };

        if (failReason) {
          updateData.fail_reason = failReason;
        }

        const result = yield _this6.where({ id: recordId }).update(updateData);
        return result > 0;
      } catch (error) {
        console.error('更新兑换记录状态失败:', error);
        return false;
      }
    })();
  }
};