function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

const Base = require('./base.js');
const moment = require('moment');

module.exports = class extends Base {
    /**
     * 获取招募规则
     * @return {Promise} []
     */
    rulesAction() {
        var _this = this;

        return _asyncToGenerator(function* () {
            try {
                const model = _this.model('recruitment_rules');
                const rules = yield model.find();

                if (rules) {
                    return _this.success(rules);
                } else {
                    // 返回默认规则
                    const defaultRules = {
                        joinCondition: 'with_conditions',
                        conditions: {
                            requirePurchase: false,
                            requireAmount: true,
                            minAmount: 99.00,
                            requireOrders: false,
                            minOrders: 1
                        },
                        applicationMethod: 'manual_apply',
                        requireApplicationInfo: false,
                        auditMethod: 'auto_audit'
                    };
                    return _this.success(defaultRules);
                }
            } catch (error) {
                console.error('获取招募规则失败:', error);
                return _this.fail('获取招募规则失败');
            }
        })();
    }

    /**
     * 保存招募规则
     * @return {Promise} []
     */
    saveRulesAction() {
        var _this2 = this;

        return _asyncToGenerator(function* () {
            try {
                const rulesData = _this2.post();

                // 验证数据
                if (!rulesData.joinCondition) {
                    return _this2.fail('请选择加入条件类型');
                }

                if (!rulesData.applicationMethod) {
                    return _this2.fail('请选择申请方式');
                }

                if (!rulesData.auditMethod) {
                    return _this2.fail('请选择审核方式');
                }

                // 如果是有条件加入，验证条件设置
                if (rulesData.joinCondition === 'with_conditions') {
                    const conditions = rulesData.conditions || {};
                    const hasCondition = conditions.requirePurchase || conditions.requireAmount || conditions.requireOrders;

                    if (!hasCondition) {
                        return _this2.fail('请至少设置一个加入条件');
                    }

                    if (conditions.requireAmount) {
                        if (!conditions.minAmount || conditions.minAmount < 0.01) {
                            return _this2.fail('自购金额最低设置为0.01元');
                        }
                    }

                    if (conditions.requireOrders) {
                        if (!conditions.minOrders || conditions.minOrders < 1) {
                            return _this2.fail('消费笔数最低设置为1笔');
                        }
                    }
                }

                const model = _this2.model('recruitment_rules');
                const currentTime = parseInt(new Date().getTime() / 1000);

                // 检查是否已存在规则
                const existingRules = yield model.find();

                const saveData = {
                    join_condition: rulesData.joinCondition,
                    conditions: JSON.stringify(rulesData.conditions || {}),
                    application_method: rulesData.applicationMethod,
                    require_application_info: rulesData.requireApplicationInfo ? 1 : 0,
                    audit_method: rulesData.auditMethod,
                    update_time: currentTime
                };

                if (existingRules && existingRules.id) {
                    // 更新现有规则
                    yield model.where({ id: existingRules.id }).update(saveData);
                } else {
                    // 创建新规则
                    saveData.add_time = currentTime;
                    yield model.add(saveData);
                }

                return _this2.success('招募规则保存成功');
            } catch (error) {
                console.error('保存招募规则失败:', error);
                return _this2.fail('保存招募规则失败');
            }
        })();
    }

    /**
     * 更新招募规则
     * @return {Promise} []
     */
    updateRulesAction() {
        var _this3 = this;

        return _asyncToGenerator(function* () {
            return _this3.saveRulesAction();
        })();
    }

    /**
     * 获取招募统计
     * @return {Promise} []
     */
    statsAction() {
        var _this4 = this;

        return _asyncToGenerator(function* () {
            try {
                // 总申请人数
                const totalApplications = yield _this4.model('distributors').where({
                    audit_status: ['IN', [0, 1, 2]] // 0:待审核 1:已通过 2:已拒绝
                }).count();

                // 待审核人数
                const pendingApplications = yield _this4.model('distributors').where({
                    audit_status: 0
                }).count();

                // 已通过人数
                const approvedApplications = yield _this4.model('distributors').where({
                    audit_status: 1
                }).count();

                // 已拒绝人数
                const rejectedApplications = yield _this4.model('distributors').where({
                    audit_status: 2
                }).count();

                // 今日新增申请
                const todayStart = moment().startOf('day').unix();
                const todayEnd = moment().endOf('day').unix();
                const todayApplications = yield _this4.model('distributors').where({
                    add_time: ['BETWEEN', todayStart, todayEnd]
                }).count();

                const stats = {
                    totalApplications: totalApplications,
                    pendingApplications: pendingApplications,
                    approvedApplications: approvedApplications,
                    rejectedApplications: rejectedApplications,
                    todayApplications: todayApplications,
                    approvalRate: totalApplications > 0 ? (approvedApplications / totalApplications * 100).toFixed(2) : '0.00'
                };

                return _this4.success(stats);
            } catch (error) {
                console.error('获取招募统计失败:', error);
                return _this4.fail('获取招募统计失败');
            }
        })();
    }

    /**
     * 检查用户是否满足招募条件
     * @return {Promise} []
     */
    checkConditionsAction() {
        var _this5 = this;

        return _asyncToGenerator(function* () {
            try {
                const userId = _this5.get('user_id');

                if (!userId) {
                    return _this5.fail('用户ID不能为空');
                }

                // 获取招募规则
                const rulesModel = _this5.model('recruitment_rules');
                const rules = yield rulesModel.find();

                if (!rules || rules.join_condition === 'no_conditions') {
                    return _this5.success({
                        canJoin: true,
                        message: '无条件加入'
                    });
                }

                const conditions = JSON.parse(rules.conditions || '{}');
                const checkResults = {
                    canJoin: true,
                    conditions: [],
                    failedConditions: []
                };

                // 检查自购金额条件
                if (conditions.requireAmount) {
                    const userOrders = yield _this5.model('order').where({
                        user_id: userId,
                        order_status: ['>=', 300] // 已支付订单
                    }).sum('actual_price');

                    const totalAmount = parseFloat(userOrders || 0);
                    const requiredAmount = parseFloat(conditions.minAmount || 0);

                    if (totalAmount >= requiredAmount) {
                        checkResults.conditions.push({
                            type: 'amount',
                            required: requiredAmount,
                            current: totalAmount,
                            passed: true
                        });
                    } else {
                        checkResults.conditions.push({
                            type: 'amount',
                            required: requiredAmount,
                            current: totalAmount,
                            passed: false
                        });
                        checkResults.failedConditions.push('自购金额不足');
                        checkResults.canJoin = false;
                    }
                }

                // 检查消费笔数条件
                if (conditions.requireOrders) {
                    const orderCount = yield _this5.model('order').where({
                        user_id: userId,
                        order_status: ['>=', 300] // 已支付订单
                    }).count();

                    const requiredOrders = parseInt(conditions.minOrders || 0);

                    if (orderCount >= requiredOrders) {
                        checkResults.conditions.push({
                            type: 'orders',
                            required: requiredOrders,
                            current: orderCount,
                            passed: true
                        });
                    } else {
                        checkResults.conditions.push({
                            type: 'orders',
                            required: requiredOrders,
                            current: orderCount,
                            passed: false
                        });
                        checkResults.failedConditions.push('消费笔数不足');
                        checkResults.canJoin = false;
                    }
                }

                // 检查指定商品购买条件
                if (conditions.requirePurchase) {
                    // 这里可以添加指定商品购买检查逻辑
                    // 暂时设为通过
                    checkResults.conditions.push({
                        type: 'purchase',
                        required: '指定商品',
                        current: '已购买',
                        passed: true
                    });
                }

                return _this5.success(checkResults);
            } catch (error) {
                console.error('检查招募条件失败:', error);
                return _this5.fail('检查招募条件失败');
            }
        })();
    }
};