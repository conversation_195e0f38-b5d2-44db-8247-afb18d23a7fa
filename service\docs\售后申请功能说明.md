# 售后申请功能实现说明

## 📋 功能概述

为小程序订单详情页面增加了"申请售后"按钮，用户可以在符合条件的订单中申请退款或退货退款。退款原因选择使用action-sheet实现，提供更好的用户体验。

## 🔧 实现内容

### 1. 前端小程序页面

#### 新增页面
- `pages/ucenter/refund-apply/index.js` - 售后申请页面逻辑
- `pages/ucenter/refund-apply/index.wxml` - 售后申请页面模板
- `pages/ucenter/refund-apply/index.wxss` - 售后申请页面样式
- `pages/ucenter/refund-apply/index.json` - 页面配置

#### 修改页面
- `pages/ucenter/order-details/index.js` - 添加售后申请跳转逻辑
- `pages/ucenter/order-details/index.wxml` - 添加申请售后按钮
- `pages/ucenter/order-details/index.wxss` - 添加按钮样式
- `config/api.js` - 添加售后申请API配置
- `app.json` - 注册新页面

### 2. 后端API接口

#### 修改文件
- `service/src/api/model/order.js` - 添加售后申请判断逻辑
- `service/src/api/controller/order.js` - 添加售后申请处理接口

#### 新增数据库表
- `service/database/refund_apply_tables.sql` - 售后申请表结构
- `service/create_refund_tables.js` - 创建表的脚本

## 🎯 功能特性

### 售后申请条件
- **已付款未发货**（状态201）：可申请售后
- **待发货**（状态300）：可申请售后  
- **已发货未收货**（状态301）：可申请售后
- **已完成订单**（状态401）：7天内可申请售后

### 售后类型
- **仅退款**：不需要退货，直接退款
- **退货退款**：需要退货后退款

### 申请信息
- 退款原因选择（预设6种常见原因）
- 退款金额输入（不超过订单金额）
- 退款说明（可选）
- 凭证图片上传（最多3张，可选）

## 🔄 业务流程

### 用户申请流程
```
订单详情页 → 点击"申请售后" → 填写申请信息 → 提交申请 → 等待处理
```

### 系统处理流程
```
接收申请 → 验证订单状态 → 检查申请条件 → 保存申请记录 → 返回结果
```

## 📊 数据库设计

### hiolabs_refund_apply 表结构
```sql
- id: 申请ID（主键）
- order_id: 订单ID
- user_id: 用户ID  
- order_sn: 订单号
- refund_type: 退款类型（refund_only/return_refund）
- refund_amount: 退款金额
- refund_reason: 退款原因
- refund_desc: 退款说明
- images: 凭证图片（JSON格式）
- status: 状态（pending/processing/approved/rejected/completed）
- apply_time: 申请时间
- created_at/updated_at: 创建/更新时间
```

## 🚀 部署步骤

### 1. 创建数据库表
```bash
cd service
node create_refund_tables.js
```

### 2. 重启后端服务
```bash
npm run start
```

### 3. 小程序端无需额外操作
页面已自动注册，功能即可使用。

## 🎨 UI设计

### 申请售后按钮
- 位置：订单详情页底部操作栏
- 样式：橙色渐变按钮
- 显示条件：根据订单状态动态显示

### 申请页面
- 简洁的表单设计
- 清晰的信息层级
- 友好的交互提示
- 图片上传支持

## 🔍 后续扩展

### 管理后台功能（待开发）
- 售后申请列表
- 申请详情查看
- 审核通过/拒绝
- 退款处理

### 微信退款集成（待完善）
- 调用微信退款API
- 退款状态同步
- 退款通知

### 用户端功能（待扩展）
- 申请状态查询
- 申请记录列表
- 撤销申请

## ⚠️ 注意事项

1. **数据库表**：需要先创建 `hiolabs_refund_apply` 表
2. **权限验证**：已验证用户身份和订单归属
3. **状态检查**：严格验证订单状态是否允许申请售后
4. **金额验证**：确保退款金额不超过订单金额
5. **重复申请**：防止同一订单重复申请售后

## 📝 测试建议

1. 测试不同订单状态下的售后申请
2. 验证表单验证逻辑
3. 测试图片上传功能
4. 检查数据库记录是否正确保存
5. 验证错误处理和用户提示
