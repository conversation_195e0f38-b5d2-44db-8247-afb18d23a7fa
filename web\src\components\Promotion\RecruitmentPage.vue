<template>
  <div class="recruitment-page">
    <div class="page-header bg-white shadow-sm border-b">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">招募设置</h1>
            <p class="text-sm text-gray-500 mt-1">配置分销员招募规则和条件</p>
          </div>
          <div class="flex items-center space-x-3">
            <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <i class="ri-save-line mr-2"></i>
              保存设置
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="p-6">
      <div class="max-w-4xl mx-auto">
        <div class="bg-gradient-to-br from-indigo-50 to-purple-100 rounded-xl p-8 text-center">
          <div class="w-24 h-24 mx-auto mb-6 bg-indigo-100 rounded-full flex items-center justify-center">
            <i class="ri-user-add-line text-4xl text-indigo-600"></i>
          </div>
          <h2 class="text-2xl font-bold text-gray-900 mb-4">招募设置</h2>
          <p class="text-lg text-gray-600 mb-6">当前功能正在开发中，敬请期待</p>
          <div class="mt-6 text-center">
            <p class="text-sm text-gray-500">预计完成时间：<span class="font-medium text-gray-700">2024年1月</span></p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RecruitmentPage',
  mounted() {
    console.log('招募设置页面已加载');
  }
}
</script>

<style scoped>
.recruitment-page {
  min-height: 100vh;
  background-color: #f9fafb;
}
.page-header {
  position: sticky;
  top: 0;
  z-index: 10;
}
</style>
