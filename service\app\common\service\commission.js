function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

module.exports = class extends think.Service {

    /**
     * 核心函数：计算用户佣金状态（基于现有数据库结构）
     * @param {number} userId 用户ID
     * @return {object} 佣金状态信息
     */
    calculateUserCommission(userId) {
        var _this = this;

        return _asyncToGenerator(function* () {
            try {
                const currentTime = parseInt(new Date().getTime() / 1000);
                const tenDaysAgo = currentTime - 10 * 24 * 3600; // 10天前

                console.log(`=== 计算用户${userId}的佣金状态 ===`);
                console.log('当前时间:', currentTime, '10天前:', tenDaysAgo);

                // 1. 获取用户佣金账户信息
                const userCommission = yield _this.model('user_commission').where({
                    user_id: userId
                }).find();

                if (think.isEmpty(userCommission)) {
                    console.log('用户没有佣金账户，返回默认值');
                    return {
                        totalCommission: '0.00',
                        availableCommission: '0.00',
                        frozenCommission: '0.00',
                        withdrawnCommission: '0.00',
                        refundedCommission: '0.00'
                    };
                }

                // 2. 查询佣金日志，计算10天期限内的冻结佣金
                const commissionLogs = yield _this.model('commission_log').where({
                    user_id: userId,
                    commission_type: 'promotion',
                    status: 'completed',
                    commission_change: ['>', 0] // 只查询正数（获得的佣金）
                }).field('commission_change, created_at').select();

                console.log('用户佣金日志记录:', commissionLogs.length, '条');

                // 3. 计算冻结中的佣金（10天内的）
                let frozenCommission = 0;
                for (const log of commissionLogs) {
                    const logTime = parseInt(new Date(log.created_at).getTime() / 1000);
                    if (logTime > tenDaysAgo) {
                        frozenCommission += parseFloat(log.commission_change);
                    }
                }

                // 4. 计算可提现佣金 = 总佣金 - 冻结佣金 - 已提现佣金
                const totalCommission = parseFloat(userCommission.total_commission);
                const withdrawnCommission = parseFloat(userCommission.withdrawn_commission);
                const availableCommission = Math.max(0, totalCommission - frozenCommission - withdrawnCommission);

                const result = {
                    totalCommission: totalCommission.toFixed(2),
                    availableCommission: availableCommission.toFixed(2),
                    frozenCommission: frozenCommission.toFixed(2),
                    withdrawnCommission: withdrawnCommission.toFixed(2),
                    refundedCommission: '0.00' // 暂时设为0，后续可以从日志中计算
                };

                console.log('佣金计算结果:', result);
                return result;
            } catch (error) {
                console.error('计算用户佣金状态失败:', error);
                return {
                    totalCommission: '0.00',
                    availableCommission: '0.00',
                    frozenCommission: '0.00',
                    withdrawnCommission: '0.00',
                    refundedCommission: '0.00'
                };
            }
        })();
    }

    /**
     * 获取用户佣金明细
     * @param {number} userId 用户ID
     * @param {number} limit 限制条数
     * @return {array} 佣金明细列表
     */
    getCommissionDetails(userId, limit = 20) {
        var _this2 = this;

        return _asyncToGenerator(function* () {
            try {
                console.log(`=== 获取用户${userId}的佣金明细 ===`);

                // 查询佣金日志
                const commissionLogs = yield _this2.model('commission_log').where({
                    user_id: userId,
                    commission_type: 'promotion'
                }).order('created_at DESC').limit(limit).select();

                console.log('查询到佣金明细:', commissionLogs.length, '条');

                // 格式化佣金明细
                const currentTime = parseInt(new Date().getTime() / 1000);
                const tenDaysAgo = currentTime - 10 * 24 * 3600;

                const formattedDetails = commissionLogs.map(function (log) {
                    const amount = parseFloat(log.commission_change);

                    // 处理时间格式：created_at是timestamp类型，需要正确转换
                    let logTime;
                    if (log.created_at instanceof Date) {
                        logTime = parseInt(log.created_at.getTime() / 1000);
                    } else if (typeof log.created_at === 'string') {
                        logTime = parseInt(new Date(log.created_at).getTime() / 1000);
                    } else {
                        // 如果已经是时间戳格式
                        logTime = parseInt(log.created_at);
                    }

                    let statusText = '';
                    if (amount < 0) {
                        statusText = '已扣除';
                    } else if (logTime > tenDaysAgo) {
                        statusText = '冻结中';
                    } else {
                        statusText = '可提现';
                    }

                    return {
                        id: log.id,
                        amount: Math.abs(amount).toFixed(2),
                        type: amount > 0 ? 'earn' : 'refund',
                        status: log.status,
                        statusText: statusText,
                        description: log.description || '推广佣金',
                        orderId: log.source_id,
                        settleTime: logTime,
                        createTime: log.created_at
                    };
                });

                return formattedDetails;
            } catch (error) {
                console.error('获取佣金明细失败:', error);
                return [];
            }
        })();
    }

    /**
     * 发放佣金（确认收货时调用）
     * @param {number} userId 用户ID
     * @param {number} amount 佣金金额
     * @param {number} orderId 订单ID
     * @param {number} promotionOrderId 推广订单ID
     * @param {string} description 描述
     */
    settleCommission(userId, amount, orderId, promotionOrderId, description) {
        var _this3 = this;

        return _asyncToGenerator(function* () {
            try {
                const currentTime = parseInt(new Date().getTime() / 1000);

                console.log(`=== 发放佣金 ===`);
                console.log('用户ID:', userId, '金额:', amount, '订单ID:', orderId);

                // 1. 记录佣金日志
                yield _this3.model('commission_log').add({
                    user_id: userId,
                    commission_change: amount,
                    commission_type: 'promotion',
                    commission_status: 'settled',
                    source_id: orderId,
                    promotion_order_id: promotionOrderId,
                    description: description,
                    balance_after: 0, // 暂时设为0，后续可以实时计算
                    settle_time: currentTime,
                    created_at: new Date()
                });

                // 2. 更新用户佣金总额
                const userCommission = yield _this3.model('user_commission').where({
                    user_id: userId
                }).find();

                if (think.isEmpty(userCommission)) {
                    // 创建用户佣金记录
                    yield _this3.model('user_commission').add({
                        user_id: userId,
                        total_commission: amount,
                        available_commission: 0, // 实时计算，不存储
                        frozen_commission: 0, // 实时计算，不存储
                        withdrawn_commission: 0,
                        created_at: new Date(),
                        updated_at: new Date()
                    });
                } else {
                    // 更新总佣金
                    yield _this3.model('user_commission').where({
                        user_id: userId
                    }).update({
                        total_commission: ['exp', `total_commission + ${amount}`],
                        updated_at: new Date()
                    });
                }

                // 3. 更新推广订单状态
                yield _this3.model('promotion_orders').where({
                    id: promotionOrderId
                }).update({
                    commission_status: 'settled',
                    settle_time: currentTime,
                    update_time: currentTime
                });

                console.log('✅ 佣金发放成功');
                return { success: true, message: '佣金发放成功' };
            } catch (error) {
                console.error('发放佣金失败:', error);
                return { success: false, message: error.message };
            }
        })();
    }

    /**
     * 处理退款扣除佣金
     * @param {number} orderId 订单ID
     */
    handleRefundCommission(orderId) {
        var _this4 = this;

        return _asyncToGenerator(function* () {
            try {
                console.log(`=== 处理订单${orderId}的退款佣金扣除 ===`);

                // 1. 查找该订单的所有推广佣金记录
                const promotionOrders = yield _this4.model('promotion_orders').where({
                    order_id: orderId,
                    commission_status: ['IN', ['settled', 'withdrawn']] // 已发放或已提现的
                }).select();

                if (think.isEmpty(promotionOrders)) {
                    console.log('没有找到需要退款的佣金记录');
                    return { success: true, message: '没有需要退款的佣金' };
                }

                const currentTime = parseInt(new Date().getTime() / 1000);

                // 2. 为每个佣金记录创建退款扣除
                for (const promotionOrder of promotionOrders) {
                    // 扣除个人佣金
                    if (parseFloat(promotionOrder.personal_commission) > 0) {
                        yield _this4.createRefundRecord(promotionOrder.promoter_user_id, promotionOrder.personal_commission, orderId, promotionOrder.id, `订单退款扣除个人佣金`);
                    }

                    // 扣除上级佣金
                    if (promotionOrder.parent_promoter_user_id && parseFloat(promotionOrder.level1_commission) > 0) {
                        yield _this4.createRefundRecord(promotionOrder.parent_promoter_user_id, promotionOrder.level1_commission, orderId, promotionOrder.id, `订单退款扣除上级佣金`);
                    }

                    // 更新推广订单状态
                    yield _this4.model('promotion_orders').where({
                        id: promotionOrder.id
                    }).update({
                        commission_status: 'refunded',
                        refund_time: currentTime,
                        update_time: currentTime
                    });
                }

                console.log('✅ 退款佣金扣除完成');
                return { success: true, message: '退款佣金扣除完成' };
            } catch (error) {
                console.error('处理退款佣金失败:', error);
                return { success: false, message: error.message };
            }
        })();
    }

    /**
     * 创建退款扣除记录
     */
    createRefundRecord(userId, amount, orderId, promotionOrderId, description) {
        var _this5 = this;

        return _asyncToGenerator(function* () {
            const currentTime = parseInt(new Date().getTime() / 1000);

            yield _this5.model('commission_log').add({
                user_id: userId,
                commission_change: -amount, // 负数表示扣除
                commission_type: 'promotion',
                commission_status: 'refunded',
                source_id: orderId,
                promotion_order_id: promotionOrderId,
                description: description,
                balance_after: 0,
                settle_time: currentTime,
                created_at: new Date()
            });

            // 更新用户总佣金
            yield _this5.model('user_commission').where({
                user_id: userId
            }).update({
                total_commission: ['exp', `total_commission - ${amount}`],
                updated_at: new Date()
            });
        })();
    }
};