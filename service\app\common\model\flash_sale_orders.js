function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

module.exports = class extends think.Model {

  /**
   * 获取表名
   */
  get tableName() {
    return 'hiolabs_flash_sale_orders';
  }

  /**
   * 记录秒杀订单
   */
  recordFlashSaleOrder(flashSaleId, orderId, userId, goodsId, quantity, flashPrice) {
    var _this = this;

    return _asyncToGenerator(function* () {
      const totalAmount = quantity * flashPrice;

      return yield _this.add({
        flash_sale_id: flashSaleId,
        order_id: orderId,
        user_id: userId,
        goods_id: goodsId,
        quantity: quantity,
        flash_price: flashPrice,
        total_amount: totalAmount
      });
    })();
  }

  /**
   * 获取用户在某个秒杀活动中的购买数量
   */
  getUserPurchaseCount(flashSaleId, userId) {
    var _this2 = this;

    return _asyncToGenerator(function* () {
      const result = yield _this2.where({
        flash_sale_id: flashSaleId,
        user_id: userId
      }).sum('quantity');

      return result || 0;
    })();
  }

  /**
   * 获取秒杀活动的订单统计
   */
  getFlashSaleOrderStats(flashSaleId) {
    var _this3 = this;

    return _asyncToGenerator(function* () {
      const orderCount = yield _this3.where({
        flash_sale_id: flashSaleId
      }).count();

      const totalQuantity = yield _this3.where({
        flash_sale_id: flashSaleId
      }).sum('quantity');

      const totalAmount = yield _this3.where({
        flash_sale_id: flashSaleId
      }).sum('total_amount');

      return {
        orderCount: orderCount || 0,
        totalQuantity: totalQuantity || 0,
        totalAmount: totalAmount || 0
      };
    })();
  }

  /**
   * 获取用户的秒杀订单历史
   */
  getUserFlashSaleOrders(userId, page = 1, size = 10) {
    var _this4 = this;

    return _asyncToGenerator(function* () {
      const offset = (page - 1) * size;

      return yield _this4.where({
        user_id: userId
      }).order('created_at DESC').limit(offset, size).select();
    })();
  }
};