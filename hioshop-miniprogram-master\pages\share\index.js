var util = require('../../utils/util.js');
var api = require('../../config/api.js');
const app = getApp()
const fsm = wx.getFileSystemManager();
const FILE_BASE_NAME = 'tmp_base64src'; //自定义文件名
Page({
    data: {
        painting: {},
        shareImage: '',
        goodsUrl: '',
        goods: {},
        userId: 0,
    },
    getQrcode: function(id, userId) {
        let that = this;
        console.log('=== 请求生成二维码 ===');
        console.log('商品ID:', id);
        console.log('用户ID:', userId);

        util.request(api.GetBase64, {
            goodsId: id,
            userId: userId
        }, 'POST').then(function(res) {
            console.log('二维码API响应:', res);
            if (res.errno === 0) {
                console.log('✅ 二维码获取成功，开始处理图片');
                that.getQrcodeJpg(res.data);
            } else {
                console.log('❌ 二维码获取失败:', res.errmsg);
                wx.hideLoading();
                wx.showToast({
                    title: '二维码生成失败',
                    icon: 'none'
                });
            }
        }).catch(function(error) {
            console.log('❌ 二维码请求异常:', error);
            wx.hideLoading();
            wx.showToast({
                title: '网络请求失败',
                icon: 'none'
            });
        });
    },
    getQrcodeJpg(code) {
        let that = this;
        console.log('=== 处理二维码图片 ===');
        console.log('Base64数据长度:', code ? code.length : 0);

        if (!code) {
            console.log('❌ 二维码数据为空');
            wx.hideLoading();
            wx.showToast({
                title: '二维码数据为空',
                icon: 'none'
            });
            return;
        }

        let num = Math.floor(Math.random() * 50);
        let promise = new Promise((resolve, reject) => {
            const filePath = wx.env.USER_DATA_PATH + '/temp_image' + num + '.jpeg';
            console.log('临时文件路径:', filePath);

            try {
                const buffer = wx.base64ToArrayBuffer(code);
                console.log('Buffer长度:', buffer.byteLength);

                wx.getFileSystemManager().writeFile({
                    filePath,
                    data: buffer,
                    encoding: 'binary',
                    success() {
                        console.log('✅ 二维码图片写入成功');
                        that.getGoodsInfo(filePath);
                    },
                    fail(error) {
                        console.log('❌ 二维码图片写入失败:', error);
                        wx.hideLoading();
                        wx.showToast({
                            title: '图片处理失败',
                            icon: 'none'
                        });
                        reject(new Error('ERROR_BASE64SRC_WRITE'));
                    },
                });
            } catch (error) {
                console.log('❌ Base64转换失败:', error);
                wx.hideLoading();
                wx.showToast({
                    title: 'Base64转换失败',
                    icon: 'none'
                });
            }
        });
    },
    onLoad(options) {
        wx.showLoading({
            title: '图片生成中',
        })
        let goodsid = options.goodsid;
        let userId = options.userId || 0;
        let goodsUrl = wx.getStorageSync('goodsImage');
        this.setData({
            goodsid: goodsid,
            userId: userId,
            goodsUrl: goodsUrl
        })
        this.getQrcode(goodsid, userId);
    },
    onShow: function() {
    },
    getGoodsInfo: function (qrcodeUrl) {
        let that = this;
        let id = that.data.goodsid;
        util.request(api.GoodsShare, {
            id: id
        }).then(function(res) {
            if (res.errno === 0) {
                that.setData({
                    goods: res.data,
                });
                // 验证商品图片URL
                that.validateAndDraw(qrcodeUrl);
            } else {
                console.log('❌ 获取商品信息失败:', res.errmsg);
                wx.hideLoading();
                wx.showToast({
                    title: '获取商品信息失败',
                    icon: 'none'
                });
            }
        }).catch(function(error) {
            console.log('❌ 获取商品信息异常:', error);
            wx.hideLoading();
            wx.showToast({
                title: '网络请求失败',
                icon: 'none'
            });
        });
    },

    validateAndDraw: function(qrcodeUrl) {
        let that = this;
        let goodsUrl = that.data.goodsUrl;

        console.log('=== 验证商品图片 ===');
        console.log('商品图片URL:', goodsUrl);
        console.log('二维码URL:', qrcodeUrl);

        if (!goodsUrl) {
            console.log('❌ 商品图片URL为空，使用默认图片');
            // 使用默认图片或跳过图片
            that.eventDrawWithoutImage(qrcodeUrl);
            return;
        }

        // 验证图片是否可以正常加载
        wx.getImageInfo({
            src: goodsUrl,
            success: function(res) {
                console.log('✅ 商品图片验证成功:', res);
                that.eventDraw(qrcodeUrl);
            },
            fail: function(error) {
                console.log('❌ 商品图片验证失败:', error);
                console.log('使用无图片模式生成分享图');
                that.eventDrawWithoutImage(qrcodeUrl);
            }
        });
    },
    eventDraw(qrcodeUrl) {
        let that = this;
        let goodsUrl = that.data.goodsUrl;
        let goods = that.data.goods;

        console.log('=== 生成完整分享图 ===');
        console.log('商品图片URL:', goodsUrl);
        console.log('商品信息:', goods);

        that.setData({
            painting: {
                width: 375,
                height: 667,
                background:'#fff',
                clear: true,
                views: [
                    {
                        type: 'rect',
                        top: 0,
                        left: 0,
                        width: 375,
                        height: 667,
                        background:'#fff'
                    },
                    {
                        type: 'rect',
                        top: 40,
                        left: 40,
                        width: 305,
                        height: 305,
                        background: '#f1f1f1'
                    },
                    {
                        type: 'image',
                        url: goodsUrl,
                        top: 35,
                        left: 35,
                        width: 305,
                        height: 305,
                    },
                    {
                        type: 'text',
                        content: goods.name,
                        fontSize: 18,
                        lineHeight: 22,
                        color: '#383549',
                        textAlign: 'left',
                        top: 360,
                        left: 35,
                        width: 305,
                        MaxLineNumber: 2,
                        breakWord: true,
                        // bolder: true
                    },
                    {
                        type: 'text',
                        content: '¥',
                        fontSize: 18,
                        lineHeight: 16,
                        color: '#e93237',
                        textAlign: 'left',
                        top: 420,
                        left: 35,
                        width: 40,
                        MaxLineNumber: 1,
                        // breakWord: true,
                        // bolder: true
                    },
                    {
                        type: 'text',
                        content: goods.retail_price,
                        fontSize: 30,
                        lineHeight: 30,
                        color: '#e93237',
                        textAlign: 'left',
                        top: 410,
                        left: 50,
                        width: 200,
                        MaxLineNumber: 1,
                        // breakWord: true,
                        // bolder: true
                    },
                    {
                        type: 'image',
                        url: qrcodeUrl,
                        top: 470,
                        left: 115.5,
                        width: 144,
                        height: 144
                    },
                    {
                        type: 'text',
                        content: '长按识别小程序',
                        fontSize: 16,
                        color: '#383549',
                        textAlign: 'center',
                        top: 625,
                        left: 187.5,
                        lineHeight: 20,
                        MaxLineNumber: 1,
                        breakWord: true,
                        width: 200
                    }
                ]
            }
        })
    },

    eventDrawWithoutImage(qrcodeUrl) {
        let that = this;
        let goods = that.data.goods;
        console.log('=== 生成无商品图片的分享图 ===');

        that.setData({
            painting: {
                width: 375,
                height: 667,
                background:'#fff',
                clear: true,
                views: [
                    {
                        type: 'rect',
                        top: 0,
                        left: 0,
                        width: 375,
                        height: 667,
                        background:'#fff'
                    },
                    {
                        type: 'rect',
                        top: 40,
                        left: 40,
                        width: 305,
                        height: 305,
                        background: '#f1f1f1'
                    },
                    {
                        type: 'text',
                        content: '商品图片',
                        fontSize: 24,
                        color: '#999',
                        textAlign: 'center',
                        top: 180,
                        left: 187.5,
                        width: 200
                    },
                    {
                        type: 'text',
                        content: goods.name,
                        fontSize: 18,
                        lineHeight: 22,
                        color: '#383549',
                        textAlign: 'left',
                        top: 360,
                        left: 35,
                        width: 305,
                        MaxLineNumber: 2,
                        breakWord: true,
                    },
                    {
                        type: 'text',
                        content: '¥',
                        fontSize: 18,
                        lineHeight: 16,
                        color: '#e93237',
                        textAlign: 'left',
                        top: 420,
                        left: 35,
                        width: 40,
                        MaxLineNumber: 1,
                    },
                    {
                        type: 'text',
                        content: goods.retail_price,
                        fontSize: 30,
                        lineHeight: 30,
                        color: '#e93237',
                        textAlign: 'left',
                        top: 410,
                        left: 50,
                        width: 200,
                        MaxLineNumber: 1,
                    },
                    {
                        type: 'image',
                        url: qrcodeUrl,
                        top: 470,
                        left: 115.5,
                        width: 144,
                        height: 144
                    },
                    {
                        type: 'text',
                        content: '长按识别小程序',
                        fontSize: 16,
                        color: '#383549',
                        textAlign: 'center',
                        top: 625,
                        left: 187.5,
                        lineHeight: 20,
                        MaxLineNumber: 1,
                        breakWord: true,
                        width: 200
                    }
                ]
            }
        })
    },
    eventSave() {
        wx.saveImageToPhotosAlbum({
            filePath: this.data.shareImage,
            success(res) {
                wx.showToast({
                    title: '保存图片成功',
                    icon: 'success',
                    duration: 2000
                })
            }
        })
    },
    eventGetImage(event) {
        wx.hideLoading()
        const {
            tempFilePath,
            errMsg
        } = event.detail

        console.log('=== 画布绘制结果 ===');
        console.log('错误信息:', errMsg);
        console.log('图片路径:', tempFilePath);

        if (errMsg === 'canvasdrawer:ok') {
            console.log('✅ 分享图生成成功');
            this.setData({
                shareImage: tempFilePath
            })
        } else {
            console.log('❌ 分享图生成失败:', errMsg);
            wx.showToast({
                title: '分享图生成失败',
                icon: 'none',
                duration: 2000
            });
        }
    }
})