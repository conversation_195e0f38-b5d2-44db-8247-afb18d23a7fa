{"version": 3, "sources": ["..\\..\\..\\src\\common\\config\\crontab.js"], "names": ["path", "require", "module", "exports", "interval", "enable", "immediate", "handle"], "mappings": "AAAA,MAAMA,OAAOC,QAAQ,MAAR,CAAb;AACAC,OAAOC,OAAP,GAAiB,CAAC;AAChBC,WAAU,KADM;AAEhBC,SAAQ,IAFQ;AAGhBC,YAAW,IAHK;AAIhBC,SAAQ;AAJQ,CAAD,EAMhB;AACCH,WAAU,KADX;AAECC,SAAQ,KAFT;AAGCC,YAAW,IAHZ;AAICC,SAAQ;AAJT,CANgB,EAYhB;AACCH,WAAU,KADX,EACkB;AACjBC,SAAQ,IAFT;AAGCC,YAAW,KAHZ;AAICC,SAAQ;AAJT,CAZgB,CAAjB", "file": "..\\..\\..\\src\\common\\config\\crontab.js", "sourcesContent": ["const path = require('path');\r\nmodule.exports = [{\r\n\t\tinterval: '60s',\r\n\t\tenable: true,\r\n\t\timmediate: true,\r\n\t\thandle: \"crontab/timetask\"\r\n\t},\r\n\t{\r\n\t\tinterval: '10s',\r\n\t\tenable: false,\r\n\t\timmediate: true,\r\n\t\thandle: \"crontab/resetSql\"\r\n\t},\r\n\t{\r\n\t\tinterval: '60s', // 每分钟执行一次\r\n\t\tenable: true,\r\n\t\timmediate: false,\r\n\t\thandle: \"crontab/flash_sale_scheduler\"\r\n\t}\r\n]\r\n"]}