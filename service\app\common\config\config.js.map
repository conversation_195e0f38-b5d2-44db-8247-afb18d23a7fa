{"version": 3, "sources": ["..\\..\\..\\src\\common\\config\\config.js"], "names": ["module", "exports", "default_module", "port", "weixin", "appid", "secret", "mch_id", "partner_key", "notify_url", "refund_notify_url", "cert_path", "key_path", "shipping_management", "enabled", "auto_sync", "msg_jump_path", "express", "appkey", "request_url", "mianexpress", "print_url", "ip_server_url", "qiniu", "access_key", "secret_key", "bucket", "domain", "qiniuHttps", "zoneNum", "aliexpress", "url", "appcode", "templateId", "deliveryId"], "mappings": "AAAA;AACAA,OAAOC,OAAP,GAAiB;AACbC,oBAAgB,KADH;AAEbC,UAAM,IAFO,EAED;AACZC,YAAQ;AACJC,eAAO,oBADH,EACyB;AAC7BC,gBAAQ,kCAFJ,EAEwC;AAC5CC,gBAAQ,YAHJ,EAGkB;AACtBC,qBAAa,kCAJT,EAI6C;AACjDC,oBAAY,uCALR,EAKiD;AACrDC,2BAAmB,8CANf,EAM+D;;AAEnE;AACAC,mBAAW,2BATP,EASoC;AACxCC,kBAAU,0BAVN,EAUoC;;AAExC;AACAC,6BAAqB;AACjBC,qBAAS,IADQ,EACF;AACfC,uBAAW,IAFM,EAEA;AACjBC,2BAAe,qBAHE,CAGoB;AAHpB;AAbjB,KAHK;AAsBbC,aAAS;AACL;AACA;AACA;AACAZ,eAAO,UAJF,EAIc;AACnBa,gBAAQ,0BALH,EAK+B;AACpCC,qBAAa;AANR,KAtBI;AA8BdC,iBAAY;AACPf,eAAO,QADA,EACU;AACjBa,gBAAQ,6BAFD,EAEgC;AACvCC,qBAAa,kDAHN;AAIPE,mBAAW,8EAJJ;AAKPC,uBAAc;AALP,KA9BE;AAqCbC,WAAO;AACHC,oBAAY,0CADT,EAC0D;AAC7DC,oBAAY,0CAFT,EAEwD;AAC3DC,gBAAQ,SAHL,EAGmC;AACtCC,gBAAQ,yBAJL,CAIuD;AAJvD,KArCM;AA2Cb;AACAC,gBAAY;AACRJ,oBAAY,0CADJ,EACgD;AACxDC,oBAAY,0CAFJ,EAEgD;AACxDC,gBAAQ,SAHA,EAGW;AACnBC,gBAAQ,yBAJA,EAI2B;AACnC;AACAE,iBAAS,CAND,CAMI;AANJ,KA5CC;AAoDbC,gBAAW;AACP;AACAC,aAAI,yCAFG,EAEwC;AAC/CC,iBAAS,kCAHF,CAGsC;AAHtC,KApDE;AAyDhBC,gBAAW;AACVC,oBAAW,sCADD,CACwC;AADxC;AAzDK,CAAjB", "file": "..\\..\\..\\src\\common\\config\\config.js", "sourcesContent": ["// default config\nmodule.exports = {\n    default_module: 'api',\n    port: 8360, //服务端口，可自定义\n    weixin: {\n        appid: 'wx919ca2ec612e6ecb', // 小程序 appid\n        secret: '4ad505935a4bf61f235efc303bf1e555', // 小程序密钥\n        mch_id: '1718527327', // 商户帐号ID\n        partner_key: 'APIV3JackC554487624asde32223UUsq', // 微信支付密钥\n        notify_url: 'https://ht.rxkjsdj.com/api/pay/notify', // 微信支付异步通知\n        refund_notify_url: 'https://ht.rxkjsdj.com/api/pay/refund_notify', // 微信退款异步通知\n\n        // API证书配置（退款接口需要）\n        cert_path: './cert/apiclient_cert.pem', // API证书路径\n        key_path: './cert/apiclient_key.pem',   // API私钥路径\n\n        // 订单发货管理配置\n        shipping_management: {\n            enabled: true, // 是否启用订单发货管理\n            auto_sync: true, // 是否自动同步发货信息\n            msg_jump_path: '/pages/order/detail' // 消息跳转路径\n        }\n    },\n    express: {\n        // 已废弃，之后考虑改回来，做成和阿里云的物流查询可以切换，方便大家的使用\n        // 免费的，但是顺丰的话，要配合快递鸟的电子面单\n        // 快递物流信息查询使用的是快递鸟接口，申请地址：http://www.kdniao.com/ \n        appid: '12312312', // 对应快递鸟用户后台 用户ID\n        appkey: '123123123123123123123123', // 对应快递鸟用户后台 API key\n        request_url: 'http://api.kdniao.com/Ebusiness/EbusinessOrderHandle.aspx'\n    },\n   mianexpress:{\n        appid: '123123', // 对应快递鸟用户后台 用户ID\n        appkey: '123123-4e61236-94cb5297309a', // 对应快递鸟用户后台 API key\n        request_url: 'http://testapi.kdniao.com:8081/api/EOrderService',\n        print_url: 'http://sandboxapi.kdniao.com:8080/kdniaosandbox/gateway/exterfaceInvoke.json',\n        ip_server_url:'http://www.kdniao.com/External/GetIp.aspx'\n    },\n    qiniu: {\n        access_key: '4Dey1S8bxqZjgpoo6h2r_W2MFkirvedK7vysREcP',      // 在七牛密钥管理中获取\n        secret_key: 'T8dG712sMGPNRmUiqyT9RfTC3WSEki44YOS0_I0b',    // 在七牛密钥管理中获取\n        bucket: 'zhangli',                    // 请填自己的bucket的名称\n        domain: 'http://img.rxkjsdj.com/'                         // 请填自己的domain域名\n    },\n    // 在七牛新建一个https的空间，这个是用来存储分享图片的https图片，对应的是goods表中的https_pic_url\n    qiniuHttps: {\n        access_key: '4Dey1S8bxqZjgpoo6h2r_W2MFkirvedK7vysREcP', // 在七牛密钥管理中获取\n        secret_key: 'T8dG712sMGPNRmUiqyT9RfTC3WSEki44YOS0_I0b', // 在七牛密钥管理中获取\n        bucket: 'zhangli', // 自己设置的\n        domain: 'http://img.rxkjsdj.com/', // 自己设置，例如：'https://img.你的域名.com/',别忘了这个”/“\n        // https://developer.qiniu.com/kodo/manual/1671/region-endpoint\n        zoneNum: 1  // 这个自己根据地区设置：华东 0；华北 1；华南 2； 北美 3；东南亚 4\n    },\n    aliexpress:{\n        // https://market.aliyun.com/products/56928004/cmapi021863.html?spm=5176.730005.productlist.d_cmapi021863.6ba73524uQjLqE&innerSource=search_%E5%85%A8%E5%9B%BD%E5%BF%AB%E9%80%92%E7%89%A9%E6%B5%81%E6%9F%A5%E8%AF%A2-%E5%BF%AB%E9%80%92%E6%9F%A5%E8%AF%A2%E6%8E%A5%E5%8F%A3#sku=yuncode1586300000\n        url:'http://wuliu.market.alicloudapi.com/kdi', //阿里云的物流查询api，收费的\n        appcode: '67ba959aa7a84c37bb4ece2bc8683fbb' ,// 阿里云后台获取\n    },\n\ttemplateId:{\n\t\tdeliveryId:'w6AMCJ0nVWTsFasdasdgnlNlmCf9TTDmG6_U' // 模板id。在订阅消息里设置好后就可以得到\n\t},\n};\n"]}