const mysql = require('mysql2/promise');

async function updateDatabase() {
  try {
    const connection = await mysql.createConnection({
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: '19841020',
      database: 'hiolabsDB'
    });
    
    console.log('开始更新分享来源数据...');
    
    // 更新推广访问表
    const result1 = await connection.execute('UPDATE hiolabs_promotion_visits SET share_source = ? WHERE share_source = ?', ['other', 'miniprogram']);
    console.log('更新推广访问表 miniprogram -> other:', result1[0].affectedRows, '条记录');
    
    const result2 = await connection.execute('UPDATE hiolabs_promotion_visits SET share_source = ? WHERE share_source = ?', ['other', 'wechat']);
    console.log('更新推广访问表 wechat -> other:', result2[0].affectedRows, '条记录');
    
    const result3 = await connection.execute('UPDATE hiolabs_promotion_visits SET share_source = ? WHERE share_source = ?', ['other', 'link']);
    console.log('更新推广访问表 link -> other:', result3[0].affectedRows, '条记录');
    
    // 更新推广订单表
    const result4 = await connection.execute('UPDATE hiolabs_promotion_orders SET share_source = ? WHERE share_source = ?', ['other', 'miniprogram']);
    console.log('更新推广订单表 miniprogram -> other:', result4[0].affectedRows, '条记录');
    
    const result5 = await connection.execute('UPDATE hiolabs_promotion_orders SET share_source = ? WHERE share_source = ?', ['other', 'wechat']);
    console.log('更新推广订单表 wechat -> other:', result5[0].affectedRows, '条记录');
    
    const result6 = await connection.execute('UPDATE hiolabs_promotion_orders SET share_source = ? WHERE share_source = ?', ['other', 'link']);
    console.log('更新推广订单表 link -> other:', result6[0].affectedRows, '条记录');
    
    console.log('分享来源数据更新完成');
    
    // 查看更新结果
    const [visits] = await connection.execute('SELECT share_source, COUNT(*) as count FROM hiolabs_promotion_visits GROUP BY share_source');
    console.log('推广访问表分享来源统计:', visits);
    
    const [orders] = await connection.execute('SELECT share_source, COUNT(*) as count FROM hiolabs_promotion_orders GROUP BY share_source');
    console.log('推广订单表分享来源统计:', orders);
    
    await connection.end();
    console.log('✅ 数据库更新完成');
    
  } catch (error) {
    console.error('❌ 数据库更新失败:', error);
  }
}

updateDatabase();
