{"version": 3, "sources": ["..\\..\\..\\src\\api\\service\\token.js"], "names": ["jwt", "require", "secret", "module", "exports", "think", "Service", "getUserId", "token", "console", "log", "substring", "result", "parse", "Object", "keys", "userId", "user_id", "id", "isEmpty", "user", "model", "where", "find", "nickname", "error", "length", "verify", "err", "message", "create", "userInfo", "sign", "getUserInfo", "field"], "mappings": ";;AAAA,MAAMA,MAAMC,QAAQ,cAAR,CAAZ;AACA,MAAMC,SAAS,kCAAf;AACAC,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,OAApB,CAA4B;AACzC;;;AAGMC,aAAN,CAAgBC,KAAhB,EAAuB;AAAA;;AAAA;AACnBC,oBAAQC,GAAR,CAAY,mBAAZ;AACAD,oBAAQC,GAAR,CAAY,QAAZ,EAAsBF,QAAQA,MAAMG,SAAN,CAAgB,CAAhB,EAAmB,EAAnB,IAAyB,KAAjC,GAAyC,MAA/D;;AAEA,gBAAI,CAACH,KAAL,EAAY;AACRC,wBAAQC,GAAR,CAAY,SAAZ;AACA,uBAAO,CAAP;AACH;;AAED,kBAAME,SAAS,MAAKC,KAAL,CAAWL,KAAX,CAAf;AACAC,oBAAQC,GAAR,CAAY,UAAZ,EAAwBE,MAAxB;AACAH,oBAAQC,GAAR,CAAY,YAAZ,EAA0BI,OAAOC,IAAP,CAAYH,UAAU,EAAtB,CAA1B;;AAEA;AACA,kBAAMI,SAAUJ,UAAUA,OAAOK,OAAlB,IAA+BL,UAAUA,OAAOM,EAAhD,IAAuD,CAAtE;AACAT,oBAAQC,GAAR,CAAY,YAAZ,EAA0BM,MAA1B;;AAEA,gBAAIX,MAAMc,OAAN,CAAcP,MAAd,KAAyB,CAACI,MAA1B,IAAoCA,UAAU,CAAlD,EAAqD;AACjDP,wBAAQC,GAAR,CAAY,mBAAZ;AACA,uBAAO,CAAP;AACH;;AAED;AACA,gBAAI;AACA,sBAAMU,OAAO,MAAM,MAAKC,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AACxCJ,wBAAIF;AADoC,iBAAzB,EAEhBO,IAFgB,EAAnB;;AAIA,oBAAIlB,MAAMc,OAAN,CAAcC,IAAd,CAAJ,EAAyB;AACrBX,4BAAQC,GAAR,CAAY,oBAAZ,EAAkCM,MAAlC;AACA,2BAAO,CAAP;AACH;;AAEDP,wBAAQC,GAAR,CAAY,iBAAZ,EAA+BM,MAA/B,EAAuC,KAAvC,EAA8CI,KAAKI,QAAnD;AACA,uBAAOR,MAAP;AACH,aAZD,CAYE,OAAOS,KAAP,EAAc;AACZhB,wBAAQC,GAAR,CAAY,UAAZ,EAAwBe,KAAxB;AACA,uBAAO,CAAP;AACH;AAtCkB;AAuCtB;AACDZ,UAAML,KAAN,EAAa;AACT,YAAIA,KAAJ,EAAW;AACP,gBAAI;AACAC,wBAAQC,GAAR,CAAY,iBAAZ;AACAD,wBAAQC,GAAR,CAAY,UAAZ,EAAwBF,MAAMkB,MAA9B;AACAjB,wBAAQC,GAAR,CAAY,SAAZ,EAAuBR,MAAvB;AACA,sBAAMU,SAASZ,IAAI2B,MAAJ,CAAWnB,KAAX,EAAkBN,MAAlB,CAAf;AACAO,wBAAQC,GAAR,CAAY,UAAZ,EAAwBE,MAAxB;AACA,uBAAOA,MAAP;AACH,aAPD,CAOE,OAAOgB,GAAP,EAAY;AACVnB,wBAAQC,GAAR,CAAY,iBAAZ;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBkB,IAAIC,OAAzB;AACApB,wBAAQC,GAAR,CAAY,QAAZ,EAAsBF,MAAMG,SAAN,CAAgB,CAAhB,EAAmB,EAAnB,IAAyB,KAA/C;AACA,uBAAO,IAAP;AACH;AACJ;AACDF,gBAAQC,GAAR,CAAY,cAAZ;AACA,eAAO,IAAP;AACH;AACEoB,UAAN,CAAaC,QAAb,EAAuB;AAAA;AACnB,kBAAMvB,QAAQR,IAAIgC,IAAJ,CAASD,QAAT,EAAmB7B,MAAnB,CAAd;AACA,mBAAOM,KAAP;AAFmB;AAGtB;AACD;;;AAGMyB,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAMjB,SAAS,MAAM,OAAKT,SAAL,EAArB;AACA,gBAAIS,UAAU,CAAd,EAAiB;AACb,uBAAO,IAAP;AACH;AACD,kBAAMe,WAAW,MAAM,OAAKV,KAAL,CAAW,MAAX,EAAmBa,KAAnB,CAAyB,CAAC,IAAD,EAAO,UAAP,EAAmB,UAAnB,EAA+B,QAA/B,EAAyC,QAAzC,EAAmD,UAAnD,CAAzB,EAAyFZ,KAAzF,CAA+F;AAClHJ,oBAAIF;AAD8G,aAA/F,EAEpBO,IAFoB,EAAvB;AAGA,mBAAOlB,MAAMc,OAAN,CAAcY,QAAd,IAA0B,IAA1B,GAAiCA,QAAxC;AARgB;AASnB;AACQJ,UAAN,GAAe;AAAA;;AAAA;AACX,kBAAMf,SAAS,MAAM,OAAKC,KAAL,EAArB;AACA,gBAAIR,MAAMc,OAAN,CAAcP,MAAd,CAAJ,EAA2B;AACvB,uBAAO,KAAP;AACH;AACD,mBAAO,IAAP;AALW;AAMd;AAtFwC,CAA7C", "file": "..\\..\\..\\src\\api\\service\\token.js", "sourcesContent": ["const jwt = require('jsonwebtoken');\nconst secret = 'sdfsdfsdf123123!ASDasdasdasdasda';\nmodule.exports = class extends think.Service {\n    /**\n     * 根据header中的x-hioshop-token值获取用户id\n     */\n    async getUserId(token) {\n        console.log('=== Token验证开始 ===');\n        console.log('Token:', token ? token.substring(0, 20) + '...' : 'null');\n\n        if (!token) {\n            console.log('Token为空');\n            return 0;\n        }\n\n        const result = this.parse(token);\n        console.log('JWT解析结果:', result);\n        console.log('JWT中的所有字段:', Object.keys(result || {}));\n\n        // 检查user_id在哪个字段中\n        const userId = (result && result.user_id) || (result && result.id) || 0;\n        console.log('提取的userId:', userId);\n\n        if (think.isEmpty(result) || !userId || userId <= 0) {\n            console.log('JWT解析失败或user_id无效');\n            return 0;\n        }\n\n        // 验证数据库中用户是否存在\n        try {\n            const user = await this.model('user').where({\n                id: userId\n            }).find();\n\n            if (think.isEmpty(user)) {\n                console.log('数据库中用户不存在，user_id:', userId);\n                return 0;\n            }\n\n            console.log('用户验证成功，user_id:', userId, '昵称:', user.nickname);\n            return userId;\n        } catch (error) {\n            console.log('数据库查询失败:', error);\n            return 0;\n        }\n    }\n    parse(token) {\n        if (token) {\n            try {\n                console.log('=== JWT解析开始 ===');\n                console.log('Token长度:', token.length);\n                console.log('Secret:', secret);\n                const result = jwt.verify(token, secret);\n                console.log('JWT解析成功:', result);\n                return result;\n            } catch (err) {\n                console.log('=== JWT解析失败 ===');\n                console.log('错误信息:', err.message);\n                console.log('Token:', token.substring(0, 50) + '...');\n                return null;\n            }\n        }\n        console.log('Token为空，无法解析');\n        return null;\n    }\n\tasync create(userInfo) {\n\t    const token = jwt.sign(userInfo, secret);\n\t    return token;\n\t}\n\t/**\n\t * 根据值获取用户信息\n\t */\n\tasync getUserInfo() {\n\t    const userId = await this.getUserId();\n\t    if (userId <= 0) {\n\t        return null;\n\t    }\n\t    const userInfo = await this.model('user').field(['id', 'username', 'nickname', 'gender', 'avatar', 'birthday']).where({\n\t        id: userId\n\t    }).find();\n\t    return think.isEmpty(userInfo) ? null : userInfo;\n\t}\n    async verify() {\n        const result = await this.parse();\n        if (think.isEmpty(result)) {\n            return false;\n        }\n        return true;\n    }\n};"]}