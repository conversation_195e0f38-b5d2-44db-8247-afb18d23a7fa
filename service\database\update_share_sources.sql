-- 更新分享来源一致性
-- 将分享来源统一为三种：好友分享、二维码分享、其他

-- 1. 更新推广访问表的分享来源数据
-- 将现有的 miniprogram 改为 other，qrcode 保持不变
UPDATE `hiolabs_promotion_visits` 
SET `share_source` = 'other' 
WHERE `share_source` = 'miniprogram';

UPDATE `hiolabs_promotion_visits` 
SET `share_source` = 'other' 
WHERE `share_source` = 'wechat';

UPDATE `hiolabs_promotion_visits` 
SET `share_source` = 'other' 
WHERE `share_source` = 'link';

-- qrcode 保持不变
-- friend 如果存在也保持不变

-- 2. 更新推广订单表的分享来源数据
UPDATE `hiolabs_promotion_orders` 
SET `share_source` = 'other' 
WHERE `share_source` = 'miniprogram';

UPDATE `hiolabs_promotion_orders` 
SET `share_source` = 'other' 
WHERE `share_source` = 'wechat';

UPDATE `hiolabs_promotion_orders` 
SET `share_source` = 'other' 
WHERE `share_source` = 'link';

-- 3. 如果存在分享记录表，也进行更新
-- 修改分享记录表的枚举值
ALTER TABLE `hiolabs_share_records` 
MODIFY COLUMN `share_type` enum('friend','qrcode','other') NOT NULL DEFAULT 'friend' 
COMMENT '分享类型：friend好友分享，qrcode二维码分享，other其他';

-- 更新分享记录表的数据
UPDATE `hiolabs_share_records` 
SET `share_type` = 'other' 
WHERE `share_type` = 'moments';

-- 4. 添加索引优化查询性能
ALTER TABLE `hiolabs_promotion_visits` 
ADD INDEX `idx_share_source` (`share_source`) IF NOT EXISTS;

ALTER TABLE `hiolabs_promotion_orders` 
ADD INDEX `idx_share_source` (`share_source`) IF NOT EXISTS;

-- 5. 显示更新结果
SELECT '=== 分享来源更新完成 ===' as result;

SELECT 
    share_source,
    COUNT(*) as count
FROM `hiolabs_promotion_visits` 
GROUP BY share_source
ORDER BY count DESC;

SELECT 
    share_source,
    COUNT(*) as count
FROM `hiolabs_promotion_orders` 
GROUP BY share_source
ORDER BY count DESC;

SELECT '✅ 分享来源一致性更新完成' as result;
