const Base = require('./base.js');
const moment = require('moment');

module.exports = class extends Base {
    /**
     * 获取分销池商品列表（所有商品都在池子里，显示分销状态）
     * @return {Promise} []
     */
    async goodsListAction() {
        const page = this.get('page') || 1;
        const size = this.get('size') || 20;
        const name = this.get('name') || '';
        const categoryId = this.get('category_id') || '';
        const isOnSale = this.get('is_on_sale') || '';
        const distributionStatus = this.get('distribution_status') || ''; // all, distributed, undistributed

        const model = this.model('goods');
        let whereMap = {
            is_delete: 0
        };

        // 搜索条件
        if (name) {
            whereMap.name = ['like', `%${name}%`];
        }
        if (categoryId) {
            whereMap.category_id = categoryId;
        }
        if (isOnSale !== '') {
            whereMap.is_on_sale = isOnSale;
        }

        // 获取所有商品（分销池概念：所有商品都在池子里）
        const data = await model.where(whereMap)
            .field('id,category_id,name,goods_brief,retail_price,goods_number,sell_volume,is_on_sale,https_pic_url,list_pic_url,goods_unit,sort_order')
            .order(['sort_order asc', 'id desc'])
            .page(page, size)
            .countSelect();

        // 批量获取分销配置，提高性能
        const goodsIds = data.data.map(item => item.id);
        const distributionConfigs = await this.model('goods_distribution')
            .where({ goods_id: ['IN', goodsIds] })
            .select();

        // 创建分销配置映射
        const configMap = {};
        distributionConfigs.forEach(config => {
            configMap[config.goods_id] = config;
        });

        // 批量获取分类信息
        const categoryIds = [...new Set(data.data.map(item => item.category_id))];
        const categories = await this.model('category')
            .where({ id: ['IN', categoryIds] })
            .field('id,name')
            .select();

        const categoryMap = {};
        categories.forEach(cat => {
            categoryMap[cat.id] = cat.name;
        });

        // 处理每个商品的分销信息
        const processedData = [];
        for (const item of data.data) {
            // 设置分类名称
            item.category_name = categoryMap[item.category_id] || '未分类';

            // 设置分销配置（体现分销池概念：每个商品都有分销状态）
            const distributionConfig = configMap[item.id];
            if (distributionConfig) {
                // 商品已投入分销池
                item.is_distributed = distributionConfig.is_distributed;
                item.commission_rate = distributionConfig.commission_rate || 0;
                item.commission_type = distributionConfig.commission_type || 'default';
                item.personal_rate = distributionConfig.personal_rate || 0;
                item.level1_rate = distributionConfig.level1_rate || 0;
                item.level2_rate = distributionConfig.level2_rate || 0;
                item.team_leader_rate = distributionConfig.team_leader_rate || 0;
                item.min_level_required = distributionConfig.min_level_required || 1;
                item.distribution_status = distributionConfig.is_distributed ? 'active' : 'inactive';
                item.estimated_commission = (parseFloat(item.retail_price) * distributionConfig.commission_rate / 100).toFixed(2);
                item.in_pool_time = distributionConfig.add_time ? moment.unix(distributionConfig.add_time).format('YYYY-MM-DD HH:mm:ss') : '';
            } else {
                // 商品未入池（没有分销配置记录）
                item.is_distributed = null;
                item.commission_rate = 0;
                item.commission_type = 'none';
                item.personal_rate = 0;
                item.level1_rate = 0;
                item.level2_rate = 0;
                item.team_leader_rate = 0;
                item.min_level_required = 1;
                item.distribution_status = 'pending';
                item.estimated_commission = '0.00';
                item.in_pool_time = '';
            }

            // 计算总佣金率
            item.total_commission_rate = (
                parseFloat(item.personal_rate || 0) +
                parseFloat(item.level1_rate || 0) +
                parseFloat(item.level2_rate || 0) +
                parseFloat(item.team_leader_rate || 0)
            ).toFixed(2);

            // 格式化时间
            item.add_time = item.add_time ? moment.unix(item.add_time).format('YYYY-MM-DD HH:mm:ss') : moment().format('YYYY-MM-DD HH:mm:ss');

            // 根据分销状态筛选
            if (distributionStatus === 'distributed' && item.is_distributed !== 1) {
                continue;
            }
            if (distributionStatus === 'undistributed' && item.is_distributed === 1) {
                continue;
            }

            processedData.push(item);
        }

        // 更新数据
        data.data = processedData;
        data.count = processedData.length;

        // 调试：输出前几个商品的分销状态
        if (processedData.length > 0) {
            console.log('=== 商品列表API返回数据 ===');
            console.log('总商品数:', processedData.length);
            processedData.slice(0, 3).forEach(item => {
                console.log(`商品${item.id}: is_distributed=${item.is_distributed}, commission_rate=${item.commission_rate}`);
            });
        }

        return this.success(data);
    }
    
    /**
     * 获取商品分类列表
     * @return {Promise} []
     */
    async categoriesAction() {
        const model = this.model('category');
        const data = await model.where({
            is_show: 1
        }).field('id,name,front_desc,parent_id').order(['sort_order asc']).select();
        
        return this.success(data);
    }
    
    /**
     * 获取分销配置
     * @return {Promise} []
     */
    async configAction() {
        const goodsId = this.get('goods_id');
        
        if (goodsId) {
            // 获取单个商品的分销配置
            const config = await this.model('goods_distribution').where({
                goods_id: goodsId
            }).find();
            return this.success(config || {});
        } else {
            // 获取所有分销配置
            const configs = await this.model('goods_distribution').select();
            return this.success(configs);
        }
    }
    
    /**
     * 投入/移出分销池操作
     * @return {Promise} []
     */
    async statusAction() {
        const goodsId = this.post('goods_id');
        const isDistributed = this.post('is_distributed');
        const commissionRate = this.post('commission_rate') || 0;
        const commissionType = this.post('commission_type') || 'default';
        const personalRate = this.post('personal_rate') || 0;
        const level1Rate = this.post('level1_rate') || 0;
        const level2Rate = this.post('level2_rate') || 0;
        const teamLeaderRate = this.post('team_leader_rate') || 0;
        const minLevelRequired = this.post('min_level_required') || 1;

        console.log('=== 分销状态设置API ===');
        console.log('商品ID:', goodsId);
        console.log('分销状态:', isDistributed);
        console.log('佣金数据:', { commissionRate, personalRate, level1Rate, level2Rate, teamLeaderRate });

        if (!goodsId) {
            return this.fail('商品ID不能为空');
        }

        // 验证商品是否存在
        const goods = await this.model('goods').where({
            id: goodsId,
            is_delete: 0
        }).find();

        if (!goods) {
            return this.fail('商品不存在');
        }

        const model = this.model('goods_distribution');
        const currentTime = parseInt(new Date().getTime() / 1000);

        // 检查是否已存在配置
        const existingConfig = await model.where({
            goods_id: goodsId
        }).find();

        console.log('现有配置查询结果:', existingConfig);
        console.log('existingConfig是否为空:', !existingConfig || Object.keys(existingConfig).length === 0);

        const configData = {
            goods_id: goodsId,
            is_distributed: isDistributed,
            commission_rate: commissionRate,
            commission_type: commissionType,
            personal_rate: personalRate,
            level1_rate: level1Rate,
            level2_rate: level2Rate,
            team_leader_rate: teamLeaderRate,
            min_level_required: minLevelRequired,
            update_time: currentTime
        };

        let operationText = '';
        if (isDistributed == 1) {
            operationText = '投入分销池';
            // 前端必须提供佣金比例，不再自动设置
            if (personalRate == 0) {
                return this.fail('投入分销池时必须设置佣金比例');
            }
            configData.commission_rate = personalRate; // 使用个人佣金作为主佣金
        } else {
            operationText = '移出分销池';
        }

        console.log('配置数据:', configData);

        // 修复判断逻辑：检查是否真的存在有效配置
        const hasValidConfig = existingConfig && Object.keys(existingConfig).length > 0 && existingConfig.id;

        if (hasValidConfig) {
            // 更新现有配置
            console.log('更新现有配置...');
            const updateResult = await model.where({
                goods_id: goodsId
            }).update(configData);
            console.log('更新结果:', updateResult);

            // 验证更新是否成功
            const verifyConfig = await model.where({
                goods_id: goodsId
            }).find();
            console.log('更新后验证配置:', verifyConfig);
        } else {
            // 创建新配置（商品首次进入分销池管理）
            console.log('创建新配置...');
            configData.add_time = currentTime;
            const addResult = await model.add(configData);
            console.log('创建结果:', addResult);

            // 验证创建是否成功
            const verifyConfig = await model.where({
                goods_id: goodsId
            }).find();
            console.log('创建后验证配置:', verifyConfig);
        }

        // 记录操作日志
        await this.logDistributionOperation(goodsId, operationText, configData);

        console.log('操作完成:', operationText);

        return this.success({
            message: `商品${operationText}成功`,
            goods_name: goods.name,
            operation: operationText,
            config: configData
        });
    }
    
    /**
     * 设置商品佣金
     * @return {Promise} []
     */
    async commissionAction() {
        const goodsId = this.post('goods_id');
        const commissionRate = this.post('commission_rate');
        const commissionType = this.post('commission_type') || 'custom';
        
        if (!goodsId || commissionRate === undefined) {
            return this.fail('参数不完整');
        }
        
        const model = this.model('goods_distribution');
        
        // 检查是否已存在配置
        const existingConfig = await model.where({
            goods_id: goodsId
        }).find();
        
        const configData = {
            commission_rate: commissionRate,
            commission_type: commissionType,
            update_time: parseInt(new Date().getTime() / 1000)
        };
        
        if (existingConfig) {
            // 更新现有配置
            await model.where({
                goods_id: goodsId
            }).update(configData);
        } else {
            // 创建新配置
            configData.goods_id = goodsId;
            configData.is_distributed = 1; // 设置佣金时自动加入分销
            configData.add_time = parseInt(new Date().getTime() / 1000);
            await model.add(configData);
        }
        
        return this.success('佣金设置成功');
    }
    
    /**
     * 获取分销池统计数据
     * @return {Promise} []
     */
    async statsAction() {
        const model = this.model('goods_distribution');

        // 获取所有商品总数（分销池总容量）
        const totalGoods = await this.model('goods').where({
            is_delete: 0
        }).count();

        // 获取已投入分销池的商品数量
        const totalDistributed = await model.where({
            is_distributed: 1
        }).count();

        // 获取待投入分销池的商品数量（未配置分销的商品）
        const configuredGoods = await model.count();
        const pendingGoods = totalGoods - configuredGoods;

        // 获取已配置但未激活的商品数量
        const inactiveGoods = await model.where({
            is_distributed: 0
        }).count();

        // 计算总佣金池和平均佣金率
        const distributedGoods = await model.alias('gd')
            .join({
                table: 'goods',
                join: 'left',
                as: 'g',
                on: ['gd.goods_id', 'g.id']
            })
            .where({
                'gd.is_distributed': 1,
                'g.is_delete': 0
            })
            .field('g.retail_price, gd.commission_rate, gd.personal_rate, gd.level1_rate, gd.level2_rate, gd.team_leader_rate')
            .select();

        let totalCommission = 0;
        let totalCommissionRate = 0;
        distributedGoods.forEach(item => {
            const itemCommission = parseFloat(item.retail_price) * parseFloat(item.commission_rate || 0) / 100;
            totalCommission += itemCommission;
            totalCommissionRate += parseFloat(item.commission_rate || 0);
        });

        // 获取高级商品数量（需要2级以上权限的商品）
        const premiumProducts = await model.where({
            is_distributed: 1,
            min_level_required: ['>=', 2]
        }).count();

        // 获取热门商品数量（销量>50的分销商品）
        const hotProducts = await model.alias('gd')
            .join({
                table: 'goods',
                join: 'left',
                as: 'g',
                on: ['gd.goods_id', 'g.id']
            })
            .where({
                'gd.is_distributed': 1,
                'g.is_delete': 0,
                'g.sell_volume': ['>', 50]
            })
            .count();

        // 获取分类统计
        const categoryStats = await model.alias('gd')
            .join({
                table: 'goods',
                join: 'left',
                as: 'g',
                on: ['gd.goods_id', 'g.id']
            })
            .join({
                table: 'category',
                join: 'left',
                as: 'c',
                on: ['g.category_id', 'c.id']
            })
            .where({
                'gd.is_distributed': 1,
                'g.is_delete': 0
            })
            .field('c.id as category_id, c.name as category_name, COUNT(*) as count')
            .group('c.id')
            .order('count DESC')
            .limit(5)
            .select();

        return this.success({
            // 分销池基础统计
            poolStats: {
                totalCapacity: totalGoods,           // 分销池总容量
                activeProducts: totalDistributed,    // 已激活分销的商品
                inactiveProducts: inactiveGoods,     // 已配置但未激活的商品
                pendingProducts: pendingGoods,       // 待配置的商品
                utilizationRate: totalGoods > 0 ? ((totalDistributed / totalGoods) * 100).toFixed(2) : 0 // 分销池利用率
            },

            // 佣金统计
            commissionStats: {
                totalCommissionPool: totalCommission.toFixed(2),
                averageCommissionRate: distributedGoods.length > 0 ?
                    (totalCommissionRate / distributedGoods.length).toFixed(2) : 0,
                estimatedMonthlyPayout: (totalCommission * 0.1).toFixed(2) // 预估月度支出
            },

            // 商品分级统计
            productTiers: {
                basicProducts: totalDistributed - premiumProducts,  // 基础商品
                premiumProducts: premiumProducts,                   // 高级商品
                hotProducts: hotProducts                           // 热门商品
            },

            // 分类统计
            topCategories: categoryStats,

            // 兼容旧版本字段
            totalProducts: totalGoods,
            distributedProducts: totalDistributed,
            undistributedProducts: totalGoods - totalDistributed,
            totalCommission: totalCommission.toFixed(2),
            hotProducts: hotProducts
        });
    }
    
    /**
     * 批量投入/移出分销池操作
     * @return {Promise} []
     */
    async batchAction() {
        const goodsIds = this.post('goods_ids'); // 商品ID数组
        const isDistributed = this.post('is_distributed');
        const commissionRate = this.post('commission_rate') || 0;
        const commissionType = this.post('commission_type') || 'default';
        const personalRate = this.post('personal_rate') || 0;
        const level1Rate = this.post('level1_rate') || 0;
        const level2Rate = this.post('level2_rate') || 0;
        const teamLeaderRate = this.post('team_leader_rate') || 0;
        const minLevelRequired = this.post('min_level_required') || 1;
        const templateId = this.post('template_id'); // 佣金模板ID

        if (!goodsIds || !Array.isArray(goodsIds) || goodsIds.length === 0) {
            return this.fail('请选择要操作的商品');
        }

        // 验证商品是否存在
        const validGoods = await this.model('goods').where({
            id: ['IN', goodsIds],
            is_delete: 0
        }).field('id,name,retail_price').select();

        if (validGoods.length !== goodsIds.length) {
            return this.fail('部分商品不存在或已删除');
        }

        const model = this.model('goods_distribution');
        const currentTime = parseInt(new Date().getTime() / 1000);

        let successCount = 0;
        let failCount = 0;
        const results = [];

        for (const goods of validGoods) {
            try {
                // 检查是否已存在配置
                const existingConfig = await model.where({
                    goods_id: goods.id
                }).find();

                let configData = {
                    goods_id: goods.id,
                    is_distributed: isDistributed,
                    commission_rate: commissionRate,
                    commission_type: commissionType,
                    personal_rate: personalRate,
                    level1_rate: level1Rate,
                    level2_rate: level2Rate,
                    team_leader_rate: teamLeaderRate,
                    min_level_required: minLevelRequired,
                    update_time: currentTime
                };

                // 如果使用模板，应用模板配置
                if (templateId) {
                    const template = await this.model('commission_templates').where({
                        id: templateId
                    }).find();

                    if (template) {
                        configData.personal_rate = template.personal_rate;
                        configData.level1_rate = template.level1_rate;
                        configData.level2_rate = template.level2_rate;
                        configData.team_leader_rate = template.team_leader_rate;
                        configData.min_level_required = template.min_level_required;
                        configData.commission_rate = template.personal_rate; // 主佣金率使用个人推广率
                        configData.commission_type = 'template';
                    }
                }

                // 如果是投入分销池且没有设置佣金，使用默认佣金
                if (isDistributed == 1 && configData.personal_rate == 0 && !templateId) {
                    const price = parseFloat(goods.retail_price);
                    if (price <= 100) {
                        configData.personal_rate = 8.0;
                        configData.level1_rate = 3.0;
                        configData.level2_rate = 1.0;
                        configData.team_leader_rate = 2.0;
                    } else if (price <= 500) {
                        configData.personal_rate = 10.0;
                        configData.level1_rate = 4.0;
                        configData.level2_rate = 2.0;
                        configData.team_leader_rate = 3.0;
                    } else {
                        configData.personal_rate = 15.0;
                        configData.level1_rate = 6.0;
                        configData.level2_rate = 3.0;
                        configData.team_leader_rate = 5.0;
                    }
                    configData.commission_rate = configData.personal_rate;
                    configData.commission_type = 'auto';
                }

                if (existingConfig) {
                    // 更新现有配置
                    await model.where({
                        goods_id: goods.id
                    }).update(configData);
                } else {
                    // 创建新配置
                    configData.add_time = currentTime;
                    await model.add(configData);
                }

                successCount++;
                results.push({
                    goods_id: goods.id,
                    goods_name: goods.name,
                    status: 'success',
                    operation: isDistributed == 1 ? '投入分销池' : '移出分销池'
                });

            } catch (error) {
                failCount++;
                results.push({
                    goods_id: goods.id,
                    goods_name: goods.name,
                    status: 'failed',
                    error: error.message
                });
            }
        }

        // 记录批量操作日志
        await this.logBatchOperation(goodsIds, isDistributed == 1 ? '批量投入分销池' : '批量移出分销池', {
            successCount,
            failCount,
            templateId
        });

        return this.success({
            message: `批量操作完成：成功${successCount}个，失败${failCount}个`,
            successCount,
            failCount,
            results
        });
    }
    
    /**
     * 获取佣金规则
     * @return {Promise} []
     */
    async rulesAction() {
        // 这里可以从配置表或者直接返回默认规则
        const rules = [
            { minPrice: 0, maxPrice: 100, rate: 8, description: '0-100元商品' },
            { minPrice: 100, maxPrice: 500, rate: 10, description: '100-500元商品' },
            { minPrice: 500, maxPrice: 999999, rate: 15, description: '500元以上商品' }
        ];

        return this.success({
            defaultRate: 10,
            rules: rules
        });
    }

    /**
     * 获取分销员列表
     * @return {Promise} []
     */
    async distributorsAction() {
        const page = this.get('page') || 1;
        const size = this.get('size') || 20;
        const search = this.get('search') || '';
        const level = this.get('level') || '';
        const status = this.get('status') || '';
        const period = this.get('period') || 'daily';

        const model = this.model('distributors');
        let whereMap = {};

        // 搜索条件
        if (search) {
            const userModel = this.model('user');
            const users = await userModel.where({
                'nickname': ['like', `%${search}%`]
            }).field('id').select();

            if (users.length > 0) {
                whereMap.user_id = ['IN', users.map(u => u.id)];
            } else {
                whereMap.user_id = 0; // 没有匹配的用户
            }
        }

        if (status) {
            if (status === 'active') {
                whereMap.is_active = 1;
                whereMap.audit_status = 1;
            } else if (status === 'inactive') {
                whereMap.is_active = 0;
            } else if (status === 'pending') {
                whereMap.audit_status = 0;
            }
        }

        // 获取分销员列表
        const distributors = await model.where(whereMap)
            .field('id,user_id,distributor_code,level_id,total_sales,total_commission,customer_count,is_active,audit_status,join_time')
            .order(['total_sales desc', 'total_commission desc'])
            .page(page, size)
            .countSelect();

        // 获取用户信息和等级信息
        for (const distributor of distributors.data) {
            // 获取用户信息
            const userInfo = await this.model('user').where({
                id: distributor.user_id
            }).field('nickname,mobile,avatar').find();

            if (userInfo) {
                distributor.name = userInfo.nickname || '未设置';
                distributor.phone = userInfo.mobile ? userInfo.mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : '未绑定';
                distributor.avatar = userInfo.avatar || `https://ui-avatars.com/api/?name=${distributor.name}&background=random&size=128`;
            }

            // 获取等级信息
            const levelInfo = await this.model('distributor_levels').where({
                id: distributor.level_id
            }).find();

            distributor.level = levelInfo ? levelInfo.level_name : '普通分销商';
            distributor.commissionRate = levelInfo ? (parseFloat(levelInfo.commission_bonus) + 10) : 10; // 基础佣金10% + 等级加成

            // 格式化数据
            distributor.sales = parseFloat(distributor.total_sales || 0).toFixed(2);
            distributor.commission = parseFloat(distributor.total_commission || 0).toFixed(2);
            distributor.joinTime = moment.unix(distributor.join_time).format('YYYY-MM-DD');
            distributor.status = distributor.is_active && distributor.audit_status === 1 ? 'active' :
                                 !distributor.is_active ? 'inactive' : 'pending';
        }

        return this.success(distributors);
    }

    /**
     * 获取分销员统计数据
     * @return {Promise} []
     */
    async distributorStatsAction() {
        try {
            // 总分销员数
            const totalDistributors = await this.model('distributors').where({
                audit_status: 1
            }).count();

            // 团长数量（假设level_id为4的是团长）
            const teamLeaders = await this.model('distributors').where({
                audit_status: 1,
                level_id: 4
            }).count();

            // 总佣金
            const totalCommissionResult = await this.model('distributors').where({
                audit_status: 1
            }).sum('total_commission');
            const totalCommission = parseFloat(totalCommissionResult || 0).toFixed(2);

            // 总销售额
            const totalSalesResult = await this.model('distributors').where({
                audit_status: 1
            }).sum('total_sales');
            const totalSales = parseFloat(totalSalesResult || 0).toFixed(2);

            const stats = {
                totalDistributors: totalDistributors,
                teamLeaders: teamLeaders,
                totalCommission: totalCommission,
                totalSales: totalSales
            };

            return this.success(stats);
        } catch (error) {
            console.error('获取分销员统计失败:', error);
            return this.fail('获取统计数据失败');
        }
    }

    /**
     * 获取分销员详情
     * @return {Promise} []
     */
    async distributorDetailAction() {
        const distributorId = this.get('id');

        if (!distributorId) {
            return this.fail('分销员ID不能为空');
        }

        try {
            const distributor = await this.model('distributors').where({
                id: distributorId
            }).find();

            if (!distributor) {
                return this.fail('分销员不存在');
            }

            // 获取用户信息
            const userInfo = await this.model('user').where({
                id: distributor.user_id
            }).find();

            // 获取等级信息
            const levelInfo = await this.model('distributor_levels').where({
                id: distributor.level_id
            }).find();

            // 组装详情数据
            const detail = {
                ...distributor,
                name: userInfo ? userInfo.nickname : '未设置',
                phone: userInfo ? userInfo.mobile : '未绑定',
                avatar: userInfo ? userInfo.avatar : '',
                level: levelInfo ? levelInfo.level_name : '普通分销商',
                commissionRate: levelInfo ? (parseFloat(levelInfo.commission_bonus) + 10) : 10,
                joinTime: moment.unix(distributor.join_time).format('YYYY-MM-DD HH:mm:ss')
            };

            return this.success(detail);
        } catch (error) {
            console.error('获取分销员详情失败:', error);
            return this.fail('获取分销员详情失败');
        }
    }

    /**
     * 更新分销员状态
     * @return {Promise} []
     */
    async updateDistributorStatusAction() {
        const distributorId = this.post('distributor_id');
        const isActive = this.post('is_active');

        if (!distributorId) {
            return this.fail('分销员ID不能为空');
        }

        try {
            await this.model('distributors').where({
                id: distributorId
            }).update({
                is_active: isActive ? 1 : 0,
                update_time: parseInt(new Date().getTime() / 1000)
            });

            return this.success('状态更新成功');
        } catch (error) {
            console.error('更新分销员状态失败:', error);
            return this.fail('更新状态失败');
        }
    }

    /**
     * 获取数据概览
     * @return {Promise} []
     */
    async overviewAction() {
        try {
            const period = this.get('period') || '30';
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(endDate.getDate() - parseInt(period));

            const startTimestamp = parseInt(startDate.getTime() / 1000);
            const endTimestamp = parseInt(endDate.getTime() / 1000);

            // 获取总销售额
            const totalSalesResult = await this.model('order').where({
                add_time: ['BETWEEN', startTimestamp, endTimestamp],
                order_status: ['>=', 300]
            }).sum('actual_price');
            const totalSales = parseFloat(totalSalesResult || 0);

            // 获取总佣金
            const totalCommissionResult = await this.model('distributors').sum('total_commission');
            const totalCommission = parseFloat(totalCommissionResult || 0);

            // 获取活跃分销员数
            const activeDistributors = await this.model('distributors').where({
                is_active: 1,
                audit_status: 1
            }).count();

            // 计算转化率（简化计算）
            const totalOrders = await this.model('order').where({
                add_time: ['BETWEEN', startTimestamp, endTimestamp]
            }).count();
            const conversionRate = totalOrders > 0 ? ((totalSales / totalOrders) * 0.01).toFixed(1) : 0;

            // 计算增长率（模拟数据）
            const overview = {
                totalSales: totalSales,
                salesGrowth: Math.floor(Math.random() * 20) + 5, // 5-25%
                totalCommission: totalCommission,
                commissionGrowth: Math.floor(Math.random() * 15) + 3, // 3-18%
                activeDistributors: activeDistributors,
                distributorGrowth: Math.floor(Math.random() * 25) + 10, // 10-35%
                conversionRate: conversionRate,
                conversionGrowth: Math.floor(Math.random() * 5) + 1, // 1-6%
                totalDistributors: await this.model('distributors').count()
            };

            return this.success(overview);
        } catch (error) {
            console.error('获取数据概览失败:', error);
            return this.fail('获取数据概览失败');
        }
    }

    /**
     * 获取热门商品
     * @return {Promise} []
     */
    async topProductsAction() {
        try {
            const period = this.get('period') || '30';
            const limit = this.get('limit') || 5;

            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(endDate.getDate() - parseInt(period));

            const startTimestamp = parseInt(startDate.getTime() / 1000);
            const endTimestamp = parseInt(endDate.getTime() / 1000);

            // 获取热门商品（按销量排序）
            const products = await this.model('goods')
                .field('id,name,list_pic_url,sell_volume,retail_price')
                .where({
                    is_delete: 0,
                    is_on_sale: 1
                })
                .order(['sell_volume desc'])
                .limit(limit)
                .select();

            // 计算每个商品的收入和佣金
            for (const product of products) {
                const revenue = parseFloat(product.retail_price) * product.sell_volume;
                const commission = revenue * 0.12; // 假设12%佣金率

                product.image = product.list_pic_url;
                product.sales = product.sell_volume;
                product.revenue = revenue.toFixed(2);
                product.commission = commission.toFixed(2);
            }

            return this.success(products);
        } catch (error) {
            console.error('获取热门商品失败:', error);
            return this.fail('获取热门商品失败');
        }
    }

    /**
     * 获取顶级分销员
     * @return {Promise} []
     */
    async topDistributorsAction() {
        try {
            const period = this.get('period') || '30';
            const limit = this.get('limit') || 5;

            // 获取顶级分销员（按销售额排序）
            const distributors = await this.model('distributors')
                .field('id,user_id,level_id,total_sales,total_commission')
                .where({
                    audit_status: 1,
                    is_active: 1
                })
                .order(['total_sales desc'])
                .limit(limit)
                .select();

            // 获取用户信息和等级信息
            for (const distributor of distributors) {
                // 获取用户信息
                const userInfo = await this.model('user').where({
                    id: distributor.user_id
                }).field('nickname,avatar').find();

                // 获取等级信息
                const levelInfo = await this.model('distributor_levels').where({
                    id: distributor.level_id
                }).find();

                distributor.name = userInfo ? userInfo.nickname : '未设置';
                distributor.avatar = userInfo ? userInfo.avatar : `https://ui-avatars.com/api/?name=${distributor.name}&background=random&size=128`;
                distributor.level = levelInfo ? levelInfo.level_name : '普通分销商';
                distributor.sales = parseFloat(distributor.total_sales || 0).toFixed(2);
                distributor.commission = parseFloat(distributor.total_commission || 0).toFixed(2);
            }

            return this.success(distributors);
        } catch (error) {
            console.error('获取顶级分销员失败:', error);
            return this.fail('获取顶级分销员失败');
        }
    }

    /**
     * 获取图表数据
     * @return {Promise} []
     */
    async chartAction() {
        try {
            const period = this.get('period') || '30';
            const type = this.get('type') || 'sales';

            // 生成模拟图表数据
            const data = [];
            const labels = [];
            const now = new Date();

            for (let i = parseInt(period) - 1; i >= 0; i--) {
                const date = new Date(now);
                date.setDate(date.getDate() - i);
                labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));

                if (type === 'sales') {
                    data.push(Math.floor(Math.random() * 50000) + 10000);
                } else if (type === 'orders') {
                    data.push(Math.floor(Math.random() * 100) + 20);
                } else if (type === 'commission') {
                    data.push(Math.floor(Math.random() * 5000) + 1000);
                }
            }

            return this.success({
                labels: labels,
                data: data
            });
        } catch (error) {
            console.error('获取图表数据失败:', error);
            return this.fail('获取图表数据失败');
        }
    }

    /**
     * 获取实时数据
     * @return {Promise} []
     */
    async realtimeAction() {
        try {
            const today = new Date();
            const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
            const todayStartTimestamp = parseInt(todayStart.getTime() / 1000);
            const todayEndTimestamp = parseInt(new Date().getTime() / 1000);

            // 今日订单数
            const todayOrders = await this.model('order').where({
                add_time: ['BETWEEN', todayStartTimestamp, todayEndTimestamp]
            }).count();

            // 今日收入
            const todayRevenueResult = await this.model('order').where({
                add_time: ['BETWEEN', todayStartTimestamp, todayEndTimestamp],
                order_status: ['>=', 300]
            }).sum('actual_price');
            const todayRevenue = parseFloat(todayRevenueResult || 0);

            // 在线用户数（模拟）
            const activeUsers = Math.floor(Math.random() * 50) + 20;

            const realTimeData = {
                todayOrders: todayOrders,
                todayRevenue: todayRevenue,
                activeUsers: activeUsers
            };

            return this.success(realTimeData);
        } catch (error) {
            console.error('获取实时数据失败:', error);
            return this.fail('获取实时数据失败');
        }
    }

    /**
     * 获取分销池概览（所有商品的分销状态概览）
     * @return {Promise} []
     */
    async poolOverviewAction() {
        const page = this.get('page') || 1;
        const size = this.get('size') || 50;

        // 获取所有商品及其分销状态
        const goodsData = await this.model('goods')
            .alias('g')
            .join({
                table: 'goods_distribution',
                join: 'left',
                as: 'gd',
                on: ['g.id', 'gd.goods_id']
            })
            .join({
                table: 'category',
                join: 'left',
                as: 'c',
                on: ['g.category_id', 'c.id']
            })
            .where({
                'g.is_delete': 0
            })
            .field('g.id,g.name,g.retail_price,g.sell_volume,g.is_on_sale,g.list_pic_url,c.name as category_name,gd.is_distributed,gd.commission_rate,gd.personal_rate,gd.level1_rate,gd.level2_rate,gd.add_time as pool_join_time')
            .order(['g.sort_order asc', 'g.id desc'])
            .page(page, size)
            .countSelect();

        // 处理数据，标记商品在分销池中的状态
        for (const item of goodsData.data) {
            if (item.is_distributed === null) {
                item.pool_status = 'not_in_pool';      // 不在分销池中
                item.pool_status_text = '未加入';
            } else if (item.is_distributed === 1) {
                item.pool_status = 'active';           // 在分销池中且激活
                item.pool_status_text = '已激活';
            } else {
                item.pool_status = 'inactive';         // 在分销池中但未激活
                item.pool_status_text = '已配置未激活';
            }

            // 计算预计佣金
            item.estimated_commission = item.commission_rate ?
                (parseFloat(item.retail_price) * item.commission_rate / 100).toFixed(2) : '0.00';

            // 格式化加入分销池时间
            item.pool_join_time = item.pool_join_time ?
                moment.unix(item.pool_join_time).format('YYYY-MM-DD HH:mm:ss') : '';
        }

        return this.success(goodsData);
    }

    /**
     * 记录分销操作日志
     * @param {number} goodsId 商品ID
     * @param {string} operation 操作类型
     * @param {object} data 操作数据
     */
    async logDistributionOperation(goodsId, operation, data) {
        try {
            const logData = {
                goods_id: goodsId,
                operation: operation,
                operation_data: JSON.stringify(data),
                operator_id: this.ctx.state.userInfo ? this.ctx.state.userInfo.id : 0,
                create_time: parseInt(new Date().getTime() / 1000)
            };

            // 这里可以记录到操作日志表
            console.log('分销操作日志:', logData);
        } catch (error) {
            console.error('记录分销操作日志失败:', error);
        }
    }

    /**
     * 记录批量操作日志
     * @param {array} goodsIds 商品ID数组
     * @param {string} operation 操作类型
     * @param {object} summary 操作摘要
     */
    async logBatchOperation(goodsIds, operation, summary) {
        try {
            const logData = {
                goods_ids: goodsIds.join(','),
                operation: operation,
                operation_summary: JSON.stringify(summary),
                operator_id: this.ctx.state.userInfo ? this.ctx.state.userInfo.id : 0,
                create_time: parseInt(new Date().getTime() / 1000)
            };

            console.log('批量操作日志:', logData);
        } catch (error) {
            console.error('记录批量操作日志失败:', error);
        }
    }

    /**
     * 智能推荐分销配置
     * @return {Promise} []
     */
    async recommendConfigAction() {
        const goodsId = this.get('goods_id');

        if (!goodsId) {
            return this.fail('商品ID不能为空');
        }

        // 获取商品信息
        const goods = await this.model('goods').where({
            id: goodsId,
            is_delete: 0
        }).find();

        if (!goods) {
            return this.fail('商品不存在');
        }

        const price = parseFloat(goods.retail_price);
        const sellVolume = parseInt(goods.sell_volume || 0);

        // 根据商品价格和销量智能推荐配置
        let recommendedConfig = {};

        if (price <= 100) {
            recommendedConfig = {
                personal_rate: 8.0,
                level1_rate: 3.0,
                level2_rate: 1.0,
                team_leader_rate: 2.0,
                min_level_required: 1,
                reason: '低价商品，适合新手分销员推广'
            };
        } else if (price <= 500) {
            recommendedConfig = {
                personal_rate: 10.0,
                level1_rate: 4.0,
                level2_rate: 2.0,
                team_leader_rate: 3.0,
                min_level_required: sellVolume > 50 ? 1 : 2,
                reason: '中档商品，平衡佣金与门槛'
            };
        } else {
            recommendedConfig = {
                personal_rate: 15.0,
                level1_rate: 6.0,
                level2_rate: 3.0,
                team_leader_rate: 5.0,
                min_level_required: 3,
                reason: '高价商品，需要有经验的分销员'
            };
        }

        // 如果是热门商品，可以适当降低佣金
        if (sellVolume > 100) {
            recommendedConfig.personal_rate *= 0.9;
            recommendedConfig.level1_rate *= 0.9;
            recommendedConfig.level2_rate *= 0.9;
            recommendedConfig.team_leader_rate *= 0.9;
            recommendedConfig.reason += '，热门商品可适当降低佣金';
        }

        // 四舍五入到一位小数
        Object.keys(recommendedConfig).forEach(key => {
            if (typeof recommendedConfig[key] === 'number' && key.includes('rate')) {
                recommendedConfig[key] = Math.round(recommendedConfig[key] * 10) / 10;
            }
        });

        return this.success({
            goods_info: {
                id: goods.id,
                name: goods.name,
                price: goods.retail_price,
                sell_volume: goods.sell_volume
            },
            recommended_config: recommendedConfig
        });
    }
};
