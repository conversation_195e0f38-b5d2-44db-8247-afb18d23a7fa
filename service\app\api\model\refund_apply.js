function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

module.exports = class extends think.Model {
    /**
     * 获取售后申请表名
     */
    get tableName() {
        return 'hiolabs_refund_apply';
    }

    /**
     * 获取售后申请详情
     * @param {number} applyId 申请ID
     * @returns {Promise<Object>}
     */
    getApplyDetail(applyId) {
        var _this = this;

        return _asyncToGenerator(function* () {
            const apply = yield _this.where({ id: applyId }).find();
            if (think.isEmpty(apply)) {
                return {};
            }

            // 解析图片JSON
            if (apply.images) {
                try {
                    apply.images = JSON.parse(apply.images);
                } catch (e) {
                    apply.images = [];
                }
            } else {
                apply.images = [];
            }

            return apply;
        })();
    }

    /**
     * 获取用户的售后申请列表
     * @param {number} userId 用户ID
     * @param {number} page 页码
     * @param {number} size 每页数量
     * @returns {Promise<Object>}
     */
    getUserApplyList(userId, page = 1, size = 10) {
        var _this2 = this;

        return _asyncToGenerator(function* () {
            const list = yield _this2.where({ user_id: userId }).order('apply_time DESC').page(page, size).countSelect();

            // 处理图片字段
            if (list.data && list.data.length > 0) {
                list.data.forEach(function (item) {
                    if (item.images) {
                        try {
                            item.images = JSON.parse(item.images);
                        } catch (e) {
                            item.images = [];
                        }
                    } else {
                        item.images = [];
                    }
                });
            }

            return list;
        })();
    }

    /**
     * 更新申请状态
     * @param {number} applyId 申请ID
     * @param {string} status 新状态
     * @param {string} memo 备注
     * @returns {Promise<number>}
     */
    updateApplyStatus(applyId, status, memo = '') {
        var _this3 = this;

        return _asyncToGenerator(function* () {
            const updateData = {
                status: status,
                updated_at: new Date()
            };

            if (status === 'processing') {
                updateData.process_time = parseInt(new Date().getTime() / 1000);
            } else if (status === 'completed') {
                updateData.complete_time = parseInt(new Date().getTime() / 1000);
            }

            if (memo) {
                updateData.admin_memo = memo;
            }

            return yield _this3.where({ id: applyId }).update(updateData);
        })();
    }

    /**
     * 拒绝申请
     * @param {number} applyId 申请ID
     * @param {string} rejectReason 拒绝原因
     * @returns {Promise<number>}
     */
    rejectApply(applyId, rejectReason) {
        var _this4 = this;

        return _asyncToGenerator(function* () {
            return yield _this4.where({ id: applyId }).update({
                status: 'rejected',
                reject_reason: rejectReason,
                process_time: parseInt(new Date().getTime() / 1000),
                updated_at: new Date()
            });
        })();
    }

    /**
     * 获取申请统计
     * @returns {Promise<Object>}
     */
    getApplyStats() {
        var _this5 = this;

        return _asyncToGenerator(function* () {
            const stats = yield _this5.field('status, COUNT(*) as count').group('status').select();

            const result = {
                pending: 0,
                processing: 0,
                approved: 0,
                rejected: 0,
                completed: 0,
                total: 0
            };

            stats.forEach(function (item) {
                result[item.status] = parseInt(item.count);
                result.total += parseInt(item.count);
            });

            return result;
        })();
    }
};