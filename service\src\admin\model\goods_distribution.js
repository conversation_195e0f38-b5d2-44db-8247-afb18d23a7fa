module.exports = class extends think.Model {
    /**
     * 获取商品的分销配置
     * @param goodsId
     * @returns {Promise.<*>}
     */
    async getDistributionConfig(goodsId) {
        const config = await this.where({
            goods_id: goodsId
        }).find();
        return config;
    }

    /**
     * 设置商品分销配置
     * @param goodsId
     * @param config
     * @returns {Promise.<*>}
     */
    async setDistributionConfig(goodsId, config) {
        const existingConfig = await this.where({
            goods_id: goodsId
        }).find();

        const configData = {
            goods_id: goodsId,
            is_distributed: config.is_distributed || 0,
            commission_rate: config.commission_rate || 0,
            commission_type: config.commission_type || 'default',
            update_time: parseInt(new Date().getTime() / 1000)
        };

        if (existingConfig) {
            // 更新现有配置
            return await this.where({
                goods_id: goodsId
            }).update(configData);
        } else {
            // 创建新配置
            configData.add_time = parseInt(new Date().getTime() / 1000);
            return await this.add(configData);
        }
    }

    /**
     * 获取所有已分销的商品
     * @returns {Promise.<*>}
     */
    async getDistributedGoods() {
        const goods = await this.where({
            is_distributed: 1
        }).select();
        return goods;
    }

    /**
     * 批量设置分销状态
     * @param goodsIds
     * @param isDistributed
     * @param commissionRate
     * @param commissionType
     * @returns {Promise.<*>}
     */
    async batchSetDistribution(goodsIds, isDistributed, commissionRate = 0, commissionType = 'default') {
        const currentTime = parseInt(new Date().getTime() / 1000);
        const results = [];

        for (const goodsId of goodsIds) {
            const existingConfig = await this.where({
                goods_id: goodsId
            }).find();

            const configData = {
                goods_id: goodsId,
                is_distributed: isDistributed,
                commission_rate: commissionRate,
                commission_type: commissionType,
                update_time: currentTime
            };

            if (existingConfig) {
                // 更新现有配置
                const result = await this.where({
                    goods_id: goodsId
                }).update(configData);
                results.push(result);
            } else {
                // 创建新配置
                configData.add_time = currentTime;
                const result = await this.add(configData);
                results.push(result);
            }
        }

        return results;
    }

    /**
     * 计算商品预计佣金
     * @param goodsId
     * @returns {Promise.<*>}
     */
    async calculateEstimatedCommission(goodsId) {
        const config = await this.where({
            goods_id: goodsId
        }).find();

        if (!config || !config.is_distributed) {
            return 0;
        }

        const goods = await this.model('goods').where({
            id: goodsId
        }).find();

        if (!goods) {
            return 0;
        }

        const estimatedCommission = parseFloat(goods.retail_price) * config.commission_rate / 100;
        return estimatedCommission.toFixed(2);
    }

    /**
     * 获取分销统计数据
     * @returns {Promise.<*>}
     */
    async getDistributionStats() {
        // 已分销商品数
        const distributedCount = await this.where({
            is_distributed: 1
        }).count();

        // 总佣金池
        const distributedGoods = await this.model('goods')
            .alias('g')
            .join({
                table: 'goods_distribution',
                join: 'inner',
                as: 'd',
                on: ['g.id', 'd.goods_id']
            })
            .where({
                'g.is_delete': 0,
                'd.is_distributed': 1
            })
            .field('g.retail_price,d.commission_rate')
            .select();

        let totalCommission = 0;
        for (const item of distributedGoods) {
            totalCommission += parseFloat(item.retail_price) * item.commission_rate / 100;
        }

        return {
            distributedCount: distributedCount,
            totalCommission: totalCommission.toFixed(2)
        };
    }
};
