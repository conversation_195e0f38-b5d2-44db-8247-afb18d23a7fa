const Base = require('./base.js');
const moment = require('moment');

module.exports = class extends Base {
	async checkLoginAction(){
		if(think.userId == 0){
			return this.fail(404,'请登录');
		}
	}
    async indexAction() {
        const goodsOnsale = await this.model('goods').where({is_on_sale: 1,is_delete:0}).count();
        const orderToDelivery = await this.model('order').where({order_status: 300}).count();
        const user = await this.model('user').count();
        let data = await this.model('settings').field('countdown').find();
        let timestamp = data.countdown;
        let info = {
            user: user,
            goodsOnsale: goodsOnsale,
            timestamp:timestamp,
            orderToDelivery: orderToDelivery,
        }
        return this.success(info);
    }
    async getQiniuTokenAction(){
        const TokenSerivce = this.service('qiniu'); // 服务里返回token
        let data = await TokenSerivce.getQiniuToken(); // 取得token值 goods
        let qiniuToken = data.uploadToken;
        let domain = data.domain;
        let info ={
            token:qiniuToken,
            url:domain
        };
        return this.success(info);
    }
    async mainAction() {
        const index = this.get('pindex');
        console.log('index:' + index);
        let todayTimeStamp = new Date(new Date().setHours(0, 0, 0, 0)) / 1000; //今天零点的时间戳
        let yesTimeStamp = todayTimeStamp - 86400; //昨天零点的时间戳
        let sevenTimeStamp = todayTimeStamp - 86400 * 7; //7天前零点的时间戳
        let thirtyTimeStamp = todayTimeStamp - 86400 * 30; //30天前零点的时间戳
        let newUser = 1;
        let oldUser = 0;
        let addCart = 0;
        let addOrderNum = 0;
        let addOrderSum = 0;
        let payOrderNum = 0;
        let payOrderSum = 0;
        let newData = [];
        let oldData = [];
        if (index == 0) {
            newData = await this.model('user').where({
                id: ['>', 0],
                register_time: ['>', todayTimeStamp]
            }).select();
            newUser = newData.length;
            for(const item of newData){
                item.nickname = Buffer.from(item.nickname, 'base64').toString();
                // 处理头像URL
                if (!item.avatar || item.avatar === '' || item.avatar.startsWith('wxfile://')) {
                    // 如果没有头像或者是微信临时文件，使用默认头像
                    item.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';
                } else if (!item.avatar.startsWith('http://') && !item.avatar.startsWith('https://')) {
                    // 如果是相对路径，补充完整的域名
                    item.avatar = `https://ht.rxkjsdj.com${item.avatar}`;
                }
            }
            oldData = await this.model('user').where({
                id: ['>', 0],
                register_time: ['<', todayTimeStamp],
                last_login_time: ['>', todayTimeStamp]
            }).select();
            for(const item of oldData){
                item.nickname = Buffer.from(item.nickname, 'base64').toString();
                // 处理头像URL
                if (!item.avatar || item.avatar === '' || item.avatar.startsWith('wxfile://')) {
                    // 如果没有头像或者是微信临时文件，使用默认头像
                    item.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';
                } else if (!item.avatar.startsWith('http://') && !item.avatar.startsWith('https://')) {
                    // 如果是相对路径，补充完整的域名
                    item.avatar = `https://ht.rxkjsdj.com${item.avatar}`;
                }
            }
            oldUser = oldData.length;
            addCart = await this.model('cart').where({is_delete: 0, add_time: ['>', todayTimeStamp]}).count();
            addOrderNum = await this.model('order').where({
                is_delete: 0,
                add_time: ['>', todayTimeStamp]
            }).count();
            addOrderSum = await this.model('order').where({
                is_delete: 0,
                add_time: ['>', todayTimeStamp]
            }).sum('actual_price');
            payOrderNum = await this.model('order').where({
                is_delete: 0,
                add_time: ['>', todayTimeStamp],
                order_status: ['IN', [201, 802, 300, 301]]
            }).count();
            payOrderSum = await this.model('order').where({
                is_delete: 0,
                add_time: ['>', todayTimeStamp],
                order_status: ['IN', [201, 802, 300, 301]]
            }).sum('actual_price');
        }
        else if (index == 1) {
            newData = await this.model('user').where({
                id: ['>', 0],
                register_time: ['BETWEEN', yesTimeStamp, todayTimeStamp]
            }).select();
            for(const item of newData){
                item.nickname = Buffer.from(item.nickname, 'base64').toString();
                // 处理头像URL
                if (!item.avatar || item.avatar === '' || item.avatar.startsWith('wxfile://')) {
                    // 如果没有头像或者是微信临时文件，使用默认头像
                    item.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';
                } else if (!item.avatar.startsWith('http://') && !item.avatar.startsWith('https://')) {
                    // 如果是相对路径，补充完整的域名
                    item.avatar = `https://ht.rxkjsdj.com${item.avatar}`;
                }
            }
            newUser = newData.length;
            oldData = await this.model('user').where({
                id: ['>', 0],
                register_time: ['<', yesTimeStamp],
                last_login_time: ['BETWEEN', yesTimeStamp, todayTimeStamp]
            }).select();
            for(const item of oldData){
                item.nickname = Buffer.from(item.nickname, 'base64').toString();
                // 处理头像URL
                if (!item.avatar || item.avatar === '' || item.avatar.startsWith('wxfile://')) {
                    // 如果没有头像或者是微信临时文件，使用默认头像
                    item.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';
                } else if (!item.avatar.startsWith('http://') && !item.avatar.startsWith('https://')) {
                    // 如果是相对路径，补充完整的域名
                    item.avatar = `https://ht.rxkjsdj.com${item.avatar}`;
                }
            }
            oldUser = oldData.length;
            addCart = await this.model('cart').where({
                is_delete: 0,
                add_time: ['BETWEEN', yesTimeStamp, todayTimeStamp]
            }).count();
            addOrderNum = await this.model('order').where({
                is_delete: 0,
                add_time: ['BETWEEN', yesTimeStamp, todayTimeStamp]
            }).count();
            addOrderSum = await this.model('order').where({
                is_delete: 0,
                add_time: ['BETWEEN', yesTimeStamp, todayTimeStamp]
            }).sum('actual_price');
            payOrderNum = await this.model('order').where({
                is_delete: 0,
                add_time: ['BETWEEN', yesTimeStamp, todayTimeStamp],
                order_status: ['IN', [201, 802, 300, 301]]
            }).count();
            console.log('------------321----------');
            console.log(payOrderNum);
            console.log('-----------3321-----------');
            payOrderSum = await this.model('order').where({
                is_delete: 0,
                add_time: ['BETWEEN', yesTimeStamp, todayTimeStamp],
                order_status: ['IN', [201, 802, 300, 301]]
            }).sum('actual_price');
            console.log('-----------123-----------');
            console.log(payOrderSum);
            console.log('-----------123-----------');

        }
        else if (index == 2) {
            newData = await this.model('user').where({
                id: ['>', 0],
                register_time: ['>', sevenTimeStamp]
            }).select();
            for(const item of newData){
                item.nickname = Buffer.from(item.nickname, 'base64').toString();
                // 处理头像URL
                if (!item.avatar || item.avatar === '' || item.avatar.startsWith('wxfile://')) {
                    // 如果没有头像或者是微信临时文件，使用默认头像
                    item.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';
                } else if (!item.avatar.startsWith('http://') && !item.avatar.startsWith('https://')) {
                    // 如果是相对路径，补充完整的域名
                    item.avatar = `https://ht.rxkjsdj.com${item.avatar}`;
                }
            }
            newUser = newData.length;
            oldData = await this.model('user').where({
                id: ['>', 0],
                register_time: ['<', sevenTimeStamp],
                last_login_time: ['>', sevenTimeStamp]
            }).select();
            for(const item of oldData){
                item.nickname = Buffer.from(item.nickname, 'base64').toString();
                // 处理头像URL
                if (!item.avatar || item.avatar === '' || item.avatar.startsWith('wxfile://')) {
                    // 如果没有头像或者是微信临时文件，使用默认头像
                    item.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';
                } else if (!item.avatar.startsWith('http://') && !item.avatar.startsWith('https://')) {
                    // 如果是相对路径，补充完整的域名
                    item.avatar = `https://ht.rxkjsdj.com${item.avatar}`;
                }
            }
            oldUser = oldData.length;
            addCart = await this.model('cart').where({
                is_delete: 0,
                add_time: ['>', sevenTimeStamp]
            }).count();
            addOrderNum = await this.model('order').where({
                is_delete: 0,
                add_time: ['>', sevenTimeStamp]
            }).count();
            addOrderSum = await this.model('order').where({
                is_delete: 0,
                add_time: ['>', sevenTimeStamp]
            }).sum('actual_price');
            payOrderNum = await this.model('order').where({
                is_delete: 0,
                add_time: ['>', sevenTimeStamp],
                order_status: ['IN', [201, 802, 300, 301]]
            }).count();
            payOrderSum = await this.model('order').where({
                is_delete: 0,
                add_time: ['>', sevenTimeStamp],
                order_status: ['IN', [201, 802, 300, 301]]
            }).sum('actual_price');
        }
        else if (index == 3) {
            newData = await this.model('user').where({
                id: ['>', 0],
                register_time: ['>', thirtyTimeStamp]
            }).select();
            for(const item of newData){
                item.nickname = Buffer.from(item.nickname, 'base64').toString();
                // 处理头像URL
                if (!item.avatar || item.avatar === '' || item.avatar.startsWith('wxfile://')) {
                    // 如果没有头像或者是微信临时文件，使用默认头像
                    item.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';
                } else if (!item.avatar.startsWith('http://') && !item.avatar.startsWith('https://')) {
                    // 如果是相对路径，补充完整的域名
                    item.avatar = `https://ht.rxkjsdj.com${item.avatar}`;
                }
            }
            newUser = newData.length;
            oldData = await this.model('user').where({
                id: ['>', 0],
                register_time: ['<', thirtyTimeStamp],
                last_login_time: ['>', thirtyTimeStamp]
            }).select();
            for(const item of oldData){
                item.nickname = Buffer.from(item.nickname, 'base64').toString();
                // 处理头像URL
                if (!item.avatar || item.avatar === '' || item.avatar.startsWith('wxfile://')) {
                    // 如果没有头像或者是微信临时文件，使用默认头像
                    item.avatar = 'https://ht.rxkjsdj.com/images/icon/default_avatar_big.jpg';
                } else if (!item.avatar.startsWith('http://') && !item.avatar.startsWith('https://')) {
                    // 如果是相对路径，补充完整的域名
                    item.avatar = `https://ht.rxkjsdj.com${item.avatar}`;
                }
            }
            oldUser = oldData.length;
            addCart = await this.model('cart').where({
                is_delete: 0,
                add_time: ['>', thirtyTimeStamp]
            }).count();
            addOrderNum = await this.model('order').where({
                is_delete: 0,
                add_time: ['>', thirtyTimeStamp]
            }).count();
            addOrderSum = await this.model('order').where({
                is_delete: 0,
                add_time: ['>', thirtyTimeStamp]
            }).sum('actual_price');
            payOrderNum = await this.model('order').where({
                is_delete: 0,
                add_time: ['>', thirtyTimeStamp],
                order_status: ['IN', [201, 802, 300, 301]]
            }).count();
            payOrderSum = await this.model('order').where({
                is_delete: 0,
                add_time: ['>', thirtyTimeStamp],
                order_status: ['IN', [201, 802, 300, 301]]
            }).sum('actual_price');
        }
        if (addOrderSum == null) {
            addOrderSum = 0;
        }
        if (payOrderSum == null) {
            payOrderSum = 0;
        }
        if(newData.length > 0){
            for(const item of newData){
                item.register_time =  moment.unix(item.register_time).format('YYYY-MM-DD HH:mm:ss');
                item.last_login_time =  moment.unix(item.last_login_time).format('YYYY-MM-DD HH:mm:ss');
            }
        }

        if(oldData.length > 0){
            for(const item of oldData){
                item.register_time =  moment.unix(item.register_time).format('YYYY-MM-DD HH:mm:ss');
                item.last_login_time =  moment.unix(item.last_login_time).format('YYYY-MM-DD HH:mm:ss');
            }
        }

        let info = {
            newUser: newUser,
            oldUser: oldUser,
            addCart: addCart,
            newData: newData,
            oldData: oldData,
            addOrderNum: addOrderNum,
            addOrderSum: addOrderSum,
            payOrderNum: payOrderNum,
            payOrderSum: payOrderSum
        }
        return this.success(info);
    }

    // 获取数据概览统计
    async getDataOverviewAction() {
        try {
            const timeRange = this.get('timeRange') || 'today'; // today, yesterday, 7days, 30days, custom
            const startTime = this.get('startTime');
            const endTime = this.get('endTime');

            let timeCondition = {};
            const now = Math.floor(Date.now() / 1000);
            const todayStart = Math.floor(new Date().setHours(0, 0, 0, 0) / 1000);
            const yesterdayStart = todayStart - 86400;
            const sevenDaysAgo = todayStart - 86400 * 7;
            const thirtyDaysAgo = todayStart - 86400 * 30;

            // 设置时间条件
            switch (timeRange) {
                case 'today':
                    timeCondition = ['>=', todayStart];
                    break;
                case 'yesterday':
                    timeCondition = ['BETWEEN', yesterdayStart, todayStart - 1];
                    break;
                case '7days':
                    timeCondition = ['>=', sevenDaysAgo];
                    break;
                case '30days':
                    timeCondition = ['>=', thirtyDaysAgo];
                    break;
                case 'custom':
                    if (startTime && endTime) {
                        timeCondition = ['BETWEEN', parseInt(startTime), parseInt(endTime)];
                    } else {
                        timeCondition = ['>=', todayStart];
                    }
                    break;
                default:
                    timeCondition = ['>=', todayStart];
            }

            // 1. 支付金额 - 已支付订单的实际支付金额
            const paymentAmount = await this.model('order').where({
                is_delete: 0,
                order_status: ['IN', [201, 300, 301, 401]], // 已付款、已发货、已收货、已完成
                pay_time: timeCondition
            }).sum('actual_price') || 0;

            // 2. 订单数 - 已支付订单数量
            const orderCount = await this.model('order').where({
                is_delete: 0,
                order_status: ['IN', [201, 300, 301, 401]],
                pay_time: timeCondition
            }).count();

            // 3. 商品访客数 - 商品浏览足迹的去重用户数
            const visitorCount = await this.model('footprint').where({
                add_time: timeCondition
            }).group('user_id').count();

            // 4. 销售件数 - 已支付订单的商品总件数
            const salesVolume = await this.model('order_goods').alias('og')
                .join({
                    table: 'order',
                    join: 'inner',
                    as: 'o',
                    on: ['og.order_id', 'o.id']
                })
                .where({
                    'og.is_delete': 0,
                    'o.is_delete': 0,
                    'o.order_status': ['IN', [201, 300, 301, 401]],
                    'o.pay_time': timeCondition
                }).sum('og.number') || 0;

            // 5. 支付转化率 = 支付订单数 / 商品访客数 * 100
            const conversionRate = visitorCount > 0 ? ((orderCount / visitorCount) * 100).toFixed(2) : 0;

            // 6. 客单价 = 支付金额 / 订单数
            const avgOrderValue = orderCount > 0 ? (paymentAmount / orderCount).toFixed(2) : 0;

            // 获取昨日对比数据
            let yesterdayCondition = {};
            if (timeRange === 'today') {
                const yesterdayStart = todayStart - 86400;
                const yesterdayEnd = todayStart - 1;
                yesterdayCondition = ['BETWEEN', yesterdayStart, yesterdayEnd];
            } else {
                // 对于其他时间范围，获取相同长度的前一个时间段
                let prevStartTime = todayStart - 86400;
                let prevEndTime = todayStart - 1;

                switch (timeRange) {
                    case 'yesterday':
                        prevStartTime = todayStart - 86400 * 2;
                        prevEndTime = todayStart - 86400 - 1;
                        break;
                    case '7days':
                        prevStartTime = todayStart - 86400 * 14;
                        prevEndTime = todayStart - 86400 * 7 - 1;
                        break;
                    case '30days':
                        prevStartTime = todayStart - 86400 * 60;
                        prevEndTime = todayStart - 86400 * 30 - 1;
                        break;
                }
                yesterdayCondition = ['BETWEEN', prevStartTime, prevEndTime];
            }

            // 获取对比期间的数据
            const prevPaymentAmount = await this.model('order').where({
                is_delete: 0,
                order_status: ['IN', [201, 300, 301, 401]],
                pay_time: yesterdayCondition
            }).sum('actual_price') || 0;

            const prevOrderCount = await this.model('order').where({
                is_delete: 0,
                order_status: ['IN', [201, 300, 301, 401]],
                pay_time: yesterdayCondition
            }).count();

            const prevVisitorCount = await this.model('footprint').where({
                add_time: yesterdayCondition
            }).group('user_id').count();

            const prevSalesVolume = await this.model('order_goods').alias('og')
                .join({
                    table: 'order',
                    join: 'inner',
                    as: 'o',
                    on: ['og.order_id', 'o.id']
                })
                .where({
                    'og.is_delete': 0,
                    'o.is_delete': 0,
                    'o.order_status': ['IN', [201, 300, 301, 401]],
                    'o.pay_time': yesterdayCondition
                }).sum('og.number') || 0;

            const prevConversionRate = prevVisitorCount > 0 ? ((prevOrderCount / prevVisitorCount) * 100) : 0;
            const prevAvgOrderValue = prevOrderCount > 0 ? (prevPaymentAmount / prevOrderCount) : 0;

            // 计算变化百分比
            const calculateChange = (current, previous) => {
                if (previous === 0) return current > 0 ? 100 : 0;
                return ((current - previous) / previous * 100);
            };

            const overview = {
                paymentAmount: parseFloat(paymentAmount).toFixed(2),
                paymentAmountChange: calculateChange(paymentAmount, prevPaymentAmount).toFixed(2),
                orderCount,
                orderCountChange: calculateChange(orderCount, prevOrderCount).toFixed(2),
                visitorCount,
                visitorCountChange: calculateChange(visitorCount, prevVisitorCount).toFixed(2),
                conversionRate: parseFloat(conversionRate),
                conversionRateChange: calculateChange(conversionRate, prevConversionRate).toFixed(2),
                salesVolume: parseInt(salesVolume),
                salesVolumeChange: calculateChange(salesVolume, prevSalesVolume).toFixed(2),
                avgOrderValue: parseFloat(avgOrderValue),
                avgOrderValueChange: calculateChange(avgOrderValue, prevAvgOrderValue).toFixed(2)
            };

            return this.success(overview);
        } catch (error) {
            console.error('获取数据概览失败:', error);
            return this.fail('获取数据概览失败');
        }
    }

    // 获取24小时支付金额图表数据
    async getPaymentChartAction() {
        try {
            const timeRange = this.get('timeRange') || 'today';
            const startTime = this.get('startTime');
            const endTime = this.get('endTime');

            let chartData = [];
            const now = Math.floor(Date.now() / 1000);
            const todayStart = Math.floor(new Date().setHours(0, 0, 0, 0) / 1000);

            if (timeRange === 'today' || timeRange === 'realtime') {
                // 24小时数据，按小时分组
                for (let i = 0; i < 24; i++) {
                    const hourStart = todayStart + i * 3600;
                    const hourEnd = hourStart + 3599;

                    const amount = await this.model('order').where({
                        is_delete: 0,
                        order_status: ['IN', [201, 300, 301, 401]],
                        pay_time: ['BETWEEN', hourStart, hourEnd]
                    }).sum('actual_price') || 0;

                    chartData.push({
                        time: `${i.toString().padStart(2, '0')}:00`,
                        amount: parseFloat(amount).toFixed(2),
                        timestamp: hourStart
                    });
                }
            } else {
                // 其他时间范围，按天分组
                let days = 1;
                let startTimestamp = todayStart;

                switch (timeRange) {
                    case 'yesterday':
                        days = 1;
                        startTimestamp = todayStart - 86400;
                        break;
                    case '7days':
                        days = 7;
                        startTimestamp = todayStart - 86400 * 7;
                        break;
                    case '30days':
                        days = 30;
                        startTimestamp = todayStart - 86400 * 30;
                        break;
                    case 'custom':
                        if (startTime && endTime) {
                            startTimestamp = parseInt(startTime);
                            days = Math.ceil((parseInt(endTime) - parseInt(startTime)) / 86400);
                        }
                        break;
                }

                for (let i = 0; i < days; i++) {
                    const dayStart = startTimestamp + i * 86400;
                    const dayEnd = dayStart + 86399;

                    const amount = await this.model('order').where({
                        is_delete: 0,
                        order_status: ['IN', [201, 300, 301, 401]],
                        pay_time: ['BETWEEN', dayStart, dayEnd]
                    }).sum('actual_price') || 0;

                    const date = new Date(dayStart * 1000);
                    chartData.push({
                        time: `${date.getMonth() + 1}/${date.getDate()}`,
                        amount: parseFloat(amount).toFixed(2),
                        timestamp: dayStart
                    });
                }
            }

            return this.success(chartData);
        } catch (error) {
            console.error('获取支付图表数据失败:', error);
            return this.fail('获取支付图表数据失败');
        }
    }


};
