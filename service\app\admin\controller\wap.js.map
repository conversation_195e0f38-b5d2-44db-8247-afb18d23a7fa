{"version": 3, "sources": ["..\\..\\..\\src\\admin\\controller\\wap.js"], "names": ["Base", "require", "module", "exports", "checkGoodsInFlashSale", "goodsId", "activeCampaigns", "model", "where", "goods_id", "status", "select", "length", "campaignNames", "map", "c", "name", "join", "inFlashSale", "message", "campaigns", "error", "console", "indexAction", "product", "field", "alias", "table", "as", "on", "log", "goods", "is_delete", "item", "jtem", "id", "value", "is_on_sale", "list_pic_url", "success", "onsaleAction", "info", "push", "outsaleAction", "updatePriceAction", "sn", "post", "price", "flashSaleCheck", "fail", "goods_sn", "update", "retail_price", "min", "outAction", "page", "get", "size", "data", "goods_number", "order", "countSelect", "category_id", "find", "category_name", "parent_id", "parentInfo", "category_p_name", "dropAction", "sortAction", "index", "saleStatusAction", "sale", "infoAction", "cateData", "c_data", "f_data", "productInfo", "infoData", "getAllCategory1Action", "is_show", "level", "newData", "children", "citem", "label", "getAllCategoryAction", "c_item", "getGoodsSnNameAction", "cateId", "storeAction", "values", "picUrl", "is_new", "add", "destoryAction", "limit", "delete"], "mappings": ";;AAAA,MAAMA,OAAOC,QAAQ,WAAR,CAAb;AACAC,OAAOC,OAAP,GAAiB,cAAcH,IAAd,CAAmB;;AAEhC;;;;;AAKMI,yBAAN,CAA4BC,OAA5B,EAAqC;AAAA;;AAAA;AACjC,gBAAI;AACA;AACA,sBAAMC,kBAAkB,MAAM,MAAKC,KAAL,CAAW,sBAAX,EAAmCC,KAAnC,CAAyC;AACnEC,8BAAUJ,OADyD;AAEnEK,4BAAQ,CAAC,IAAD,EAAO,CAAC,QAAD,EAAW,UAAX,CAAP;AAF2D,iBAAzC,EAG3BC,MAH2B,EAA9B;;AAKA,oBAAIL,gBAAgBM,MAAhB,GAAyB,CAA7B,EAAgC;AAC5B,0BAAMC,gBAAgBP,gBAAgBQ,GAAhB,CAAoB;AAAA,+BAAKC,EAAEC,IAAP;AAAA,qBAApB,EAAiCC,IAAjC,CAAsC,GAAtC,CAAtB;AACA,2BAAO;AACHC,qCAAa,IADV;AAEHC,iCAAU,eAAcN,aAAc,SAFnC;AAGHO,mCAAWd;AAHR,qBAAP;AAKH;;AAED,uBAAO;AACHY,iCAAa,KADV;AAEHC,6BAAS;AAFN,iBAAP;AAKH,aArBD,CAqBE,OAAOE,KAAP,EAAc;AACZC,wBAAQD,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO;AACHH,iCAAa,KADV;AAEHC,6BAAS,YAFN;AAGHE,2BAAOA,MAAMF;AAHV,iBAAP;AAKH;AA7BgC;AA8BpC;;AAED;;;;AAIMI,eAAN,GAAoB;AAAA;;AAAA;AAChB;AACA,kBAAMC,UAAU,MAAM,OAAKjB,KAAL,CAAW,SAAX,EAAsBkB,KAAtB,CAA4B,CAAC,YAAD,EAAe,YAAf,EAA6B,2BAA7B,EAA0D,gBAA1D,EAA4E,SAA5E,CAA5B,EAAoHC,KAApH,CAA0H,GAA1H,EAA+HT,IAA/H,CAAoI;AACtJU,uBAAO,qBAD+I;AAEtJV,sBAAM,MAFgJ;AAGtJW,oBAAI,GAHkJ;AAItJC,oBAAI,CAAC,2BAAD,EAA8B,MAA9B;AAJkJ,aAApI,EAKnBlB,MALmB,EAAtB,CAFgB,CAOH;AACbW,oBAAQQ,GAAR,CAAYN,OAAZ;AACA;AACA,kBAAMO,QAAQ,MAAM,OAAKxB,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1CwB,2BAAW;AAD+B,aAA1B,EAEjBrB,MAFiB,EAApB;AAGA,iBAAK,MAAMsB,IAAX,IAAmBT,OAAnB,EAA4B;AACxB,oBAAIf,WAAWwB,KAAKxB,QAApB;AACA,qBAAK,MAAMyB,IAAX,IAAmBH,KAAnB,EAA0B;AACtB,wBAAItB,YAAYyB,KAAKC,EAArB,EAAyB;AACrB;AACAF,6BAAKjB,IAAL,GAAYkB,KAAKlB,IAAL,GAAY,GAAZ,GAAkBiB,KAAKG,KAAnC;AACAH,6BAAKI,UAAL,GAAkBH,KAAKG,UAAvB;AACAJ,6BAAKK,YAAL,GAAoBJ,KAAKI,YAAzB;AACA,4BAAIL,KAAKI,UAAL,IAAmB,CAAvB,EAA0B;AACtBJ,iCAAKI,UAAL,GAAkB,IAAlB;AACH,yBAFD,MAEO;AACHJ,iCAAKI,UAAL,GAAkB,KAAlB;AACH;AACJ;AACJ;AACJ;AACD,mBAAO,OAAKE,OAAL,CAAaf,OAAb,CAAP;AA7BgB;AA8BnB;AACKgB,gBAAN,GAAqB;AAAA;;AAAA;AACjB,kBAAMhB,UAAU,MAAM,OAAKjB,KAAL,CAAW,SAAX,EAAsBkB,KAAtB,CAA4B,CAAC,YAAD,EAAe,YAAf,EAA6B,2BAA7B,EAA0D,gBAA1D,EAA4E,SAA5E,CAA5B,EAAoHC,KAApH,CAA0H,GAA1H,EAA+HT,IAA/H,CAAoI;AACtJU,uBAAO,qBAD+I;AAEtJV,sBAAM,MAFgJ;AAGtJW,oBAAI,GAHkJ;AAItJC,oBAAI,CAAC,2BAAD,EAA8B,MAA9B;AAJkJ,aAApI,EAKnBlB,MALmB,EAAtB,CADiB,CAMJ;AACb,kBAAMoB,QAAQ,MAAM,OAAKxB,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1C6B,4BAAY,CAD8B;AAE1CL,2BAAW;AAF+B,aAA1B,EAGjBrB,MAHiB,EAApB;AAIAW,oBAAQQ,GAAR,CAAYC,KAAZ;AACA,gBAAIU,OAAO,EAAX;AACA,iBAAK,MAAMR,IAAX,IAAmBT,OAAnB,EAA4B;AACxB,oBAAIf,WAAWwB,KAAKxB,QAApB;AACA,qBAAK,MAAMyB,IAAX,IAAmBH,KAAnB,EAA0B;AACtB,wBAAItB,YAAYyB,KAAKC,EAArB,EAAyB;AACrBF,6BAAKjB,IAAL,GAAYkB,KAAKlB,IAAL,GAAY,GAAZ,GAAkBiB,KAAKG,KAAnC;AACAH,6BAAKI,UAAL,GAAkBH,KAAKG,UAAvB;AACAJ,6BAAKK,YAAL,GAAoBJ,KAAKI,YAAzB;AACA,4BAAIL,KAAKI,UAAL,IAAmB,CAAvB,EAA0B;AACtBJ,iCAAKI,UAAL,GAAkB,IAAlB;AACAI,iCAAKC,IAAL,CAAUT,IAAV;AACH;AACJ;AACJ;AACJ;AACDX,oBAAQQ,GAAR,CAAYN,OAAZ;AACA,mBAAO,OAAKe,OAAL,CAAaE,IAAb,CAAP;AA5BiB;AA6BpB;AACKE,iBAAN,GAAsB;AAAA;;AAAA;AAClB,kBAAMnB,UAAU,MAAM,OAAKjB,KAAL,CAAW,SAAX,EAAsBkB,KAAtB,CAA4B,CAAC,YAAD,EAAe,YAAf,EAA6B,2BAA7B,EAA0D,gBAA1D,EAA4E,SAA5E,CAA5B,EAAoHC,KAApH,CAA0H,GAA1H,EAA+HT,IAA/H,CAAoI;AACtJU,uBAAO,qBAD+I;AAEtJV,sBAAM,MAFgJ;AAGtJW,oBAAI,GAHkJ;AAItJC,oBAAI,CAAC,2BAAD,EAA8B,MAA9B;AAJkJ,aAApI,EAKnBlB,MALmB,EAAtB,CADkB,CAML;AACb,gBAAI8B,OAAO,EAAX;AACA,kBAAMV,QAAQ,MAAM,OAAKxB,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC1C6B,4BAAY,CAD8B;AAE1CL,2BAAW;AAF+B,aAA1B,EAGjBrB,MAHiB,EAApB;AAIAW,oBAAQQ,GAAR,CAAYC,KAAZ;AACA,iBAAK,MAAME,IAAX,IAAmBT,OAAnB,EAA4B;AACxB,oBAAIf,WAAWwB,KAAKxB,QAApB;AACA,qBAAK,MAAMyB,IAAX,IAAmBH,KAAnB,EAA0B;AACtB,wBAAItB,YAAYyB,KAAKC,EAArB,EAAyB;AACrBF,6BAAKjB,IAAL,GAAYkB,KAAKlB,IAAL,GAAY,GAAZ,GAAkBiB,KAAKG,KAAnC;AACAH,6BAAKI,UAAL,GAAkBH,KAAKG,UAAvB;AACAJ,6BAAKK,YAAL,GAAoBJ,KAAKI,YAAzB;AACA,4BAAIL,KAAKI,UAAL,IAAmB,CAAvB,EAA0B;AACtBJ,iCAAKI,UAAL,GAAkB,KAAlB;AACAI,iCAAKC,IAAL,CAAUT,IAAV;AACH;AACJ;AACJ;AACJ;AACDX,oBAAQQ,GAAR,CAAYN,OAAZ;AACA,mBAAO,OAAKe,OAAL,CAAaE,IAAb,CAAP;AA5BkB;AA6BrB;AACKG,qBAAN,GAA0B;AAAA;;AAAA;AACtB,kBAAMC,KAAK,OAAKC,IAAL,CAAU,IAAV,CAAX;AACA,kBAAMX,KAAK,OAAKW,IAAL,CAAU,IAAV,CAAX;AACA,kBAAMC,QAAQ,OAAKD,IAAL,CAAU,OAAV,CAAd;;AAEA;AACA,kBAAME,iBAAiB,MAAM,OAAK5C,qBAAL,CAA2B+B,EAA3B,CAA7B;AACA,gBAAIa,eAAe9B,WAAnB,EAAgC;AAC5B,uBAAO,OAAK+B,IAAL,CAAU,GAAV,EAAeD,eAAe7B,OAA9B,CAAP;AACH;;AAED,kBAAMsB,OAAO,MAAM,OAAKlC,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAC3C0C,0BAAUL;AADiC,aAA5B,EAEhBM,MAFgB,CAET;AACNC,8BAAcL;AADR,aAFS,CAAnB;AAKA,gBAAIM,MAAM,MAAM,OAAK9C,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AACxCC,0BAAU0B;AAD8B,aAA5B,EAEbkB,GAFa,CAET,cAFS,CAAhB;AAGA,kBAAM,OAAK9C,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3B0C,0BAAUL;AADiB,aAAzB,EAEHM,MAFG,CAEI;AACNC,8BAAcL;AADR,aAFJ,CAAN;AAKA,kBAAM,OAAKxC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5B2B,oBAAIA;AADwB,aAA1B,EAEHgB,MAFG,CAEI;AACNC,8BAAcC;AADR,aAFJ,CAAN;AAKA,mBAAO,OAAKd,OAAL,EAAP;AA7BsB;AA8BzB;AACKe,aAAN,GAAkB;AAAA;;AAAA;AACd,kBAAMC,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,OAAKD,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,kBAAMjD,QAAQ,OAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMmD,OAAO,MAAMnD,MAAMC,KAAN,CAAY;AAC3BwB,2BAAW,CADgB;AAE3B2B,8BAAc,CAAC,IAAD,EAAO,CAAP;AAFa,aAAZ,EAGhBC,KAHgB,CAGV,CAAC,SAAD,CAHU,EAGGL,IAHH,CAGQA,IAHR,EAGcE,IAHd,EAGoBI,WAHpB,EAAnB;AAIA,iBAAK,MAAM5B,IAAX,IAAmByB,KAAKA,IAAxB,EAA8B;AAC1B,sBAAMjB,OAAO,MAAM,OAAKlC,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAC5C2B,wBAAIF,KAAK6B;AADmC,iBAA7B,EAEhBC,IAFgB,EAAnB;AAGA9B,qBAAK+B,aAAL,GAAqBvB,KAAKzB,IAA1B;AACA,oBAAIyB,KAAKwB,SAAL,IAAkB,CAAtB,EAAyB;AACrB,0BAAMC,aAAa,MAAM,OAAK3D,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAClD2B,4BAAIM,KAAKwB;AADyC,qBAA7B,EAEtBF,IAFsB,EAAzB;AAGA9B,yBAAKkC,eAAL,GAAuBD,WAAWlD,IAAlC;AACH;AACD,oBAAIiB,KAAKI,UAAL,IAAmB,CAAvB,EAA0B;AACtBJ,yBAAKI,UAAL,GAAkB,IAAlB;AACH,iBAFD,MAEO;AACHJ,yBAAKI,UAAL,GAAkB,KAAlB;AACH;AACJ;AACD,mBAAO,OAAKE,OAAL,CAAamB,IAAb,CAAP;AAzBc;AA0BjB;AACKU,cAAN,GAAmB;AAAA;;AAAA;AACf,kBAAMb,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,OAAKD,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,kBAAMjD,QAAQ,OAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMmD,OAAO,MAAMnD,MAAMC,KAAN,CAAY;AAC3BwB,2BAAW,CADgB;AAE3BK,4BAAY;AAFe,aAAZ,EAGhBuB,KAHgB,CAGV,CAAC,SAAD,CAHU,EAGGL,IAHH,CAGQA,IAHR,EAGcE,IAHd,EAGoBI,WAHpB,EAAnB;AAIA,iBAAK,MAAM5B,IAAX,IAAmByB,KAAKA,IAAxB,EAA8B;AAC1B,sBAAMjB,OAAO,MAAM,OAAKlC,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAC5C2B,wBAAIF,KAAK6B;AADmC,iBAA7B,EAEhBC,IAFgB,EAAnB;AAGA9B,qBAAK+B,aAAL,GAAqBvB,KAAKzB,IAA1B;AACA,oBAAIyB,KAAKwB,SAAL,IAAkB,CAAtB,EAAyB;AACrB,0BAAMC,aAAa,MAAM,OAAK3D,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAClD2B,4BAAIM,KAAKwB;AADyC,qBAA7B,EAEtBF,IAFsB,EAAzB;AAGA9B,yBAAKkC,eAAL,GAAuBD,WAAWlD,IAAlC;AACH;AACD,oBAAIiB,KAAKI,UAAL,IAAmB,CAAvB,EAA0B;AACtBJ,yBAAKI,UAAL,GAAkB,IAAlB;AACH,iBAFD,MAEO;AACHJ,yBAAKI,UAAL,GAAkB,KAAlB;AACH;AACJ;AACD,mBAAO,OAAKE,OAAL,CAAamB,IAAb,CAAP;AAzBe;AA0BlB;AACKW,cAAN,GAAmB;AAAA;;AAAA;AACf,kBAAMd,OAAO,OAAKC,GAAL,CAAS,MAAT,KAAoB,CAAjC;AACA,kBAAMC,OAAO,OAAKD,GAAL,CAAS,MAAT,KAAoB,EAAjC;AACA,kBAAMjD,QAAQ,OAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAM+D,QAAQ,OAAKd,GAAL,CAAS,OAAT,CAAd;AACA,gBAAIc,SAAS,CAAb,EAAgB;AACZ,sBAAMZ,OAAO,MAAMnD,MAAMC,KAAN,CAAY;AAC3BwB,+BAAW;AADgB,iBAAZ,EAEhB4B,KAFgB,CAEV,CAAC,kBAAD,CAFU,EAEYL,IAFZ,CAEiBA,IAFjB,EAEuBE,IAFvB,EAE6BI,WAF7B,EAAnB;AAGA,qBAAK,MAAM5B,IAAX,IAAmByB,KAAKA,IAAxB,EAA8B;AAC1B,0BAAMjB,OAAO,MAAM,OAAKlC,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAC5C2B,4BAAIF,KAAK6B;AADmC,qBAA7B,EAEhBC,IAFgB,EAAnB;AAGA9B,yBAAK+B,aAAL,GAAqBvB,KAAKzB,IAA1B;AACA,wBAAIyB,KAAKwB,SAAL,IAAkB,CAAtB,EAAyB;AACrB,8BAAMC,aAAa,MAAM,OAAK3D,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAClD2B,gCAAIM,KAAKwB;AADyC,yBAA7B,EAEtBF,IAFsB,EAAzB;AAGA9B,6BAAKkC,eAAL,GAAuBD,WAAWlD,IAAlC;AACH;AACD,wBAAIiB,KAAKI,UAAL,IAAmB,CAAvB,EAA0B;AACtBJ,6BAAKI,UAAL,GAAkB,IAAlB;AACH,qBAFD,MAEO;AACHJ,6BAAKI,UAAL,GAAkB,KAAlB;AACH;AACJ;AACD,uBAAO,OAAKE,OAAL,CAAamB,IAAb,CAAP;AACH,aAtBD,MAsBO,IAAIY,SAAS,CAAb,EAAgB;AACnB,sBAAMZ,OAAO,MAAMnD,MAAMC,KAAN,CAAY;AAC3BwB,+BAAW;AADgB,iBAAZ,EAEhB4B,KAFgB,CAEV,CAAC,mBAAD,CAFU,EAEaL,IAFb,CAEkBA,IAFlB,EAEwBE,IAFxB,EAE8BI,WAF9B,EAAnB;AAGA,qBAAK,MAAM5B,IAAX,IAAmByB,KAAKA,IAAxB,EAA8B;AAC1B,0BAAMjB,OAAO,MAAM,OAAKlC,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAC5C2B,4BAAIF,KAAK6B;AADmC,qBAA7B,EAEhBC,IAFgB,EAAnB;AAGA9B,yBAAK+B,aAAL,GAAqBvB,KAAKzB,IAA1B;AACA,wBAAIyB,KAAKwB,SAAL,IAAkB,CAAtB,EAAyB;AACrB,8BAAMC,aAAa,MAAM,OAAK3D,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAClD2B,gCAAIM,KAAKwB;AADyC,yBAA7B,EAEtBF,IAFsB,EAAzB;AAGA9B,6BAAKkC,eAAL,GAAuBD,WAAWlD,IAAlC;AACH;AACD,wBAAIiB,KAAKI,UAAL,IAAmB,CAAvB,EAA0B;AACtBJ,6BAAKI,UAAL,GAAkB,IAAlB;AACH,qBAFD,MAEO;AACHJ,6BAAKI,UAAL,GAAkB,KAAlB;AACH;AACJ;AACD,uBAAO,OAAKE,OAAL,CAAamB,IAAb,CAAP;AACH,aAtBM,MAsBA,IAAIY,SAAS,CAAb,EAAgB;AACnB,sBAAMZ,OAAO,MAAMnD,MAAMC,KAAN,CAAY;AAC3BwB,+BAAW;AADgB,iBAAZ,EAEhB4B,KAFgB,CAEV,CAAC,mBAAD,CAFU,EAEaL,IAFb,CAEkBA,IAFlB,EAEwBE,IAFxB,EAE8BI,WAF9B,EAAnB;AAGA,qBAAK,MAAM5B,IAAX,IAAmByB,KAAKA,IAAxB,EAA8B;AAC1B,0BAAMjB,OAAO,MAAM,OAAKlC,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAC5C2B,4BAAIF,KAAK6B;AADmC,qBAA7B,EAEhBC,IAFgB,EAAnB;AAGA9B,yBAAK+B,aAAL,GAAqBvB,KAAKzB,IAA1B;AACA,wBAAIyB,KAAKwB,SAAL,IAAkB,CAAtB,EAAyB;AACrB,8BAAMC,aAAa,MAAM,OAAK3D,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAClD2B,gCAAIM,KAAKwB;AADyC,yBAA7B,EAEtBF,IAFsB,EAAzB;AAGA9B,6BAAKkC,eAAL,GAAuBD,WAAWlD,IAAlC;AACH;AACD,wBAAIiB,KAAKI,UAAL,IAAmB,CAAvB,EAA0B;AACtBJ,6BAAKI,UAAL,GAAkB,IAAlB;AACH,qBAFD,MAEO;AACHJ,6BAAKI,UAAL,GAAkB,KAAlB;AACH;AACJ;AACD,uBAAO,OAAKE,OAAL,CAAamB,IAAb,CAAP;AACH;AAvEc;AAwElB;AACKa,oBAAN,GAAyB;AAAA;;AAAA;AACrB,kBAAMpC,KAAK,OAAKqB,GAAL,CAAS,IAAT,CAAX;AACA,kBAAM9C,SAAS,OAAK8C,GAAL,CAAS,QAAT,CAAf;AACA,gBAAIgB,OAAO,CAAX;AACA,gBAAI9D,UAAU,MAAd,EAAsB;AAClB8D,uBAAO,CAAP;AACH;AACD,kBAAMjE,QAAQ,OAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMA,MAAMC,KAAN,CAAY;AACd2B,oBAAIA;AADU,aAAZ,EAEHgB,MAFG,CAEI;AACNd,4BAAYmC;AADN,aAFJ,CAAN;AARqB;AAaxB;AACKC,cAAN,GAAmB;AAAA;;AAAA;AACf,kBAAMtC,KAAK,QAAKqB,GAAL,CAAS,IAAT,CAAX;AACA,kBAAMjD,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMmD,OAAO,MAAMnD,MAAMC,KAAN,CAAY;AAC3B2B,oBAAIA;AADuB,aAAZ,EAEhB4B,IAFgB,EAAnB;AAGAzC,oBAAQQ,GAAR,CAAY4B,IAAZ;AACA,gBAAII,cAAcJ,KAAKI,WAAvB;AACA,gBAAIY,WAAW,EAAf;AACA,kBAAMC,SAAS,MAAM,QAAKpE,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAC9C2B,oBAAI2B;AAD0C,aAA7B,EAElBC,IAFkB,EAArB;AAGA,kBAAMa,SAAS,MAAM,QAAKrE,KAAL,CAAW,UAAX,EAAuBC,KAAvB,CAA6B;AAC9C2B,oBAAIwC,OAAOV;AADmC,aAA7B,EAElBF,IAFkB,EAArB;AAGAW,qBAAShC,IAAT,CAAckC,OAAOzC,EAArB,EAAyBwC,OAAOxC,EAAhC;AACA,gBAAI0C,cAAc,MAAM,QAAKtE,KAAL,CAAW,SAAX,EAAsBC,KAAtB,CAA4B;AAChDC,0BAAU0B;AADsC,aAA5B,EAErBxB,MAFqB,EAAxB;AAGA,gBAAIkE,YAAYjE,MAAZ,GAAqB,CAAzB,EAA4B,CAAE;AAC9B,gBAAIkE,WAAW;AACXrC,sBAAMiB,IADK;AAEXgB,0BAAUA;AAFC,aAAf;AAIA,mBAAO,QAAKnC,OAAL,CAAauC,QAAb,CAAP;AAxBe;AAyBlB;AACKC,yBAAN,GAA8B;AAAA;;AAAA;AAAE;AAC5B,kBAAMxE,QAAQ,QAAKA,KAAL,CAAW,UAAX,CAAd;AACA,kBAAMmD,OAAO,MAAMnD,MAAMC,KAAN,CAAY;AAC3BwE,yBAAS,CADkB;AAE3BC,uBAAO;AAFoB,aAAZ,EAGhBtE,MAHgB,EAAnB;AAIA,kBAAMgE,SAAS,MAAMpE,MAAMC,KAAN,CAAY;AAC7BwE,yBAAS,CADoB;AAE7BC,uBAAO;AAFsB,aAAZ,EAGlBtE,MAHkB,EAArB;AAIA,gBAAIuE,UAAU,EAAd;AACA,iBAAK,MAAMjD,IAAX,IAAmByB,IAAnB,EAAyB;AACrB,oBAAIyB,WAAW,EAAf;AACA,qBAAK,MAAMC,KAAX,IAAoBT,MAApB,EAA4B;AACxB,wBAAIS,MAAMnB,SAAN,IAAmBhC,KAAKE,EAA5B,EAAgC;AAC5BgD,iCAASzC,IAAT,CAAc;AACVN,mCAAOgD,MAAMjD,EADH;AAEVkD,mCAAOD,MAAMpE;AAFH,yBAAd;AAIH;AACJ;AACDkE,wBAAQxC,IAAR,CAAa;AACTN,2BAAOH,KAAKE,EADH;AAETkD,2BAAOpD,KAAKjB,IAFH;AAGTmE,8BAAUA;AAHD,iBAAb;AAKH;AACD,mBAAO,QAAK5C,OAAL,CAAa2C,OAAb,CAAP;AA3B0B;AA4B7B;AACKI,wBAAN,GAA6B;AAAA;;AAAA;AAAE;AAC3B,kBAAM/E,QAAQ,QAAKA,KAAL,CAAW,UAAX,CAAd;AACA,kBAAMmD,OAAO,MAAMnD,MAAMC,KAAN,CAAY;AAC3BwE,yBAAS,CADkB;AAE3BC,uBAAO;AAFoB,aAAZ,EAGhBxD,KAHgB,CAGV,SAHU,EAGCd,MAHD,EAAnB;AAIA,gBAAIuE,UAAU,EAAd;AACA,iBAAK,MAAMjD,IAAX,IAAmByB,IAAnB,EAAyB;AACrB,oBAAIyB,WAAW,EAAf;AACA,sBAAMR,SAAS,MAAMpE,MAAMC,KAAN,CAAY;AAC7BwE,6BAAS,CADoB;AAE7BC,2BAAO,IAFsB;AAG7BhB,+BAAWhC,KAAKE;AAHa,iBAAZ,EAIlBV,KAJkB,CAIZ,SAJY,EAIDd,MAJC,EAArB;AAKA,qBAAK,MAAM4E,MAAX,IAAqBZ,MAArB,EAA6B;AACzBQ,6BAASzC,IAAT,CAAc;AACVN,+BAAOmD,OAAOpD,EADJ;AAEVkD,+BAAOE,OAAOvE;AAFJ,qBAAd;AAIH;AACDkE,wBAAQxC,IAAR,CAAa;AACTN,2BAAOH,KAAKE,EADH;AAETkD,2BAAOpD,KAAKjB,IAFH;AAGTmE,8BAAUA;AAHD,iBAAb;AAKH;AACD,mBAAO,QAAK5C,OAAL,CAAa2C,OAAb,CAAP;AA1ByB;AA2B5B;AACKM,wBAAN,GAA6B;AAAA;;AAAA;AACzB,kBAAMC,SAAS,QAAKjC,GAAL,CAAS,QAAT,CAAf;AACA,kBAAMjD,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,kBAAMmD,OAAO,MAAMnD,MAAMC,KAAN,CAAY;AAC3BsD,6BAAa2B,MADc;AAE3BzD,2BAAW;AAFgB,aAAZ,EAGhBP,KAHgB,CAGV,eAHU,EAGOmC,KAHP,CAGa;AAC5B,4BAAY;AADgB,aAHb,EAKhBjD,MALgB,EAAnB;AAMA,mBAAO,QAAK4B,OAAL,CAAamB,IAAb,CAAP;AATyB;AAU5B;AACKgC,eAAN,GAAoB;AAAA;;AAAA;AAChB,kBAAMC,SAAS,QAAK7C,IAAL,CAAU,MAAV,CAAf;AACA,kBAAMvC,QAAQ,QAAKA,KAAL,CAAW,OAAX,CAAd;AACA,gBAAIqF,SAASD,OAAOrD,YAApB;AACA,gBAAI7B,WAAWkF,OAAOxD,EAAtB;AACA,kBAAM,QAAK5B,KAAL,CAAW,MAAX,EAAmBC,KAAnB,CAAyB;AAC3BC,0BAAUA;AADiB,aAAzB,EAEH0C,MAFG,CAEI;AACNb,8BAAcsD;AADR,aAFJ,CAAN;AAKAD,mBAAOE,MAAP,GAAgBF,OAAOE,MAAP,GAAgB,CAAhB,GAAoB,CAApC;AACA,gBAAI1D,KAAKwD,OAAOxD,EAAhB;AACA,gBAAIA,KAAK,CAAT,EAAY;AACR,sBAAM5B,MAAMC,KAAN,CAAY;AACd2B,wBAAIA;AADU,iBAAZ,EAEHgB,MAFG,CAEIwC,MAFJ,CAAN;AAGH,aAJD,MAIO;AACH,uBAAOA,OAAOxD,EAAd;AACA,oBAAI1B,WAAW,MAAMF,MAAMuF,GAAN,CAAUH,MAAV,CAArB;AACA,sBAAMpF,MAAMC,KAAN,CAAY;AACd2B,wBAAI1B;AADU,iBAAZ,EAEH0C,MAFG,CAEI;AACND,8BAAUzC;AADJ,iBAFJ,CAAN;AAKH;AACD,mBAAO,QAAK8B,OAAL,CAAaoD,MAAb,CAAP;AAzBgB;AA0BnB;;AAEKI,iBAAN,GAAsB;AAAA;;AAAA;AAClB,kBAAM5D,KAAK,QAAKW,IAAL,CAAU,IAAV,CAAX;AACA,kBAAM,QAAKvC,KAAL,CAAW,OAAX,EAAoBC,KAApB,CAA0B;AAC5B2B,oBAAIA;AADwB,aAA1B,EAEH6D,KAFG,CAEG,CAFH,EAEMC,MAFN,EAAN;AAGA,mBAAO,QAAK1D,OAAL,EAAP;AALkB;AAMrB;AAlb+B,CAApC", "file": "..\\..\\..\\src\\admin\\controller\\wap.js", "sourcesContent": ["const Base = require('./base.js');\nmodule.exports = class extends Base {\n\n    /**\n     * 检查商品是否正在参与秒杀活动\n     * @param {number} goodsId 商品ID\n     * @returns {Promise<Object>} 返回检查结果\n     */\n    async checkGoodsInFlashSale(goodsId) {\n        try {\n            // 检查是否有进行中或即将开始的秒杀活动\n            const activeCampaigns = await this.model('flash_sale_campaigns').where({\n                goods_id: goodsId,\n                status: ['IN', ['active', 'upcoming']]\n            }).select();\n\n            if (activeCampaigns.length > 0) {\n                const campaignNames = activeCampaigns.map(c => c.name).join('、');\n                return {\n                    inFlashSale: true,\n                    message: `该商品正在参与秒杀活动：${campaignNames}，无法修改价格`,\n                    campaigns: activeCampaigns\n                };\n            }\n\n            return {\n                inFlashSale: false,\n                message: '商品未参与秒杀活动，可以修改价格'\n            };\n\n        } catch (error) {\n            console.error('检查商品秒杀状态失败:', error);\n            return {\n                inFlashSale: false,\n                message: '检查失败，但允许操作',\n                error: error.message\n            };\n        }\n    }\n\n    /**\n     * index action\n     * @return {Promise} []\n     */\n    async indexAction() {\n        // const product = await this.model('product').where({is_delete:1}).delete()\n        const product = await this.model('product').field(['c.goods_sn', 'c.goods_id', 'c.goods_specification_ids', 'c.retail_price', 'g.value']).alias('c').join({\n            table: 'goods_specification',\n            join: 'left',\n            as: 'g',\n            on: ['c.goods_specification_ids', 'g.id']\n        }).select(); // 如果出错了，不会更新数据的\n        console.log(product);\n        // const goods = await this.model('goods').where({is_delete:0}).select();\n        const goods = await this.model('goods').where({\n            is_delete: 0\n        }).select();\n        for (const item of product) {\n            let goods_id = item.goods_id;\n            for (const jtem of goods) {\n                if (goods_id == jtem.id) {\n                    // const product = await this.model('product').where({goods_id:jtem.id}).update({is_delete:0})\n                    item.name = jtem.name + '-' + item.value;\n                    item.is_on_sale = jtem.is_on_sale;\n                    item.list_pic_url = jtem.list_pic_url;\n                    if (item.is_on_sale == 1) {\n                        item.is_on_sale = true;\n                    } else {\n                        item.is_on_sale = false;\n                    }\n                }\n            }\n        }\n        return this.success(product);\n    }\n    async onsaleAction() {\n        const product = await this.model('product').field(['c.goods_sn', 'c.goods_id', 'c.goods_specification_ids', 'c.retail_price', 'g.value']).alias('c').join({\n            table: 'goods_specification',\n            join: 'left',\n            as: 'g',\n            on: ['c.goods_specification_ids', 'g.id']\n        }).select(); // 如果出错了，不会更新数据的\n        const goods = await this.model('goods').where({\n            is_on_sale: 1,\n            is_delete: 0\n        }).select();\n        console.log(goods);\n        let info = [];\n        for (const item of product) {\n            let goods_id = item.goods_id;\n            for (const jtem of goods) {\n                if (goods_id == jtem.id) {\n                    item.name = jtem.name + '-' + item.value;\n                    item.is_on_sale = jtem.is_on_sale;\n                    item.list_pic_url = jtem.list_pic_url;\n                    if (item.is_on_sale == 1) {\n                        item.is_on_sale = true;\n                        info.push(item);\n                    }\n                }\n            }\n        }\n        console.log(product);\n        return this.success(info);\n    }\n    async outsaleAction() {\n        const product = await this.model('product').field(['c.goods_sn', 'c.goods_id', 'c.goods_specification_ids', 'c.retail_price', 'g.value']).alias('c').join({\n            table: 'goods_specification',\n            join: 'left',\n            as: 'g',\n            on: ['c.goods_specification_ids', 'g.id']\n        }).select(); // 如果出错了，不会更新数据的\n        let info = [];\n        const goods = await this.model('goods').where({\n            is_on_sale: 0,\n            is_delete: 0\n        }).select();\n        console.log(goods);\n        for (const item of product) {\n            let goods_id = item.goods_id;\n            for (const jtem of goods) {\n                if (goods_id == jtem.id) {\n                    item.name = jtem.name + '-' + item.value;\n                    item.is_on_sale = jtem.is_on_sale;\n                    item.list_pic_url = jtem.list_pic_url;\n                    if (item.is_on_sale == 0) {\n                        item.is_on_sale = false;\n                        info.push(item);\n                    }\n                }\n            }\n        }\n        console.log(product);\n        return this.success(info);\n    }\n    async updatePriceAction() {\n        const sn = this.post('sn');\n        const id = this.post('id');\n        const price = this.post('price');\n\n        // 检查商品是否正在参与秒杀\n        const flashSaleCheck = await this.checkGoodsInFlashSale(id);\n        if (flashSaleCheck.inFlashSale) {\n            return this.fail(400, flashSaleCheck.message);\n        }\n\n        const info = await this.model('product').where({\n            goods_sn: sn\n        }).update({\n            retail_price: price\n        });\n        let min = await this.model('product').where({\n            goods_id: id\n        }).min('retail_price');\n        await this.model('cart').where({\n            goods_sn: sn\n        }).update({\n            retail_price: price\n        });\n        await this.model('goods').where({\n            id: id\n        }).update({\n            retail_price: min\n        });\n        return this.success();\n    }\n    async outAction() {\n        const page = this.get('page') || 1;\n        const size = this.get('size') || 10;\n        const model = this.model('goods');\n        const data = await model.where({\n            is_delete: 0,\n            goods_number: ['<=', 0]\n        }).order(['id DESC']).page(page, size).countSelect();\n        for (const item of data.data) {\n            const info = await this.model('category').where({\n                id: item.category_id\n            }).find();\n            item.category_name = info.name;\n            if (info.parent_id != 0) {\n                const parentInfo = await this.model('category').where({\n                    id: info.parent_id\n                }).find();\n                item.category_p_name = parentInfo.name;\n            }\n            if (item.is_on_sale == 1) {\n                item.is_on_sale = true;\n            } else {\n                item.is_on_sale = false;\n            }\n        }\n        return this.success(data);\n    }\n    async dropAction() {\n        const page = this.get('page') || 1;\n        const size = this.get('size') || 10;\n        const model = this.model('goods');\n        const data = await model.where({\n            is_delete: 0,\n            is_on_sale: 0\n        }).order(['id DESC']).page(page, size).countSelect();\n        for (const item of data.data) {\n            const info = await this.model('category').where({\n                id: item.category_id\n            }).find();\n            item.category_name = info.name;\n            if (info.parent_id != 0) {\n                const parentInfo = await this.model('category').where({\n                    id: info.parent_id\n                }).find();\n                item.category_p_name = parentInfo.name;\n            }\n            if (item.is_on_sale == 1) {\n                item.is_on_sale = true;\n            } else {\n                item.is_on_sale = false;\n            }\n        }\n        return this.success(data);\n    }\n    async sortAction() {\n        const page = this.get('page') || 1;\n        const size = this.get('size') || 10;\n        const model = this.model('goods');\n        const index = this.get('index');\n        if (index == 1) {\n            const data = await model.where({\n                is_delete: 0\n            }).order(['sell_volume DESC']).page(page, size).countSelect();\n            for (const item of data.data) {\n                const info = await this.model('category').where({\n                    id: item.category_id\n                }).find();\n                item.category_name = info.name;\n                if (info.parent_id != 0) {\n                    const parentInfo = await this.model('category').where({\n                        id: info.parent_id\n                    }).find();\n                    item.category_p_name = parentInfo.name;\n                }\n                if (item.is_on_sale == 1) {\n                    item.is_on_sale = true;\n                } else {\n                    item.is_on_sale = false;\n                }\n            }\n            return this.success(data);\n        } else if (index == 2) {\n            const data = await model.where({\n                is_delete: 0\n            }).order(['retail_price DESC']).page(page, size).countSelect();\n            for (const item of data.data) {\n                const info = await this.model('category').where({\n                    id: item.category_id\n                }).find();\n                item.category_name = info.name;\n                if (info.parent_id != 0) {\n                    const parentInfo = await this.model('category').where({\n                        id: info.parent_id\n                    }).find();\n                    item.category_p_name = parentInfo.name;\n                }\n                if (item.is_on_sale == 1) {\n                    item.is_on_sale = true;\n                } else {\n                    item.is_on_sale = false;\n                }\n            }\n            return this.success(data);\n        } else if (index == 3) {\n            const data = await model.where({\n                is_delete: 0\n            }).order(['goods_number DESC']).page(page, size).countSelect();\n            for (const item of data.data) {\n                const info = await this.model('category').where({\n                    id: item.category_id\n                }).find();\n                item.category_name = info.name;\n                if (info.parent_id != 0) {\n                    const parentInfo = await this.model('category').where({\n                        id: info.parent_id\n                    }).find();\n                    item.category_p_name = parentInfo.name;\n                }\n                if (item.is_on_sale == 1) {\n                    item.is_on_sale = true;\n                } else {\n                    item.is_on_sale = false;\n                }\n            }\n            return this.success(data);\n        }\n    }\n    async saleStatusAction() {\n        const id = this.get('id');\n        const status = this.get('status');\n        let sale = 0;\n        if (status == 'true') {\n            sale = 1;\n        }\n        const model = this.model('goods');\n        await model.where({\n            id: id\n        }).update({\n            is_on_sale: sale\n        });\n    }\n    async infoAction() {\n        const id = this.get('id');\n        const model = this.model('goods');\n        const data = await model.where({\n            id: id\n        }).find();\n        console.log(data);\n        let category_id = data.category_id;\n        let cateData = [];\n        const c_data = await this.model('category').where({\n            id: category_id\n        }).find();\n        const f_data = await this.model('category').where({\n            id: c_data.parent_id\n        }).find();\n        cateData.push(f_data.id, c_data.id);\n        let productInfo = await this.model('product').where({\n            goods_id: id\n        }).select();\n        if (productInfo.length > 1) {}\n        let infoData = {\n            info: data,\n            cateData: cateData,\n        };\n        return this.success(infoData);\n    }\n    async getAllCategory1Action() { // 我写的算法\n        const model = this.model('category');\n        const data = await model.where({\n            is_show: 1,\n            level: 'L1'\n        }).select();\n        const c_data = await model.where({\n            is_show: 1,\n            level: 'L2'\n        }).select();\n        let newData = [];\n        for (const item of data) {\n            let children = [];\n            for (const citem of c_data) {\n                if (citem.parent_id == item.id) {\n                    children.push({\n                        value: citem.id,\n                        label: citem.name\n                    })\n                }\n            }\n            newData.push({\n                value: item.id,\n                label: item.name,\n                children: children\n            });\n        }\n        return this.success(newData);\n    }\n    async getAllCategoryAction() { // 老婆的算法\n        const model = this.model('category');\n        const data = await model.where({\n            is_show: 1,\n            level: 'L1'\n        }).field('id,name').select();\n        let newData = [];\n        for (const item of data) {\n            let children = [];\n            const c_data = await model.where({\n                is_show: 1,\n                level: 'L2',\n                parent_id: item.id\n            }).field('id,name').select();\n            for (const c_item of c_data) {\n                children.push({\n                    value: c_item.id,\n                    label: c_item.name\n                })\n            }\n            newData.push({\n                value: item.id,\n                label: item.name,\n                children: children\n            });\n        }\n        return this.success(newData);\n    }\n    async getGoodsSnNameAction() {\n        const cateId = this.get('cateId');\n        const model = this.model('goods');\n        const data = await model.where({\n            category_id: cateId,\n            is_delete: 0\n        }).field('goods_sn,name').order({\n            'goods_sn': 'DESC'\n        }).select();\n        return this.success(data);\n    }\n    async storeAction() {\n        const values = this.post('info');\n        const model = this.model('goods');\n        let picUrl = values.list_pic_url;\n        let goods_id = values.id;\n        await this.model('cart').where({\n            goods_id: goods_id\n        }).update({\n            list_pic_url: picUrl\n        });\n        values.is_new = values.is_new ? 1 : 0;\n        let id = values.id;\n        if (id > 0) {\n            await model.where({\n                id: id\n            }).update(values);\n        } else {\n            delete values.id;\n            let goods_id = await model.add(values);\n            await model.where({\n                id: goods_id\n            }).update({\n                goods_sn: goods_id\n            });\n        }\n        return this.success(values);\n    }\n   \n    async destoryAction() {\n        const id = this.post('id');\n        await this.model('goods').where({\n            id: id\n        }).limit(1).delete();\n        return this.success();\n    }\n};"]}