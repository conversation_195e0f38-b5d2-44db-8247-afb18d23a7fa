{"version": 3, "sources": ["..\\..\\..\\src\\common\\service\\commission.js"], "names": ["module", "exports", "think", "Service", "calculateUserCommission", "userId", "currentTime", "parseInt", "Date", "getTime", "tenDaysAgo", "console", "log", "userCommission", "model", "where", "user_id", "find", "isEmpty", "totalCommission", "availableCommission", "frozenCommission", "withdrawnCommission", "refundedCommission", "commissionLogs", "commission_type", "status", "commission_change", "field", "select", "length", "logTime", "created_at", "parseFloat", "total_commission", "withdrawn_commission", "Math", "max", "result", "toFixed", "error", "getCommissionDetails", "limit", "order", "formattedDetails", "map", "amount", "statusText", "id", "abs", "type", "description", "orderId", "source_id", "settleTime", "createTime", "settleCommission", "promotionOrderId", "add", "commission_status", "promotion_order_id", "balance_after", "settle_time", "available_commission", "frozen_commission", "updated_at", "update", "update_time", "success", "message", "handleRefundCommission", "promotionOrders", "order_id", "promotionOrder", "personal_commission", "createRefundRecord", "promoter_user_id", "parent_promoter_user_id", "level1_commission", "refund_time"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,cAAcC,MAAMC,OAApB,CAA4B;;AAEzC;;;;;AAKMC,2BAAN,CAA8BC,MAA9B,EAAsC;AAAA;;AAAA;AAClC,gBAAI;AACA,sBAAMC,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACA,sBAAMC,aAAaJ,cAAe,KAAK,EAAL,GAAU,IAA5C,CAFA,CAEmD;;AAEnDK,wBAAQC,GAAR,CAAa,WAAUP,MAAO,WAA9B;AACAM,wBAAQC,GAAR,CAAY,OAAZ,EAAqBN,WAArB,EAAkC,OAAlC,EAA2CI,UAA3C;;AAEA;AACA,sBAAMG,iBAAiB,MAAM,MAAKC,KAAL,CAAW,iBAAX,EAA8BC,KAA9B,CAAoC;AAC7DC,6BAASX;AADoD,iBAApC,EAE1BY,IAF0B,EAA7B;;AAIA,oBAAIf,MAAMgB,OAAN,CAAcL,cAAd,CAAJ,EAAmC;AAC/BF,4BAAQC,GAAR,CAAY,gBAAZ;AACA,2BAAO;AACHO,yCAAiB,MADd;AAEHC,6CAAqB,MAFlB;AAGHC,0CAAkB,MAHf;AAIHC,6CAAqB,MAJlB;AAKHC,4CAAoB;AALjB,qBAAP;AAOH;;AAED;AACA,sBAAMC,iBAAiB,MAAM,MAAKV,KAAL,CAAW,gBAAX,EAA6BC,KAA7B,CAAmC;AAC5DC,6BAASX,MADmD;AAE5DoB,qCAAiB,WAF2C;AAG5DC,4BAAQ,WAHoD;AAI5DC,uCAAmB,CAAC,GAAD,EAAM,CAAN,CAJyC,CAIhC;AAJgC,iBAAnC,EAK1BC,KAL0B,CAKpB,+BALoB,EAKaC,MALb,EAA7B;;AAOAlB,wBAAQC,GAAR,CAAY,WAAZ,EAAyBY,eAAeM,MAAxC,EAAgD,GAAhD;;AAEA;AACA,oBAAIT,mBAAmB,CAAvB;AACA,qBAAK,MAAMT,GAAX,IAAkBY,cAAlB,EAAkC;AAC9B,0BAAMO,UAAUxB,SAAS,IAAIC,IAAJ,CAASI,IAAIoB,UAAb,EAAyBvB,OAAzB,KAAqC,IAA9C,CAAhB;AACA,wBAAIsB,UAAUrB,UAAd,EAA0B;AACtBW,4CAAoBY,WAAWrB,IAAIe,iBAAf,CAApB;AACH;AACJ;;AAED;AACA,sBAAMR,kBAAkBc,WAAWpB,eAAeqB,gBAA1B,CAAxB;AACA,sBAAMZ,sBAAsBW,WAAWpB,eAAesB,oBAA1B,CAA5B;AACA,sBAAMf,sBAAsBgB,KAAKC,GAAL,CAAS,CAAT,EAAYlB,kBAAkBE,gBAAlB,GAAqCC,mBAAjD,CAA5B;;AAEA,sBAAMgB,SAAS;AACXnB,qCAAiBA,gBAAgBoB,OAAhB,CAAwB,CAAxB,CADN;AAEXnB,yCAAqBA,oBAAoBmB,OAApB,CAA4B,CAA5B,CAFV;AAGXlB,sCAAkBA,iBAAiBkB,OAAjB,CAAyB,CAAzB,CAHP;AAIXjB,yCAAqBA,oBAAoBiB,OAApB,CAA4B,CAA5B,CAJV;AAKXhB,wCAAoB,MALT,CAKgB;AALhB,iBAAf;;AAQAZ,wBAAQC,GAAR,CAAY,SAAZ,EAAuB0B,MAAvB;AACA,uBAAOA,MAAP;AAEH,aA1DD,CA0DE,OAAOE,KAAP,EAAc;AACZ7B,wBAAQ6B,KAAR,CAAc,aAAd,EAA6BA,KAA7B;AACA,uBAAO;AACHrB,qCAAiB,MADd;AAEHC,yCAAqB,MAFlB;AAGHC,sCAAkB,MAHf;AAIHC,yCAAqB,MAJlB;AAKHC,wCAAoB;AALjB,iBAAP;AAOH;AApEiC;AAqErC;;AAED;;;;;;AAMMkB,wBAAN,CAA2BpC,MAA3B,EAAmCqC,QAAQ,EAA3C,EAA+C;AAAA;;AAAA;AAC3C,gBAAI;AACA/B,wBAAQC,GAAR,CAAa,WAAUP,MAAO,WAA9B;;AAEA;AACA,sBAAMmB,iBAAiB,MAAM,OAAKV,KAAL,CAAW,gBAAX,EAA6BC,KAA7B,CAAmC;AAC5DC,6BAASX,MADmD;AAE5DoB,qCAAiB;AAF2C,iBAAnC,EAG1BkB,KAH0B,CAGpB,iBAHoB,EAGDD,KAHC,CAGKA,KAHL,EAGYb,MAHZ,EAA7B;;AAKAlB,wBAAQC,GAAR,CAAY,UAAZ,EAAwBY,eAAeM,MAAvC,EAA+C,GAA/C;;AAEA;AACA,sBAAMxB,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;AACA,sBAAMC,aAAaJ,cAAe,KAAK,EAAL,GAAU,IAA5C;;AAEA,sBAAMsC,mBAAmBpB,eAAeqB,GAAf,CAAmB,eAAO;AAC/C,0BAAMC,SAASb,WAAWrB,IAAIe,iBAAf,CAAf;;AAEA;AACA,wBAAII,OAAJ;AACA,wBAAInB,IAAIoB,UAAJ,YAA0BxB,IAA9B,EAAoC;AAChCuB,kCAAUxB,SAASK,IAAIoB,UAAJ,CAAevB,OAAf,KAA2B,IAApC,CAAV;AACH,qBAFD,MAEO,IAAI,OAAOG,IAAIoB,UAAX,KAA0B,QAA9B,EAAwC;AAC3CD,kCAAUxB,SAAS,IAAIC,IAAJ,CAASI,IAAIoB,UAAb,EAAyBvB,OAAzB,KAAqC,IAA9C,CAAV;AACH,qBAFM,MAEA;AACH;AACAsB,kCAAUxB,SAASK,IAAIoB,UAAb,CAAV;AACH;;AAED,wBAAIe,aAAa,EAAjB;AACA,wBAAID,SAAS,CAAb,EAAgB;AACZC,qCAAa,KAAb;AACH,qBAFD,MAEO,IAAIhB,UAAUrB,UAAd,EAA0B;AAC7BqC,qCAAa,KAAb;AACH,qBAFM,MAEA;AACHA,qCAAa,KAAb;AACH;;AAED,2BAAO;AACHC,4BAAIpC,IAAIoC,EADL;AAEHF,gCAAQV,KAAKa,GAAL,CAASH,MAAT,EAAiBP,OAAjB,CAAyB,CAAzB,CAFL;AAGHW,8BAAMJ,SAAS,CAAT,GAAa,MAAb,GAAsB,QAHzB;AAIHpB,gCAAQd,IAAIc,MAJT;AAKHqB,oCAAYA,UALT;AAMHI,qCAAavC,IAAIuC,WAAJ,IAAmB,MAN7B;AAOHC,iCAASxC,IAAIyC,SAPV;AAQHC,oCAAYvB,OART;AASHwB,oCAAY3C,IAAIoB;AATb,qBAAP;AAWH,iBAlCwB,CAAzB;;AAoCA,uBAAOY,gBAAP;AAEH,aArDD,CAqDE,OAAOJ,KAAP,EAAc;AACZ7B,wBAAQ6B,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,EAAP;AACH;AAzD0C;AA0D9C;;AAED;;;;;;;;AAQMgB,oBAAN,CAAuBnD,MAAvB,EAA+ByC,MAA/B,EAAuCM,OAAvC,EAAgDK,gBAAhD,EAAkEN,WAAlE,EAA+E;AAAA;;AAAA;AAC3E,gBAAI;AACA,sBAAM7C,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;;AAEAE,wBAAQC,GAAR,CAAa,cAAb;AACAD,wBAAQC,GAAR,CAAY,OAAZ,EAAqBP,MAArB,EAA6B,KAA7B,EAAoCyC,MAApC,EAA4C,OAA5C,EAAqDM,OAArD;;AAEA;AACA,sBAAM,OAAKtC,KAAL,CAAW,gBAAX,EAA6B4C,GAA7B,CAAiC;AACnC1C,6BAASX,MAD0B;AAEnCsB,uCAAmBmB,MAFgB;AAGnCrB,qCAAiB,WAHkB;AAInCkC,uCAAmB,SAJgB;AAKnCN,+BAAWD,OALwB;AAMnCQ,wCAAoBH,gBANe;AAOnCN,iCAAaA,WAPsB;AAQnCU,mCAAe,CARoB,EAQjB;AAClBC,iCAAaxD,WATsB;AAUnC0B,gCAAY,IAAIxB,IAAJ;AAVuB,iBAAjC,CAAN;;AAaA;AACA,sBAAMK,iBAAiB,MAAM,OAAKC,KAAL,CAAW,iBAAX,EAA8BC,KAA9B,CAAoC;AAC7DC,6BAASX;AADoD,iBAApC,EAE1BY,IAF0B,EAA7B;;AAIA,oBAAIf,MAAMgB,OAAN,CAAcL,cAAd,CAAJ,EAAmC;AAC/B;AACA,0BAAM,OAAKC,KAAL,CAAW,iBAAX,EAA8B4C,GAA9B,CAAkC;AACpC1C,iCAASX,MAD2B;AAEpC6B,0CAAkBY,MAFkB;AAGpCiB,8CAAsB,CAHc,EAGX;AACzBC,2CAAmB,CAJiB,EAIX;AACzB7B,8CAAsB,CALc;AAMpCH,oCAAY,IAAIxB,IAAJ,EANwB;AAOpCyD,oCAAY,IAAIzD,IAAJ;AAPwB,qBAAlC,CAAN;AASH,iBAXD,MAWO;AACH;AACA,0BAAM,OAAKM,KAAL,CAAW,iBAAX,EAA8BC,KAA9B,CAAoC;AACtCC,iCAASX;AAD6B,qBAApC,EAEH6D,MAFG,CAEI;AACNhC,0CAAkB,CAAC,KAAD,EAAS,sBAAqBY,MAAO,EAArC,CADZ;AAENmB,oCAAY,IAAIzD,IAAJ;AAFN,qBAFJ,CAAN;AAMH;;AAED;AACA,sBAAM,OAAKM,KAAL,CAAW,kBAAX,EAA+BC,KAA/B,CAAqC;AACvCiC,wBAAIS;AADmC,iBAArC,EAEHS,MAFG,CAEI;AACNP,uCAAmB,SADb;AAENG,iCAAaxD,WAFP;AAGN6D,iCAAa7D;AAHP,iBAFJ,CAAN;;AAQAK,wBAAQC,GAAR,CAAY,UAAZ;AACA,uBAAO,EAAEwD,SAAS,IAAX,EAAiBC,SAAS,QAA1B,EAAP;AAEH,aA1DD,CA0DE,OAAO7B,KAAP,EAAc;AACZ7B,wBAAQ6B,KAAR,CAAc,SAAd,EAAyBA,KAAzB;AACA,uBAAO,EAAE4B,SAAS,KAAX,EAAkBC,SAAS7B,MAAM6B,OAAjC,EAAP;AACH;AA9D0E;AA+D9E;;AAED;;;;AAIMC,0BAAN,CAA6BlB,OAA7B,EAAsC;AAAA;;AAAA;AAClC,gBAAI;AACAzC,wBAAQC,GAAR,CAAa,WAAUwC,OAAQ,aAA/B;;AAEA;AACA,sBAAMmB,kBAAkB,MAAM,OAAKzD,KAAL,CAAW,kBAAX,EAA+BC,KAA/B,CAAqC;AAC/DyD,8BAAUpB,OADqD;AAE/DO,uCAAmB,CAAC,IAAD,EAAO,CAAC,SAAD,EAAY,WAAZ,CAAP,CAF4C,CAEX;AAFW,iBAArC,EAG3B9B,MAH2B,EAA9B;;AAKA,oBAAI3B,MAAMgB,OAAN,CAAcqD,eAAd,CAAJ,EAAoC;AAChC5D,4BAAQC,GAAR,CAAY,eAAZ;AACA,2BAAO,EAAEwD,SAAS,IAAX,EAAiBC,SAAS,WAA1B,EAAP;AACH;;AAED,sBAAM/D,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;;AAEA;AACA,qBAAK,MAAMgE,cAAX,IAA6BF,eAA7B,EAA8C;AAC1C;AACA,wBAAItC,WAAWwC,eAAeC,mBAA1B,IAAiD,CAArD,EAAwD;AACpD,8BAAM,OAAKC,kBAAL,CACFF,eAAeG,gBADb,EAEFH,eAAeC,mBAFb,EAGFtB,OAHE,EAIFqB,eAAezB,EAJb,EAKD,YALC,CAAN;AAOH;;AAED;AACA,wBAAIyB,eAAeI,uBAAf,IAA0C5C,WAAWwC,eAAeK,iBAA1B,IAA+C,CAA7F,EAAgG;AAC5F,8BAAM,OAAKH,kBAAL,CACFF,eAAeI,uBADb,EAEFJ,eAAeK,iBAFb,EAGF1B,OAHE,EAIFqB,eAAezB,EAJb,EAKD,YALC,CAAN;AAOH;;AAED;AACA,0BAAM,OAAKlC,KAAL,CAAW,kBAAX,EAA+BC,KAA/B,CAAqC;AACvCiC,4BAAIyB,eAAezB;AADoB,qBAArC,EAEHkB,MAFG,CAEI;AACNP,2CAAmB,UADb;AAENoB,qCAAazE,WAFP;AAGN6D,qCAAa7D;AAHP,qBAFJ,CAAN;AAOH;;AAEDK,wBAAQC,GAAR,CAAY,YAAZ;AACA,uBAAO,EAAEwD,SAAS,IAAX,EAAiBC,SAAS,UAA1B,EAAP;AAEH,aArDD,CAqDE,OAAO7B,KAAP,EAAc;AACZ7B,wBAAQ6B,KAAR,CAAc,WAAd,EAA2BA,KAA3B;AACA,uBAAO,EAAE4B,SAAS,KAAX,EAAkBC,SAAS7B,MAAM6B,OAAjC,EAAP;AACH;AAzDiC;AA0DrC;;AAED;;;AAGMM,sBAAN,CAAyBtE,MAAzB,EAAiCyC,MAAjC,EAAyCM,OAAzC,EAAkDK,gBAAlD,EAAoEN,WAApE,EAAiF;AAAA;;AAAA;AAC7E,kBAAM7C,cAAcC,SAAS,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,IAAhC,CAApB;;AAEA,kBAAM,OAAKK,KAAL,CAAW,gBAAX,EAA6B4C,GAA7B,CAAiC;AACnC1C,yBAASX,MAD0B;AAEnCsB,mCAAmB,CAACmB,MAFe,EAEP;AAC5BrB,iCAAiB,WAHkB;AAInCkC,mCAAmB,UAJgB;AAKnCN,2BAAWD,OALwB;AAMnCQ,oCAAoBH,gBANe;AAOnCN,6BAAaA,WAPsB;AAQnCU,+BAAe,CARoB;AASnCC,6BAAaxD,WATsB;AAUnC0B,4BAAY,IAAIxB,IAAJ;AAVuB,aAAjC,CAAN;;AAaA;AACA,kBAAM,OAAKM,KAAL,CAAW,iBAAX,EAA8BC,KAA9B,CAAoC;AACtCC,yBAASX;AAD6B,aAApC,EAEH6D,MAFG,CAEI;AACNhC,kCAAkB,CAAC,KAAD,EAAS,sBAAqBY,MAAO,EAArC,CADZ;AAENmB,4BAAY,IAAIzD,IAAJ;AAFN,aAFJ,CAAN;AAjB6E;AAuBhF;AAnTwC,CAA7C", "file": "..\\..\\..\\src\\common\\service\\commission.js", "sourcesContent": ["module.exports = class extends think.Service {\n    \n    /**\n     * 核心函数：计算用户佣金状态（基于现有数据库结构）\n     * @param {number} userId 用户ID\n     * @return {object} 佣金状态信息\n     */\n    async calculateUserCommission(userId) {\n        try {\n            const currentTime = parseInt(new Date().getTime() / 1000);\n            const tenDaysAgo = currentTime - (10 * 24 * 3600); // 10天前\n\n            console.log(`=== 计算用户${userId}的佣金状态 ===`);\n            console.log('当前时间:', currentTime, '10天前:', tenDaysAgo);\n\n            // 1. 获取用户佣金账户信息\n            const userCommission = await this.model('user_commission').where({\n                user_id: userId\n            }).find();\n\n            if (think.isEmpty(userCommission)) {\n                console.log('用户没有佣金账户，返回默认值');\n                return {\n                    totalCommission: '0.00',\n                    availableCommission: '0.00',\n                    frozenCommission: '0.00',\n                    withdrawnCommission: '0.00',\n                    refundedCommission: '0.00'\n                };\n            }\n\n            // 2. 查询佣金日志，计算10天期限内的冻结佣金\n            const commissionLogs = await this.model('commission_log').where({\n                user_id: userId,\n                commission_type: 'promotion',\n                status: 'completed',\n                commission_change: ['>', 0] // 只查询正数（获得的佣金）\n            }).field('commission_change, created_at').select();\n\n            console.log('用户佣金日志记录:', commissionLogs.length, '条');\n\n            // 3. 计算冻结中的佣金（10天内的）\n            let frozenCommission = 0;\n            for (const log of commissionLogs) {\n                const logTime = parseInt(new Date(log.created_at).getTime() / 1000);\n                if (logTime > tenDaysAgo) {\n                    frozenCommission += parseFloat(log.commission_change);\n                }\n            }\n\n            // 4. 计算可提现佣金 = 总佣金 - 冻结佣金 - 已提现佣金\n            const totalCommission = parseFloat(userCommission.total_commission);\n            const withdrawnCommission = parseFloat(userCommission.withdrawn_commission);\n            const availableCommission = Math.max(0, totalCommission - frozenCommission - withdrawnCommission);\n\n            const result = {\n                totalCommission: totalCommission.toFixed(2),\n                availableCommission: availableCommission.toFixed(2),\n                frozenCommission: frozenCommission.toFixed(2),\n                withdrawnCommission: withdrawnCommission.toFixed(2),\n                refundedCommission: '0.00' // 暂时设为0，后续可以从日志中计算\n            };\n\n            console.log('佣金计算结果:', result);\n            return result;\n\n        } catch (error) {\n            console.error('计算用户佣金状态失败:', error);\n            return {\n                totalCommission: '0.00',\n                availableCommission: '0.00',\n                frozenCommission: '0.00',\n                withdrawnCommission: '0.00',\n                refundedCommission: '0.00'\n            };\n        }\n    }\n\n    /**\n     * 获取用户佣金明细\n     * @param {number} userId 用户ID\n     * @param {number} limit 限制条数\n     * @return {array} 佣金明细列表\n     */\n    async getCommissionDetails(userId, limit = 20) {\n        try {\n            console.log(`=== 获取用户${userId}的佣金明细 ===`);\n\n            // 查询佣金日志\n            const commissionLogs = await this.model('commission_log').where({\n                user_id: userId,\n                commission_type: 'promotion'\n            }).order('created_at DESC').limit(limit).select();\n\n            console.log('查询到佣金明细:', commissionLogs.length, '条');\n\n            // 格式化佣金明细\n            const currentTime = parseInt(new Date().getTime() / 1000);\n            const tenDaysAgo = currentTime - (10 * 24 * 3600);\n\n            const formattedDetails = commissionLogs.map(log => {\n                const amount = parseFloat(log.commission_change);\n\n                // 处理时间格式：created_at是timestamp类型，需要正确转换\n                let logTime;\n                if (log.created_at instanceof Date) {\n                    logTime = parseInt(log.created_at.getTime() / 1000);\n                } else if (typeof log.created_at === 'string') {\n                    logTime = parseInt(new Date(log.created_at).getTime() / 1000);\n                } else {\n                    // 如果已经是时间戳格式\n                    logTime = parseInt(log.created_at);\n                }\n\n                let statusText = '';\n                if (amount < 0) {\n                    statusText = '已扣除';\n                } else if (logTime > tenDaysAgo) {\n                    statusText = '冻结中';\n                } else {\n                    statusText = '可提现';\n                }\n\n                return {\n                    id: log.id,\n                    amount: Math.abs(amount).toFixed(2),\n                    type: amount > 0 ? 'earn' : 'refund',\n                    status: log.status,\n                    statusText: statusText,\n                    description: log.description || '推广佣金',\n                    orderId: log.source_id,\n                    settleTime: logTime,\n                    createTime: log.created_at\n                };\n            });\n\n            return formattedDetails;\n\n        } catch (error) {\n            console.error('获取佣金明细失败:', error);\n            return [];\n        }\n    }\n    \n    /**\n     * 发放佣金（确认收货时调用）\n     * @param {number} userId 用户ID\n     * @param {number} amount 佣金金额\n     * @param {number} orderId 订单ID\n     * @param {number} promotionOrderId 推广订单ID\n     * @param {string} description 描述\n     */\n    async settleCommission(userId, amount, orderId, promotionOrderId, description) {\n        try {\n            const currentTime = parseInt(new Date().getTime() / 1000);\n            \n            console.log(`=== 发放佣金 ===`);\n            console.log('用户ID:', userId, '金额:', amount, '订单ID:', orderId);\n            \n            // 1. 记录佣金日志\n            await this.model('commission_log').add({\n                user_id: userId,\n                commission_change: amount,\n                commission_type: 'promotion',\n                commission_status: 'settled',\n                source_id: orderId,\n                promotion_order_id: promotionOrderId,\n                description: description,\n                balance_after: 0, // 暂时设为0，后续可以实时计算\n                settle_time: currentTime,\n                created_at: new Date()\n            });\n            \n            // 2. 更新用户佣金总额\n            const userCommission = await this.model('user_commission').where({\n                user_id: userId\n            }).find();\n            \n            if (think.isEmpty(userCommission)) {\n                // 创建用户佣金记录\n                await this.model('user_commission').add({\n                    user_id: userId,\n                    total_commission: amount,\n                    available_commission: 0, // 实时计算，不存储\n                    frozen_commission: 0,    // 实时计算，不存储\n                    withdrawn_commission: 0,\n                    created_at: new Date(),\n                    updated_at: new Date()\n                });\n            } else {\n                // 更新总佣金\n                await this.model('user_commission').where({\n                    user_id: userId\n                }).update({\n                    total_commission: ['exp', `total_commission + ${amount}`],\n                    updated_at: new Date()\n                });\n            }\n            \n            // 3. 更新推广订单状态\n            await this.model('promotion_orders').where({\n                id: promotionOrderId\n            }).update({\n                commission_status: 'settled',\n                settle_time: currentTime,\n                update_time: currentTime\n            });\n            \n            console.log('✅ 佣金发放成功');\n            return { success: true, message: '佣金发放成功' };\n            \n        } catch (error) {\n            console.error('发放佣金失败:', error);\n            return { success: false, message: error.message };\n        }\n    }\n    \n    /**\n     * 处理退款扣除佣金\n     * @param {number} orderId 订单ID\n     */\n    async handleRefundCommission(orderId) {\n        try {\n            console.log(`=== 处理订单${orderId}的退款佣金扣除 ===`);\n            \n            // 1. 查找该订单的所有推广佣金记录\n            const promotionOrders = await this.model('promotion_orders').where({\n                order_id: orderId,\n                commission_status: ['IN', ['settled', 'withdrawn']] // 已发放或已提现的\n            }).select();\n            \n            if (think.isEmpty(promotionOrders)) {\n                console.log('没有找到需要退款的佣金记录');\n                return { success: true, message: '没有需要退款的佣金' };\n            }\n            \n            const currentTime = parseInt(new Date().getTime() / 1000);\n            \n            // 2. 为每个佣金记录创建退款扣除\n            for (const promotionOrder of promotionOrders) {\n                // 扣除个人佣金\n                if (parseFloat(promotionOrder.personal_commission) > 0) {\n                    await this.createRefundRecord(\n                        promotionOrder.promoter_user_id,\n                        promotionOrder.personal_commission,\n                        orderId,\n                        promotionOrder.id,\n                        `订单退款扣除个人佣金`\n                    );\n                }\n                \n                // 扣除上级佣金\n                if (promotionOrder.parent_promoter_user_id && parseFloat(promotionOrder.level1_commission) > 0) {\n                    await this.createRefundRecord(\n                        promotionOrder.parent_promoter_user_id,\n                        promotionOrder.level1_commission,\n                        orderId,\n                        promotionOrder.id,\n                        `订单退款扣除上级佣金`\n                    );\n                }\n                \n                // 更新推广订单状态\n                await this.model('promotion_orders').where({\n                    id: promotionOrder.id\n                }).update({\n                    commission_status: 'refunded',\n                    refund_time: currentTime,\n                    update_time: currentTime\n                });\n            }\n            \n            console.log('✅ 退款佣金扣除完成');\n            return { success: true, message: '退款佣金扣除完成' };\n            \n        } catch (error) {\n            console.error('处理退款佣金失败:', error);\n            return { success: false, message: error.message };\n        }\n    }\n    \n    /**\n     * 创建退款扣除记录\n     */\n    async createRefundRecord(userId, amount, orderId, promotionOrderId, description) {\n        const currentTime = parseInt(new Date().getTime() / 1000);\n        \n        await this.model('commission_log').add({\n            user_id: userId,\n            commission_change: -amount, // 负数表示扣除\n            commission_type: 'promotion',\n            commission_status: 'refunded',\n            source_id: orderId,\n            promotion_order_id: promotionOrderId,\n            description: description,\n            balance_after: 0,\n            settle_time: currentTime,\n            created_at: new Date()\n        });\n        \n        // 更新用户总佣金\n        await this.model('user_commission').where({\n            user_id: userId\n        }).update({\n            total_commission: ['exp', `total_commission - ${amount}`],\n            updated_at: new Date()\n        });\n    }\n};\n"]}